TID: [-1234] [] [2025-07-29 00:01:51,740]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-json/click5_sitemap/API/update_html_option_AJAX, HEALTH CHECK URL = /wp-json/click5_sitemap/API/update_html_option_AJAX
TID: [-1234] [] [2025-07-29 00:02:10,031]  WARN {org.apache.synapse.commons.util.MiscellaneousUtil} - Error loading properties from a file at from the System defined location: access-log.properties
TID: [-1234] [] [2025-07-29 00:02:18,744]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ws/km-wsdl/setting/address_book, HEALTH CHECK URL = /ws/km-wsdl/setting/address_book
TID: [-1234] [] [2025-07-29 00:02:18,746] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream org.apache.axis2.AxisFault: A required message information header, To, MessageID, or Action, is not present.
	at org.apache.axis2.addressing.AddressingFaultsHelper.triggerAddressingFault(AddressingFaultsHelper.java:373)
	at org.apache.axis2.addressing.AddressingFaultsHelper.triggerMessageAddressingRequiredFault(AddressingFaultsHelper.java:293)
	at org.apache.axis2.handlers.addressing.AddressingInHandler.checkForMandatoryHeaders(AddressingInHandler.java:309)
	at org.apache.axis2.handlers.addressing.AddressingInHandler.extractAddressingInformation(AddressingInHandler.java:288)
	at org.apache.axis2.handlers.addressing.AddressingInHandler.doInvoke(AddressingInHandler.java:164)
	at org.apache.axis2.handlers.AbstractTemplatedHandler.invoke(AbstractTemplatedHandler.java:44)
	at org.apache.synapse.transport.passthru.util.RelayUtils.processAddressing(RelayUtils.java:379)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:192)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:171)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:478)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2025-07-29 00:02:18,747] ERROR {org.apache.synapse.mediators.filters.FilterMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:171)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:478)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.axis2.AxisFault: A required message information header, To, MessageID, or Action, is not present.
	at org.apache.axis2.addressing.AddressingFaultsHelper.triggerAddressingFault(AddressingFaultsHelper.java:373)
	at org.apache.axis2.addressing.AddressingFaultsHelper.triggerMessageAddressingRequiredFault(AddressingFaultsHelper.java:293)
	at org.apache.axis2.handlers.addressing.AddressingInHandler.checkForMandatoryHeaders(AddressingInHandler.java:309)
	at org.apache.axis2.handlers.addressing.AddressingInHandler.extractAddressingInformation(AddressingInHandler.java:288)
	at org.apache.axis2.handlers.addressing.AddressingInHandler.doInvoke(AddressingInHandler.java:164)
	at org.apache.axis2.handlers.AbstractTemplatedHandler.invoke(AbstractTemplatedHandler.java:44)
	at org.apache.synapse.transport.passthru.util.RelayUtils.processAddressing(RelayUtils.java:379)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:192)
	... 20 more

TID: [-1234] [] [2025-07-29 00:02:18,792]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2025-07-29 00:04:10,736]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-json/metform/v1/forms/templates/0, HEALTH CHECK URL = /wp-json/metform/v1/forms/templates/0
TID: [-1234] [] [2025-07-29 00:05:27,736]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /proxy?url=http%3a//0:8080/, HEALTH CHECK URL = /proxy?url=http%3a//0:8080/
TID: [-1234] [] [2025-07-29 00:06:26,735]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-json/click5_sitemap/API/update_html_option_AJAX, HEALTH CHECK URL = /wp-json/click5_sitemap/API/update_html_option_AJAX
TID: [-1234] [] [2025-07-29 00:06:30,765]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-29 00:07:34,726]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-json/rsvpmaker/v1/stripesuccess/anythinghere, HEALTH CHECK URL = /wp-json/rsvpmaker/v1/stripesuccess/anythinghere
TID: [-1234] [] [2025-07-29 00:09:16,724]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/search/attribute?versionid=*&tf_version=%27+and+(select%20pg_sleep(7))+ISNULL--, HEALTH CHECK URL = /api/search/attribute?versionid=*&tf_version=%27+and+(select%20pg_sleep(7))+ISNULL--
TID: [-1234] [] [2025-07-29 00:09:45,725]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php, HEALTH CHECK URL = /wp-admin/admin-ajax.php
TID: [-1234] [] [2025-07-29 00:11:01,730]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-json/click5_sitemap/API/update_html_option_AJAX, HEALTH CHECK URL = /wp-json/click5_sitemap/API/update_html_option_AJAX
TID: [-1234] [] [2025-07-29 00:17:31,699]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php, HEALTH CHECK URL = /wp-admin/admin-ajax.php
TID: [-1234] [] [2025-07-29 00:19:05,683]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backend/backend/auth/signin, HEALTH CHECK URL = /backend/backend/auth/signin
TID: [-1234] [] [2025-07-29 00:19:47,692]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-json/rps_result/v1/route/student_fields, HEALTH CHECK URL = /wp-json/rps_result/v1/route/student_fields
TID: [-1234] [] [2025-07-29 00:20:15,683]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ca/rest/certrequests, HEALTH CHECK URL = /ca/rest/certrequests
TID: [-1234] [] [2025-07-29 00:20:15,684] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream org.apache.axiom.om.OMException: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.axis2.builder.ApplicationXMLBuilder.processDocument(ApplicationXMLBuilder.java:81)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:36)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 28 more

TID: [-1234] [] [2025-07-29 00:20:15,687] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.axiom.om.OMException: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.axis2.builder.ApplicationXMLBuilder.processDocument(ApplicationXMLBuilder.java:81)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more
Caused by: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:36)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 28 more

TID: [-1234] [] [2025-07-29 00:20:15,738]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2025-07-29 00:23:25,678]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 00:24:13,673]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /alerts/alertLightbox.php, HEALTH CHECK URL = /alerts/alertLightbox.php
TID: [-1234] [] [2025-07-29 00:24:35,214]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4eabcf92-eb15-497c-8c18-24c9e3614fff
TID: [-1234] [] [2025-07-29 00:24:45,673]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-json/rps_result/v1/route/search_student?department_id=1&batch_id=1, HEALTH CHECK URL = /wp-json/rps_result/v1/route/search_student?department_id=1&batch_id=1
TID: [-1234] [] [2025-07-29 00:30:37,670]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /alerts/alertConfigField.php, HEALTH CHECK URL = /alerts/alertConfigField.php
TID: [-1234] [] [2025-07-29 00:31:34,669]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /..\..\..\..\..\..\..\..\..\..\windows\win.ini, HEALTH CHECK URL = /..\..\..\..\..\..\..\..\..\..\windows\win.ini
TID: [-1234] [] [2025-07-29 00:31:55,674]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php/video/?dl=aHR0cHM6Ly9vYXN0Lm1lLw==, HEALTH CHECK URL = /index.php/video/?dl=aHR0cHM6Ly9vYXN0Lm1lLw==
TID: [-1234] [] [2025-07-29 00:32:40,663]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/asign-single-student-subjects.php, HEALTH CHECK URL = /admin/asign-single-student-subjects.php
TID: [-1234] [] [2025-07-29 00:34:46,663]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ubus/, HEALTH CHECK URL = /ubus/
TID: [-1234] [] [2025-07-29 00:34:46,664] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2025-07-29 00:34:46,666] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more

TID: [-1234] [] [2025-07-29 00:34:46,714]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2025-07-29 00:35:30,660]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /v1/agent/check/register, HEALTH CHECK URL = /v1/agent/check/register
TID: [-1234] [] [2025-07-29 00:36:30,898]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-29 00:36:50,660]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /live_check.shtml, HEALTH CHECK URL = /live_check.shtml
TID: [-1234] [] [2025-07-29 00:39:28,040]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20250729&denNgay=20250729&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20250729&denNgay=20250729&maTthc=
TID: [-1234] [] [2025-07-29 00:39:28,082]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2025-07-29 00:40:05,652]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /v1/agent/check/deregister/30VLiJZwGsQHahiW0noVjXEYknX, HEALTH CHECK URL = /v1/agent/check/deregister/30VLiJZwGsQHahiW0noVjXEYknX
TID: [-1234] [] [2025-07-29 00:44:23,668]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/ajax.php?action=login, HEALTH CHECK URL = /admin/ajax.php?action=login
TID: [-1234] [] [2025-07-29 00:45:15,671]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/ajax.php?action=login, HEALTH CHECK URL = /admin/ajax.php?action=login
TID: [-1234] [] [2025-07-29 00:45:49,668]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/ajax.php?action=login, HEALTH CHECK URL = /admin/ajax.php?action=login
TID: [-1234] [] [2025-07-29 00:46:28,663]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/ajax.php?action=login, HEALTH CHECK URL = /admin/ajax.php?action=login
TID: [-1234] [] [2025-07-29 00:46:52,673]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/ajax.php?action=login, HEALTH CHECK URL = /admin/ajax.php?action=login
TID: [-1234] [] [2025-07-29 00:47:31,669]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi/get_param.cgi?xml&sys.passwd&sys.su.name, HEALTH CHECK URL = /cgi/get_param.cgi?xml&sys.passwd&sys.su.name
TID: [-1234] [] [2025-07-29 00:47:51,663]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sysinit.shtml?r=52300, HEALTH CHECK URL = /sysinit.shtml?r=52300
TID: [-1234] [] [2025-07-29 00:48:19,665]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /set_safety.shtml?r=52300, HEALTH CHECK URL = /set_safety.shtml?r=52300
TID: [-1234] [] [2025-07-29 00:48:58,668]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/index.php?page=home, HEALTH CHECK URL = /admin/index.php?page=home
TID: [-1234] [] [2025-07-29 00:49:31,666]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /templates/default/html/windows/right.php, HEALTH CHECK URL = /templates/default/html/windows/right.php
TID: [-1234] [] [2025-07-29 00:49:32,580] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 00:49:32,831] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 00:49:33,063] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 00:49:33,297] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 00:49:33,526] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 00:49:33,768] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 00:49:34,011] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 00:49:34,238] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 00:49:34,471] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 00:49:34,698] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 00:49:34,913] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 00:49:35,112] ERROR {org.wso2.carbon.identity.auth.valve.AuthenticationValve} - Error while normalizing the request URI to process the authentication: java.net.URISyntaxException: Illegal character in path at index 5: /nice ports,/Trinity.txt.bak
	at java.net.URI$Parser.fail(URI.java:2845)
	at java.net.URI$Parser.checkChars(URI.java:3018)
	at java.net.URI$Parser.parseHierarchical(URI.java:3102)
	at java.net.URI$Parser.parse(URI.java:3060)
	at java.net.URI.<init>(URI.java:588)
	at org.wso2.carbon.identity.auth.service.util.AuthConfigurationUtil.getNormalizedRequestURI(AuthConfigurationUtil.java:244)
	at org.wso2.carbon.identity.auth.valve.AuthenticationValve.invoke(AuthenticationValve.java:88)
	at org.wso2.carbon.tomcat.ext.valves.CompositeValve.continueInvocation(CompositeValve.java:101)
	at org.wso2.carbon.tomcat.ext.valves.TomcatValveContainer.invokeValves(TomcatValveContainer.java:49)
	at org.wso2.carbon.tomcat.ext.valves.CompositeValve.invoke(CompositeValve.java:62)
	at org.wso2.carbon.tomcat.ext.valves.CarbonStuckThreadDetectionValve.invoke(CarbonStuckThreadDetectionValve.java:145)
	at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:687)
	at org.wso2.carbon.tomcat.ext.valves.CarbonContextCreatorValve.invoke(CarbonContextCreatorValve.java:59)
	at org.wso2.carbon.tomcat.ext.valves.RequestCorrelationIdValve.invoke(RequestCorrelationIdValve.java:124)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:891)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1784)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2025-07-29 00:49:35,325] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 00:49:35,552] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 00:49:35,772] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 00:49:35,987] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 00:49:36,215] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 00:49:36,470] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 00:49:36,692] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 00:49:36,940] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 00:49:37,152] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 00:49:37,374] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 00:49:37,580] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 00:49:37,772] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 00:49:37,966] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 00:49:38,189] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 00:49:51,669]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /booking.php?car_id=-1%20union%20select%201,md5(999999999),3,4,5,6,7,8,9,10--+, HEALTH CHECK URL = /booking.php?car_id=-1%20union%20select%201,md5(999999999),3,4,5,6,7,8,9,10--+
TID: [-1234] [] [2025-07-29 00:50:11,666]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/view_car.php?id=-1%20union%20select%201,md5(999999999),3,4,5,6,7,8,9,10--+, HEALTH CHECK URL = /admin/view_car.php?id=-1%20union%20select%201,md5(999999999),3,4,5,6,7,8,9,10--+
TID: [-1234] [] [2025-07-29 00:50:32,665]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /hms/admin/, HEALTH CHECK URL = /hms/admin/
TID: [-1234] [] [2025-07-29 00:50:51,667]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/manage_booking.php?id=-1%20union%20select%201,2,3,4,5,6,md5(999999999),8,9,10,11--+, HEALTH CHECK URL = /admin/manage_booking.php?id=-1%20union%20select%201,2,3,4,5,6,md5(999999999),8,9,10,11--+
TID: [-1234] [] [2025-07-29 00:51:12,661]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /delsnap.pl?name=|id, HEALTH CHECK URL = /delsnap.pl?name=|id
TID: [-1234] [] [2025-07-29 00:51:31,660]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/manage_user.php?id=-1%20union%20select%201,md5(999999999),3,4,5--+, HEALTH CHECK URL = /admin/manage_user.php?id=-1%20union%20select%201,md5(999999999),3,4,5--+
TID: [-1234] [] [2025-07-29 00:51:52,656]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /404%0dnew-header:value%0da:, HEALTH CHECK URL = /404%0dnew-header:value%0da:
TID: [-1234] [] [2025-07-29 00:53:52,654]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 00:54:32,437]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ba98539c-2297-41b7-8a97-e1599842f664
TID: [-1234] [] [2025-07-29 00:57:46,651]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.zul, HEALTH CHECK URL = /login.zul
TID: [-1234] [] [2025-07-29 00:58:28,644]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php, HEALTH CHECK URL = /wp-admin/admin-ajax.php
TID: [-1234] [] [2025-07-29 01:01:20,644]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /rest/api/latest/repos, HEALTH CHECK URL = /rest/api/latest/repos
TID: [-1234] [] [2025-07-29 01:02:59,642]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v2/open/rowsInfo, HEALTH CHECK URL = /api/v2/open/rowsInfo
TID: [-1234] [] [2025-07-29 01:03:26,640]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/subscriber, HEALTH CHECK URL = /api/subscriber
TID: [-1234] [] [2025-07-29 01:04:24,638]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login/, HEALTH CHECK URL = /login/
TID: [-1234] [] [2025-07-29 01:05:10,645]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mgmt/shared/iapp/rpm-spec-creator, HEALTH CHECK URL = /mgmt/shared/iapp/rpm-spec-creator
TID: [-1234] [] [2025-07-29 01:05:40,877]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 01:06:31,297]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-29 01:10:14,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/ExportLogs.sh, HEALTH CHECK URL = /cgi-bin/ExportLogs.sh
TID: [-1234] [] [2025-07-29 01:11:58,642]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?phonepe_action=curltestPhonePe&url=http://d23nb3p66jedqisnm3k0xds34zupqj9kd.oast.me, HEALTH CHECK URL = /?phonepe_action=curltestPhonePe&url=http://d23nb3p66jedqisnm3k0xds34zupqj9kd.oast.me
TID: [-1234] [] [2025-07-29 01:13:26,626]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /solr/solrdefault/debug/dump?param=ContentStreams&stream.url=file://c:/windows/win.ini, HEALTH CHECK URL = /solr/solrdefault/debug/dump?param=ContentStreams&stream.url=file://c:/windows/win.ini
TID: [-1234] [] [2025-07-29 01:13:26,629]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /solr/solrdefault/debug/dump?param=ContentStreams&stream.url=file:///etc/passwd, HEALTH CHECK URL = /solr/solrdefault/debug/dump?param=ContentStreams&stream.url=file:///etc/passwd
TID: [-1234] [] [2025-07-29 01:15:05,626]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php, HEALTH CHECK URL = /wp-admin/admin-ajax.php
TID: [-1234] [] [2025-07-29 01:27:00,612]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /kubepi/api/v1/systems/login/logs/search?pageNum=1&&pageSize=10, HEALTH CHECK URL = /kubepi/api/v1/systems/login/logs/search?pageNum=1&&pageSize=10
TID: [-1234] [] [2025-07-29 01:27:20,607]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 01:27:40,609]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /spcgi.cgi, HEALTH CHECK URL = /spcgi.cgi
TID: [-1234] [] [2025-07-29 01:28:41,621]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/auth/cognito/callback?access_token=ddaqfrhb&id_token=eyJhbGciOiJub25lIiwidHlwIjoiSldUIn0.eyJjb2duaXRvOnVzZXJuYW1lIjoid3NsbXNmc2pteCIsImVtYWlsIjoie3tlbWFpbH19In0=., HEALTH CHECK URL = /api/auth/cognito/callback?access_token=ddaqfrhb&id_token=eyJhbGciOiJub25lIiwidHlwIjoiSldUIn0.eyJjb2duaXRvOnVzZXJuYW1lIjoid3NsbXNmc2pteCIsImVtYWlsIjoie3tlbWFpbH19In0=.
TID: [-1234] [] [2025-07-29 01:29:41,603]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/2.0/mlflow/registered-models/create, HEALTH CHECK URL = /api/2.0/mlflow/registered-models/create
TID: [-1234] [] [2025-07-29 01:30:05,606]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /druid/indexer/v1/sampler?for=connect, HEALTH CHECK URL = /druid/indexer/v1/sampler?for=connect
TID: [-1234] [] [2025-07-29 01:32:56,603]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/2.0/mlflow/model-versions/create, HEALTH CHECK URL = /api/2.0/mlflow/model-versions/create
TID: [-1234] [] [2025-07-29 01:34:36,604]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/jmeter/download/files, HEALTH CHECK URL = /api/jmeter/download/files
TID: [-1234] [] [2025-07-29 01:34:56,599]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/fax_change_faxtrace_settings, HEALTH CHECK URL = /cgi-bin/fax_change_faxtrace_settings
TID: [-1234] [] [2025-07-29 01:35:35,608]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /hax/..CFIDE/adminapi/administrator.cfc?method=getBuildNumber&_cfclient=true, HEALTH CHECK URL = /hax/..CFIDE/adminapi/administrator.cfc?method=getBuildNumber&_cfclient=true
TID: [-1234] [] [2025-07-29 01:35:55,596]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cf_scripts/scripts/ajax/ckeditor/plugins/filemanager/iedit.cfc?method=wizardHash&_cfclient=true&returnFormat=wddx&inPassword=foo, HEALTH CHECK URL = /cf_scripts/scripts/ajax/ckeditor/plugins/filemanager/iedit.cfc?method=wizardHash&_cfclient=true&returnFormat=wddx&inPassword=foo
TID: [-1234] [] [2025-07-29 01:36:31,389]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-29 01:38:20,592]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tshirtecommerce/ajax.php?type=svg, HEALTH CHECK URL = /tshirtecommerce/ajax.php?type=svg
TID: [-1234] [] [2025-07-29 01:38:54,594]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CFIDE/wizards/common/utils.cfc?method=wizardHash&inPassword=foo&_cfclient=true&returnFormat=wddx, HEALTH CHECK URL = /CFIDE/wizards/common/utils.cfc?method=wizardHash&inPassword=foo&_cfclient=true&returnFormat=wddx
TID: [-1234] [] [2025-07-29 01:39:43,903]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /users/sign_in, HEALTH CHECK URL = /users/sign_in
TID: [-1234] [] [2025-07-29 01:41:47,591]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cfusion/..CFIDE/wizards/common/utils.cfc?method=wizardHash&inPassword=foo&_cfclient=true&returnFormat=wddx, HEALTH CHECK URL = /cfusion/..CFIDE/wizards/common/utils.cfc?method=wizardHash&inPassword=foo&_cfclient=true&returnFormat=wddx
TID: [-1234] [] [2025-07-29 01:43:25,582]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /minio/bootstrap/v1/verify, HEALTH CHECK URL = /minio/bootstrap/v1/verify
TID: [-1234] [] [2025-07-29 01:44:38,582]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /v1/warehouse/pending-events, HEALTH CHECK URL = /v1/warehouse/pending-events
TID: [-1234] [] [2025-07-29 01:44:42,583]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = //CFIDE/wizards/common/utils.cfc?method=wizardHash&inPassword=foo&_cfclient=true&returnFormat=wddx, HEALTH CHECK URL = //CFIDE/wizards/common/utils.cfc?method=wizardHash&inPassword=foo&_cfclient=true&returnFormat=wddx
TID: [-1234] [] [2025-07-29 01:45:44,582]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/router/mesh/status, HEALTH CHECK URL = /api/router/mesh/status
TID: [-1234] [] [2025-07-29 01:47:43,574]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cf_scripts/scripts/ajax/ckeditor/plugins/filemanager/iedit.cfc?method=wizardHash&_cfclient=true&returnFormat=wddx&inPassword=foo, HEALTH CHECK URL = /cf_scripts/scripts/ajax/ckeditor/plugins/filemanager/iedit.cfc?method=wizardHash&_cfclient=true&returnFormat=wddx&inPassword=foo
TID: [-1234] [] [2025-07-29 01:52:15,578]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ajax.php?action=save_user, HEALTH CHECK URL = /ajax.php?action=save_user
TID: [-1234] [] [2025-07-29 01:52:35,574]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /goform/aspForm, HEALTH CHECK URL = /goform/aspForm
TID: [-1234] [] [2025-07-29 01:53:35,574]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/snapshots, HEALTH CHECK URL = /api/v1/snapshots
TID: [-1234] [] [2025-07-29 01:54:35,571]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /PhoneBackup/9YiTgASR.php, HEALTH CHECK URL = /PhoneBackup/9YiTgASR.php
TID: [-1234] [] [2025-07-29 01:54:51,571]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.php, HEALTH CHECK URL = /login.php
TID: [-1234] [] [2025-07-29 01:55:11,574]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /chcalyk, HEALTH CHECK URL = /chcalyk
TID: [-1234] [] [2025-07-29 01:56:23,512]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20250729&denNgay=20250729&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20250729&denNgay=20250729&maTthc=
TID: [-1234] [] [2025-07-29 01:56:23,549]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2025-07-29 01:56:32,563]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin/view/%22%5d%5d%20%7b%7b%61%73%79%6e%63%20%61%73%79%6e%63%3d%22%74%72%75%65%22%20%63%61%63%68%65%64%3d%22%66%61%6c%73%65%22%20%63%6f%6e%74%65%78%74%3d%22%64%6f%63%2e%72%65%66%65%72%65%6e%63%65%22%7d%7d%7b%7b%70%79%74%68%6f%6e%7d%7d%70%72%69%6e%74%28%33%37%32%34%33%34%38%20%2a%20%38%34%37%33%33%33%34%29%7b%7b%2f%70%79%74%68%6f%6e%7d%7d%7b%7b%2f%61%73%79%6e%63%7d%7d?sheet=SkinsCode.XWikiSkinsSheet&xpage=view, HEALTH CHECK URL = /bin/view/%22%5d%5d%20%7b%7b%61%73%79%6e%63%20%61%73%79%6e%63%3d%22%74%72%75%65%22%20%63%61%63%68%65%64%3d%22%66%61%6c%73%65%22%20%63%6f%6e%74%65%78%74%3d%22%64%6f%63%2e%72%65%66%65%72%65%6e%63%65%22%7d%7d%7b%7b%70%79%74%68%6f%6e%7d%7d%70%72%69%6e%74%28%33%37%32%34%33%34%38%20%2a%20%38%34%37%33%33%33%34%29%7b%7b%2f%70%79%74%68%6f%6e%7d%7d%7b%7b%2f%61%73%79%6e%63%7d%7d?sheet=SkinsCode.XWikiSkinsSheet&xpage=view
TID: [-1234] [] [2025-07-29 01:57:05,562]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /b_download/index.html, HEALTH CHECK URL = /b_download/index.html
TID: [-1234] [] [2025-07-29 01:57:05,565]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /b_download/index.html, HEALTH CHECK URL = /b_download/index.html
TID: [-1234] [] [2025-07-29 01:57:36,565]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /PhoneBackup/9YiTgASR.php?input=MzBWTGlJY2VaeklCYmpMY29nMFJrREJxalNP, HEALTH CHECK URL = /PhoneBackup/9YiTgASR.php?input=MzBWTGlJY2VaeklCYmpMY29nMFJrREJxalNP
TID: [-1234] [] [2025-07-29 01:59:31,566]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /asyncrenderer/%7B%7Burl%7D%7D?clientId={{id}}&timeout=500&wiki=xwiki, HEALTH CHECK URL = /asyncrenderer/%7B%7Burl%7D%7D?clientId={{id}}&timeout=500&wiki=xwiki
TID: [-1234] [] [2025-07-29 02:02:28,562]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /iclock/file?url=/../../../../../../../../../windows/win.ini, HEALTH CHECK URL = /iclock/file?url=/../../../../../../../../../windows/win.ini
TID: [-1234] [] [2025-07-29 02:05:32,552]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.php, HEALTH CHECK URL = /login.php
TID: [-1234] [] [2025-07-29 02:05:38,248]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20250729&denNgay=20250729&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20250729&denNgay=20250729&maTthc=
TID: [-1234] [] [2025-07-29 02:05:38,286]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2025-07-29 02:06:31,676]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-29 02:08:15,544]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.php, HEALTH CHECK URL = /login.php
TID: [-1234] [] [2025-07-29 02:08:49,562]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.php, HEALTH CHECK URL = /login.php
TID: [-1234] [] [2025-07-29 02:09:37,612]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 02:10:16,546]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/sys/set_passwd, HEALTH CHECK URL = /api/sys/set_passwd
TID: [-1234] [] [2025-07-29 02:11:43,618]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /opencms/cmisatom/cmis-online/query, HEALTH CHECK URL = /opencms/cmisatom/cmis-online/query
TID: [-1234] [] [2025-07-29 02:11:43,619] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream org.apache.axiom.om.OMException: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.axis2.builder.ApplicationXMLBuilder.processDocument(ApplicationXMLBuilder.java:81)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:36)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 28 more

TID: [-1234] [] [2025-07-29 02:11:43,621] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.axiom.om.OMException: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.axis2.builder.ApplicationXMLBuilder.processDocument(ApplicationXMLBuilder.java:81)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more
Caused by: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:36)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 28 more

TID: [-1234] [] [2025-07-29 02:11:43,673]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2025-07-29 02:14:02,545]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cmisatom/cmis-online/query, HEALTH CHECK URL = /cmisatom/cmis-online/query
TID: [-1234] [] [2025-07-29 02:14:02,546] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream org.apache.axiom.om.OMException: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.axis2.builder.ApplicationXMLBuilder.processDocument(ApplicationXMLBuilder.java:81)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:36)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 28 more

TID: [-1234] [] [2025-07-29 02:14:02,548] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.axiom.om.OMException: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.axis2.builder.ApplicationXMLBuilder.processDocument(ApplicationXMLBuilder.java:81)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more
Caused by: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:36)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 28 more

TID: [-1234] [] [2025-07-29 02:14:02,599]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2025-07-29 02:16:59,536]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/sys/login, HEALTH CHECK URL = /api/sys/login
TID: [-1234] [] [2025-07-29 02:18:28,532]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-job-portal-jobseeker-controlpanel/jobs, HEALTH CHECK URL = /wp-job-portal-jobseeker-controlpanel/jobs
TID: [-1234] [] [2025-07-29 02:19:27,531]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login, HEALTH CHECK URL = /login
TID: [-1234] [] [2025-07-29 02:21:33,540]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/totp/user-backup-code/../../system/system-information, HEALTH CHECK URL = /api/v1/totp/user-backup-code/../../system/system-information
TID: [-1234] [] [2025-07-29 02:21:58,537]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /chaosblade?cmd=$(id), HEALTH CHECK URL = /chaosblade?cmd=$(id)
TID: [-1234] [] [2025-07-29 02:22:17,526]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/cav/client/status/../../admin/options, HEALTH CHECK URL = /api/v1/cav/client/status/../../admin/options
TID: [-1234] [] [2025-07-29 02:23:02,527]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /user/login/, HEALTH CHECK URL = /user/login/
TID: [-1234] [] [2025-07-29 02:24:47,525]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /user/login/, HEALTH CHECK URL = /user/login/
TID: [-1234] [] [2025-07-29 02:25:42,850]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20250729&denNgay=20250729&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20250729&denNgay=20250729&maTthc=
TID: [-1234] [] [2025-07-29 02:25:42,887]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2025-07-29 02:32:23,535]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /partymgr/control/getJSONuiLabelArray, HEALTH CHECK URL = /partymgr/control/getJSONuiLabelArray
TID: [-1234] [] [2025-07-29 02:32:23,536]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /partymgr/control/getJSONuiLabel, HEALTH CHECK URL = /partymgr/control/getJSONuiLabel
TID: [-1234] [] [2025-07-29 02:32:24,519]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /partymgr/control/getJSONuiLabel, HEALTH CHECK URL = /partymgr/control/getJSONuiLabel
TID: [-1234] [] [2025-07-29 02:32:24,519]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /partymgr/control/getJSONuiLabelArray, HEALTH CHECK URL = /partymgr/control/getJSONuiLabelArray
TID: [-1234] [] [2025-07-29 02:35:24,512]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /labkey/home/<USER>/labkey/home/<USER>
TID: [-1234] [] [2025-07-29 02:35:51,509]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /upload, HEALTH CHECK URL = /upload
TID: [-1234] [] [2025-07-29 02:36:31,787]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-29 02:37:53,869]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/clusters, HEALTH CHECK URL = /api/clusters
TID: [-1234] [] [2025-07-29 02:40:53,500]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nodes?view=summary, HEALTH CHECK URL = /nodes?view=summary
TID: [-1234] [] [2025-07-29 02:41:03,496]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /global-protect/login.esp, HEALTH CHECK URL = /global-protect/login.esp
TID: [-1234] [] [2025-07-29 02:42:56,504]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /3/ImportFiles?path=%2Fetc%2Fpasswd, HEALTH CHECK URL = /3/ImportFiles?path=%2Fetc%2Fpasswd
TID: [-1234] [] [2025-07-29 02:43:16,500]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /3/ParseSetup, HEALTH CHECK URL = /3/ParseSetup
TID: [-1234] [] [2025-07-29 02:44:54,497]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 02:45:18,737]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php, HEALTH CHECK URL = /wp-admin/admin-ajax.php
TID: [-1234] [] [2025-07-29 02:45:32,797]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20250729&denNgay=20250729&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20250729&denNgay=20250729&maTthc=
TID: [-1234] [] [2025-07-29 02:45:32,833]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2025-07-29 02:45:40,494]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /misc.php?action=showpopups&type=friend, HEALTH CHECK URL = /misc.php?action=showpopups&type=friend
TID: [-1234] [] [2025-07-29 02:46:01,491]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 02:46:24,495]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /geoserver/, HEALTH CHECK URL = /geoserver/
TID: [-1234] [] [2025-07-29 02:47:00,493]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?p=member&destination, HEALTH CHECK URL = /index.php?p=member&destination
TID: [-1234] [] [2025-07-29 02:47:42,732]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 02:48:02,731]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 02:48:45,773]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php, HEALTH CHECK URL = /index.php
TID: [-1234] [] [2025-07-29 02:49:07,690]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 02:49:32,751]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 02:49:55,493]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /userportal/api/rest/contentChannels/?startIndex=0&pageSize=4&sort=TIME&showType=all, HEALTH CHECK URL = /userportal/api/rest/contentChannels/?startIndex=0&pageSize=4&sort=TIME&showType=all
TID: [-1234] [] [2025-07-29 02:50:20,492]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 02:50:41,849]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 02:51:02,481]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 02:51:49,617]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/MANGA/index.cgi, HEALTH CHECK URL = /cgi-bin/MANGA/index.cgi
TID: [-1234] [] [2025-07-29 02:52:10,769]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 02:53:26,483]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/wp-fastest-cache/readme.txt, HEALTH CHECK URL = /wp-content/plugins/wp-fastest-cache/readme.txt
TID: [-1234] [] [2025-07-29 02:57:14,484]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-json/lp/v1/courses/archive-course?order_by=1+AND+(SELECT+1+FROM+(SELECT(SLEEP(6)))X)&limit=-1, HEALTH CHECK URL = /wp-json/lp/v1/courses/archive-course?order_by=1+AND+(SELECT+1+FROM+(SELECT(SLEEP(6)))X)&limit=-1
TID: [-1234] [] [2025-07-29 02:58:33,480]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/2.0/mlflow-artifacts/artifacts/30VLiEUUrTJtqjPJoeAO6uYeP9f, HEALTH CHECK URL = /api/2.0/mlflow-artifacts/artifacts/30VLiEUUrTJtqjPJoeAO6uYeP9f
TID: [-1234] [] [2025-07-29 02:58:33,481] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2025-07-29 02:58:33,483] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more

TID: [-1234] [] [2025-07-29 02:58:33,531]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2025-07-29 02:58:54,477]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/2.0/mlflow-artifacts/artifacts/%252E%252E%252F%252E%252E%252F%252E%252E%252F%252E%252E%252F%252E%252E%252F%252E%252E%252Fetc%252fpasswd, HEALTH CHECK URL = /api/2.0/mlflow-artifacts/artifacts/%252E%252E%252F%252E%252E%252F%252E%252E%252F%252E%252E%252F%252E%252E%252F%252E%252E%252Fetc%252fpasswd
TID: [-1234] [] [2025-07-29 03:00:45,467]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ajax-api/2.0/mlflow/experiments/create, HEALTH CHECK URL = /ajax-api/2.0/mlflow/experiments/create
TID: [-1234] [] [2025-07-29 03:02:51,473]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ajax-api/2.0/mlflow/registered-models/create, HEALTH CHECK URL = /ajax-api/2.0/mlflow/registered-models/create
TID: [-1234] [] [2025-07-29 03:03:12,464]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ajax-api/2.0/mlflow/model-versions/create, HEALTH CHECK URL = /ajax-api/2.0/mlflow/model-versions/create
TID: [-1234] [] [2025-07-29 03:03:33,467]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /model-versions/get-artifact?name=30VLiGlQcEiHtQTqalHpf5xudqp&path=etc%2Fpasswd&version=1, HEALTH CHECK URL = /model-versions/get-artifact?name=30VLiGlQcEiHtQTqalHpf5xudqp&path=etc%2Fpasswd&version=1
TID: [-1234] [] [2025-07-29 03:06:32,067]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-29 03:08:22,457]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /classes/common/busiFacade.php, HEALTH CHECK URL = /classes/common/busiFacade.php
TID: [-1234] [] [2025-07-29 03:08:22,459] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2025-07-29 03:08:22,460] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more

TID: [-1234] [] [2025-07-29 03:08:22,507]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2025-07-29 03:08:42,460]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webui/, HEALTH CHECK URL = /webui/
TID: [-1234] [] [2025-07-29 03:09:22,469]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = //streamingamf, HEALTH CHECK URL = /services/messagebroker/streamingamf
TID: [-1234] [] [2025-07-29 03:09:22,470] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream org.apache.axiom.om.OMException: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character 'a' (code 97) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:165)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character 'a' (code 97) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at com.ctc.wstx.sr.StreamScanner.throwUnexpectedChar(StreamScanner.java:639)
	at com.ctc.wstx.sr.BasicStreamReader.nextFromProlog(BasicStreamReader.java:2052)
	at com.ctc.wstx.sr.BasicStreamReader.next(BasicStreamReader.java:1134)
	at org.apache.axiom.util.stax.wrapper.XMLStreamReaderWrapper.next(XMLStreamReaderWrapper.java:225)
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:34)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 27 more

TID: [-1234] [] [2025-07-29 03:09:22,488] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.axiom.om.OMException: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character 'a' (code 97) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:165)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more
Caused by: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character 'a' (code 97) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at com.ctc.wstx.sr.StreamScanner.throwUnexpectedChar(StreamScanner.java:639)
	at com.ctc.wstx.sr.BasicStreamReader.nextFromProlog(BasicStreamReader.java:2052)
	at com.ctc.wstx.sr.BasicStreamReader.next(BasicStreamReader.java:1134)
	at org.apache.axiom.util.stax.wrapper.XMLStreamReaderWrapper.next(XMLStreamReaderWrapper.java:225)
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:34)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 27 more

TID: [-1234] [] [2025-07-29 03:09:22,527]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 601000, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2025-07-29 03:09:36,376]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 72dc0079-af5b-4ee4-8243-cb33c962f768
TID: [-1234] [] [2025-07-29 03:11:27,461]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 03:15:12,454]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?__wpdmxp=%27][/wpdm_package][wpdm_all_packages][wpdm_package%20id=%27, HEALTH CHECK URL = /?__wpdmxp=%27][/wpdm_package][wpdm_all_packages][wpdm_package%20id=%27
TID: [-1234] [] [2025-07-29 03:17:21,442]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php, HEALTH CHECK URL = /wp-admin/admin-ajax.php
TID: [-1234] [] [2025-07-29 03:18:43,445]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 03:21:20,444]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ajax-api/2.0/mlflow/experiments/create, HEALTH CHECK URL = /ajax-api/2.0/mlflow/experiments/create
TID: [-1234] [] [2025-07-29 03:23:26,441]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /component_server, HEALTH CHECK URL = /component_server
TID: [-1234] [] [2025-07-29 03:23:46,434]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /file=%3E%3Cam:code%3E404%3C/am:code%3E%3Cam:type%3EStatus, HEALTH CHECK URL = /file=%3E%3Cam:code%3E404%3C/am:code%3E%3Cam:type%3EStatus
TID: [-1234] [] [2025-07-29 03:24:07,445]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /component_server, HEALTH CHECK URL = /component_server
TID: [-1234] [] [2025-07-29 03:24:28,433]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /file=%3Cam:fault, HEALTH CHECK URL = /file=%3Cam:fault
TID: [-1234] [] [2025-07-29 03:24:35,385]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9b6ea8dc-9444-4a39-9b4e-f3c793441587
TID: [-1234] [] [2025-07-29 03:26:55,432]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /queue/join, HEALTH CHECK URL = /queue/join
TID: [-1234] [] [2025-07-29 03:27:15,438]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /queue/data?session_hash=30VLiK2C4TXWYzEYNDUyxcZ7UVw, HEALTH CHECK URL = /queue/data?session_hash=30VLiK2C4TXWYzEYNDUyxcZ7UVw
TID: [-1234] [] [2025-07-29 03:29:58,430]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cslu/v1/var/logs/customer-cslu-lib-log.log, HEALTH CHECK URL = /cslu/v1/var/logs/customer-cslu-lib-log.log
TID: [-1234] [] [2025-07-29 03:31:06,423]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /hax/..CFIDE/adminapi/_servermanager/servermanager.cfc?method=getHeartBeat, HEALTH CHECK URL = /hax/..CFIDE/adminapi/_servermanager/servermanager.cfc?method=getHeartBeat
TID: [-1234] [] [2025-07-29 03:31:27,427]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /xstoremgwt/cheetahImages?imageId=..\..\..\..\windows\win.ini, HEALTH CHECK URL = /xstoremgwt/cheetahImages?imageId=..\..\..\..\windows\win.ini
TID: [-1234] [] [2025-07-29 03:33:37,426]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 03:36:32,208]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-29 03:37:06,421]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dana-ws/saml20.ws, HEALTH CHECK URL = /dana-ws/saml20.ws
TID: [-1234] [] [2025-07-29 03:37:27,419]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dana-na/auth/saml-sso.cgi, HEALTH CHECK URL = /dana-na/auth/saml-sso.cgi
TID: [-1234] [] [2025-07-29 03:39:36,419]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /clients/MyCRL, HEALTH CHECK URL = /clients/MyCRL
TID: [-1234] [] [2025-07-29 03:40:42,413]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /goform/AccessControl, HEALTH CHECK URL = /goform/AccessControl
TID: [-1234] [] [2025-07-29 03:40:42,414] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2025-07-29 03:40:42,416] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more

TID: [-1234] [] [2025-07-29 03:40:42,464]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2025-07-29 03:41:23,414]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Yi7eq.txt, HEALTH CHECK URL = /Yi7eq.txt
TID: [-1234] [] [2025-07-29 03:43:06,413]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /gremlin, HEALTH CHECK URL = /gremlin
TID: [-1234] [] [2025-07-29 03:44:42,411]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /importexport.php?sql=c2VsZWN0KzksbWQ1KDk4MzE1MTIpLDk=&type=exportexcelbysql, HEALTH CHECK URL = /importexport.php?sql=c2VsZWN0KzksbWQ1KDk4MzE1MTIpLDk=&type=exportexcelbysql
TID: [-1234] [] [2025-07-29 03:45:55,412]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?InternalDir=/../../../../windows&InternalFile=win.ini, HEALTH CHECK URL = /?InternalDir=/../../../../windows&InternalFile=win.ini
TID: [-1234] [] [2025-07-29 03:46:20,406]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /RemoteApplicationMetadata.rem?wsdl, HEALTH CHECK URL = /RemoteApplicationMetadata.rem?wsdl
TID: [-1234] [] [2025-07-29 03:46:35,416]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?InternalDir=\..\..\..\..\etc&InternalFile=passwd, HEALTH CHECK URL = /?InternalDir=\..\..\..\..\etc&InternalFile=passwd
TID: [-1234] [] [2025-07-29 03:49:55,402]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ajax-api/2.0/mlflow/experiments/create, HEALTH CHECK URL = /ajax-api/2.0/mlflow/experiments/create
TID: [-1234] [] [2025-07-29 03:52:12,400]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/pr_monitor/getting_index_data.php, HEALTH CHECK URL = /admin/pr_monitor/getting_index_data.php
TID: [-1234] [] [2025-07-29 03:53:05,398]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /users/sign_in, HEALTH CHECK URL = /users/sign_in
TID: [-1234] [] [2025-07-29 03:54:08,391]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webtools/control/forgotPassword/%2e/%2e/ProgramExport, HEALTH CHECK URL = /webtools/control/forgotPassword/%2e/%2e/ProgramExport
TID: [-1234] [] [2025-07-29 03:55:17,399]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Admin/login.php, HEALTH CHECK URL = /Admin/login.php
TID: [-1234] [] [2025-07-29 03:55:37,387]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Admin/index.php, HEALTH CHECK URL = /Admin/index.php
TID: [-1234] [] [2025-07-29 03:56:27,397]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/cstecgi.cgi, HEALTH CHECK URL = /cgi-bin/cstecgi.cgi
TID: [-1234] [] [2025-07-29 03:57:07,400]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ySMAVh.txt, HEALTH CHECK URL = /ySMAVh.txt
TID: [-1234] [] [2025-07-29 04:01:01,381]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /javadoc/releases/javadoc/1.0.0//raw/..%5c..%2f..%2f..%2f..%2f..%2freposilite.db, HEALTH CHECK URL = /javadoc/releases/javadoc/1.0.0//raw/..%5c..%2f..%2f..%2f..%2f..%2freposilite.db
TID: [-1234] [] [2025-07-29 04:02:06,389]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /realms/master/protocol/openid-connect/auth?client_id=security-admin-console&redirect_uri=https%3A%2F%2fagm.haiduong.gov.vn%2Fadmin%2Fmaster%2Fconsole%2F&state=1&response_mode=query&response_type=code&scope=openid&nonce=1&code_challenge_method=S256&code_challenge=wMYxCiAZ5DmiZvqD0h5G_9QwE7IDDFRojvORiaqiTto, HEALTH CHECK URL = /realms/master/protocol/openid-connect/auth?client_id=security-admin-console&redirect_uri=https%3A%2F%2fagm.haiduong.gov.vn%2Fadmin%2Fmaster%2Fconsole%2F&state=1&response_mode=query&response_type=code&scope=openid&nonce=1&code_challenge_method=S256&code_challenge=wMYxCiAZ5DmiZvqD0h5G_9QwE7IDDFRojvORiaqiTto
TID: [-1234] [] [2025-07-29 04:06:32,428]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-29 04:07:05,379]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /controlloLogin.js, HEALTH CHECK URL = /controlloLogin.js
TID: [-1234] [] [2025-07-29 04:07:58,589]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /assets/php/upload.php, HEALTH CHECK URL = /assets/php/upload.php
TID: [-1234] [] [2025-07-29 04:08:29,638]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 04:09:15,378]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ajax-api/2.0/mlflow/experiments/create, HEALTH CHECK URL = /ajax-api/2.0/mlflow/experiments/create
TID: [-1234] [] [2025-07-29 04:11:07,609]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 04:11:24,372]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/webdav/chatgpt-next-web/backup.json?endpoint=https://webdav.yandex.com.d23nb3p66jedqisnm3k04zz4jbs9op9qi.oast.me/, HEALTH CHECK URL = /api/webdav/chatgpt-next-web/backup.json?endpoint=https://webdav.yandex.com.d23nb3p66jedqisnm3k04zz4jbs9op9qi.oast.me/
TID: [-1234] [] [2025-07-29 04:12:32,366]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mdm/checkin, HEALTH CHECK URL = /mdm/checkin
TID: [-1234] [] [2025-07-29 04:12:32,368] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream org.apache.axiom.om.OMException: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.axis2.builder.ApplicationXMLBuilder.processDocument(ApplicationXMLBuilder.java:81)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:36)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 28 more

TID: [-1234] [] [2025-07-29 04:12:32,370] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.axiom.om.OMException: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.axis2.builder.ApplicationXMLBuilder.processDocument(ApplicationXMLBuilder.java:81)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more
Caused by: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:36)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 28 more

TID: [-1234] [] [2025-07-29 04:12:32,422]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2025-07-29 04:13:25,367]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 04:14:10,369]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/livechat/sms-incoming/twilio, HEALTH CHECK URL = /api/v1/livechat/sms-incoming/twilio
TID: [-1234] [] [2025-07-29 04:15:17,359]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /static/nbextensions/#/../../../../../../../../../../etc/passwd, HEALTH CHECK URL = /static/nbextensions/#/../../../../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-29 04:15:42,365]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 04:16:21,364]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 04:16:40,844]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 04:17:00,766]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 04:17:20,658]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ajax/calendar.php, HEALTH CHECK URL = /ajax/calendar.php
TID: [-1234] [] [2025-07-29 04:17:40,730]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 04:18:00,361]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/users, HEALTH CHECK URL = /api/users
TID: [-1234] [] [2025-07-29 04:18:20,607]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/LayerSlider/assets/static/public/front.css, HEALTH CHECK URL = /wp-content/plugins/LayerSlider/assets/static/public/front.css
TID: [-1234] [] [2025-07-29 04:18:40,727]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?noAUTO=1, HEALTH CHECK URL = /index.php?noAUTO=1
TID: [-1234] [] [2025-07-29 04:19:36,355]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.rst, HEALTH CHECK URL = /login.rst
TID: [-1234] [] [2025-07-29 04:20:00,655]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.rst, HEALTH CHECK URL = /login.rst
TID: [-1234] [] [2025-07-29 04:20:20,595]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mailinspector/login.php, HEALTH CHECK URL = /mailinspector/login.php
TID: [-1234] [] [2025-07-29 04:20:40,681]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /include/file.php, HEALTH CHECK URL = /include/file.php
TID: [-1234] [] [2025-07-29 04:21:01,359]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/gen/clients/csharp, HEALTH CHECK URL = /api/gen/clients/csharp
TID: [-1234] [] [2025-07-29 04:21:21,355]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /en-US/login, HEALTH CHECK URL = /en-US/login
TID: [-1234] [] [2025-07-29 04:26:19,360]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /adminer.php, HEALTH CHECK URL = /adminer.php
TID: [-1234] [] [2025-07-29 04:26:19,361]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cache/index.tpl.php, HEALTH CHECK URL = /cache/index.tpl.php
TID: [-1234] [] [2025-07-29 04:26:19,363]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin.php, HEALTH CHECK URL = /admin.php
TID: [-1234] [] [2025-07-29 04:26:19,365]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /php_info.php, HEALTH CHECK URL = /php_info.php
TID: [-1234] [] [2025-07-29 04:26:19,365]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /info.php, HEALTH CHECK URL = /info.php
TID: [-1234] [] [2025-07-29 04:26:19,365]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cpanel.php, HEALTH CHECK URL = /cpanel.php
TID: [-1234] [] [2025-07-29 04:26:19,365]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env, HEALTH CHECK URL = /.env
TID: [-1234] [] [2025-07-29 04:26:19,366]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpinfo.php, HEALTH CHECK URL = /phpinfo.php
TID: [-1234] [] [2025-07-29 04:26:19,366]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /adminer.php, HEALTH CHECK URL = /adminer.php
TID: [-1234] [] [2025-07-29 04:26:19,367]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin.php, HEALTH CHECK URL = /admin.php
TID: [-1234] [] [2025-07-29 04:26:19,367]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /xmlrpc.php, HEALTH CHECK URL = /xmlrpc.php
TID: [-1234] [] [2025-07-29 04:26:19,367]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin/cron.php, HEALTH CHECK URL = /bin/cron.php
TID: [-1234] [] [2025-07-29 04:26:19,667]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /xmlrpc.php, HEALTH CHECK URL = /xmlrpc.php
TID: [-1234] [] [2025-07-29 04:26:19,667]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /php-info.php, HEALTH CHECK URL = /php-info.php
TID: [-1234] [] [2025-07-29 04:26:25,348]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html/usr/share/doc/hostname/copyright%3f, HEALTH CHECK URL = /html/usr/share/doc/hostname/copyright%3f
TID: [-1234] [] [2025-07-29 04:28:18,575]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Login.aspx, HEALTH CHECK URL = /Login.aspx
TID: [-1234] [] [2025-07-29 04:29:08,347]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login, HEALTH CHECK URL = /login
TID: [-1234] [] [2025-07-29 04:36:33,466]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-29 04:39:33,901]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 29054987-19cf-4963-a0b4-412f91bde719
TID: [-1234] [] [2025-07-29 04:40:55,329]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /npm-pwg/..;/axis2-AWC/services/listServices, HEALTH CHECK URL = /npm-pwg/..;/axis2-AWC/services/listServices
TID: [-1234] [] [2025-07-29 04:43:53,327]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webtools/control/forgotPassword/xmldsdump, HEALTH CHECK URL = /webtools/control/forgotPassword/xmldsdump
TID: [-1234] [] [2025-07-29 04:44:13,320]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /common/gfpus.txt, HEALTH CHECK URL = /common/gfpus.txt
TID: [-1234] [] [2025-07-29 04:47:00,320]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v2/simulation, HEALTH CHECK URL = /api/v2/simulation
TID: [-1234] [] [2025-07-29 04:47:00,321] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2025-07-29 04:47:00,323] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more

TID: [-1234] [] [2025-07-29 04:47:00,372]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2025-07-29 04:48:28,331]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v2/templates/, HEALTH CHECK URL = /api/v2/templates/
TID: [-1234] [] [2025-07-29 04:48:49,313]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v2/observables/extended, HEALTH CHECK URL = /api/v2/observables/extended
TID: [-1234] [] [2025-07-29 04:52:25,319]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /c/router, HEALTH CHECK URL = /c/router
TID: [-1234] [] [2025-07-29 04:52:25,320]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /c/router, HEALTH CHECK URL = /c/router
TID: [-1234] [] [2025-07-29 04:54:37,105]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d261108f-9ff8-4319-a403-a796e3147dd6
TID: [-1234] [] [2025-07-29 04:54:44,309]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/skk_get.cgi, HEALTH CHECK URL = /cgi-bin/skk_get.cgi
TID: [-1234] [] [2025-07-29 04:55:14,348]  INFO {org.wso2.carbon.identity.application.authentication.framework.store.SessionCleanUpService} - Session Data cleanup task is running successfully for removing expired Data
TID: [-1234] [] [2025-07-29 04:56:39,301]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/get-browser-snapshot?snapshot_path=/etc/passwd, HEALTH CHECK URL = /api/get-browser-snapshot?snapshot_path=/etc/passwd
TID: [-1234] [] [2025-07-29 04:57:58,297]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 04:58:08,297]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /device/description_en.html, HEALTH CHECK URL = /device/description_en.html
TID: [-1234] [] [2025-07-29 05:01:08,297]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = //juis_boxinfo.xml, HEALTH CHECK URL = //juis_boxinfo.xml
TID: [-1234] [] [2025-07-29 05:02:14,300]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/login, HEALTH CHECK URL = /admin/login
TID: [-1234] [] [2025-07-29 05:02:56,299]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/compass?download=L2V0Yy9wYXNzd2Q=, HEALTH CHECK URL = /admin/compass?download=L2V0Yy9wYXNzd2Q=
TID: [-1234] [] [2025-07-29 05:05:01,329]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 05:05:40,289]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /RST_status.htm, HEALTH CHECK URL = /RST_status.htm
TID: [-1234] [] [2025-07-29 05:06:01,292]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /RST_status.htm?x=1.gif, HEALTH CHECK URL = /RST_status.htm?x=1.gif
TID: [-1234] [] [2025-07-29 05:06:33,623]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-29 05:07:56,290]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /toolbox-resource/../serverconfig.xml, HEALTH CHECK URL = /toolbox-resource/../serverconfig.xml
TID: [-1234] [] [2025-07-29 05:10:03,284]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /chat/completions, HEALTH CHECK URL = /chat/completions
TID: [-1234] [] [2025-07-29 05:11:02,286]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /openam/json/realms/root/authenticate, HEALTH CHECK URL = /openam/json/realms/root/authenticate
TID: [-1234] [] [2025-07-29 05:16:17,280]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /interface-data/books-init, HEALTH CHECK URL = /interface-data/books-init
TID: [-1234] [] [2025-07-29 05:17:24,279]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /queue/join, HEALTH CHECK URL = /queue/join
TID: [-1234] [] [2025-07-29 05:18:18,271]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jtcgi/soap_cgi.pyc, HEALTH CHECK URL = /jtcgi/soap_cgi.pyc
TID: [-1234] [] [2025-07-29 05:18:18,272] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2025-07-29 05:18:18,274] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more

TID: [-1234] [] [2025-07-29 05:18:18,322]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2025-07-29 05:19:18,265]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 05:20:03,272]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /v1/proxy/test, HEALTH CHECK URL = /v1/proxy/test
TID: [-1234] [] [2025-07-29 05:20:57,262]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /GeneralDocs.aspx?rpt=../../../../Windows/win.ini, HEALTH CHECK URL = /GeneralDocs.aspx?rpt=../../../../Windows/win.ini
TID: [-1234] [] [2025-07-29 05:21:54,278]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /~projects, HEALTH CHECK URL = /~projects
TID: [-1234] [] [2025-07-29 05:22:14,266]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /-/media/doo-doo.ashx, HEALTH CHECK URL = /-/media/doo-doo.ashx
TID: [-1234] [] [2025-07-29 05:22:34,259]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 05:22:54,265]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 05:23:14,264]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 05:23:34,269]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Synchronization, HEALTH CHECK URL = /Synchronization
TID: [-1234] [] [2025-07-29 05:23:54,273]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nexus/service/local/repositories/releases/content/com/sbt/ignite/ignite-bom/maven-metadata.xml, HEALTH CHECK URL = /nexus/service/local/repositories/releases/content/com/sbt/ignite/ignite-bom/maven-metadata.xml
TID: [-1234] [] [2025-07-29 05:23:54,275] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream org.apache.axiom.om.OMException: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character '#' (code 35) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.axis2.builder.ApplicationXMLBuilder.processDocument(ApplicationXMLBuilder.java:81)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:478)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character '#' (code 35) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at com.ctc.wstx.sr.StreamScanner.throwUnexpectedChar(StreamScanner.java:639)
	at com.ctc.wstx.sr.BasicStreamReader.nextFromProlog(BasicStreamReader.java:2052)
	at com.ctc.wstx.sr.BasicStreamReader.next(BasicStreamReader.java:1134)
	at org.apache.axiom.util.stax.wrapper.XMLStreamReaderWrapper.next(XMLStreamReaderWrapper.java:225)
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:34)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 27 more

TID: [-1234] [] [2025-07-29 05:23:54,277] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:478)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.axiom.om.OMException: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character '#' (code 35) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.axis2.builder.ApplicationXMLBuilder.processDocument(ApplicationXMLBuilder.java:81)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 21 more
Caused by: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character '#' (code 35) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at com.ctc.wstx.sr.StreamScanner.throwUnexpectedChar(StreamScanner.java:639)
	at com.ctc.wstx.sr.BasicStreamReader.nextFromProlog(BasicStreamReader.java:2052)
	at com.ctc.wstx.sr.BasicStreamReader.next(BasicStreamReader.java:1134)
	at org.apache.axiom.util.stax.wrapper.XMLStreamReaderWrapper.next(XMLStreamReaderWrapper.java:225)
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:34)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 27 more

TID: [-1234] [] [2025-07-29 05:23:54,312]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 601000, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2025-07-29 05:24:14,272]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 05:24:34,266]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 05:25:14,263]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/login, HEALTH CHECK URL = /admin/login
TID: [-1234] [] [2025-07-29 05:25:34,259]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 05:26:14,259]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 05:26:34,259]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 05:27:24,255]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 05:28:22,258]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ProcessPlus, HEALTH CHECK URL = /ProcessPlus
TID: [-1234] [] [2025-07-29 05:29:07,262]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 05:30:02,252]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 05:33:40,249]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/supervisor/Factory.cgi, HEALTH CHECK URL = /cgi-bin/supervisor/Factory.cgi
TID: [-1234] [] [2025-07-29 05:33:40,250] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2025-07-29 05:33:40,252] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more

TID: [-1234] [] [2025-07-29 05:33:40,300]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2025-07-29 05:34:55,249]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /listing?cat=6&filter=1&job-type=1&keywords=Mr.&location=1&order=desc&placeid=US&placetype=country&range1=1&range2=1)%20AND%20(SELECT%201864%20FROM%20(SELECT(SLEEP(6)))gOGh)%20AND%20(6900=6900&salary-type=1&sort=id&subcat, HEALTH CHECK URL = /listing?cat=6&filter=1&job-type=1&keywords=Mr.&location=1&order=desc&placeid=US&placetype=country&range1=1&range2=1)%20AND%20(SELECT%201864%20FROM%20(SELECT(SLEEP(6)))gOGh)%20AND%20(6900=6900&salary-type=1&sort=id&subcat
TID: [-1234] [] [2025-07-29 05:36:03,251]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /__weave/file/tmp/weave/fs/../../../etc/passwd, HEALTH CHECK URL = /__weave/file/tmp/weave/fs/../../../etc/passwd
TID: [-1234] [] [2025-07-29 05:36:33,707]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-29 05:39:33,237]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/apikey?/api/v1/ping, HEALTH CHECK URL = /api/v1/apikey?/api/v1/ping
TID: [-1234] [] [2025-07-29 05:41:10,241]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.webui/..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5cwindows%5cwin.ini, HEALTH CHECK URL = /.webui/..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5cwindows%5cwin.ini
TID: [-1234] [] [2025-07-29 05:44:22,235]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app/modules/ut-cac/admin/cli.php, HEALTH CHECK URL = /app/modules/ut-cac/admin/cli.php
TID: [-1234] [] [2025-07-29 05:47:50,226]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 05:48:16,224]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 05:48:16,231]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 05:48:16,233]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 05:48:16,234]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 05:48:16,235]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 05:48:16,236]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 05:48:16,236]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 05:48:16,236]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 05:48:16,236]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 05:55:58,223]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 05:56:23,225]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 05:58:06,212]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php?s=9999')union+select+111,222,(select(concat(0x44617461626173653a20,database()))),4444,+5--+-&perpage=20&page=1&orderBy=source_id&dateEnd&dateStart&order=DESC&sources&action=depicter-lead-index, HEALTH CHECK URL = /wp-admin/admin-ajax.php?s=9999')union+select+111,222,(select(concat(0x44617461626173653a20,database()))),4444,+5--+-&perpage=20&page=1&orderBy=source_id&dateEnd&dateStart&order=DESC&sources&action=depicter-lead-index
TID: [-1234] [] [2025-07-29 06:00:38,215]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/profile.php, HEALTH CHECK URL = /wp-admin/profile.php
TID: [-1234] [] [2025-07-29 06:01:58,218]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/users.php?role=administrator, HEALTH CHECK URL = /wp-admin/users.php?role=administrator
TID: [-1234] [] [2025-07-29 06:02:25,213]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 06:06:41,204]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?instawp-database-manager=/../../../%2e%2fmigrate%2ftemplates%2fdebug%2fdb-table&table_name=wp_users--%20-, HEALTH CHECK URL = /?instawp-database-manager=/../../../%2e%2fmigrate%2ftemplates%2fdebug%2fdb-table&table_name=wp_users--%20-
TID: [-1234] [] [2025-07-29 06:07:51,202]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/sessions, HEALTH CHECK URL = /api/sessions
TID: [-1234] [] [2025-07-29 06:09:33,883]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ced041e9-e775-40de-9096-63699cb99eb6
TID: [-1234] [] [2025-07-29 06:09:56,204]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-json/sure-triggers/v1/automation/action, HEALTH CHECK URL = /wp-json/sure-triggers/v1/automation/action
TID: [-1234] [] [2025-07-29 06:11:05,194]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?UrkCEO/edit&theme=margot&squelette=..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2fetc%2fpasswd&style=margot.css, HEALTH CHECK URL = /?UrkCEO/edit&theme=margot&squelette=..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2fetc%2fpasswd&style=margot.css
TID: [-1234] [] [2025-07-29 06:12:36,198]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webui/application/get_saml_request?saml_id=1%26$(id|%20base64);, HEALTH CHECK URL = /webui/application/get_saml_request?saml_id=1%26$(id|%20base64);
TID: [-1234] [] [2025-07-29 06:14:12,190]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public/template.cgi?templatefile=$(id), HEALTH CHECK URL = /public/template.cgi?templatefile=$(id)
TID: [-1234] [] [2025-07-29 06:14:40,187]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /render/public/..%252f%255Cd23nb3p66jedqisnm3k0ucieseqt7ducm.oast.me%252f%253F%252f..%252f.., HEALTH CHECK URL = /render/public/..%252f%255Cd23nb3p66jedqisnm3k0ucieseqt7ducm.oast.me%252f%253F%252f..%252f..
TID: [-1234] [] [2025-07-29 06:15:21,184]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public/..%2F%5coast.pro%2F%3f%2F..%2F.., HEALTH CHECK URL = /public/..%2F%5coast.pro%2F%3f%2F..%2F..
TID: [-1234] [] [2025-07-29 06:16:19,195]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?s=30VLiJahkAWVT71ID0Bj1yH7whw&cats=1*sleep(5), HEALTH CHECK URL = /?s=30VLiJahkAWVT71ID0Bj1yH7whw&cats=1*sleep(5)
TID: [-1234] [] [2025-07-29 06:18:04,416]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /MagicInfo/servlet/SWUpdateFileUploader?fileName=./../../../../../../server/GS2Vh7.jsp&deviceType=abc&deviceModelName=test&swVer=123, HEALTH CHECK URL = /MagicInfo/servlet/SWUpdateFileUploader?fileName=./../../../../../../server/GS2Vh7.jsp&deviceType=abc&deviceModelName=test&swVer=123
TID: [-1234] [] [2025-07-29 06:18:41,183]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Providers/HtmlEditorProviders/DNNConnect.CKE/Browser/FileUploader.ashx?PortalID=0&storageFolderID=1&overrideFiles=false, HEALTH CHECK URL = /Providers/HtmlEditorProviders/DNNConnect.CKE/Browser/FileUploader.ashx?PortalID=0&storageFolderID=1&overrideFiles=false
TID: [-1234] [] [2025-07-29 06:19:57,187]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php, HEALTH CHECK URL = /wp-admin/admin-ajax.php
TID: [-1234] [] [2025-07-29 06:21:21,180]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.html, HEALTH CHECK URL = /login.html
TID: [-1234] [] [2025-07-29 06:21:21,182]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.htm, HEALTH CHECK URL = /index.htm
TID: [-1234] [] [2025-07-29 06:21:23,186]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-29 06:23:10,176]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.html, HEALTH CHECK URL = /login.html
TID: [-1234] [] [2025-07-29 06:24:17,171]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.html, HEALTH CHECK URL = /login.html
TID: [-1234] [] [2025-07-29 06:24:40,174]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ucmdb-ui/cms/loginRequest.do;, HEALTH CHECK URL = /ucmdb-ui/cms/loginRequest.do;
TID: [-1234] [] [2025-07-29 06:26:18,172]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /console/auth/login, HEALTH CHECK URL = /console/auth/login
TID: [-1234] [] [2025-07-29 06:27:00,172]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/, HEALTH CHECK URL = /admin/
TID: [-1234] [] [2025-07-29 06:27:01,172]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/, HEALTH CHECK URL = /admin/
TID: [-1234] [] [2025-07-29 06:30:22,166]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php, HEALTH CHECK URL = /index.php
TID: [-1234] [] [2025-07-29 06:30:25,162]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php, HEALTH CHECK URL = /index.php
TID: [-1234] [] [2025-07-29 06:30:25,170]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php, HEALTH CHECK URL = /index.php
TID: [-1234] [] [2025-07-29 06:30:25,174]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php, HEALTH CHECK URL = /index.php
TID: [-1234] [] [2025-07-29 06:30:25,174]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php, HEALTH CHECK URL = /index.php
TID: [-1234] [] [2025-07-29 06:32:19,167]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /libs/granite/core/content/login.html/j_security_check, HEALTH CHECK URL = /libs/granite/core/content/login.html/j_security_check
TID: [-1234] [] [2025-07-29 06:32:32,161]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /libs/granite/core/content/login.html/j_security_check, HEALTH CHECK URL = /libs/granite/core/content/login.html/j_security_check
TID: [-1234] [] [2025-07-29 06:32:42,166]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /libs/granite/core/content/login.html/j_security_check, HEALTH CHECK URL = /libs/granite/core/content/login.html/j_security_check
TID: [-1234] [] [2025-07-29 06:32:45,166]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /libs/granite/core/content/login.html/j_security_check, HEALTH CHECK URL = /libs/granite/core/content/login.html/j_security_check
TID: [-1234] [] [2025-07-29 06:32:46,158]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /libs/granite/core/content/login.html/j_security_check, HEALTH CHECK URL = /libs/granite/core/content/login.html/j_security_check
TID: [-1234] [] [2025-07-29 06:32:50,167]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /libs/granite/core/content/login.html/j_security_check, HEALTH CHECK URL = /libs/granite/core/content/login.html/j_security_check
TID: [-1234] [] [2025-07-29 06:32:52,162]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /libs/granite/core/content/login.html/j_security_check, HEALTH CHECK URL = /libs/granite/core/content/login.html/j_security_check
TID: [-1234] [] [2025-07-29 06:32:55,164]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /libs/granite/core/content/login.html/j_security_check, HEALTH CHECK URL = /libs/granite/core/content/login.html/j_security_check
TID: [-1234] [] [2025-07-29 06:42:18,149]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /system/console/bundles, HEALTH CHECK URL = /system/console/bundles
TID: [-1234] [] [2025-07-29 06:42:18,152]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = ///system///console///bundles, HEALTH CHECK URL = ///system///console///bundles
TID: [-1234] [] [2025-07-29 06:42:49,152]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/user/login, HEALTH CHECK URL = /api/v1/user/login
TID: [-1234] [] [2025-07-29 06:45:16,144]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/dispatcher.cgi?cmd=3, HEALTH CHECK URL = /cgi-bin/dispatcher.cgi?cmd=3
TID: [-1234] [] [2025-07-29 06:45:17,152]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/dispatcher.cgi?cmd=1, HEALTH CHECK URL = /cgi-bin/dispatcher.cgi?cmd=1
TID: [-1234] [] [2025-07-29 06:45:42,140]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/users/admin?fields=*,privileges/PrivilegeInfo/cluster_name,privileges/PrivilegeInfo/permission_name, HEALTH CHECK URL = /api/v1/users/admin?fields=*,privileges/PrivilegeInfo/cluster_name,privileges/PrivilegeInfo/permission_name
TID: [-1234] [] [2025-07-29 06:46:22,139]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.php, HEALTH CHECK URL = /login.php
TID: [-1234] [] [2025-07-29 06:48:59,141]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login/, HEALTH CHECK URL = /login/
TID: [-1234] [] [2025-07-29 06:49:30,142]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /auth/login/, HEALTH CHECK URL = /auth/login/
TID: [-1234] [] [2025-07-29 06:51:23,330]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-29 06:52:46,138]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/json/session/signin, HEALTH CHECK URL = /api/json/session/signin
TID: [-1234] [] [2025-07-29 06:53:44,137]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /console/index.html, HEALTH CHECK URL = /console/index.html
TID: [-1234] [] [2025-07-29 06:55:42,131]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/account/auth/form, HEALTH CHECK URL = /api/account/auth/form
TID: [-1234] [] [2025-07-29 06:55:43,131]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/account/auth/form, HEALTH CHECK URL = /api/account/auth/form
TID: [-1234] [] [2025-07-29 06:55:43,131]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/account/auth/form, HEALTH CHECK URL = /api/account/auth/form
TID: [-1234] [] [2025-07-29 06:55:43,132]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/account/auth/form, HEALTH CHECK URL = /api/account/auth/form
TID: [-1234] [] [2025-07-29 06:56:17,132]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /apisix/admin/user/login, HEALTH CHECK URL = /apisix/admin/user/login
TID: [-1234] [] [2025-07-29 07:00:49,119]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /client/api/, HEALTH CHECK URL = /client/api/
TID: [-1234] [] [2025-07-29 07:01:09,127]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dolphinscheduler/login, HEALTH CHECK URL = /dolphinscheduler/login
TID: [-1234] [] [2025-07-29 07:01:55,125]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /rest/v1/login, HEALTH CHECK URL = /rest/v1/login
TID: [-1234] [] [2025-07-29 07:01:55,126]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /rest/v1/login, HEALTH CHECK URL = /rest/v1/login
TID: [-1234] [] [2025-07-29 07:05:04,112]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 07:05:04,121]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 07:05:26,115]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login/system, HEALTH CHECK URL = /login/system
TID: [-1234] [] [2025-07-29 07:05:57,118]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /system/console, HEALTH CHECK URL = /system/console
TID: [-1234] [] [2025-07-29 07:09:34,363]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 835b8370-9b63-415a-8cbc-83e41f1a7a64
TID: [-1234] [] [2025-07-29 07:10:05,107]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /kylin/api/user/authentication, HEALTH CHECK URL = /kylin/api/user/authentication
TID: [-1234] [] [2025-07-29 07:10:07,115]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /kylin/api/user/authentication, HEALTH CHECK URL = /kylin/api/user/authentication
TID: [-1234] [] [2025-07-29 07:10:07,117]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /kylin/api/user/authentication, HEALTH CHECK URL = /kylin/api/user/authentication
TID: [-1234] [] [2025-07-29 07:10:07,118]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /kylin/api/user/authentication, HEALTH CHECK URL = /kylin/api/user/authentication
TID: [-1234] [] [2025-07-29 07:10:07,118]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /kylin/api/user/authentication, HEALTH CHECK URL = /kylin/api/user/authentication
TID: [-1234] [] [2025-07-29 07:10:07,118]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /kylin/api/user/authentication, HEALTH CHECK URL = /kylin/api/user/authentication
TID: [-1234] [] [2025-07-29 07:10:44,109]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login, HEALTH CHECK URL = /login
TID: [-1234] [] [2025-07-29 07:18:47,101]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:19:11,093]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:19:11,099]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:19:11,102]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:19:11,103]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:19:11,103]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:19:11,103]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:19:11,104]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:19:11,104]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:19:11,107]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:19:11,107]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:19:11,108]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:19:11,109]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:19:12,093]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:19:12,097]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:19:13,093]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:19:13,098]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:19:13,100]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:19:15,098]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:19:16,105]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:19:16,105]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:19:17,091]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:19:17,097]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:19:18,097]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:19:18,098]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:19:19,103]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:19:21,096]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:19:22,099]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:19:29,102]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:20:15,090]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:21:23,528]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-29 07:24:32,328]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 83992408-d280-4f59-8533-67e09f5774d4
TID: [-1234] [] [2025-07-29 07:31:08,079]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:31:31,089]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:31:32,078]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:31:32,082]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:31:32,082]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:31:32,083]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:31:32,084]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:31:32,085]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:31:32,085]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:31:32,086]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:31:32,086]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:31:32,087]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:31:32,091]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:31:32,093]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:31:33,080]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:31:33,082]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:31:34,074]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:31:34,079]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:31:35,076]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:31:37,079]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:31:37,082]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:31:38,073]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:31:38,075]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:31:39,078]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:31:39,079]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:31:40,080]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:31:42,081]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:31:43,085]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:31:50,081]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:32:35,080]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:35:42,073]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Forms/login1, HEALTH CHECK URL = /Forms/login1
TID: [-1234] [] [2025-07-29 07:35:42,075]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Forms/login1, HEALTH CHECK URL = /Forms/login1
TID: [-1234] [] [2025-07-29 07:43:50,094]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:44:13,063]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:44:14,053]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:44:14,058]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:44:14,061]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:44:14,068]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:44:14,068]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:44:14,069]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:44:14,074]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:44:14,075]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:44:14,075]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:44:14,077]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:44:14,077]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:44:14,398]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:44:15,054]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:44:15,062]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:44:16,055]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:44:16,061]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:44:17,061]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:44:19,059]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:44:19,061]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:44:19,064]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:44:20,063]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:44:20,064]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:44:20,066]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:44:21,060]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:44:23,060]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:44:24,058]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:44:31,061]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:45:16,059]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:46:47,057]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /signin, HEALTH CHECK URL = /signin
TID: [-1234] [] [2025-07-29 07:46:47,057]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /user, HEALTH CHECK URL = /user
TID: [-1234] [] [2025-07-29 07:47:45,059]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/user/login, HEALTH CHECK URL = /api/user/login
TID: [-1234] [] [2025-07-29 07:51:23,689]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-29 07:55:31,045]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:55:54,055]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:55:55,039]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:55:55,044]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:55:55,044]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:55:55,045]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:55:55,045]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:55:55,045]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:55:55,046]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:55:55,045]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:55:55,047]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:55:55,048]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:55:55,051]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:55:55,052]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:55:56,047]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:55:56,051]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:55:57,043]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:55:57,046]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:55:58,039]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:56:00,043]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:56:00,045]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:56:00,056]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:56:01,043]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:56:01,049]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:56:01,050]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:56:02,043]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:56:04,045]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:56:05,044]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:56:12,042]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 07:56:57,044]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:07:10,028]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:07:32,025]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:07:33,034]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:07:33,034]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:07:33,034]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:07:33,035]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:07:33,034]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:07:33,034]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:07:33,034]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:07:33,035]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:07:33,035]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:07:33,036]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:07:33,037]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:07:33,037]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:07:34,023]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:07:34,024]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:07:35,024]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:07:35,030]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:07:36,029]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:07:38,020]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:07:38,024]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:07:38,024]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:07:39,024]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:07:39,030]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:07:39,031]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:07:40,027]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:07:42,020]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:07:43,031]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:07:50,023]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:08:35,023]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:10:20,016]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 08:18:27,008]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:18:48,012]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:18:49,005]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:18:49,007]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:18:49,007]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:18:49,009]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:18:49,010]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:18:49,010]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:18:49,010]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:18:49,011]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:18:49,014]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:18:49,014]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:18:50,015]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:18:50,016]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:18:50,018]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:18:50,018]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:18:51,008]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:18:51,013]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:18:52,009]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:18:54,005]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:18:54,008]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:18:54,011]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:18:55,004]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:18:55,010]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:18:55,011]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:18:56,013]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:18:58,017]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:18:59,011]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:19:06,008]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:19:51,006]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:20:59,014]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.asp, HEALTH CHECK URL = /index.asp
TID: [-1234] [] [2025-07-29 08:21:23,997]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-29 08:21:51,998]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 08:29:27,993]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:29:48,994]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:29:49,993]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:29:49,996]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:29:50,002]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:29:50,003]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:29:50,003]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:29:50,003]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:29:50,003]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:29:50,003]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:29:50,004]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:29:50,005]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:29:50,997]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:29:50,998]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:29:50,998]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:29:50,999]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:29:51,999]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:29:52,000]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:29:52,996]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:29:54,997]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:29:54,998]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:29:54,999]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:29:55,992]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:29:55,992]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:29:55,993]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:29:56,996]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:29:58,997]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:29:59,994]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:30:06,994]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:30:51,990]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:40:27,983]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:40:47,980]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:40:48,977]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:40:48,979]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:40:48,979]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:40:48,979]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:40:48,980]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:40:48,984]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:40:48,984]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:40:48,984]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:40:48,985]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:40:49,974]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:40:49,979]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:40:49,981]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:40:49,981]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:40:49,982]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:40:50,977]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:40:50,982]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:40:51,976]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:40:53,982]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:40:53,983]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:40:53,983]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:40:54,973]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:40:54,980]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:40:54,984]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:40:55,983]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:40:57,976]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:40:59,985]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:41:05,981]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:41:50,978]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:43:29,986]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.cgi, HEALTH CHECK URL = /login.cgi
TID: [-1234] [] [2025-07-29 08:47:44,916]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20250729&denNgay=20250729&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20250729&denNgay=20250729&maTthc=
TID: [-1234] [] [2025-07-29 08:47:44,959]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2025-07-29 08:50:14,951]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20250729&denNgay=20250729&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20250729&denNgay=20250729&maTthc=
TID: [-1234] [] [2025-07-29 08:50:15,000]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2025-07-29 08:50:52,763] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 08:50:52,995] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 08:50:53,222] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 08:50:53,428] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 08:50:53,710] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 08:50:53,926] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 08:50:54,129] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 08:50:54,359] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 08:50:54,590] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 08:50:54,810] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 08:50:55,036] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 08:50:55,256] ERROR {org.wso2.carbon.identity.auth.valve.AuthenticationValve} - Error while normalizing the request URI to process the authentication: java.net.URISyntaxException: Illegal character in path at index 5: /nice ports,/Trinity.txt.bak
	at java.net.URI$Parser.fail(URI.java:2845)
	at java.net.URI$Parser.checkChars(URI.java:3018)
	at java.net.URI$Parser.parseHierarchical(URI.java:3102)
	at java.net.URI$Parser.parse(URI.java:3060)
	at java.net.URI.<init>(URI.java:588)
	at org.wso2.carbon.identity.auth.service.util.AuthConfigurationUtil.getNormalizedRequestURI(AuthConfigurationUtil.java:244)
	at org.wso2.carbon.identity.auth.valve.AuthenticationValve.invoke(AuthenticationValve.java:88)
	at org.wso2.carbon.tomcat.ext.valves.CompositeValve.continueInvocation(CompositeValve.java:101)
	at org.wso2.carbon.tomcat.ext.valves.TomcatValveContainer.invokeValves(TomcatValveContainer.java:49)
	at org.wso2.carbon.tomcat.ext.valves.CompositeValve.invoke(CompositeValve.java:62)
	at org.wso2.carbon.tomcat.ext.valves.CarbonStuckThreadDetectionValve.invoke(CarbonStuckThreadDetectionValve.java:145)
	at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:687)
	at org.wso2.carbon.tomcat.ext.valves.CarbonContextCreatorValve.invoke(CarbonContextCreatorValve.java:59)
	at org.wso2.carbon.tomcat.ext.valves.RequestCorrelationIdValve.invoke(RequestCorrelationIdValve.java:124)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:891)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1784)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2025-07-29 08:50:55,470] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 08:50:55,705] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 08:50:55,915] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 08:50:56,114] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 08:50:56,381] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 08:50:56,614] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 08:50:56,836] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 08:50:57,052] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 08:50:57,253] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 08:50:57,478] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 08:50:57,717] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 08:50:57,925] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 08:50:58,140] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 08:50:58,371] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 08:51:24,156]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-29 08:51:25,961]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:51:45,963]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:51:46,970]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:51:46,970]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:51:46,971]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:51:46,971]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:51:46,971]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:51:46,971]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:51:46,971]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:51:46,972]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:51:46,972]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:51:47,958]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:51:47,963]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:51:47,964]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:51:47,965]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:51:47,965]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:51:48,961]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:51:48,965]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:51:49,956]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:51:51,964]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:51:51,965]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:51:51,967]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:51:52,961]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:51:52,966]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:51:52,967]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:51:52,970]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:51:55,959]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:51:57,965]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:52:03,965]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:52:48,968]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 08:54:06,959]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 08:55:00,963]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login, HEALTH CHECK URL = /login
TID: [-1234] [] [2025-07-29 09:02:26,955]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:02:46,952]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:02:47,947]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:02:47,948]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:02:47,946]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:02:47,949]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:02:47,949]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:02:47,949]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:02:47,950]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:02:47,952]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:02:48,945]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:02:49,944]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:02:49,944]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:02:49,945]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:02:49,946]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:02:49,946]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:02:49,949]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:02:49,950]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:02:50,953]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:02:52,943]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:02:52,949]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:02:52,951]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:02:53,942]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:02:53,944]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:02:53,944]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:02:53,944]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:02:56,950]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:02:58,947]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:03:04,943]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:03:49,941]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:13:26,929]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:13:45,934]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:13:46,938]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:13:46,941]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:13:46,941]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:13:46,941]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:13:46,942]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:13:46,941]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:13:46,942]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:13:46,942]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:13:47,933]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:13:48,935]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:13:48,936]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:13:48,937]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:13:48,937]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:13:48,937]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:13:48,937]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:13:48,937]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:13:49,935]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:13:51,927]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:13:51,929]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:13:51,930]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:13:52,932]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:13:52,932]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:13:52,934]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:13:52,934]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:13:55,927]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:13:57,930]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:14:03,928]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:14:48,932]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:16:35,920]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/, HEALTH CHECK URL = /admin/
TID: [-1234] [] [2025-07-29 09:24:26,915]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:24:44,917]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:24:45,936]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:24:45,936]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:24:45,937]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:24:45,937]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:24:45,939]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:24:45,939]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:24:45,940]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:24:45,936]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:24:46,918]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:24:47,918]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:24:47,923]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:24:47,923]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:24:47,928]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:24:47,928]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:24:47,931]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:24:47,932]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:24:48,936]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:24:50,922]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:24:50,925]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:24:50,928]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:24:51,935]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:24:51,939]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:24:51,944]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:24:51,945]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:24:54,913]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:24:56,912]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:25:02,916]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:25:47,909]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:26:10,778]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20250729&denNgay=20250729&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20250729&denNgay=20250729&maTthc=
TID: [-1234] [] [2025-07-29 09:26:10,816]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2025-07-29 09:27:24,910]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/index.php, HEALTH CHECK URL = /admin/index.php
TID: [-1234] [] [2025-07-29 09:33:12,702]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-29 09:35:27,898]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:35:45,906]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:35:46,894]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:35:46,902]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:35:46,903]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:35:46,905]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:35:46,905]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:35:46,905]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:35:46,906]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:35:46,907]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:35:47,912]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:35:48,893]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:35:48,902]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:35:48,902]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:35:48,903]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:35:48,903]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:35:48,903]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:35:48,904]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:35:49,903]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:35:51,901]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:35:51,906]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:35:51,909]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:35:52,902]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:35:52,902]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:35:52,904]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:35:52,905]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:35:55,896]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:35:57,898]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:36:03,899]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:36:48,904]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:42:31,892]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:42:41,893]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:42:41,893]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:42:41,893]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:42:41,894]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:42:41,894]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:42:41,894]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:42:41,895]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:42:41,895]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:42:41,897]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:42:42,882]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:42:42,884]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:42:42,885]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:42:42,887]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:42:42,890]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2025-07-29 09:44:52,889]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bonita/loginservice?redirect=true&redirectUrl=%2Fbonita%2Fapps%2FappDirectoryBonita, HEALTH CHECK URL = /bonita/loginservice?redirect=true&redirectUrl=%2Fbonita%2Fapps%2FappDirectoryBonita
TID: [-1234] [] [2025-07-29 09:55:16,870]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.asp, HEALTH CHECK URL = /index.asp
TID: [-1234] [] [2025-07-29 10:03:13,060]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-29 10:13:42,448]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20250729&denNgay=20250729&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20250729&denNgay=20250729&maTthc=
TID: [-1234] [] [2025-07-29 10:13:42,487]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2025-07-29 10:17:30,842]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.cgi, HEALTH CHECK URL = /login.cgi
TID: [-1234] [] [2025-07-29 10:17:30,842]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.cgi, HEALTH CHECK URL = /login.cgi
TID: [-1234] [] [2025-07-29 10:17:30,843] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2025-07-29 10:17:30,843] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2025-07-29 10:17:30,845] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more

TID: [-1234] [] [2025-07-29 10:17:30,845] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more

TID: [-1234] [] [2025-07-29 10:17:30,887]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2025-07-29 10:17:30,892]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2025-07-29 10:27:53,831]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /camunda/app/welcome/default/, HEALTH CHECK URL = /camunda/app/welcome/default/
TID: [-1234] [] [2025-07-29 10:33:13,266]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-29 10:50:43,787]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login, HEALTH CHECK URL = /login
TID: [-1234] [] [2025-07-29 11:03:13,490]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-29 11:09:35,497]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2c1e361b-f515-4231-917b-a5eb10f3d589
TID: [-1234] [] [2025-07-29 11:10:10,763]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php/wp-json/wp/v2/sensei_email/, HEALTH CHECK URL = /index.php/wp-json/wp/v2/sensei_email/
TID: [-1234] [] [2025-07-29 11:14:05,832]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20250729&denNgay=20250729&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20250729&denNgay=20250729&maTthc=
TID: [-1234] [] [2025-07-29 11:14:05,879]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2025-07-29 11:22:52,739]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v2/login, HEALTH CHECK URL = /api/v2/login
TID: [-1234] [] [2025-07-29 11:32:41,726]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cu.html, HEALTH CHECK URL = /cu.html
TID: [-1234] [] [2025-07-29 11:33:14,116]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-29 11:39:51,087]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f2d00508-729d-4043-aa68-6c9a7a3f2886
TID: [-1234] [] [2025-07-29 11:44:25,247] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 11:44:25,482] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 11:44:25,714] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 11:44:25,943] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 11:44:26,185] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 11:44:26,466] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 11:44:26,731] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 11:44:26,943] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 11:44:27,214] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 11:44:27,431] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 11:44:27,656] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 11:44:27,866] ERROR {org.wso2.carbon.identity.auth.valve.AuthenticationValve} - Error while normalizing the request URI to process the authentication: java.net.URISyntaxException: Illegal character in path at index 5: /nice ports,/Trinity.txt.bak
	at java.net.URI$Parser.fail(URI.java:2845)
	at java.net.URI$Parser.checkChars(URI.java:3018)
	at java.net.URI$Parser.parseHierarchical(URI.java:3102)
	at java.net.URI$Parser.parse(URI.java:3060)
	at java.net.URI.<init>(URI.java:588)
	at org.wso2.carbon.identity.auth.service.util.AuthConfigurationUtil.getNormalizedRequestURI(AuthConfigurationUtil.java:244)
	at org.wso2.carbon.identity.auth.valve.AuthenticationValve.invoke(AuthenticationValve.java:88)
	at org.wso2.carbon.tomcat.ext.valves.CompositeValve.continueInvocation(CompositeValve.java:101)
	at org.wso2.carbon.tomcat.ext.valves.TomcatValveContainer.invokeValves(TomcatValveContainer.java:49)
	at org.wso2.carbon.tomcat.ext.valves.CompositeValve.invoke(CompositeValve.java:62)
	at org.wso2.carbon.tomcat.ext.valves.CarbonStuckThreadDetectionValve.invoke(CarbonStuckThreadDetectionValve.java:145)
	at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:687)
	at org.wso2.carbon.tomcat.ext.valves.CarbonContextCreatorValve.invoke(CarbonContextCreatorValve.java:59)
	at org.wso2.carbon.tomcat.ext.valves.RequestCorrelationIdValve.invoke(RequestCorrelationIdValve.java:124)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:891)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1784)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2025-07-29 11:44:28,065] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 11:44:28,264] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 11:44:28,494] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 11:44:28,705] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 11:44:28,920] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 11:44:29,121] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 11:44:29,334] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 11:44:29,612] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 11:44:29,836] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 11:44:30,053] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 11:44:30,299] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 11:44:30,517] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 11:44:30,717] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 11:44:30,947] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-07-29 11:55:00,698]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.InternalService/Login, HEALTH CHECK URL = /api.InternalService/Login
TID: [-1234] [] [2025-07-29 11:55:00,700] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream org.apache.axiom.om.OMException: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character 'A' (code 65) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:165)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character 'A' (code 65) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at com.ctc.wstx.sr.StreamScanner.throwUnexpectedChar(StreamScanner.java:639)
	at com.ctc.wstx.sr.BasicStreamReader.nextFromProlog(BasicStreamReader.java:2052)
	at com.ctc.wstx.sr.BasicStreamReader.next(BasicStreamReader.java:1134)
	at org.apache.axiom.util.stax.wrapper.XMLStreamReaderWrapper.next(XMLStreamReaderWrapper.java:225)
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:34)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 27 more

TID: [-1234] [] [2025-07-29 11:55:00,703] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.axiom.om.OMException: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character 'A' (code 65) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:165)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more
Caused by: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character 'A' (code 65) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at com.ctc.wstx.sr.StreamScanner.throwUnexpectedChar(StreamScanner.java:639)
	at com.ctc.wstx.sr.BasicStreamReader.nextFromProlog(BasicStreamReader.java:2052)
	at com.ctc.wstx.sr.BasicStreamReader.next(BasicStreamReader.java:1134)
	at org.apache.axiom.util.stax.wrapper.XMLStreamReaderWrapper.next(XMLStreamReaderWrapper.java:225)
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:34)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 27 more

TID: [-1234] [] [2025-07-29 11:55:00,739]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 601000, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2025-07-29 12:03:15,998]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-29 12:04:35,688]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cobbler_api, HEALTH CHECK URL = /cobbler_api
TID: [-1234] [] [2025-07-29 12:04:37,677]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cobbler_api, HEALTH CHECK URL = /cobbler_api
TID: [-1234] [] [2025-07-29 12:22:33,651]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /hue/accounts/login?next=/, HEALTH CHECK URL = /hue/accounts/login?next=/
TID: [-1234] [] [2025-07-29 12:33:28,086]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-29 12:35:10,750]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_session, HEALTH CHECK URL = /_session
TID: [-1234] [] [2025-07-29 12:35:26,641]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_session, HEALTH CHECK URL = /_session
TID: [-1234] [] [2025-07-29 12:35:26,643]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_session, HEALTH CHECK URL = /_session
TID: [-1234] [] [2025-07-29 12:35:26,646]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_session, HEALTH CHECK URL = /_session
TID: [-1234] [] [2025-07-29 12:35:26,733]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_session, HEALTH CHECK URL = /_session
TID: [-1234] [] [2025-07-29 12:35:26,748]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_session, HEALTH CHECK URL = /_session
TID: [-1234] [] [2025-07-29 12:35:26,758]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_session, HEALTH CHECK URL = /_session
TID: [-1234] [] [2025-07-29 12:35:27,064]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_session, HEALTH CHECK URL = /_session
TID: [-1234] [] [2025-07-29 12:35:27,073]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_session, HEALTH CHECK URL = /_session
TID: [-1234] [] [2025-07-29 12:35:27,326]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_session, HEALTH CHECK URL = /_session
TID: [-1234] [] [2025-07-29 12:35:27,350]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_session, HEALTH CHECK URL = /_session
TID: [-1234] [] [2025-07-29 12:35:27,414]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_session, HEALTH CHECK URL = /_session
TID: [-1234] [] [2025-07-29 12:35:27,444]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_session, HEALTH CHECK URL = /_session
TID: [-1234] [] [2025-07-29 12:35:27,669]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_session, HEALTH CHECK URL = /_session
TID: [-1234] [] [2025-07-29 12:35:30,750]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_session, HEALTH CHECK URL = /_session
TID: [-1234] [] [2025-07-29 12:35:31,885]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_session, HEALTH CHECK URL = /_session
TID: [-1234] [] [2025-07-29 12:41:40,073]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20250729&denNgay=20250729&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20250729&denNgay=20250729&maTthc=
TID: [-1234] [] [2025-07-29 12:41:40,110]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2025-07-29 13:03:28,191]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-29 13:13:18,584]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /WebInterface/, HEALTH CHECK URL = /WebInterface/
TID: [-1234] [] [2025-07-29 13:36:22,455]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-29 14:05:50,502]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /WebInterface/, HEALTH CHECK URL = /WebInterface/
TID: [-1234] [] [2025-07-29 14:06:22,727]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-29 14:14:23,492]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /WebInterface/function/, HEALTH CHECK URL = /WebInterface/function/
TID: [-1234] [] [2025-07-29 14:22:42,472]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.cgi, HEALTH CHECK URL = /login.cgi
TID: [-1234] [] [2025-07-29 14:27:16,470]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/auth/login, HEALTH CHECK URL = /api/auth/login
TID: [-1234] [] [2025-07-29 14:28:32,472]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /rest/auth/login, HEALTH CHECK URL = /rest/auth/login
TID: [-1234] [] [2025-07-29 14:29:03,460]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /logIn, HEALTH CHECK URL = /logIn
TID: [-1234] [] [2025-07-29 14:30:13,465]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dip/api/login, HEALTH CHECK URL = /dip/api/login
TID: [-1234] [] [2025-07-29 14:31:35,459]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /data/login, HEALTH CHECK URL = /data/login
TID: [-1234] [] [2025-07-29 14:31:35,461] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream org.apache.axiom.om.OMException: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character 'u' (code 117) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:165)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character 'u' (code 117) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at com.ctc.wstx.sr.StreamScanner.throwUnexpectedChar(StreamScanner.java:639)
	at com.ctc.wstx.sr.BasicStreamReader.nextFromProlog(BasicStreamReader.java:2052)
	at com.ctc.wstx.sr.BasicStreamReader.next(BasicStreamReader.java:1134)
	at org.apache.axiom.util.stax.wrapper.XMLStreamReaderWrapper.next(XMLStreamReaderWrapper.java:225)
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:34)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 27 more

TID: [-1234] [] [2025-07-29 14:31:35,465] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.axiom.om.OMException: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character 'u' (code 117) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:165)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more
Caused by: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character 'u' (code 117) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at com.ctc.wstx.sr.StreamScanner.throwUnexpectedChar(StreamScanner.java:639)
	at com.ctc.wstx.sr.BasicStreamReader.nextFromProlog(BasicStreamReader.java:2052)
	at com.ctc.wstx.sr.BasicStreamReader.next(BasicStreamReader.java:1134)
	at org.apache.axiom.util.stax.wrapper.XMLStreamReaderWrapper.next(XMLStreamReaderWrapper.java:225)
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:34)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 27 more

TID: [-1234] [] [2025-07-29 14:31:35,500]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 601000, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2025-07-29 14:31:37,460]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /data/login, HEALTH CHECK URL = /data/login
TID: [-1234] [] [2025-07-29 14:31:37,461] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream org.apache.axiom.om.OMException: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character 'u' (code 117) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:165)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character 'u' (code 117) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at com.ctc.wstx.sr.StreamScanner.throwUnexpectedChar(StreamScanner.java:639)
	at com.ctc.wstx.sr.BasicStreamReader.nextFromProlog(BasicStreamReader.java:2052)
	at com.ctc.wstx.sr.BasicStreamReader.next(BasicStreamReader.java:1134)
	at org.apache.axiom.util.stax.wrapper.XMLStreamReaderWrapper.next(XMLStreamReaderWrapper.java:225)
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:34)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 27 more

TID: [-1234] [] [2025-07-29 14:31:37,463] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.axiom.om.OMException: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character 'u' (code 117) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:165)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more
Caused by: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character 'u' (code 117) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at com.ctc.wstx.sr.StreamScanner.throwUnexpectedChar(StreamScanner.java:639)
	at com.ctc.wstx.sr.BasicStreamReader.nextFromProlog(BasicStreamReader.java:2052)
	at com.ctc.wstx.sr.BasicStreamReader.next(BasicStreamReader.java:1134)
	at org.apache.axiom.util.stax.wrapper.XMLStreamReaderWrapper.next(XMLStreamReaderWrapper.java:225)
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:34)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 27 more

TID: [-1234] [] [2025-07-29 14:31:37,501]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 601000, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2025-07-29 14:32:17,464]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sysmgmt/2015/bmc/session, HEALTH CHECK URL = /sysmgmt/2015/bmc/session
TID: [-1234] [] [2025-07-29 14:33:26,459]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 14:35:19,460]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /json, HEALTH CHECK URL = /json
TID: [-1234] [] [2025-07-29 14:35:55,456]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/login, HEALTH CHECK URL = /admin/login
TID: [-1234] [] [2025-07-29 14:36:22,807]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-29 14:36:49,452]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php/verifyLogin/login, HEALTH CHECK URL = /index.php/verifyLogin/login
TID: [-1234] [] [2025-07-29 14:37:07,452]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 14:40:13,449]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web/rest/v1/login/sessions, HEALTH CHECK URL = /web/rest/v1/login/sessions
TID: [-1234] [] [2025-07-29 14:40:14,463]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web/rest/v1/login/sessions, HEALTH CHECK URL = /web/rest/v1/login/sessions
TID: [-1234] [] [2025-07-29 14:40:14,465]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web/rest/v1/login/sessions, HEALTH CHECK URL = /web/rest/v1/login/sessions
TID: [-1234] [] [2025-07-29 14:40:14,465]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web/rest/v1/login/sessions, HEALTH CHECK URL = /web/rest/v1/login/sessions
TID: [-1234] [] [2025-07-29 14:40:14,467]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web/rest/v1/login/sessions, HEALTH CHECK URL = /web/rest/v1/login/sessions
TID: [-1234] [] [2025-07-29 14:40:14,468]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web/rest/v1/login/sessions, HEALTH CHECK URL = /web/rest/v1/login/sessions
TID: [-1234] [] [2025-07-29 14:40:14,469]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web/rest/v1/login/sessions, HEALTH CHECK URL = /web/rest/v1/login/sessions
TID: [-1234] [] [2025-07-29 14:40:14,553]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web/rest/v1/login/sessions, HEALTH CHECK URL = /web/rest/v1/login/sessions
TID: [-1234] [] [2025-07-29 14:44:35,450]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v3/users, HEALTH CHECK URL = /api/v3/users
TID: [-1234] [] [2025-07-29 14:47:26,442]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/users/signin, HEALTH CHECK URL = /api/v1/users/signin
TID: [-1234] [] [2025-07-29 14:48:41,453]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /member/authenticate, HEALTH CHECK URL = /member/authenticate
TID: [-1234] [] [2025-07-29 14:49:09,437]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /internal/security/login, HEALTH CHECK URL = /internal/security/login
TID: [-1234] [] [2025-07-29 14:51:50,434]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v5/login, HEALTH CHECK URL = /api/v5/login
TID: [-1234] [] [2025-07-29 14:51:53,432]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/auth, HEALTH CHECK URL = /api/v4/auth
TID: [-1234] [] [2025-07-29 14:54:47,157]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 322de132-5722-45e1-9762-052ca94479da
TID: [-1234] [] [2025-07-29 15:00:15,424]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CDGServer3/SystemConfig, HEALTH CHECK URL = /CDGServer3/SystemConfig
TID: [-1234] [] [2025-07-29 15:00:15,425]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CDGServer3/SystemConfig, HEALTH CHECK URL = /CDGServer3/SystemConfig
TID: [-1234] [] [2025-07-29 15:00:15,425]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CDGServer3/SystemConfig, HEALTH CHECK URL = /CDGServer3/SystemConfig
TID: [-1234] [] [2025-07-29 15:00:15,426]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CDGServer3/SystemConfig, HEALTH CHECK URL = /CDGServer3/SystemConfig
TID: [-1234] [] [2025-07-29 15:00:15,426]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CDGServer3/SystemConfig, HEALTH CHECK URL = /CDGServer3/SystemConfig
TID: [-1234] [] [2025-07-29 15:00:15,426]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CDGServer3/SystemConfig, HEALTH CHECK URL = /CDGServer3/SystemConfig
TID: [-1234] [] [2025-07-29 15:00:15,426]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CDGServer3/SystemConfig, HEALTH CHECK URL = /CDGServer3/SystemConfig
TID: [-1234] [] [2025-07-29 15:00:15,426]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CDGServer3/SystemConfig, HEALTH CHECK URL = /CDGServer3/SystemConfig
TID: [-1234] [] [2025-07-29 15:00:15,427]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CDGServer3/SystemConfig, HEALTH CHECK URL = /CDGServer3/SystemConfig
TID: [-1234] [] [2025-07-29 15:00:15,426]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CDGServer3/SystemConfig, HEALTH CHECK URL = /CDGServer3/SystemConfig
TID: [-1234] [] [2025-07-29 15:00:15,426]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CDGServer3/SystemConfig, HEALTH CHECK URL = /CDGServer3/SystemConfig
TID: [-1234] [] [2025-07-29 15:00:15,427]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CDGServer3/SystemConfig, HEALTH CHECK URL = /CDGServer3/SystemConfig
TID: [-1234] [] [2025-07-29 15:00:15,426]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CDGServer3/SystemConfig, HEALTH CHECK URL = /CDGServer3/SystemConfig
TID: [-1234] [] [2025-07-29 15:00:16,424]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CDGServer3/SystemConfig, HEALTH CHECK URL = /CDGServer3/SystemConfig
TID: [-1234] [] [2025-07-29 15:00:16,424]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CDGServer3/SystemConfig, HEALTH CHECK URL = /CDGServer3/SystemConfig
TID: [-1234] [] [2025-07-29 15:00:16,425]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CDGServer3/SystemConfig, HEALTH CHECK URL = /CDGServer3/SystemConfig
TID: [-1234] [] [2025-07-29 15:00:16,425]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CDGServer3/SystemConfig, HEALTH CHECK URL = /CDGServer3/SystemConfig
TID: [-1234] [] [2025-07-29 15:00:16,425]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CDGServer3/SystemConfig, HEALTH CHECK URL = /CDGServer3/SystemConfig
TID: [-1234] [] [2025-07-29 15:00:16,426]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CDGServer3/SystemConfig, HEALTH CHECK URL = /CDGServer3/SystemConfig
TID: [-1234] [] [2025-07-29 15:00:16,426]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CDGServer3/SystemConfig, HEALTH CHECK URL = /CDGServer3/SystemConfig
TID: [-1234] [] [2025-07-29 15:00:16,427]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CDGServer3/SystemConfig, HEALTH CHECK URL = /CDGServer3/SystemConfig
TID: [-1234] [] [2025-07-29 15:00:16,430]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CDGServer3/SystemConfig, HEALTH CHECK URL = /CDGServer3/SystemConfig
TID: [-1234] [] [2025-07-29 15:00:16,430]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CDGServer3/SystemConfig, HEALTH CHECK URL = /CDGServer3/SystemConfig
TID: [-1234] [] [2025-07-29 15:00:16,431]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CDGServer3/SystemConfig, HEALTH CHECK URL = /CDGServer3/SystemConfig
TID: [-1234] [] [2025-07-29 15:00:16,432]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CDGServer3/SystemConfig, HEALTH CHECK URL = /CDGServer3/SystemConfig
TID: [-1234] [] [2025-07-29 15:03:19,412]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /exciter.php, HEALTH CHECK URL = /exciter.php
TID: [-1234] [] [2025-07-29 15:03:19,418]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php, HEALTH CHECK URL = /index.php
TID: [-1234] [] [2025-07-29 15:05:01,439]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CDGServer3/SystemConfig, HEALTH CHECK URL = /CDGServer3/SystemConfig
TID: [-1234] [] [2025-07-29 15:05:01,439]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CDGServer3/SystemConfig, HEALTH CHECK URL = /CDGServer3/SystemConfig
TID: [-1234] [] [2025-07-29 15:05:01,439]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CDGServer3/SystemConfig, HEALTH CHECK URL = /CDGServer3/SystemConfig
TID: [-1234] [] [2025-07-29 15:05:01,439]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CDGServer3/SystemConfig, HEALTH CHECK URL = /CDGServer3/SystemConfig
TID: [-1234] [] [2025-07-29 15:05:01,439]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CDGServer3/SystemConfig, HEALTH CHECK URL = /CDGServer3/SystemConfig
TID: [-1234] [] [2025-07-29 15:05:02,415]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CDGServer3/SystemConfig, HEALTH CHECK URL = /CDGServer3/SystemConfig
TID: [-1234] [] [2025-07-29 15:05:02,420]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CDGServer3/SystemConfig, HEALTH CHECK URL = /CDGServer3/SystemConfig
TID: [-1234] [] [2025-07-29 15:06:21,413]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /service.web, HEALTH CHECK URL = /service.web
TID: [-1234] [] [2025-07-29 15:06:23,155]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-29 15:19:38,392]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /send_order.cgi?parameter=login, HEALTH CHECK URL = /send_order.cgi?parameter=login
TID: [-1234] [] [2025-07-29 15:19:38,393] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2025-07-29 15:19:38,395] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more

TID: [-1234] [] [2025-07-29 15:19:38,441]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2025-07-29 15:26:08,378]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 15:29:29,377]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /21408623/cgi-bin/tsaws.cgi, HEALTH CHECK URL = /21408623/cgi-bin/tsaws.cgi
TID: [-1234] [] [2025-07-29 15:30:09,370]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/proxy/tcp, HEALTH CHECK URL = /api/proxy/tcp
TID: [-1234] [] [2025-07-29 15:32:17,368]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /fuel/login, HEALTH CHECK URL = /fuel/login
TID: [-1234] [] [2025-07-29 15:32:37,373]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /prop.htm, HEALTH CHECK URL = /prop.htm
TID: [-1234] [] [2025-07-29 15:33:34,366]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /geoserver/j_spring_security_check, HEALTH CHECK URL = /geoserver/j_spring_security_check
TID: [-1234] [] [2025-07-29 15:33:34,370]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /geoserver/web/, HEALTH CHECK URL = /geoserver/web/
TID: [-1234] [] [2025-07-29 15:36:37,360]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?wicket:interface=:0:userPanel:loginForm::IFormSubmitListener::, HEALTH CHECK URL = /?wicket:interface=:0:userPanel:loginForm::IFormSubmitListener::
TID: [-1234] [] [2025-07-29 15:38:43,358]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /oauth/token, HEALTH CHECK URL = /oauth/token
TID: [-1234] [] [2025-07-29 15:38:45,357]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /oauth/token, HEALTH CHECK URL = /oauth/token
TID: [-1234] [] [2025-07-29 15:38:45,360]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /oauth/token, HEALTH CHECK URL = /oauth/token
TID: [-1234] [] [2025-07-29 15:38:45,365]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /oauth/token, HEALTH CHECK URL = /oauth/token
TID: [-1234] [] [2025-07-29 15:38:47,369]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /oauth/token, HEALTH CHECK URL = /oauth/token
TID: [-1234] [] [2025-07-29 15:38:47,369]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /oauth/token, HEALTH CHECK URL = /oauth/token
TID: [-1234] [] [2025-07-29 15:41:59,361]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 15:47:20,348]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /default/en_US/status.html, HEALTH CHECK URL = /default/en_US/status.html
TID: [-1234] [] [2025-07-29 15:47:27,354]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /default/en_US/status.html, HEALTH CHECK URL = /default/en_US/status.html
TID: [-1234] [] [2025-07-29 15:47:27,355]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /default/en_US/status.html, HEALTH CHECK URL = /default/en_US/status.html
TID: [-1234] [] [2025-07-29 15:47:27,355]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /default/en_US/status.html, HEALTH CHECK URL = /default/en_US/status.html
TID: [-1234] [] [2025-07-29 15:47:27,356]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /default/en_US/status.html, HEALTH CHECK URL = /default/en_US/status.html
TID: [-1234] [] [2025-07-29 15:47:27,357]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /default/en_US/status.html, HEALTH CHECK URL = /default/en_US/status.html
TID: [-1234] [] [2025-07-29 15:47:27,358]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /default/en_US/status.html, HEALTH CHECK URL = /default/en_US/status.html
TID: [-1234] [] [2025-07-29 15:47:27,358]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /default/en_US/status.html, HEALTH CHECK URL = /default/en_US/status.html
TID: [-1234] [] [2025-07-29 15:47:27,359]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /default/en_US/status.html, HEALTH CHECK URL = /default/en_US/status.html
TID: [-1234] [] [2025-07-29 15:47:27,359]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /default/en_US/status.html, HEALTH CHECK URL = /default/en_US/status.html
TID: [-1234] [] [2025-07-29 15:47:27,359]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /default/en_US/status.html, HEALTH CHECK URL = /default/en_US/status.html
TID: [-1234] [] [2025-07-29 15:47:27,360]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /default/en_US/status.html, HEALTH CHECK URL = /default/en_US/status.html
TID: [-1234] [] [2025-07-29 15:48:36,357]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/, HEALTH CHECK URL = /admin/
TID: [-1234] [] [2025-07-29 15:51:22,136]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-29 15:57:43,338]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login, HEALTH CHECK URL = /login
TID: [-1234] [] [2025-07-29 15:58:26,339]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login, HEALTH CHECK URL = /login
TID: [-1234] [] [2025-07-29 15:58:26,339]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login, HEALTH CHECK URL = /login
TID: [-1234] [] [2025-07-29 15:59:04,330]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/tokens, HEALTH CHECK URL = /api/tokens
TID: [-1234] [] [2025-07-29 16:01:39,325]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 16:03:03,320]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 16:03:03,325]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 16:03:03,328]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 16:03:06,327]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 16:03:49,331]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /apt/v1/context, HEALTH CHECK URL = /apt/v1/context
TID: [-1234] [] [2025-07-29 16:05:54,318]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /hp/device/SignIn/Index, HEALTH CHECK URL = /hp/device/SignIn/Index
TID: [-1234] [] [2025-07-29 16:07:52,316]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs/login/login.lua, HEALTH CHECK URL = /htdocs/login/login.lua
TID: [-1234] [] [2025-07-29 16:08:18,323]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index/login.cgi, HEALTH CHECK URL = /index/login.cgi
TID: [-1234] [] [2025-07-29 16:10:48,313]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login, HEALTH CHECK URL = /login
TID: [-1234] [] [2025-07-29 16:11:16,315]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /decisioncenter/j_security_check, HEALTH CHECK URL = /decisioncenter/j_security_check
TID: [-1234] [] [2025-07-29 16:11:39,327]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /j_spring_security_check, HEALTH CHECK URL = /j_spring_security_check
TID: [-1234] [] [2025-07-29 16:12:19,316]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /teamserver/j_security_check, HEALTH CHECK URL = /teamserver/j_security_check
TID: [-1234] [] [2025-07-29 16:12:40,315]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 16:13:37,318]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /hac/login, HEALTH CHECK URL = /hac/login
TID: [-1234] [] [2025-07-29 16:14:15,313]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /res/j_security_check, HEALTH CHECK URL = /res/j_security_check
TID: [-1234] [] [2025-07-29 16:14:34,311]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /hac/j_spring_security_check, HEALTH CHECK URL = /hac/j_spring_security_check
TID: [-1234] [] [2025-07-29 16:15:15,306]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /hmc/j_security_check, HEALTH CHECK URL = /hmc/j_security_check
TID: [-1234] [] [2025-07-29 16:15:35,311]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /hac/, HEALTH CHECK URL = /hac/
TID: [-1234] [] [2025-07-29 16:16:32,306]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /hmc/login, HEALTH CHECK URL = /hmc/login
TID: [-1234] [] [2025-07-29 16:17:42,307]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ibmmq/console/j_security_check, HEALTH CHECK URL = /ibmmq/console/j_security_check
TID: [-1234] [] [2025-07-29 16:17:42,312]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ibmmq/console/j_security_check, HEALTH CHECK URL = /ibmmq/console/j_security_check
TID: [-1234] [] [2025-07-29 16:17:42,313]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ibmmq/console/j_security_check, HEALTH CHECK URL = /ibmmq/console/j_security_check
TID: [-1234] [] [2025-07-29 16:18:09,307]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /hmc/j_spring_security_check, HEALTH CHECK URL = /hmc/j_spring_security_check
TID: [-1234] [] [2025-07-29 16:19:08,309]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 16:19:08,311]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /core/j_security_check, HEALTH CHECK URL = /core/j_security_check
TID: [-1234] [] [2025-07-29 16:20:10,299]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /hmc/, HEALTH CHECK URL = /hmc/
TID: [-1234] [] [2025-07-29 16:21:22,265]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-29 16:23:15,298]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /0/Authenticate, HEALTH CHECK URL = /0/Authenticate
TID: [-1234] [] [2025-07-29 16:23:56,296]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /data/login, HEALTH CHECK URL = /data/login
TID: [-1234] [] [2025-07-29 16:24:30,242]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 097de163-01ee-43bc-9ddc-72d16cfc8e24
TID: [-1234] [] [2025-07-29 16:26:30,288]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sess-bin/login_handler.cgi, HEALTH CHECK URL = /sess-bin/login_handler.cgi
TID: [-1234] [] [2025-07-29 16:29:08,299]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login/index.php, HEALTH CHECK URL = /login/index.php
TID: [-1234] [] [2025-07-29 16:29:08,300]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login, HEALTH CHECK URL = /login
TID: [-1234] [] [2025-07-29 16:29:09,290]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login, HEALTH CHECK URL = /login
TID: [-1234] [] [2025-07-29 16:29:09,296]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sites/web_vhost_domain_list.php, HEALTH CHECK URL = /sites/web_vhost_domain_list.php
TID: [-1234] [] [2025-07-29 16:29:09,299]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login/index.php, HEALTH CHECK URL = /login/index.php
TID: [-1234] [] [2025-07-29 16:29:09,299]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login/index.php, HEALTH CHECK URL = /login/index.php
TID: [-1234] [] [2025-07-29 16:29:09,302]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sites/web_vhost_domain_list.php, HEALTH CHECK URL = /sites/web_vhost_domain_list.php
TID: [-1234] [] [2025-07-29 16:29:09,302]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sites/web_vhost_domain_list.php, HEALTH CHECK URL = /sites/web_vhost_domain_list.php
TID: [-1234] [] [2025-07-29 16:29:09,302]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login, HEALTH CHECK URL = /login
TID: [-1234] [] [2025-07-29 16:36:51,277]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /content.php, HEALTH CHECK URL = /content.php
TID: [-1234] [] [2025-07-29 16:40:27,269]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jbpm-console/app/tasks.jsf, HEALTH CHECK URL = /jbpm-console/app/tasks.jsf
TID: [-1234] [] [2025-07-29 16:40:29,265]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jbpm-console/app/tasks.jsf, HEALTH CHECK URL = /jbpm-console/app/tasks.jsf
TID: [-1234] [] [2025-07-29 16:40:29,266]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jbpm-console/app/j_security_check, HEALTH CHECK URL = /jbpm-console/app/j_security_check
TID: [-1234] [] [2025-07-29 16:40:29,269]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jbpm-console/app/j_security_check, HEALTH CHECK URL = /jbpm-console/app/j_security_check
TID: [-1234] [] [2025-07-29 16:40:29,272]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jbpm-console/app/tasks.jsf, HEALTH CHECK URL = /jbpm-console/app/tasks.jsf
TID: [-1234] [] [2025-07-29 16:40:29,272]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jbpm-console/app/j_security_check, HEALTH CHECK URL = /jbpm-console/app/j_security_check
TID: [-1234] [] [2025-07-29 16:40:29,273]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jbpm-console/app/j_security_check, HEALTH CHECK URL = /jbpm-console/app/j_security_check
TID: [-1234] [] [2025-07-29 16:40:29,274]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jbpm-console/app/tasks.jsf, HEALTH CHECK URL = /jbpm-console/app/tasks.jsf
TID: [-1234] [] [2025-07-29 16:40:29,274]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jbpm-console/app/tasks.jsf, HEALTH CHECK URL = /jbpm-console/app/tasks.jsf
TID: [-1234] [] [2025-07-29 16:40:29,275]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jbpm-console/app/tasks.jsf, HEALTH CHECK URL = /jbpm-console/app/tasks.jsf
TID: [-1234] [] [2025-07-29 16:40:29,276]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jbpm-console/app/tasks.jsf, HEALTH CHECK URL = /jbpm-console/app/tasks.jsf
TID: [-1234] [] [2025-07-29 16:40:29,276]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jbpm-console/app/tasks.jsf, HEALTH CHECK URL = /jbpm-console/app/tasks.jsf
TID: [-1234] [] [2025-07-29 16:47:37,279]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jmx-console/, HEALTH CHECK URL = /jmx-console/
TID: [-1234] [] [2025-07-29 16:47:37,279]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jmx-console/, HEALTH CHECK URL = /jmx-console/
TID: [-1234] [] [2025-07-29 16:47:37,279]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jmx-console/, HEALTH CHECK URL = /jmx-console/
TID: [-1234] [] [2025-07-29 16:47:37,281]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jmx-console/, HEALTH CHECK URL = /jmx-console/
TID: [-1234] [] [2025-07-29 16:47:37,280]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jmx-console/, HEALTH CHECK URL = /jmx-console/
TID: [-1234] [] [2025-07-29 16:47:37,280]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jmx-console/, HEALTH CHECK URL = /jmx-console/
TID: [-1234] [] [2025-07-29 16:47:37,281]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jmx-console/, HEALTH CHECK URL = /jmx-console/
TID: [-1234] [] [2025-07-29 16:47:37,281]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jmx-console/, HEALTH CHECK URL = /jmx-console/
TID: [-1234] [] [2025-07-29 16:47:37,281]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jmx-console/, HEALTH CHECK URL = /jmx-console/
TID: [-1234] [] [2025-07-29 16:47:37,282]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jmx-console/, HEALTH CHECK URL = /jmx-console/
TID: [-1234] [] [2025-07-29 16:47:37,282]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jmx-console/, HEALTH CHECK URL = /jmx-console/
TID: [-1234] [] [2025-07-29 16:47:37,282]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jmx-console/, HEALTH CHECK URL = /jmx-console/
TID: [-1234] [] [2025-07-29 16:47:37,282]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jmx-console/, HEALTH CHECK URL = /jmx-console/
TID: [-1234] [] [2025-07-29 16:47:37,283]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jmx-console/, HEALTH CHECK URL = /jmx-console/
TID: [-1234] [] [2025-07-29 16:47:37,284]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jmx-console/, HEALTH CHECK URL = /jmx-console/
TID: [-1234] [] [2025-07-29 16:47:40,053]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jmx-console/, HEALTH CHECK URL = /jmx-console/
TID: [-1234] [] [2025-07-29 16:47:41,274]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jmx-console/, HEALTH CHECK URL = /jmx-console/
TID: [-1234] [] [2025-07-29 16:47:42,257]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jmx-console/, HEALTH CHECK URL = /jmx-console/
TID: [-1234] [] [2025-07-29 16:48:39,256]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jmx-console/, HEALTH CHECK URL = /jmx-console/
TID: [-1234] [] [2025-07-29 16:48:46,259]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jmx-console/, HEALTH CHECK URL = /jmx-console/
TID: [-1234] [] [2025-07-29 16:48:50,256]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jmx-console/, HEALTH CHECK URL = /jmx-console/
TID: [-1234] [] [2025-07-29 16:48:50,260]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jmx-console/, HEALTH CHECK URL = /jmx-console/
TID: [-1234] [] [2025-07-29 16:48:50,260]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jmx-console/, HEALTH CHECK URL = /jmx-console/
TID: [-1234] [] [2025-07-29 16:48:50,263]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jmx-console/, HEALTH CHECK URL = /jmx-console/
TID: [-1234] [] [2025-07-29 16:48:50,263]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jmx-console/, HEALTH CHECK URL = /jmx-console/
TID: [-1234] [] [2025-07-29 16:51:34,102]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-29 16:55:06,260]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jmx-console/, HEALTH CHECK URL = /jmx-console/
TID: [-1234] [] [2025-07-29 16:55:06,262]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jmx-console/, HEALTH CHECK URL = /jmx-console/
TID: [-1234] [] [2025-07-29 16:55:06,263]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jmx-console/, HEALTH CHECK URL = /jmx-console/
TID: [-1234] [] [2025-07-29 17:07:44,259]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /core/ajax/user.ajax.php, HEALTH CHECK URL = /core/ajax/user.ajax.php
TID: [-1234] [] [2025-07-29 17:11:01,220]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Users/<USER>/Users/<USER>
TID: [-1234] [] [2025-07-29 17:11:14,227]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Users/<USER>/Users/<USER>
TID: [-1234] [] [2025-07-29 17:11:15,222]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Users/<USER>/Users/<USER>
TID: [-1234] [] [2025-07-29 17:11:15,231]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Users/<USER>/Users/<USER>
TID: [-1234] [] [2025-07-29 17:11:15,231]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Users/<USER>/Users/<USER>
TID: [-1234] [] [2025-07-29 17:11:15,232]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Users/<USER>/Users/<USER>
TID: [-1234] [] [2025-07-29 17:11:15,233]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Users/<USER>/Users/<USER>
TID: [-1234] [] [2025-07-29 17:11:15,234]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Users/<USER>/Users/<USER>
TID: [-1234] [] [2025-07-29 17:11:15,240]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Users/<USER>/Users/<USER>
TID: [-1234] [] [2025-07-29 17:11:15,249]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Users/<USER>/Users/<USER>
TID: [-1234] [] [2025-07-29 17:11:15,249]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Users/<USER>/Users/<USER>
TID: [-1234] [] [2025-07-29 17:11:15,252]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Users/<USER>/Users/<USER>
TID: [-1234] [] [2025-07-29 17:13:21,246]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?v=d&p=dashboard, HEALTH CHECK URL = /index.php?v=d&p=dashboard
TID: [-1234] [] [2025-07-29 17:29:50,204]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 17:30:39,194]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /c6/Jhsoft.Web.login/AjaxForLogin.aspx, HEALTH CHECK URL = /c6/Jhsoft.Web.login/AjaxForLogin.aspx
TID: [-1234] [] [2025-07-29 17:31:03,199]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /j_spring_security_check, HEALTH CHECK URL = /j_spring_security_check
TID: [-1234] [] [2025-07-29 17:32:15,201]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 17:32:48,193]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/sessions, HEALTH CHECK URL = /api/sessions
TID: [-1234] [] [2025-07-29 17:33:12,007]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-29 17:33:49,188]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /hub/login?next, HEALTH CHECK URL = /hub/login?next
TID: [-1234] [] [2025-07-29 17:33:49,194]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /hub/login?next, HEALTH CHECK URL = /hub/login?next
TID: [-1234] [] [2025-07-29 17:34:53,189]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?controller=AuthController&action=login, HEALTH CHECK URL = /?controller=AuthController&action=login
TID: [-1234] [] [2025-07-29 17:37:47,184]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /actions/entree.php, HEALTH CHECK URL = /actions/entree.php
TID: [-1234] [] [2025-07-29 17:37:47,186]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php, HEALTH CHECK URL = /index.php
TID: [-1234] [] [2025-07-29 17:39:00,179]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 17:42:11,184]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webui/login, HEALTH CHECK URL = /webui/login
TID: [-1234] [] [2025-07-29 17:42:36,178]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lucee/admin/web.cfm, HEALTH CHECK URL = /lucee/admin/web.cfm
TID: [-1234] [] [2025-07-29 17:43:08,196]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.magnolia/admincentral, HEALTH CHECK URL = /.magnolia/admincentral
TID: [-1234] [] [2025-07-29 17:43:36,172]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mbilling/index.php/authentication/login, HEALTH CHECK URL = /mbilling/index.php/authentication/login
TID: [-1234] [] [2025-07-29 17:45:14,174]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 17:45:57,177]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login_password_page.php, HEALTH CHECK URL = /login_password_page.php
TID: [-1234] [] [2025-07-29 17:47:03,175]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /minio/webrpc, HEALTH CHECK URL = /minio/webrpc
TID: [-1234] [] [2025-07-29 17:47:03,175]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /minio/webrpc, HEALTH CHECK URL = /minio/webrpc
TID: [-1234] [] [2025-07-29 17:47:57,175]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /control/userimage.html, HEALTH CHECK URL = /control/userimage.html
TID: [-1234] [] [2025-07-29 17:49:34,169]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /control/userimage.html, HEALTH CHECK URL = /control/userimage.html
TID: [-1234] [] [2025-07-29 17:50:58,516]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20250729&denNgay=20250729&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20250729&denNgay=20250729&maTthc=
TID: [-1234] [] [2025-07-29 17:50:58,554]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2025-07-29 17:51:12,172]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/luci/, HEALTH CHECK URL = /cgi-bin/luci/
TID: [-1234] [] [2025-07-29 17:51:51,166]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login, HEALTH CHECK URL = /login
TID: [-1234] [] [2025-07-29 17:54:42,508]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 73ddb868-61ec-457a-bf2d-89631db2c385
TID: [-1234] [] [2025-07-29 17:54:52,218]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nacos/v1/auth/users/login, HEALTH CHECK URL = /nacos/v1/auth/users/login
TID: [-1234] [] [2025-07-29 17:54:52,219]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /v1/auth/users/login, HEALTH CHECK URL = /v1/auth/users/login
TID: [-1234] [] [2025-07-29 17:55:32,158]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nagios/side.php, HEALTH CHECK URL = /nagios/side.php
TID: [-1234] [] [2025-07-29 17:55:33,159]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nagios/side.php, HEALTH CHECK URL = /nagios/side.php
TID: [-1234] [] [2025-07-29 17:55:57,159]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /netflow/jspui/j_security_check, HEALTH CHECK URL = /netflow/jspui/j_security_check
TID: [-1234] [] [2025-07-29 17:56:25,154]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webadmin/index.php, HEALTH CHECK URL = /webadmin/index.php
TID: [-1234] [] [2025-07-29 18:00:15,153]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login, HEALTH CHECK URL = /login
TID: [-1234] [] [2025-07-29 18:01:01,150]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/tokens, HEALTH CHECK URL = /api/tokens
TID: [-1234] [] [2025-07-29 18:01:37,150]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/auth:signIn, HEALTH CHECK URL = /api/auth:signIn
TID: [-1234] [] [2025-07-29 18:03:12,256]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-29 18:04:03,144]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login/verify, HEALTH CHECK URL = /login/verify
TID: [-1234] [] [2025-07-29 18:05:04,155]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /goform/login, HEALTH CHECK URL = /goform/login
TID: [-1234] [] [2025-07-29 18:05:32,153]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.php, HEALTH CHECK URL = /login.php
TID: [-1234] [] [2025-07-29 18:07:12,142]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.php, HEALTH CHECK URL = /login.php
TID: [-1234] [] [2025-07-29 18:08:29,146]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /setting.php, HEALTH CHECK URL = /setting.php
TID: [-1234] [] [2025-07-29 18:08:51,138]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /x_organization_assemble_authentication/jaxrs/authentication/captcha, HEALTH CHECK URL = /x_organization_assemble_authentication/jaxrs/authentication/captcha
TID: [-1234] [] [2025-07-29 18:09:11,136]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backend/backend/auth/signin, HEALTH CHECK URL = /backend/backend/auth/signin
TID: [-1234] [] [2025-07-29 18:09:38,139]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /control/login, HEALTH CHECK URL = /control/login
TID: [-1234] [] [2025-07-29 18:12:54,138]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /interface/main/main_screen.php?auth=login&site=default, HEALTH CHECK URL = /interface/main/main_screen.php?auth=login&site=default
TID: [-1234] [] [2025-07-29 18:13:43,457]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /rpc.php, HEALTH CHECK URL = /rpc.php
TID: [-1234] [] [2025-07-29 18:15:48,132]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin/configurations/parsers/Checkpoint/CHECKPOINT.php, HEALTH CHECK URL = /bin/configurations/parsers/Checkpoint/CHECKPOINT.php
TID: [-1234] [] [2025-07-29 18:16:20,133]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /auth/login, HEALTH CHECK URL = /auth/login
TID: [-1234] [] [2025-07-29 18:16:41,126]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /xmlpserver/services/XMLPService, HEALTH CHECK URL = /xmlpserver/services/XMLPService
TID: [-1234] [] [2025-07-29 18:17:03,127]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /users/login, HEALTH CHECK URL = /users/login
TID: [-1234] [] [2025-07-29 18:17:32,145]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /swarm.cgi, HEALTH CHECK URL = /swarm.cgi
TID: [-1234] [] [2025-07-29 18:20:07,119]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/auth/tokens/, HEALTH CHECK URL = /api/v1/auth/tokens/
TID: [-1234] [] [2025-07-29 18:20:28,460]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?q=common/login, HEALTH CHECK URL = /?q=common/login
TID: [-1234] [] [2025-07-29 18:20:48,150]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login, HEALTH CHECK URL = /login
TID: [-1234] [] [2025-07-29 18:21:21,119]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /inter/ajax.php?cmd=get_user_login_cmd, HEALTH CHECK URL = /inter/ajax.php?cmd=get_user_login_cmd
TID: [-1234] [] [2025-07-29 18:24:43,113]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?m=login&a=attemptLogin, HEALTH CHECK URL = /index.php?m=login&a=attemptLogin
TID: [-1234] [] [2025-07-29 18:24:43,116]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?m=login&a=attemptLogin, HEALTH CHECK URL = /index.php?m=login&a=attemptLogin
TID: [-1234] [] [2025-07-29 18:24:43,119]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?m=login&a=attemptLogin, HEALTH CHECK URL = /index.php?m=login&a=attemptLogin
TID: [-1234] [] [2025-07-29 18:24:43,120]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?m=login&a=attemptLogin, HEALTH CHECK URL = /index.php?m=login&a=attemptLogin
TID: [-1234] [] [2025-07-29 18:25:15,114]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login/userverify.cgi, HEALTH CHECK URL = /login/userverify.cgi
TID: [-1234] [] [2025-07-29 18:25:36,112]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.cgi, HEALTH CHECK URL = /login.cgi
TID: [-1234] [] [2025-07-29 18:26:07,120]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /supershell/login/auth, HEALTH CHECK URL = /supershell/login/auth
TID: [-1234] [] [2025-07-29 18:30:30,112]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/login.php, HEALTH CHECK URL = /manager/login.php
TID: [-1234] [] [2025-07-29 18:30:50,104]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /php/login.php, HEALTH CHECK URL = /php/login.php
TID: [-1234] [] [2025-07-29 18:31:11,103]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /config/pw_left_bar.html, HEALTH CHECK URL = /config/pw_left_bar.html
TID: [-1234] [] [2025-07-29 18:31:51,106]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pentaho/j_spring_security_check, HEALTH CHECK URL = /pentaho/j_spring_security_check
TID: [-1234] [] [2025-07-29 18:33:12,427]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-29 18:34:12,096]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/session, HEALTH CHECK URL = /api/v1/session
TID: [-1234] [] [2025-07-29 18:34:37,098]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php, HEALTH CHECK URL = /index.php
TID: [-1234] [] [2025-07-29 18:34:57,101]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /appInfo/assert, HEALTH CHECK URL = /appInfo/assert
TID: [-1234] [] [2025-07-29 18:36:18,096]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/signin, HEALTH CHECK URL = /api/v1/signin
TID: [-1234] [] [2025-07-29 18:36:18,104]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/signin, HEALTH CHECK URL = /api/v1/signin
TID: [-1234] [] [2025-07-29 18:36:18,108]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/signin, HEALTH CHECK URL = /api/v1/signin
TID: [-1234] [] [2025-07-29 18:39:17,090]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public/checklogin.htm, HEALTH CHECK URL = /public/checklogin.htm
TID: [-1234] [] [2025-07-29 18:41:02,089]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login, HEALTH CHECK URL = /login
TID: [-1234] [] [2025-07-29 18:41:33,096]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/whoami, HEALTH CHECK URL = /api/whoami
TID: [-1234] [] [2025-07-29 18:43:27,089]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?/AdminAppData@no-mobile-0/0/15503332983847185/, HEALTH CHECK URL = /?/AdminAppData@no-mobile-0/0/15503332983847185/
TID: [-1234] [] [2025-07-29 18:44:17,092]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /v3/settings/first-login, HEALTH CHECK URL = /v3/settings/first-login
TID: [-1234] [] [2025-07-29 18:45:22,081]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lib/crud/userprocess.php, HEALTH CHECK URL = /lib/crud/userprocess.php
TID: [-1234] [] [2025-07-29 18:45:22,085]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.php, HEALTH CHECK URL = /login.php
TID: [-1234] [] [2025-07-29 18:49:03,079]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web/guest/tw/websys/webArch/login.cgi, HEALTH CHECK URL = /web/guest/tw/websys/webArch/login.cgi
TID: [-1234] [] [2025-07-29 18:50:05,081]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/login.cgi?username=admin&password=admin, HEALTH CHECK URL = /cgi-bin/login.cgi?username=admin&password=admin
TID: [-1234] [] [2025-07-29 18:50:42,074]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login, HEALTH CHECK URL = /login
TID: [-1234] [] [2025-07-29 18:51:28,074]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?action=login.index, HEALTH CHECK URL = /index.php?action=login.index
TID: [-1234] [] [2025-07-29 18:52:04,074]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php, HEALTH CHECK URL = /index.php
TID: [-1234] [] [2025-07-29 18:53:51,074]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.cgi, HEALTH CHECK URL = /login.cgi
TID: [-1234] [] [2025-07-29 18:55:30,070]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /j_security_check, HEALTH CHECK URL = /j_security_check
TID: [-1234] [] [2025-07-29 18:56:12,064]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sws/app/gnb/login/login.jsp, HEALTH CHECK URL = /sws/app/gnb/login/login.jsp
TID: [-1234] [] [2025-07-29 18:56:44,064]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /main.ehp, HEALTH CHECK URL = /main.ehp
TID: [-1234] [] [2025-07-29 18:57:03,067]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /menu/home, HEALTH CHECK URL = /menu/home
TID: [-1234] [] [2025-07-29 18:58:01,063]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /WebConfig/lua/auth.lua, HEALTH CHECK URL = /WebConfig/lua/auth.lua
TID: [-1234] [] [2025-07-29 18:58:01,065]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /WebConfig/lua/auth.lua, HEALTH CHECK URL = /WebConfig/lua/auth.lua
TID: [-1234] [] [2025-07-29 19:00:43,062]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /op/op.Login.php, HEALTH CHECK URL = /op/op.Login.php
TID: [-1234] [] [2025-07-29 19:01:50,060]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /seeyon/rest/authentication/ucpcLogin, HEALTH CHECK URL = /seeyon/rest/authentication/ucpcLogin
TID: [-1234] [] [2025-07-29 19:02:55,060]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /seeyon/management/index.jsp, HEALTH CHECK URL = /seeyon/management/index.jsp
TID: [-1234] [] [2025-07-29 19:03:12,781]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-29 19:03:40,058]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 19:04:08,061]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /FormLogin, HEALTH CHECK URL = /FormLogin
TID: [-1234] [] [2025-07-29 19:05:34,088]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /vision/RMIServlet, HEALTH CHECK URL = /vision/RMIServlet
TID: [-1234] [] [2025-07-29 19:05:34,088]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /smartbi/vision/RMIServlet, HEALTH CHECK URL = /smartbi/vision/RMIServlet
TID: [-1234] [] [2025-07-29 19:06:47,056]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/default/, HEALTH CHECK URL = /admin/default/
TID: [-1234] [] [2025-07-29 19:08:23,049]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /SolarWinds/InformationService/v3/Json/Query?query=SELECT+Uri+FROM+Orion.Pollers+ORDER+BY+PollerID+WITH+ROWS+1+TO+3+WITH+TOTALROWS, HEALTH CHECK URL = /SolarWinds/InformationService/v3/Json/Query?query=SELECT+Uri+FROM+Orion.Pollers+ORDER+BY+PollerID+WITH+ROWS+1+TO+3+WITH+TOTALROWS
TID: [-1234] [] [2025-07-29 19:08:24,052]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /InformationService/v3/Json/Query?query=SELECT+Uri+FROM+Orion.Pollers+ORDER+BY+PollerID+WITH+ROWS+1+TO+3+WITH+TOTALROWS, HEALTH CHECK URL = /InformationService/v3/Json/Query?query=SELECT+Uri+FROM+Orion.Pollers+ORDER+BY+PollerID+WITH+ROWS+1+TO+3+WITH+TOTALROWS
TID: [-1234] [] [2025-07-29 19:09:36,287]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 034fbc66-0a9b-43e2-9ba7-d2a5319d4b2a
TID: [-1234] [] [2025-07-29 19:10:28,044]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/authentication/login, HEALTH CHECK URL = /api/authentication/login
TID: [-1234] [] [2025-07-29 19:10:28,046]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/authentication/login, HEALTH CHECK URL = /api/authentication/login
TID: [-1234] [] [2025-07-29 19:10:28,050]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/authentication/login, HEALTH CHECK URL = /api/authentication/login
TID: [-1234] [] [2025-07-29 19:10:28,053]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/authentication/login, HEALTH CHECK URL = /api/authentication/login
TID: [-1234] [] [2025-07-29 19:11:04,052]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /process/login.php, HEALTH CHECK URL = /process/login.php
TID: [-1234] [] [2025-07-29 19:12:41,041]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /users/login, HEALTH CHECK URL = /users/login
TID: [-1234] [] [2025-07-29 19:15:04,058]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /en-US/account/login?return_to=%2Fen-US%2Faccount%2F, HEALTH CHECK URL = /en-US/account/login?return_to=%2Fen-US%2Faccount%2F
TID: [-1234] [] [2025-07-29 19:17:47,040]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /signin, HEALTH CHECK URL = /signin
TID: [-1234] [] [2025-07-29 19:18:37,038]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi/login.cgi, HEALTH CHECK URL = /cgi/login.cgi
TID: [-1234] [] [2025-07-29 19:18:37,039]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi/login.cgi, HEALTH CHECK URL = /cgi/login.cgi
TID: [-1234] [] [2025-07-29 19:19:57,032]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login?arg1=YWRtaW4%3D&arg2=dGltZWtlZXBlcg%3D%3D, HEALTH CHECK URL = /login?arg1=YWRtaW4%3D&arg2=dGltZWtlZXBlcg%3D%3D
TID: [-1234] [] [2025-07-29 19:22:38,032]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 19:23:18,033]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /php/utils/CmsGetDeviceSoftwareVersion.php/.js.map, HEALTH CHECK URL = /php/utils/CmsGetDeviceSoftwareVersion.php/.js.map
TID: [-1234] [] [2025-07-29 19:24:05,024]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/authenticate, HEALTH CHECK URL = /api/authenticate
TID: [-1234] [] [2025-07-29 19:24:11,025]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 19:25:20,026]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 19:25:44,024]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?p, HEALTH CHECK URL = /?p
TID: [-1234] [] [2025-07-29 19:26:10,039]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /userRpm/MenuRpm.htm, HEALTH CHECK URL = /userRpm/MenuRpm.htm
TID: [-1234] [] [2025-07-29 19:28:03,023]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /userRpm/LoginRpm.htm?Save=Save, HEALTH CHECK URL = /userRpm/LoginRpm.htm?Save=Save
TID: [-1234] [] [2025-07-29 19:28:56,016]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login, HEALTH CHECK URL = /login
TID: [-1234] [] [2025-07-29 19:31:12,016]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/auth/login, HEALTH CHECK URL = /api/auth/login
TID: [-1234] [] [2025-07-29 19:32:53,017]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 19:33:12,964]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-29 19:33:47,568]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20250729&denNgay=20250729&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20250729&denNgay=20250729&maTthc=
TID: [-1234] [] [2025-07-29 19:33:47,605]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2025-07-29 19:34:13,012]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /versa/login.html, HEALTH CHECK URL = /versa/login.html
TID: [-1234] [] [2025-07-29 19:34:13,019]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /versa/login, HEALTH CHECK URL = /versa/login
TID: [-1234] [] [2025-07-29 19:34:41,013]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/ikon_button.exe?page=1500&button=51&line=0&eventid=0&WbmSessionId=nosession&Username=Unify&Password=1q21q2&CountryCode=en&NoSessionTimeout=false&SwitchLanguage=false, HEALTH CHECK URL = /cgi-bin/ikon_button.exe?page=1500&button=51&line=0&eventid=0&WbmSessionId=nosession&Username=Unify&Password=1q21q2&CountryCode=en&NoSessionTimeout=false&SwitchLanguage=false
TID: [-1234] [] [2025-07-29 19:35:05,016]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /authenticate, HEALTH CHECK URL = /authenticate
TID: [-1234] [] [2025-07-29 19:35:41,008]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ViewPoint/admin/Site/ViewPointLogin, HEALTH CHECK URL = /ViewPoint/admin/Site/ViewPointLogin
TID: [-1234] [] [2025-07-29 19:37:14,009]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /VisionHubWebApi/api/Login, HEALTH CHECK URL = /VisionHubWebApi/api/Login
TID: [-1234] [] [2025-07-29 19:39:33,085]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8b8f8363-76cb-460d-9bf4-fe84e990cb7f
TID: [-1234] [] [2025-07-29 19:39:41,012]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wbm/login.php, HEALTH CHECK URL = /wbm/login.php
TID: [-1234] [] [2025-07-29 19:41:08,004]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.cgi, HEALTH CHECK URL = /login.cgi
TID: [-1234] [] [2025-07-29 19:42:24,008]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app/login, HEALTH CHECK URL = /app/login
TID: [-1234] [] [2025-07-29 19:47:23,997]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /console/j_security_check, HEALTH CHECK URL = /console/j_security_check
TID: [-1234] [] [2025-07-29 19:47:23,999]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /console/j_security_check, HEALTH CHECK URL = /console/j_security_check
TID: [-1234] [] [2025-07-29 19:47:24,002]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /console/, HEALTH CHECK URL = /console/
TID: [-1234] [] [2025-07-29 19:47:24,002]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /console/, HEALTH CHECK URL = /console/
TID: [-1234] [] [2025-07-29 19:47:24,003]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /console/j_security_check, HEALTH CHECK URL = /console/j_security_check
TID: [-1234] [] [2025-07-29 19:47:24,004]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /console/, HEALTH CHECK URL = /console/
TID: [-1234] [] [2025-07-29 19:47:24,004]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /console/, HEALTH CHECK URL = /console/
TID: [-1234] [] [2025-07-29 19:47:24,004]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /console/j_security_check, HEALTH CHECK URL = /console/j_security_check
TID: [-1234] [] [2025-07-29 19:47:24,004]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /console/, HEALTH CHECK URL = /console/
TID: [-1234] [] [2025-07-29 19:47:24,006]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /console/, HEALTH CHECK URL = /console/
TID: [-1234] [] [2025-07-29 19:47:24,007]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /console/j_security_check, HEALTH CHECK URL = /console/j_security_check
TID: [-1234] [] [2025-07-29 19:47:24,007]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /console/j_security_check, HEALTH CHECK URL = /console/j_security_check
TID: [-1234] [] [2025-07-29 19:47:24,009]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /console/j_security_check, HEALTH CHECK URL = /console/j_security_check
TID: [-1234] [] [2025-07-29 19:47:24,009]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /console/, HEALTH CHECK URL = /console/
TID: [-1234] [] [2025-07-29 19:47:24,009]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /console/, HEALTH CHECK URL = /console/
TID: [-1234] [] [2025-07-29 19:47:24,009]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /console/j_security_check, HEALTH CHECK URL = /console/j_security_check
TID: [-1234] [] [2025-07-29 19:56:58,977]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /session_login.cgi, HEALTH CHECK URL = /session_login.cgi
TID: [-1234] [] [2025-07-29 19:56:58,981]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sysinfo.cgi, HEALTH CHECK URL = /sysinfo.cgi
TID: [-1234] [] [2025-07-29 20:03:13,167]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-29 20:04:09,967]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.php?action=login&type=admin, HEALTH CHECK URL = /login.php?action=login&type=admin
TID: [-1234] [] [2025-07-29 20:04:32,966]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /management, HEALTH CHECK URL = /management
TID: [-1234] [] [2025-07-29 20:08:10,960]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /carbon/admin/login_action.jsp, HEALTH CHECK URL = /carbon/admin/login_action.jsp
TID: [-1234] [] [2025-07-29 20:08:46,958]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /userpost/xerox.set, HEALTH CHECK URL = /userpost/xerox.set
TID: [-1234] [] [2025-07-29 20:09:27,959]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login, HEALTH CHECK URL = /login
TID: [-1234] [] [2025-07-29 20:10:07,958]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login, HEALTH CHECK URL = /login
TID: [-1234] [] [2025-07-29 20:11:54,959]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login, HEALTH CHECK URL = /login
TID: [-1234] [] [2025-07-29 20:12:48,958]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /xxl-job-admin/login, HEALTH CHECK URL = /xxl-job-admin/login
TID: [-1234] [] [2025-07-29 20:12:48,958]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login, HEALTH CHECK URL = /login
TID: [-1234] [] [2025-07-29 20:13:13,954]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login, HEALTH CHECK URL = /login
TID: [-1234] [] [2025-07-29 20:13:33,956]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/auth/login, HEALTH CHECK URL = /api/auth/login
TID: [-1234] [] [2025-07-29 20:14:56,953]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/auth/login?p=Login&t=1, HEALTH CHECK URL = /api/auth/login?p=Login&t=1
TID: [-1234] [] [2025-07-29 20:14:57,954]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/auth/login?p=Login&t=1, HEALTH CHECK URL = /api/auth/login?p=Login&t=1
TID: [-1234] [] [2025-07-29 20:17:28,950]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 20:18:14,951]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php, HEALTH CHECK URL = /index.php
TID: [-1234] [] [2025-07-29 20:18:14,951]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /zabbix/index.php, HEALTH CHECK URL = /zabbix/index.php
TID: [-1234] [] [2025-07-29 20:20:47,951]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ZMC_Admin_Login, HEALTH CHECK URL = /ZMC_Admin_Login
TID: [-1234] [] [2025-07-29 20:22:53,947]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.do, HEALTH CHECK URL = /index.do
TID: [-1234] [] [2025-07-29 20:26:50,940]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.development.local, HEALTH CHECK URL = /.env.development.local
TID: [-1234] [] [2025-07-29 20:26:50,940]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.production.local, HEALTH CHECK URL = /.env.production.local
TID: [-1234] [] [2025-07-29 20:26:50,940]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env, HEALTH CHECK URL = /.env
TID: [-1234] [] [2025-07-29 20:26:50,940]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.prod.local, HEALTH CHECK URL = /.env.prod.local
TID: [-1234] [] [2025-07-29 20:26:50,941]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.dev.local, HEALTH CHECK URL = /.env.dev.local
TID: [-1234] [] [2025-07-29 20:26:51,934]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env_sample, HEALTH CHECK URL = /.env_sample
TID: [-1234] [] [2025-07-29 20:26:51,937]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.local, HEALTH CHECK URL = /.env.local
TID: [-1234] [] [2025-07-29 20:26:52,932]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.example, HEALTH CHECK URL = /.env.example
TID: [-1234] [] [2025-07-29 20:26:52,935]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.old, HEALTH CHECK URL = /.env.old
TID: [-1234] [] [2025-07-29 20:26:52,935]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env_1, HEALTH CHECK URL = /.env_1
TID: [-1234] [] [2025-07-29 20:26:52,937]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.stage, HEALTH CHECK URL = /.env.stage
TID: [-1234] [] [2025-07-29 20:26:53,934]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.live, HEALTH CHECK URL = /.env.live
TID: [-1234] [] [2025-07-29 20:27:51,938]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /j_security_check, HEALTH CHECK URL = /j_security_check
TID: [-1234] [] [2025-07-29 20:33:48,923]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.production.local, HEALTH CHECK URL = /.env.production.local
TID: [-1234] [] [2025-07-29 20:33:48,925]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.local, HEALTH CHECK URL = /.env.local
TID: [-1234] [] [2025-07-29 20:33:48,928]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.example, HEALTH CHECK URL = /.env.example
TID: [-1234] [] [2025-07-29 20:33:49,927]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.bak, HEALTH CHECK URL = /.env.bak
TID: [-1234] [] [2025-07-29 20:33:49,929]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env, HEALTH CHECK URL = /.env
TID: [-1234] [] [2025-07-29 20:33:50,925]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.dev, HEALTH CHECK URL = /.env.dev
TID: [-1234] [] [2025-07-29 20:33:50,925]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.prod, HEALTH CHECK URL = /.env.prod
TID: [-1234] [] [2025-07-29 20:33:50,927]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.dev.local, HEALTH CHECK URL = /.env.dev.local
TID: [-1234] [] [2025-07-29 20:33:50,930]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.prod.local, HEALTH CHECK URL = /.env.prod.local
TID: [-1234] [] [2025-07-29 20:33:50,934]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.development.local, HEALTH CHECK URL = /.env.development.local
TID: [-1234] [] [2025-07-29 20:33:51,926]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.live, HEALTH CHECK URL = /.env.live
TID: [-1234] [] [2025-07-29 20:33:53,920]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.stage, HEALTH CHECK URL = /.env.stage
TID: [-1234] [] [2025-07-29 20:33:53,921]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.production, HEALTH CHECK URL = /.env.production
TID: [-1234] [] [2025-07-29 20:33:54,931]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.backup, HEALTH CHECK URL = /.env.backup
TID: [-1234] [] [2025-07-29 20:33:57,925]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.save, HEALTH CHECK URL = /.env.save
TID: [-1234] [] [2025-07-29 20:33:58,919]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.www, HEALTH CHECK URL = /.env.www
TID: [-1234] [] [2025-07-29 20:33:58,922]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env_sample, HEALTH CHECK URL = /.env_sample
TID: [-1234] [] [2025-07-29 20:33:58,925]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.old, HEALTH CHECK URL = /.env.old
TID: [-1234] [] [2025-07-29 20:33:58,926]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env_1, HEALTH CHECK URL = /.env_1
TID: [-1234] [] [2025-07-29 20:33:58,927]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.agm, HEALTH CHECK URL = /.env.agm
TID: [-1234] [] [2025-07-29 20:33:58,928]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.haiduong, HEALTH CHECK URL = /.env.haiduong
TID: [-1234] [] [2025-07-29 20:33:58,929]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/.env, HEALTH CHECK URL = /api/.env
TID: [-1234] [] [2025-07-29 20:35:45,922]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /WEB_VMS/LEVEL15/, HEALTH CHECK URL = /WEB_VMS/LEVEL15/
TID: [-1234] [] [2025-07-29 20:35:58,979]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20250729&denNgay=20250729&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20250729&denNgay=20250729&maTthc=
TID: [-1234] [] [2025-07-29 20:35:59,017]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2025-07-29 20:36:21,851]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-29 20:39:51,916]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /showresource.do?group=All&method=showResourceTypes&monitor_viewtype=categoryview, HEALTH CHECK URL = /showresource.do?group=All&method=showResourceTypes&monitor_viewtype=categoryview
TID: [-1234] [] [2025-07-29 21:03:49,893]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /id_rsa_1024, HEALTH CHECK URL = /id_rsa_1024
TID: [-1234] [] [2025-07-29 21:03:50,886]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /localhost.key, HEALTH CHECK URL = /localhost.key
TID: [-1234] [] [2025-07-29 21:03:50,887]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /id_rsa_2048, HEALTH CHECK URL = /id_rsa_2048
TID: [-1234] [] [2025-07-29 21:03:51,875]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ssl/localhost.key, HEALTH CHECK URL = /ssl/localhost.key
TID: [-1234] [] [2025-07-29 21:03:51,879]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /my.key, HEALTH CHECK URL = /my.key
TID: [-1234] [] [2025-07-29 21:03:51,885]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ssl/agm.haiduong.gov.vn.key, HEALTH CHECK URL = /ssl/agm.haiduong.gov.vn.key
TID: [-1234] [] [2025-07-29 21:03:51,885]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.key, HEALTH CHECK URL = /www.key
TID: [-1234] [] [2025-07-29 21:03:51,886]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /private-key, HEALTH CHECK URL = /private-key
TID: [-1234] [] [2025-07-29 21:03:51,886]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /host.key, HEALTH CHECK URL = /host.key
TID: [-1234] [] [2025-07-29 21:03:51,888]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /server.key, HEALTH CHECK URL = /server.key
TID: [-1234] [] [2025-07-29 21:03:51,888]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /key.pem, HEALTH CHECK URL = /key.pem
TID: [-1234] [] [2025-07-29 21:03:52,889]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /id_rsa, HEALTH CHECK URL = /id_rsa
TID: [-1234] [] [2025-07-29 21:03:52,894]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /id_dsa, HEALTH CHECK URL = /id_dsa
TID: [-1234] [] [2025-07-29 21:03:56,886]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /privatekey.key, HEALTH CHECK URL = /privatekey.key
TID: [-1234] [] [2025-07-29 21:04:01,878]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /id_rsa_3072, HEALTH CHECK URL = /id_rsa_3072
TID: [-1234] [] [2025-07-29 21:04:06,875]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /id_rsa_4096, HEALTH CHECK URL = /id_rsa_4096
TID: [-1234] [] [2025-07-29 21:04:08,877]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /id_ed25519, HEALTH CHECK URL = /id_ed25519
TID: [-1234] [] [2025-07-29 21:04:09,878]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.ssh/id_rsa, HEALTH CHECK URL = /.ssh/id_rsa
TID: [-1234] [] [2025-07-29 21:04:15,881]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.ssh/id_dsa, HEALTH CHECK URL = /.ssh/id_dsa
TID: [-1234] [] [2025-07-29 21:04:15,883]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.key, HEALTH CHECK URL = /agm.haiduong.gov.vn.key
TID: [-1234] [] [2025-07-29 21:04:15,883]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.ssh/id_rsa_1024, HEALTH CHECK URL = /.ssh/id_rsa_1024
TID: [-1234] [] [2025-07-29 21:04:15,888]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.ssh/id_rsa_2048, HEALTH CHECK URL = /.ssh/id_rsa_2048
TID: [-1234] [] [2025-07-29 21:04:15,889]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.ssh/id_rsa_3072, HEALTH CHECK URL = /.ssh/id_rsa_3072
TID: [-1234] [] [2025-07-29 21:04:15,889]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.ssh/id_rsa_4096, HEALTH CHECK URL = /.ssh/id_rsa_4096
TID: [-1234] [] [2025-07-29 21:04:15,889]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.ssh/id_ed25519, HEALTH CHECK URL = /.ssh/id_ed25519
TID: [-1234] [] [2025-07-29 21:06:21,937]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-29 21:09:38,876]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /application.ini, HEALTH CHECK URL = /application.ini
TID: [-1234] [] [2025-07-29 21:09:42,872]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /application/configs/application.ini, HEALTH CHECK URL = /application/configs/application.ini
TID: [-1234] [] [2025-07-29 21:09:45,869]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/configs/application.ini, HEALTH CHECK URL = /admin/configs/application.ini
TID: [-1234] [] [2025-07-29 21:09:49,877]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Partners/application/configs/application.ini, HEALTH CHECK URL = /Partners/application/configs/application.ini
TID: [-1234] [] [2025-07-29 21:09:49,881]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cms/application/configs/application.ini, HEALTH CHECK URL = /cms/application/configs/application.ini
TID: [-1234] [] [2025-07-29 21:09:49,882]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /moto/application/configs/application.ini, HEALTH CHECK URL = /moto/application/configs/application.ini
TID: [-1234] [] [2025-07-29 21:09:50,877]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /aplicacao/application/configs/application.ini, HEALTH CHECK URL = /aplicacao/application/configs/application.ini
TID: [-1234] [] [2025-07-29 21:09:51,879]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /radio/application/configs/application.ini, HEALTH CHECK URL = /radio/application/configs/application.ini
TID: [-1234] [] [2025-07-29 21:09:52,872]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cloudexp/application/configs/application.ini, HEALTH CHECK URL = /cloudexp/application/configs/application.ini
TID: [-1234] [] [2025-07-29 21:09:56,870]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /shop/application/configs/application.ini, HEALTH CHECK URL = /shop/application/configs/application.ini
TID: [-1234] [] [2025-07-29 21:09:57,872]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /seminovos/application/configs/application.ini, HEALTH CHECK URL = /seminovos/application/configs/application.ini
TID: [-1234] [] [2025-07-29 21:09:59,869]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /slr/application/configs/application.ini, HEALTH CHECK URL = /slr/application/configs/application.ini
TID: [-1234] [] [2025-07-29 21:10:00,871]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /site_cg/application/configs/application.ini, HEALTH CHECK URL = /site_cg/application/configs/application.ini
TID: [-1234] [] [2025-07-29 21:13:10,873]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /crl/, HEALTH CHECK URL = /crl/
TID: [-1234] [] [2025-07-29 21:13:10,874]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 21:13:10,874]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /PKI/, HEALTH CHECK URL = /PKI/
TID: [-1234] [] [2025-07-29 21:13:10,875]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.well-known/pki-validation/, HEALTH CHECK URL = /.well-known/pki-validation/
TID: [-1234] [] [2025-07-29 21:13:10,875]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CertSrv/, HEALTH CHECK URL = /CertSrv/
TID: [-1234] [] [2025-07-29 21:13:10,876]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /certsrv/, HEALTH CHECK URL = /certsrv/
TID: [-1234] [] [2025-07-29 21:13:10,875]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pki/, HEALTH CHECK URL = /pki/
TID: [-1234] [] [2025-07-29 21:13:10,875]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CRL/, HEALTH CHECK URL = /CRL/
TID: [-1234] [] [2025-07-29 21:13:11,874]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CertEnroll/, HEALTH CHECK URL = /CertEnroll/
TID: [-1234] [] [2025-07-29 21:13:11,881]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ocsp/, HEALTH CHECK URL = /ocsp/
TID: [-1234] [] [2025-07-29 21:19:24,864]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.pem, HEALTH CHECK URL = /agm.haiduong.gov.vn.pem
TID: [-1234] [] [2025-07-29 21:19:25,870]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jwt/private.pem, HEALTH CHECK URL = /jwt/private.pem
TID: [-1234] [] [2025-07-29 21:19:25,876]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /config/jwt/private.pem, HEALTH CHECK URL = /config/jwt/private.pem
TID: [-1234] [] [2025-07-29 21:19:26,861]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ssl/agm.haiduong.gov.vn_key.txt, HEALTH CHECK URL = /ssl/agm.haiduong.gov.vn_key.txt
TID: [-1234] [] [2025-07-29 21:19:26,861]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ssl.txt, HEALTH CHECK URL = /ssl.txt
TID: [-1234] [] [2025-07-29 21:19:26,862]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /certificates/agm.haiduong.gov.vn.pfx, HEALTH CHECK URL = /certificates/agm.haiduong.gov.vn.pfx
TID: [-1234] [] [2025-07-29 21:19:26,864]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /private.pem, HEALTH CHECK URL = /private.pem
TID: [-1234] [] [2025-07-29 21:19:26,864]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ssl/agm.haiduong.gov.vn.pem, HEALTH CHECK URL = /ssl/agm.haiduong.gov.vn.pem
TID: [-1234] [] [2025-07-29 21:19:26,867]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /var/jwt/private.pem, HEALTH CHECK URL = /var/jwt/private.pem
TID: [-1234] [] [2025-07-29 21:19:26,867]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /certificates/agm.haiduong.gov.vn.p12, HEALTH CHECK URL = /certificates/agm.haiduong.gov.vn.p12
TID: [-1234] [] [2025-07-29 21:19:26,868]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ssl_key.txt, HEALTH CHECK URL = /ssl_key.txt
TID: [-1234] [] [2025-07-29 21:19:27,857]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cert/agm.haiduong.gov.vn_key.txt, HEALTH CHECK URL = /cert/agm.haiduong.gov.vn_key.txt
TID: [-1234] [] [2025-07-29 21:19:27,857]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cert/haiduong.gov.vn_key.txt, HEALTH CHECK URL = /cert/haiduong.gov.vn_key.txt
TID: [-1234] [] [2025-07-29 21:19:30,856]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cert/agm.haiduong.gov.vn.txt, HEALTH CHECK URL = /cert/agm.haiduong.gov.vn.txt
TID: [-1234] [] [2025-07-29 21:19:35,859]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ssl/private/agm.haiduong.gov.vn_key.pem, HEALTH CHECK URL = /ssl/private/agm.haiduong.gov.vn_key.pem
TID: [-1234] [] [2025-07-29 21:19:39,901]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /certs/agm.haiduong.gov.vn_private.key, HEALTH CHECK URL = /certs/agm.haiduong.gov.vn_private.key
TID: [-1234] [] [2025-07-29 21:19:41,864]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /certs/agm.haiduong.gov.vn.key, HEALTH CHECK URL = /certs/agm.haiduong.gov.vn.key
TID: [-1234] [] [2025-07-29 21:19:42,862]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /certificates/agm.haiduong.gov.vn_priv.pem, HEALTH CHECK URL = /certificates/agm.haiduong.gov.vn_priv.pem
TID: [-1234] [] [2025-07-29 21:19:47,856]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /certs/agm.haiduong.gov.vn.pem, HEALTH CHECK URL = /certs/agm.haiduong.gov.vn.pem
TID: [-1234] [] [2025-07-29 21:19:47,857]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /keys/agm.haiduong.gov.vn.pem, HEALTH CHECK URL = /keys/agm.haiduong.gov.vn.pem
TID: [-1234] [] [2025-07-29 21:19:47,858]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /certificates/agm.haiduong.gov.vn_privkey.pem, HEALTH CHECK URL = /certificates/agm.haiduong.gov.vn_privkey.pem
TID: [-1234] [] [2025-07-29 21:19:47,861]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /private/agm.haiduong.gov.vn.key, HEALTH CHECK URL = /private/agm.haiduong.gov.vn.key
TID: [-1234] [] [2025-07-29 21:25:20,398]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 659f0b24-5bfd-48f9-b0c3-e4484404c352
TID: [-1234] [] [2025-07-29 21:36:22,067]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-29 22:04:26,794]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 22:04:54,818]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /auth/login, HEALTH CHECK URL = /auth/login
TID: [-1234] [] [2025-07-29 22:05:28,043]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 22:05:48,793]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mbilling/index.php/authentication/login, HEALTH CHECK URL = /mbilling/index.php/authentication/login
TID: [-1234] [] [2025-07-29 22:06:08,792]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mbilling/index.php/authentication/login, HEALTH CHECK URL = /mbilling/index.php/authentication/login
TID: [-1234] [] [2025-07-29 22:06:22,461]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-29 22:06:49,035]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php?action=wp01_generate_zip_archive, HEALTH CHECK URL = /wp-admin/admin-ajax.php?action=wp01_generate_zip_archive
TID: [-1234] [] [2025-07-29 22:07:26,796]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 22:07:51,787]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 22:08:11,788]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 22:08:59,528]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/login, HEALTH CHECK URL = /admin/login
TID: [-1234] [] [2025-07-29 22:09:33,148]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 12343935-31e0-4d60-9de7-5b7b378fe8b5
TID: [-1234] [] [2025-07-29 22:09:53,786]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /druid/submitLogin, HEALTH CHECK URL = /druid/submitLogin
TID: [-1234] [] [2025-07-29 22:11:41,778]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /invoke/pub.file/getFile, HEALTH CHECK URL = /invoke/pub.file/getFile
TID: [-1234] [] [2025-07-29 22:12:20,010]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 22:12:40,089]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 22:13:01,893]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 22:13:23,984]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-29 22:23:54,763]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cps/test_backup_server?ACTION=TEST_IP&NOCONTINUE=TRUE, HEALTH CHECK URL = /cps/test_backup_server?ACTION=TEST_IP&NOCONTINUE=TRUE
TID: [-1234] [] [2025-07-29 22:24:42,290]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9b157e1f-b57d-40a4-9681-804372a2eed1
TID: [-1234] [] [2025-07-29 22:27:10,760]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mcmadmin, HEALTH CHECK URL = /mcmadmin
TID: [-1234] [] [2025-07-29 22:27:10,760]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /miscadmin, HEALTH CHECK URL = /miscadmin
TID: [-1234] [] [2025-07-29 22:27:10,761]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /miscadmin, HEALTH CHECK URL = /miscadmin
TID: [-1234] [] [2025-07-29 22:27:10,761]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /miscadmin, HEALTH CHECK URL = /miscadmin
TID: [-1234] [] [2025-07-29 22:27:11,753]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /miscadmin, HEALTH CHECK URL = /miscadmin
TID: [-1234] [] [2025-07-29 22:27:11,753]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /miscadmin, HEALTH CHECK URL = /miscadmin
TID: [-1234] [] [2025-07-29 22:27:11,757]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /miscadmin, HEALTH CHECK URL = /miscadmin
TID: [-1234] [] [2025-07-29 22:27:11,757]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /miscadmin, HEALTH CHECK URL = /miscadmin
TID: [-1234] [] [2025-07-29 22:27:11,761]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /miscadmin, HEALTH CHECK URL = /miscadmin
TID: [-1234] [] [2025-07-29 22:33:46,743]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 22:33:46,764]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 22:34:01,761]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 22:34:02,747]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 22:34:04,750]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 22:34:54,746]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 22:35:17,751]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 22:35:23,750]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 22:35:25,746]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 22:36:01,743]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 22:36:01,746]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 22:36:01,749]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 22:36:22,874]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-29 22:45:00,732]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 22:45:07,733]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 22:45:22,735]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 22:45:22,736]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 22:45:23,734]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 22:45:41,728]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 22:46:15,726]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 22:46:44,731]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 22:46:46,728]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 22:47:09,728]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 22:47:22,724]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 22:47:22,728]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 22:47:22,729]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 22:55:48,716]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 22:55:49,719]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 22:56:04,719]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 22:56:04,725]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 22:56:06,717]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 22:56:48,716]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 22:57:20,715]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 22:57:28,719]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 22:57:28,719]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 22:58:03,719]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 22:58:03,729]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 22:58:04,713]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 22:58:31,727]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/session, HEALTH CHECK URL = /api/session
TID: [-1234] [] [2025-07-29 23:04:23,711]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:04:29,706]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:04:45,704]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:04:45,704]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:04:46,702]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:05:04,713]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:05:38,703]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:06:07,705]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:06:09,706]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:06:22,969]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-29 23:06:32,704]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:06:44,706]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:06:45,719]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:06:45,721]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:10:14,137] ERROR {org.apache.catalina.core.ContainerBase.[Catalina].[localhost].[/].[bridgeservlet]} - Servlet.service() for servlet [bridgeservlet] in context with path [/] threw exception org.wso2.carbon.identity.base.IdentityRuntimeException: Invalid tenant domain null
	at org.wso2.carbon.identity.base.IdentityRuntimeException.error(IdentityRuntimeException.java:63)
	at org.wso2.carbon.identity.core.util.IdentityTenantUtil.getTenantId(IdentityTenantUtil.java:269)
	at org.wso2.carbon.identity.application.authentication.framework.util.LoginContextManagementUtil.getRelyingPartyRedirectUrl(LoginContextManagementUtil.java:121)
	at org.wso2.carbon.identity.application.authentication.framework.util.LoginContextManagementUtil.handleLoginContext(LoginContextManagementUtil.java:79)
	at org.wso2.carbon.identity.application.authentication.framework.servlet.LoginContextServlet.doGet(LoginContextServlet.java:39)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:670)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779)
	at org.eclipse.equinox.http.helper.ContextPathServletAdaptor.service(ContextPathServletAdaptor.java:37)
	at org.eclipse.equinox.http.servlet.internal.ServletRegistration.service(ServletRegistration.java:61)
	at org.eclipse.equinox.http.servlet.internal.ProxyServlet.processAlias(ProxyServlet.java:128)
	at org.eclipse.equinox.http.servlet.internal.ProxyServlet.service(ProxyServlet.java:60)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779)
	at org.wso2.carbon.tomcat.ext.servlet.DelegationServlet.service(DelegationServlet.java:68)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.owasp.csrfguard.CsrfGuardFilter.doFilter(CsrfGuardFilter.java:72)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.wso2.carbon.tomcat.ext.filter.CharacterSetFilter.doFilter(CharacterSetFilter.java:65)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.filters.HttpHeaderSecurityFilter.doFilter(HttpHeaderSecurityFilter.java:126)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:177)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.wso2.carbon.identity.context.rewrite.valve.TenantContextRewriteValve.invoke(TenantContextRewriteValve.java:86)
	at org.wso2.carbon.identity.authz.valve.AuthorizationValve.invoke(AuthorizationValve.java:110)
	at org.wso2.carbon.identity.auth.valve.AuthenticationValve.invoke(AuthenticationValve.java:111)
	at org.wso2.carbon.tomcat.ext.valves.CompositeValve.continueInvocation(CompositeValve.java:101)
	at org.wso2.carbon.tomcat.ext.valves.TomcatValveContainer.invokeValves(TomcatValveContainer.java:49)
	at org.wso2.carbon.tomcat.ext.valves.CompositeValve.invoke(CompositeValve.java:62)
	at org.wso2.carbon.tomcat.ext.valves.CarbonStuckThreadDetectionValve.invoke(CarbonStuckThreadDetectionValve.java:145)
	at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:687)
	at org.wso2.carbon.tomcat.ext.valves.CarbonContextCreatorValve.invoke(CarbonContextCreatorValve.java:59)
	at org.wso2.carbon.tomcat.ext.valves.RequestCorrelationIdValve.invoke(RequestCorrelationIdValve.java:124)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:891)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1784)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2025-07-29 23:13:10,701]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:13:11,693]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:13:26,686]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:13:26,693]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:13:28,690]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:14:10,695]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:14:42,691]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:14:50,701]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:14:50,703]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:15:25,703]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:15:25,703]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:15:26,688]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:21:41,688]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:21:47,683]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:22:03,688]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:22:03,689]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:22:04,677]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:22:22,693]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:22:56,676]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:23:25,683]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:23:27,675]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:23:50,674]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:24:02,685]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:24:03,682]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:24:03,686]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:24:37,679]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/webproc, HEALTH CHECK URL = /cgi-bin/webproc
TID: [-1234] [] [2025-07-29 23:30:28,672]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:30:29,664]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:30:44,668]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:30:44,668]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:30:46,662]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:31:28,667]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:32:00,665]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:32:08,671]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:32:08,671]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:32:43,668]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:32:43,669]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:32:45,066]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:36:23,220]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-29 23:39:03,654]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:39:09,668]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:39:25,649]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:39:25,655]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:39:26,659]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:39:44,660]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:40:18,649]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:40:47,648]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:40:49,649]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:41:12,685]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:41:24,662]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:41:25,650]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:41:25,657]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:42:49,651]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:42:50,991]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?5Shaj=x, HEALTH CHECK URL = /nuclei.svg?5Shaj=x
TID: [-1234] [] [2025-07-29 23:46:52,644]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?SPX_KEY=testing&SPX_UI_URI=/, HEALTH CHECK URL = /?SPX_KEY=testing&SPX_UI_URI=/
TID: [-1234] [] [2025-07-29 23:46:55,645]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?SPX_KEY=prod&SPX_UI_URI=/, HEALTH CHECK URL = /?SPX_KEY=prod&SPX_UI_URI=/
TID: [-1234] [] [2025-07-29 23:46:55,646]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?SPX_KEY=test&SPX_UI_URI=/, HEALTH CHECK URL = /?SPX_KEY=test&SPX_UI_URI=/
TID: [-1234] [] [2025-07-29 23:46:55,647]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?SPX_KEY=stg&SPX_UI_URI=/, HEALTH CHECK URL = /?SPX_KEY=stg&SPX_UI_URI=/
TID: [-1234] [] [2025-07-29 23:46:55,647]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?SPX_KEY=production&SPX_UI_URI=/, HEALTH CHECK URL = /?SPX_KEY=production&SPX_UI_URI=/
TID: [-1234] [] [2025-07-29 23:46:55,648]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?SPX_KEY=devel&SPX_UI_URI=/, HEALTH CHECK URL = /?SPX_KEY=devel&SPX_UI_URI=/
TID: [-1234] [] [2025-07-29 23:46:55,647]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?SPX_KEY=spx&SPX_UI_URI=/, HEALTH CHECK URL = /?SPX_KEY=spx&SPX_UI_URI=/
TID: [-1234] [] [2025-07-29 23:46:55,648]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?SPX_KEY=staging&SPX_UI_URI=/, HEALTH CHECK URL = /?SPX_KEY=staging&SPX_UI_URI=/
TID: [-1234] [] [2025-07-29 23:46:55,648]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?SPX_KEY=dev&SPX_UI_URI=/, HEALTH CHECK URL = /?SPX_KEY=dev&SPX_UI_URI=/
TID: [-1234] [] [2025-07-29 23:46:55,649]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?SPX_KEY=prd&SPX_UI_URI=/, HEALTH CHECK URL = /?SPX_KEY=prd&SPX_UI_URI=/
TID: [-1234] [] [2025-07-29 23:46:55,648]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?SPX_KEY=stag&SPX_UI_URI=/, HEALTH CHECK URL = /?SPX_KEY=stag&SPX_UI_URI=/
TID: [-1234] [] [2025-07-29 23:54:31,758]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 189d269b-942b-441e-a3dc-f2e5bb12273a
