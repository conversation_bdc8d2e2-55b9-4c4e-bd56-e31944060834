TID: [-1234] [] [2024-11-16 00:00:00,096]  WARN {org.apache.synapse.commons.util.MiscellaneousUtil} - Error loading properties from a file at from the System defined location: access-log.properties
TID: [-1234] [] [2024-11-16 00:01:38,131]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-16 00:06:32,831]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 82fa85ea-c95a-4544-8fb9-6a7ced8e8418
TID: [-1234] [] [2024-11-16 00:06:33,434]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5c92f452-2474-4708-8c2c-d103660c8c8a
TID: [-1234] [] [2024-11-16 00:06:33,925]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d98fe9ed-75e5-4308-a4b6-6a79d5ae025c
TID: [-1234] [] [2024-11-16 00:06:35,782]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c8a189a0-e6cd-414d-92b5-f427d13e2649
TID: [-1234] [] [2024-11-16 00:06:41,451]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 96113fb8-22e0-4e79-814c-37bef8cd6675
TID: [-1234] [] [2024-11-16 00:06:43,621]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5367b3e4-bbce-47f9-b9bb-db7d9c9a3180
TID: [-1234] [] [2024-11-16 00:07:47,612]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241116&denNgay=20241116&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241116&denNgay=20241116&maTthc=
TID: [-1234] [] [2024-11-16 00:07:47,659]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-16 00:31:38,655]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-16 00:35:02,927]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 08c73808-28b0-47e0-b786-468005008a47
TID: [-1234] [] [2024-11-16 01:01:39,280]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-16 01:06:53,246]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bb73ee0b-905b-4a50-b7fe-685600825e79
TID: [-1234] [] [2024-11-16 01:06:56,575]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cff8d9c3-fe6b-4527-aebe-b00a8590249f
TID: [-1234] [] [2024-11-16 01:06:56,727]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8a03e0fc-3788-4ee6-b9d8-40a0c7d127da
TID: [-1234] [] [2024-11-16 01:06:58,868]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f5232250-0261-4564-a45f-36d44f602795
TID: [-1234] [] [2024-11-16 01:07:04,063]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b8ed0118-d889-4e48-a77f-8c7b861294e9
TID: [-1234] [] [2024-11-16 01:07:07,407]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1a30f7e2-6b67-4f46-aa92-92bc63fd8cd1
TID: [-1234] [] [2024-11-16 01:07:08,219]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 31d58e28-37f9-4467-a2d9-30a61a6fdf8a
TID: [-1234] [] [2024-11-16 01:31:39,948]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-16 02:01:36,058]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-11-16 02:01:36,059]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/ChuyenHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:48234, CORRELATION_ID = 0aae38ad-6995-4957-afbf-e672ce96b647, CONNECTION = http-incoming-1072807
TID: [-1234] [] [2024-11-16 02:01:36,083]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 28625b88-90bc-4ce5-8155-a8d34069745b
TID: [-1234] [] [2024-11-16 02:01:36,084]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/KetThucHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-57883, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 28625b88-90bc-4ce5-8155-a8d34069745b
TID: [-1234] [] [2024-11-16 02:01:36,085]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-11-16 02:01:36,088]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 16cbfca3-498f-4bf3-88a5-8055b8d0e1d6
TID: [-1234] [] [2024-11-16 02:01:36,088]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/KetThucHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-57882, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 16cbfca3-498f-4bf3-88a5-8055b8d0e1d6
TID: [-1234] [] [2024-11-16 02:01:36,089]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-11-16 02:01:36,092]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 0aae38ad-6995-4957-afbf-e672ce96b647
TID: [-1234] [] [2024-11-16 02:01:36,092]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/ChuyenHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-57884, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 0aae38ad-6995-4957-afbf-e672ce96b647
TID: [-1234] [] [2024-11-16 02:01:36,093]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-11-16 02:01:36,094]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 2176b7e8-fedc-4ca2-90db-54d08161a006
TID: [-1234] [] [2024-11-16 02:01:36,095]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/ChuyenHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-57885, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 2176b7e8-fedc-4ca2-90db-54d08161a006
TID: [-1234] [] [2024-11-16 02:01:36,095]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-11-16 02:01:36,120]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-1072807, CORRELATION_ID = 0aae38ad-6995-4957-afbf-e672ce96b647
TID: [-1234] [] [2024-11-16 02:01:36,459]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-11-16 02:01:36,460]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/KetThucHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:48246, CORRELATION_ID = 23fa3483-2194-48cd-8f84-ceb5abec5afd, CONNECTION = http-incoming-1072809
TID: [-1234] [] [2024-11-16 02:01:36,661]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-11-16 02:01:36,661]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/KetThucHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:48248, CORRELATION_ID = 79063900-fa0f-4da6-ad17-4fd44b3e2acf, CONNECTION = http-incoming-1072810
TID: [-1234] [] [2024-11-16 02:01:36,761]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 23fa3483-2194-48cd-8f84-ceb5abec5afd
TID: [-1234] [] [2024-11-16 02:01:36,762]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/KetThucHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-57886, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 23fa3483-2194-48cd-8f84-ceb5abec5afd
TID: [-1234] [] [2024-11-16 02:01:36,762]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-11-16 02:01:36,775]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-1072809, CORRELATION_ID = 23fa3483-2194-48cd-8f84-ceb5abec5afd
TID: [-1234] [] [2024-11-16 02:01:36,823]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 79063900-fa0f-4da6-ad17-4fd44b3e2acf
TID: [-1234] [] [2024-11-16 02:01:36,824]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/KetThucHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-57887, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 79063900-fa0f-4da6-ad17-4fd44b3e2acf
TID: [-1234] [] [2024-11-16 02:01:36,825]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-11-16 02:01:36,836]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-1072810, CORRELATION_ID = 79063900-fa0f-4da6-ad17-4fd44b3e2acf
TID: [-1234] [] [2024-11-16 02:01:37,228]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-11-16 02:01:37,229]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/KetThucHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:48258, CORRELATION_ID = 2dbfe4b9-3a2a-43d1-b030-f69fa7db6978, CONNECTION = http-incoming-1072811
TID: [-1234] [] [2024-11-16 02:01:37,367]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 2dbfe4b9-3a2a-43d1-b030-f69fa7db6978
TID: [-1234] [] [2024-11-16 02:01:37,368]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/KetThucHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-57888, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 2dbfe4b9-3a2a-43d1-b030-f69fa7db6978
TID: [-1234] [] [2024-11-16 02:01:37,368]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-11-16 02:01:37,380]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-1072811, CORRELATION_ID = 2dbfe4b9-3a2a-43d1-b030-f69fa7db6978
TID: [-1234] [] [2024-11-16 02:01:38,091]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-11-16 02:01:38,091]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/KetThucHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:48266, CORRELATION_ID = ef97b39c-d556-4b94-b3b6-8130b9328066, CONNECTION = http-incoming-1072812
TID: [-1234] [] [2024-11-16 02:01:38,305]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = ef97b39c-d556-4b94-b3b6-8130b9328066
TID: [-1234] [] [2024-11-16 02:01:38,305]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/KetThucHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-57889, SOCKET_TIMEOUT = 180000, CORRELATION_ID = ef97b39c-d556-4b94-b3b6-8130b9328066
TID: [-1234] [] [2024-11-16 02:01:38,306]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-11-16 02:01:38,320]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-1072812, CORRELATION_ID = ef97b39c-d556-4b94-b3b6-8130b9328066
TID: [-1234] [] [2024-11-16 02:01:38,755]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 211ba699-e89e-4f3f-96ba-7fb65aef77b5
TID: [-1234] [] [2024-11-16 02:01:38,756]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/KetThucHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-57891, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 211ba699-e89e-4f3f-96ba-7fb65aef77b5
TID: [-1234] [] [2024-11-16 02:01:38,757]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-11-16 02:01:40,741]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-16 02:08:29,874]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9735cf1c-bf5e-4e4c-86b0-8e6d2c9d61ec
TID: [-1234] [] [2024-11-16 02:08:32,017]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 03389521-4431-47a0-a314-3b38a59313a2
TID: [-1234] [] [2024-11-16 02:08:32,044]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cfcff069-7f59-457c-adc2-351639aae493
TID: [-1234] [] [2024-11-16 02:08:33,503]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c4eb4611-3c7e-47ff-b3f5-3679370f49f2
TID: [-1234] [] [2024-11-16 02:08:33,607]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5a8ca786-902b-4655-a753-232296711bd1
TID: [-1234] [] [2024-11-16 02:08:33,962]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 909d23bb-e06c-464e-8ccf-4f2ce8509fd6
TID: [-1234] [] [2024-11-16 02:08:37,309]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8437de71-0a03-4ebf-ae8f-c7550ed54c9e
TID: [-1234] [] [2024-11-16 02:08:38,358]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d5100648-dd13-4e25-ae79-e3db7f927529
TID: [-1234] [] [2024-11-16 02:08:41,627]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 87ccbae0-5102-4baa-a2c3-3abb494c46f3
TID: [-1234] [] [2024-11-16 02:08:43,360]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6664827f-9ab9-4c91-ad92-fb94c7f8f284
TID: [-1234] [] [2024-11-16 02:31:40,994]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-16 02:34:57,255]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0893ae23-c76c-4fe8-a8fd-536f3e7cd651
TID: [-1234] [] [2024-11-16 03:01:41,450]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-16 03:06:57,931]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e48bb005-a387-4247-a804-6fbfd0ce4350
TID: [-1234] [] [2024-11-16 03:06:58,300]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 744ed366-d23e-4c1d-91bc-1002e0d1827f
TID: [-1234] [] [2024-11-16 03:06:58,749]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e04a41b3-5760-445e-a4ca-ae48cc16f768
TID: [-1234] [] [2024-11-16 03:06:59,307]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9731a44b-be0b-4137-a2a8-272ea60c0796
TID: [-1234] [] [2024-11-16 03:07:04,875]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e69bf94b-caec-4d02-a593-40d0d50c7c8d
TID: [-1234] [] [2024-11-16 03:07:06,300]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f2bf6b08-fece-4a1d-a275-5bf104ccb7dd
TID: [-1234] [] [2024-11-16 03:31:42,230]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-16 04:01:42,463]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-16 04:06:05,160]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bab907da-b5c3-4212-9b1f-f2cbd447103d
TID: [-1234] [] [2024-11-16 04:06:05,789]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fbe9cd61-f4bf-46bd-b7b0-6c48fbbb9de9
TID: [-1234] [] [2024-11-16 04:06:06,645]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6115615e-cb80-47de-97e7-de44f23cc954
TID: [-1234] [] [2024-11-16 04:06:08,232]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9d95dc54-934a-48dc-bb0a-ae94af69fac4
TID: [-1234] [] [2024-11-16 04:06:11,188]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6c390011-fdde-4b31-a47d-40ba9874f7ae
TID: [-1234] [] [2024-11-16 04:06:12,720]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b5725f75-abfa-4513-9233-a0116b7cdb60
TID: [-1234] [] [2024-11-16 04:06:15,279]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a7a7962c-adcb-4025-8a79-53c6fa32fc5e
TID: [-1234] [] [2024-11-16 04:06:17,386]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a4114b9c-c16f-475a-bbb5-960230bd101a
TID: [-1234] [] [2024-11-16 04:31:42,817]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-16 04:34:57,125]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 910329cd-642c-4f91-8f31-823c8cf380a0
TID: [-1234] [] [2024-11-16 05:01:43,267]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-16 05:06:17,215]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 31a4df44-ebfc-4eed-a101-1f375e774b73
TID: [-1234] [] [2024-11-16 05:06:21,537]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1677844c-4b68-43de-a2ca-2b0991cd686a
TID: [-1234] [] [2024-11-16 05:06:22,144]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cdc11b53-e1a4-4528-bdab-263e49bc73c5
TID: [-1234] [] [2024-11-16 05:06:23,068]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e93b30ce-47b2-49d6-86ee-19a937fbb6a2
TID: [-1234] [] [2024-11-16 05:06:29,222]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0fc9a135-7028-4709-8f5d-539cc80be785
TID: [-1234] [] [2024-11-16 05:06:33,181]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ada9a293-3dcf-4df5-9659-f3dae19aa446
TID: [-1234] [] [2024-11-16 05:06:33,905]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b2ba3ff6-948f-4c54-bab9-76bd5fa12805
TID: [-1234] [] [2024-11-16 05:17:37,104]  WARN {org.wso2.carbon.ui.transports.fileupload.AbstractFileUploadExecutor} - ToolsAnyFileUpload method is not supported
TID: [-1234] [] [2024-11-16 05:31:43,672]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-16 06:01:44,157]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-16 06:06:48,611]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 71da4109-de49-4a48-9db1-1f6e01c70317
TID: [-1234] [] [2024-11-16 06:06:49,354]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d31af5b1-2de9-4068-8d88-d2ceeb1c5635
TID: [-1234] [] [2024-11-16 06:06:54,079]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6625ce65-cb95-42f6-9c91-ec1a8472dee0
TID: [-1234] [] [2024-11-16 06:06:55,778]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5a90be82-39b3-48ae-98e0-db5c3e0ae36e
TID: [-1234] [] [2024-11-16 06:07:01,205]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f098ffbc-cc4e-474f-aab5-123c3e9404fc
TID: [-1234] [] [2024-11-16 06:07:05,353]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b92e58f1-3474-4ab3-989e-ff2e64b94819
TID: [-1234] [] [2024-11-16 06:07:07,082]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a7e5e151-73d6-400b-9dc2-de404a5e8765
TID: [-1234] [] [2024-11-16 06:31:44,601]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-16 07:01:45,239]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-16 07:06:13,646]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6b5cce96-5288-4ac5-9e01-abfe1507084d
TID: [-1234] [] [2024-11-16 07:06:19,702]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d413eec3-83ed-4715-a192-9b778874d465
TID: [-1234] [] [2024-11-16 07:06:21,791]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1bcabaa6-2392-42f0-a20e-8d0b42c6fde7
TID: [-1234] [] [2024-11-16 07:06:26,970]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = eb37444a-cfb5-4228-8e0f-840051a85140
TID: [-1234] [] [2024-11-16 07:06:31,118]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 68320df8-e5e1-45fb-8f5b-1f86af5c1aa1
TID: [-1234] [] [2024-11-16 07:06:31,529]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 10c98a50-77e5-4d64-863f-0584aa585387
TID: [-1234] [] [2024-11-16 07:31:45,711]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-16 07:35:43,637]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241116&denNgay=20241116&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241116&denNgay=20241116&maTthc=
TID: [-1234] [] [2024-11-16 07:35:43,678]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-16 08:01:46,197]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-16 08:06:42,212]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e44723b3-52a3-4862-9ba9-2d7fb579af6b
TID: [-1234] [] [2024-11-16 08:06:49,345]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0f2ffdc4-e59f-46e9-8d9a-926455d473a3
TID: [-1234] [] [2024-11-16 08:06:57,876]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9a2b7214-0d54-444a-8174-a5f42169ed03
TID: [-1234] [] [2024-11-16 08:07:02,423]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 087b25d9-ca9e-4554-afd4-bcb1a4480b00
TID: [-1234] [] [2024-11-16 08:07:02,941]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9d53b16a-4db5-45eb-99b5-2f667bbe7fa2
TID: [-1234] [] [2024-11-16 08:31:47,477]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-16 08:36:25,031]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241116&denNgay=20241116&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241116&denNgay=20241116&maTthc=
TID: [-1234] [] [2024-11-16 08:36:25,070]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-16 08:48:55,603]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241116&denNgay=20241116&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241116&denNgay=20241116&maTthc=
TID: [-1234] [] [2024-11-16 08:48:55,644]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-11-16 09:02:05,002]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-16 09:07:35,327]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3e46b5e2-3010-41c5-bbe8-4b8184338be8
TID: [-1234] [] [2024-11-16 09:07:36,932]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 546415b8-9289-4637-85ba-d946e055132b
TID: [-1234] [] [2024-11-16 09:07:37,953]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8e9aba60-6067-45bc-8760-608cf87de77a
TID: [-1234] [] [2024-11-16 09:07:38,709]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3e5625d3-2fc8-4699-89d1-b5b53f426eb2
TID: [-1234] [] [2024-11-16 09:07:42,265]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3a6d5ecf-ef46-4164-b428-e28097754495
TID: [-1234] [] [2024-11-16 09:07:47,422]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 400fac63-9590-473a-a031-a7b2c8e777a2
TID: [-1234] [] [2024-11-16 09:32:29,934]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-16 10:02:30,912]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-16 10:06:59,664]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 72e9ee85-1f5f-42f5-baa1-37a876d703ae
TID: [-1234] [] [2024-11-16 10:06:59,927]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 09274792-aad1-41a0-b4e2-631520492be1
TID: [-1234] [] [2024-11-16 10:07:00,069]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5c32fe55-65c0-4a63-a470-b543c55b80a1
TID: [-1234] [] [2024-11-16 10:07:00,095]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0cdceb6c-9e0b-47e6-a069-62c4a27a322d
TID: [-1234] [] [2024-11-16 10:07:03,446]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f881c337-4c85-4f2c-9370-3e2e967ed21a
TID: [-1234] [] [2024-11-16 10:07:03,915]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6762b25b-9558-4b53-94f5-279b41b4d6bf
TID: [-1234] [] [2024-11-16 10:07:08,477]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d70eaf80-954f-418a-bdee-c783035a0c70
TID: [-1234] [] [2024-11-16 10:07:08,860]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b0717b37-01e1-4146-ac60-b34eb444de75
TID: [-1234] [] [2024-11-16 10:34:57,292]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 35fdec17-f23e-4b57-8847-ea77d3995718
TID: [-1234] [] [2024-11-16 10:43:31,858]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-16 11:06:16,993]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 645f9b3c-7d49-45dd-86d4-4e69fc932ee7
TID: [-1234] [] [2024-11-16 11:06:16,996]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bbd2f099-b5b9-412c-b58f-99e5d9bcf63d
TID: [-1234] [] [2024-11-16 11:06:19,075]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6fb41d16-b39c-466a-903c-fe96277a3342
TID: [-1234] [] [2024-11-16 11:06:22,848]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6e1eb1f7-c1a5-482d-bd37-b0f3f34ae0ba
TID: [-1234] [] [2024-11-16 11:06:30,846]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bbe15b3a-041d-48c0-97f5-bbbd268be246
TID: [-1234] [] [2024-11-16 11:06:32,366]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6da38a42-bf0a-43dc-8fa7-40232ca7fd41
TID: [-1234] [] [2024-11-16 11:06:36,369]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ea73d2b7-2609-40fb-9d23-aa2d5bbe8ff2
TID: [-1234] [] [2024-11-16 11:06:39,406]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f037a74c-363d-4392-ac6c-5be2987db573
TID: [-1234] [] [2024-11-16 11:06:40,563]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d60b3658-314c-4f2f-a71f-df06563b08bc
TID: [-1234] [] [2024-11-16 11:13:32,144]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-16 11:43:32,392]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-16 12:07:10,334]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 63238e4b-bd5b-41ad-9bdd-a9b51813fb0f
TID: [-1234] [] [2024-11-16 12:07:11,637]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c5e8bf67-45b6-4141-b0a8-8f82dbaa8412
TID: [-1234] [] [2024-11-16 12:07:17,560]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a9929ecb-2081-4b55-bf03-acdd6b1b35b1
TID: [-1234] [] [2024-11-16 12:07:20,680]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 64e70296-ccc0-4781-82ca-e12ab28338cd
TID: [-1234] [] [2024-11-16 12:07:22,969]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2c0c371b-faa3-41d7-bd83-39d3e53428a4
TID: [-1234] [] [2024-11-16 12:13:32,779]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-16 12:43:33,585]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-16 12:44:29,419]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241116&denNgay=20241116&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241116&denNgay=20241116&maTthc=
TID: [-1234] [] [2024-11-16 12:44:29,458]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-16 13:06:39,204]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c7df48f3-f813-47dc-86ec-20f5d51ee75b
TID: [-1234] [] [2024-11-16 13:06:40,132]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b1e5956b-63e4-4ac3-b0c5-36d68e86d68d
TID: [-1234] [] [2024-11-16 13:06:41,837]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 93015076-d6f7-410d-95d1-2e7ad8f19149
TID: [-1234] [] [2024-11-16 13:06:42,569]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c2ff43e5-db37-44b8-88c7-cf072f208a48
TID: [-1234] [] [2024-11-16 13:06:42,912]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1d3ae460-a286-433a-8b0a-373164d990f6
TID: [-1234] [] [2024-11-16 13:06:45,577]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1e55b04d-b3a6-4b52-bcaa-6a19428f4099
TID: [-1234] [] [2024-11-16 13:06:51,510]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fcc8071e-20cb-452a-a2ce-13ff329dc908
TID: [-1234] [] [2024-11-16 13:06:54,944]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0764d4a2-d3d1-4cca-9364-315af8cfca91
TID: [-1234] [] [2024-11-16 13:13:33,706]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-16 13:43:34,005]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-16 14:07:07,719]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9eb4299c-b691-49ae-aacb-f551fd788f04
TID: [-1234] [] [2024-11-16 14:07:11,092]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b387b39a-a0b6-454b-abaa-c5c965a71f2c
TID: [-1234] [] [2024-11-16 14:07:13,715]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e5bf86c0-72c3-49ea-ac73-9f4f0cd039af
TID: [-1234] [] [2024-11-16 14:07:15,388]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a39fc441-7067-41bc-9bf0-0ebb0d5c773b
TID: [-1234] [] [2024-11-16 14:07:15,527]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5afb1100-6907-417b-a92b-aa6cd9914167
TID: [-1234] [] [2024-11-16 14:07:21,322]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 32ed09b9-a315-44a0-bf94-845fce9cff5c
TID: [-1234] [] [2024-11-16 14:07:22,213]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 29f71b32-add6-41e3-8885-8629d1ea4efd
TID: [-1234] [] [2024-11-16 14:29:04,901]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-16 14:59:05,171]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-16 15:06:18,339]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b044a0ba-4aff-49e1-ac7c-a08cb567c71d
TID: [-1234] [] [2024-11-16 15:06:19,588]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a0b10ffc-5f43-43c1-b75d-d817efeed8f8
TID: [-1234] [] [2024-11-16 15:06:19,664]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 65934de5-8518-4cfd-a174-987adaccfec3
TID: [-1234] [] [2024-11-16 15:06:20,773]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4df6e293-da53-4747-985c-a237546b2312
TID: [-1234] [] [2024-11-16 15:06:22,637]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c325c2a6-0574-4362-8d36-78e8e1fc01e0
TID: [-1234] [] [2024-11-16 15:06:24,560]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 946ffd60-0e6e-436e-a094-5264d4cd5535
TID: [-1234] [] [2024-11-16 15:06:24,953]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c8d9b7c0-60cf-482b-80e7-19da380bbb20
TID: [-1234] [] [2024-11-16 15:06:26,071]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 131f7e79-cd8a-4920-92e7-78bc021ef1b6
TID: [-1234] [] [2024-11-16 15:06:27,190]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0728db00-d074-4824-baac-459cf2bf8168
TID: [-1234] [] [2024-11-16 15:06:29,559]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bd829ece-fd37-4575-9a61-0b0830630c08
TID: [-1234] [] [2024-11-16 15:06:31,358]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3678e56f-7cd2-47d7-ae9f-dbe11e3b45e6
TID: [-1234] [] [2024-11-16 15:29:05,352]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-16 15:59:05,974]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-16 16:06:27,858]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2f6bdfee-c826-4251-b80f-5ff31c5d02e7
TID: [-1234] [] [2024-11-16 16:06:29,492]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e1f53d8a-c61a-4f10-b6c9-c2812cede7d9
TID: [-1234] [] [2024-11-16 16:06:30,501]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 08468a9c-a0c5-45cb-b651-6dbfdb7222ab
TID: [-1234] [] [2024-11-16 16:06:34,429]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 92624a09-b09a-41aa-9a03-3d7101ac5eb6
TID: [-1234] [] [2024-11-16 16:06:37,410]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f89f5f08-d08c-46e5-bc58-0db3d7ff6749
TID: [-1234] [] [2024-11-16 16:06:39,882]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bd29358a-2655-4a34-bff0-3ebf20252537
TID: [-1234] [] [2024-11-16 16:29:06,381]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-16 16:32:41,737]  INFO {org.wso2.carbon.identity.application.authentication.framework.store.SessionCleanUpService} - Session Data cleanup task is running successfully for removing expired Data
TID: [-1234] [] [2024-11-16 16:59:07,294]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-16 17:06:46,806]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a3b18d5c-6dff-4a78-ba14-01d688a6a4f9
TID: [-1234] [] [2024-11-16 17:06:50,388]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f699a5e2-8828-4e2a-8fa3-0013339f8e28
TID: [-1234] [] [2024-11-16 17:06:51,201]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5268f5b4-2bb4-4a8f-8f06-ed090ad4676e
TID: [-1234] [] [2024-11-16 17:06:52,371]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 28a098af-a09c-4df2-8e41-2ea7b21a232c
TID: [-1234] [] [2024-11-16 17:06:57,291]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b16d8089-7fa1-46a7-9b5a-459f3d492452
TID: [-1234] [] [2024-11-16 17:06:59,928]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1882663d-de6d-4049-84b8-8cbf26840730
TID: [-1234] [] [2024-11-16 17:07:01,081]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e39f5402-675c-422a-92b7-7748854f1169
TID: [-1234] [] [2024-11-16 17:31:37,330]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-16 17:34:55,236]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bf5bf940-51b3-451b-ba5c-e7dca712656c
TID: [-1234] [] [2024-11-16 17:41:10,375]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241116&denNgay=20241116&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241116&denNgay=20241116&maTthc=
TID: [-1234] [] [2024-11-16 17:41:10,415]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-16 17:53:20,307]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241116&denNgay=20241116&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241116&denNgay=20241116&maTthc=
TID: [-1234] [] [2024-11-16 17:53:20,348]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-16 17:53:27,221]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241116&denNgay=20241116&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241116&denNgay=20241116&maTthc=
TID: [-1234] [] [2024-11-16 17:53:27,260]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-16 18:01:38,080]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-16 18:07:28,189]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ffa47e5a-b767-4e9c-a9cf-e9f2ca6be6c7
TID: [-1234] [] [2024-11-16 18:07:28,886]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8e233397-29de-4967-bff7-98d7e305096d
TID: [-1234] [] [2024-11-16 18:07:28,900]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d7df29a4-4ada-4cd2-8574-331ef557102b
TID: [-1234] [] [2024-11-16 18:07:32,031]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 489edc53-b17c-47ca-828a-77d63b60b37b
TID: [-1234] [] [2024-11-16 18:07:33,500]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cedddb73-50bf-48ac-bc82-0660cfaa080d
TID: [-1234] [] [2024-11-16 18:07:34,142]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c4219878-6c58-4011-ba93-edcfd53766e9
TID: [-1234] [] [2024-11-16 18:07:38,763]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 07e9d613-eea6-4d63-b552-db82caa95cfc
TID: [-1234] [] [2024-11-16 18:07:39,913]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7cb55cef-44cc-476a-bd44-862a40fa9fb9
TID: [-1234] [] [2024-11-16 18:31:38,433]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-16 19:01:38,908]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-16 19:06:43,712]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4ffbc82e-415f-4fe2-ace2-03d9674a7ccb
TID: [-1234] [] [2024-11-16 19:06:44,507]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b5ee2ec0-0c0e-409f-92cf-b0fd212a7fc5
TID: [-1234] [] [2024-11-16 19:06:44,769]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8aafed07-9ae0-45e1-90eb-ee26635911d7
TID: [-1234] [] [2024-11-16 19:06:49,318]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5a7383af-e816-483c-84e0-d1817a738f48
TID: [-1234] [] [2024-11-16 19:06:51,788]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bae29d10-2c36-4dd0-908a-8f24638f70e9
TID: [-1234] [] [2024-11-16 19:06:55,570]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ca6df07c-14fa-4401-885a-dc0bdb76d641
TID: [-1234] [] [2024-11-16 19:06:56,327]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 36c6bec4-3a4b-43f8-8aca-5e8e9e301204
TID: [-1234] [] [2024-11-16 19:31:39,225]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-16 19:34:58,826]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6bcb6307-72e2-46a2-b375-5002a96a47c1
TID: [-1234] [] [2024-11-16 20:01:39,314]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-16 20:07:01,278]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1c68b020-dd4c-42f1-b07a-0aa04246d1ea
TID: [-1234] [] [2024-11-16 20:07:01,906]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0a6daed1-c53f-4024-a873-b8b33b965761
TID: [-1234] [] [2024-11-16 20:07:03,243]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 99f465ab-e4b9-4724-b4d3-e4b2aba896f4
TID: [-1234] [] [2024-11-16 20:07:06,895]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8c5bf531-7759-438b-b099-8714490daf91
TID: [-1234] [] [2024-11-16 20:07:10,508]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 40ba2aa2-be09-4e46-a2c0-6b7864b6edf9
TID: [-1234] [] [2024-11-16 20:07:15,938]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 696dc315-ad3f-4c70-92cc-9d414727c139
TID: [-1234] [] [2024-11-16 20:07:19,355]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 69b0b9cc-ff96-49b7-813f-50428a1d5561
TID: [-1234] [] [2024-11-16 20:07:21,590]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f49e1adf-f520-407d-af43-1f4db032a690
TID: [-1234] [] [2024-11-16 20:31:39,819]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-16 20:34:02,264]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241116&denNgay=20241116&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241116&denNgay=20241116&maTthc=
TID: [-1234] [] [2024-11-16 20:34:02,303]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-16 20:34:02,900]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241116&denNgay=20241116&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241116&denNgay=20241116&maTthc=
TID: [-1234] [] [2024-11-16 20:34:02,941]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-16 20:34:03,289]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241116&denNgay=20241116&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241116&denNgay=20241116&maTthc=
TID: [-1234] [] [2024-11-16 20:34:03,326]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-16 20:34:03,899]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241116&denNgay=20241116&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241116&denNgay=20241116&maTthc=
TID: [-1234] [] [2024-11-16 20:34:03,943]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-11-16 20:34:32,400]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241116&denNgay=20241116&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241116&denNgay=20241116&maTthc=
TID: [-1234] [] [2024-11-16 20:34:32,440]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-16 20:34:53,626]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6183aadb-eb24-49f3-b184-7f0b46eab7ce
TID: [-1234] [] [2024-11-16 21:01:39,922]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-16 21:06:30,977]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0dba412a-8615-43e2-8620-aabf1253256e
TID: [-1234] [] [2024-11-16 21:06:32,375]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 383a7d87-0b06-452b-9742-694bd60d4a54
TID: [-1234] [] [2024-11-16 21:06:33,125]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cf1d96fd-5d7d-47b5-8d2c-9048156cb69b
TID: [-1234] [] [2024-11-16 21:06:33,442]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 10a6f074-6eb8-41dc-8c6c-754def9aa6d4
TID: [-1234] [] [2024-11-16 21:06:34,086]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 455490f2-0886-47d4-aac1-f9297b1328d7
TID: [-1234] [] [2024-11-16 21:06:35,756]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 753aac7e-ed36-4412-881b-b2fe0417d68c
TID: [-1234] [] [2024-11-16 21:06:39,572]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 48e9733c-9de4-4853-a1d5-cf74491b4a33
TID: [-1234] [] [2024-11-16 21:06:43,338]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4b245de4-0784-4583-ac5a-2cc6f57cc15d
TID: [-1234] [] [2024-11-16 21:06:43,913]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = af9b8019-7f3b-44de-b8dc-182161004ddf
TID: [-1234] [] [2024-11-16 21:31:44,143]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-16 22:01:44,437]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-16 22:06:36,259]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6d85de00-014a-417b-843f-728ef8b58866
TID: [-1234] [] [2024-11-16 22:06:36,265]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9c1182b7-77e0-4004-957d-8c40183d493f
TID: [-1234] [] [2024-11-16 22:06:37,771]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f13c0a32-1fcc-4713-8b0a-c7f24d3e5f91
TID: [-1234] [] [2024-11-16 22:06:39,266]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 63800d40-36a2-46b1-a7b3-ef7462d31052
TID: [-1234] [] [2024-11-16 22:06:43,605]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c3ad2364-04ac-4376-a46f-060e0929c933
TID: [-1234] [] [2024-11-16 22:06:45,511]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 60de4050-ed75-467e-a114-d28c9a3d111f
TID: [-1234] [] [2024-11-16 22:06:48,392]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e127af68-7a35-4c8d-be04-130c5c738955
TID: [-1234] [] [2024-11-16 22:06:49,434]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 87cc7944-e4e7-4314-a8ca-a33b67916850
TID: [-1234] [] [2024-11-16 22:31:45,991]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-16 23:01:46,474]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-16 23:07:07,594]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0d96474c-a0ec-4313-a1f3-c2b8dad045c4
TID: [-1234] [] [2024-11-16 23:07:11,069]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1cbab0e8-c448-4e3c-b7e2-0d92c645e79e
TID: [-1234] [] [2024-11-16 23:07:13,970]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = be9ce715-7213-46ce-a10f-60ce60626e97
TID: [-1234] [] [2024-11-16 23:07:21,461]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a86967dd-6474-4413-b754-601233c7d63a
TID: [-1234] [] [2024-11-16 23:07:24,234]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a977ee4c-be73-45de-9b19-586267e06bcd
TID: [-1234] [] [2024-11-16 23:07:25,376]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 37d4e047-0eb7-4251-9bdc-915561e02743
TID: [-1234] [] [2024-11-16 23:43:31,487]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
