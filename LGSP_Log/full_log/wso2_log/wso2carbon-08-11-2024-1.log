TID: [-1234] [] [2024-08-11 00:00:13,503]  WARN {org.apache.synapse.commons.util.MiscellaneousUtil} - Error loading properties from a file at from the System defined location: access-log.properties
TID: [-1234] [] [2024-08-11 00:10:54,720]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after <PERSON> written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-10847, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 812bc82c-777a-422b-949a-9cd05becb65a
TID: [-1234] [] [2024-08-11 00:10:54,723]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 00:14:23,862]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-10860, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 2f71c046-3869-4ee3-a9f8-8d152ed5dd16
TID: [-1234] [] [2024-08-11 00:14:23,864]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 00:19:13,775]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-10868, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 44f8b9a4-347b-491e-9d4f-5a9f81c78864
TID: [-1234] [] [2024-08-11 00:19:13,777]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 00:19:13,779]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 00:19:13,779]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:38510, CORRELATION_ID = 44f8b9a4-347b-491e-9d4f-5a9f81c78864, CONNECTION = http-incoming-357830
TID: [-1234] [] [2024-08-11 00:19:13,794]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-357830, CORRELATION_ID = 44f8b9a4-347b-491e-9d4f-5a9f81c78864
TID: [-1234] [] [2024-08-11 00:23:04,756]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-10878, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 4f37e020-8d51-4689-906a-60380ffbb89d
TID: [-1234] [] [2024-08-11 00:23:04,757]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 00:23:05,703]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-10881, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 9b95ea42-2125-459f-b674-c65ac4f10f9f
TID: [-1234] [] [2024-08-11 00:23:05,704]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 00:23:06,030]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-11 00:23:06,786]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 00:23:06,787]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:55218, CORRELATION_ID = b886772e-19a3-4082-9eab-7423b36e5e23, CONNECTION = http-incoming-358014
TID: [-1234] [] [2024-08-11 00:23:06,788]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-10880, SOCKET_TIMEOUT = 180000, CORRELATION_ID = b886772e-19a3-4082-9eab-7423b36e5e23
TID: [-1234] [] [2024-08-11 00:23:06,789]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 00:23:06,828]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-358014, CORRELATION_ID = b886772e-19a3-4082-9eab-7423b36e5e23
TID: [-1234] [] [2024-08-11 00:26:19,242]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-10896, SOCKET_TIMEOUT = 180000, CORRELATION_ID = e67c4267-6b78-41ba-b790-38cd5ff5a4a8
TID: [-1234] [] [2024-08-11 00:26:19,243]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 00:29:19,783]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = c3ab93a8-4aa1-4e6c-b02b-531b069c1706
TID: [-1234] [] [2024-08-11 00:29:19,784]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-10910, SOCKET_TIMEOUT = 180000, CORRELATION_ID = c3ab93a8-4aa1-4e6c-b02b-531b069c1706
TID: [-1234] [] [2024-08-11 00:29:19,785]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 00:32:25,493]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-10930, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 6b55d9f8-9ce3-4361-b58d-316dff8985ed
TID: [-1234] [] [2024-08-11 00:32:25,494]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 00:32:27,780]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-10924, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 7db84d27-bc4f-429e-a728-a240ece449cf
TID: [-1234] [] [2024-08-11 00:32:27,781]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 00:37:12,692]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ad577c30-7b27-4f8a-91b9-95a81d69963d
TID: [-1234] [] [2024-08-11 00:37:12,703]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b556e4f3-ecd7-4d0c-8794-9e8aa2f49fe4
TID: [-1234] [] [2024-08-11 00:37:12,711]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 75c8e034-f59d-400c-8913-c05ee2d1e10e
TID: [-1234] [] [2024-08-11 00:37:12,724]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3db91a59-ac6b-4abf-9b64-c7a5f28e49fa
TID: [-1234] [] [2024-08-11 00:59:47,188]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-11 01:11:04,622]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-10963, SOCKET_TIMEOUT = 180000, CORRELATION_ID = a90589a4-6384-4ce5-980f-017d146e1fbf
TID: [-1234] [] [2024-08-11 01:11:04,624]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 01:14:29,150]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-10964, SOCKET_TIMEOUT = 180000, CORRELATION_ID = e1d02bd4-0c40-4434-86db-c37d7d3349f1
TID: [-1234] [] [2024-08-11 01:14:29,152]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 01:14:29,929]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c22f33ae-137e-4c45-9678-6ad71c492871
TID: [-1234] [] [2024-08-11 01:19:55,030]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 01:19:55,031]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:33008, CORRELATION_ID = 1d586bd0-a121-4505-8f4d-3ee627fd2584, CONNECTION = http-incoming-361056
TID: [-1234] [] [2024-08-11 01:19:55,036]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 1d586bd0-a121-4505-8f4d-3ee627fd2584
TID: [-1234] [] [2024-08-11 01:19:55,037]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-10978, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 1d586bd0-a121-4505-8f4d-3ee627fd2584
TID: [-1234] [] [2024-08-11 01:19:55,038]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 01:19:55,057]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-361056, CORRELATION_ID = 1d586bd0-a121-4505-8f4d-3ee627fd2584
TID: [-1234] [] [2024-08-11 01:23:30,518]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 01:23:30,520]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:42104, CORRELATION_ID = ffe1a89f-89bd-4c0f-a571-75d43042d802, CONNECTION = http-incoming-361177
TID: [-1234] [] [2024-08-11 01:23:30,530]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-10988, SOCKET_TIMEOUT = 180000, CORRELATION_ID = ffe1a89f-89bd-4c0f-a571-75d43042d802
TID: [-1234] [] [2024-08-11 01:23:30,532]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 01:23:30,550]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-361177, CORRELATION_ID = ffe1a89f-89bd-4c0f-a571-75d43042d802
TID: [-1234] [] [2024-08-11 01:23:31,640]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 51741b03-e120-40dd-b4ef-d8241287457e
TID: [-1234] [] [2024-08-11 01:23:31,641]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-10981, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 51741b03-e120-40dd-b4ef-d8241287457e
TID: [-1234] [] [2024-08-11 01:23:31,642]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 01:23:32,961]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = a65b9cc7-e878-4a79-a03a-4fab1fdb818d
TID: [-1234] [] [2024-08-11 01:23:32,962]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-10986, SOCKET_TIMEOUT = 180000, CORRELATION_ID = a65b9cc7-e878-4a79-a03a-4fab1fdb818d
TID: [-1234] [] [2024-08-11 01:23:32,963]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 01:26:43,263]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 01:26:43,264]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:44120, CORRELATION_ID = f76d5f97-1bc5-444a-8357-5598bca5a30e, CONNECTION = http-incoming-361364
TID: [-1234] [] [2024-08-11 01:26:43,422]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = f76d5f97-1bc5-444a-8357-5598bca5a30e
TID: [-1234] [] [2024-08-11 01:26:43,422]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-10996, SOCKET_TIMEOUT = 180000, CORRELATION_ID = f76d5f97-1bc5-444a-8357-5598bca5a30e
TID: [-1234] [] [2024-08-11 01:26:43,423]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 01:26:43,441]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-361364, CORRELATION_ID = f76d5f97-1bc5-444a-8357-5598bca5a30e
TID: [-1234] [] [2024-08-11 01:26:46,266]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 27a952ec-43fb-4248-b8cc-f26c344181a0
TID: [-1234] [] [2024-08-11 01:26:46,318]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3e1975ef-a65d-4c88-9fa6-36e5ffcb487b
TID: [-1234] [] [2024-08-11 01:26:46,575]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c4622a52-00b7-43a0-98a6-1d5156256ad6
TID: [-1234] [] [2024-08-11 01:29:50,615]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11010, SOCKET_TIMEOUT = 180000, CORRELATION_ID = cd6ec0d9-d356-4134-8db8-820e51040b5c
TID: [-1234] [] [2024-08-11 01:29:50,617]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 01:29:52,543]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-11 01:32:54,785]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = aab8268d-76e7-41e5-90f3-0faf2ab8078f
TID: [-1234] [] [2024-08-11 01:32:54,786]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11030, SOCKET_TIMEOUT = 180000, CORRELATION_ID = aab8268d-76e7-41e5-90f3-0faf2ab8078f
TID: [-1234] [] [2024-08-11 01:32:54,787]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 01:32:56,613]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 01:32:56,614]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:54148, CORRELATION_ID = 63675ac9-8140-4633-8d00-da3d3d36affe, CONNECTION = http-incoming-361534
TID: [-1234] [] [2024-08-11 01:32:56,735]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 63675ac9-8140-4633-8d00-da3d3d36affe
TID: [-1234] [] [2024-08-11 01:32:56,735]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11036, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 63675ac9-8140-4633-8d00-da3d3d36affe
TID: [-1234] [] [2024-08-11 01:32:56,736]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 01:32:56,754]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-361534, CORRELATION_ID = 63675ac9-8140-4633-8d00-da3d3d36affe
TID: [-1234] [] [2024-08-11 01:37:35,665]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5b86fd7f-1e13-4072-840e-ae519c5bf27f
TID: [-1234] [] [2024-08-11 01:37:37,143]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 269f3f78-5a71-417f-b92b-753b2cf377d0
TID: [-1234] [] [2024-08-11 02:00:04,910]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-11 02:11:36,222]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11070, SOCKET_TIMEOUT = 180000, CORRELATION_ID = f96ff01b-8705-4aa2-b439-c27899055d78
TID: [-1234] [] [2024-08-11 02:11:36,224]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 02:15:04,288]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 2410a4bc-9efe-4ed5-a439-95f8597b0eeb
TID: [-1234] [] [2024-08-11 02:15:04,290]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11064, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 2410a4bc-9efe-4ed5-a439-95f8597b0eeb
TID: [-1234] [] [2024-08-11 02:15:04,291]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 02:20:37,606]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11083, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 18fa6873-95e0-4067-8992-8a5513e579bd
TID: [-1234] [] [2024-08-11 02:20:37,608]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 02:24:21,973]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11090, SOCKET_TIMEOUT = 180000, CORRELATION_ID = a32c1cae-244b-463c-8ad7-b585af14ec1e
TID: [-1234] [] [2024-08-11 02:24:21,975]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 02:24:23,682]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11100, SOCKET_TIMEOUT = 180000, CORRELATION_ID = cbe727a0-af63-4e67-9f37-8950c9b2b8e5
TID: [-1234] [] [2024-08-11 02:24:23,683]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 02:24:25,017]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11093, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 69997093-8d20-460f-ae67-92c9547c6bba
TID: [-1234] [] [2024-08-11 02:24:25,018]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 02:27:41,720]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11106, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 13e9b11e-da56-4432-bf18-88936fe728af
TID: [-1234] [] [2024-08-11 02:27:41,722]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 02:30:29,495]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-11 02:30:44,507]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 02:30:44,508]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:53448, CORRELATION_ID = 48c8aa55-23ef-4681-83c4-2b72c82a2541, CONNECTION = http-incoming-364683
TID: [-1234] [] [2024-08-11 02:30:44,685]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11129, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 48c8aa55-23ef-4681-83c4-2b72c82a2541
TID: [-1234] [] [2024-08-11 02:30:44,686]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 02:30:44,722]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-364683, CORRELATION_ID = 48c8aa55-23ef-4681-83c4-2b72c82a2541
TID: [-1234] [] [2024-08-11 02:33:45,625]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = e46700fd-68bb-480d-ae6c-40ff04dd54a7
TID: [-1234] [] [2024-08-11 02:33:45,626]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11135, SOCKET_TIMEOUT = 180000, CORRELATION_ID = e46700fd-68bb-480d-ae6c-40ff04dd54a7
TID: [-1234] [] [2024-08-11 02:33:45,627]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 02:33:45,638]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 02:33:45,638]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:52784, CORRELATION_ID = e46700fd-68bb-480d-ae6c-40ff04dd54a7, CONNECTION = http-incoming-364752
TID: [-1234] [] [2024-08-11 02:33:45,647]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-364752, CORRELATION_ID = e46700fd-68bb-480d-ae6c-40ff04dd54a7
TID: [-1234] [] [2024-08-11 02:33:48,487]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 02:33:48,488]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:52836, CORRELATION_ID = 8032a471-e8b5-42b4-b0b3-3eb12ef70074, CONNECTION = http-incoming-364757
TID: [-1234] [] [2024-08-11 02:33:48,617]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11136, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 8032a471-e8b5-42b4-b0b3-3eb12ef70074
TID: [-1234] [] [2024-08-11 02:33:48,618]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 02:33:48,636]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-364757, CORRELATION_ID = 8032a471-e8b5-42b4-b0b3-3eb12ef70074
TID: [-1234] [] [2024-08-11 02:38:27,327]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ac4d6f0b-a70f-4eeb-8814-3bb30350737d
TID: [-1234] [] [2024-08-11 02:38:27,327]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d64edad6-56a3-427e-926e-733eb15b2db9
TID: [-1234] [] [2024-08-11 02:55:06,620]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20240811&denNgay=20240811&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20240811&denNgay=20240811&maTthc=
TID: [-1234] [] [2024-08-11 02:55:06,669]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-08-11 03:00:29,685]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-11 03:11:51,144]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11165, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 90088c16-1309-4f9e-a00f-1e9bee66fa3e
TID: [-1234] [] [2024-08-11 03:11:51,147]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 03:15:22,845]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11170, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 712d6ab4-449b-4332-9c59-9e1f1804f9d5
TID: [-1234] [] [2024-08-11 03:15:22,847]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 03:15:22,968]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c1c2474e-2850-4ef7-9645-32872dd961f1
TID: [-1234] [] [2024-08-11 03:20:11,100]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11189, SOCKET_TIMEOUT = 180000, CORRELATION_ID = b15b6865-57bf-490e-95c7-77005c6f9a16
TID: [-1234] [] [2024-08-11 03:20:11,102]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 03:23:40,602]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 78dae134-9093-46af-8766-4023b96456b0
TID: [-1234] [] [2024-08-11 03:23:40,603]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11198, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 78dae134-9093-46af-8766-4023b96456b0
TID: [-1234] [] [2024-08-11 03:23:40,604]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 03:23:42,946]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 5d5ad26c-a124-47da-b9b9-770bba862a0e
TID: [-1234] [] [2024-08-11 03:23:42,947]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11203, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 5d5ad26c-a124-47da-b9b9-770bba862a0e
TID: [-1234] [] [2024-08-11 03:23:42,948]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 03:23:43,069]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = b3cb0f50-6aad-478b-a58f-dd0db7681f2f
TID: [-1234] [] [2024-08-11 03:23:43,070]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11201, SOCKET_TIMEOUT = 180000, CORRELATION_ID = b3cb0f50-6aad-478b-a58f-dd0db7681f2f
TID: [-1234] [] [2024-08-11 03:23:43,071]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 03:26:52,494]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11220, SOCKET_TIMEOUT = 180000, CORRELATION_ID = ba0f2105-b981-4ffc-a270-15aaf6683a11
TID: [-1234] [] [2024-08-11 03:26:52,495]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 03:29:53,997]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11226, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 53abf6fa-fdef-4fa7-bc88-39bd33c22d42
TID: [-1234] [] [2024-08-11 03:29:53,998]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 03:31:11,903]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-11 03:32:57,522]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = d2945ddc-3149-4e19-a952-404f96608dec
TID: [-1234] [] [2024-08-11 03:32:57,522]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11241, SOCKET_TIMEOUT = 180000, CORRELATION_ID = d2945ddc-3149-4e19-a952-404f96608dec
TID: [-1234] [] [2024-08-11 03:32:57,523]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 03:32:59,467]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11243, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 18e0b78f-3030-4fe6-93a8-24fbc0c595eb
TID: [-1234] [] [2024-08-11 03:32:59,469]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 03:33:15,340]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20240811&denNgay=20240811&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20240811&denNgay=20240811&maTthc=
TID: [-1234] [] [2024-08-11 03:33:15,378]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-08-11 03:37:46,543]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1c952985-4920-4782-a227-0b3e3191be81
TID: [-1234] [] [2024-08-11 03:37:46,555]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 19dcdbab-7c25-4437-9c64-883289293d64
TID: [-1234] [] [2024-08-11 03:37:46,558]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1c1caad7-aee7-43fb-b3f2-e877b05fd246
TID: [-1234] [] [2024-08-11 03:37:47,389]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 70d7f41c-71b7-48b1-b4f7-450ea921c4c3
TID: [-1234] [] [2024-08-11 03:37:47,394]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6a6c965b-d308-4260-9200-ff91026ade94
TID: [-1234] [] [2024-08-11 03:37:47,418]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ffee8007-22fb-4878-b6c8-9f13416183ff
TID: [-1234] [] [2024-08-11 04:01:12,167]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-11 04:10:47,046]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 04:10:47,047]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:38884, CORRELATION_ID = 2a83d5af-ff83-42af-b82e-661deb2b56cc, CONNECTION = http-incoming-370068
TID: [-1234] [] [2024-08-11 04:10:47,179]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11275, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 2a83d5af-ff83-42af-b82e-661deb2b56cc
TID: [-1234] [] [2024-08-11 04:10:47,180]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 04:10:47,210]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-370068, CORRELATION_ID = 2a83d5af-ff83-42af-b82e-661deb2b56cc
TID: [-1234] [] [2024-08-11 04:14:18,913]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 04:14:18,914]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:40232, CORRELATION_ID = a73c6837-7b07-446e-b926-dd0f6bc32c61, CONNECTION = http-incoming-370315
TID: [-1234] [] [2024-08-11 04:14:18,953]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = a73c6837-7b07-446e-b926-dd0f6bc32c61
TID: [-1234] [] [2024-08-11 04:14:18,954]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11285, SOCKET_TIMEOUT = 180000, CORRELATION_ID = a73c6837-7b07-446e-b926-dd0f6bc32c61
TID: [-1234] [] [2024-08-11 04:14:18,954]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 04:14:18,996]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-370315, CORRELATION_ID = a73c6837-7b07-446e-b926-dd0f6bc32c61
TID: [-1234] [] [2024-08-11 04:19:06,485]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11297, SOCKET_TIMEOUT = 180000, CORRELATION_ID = bf6eeffc-90ac-465e-a4b8-bca87a3f34ba
TID: [-1234] [] [2024-08-11 04:19:06,486]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 04:22:42,794]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 65d0d3c5-2f93-4231-a622-f36f2d2c65e2
TID: [-1234] [] [2024-08-11 04:22:42,795]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11306, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 65d0d3c5-2f93-4231-a622-f36f2d2c65e2
TID: [-1234] [] [2024-08-11 04:22:42,796]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 04:22:42,799]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 04:22:42,800]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:58006, CORRELATION_ID = 65d0d3c5-2f93-4231-a622-f36f2d2c65e2, CONNECTION = http-incoming-370892
TID: [-1234] [] [2024-08-11 04:22:42,843]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-370892, CORRELATION_ID = 65d0d3c5-2f93-4231-a622-f36f2d2c65e2
TID: [-1234] [] [2024-08-11 04:22:44,898]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 04:22:44,899]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:58356, CORRELATION_ID = c60541d4-e408-4cc0-81a0-580734c30d94, CONNECTION = http-incoming-370930
TID: [-1234] [] [2024-08-11 04:22:45,038]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = c60541d4-e408-4cc0-81a0-580734c30d94
TID: [-1234] [] [2024-08-11 04:22:45,039]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11316, SOCKET_TIMEOUT = 180000, CORRELATION_ID = c60541d4-e408-4cc0-81a0-580734c30d94
TID: [-1234] [] [2024-08-11 04:22:45,040]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 04:22:45,058]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-370930, CORRELATION_ID = c60541d4-e408-4cc0-81a0-580734c30d94
TID: [-1234] [] [2024-08-11 04:22:47,560]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9643c4c1-6528-4c67-8893-3fc4d9d74c52
TID: [-1234] [] [2024-08-11 04:22:48,339]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8e4809fe-f582-4d92-8998-dba61dca6dfe
TID: [-1234] [] [2024-08-11 04:26:01,533]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 04:26:01,533]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:59032, CORRELATION_ID = 7b4ee581-fd4e-4b75-b450-62de5240581f, CONNECTION = http-incoming-371056
TID: [-1234] [] [2024-08-11 04:26:01,670]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11327, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 7b4ee581-fd4e-4b75-b450-62de5240581f
TID: [-1234] [] [2024-08-11 04:26:01,671]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 04:26:01,682]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-371056, CORRELATION_ID = 7b4ee581-fd4e-4b75-b450-62de5240581f
TID: [-1234] [] [2024-08-11 04:29:03,361]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 04:29:03,361]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:42562, CORRELATION_ID = 8da47557-42a9-48ed-8de0-bb699d6eccf3, CONNECTION = http-incoming-371139
TID: [-1234] [] [2024-08-11 04:29:03,466]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11344, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 8da47557-42a9-48ed-8de0-bb699d6eccf3
TID: [-1234] [] [2024-08-11 04:29:03,468]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 04:29:03,487]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-371139, CORRELATION_ID = 8da47557-42a9-48ed-8de0-bb699d6eccf3
TID: [-1234] [] [2024-08-11 04:32:10,476]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11351, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 0874cedb-a2e8-469f-8961-81787f54269b
TID: [-1234] [] [2024-08-11 04:32:10,478]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 04:32:11,017]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-11 04:36:50,644]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d8f55864-a926-4b1e-bfdc-38c603adf459
TID: [-1234] [] [2024-08-11 04:36:50,658]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3a0e1964-c26d-47d1-bb0e-0cee8fe601b6
TID: [-1234] [] [2024-08-11 04:36:50,665]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5dce8e20-2acb-4edc-8a48-9a22825af20f
TID: [-1234] [] [2024-08-11 04:36:52,549]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ed5e55d7-94c0-4395-ba53-a4218209ba52
TID: [-1234] [] [2024-08-11 04:36:52,570]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 59979224-e6c0-464c-beda-b00b4f85bf30
TID: [-1234] [] [2024-08-11 04:36:52,604]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9d082439-3741-4998-9627-8e17ccf6f90d
TID: [-1234] [] [2024-08-11 05:02:12,315]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-11 05:11:13,212]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = ad4a7685-c86f-4409-89ba-c2ad04bd26f7
TID: [-1234] [] [2024-08-11 05:11:13,213]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11391, SOCKET_TIMEOUT = 180000, CORRELATION_ID = ad4a7685-c86f-4409-89ba-c2ad04bd26f7
TID: [-1234] [] [2024-08-11 05:11:13,214]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 05:12:49,138]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20240811&denNgay=20240811&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20240811&denNgay=20240811&maTthc=
TID: [-1234] [] [2024-08-11 05:12:49,234]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-08-11 05:14:46,430]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 05:14:46,431]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:43732, CORRELATION_ID = 130a707d-357b-44f6-9506-7944a3faa837, CONNECTION = http-incoming-373538
TID: [-1234] [] [2024-08-11 05:14:46,529]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11393, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 130a707d-357b-44f6-9506-7944a3faa837
TID: [-1234] [] [2024-08-11 05:14:46,530]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 05:14:46,547]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-373538, CORRELATION_ID = 130a707d-357b-44f6-9506-7944a3faa837
TID: [-1234] [] [2024-08-11 05:14:46,616]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 932111a3-2931-483c-817f-e54825878ba3
TID: [-1234] [] [2024-08-11 05:20:07,269]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11408, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 24da91b3-d9ab-444e-afde-f68f0137e77d
TID: [-1234] [] [2024-08-11 05:20:07,270]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 05:23:32,853]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11413, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 546006b9-d847-4a12-8bdd-63e310623963
TID: [-1234] [] [2024-08-11 05:23:32,855]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 05:23:35,172]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11403, SOCKET_TIMEOUT = 180000, CORRELATION_ID = e0ee64c4-73d5-433c-8376-bd5b178878ad
TID: [-1234] [] [2024-08-11 05:23:35,174]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 05:23:35,947]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11424, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 63ef9dda-4b52-4c5d-a5f6-c3a046903a90
TID: [-1234] [] [2024-08-11 05:23:35,949]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 05:26:48,189]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 18f93efc-f225-420e-b8c4-f1f261029123
TID: [-1234] [] [2024-08-11 05:26:48,190]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11431, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 18f93efc-f225-420e-b8c4-f1f261029123
TID: [-1234] [] [2024-08-11 05:26:48,191]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 05:29:51,213]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = e4db3841-e585-482e-b3e8-dccc7a7809e8
TID: [-1234] [] [2024-08-11 05:29:51,213]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11444, SOCKET_TIMEOUT = 180000, CORRELATION_ID = e4db3841-e585-482e-b3e8-dccc7a7809e8
TID: [-1234] [] [2024-08-11 05:29:51,214]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 05:32:55,249]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 248c8d06-fd88-45d2-bd5d-c226057dce34
TID: [-1234] [] [2024-08-11 05:32:55,250]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11457, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 248c8d06-fd88-45d2-bd5d-c226057dce34
TID: [-1234] [] [2024-08-11 05:32:55,251]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 05:32:57,728]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 05:32:57,729]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:38490, CORRELATION_ID = 1a06775e-740d-4101-b38d-43e080b835a2, CONNECTION = http-incoming-374470
TID: [-1234] [] [2024-08-11 05:32:57,758]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11463, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 1a06775e-740d-4101-b38d-43e080b835a2
TID: [-1234] [] [2024-08-11 05:32:57,759]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 05:32:57,776]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-374470, CORRELATION_ID = 1a06775e-740d-4101-b38d-43e080b835a2
TID: [-1234] [] [2024-08-11 05:32:58,242]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-11 05:37:29,603]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 493d2796-aead-4c45-84cf-e7c68b03a1b0
TID: [-1234] [] [2024-08-11 06:02:58,400]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-11 06:11:01,698]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = a105ef5b-1689-4820-8de5-7b29ec6cb00e
TID: [-1234] [] [2024-08-11 06:11:01,699]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11485, SOCKET_TIMEOUT = 180000, CORRELATION_ID = a105ef5b-1689-4820-8de5-7b29ec6cb00e
TID: [-1234] [] [2024-08-11 06:11:01,701]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 06:14:36,121]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11487, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 78f63e4b-fa97-4f6d-9987-6aa718b2add6
TID: [-1234] [] [2024-08-11 06:14:36,123]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 06:19:56,356]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11500, SOCKET_TIMEOUT = 180000, CORRELATION_ID = caa197d6-9a8f-4d1c-90b0-34bc4d701903
TID: [-1234] [] [2024-08-11 06:19:56,358]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 06:23:35,825]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 06:23:35,826]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:42846, CORRELATION_ID = 4d6467f4-2a4d-41b1-b4a3-4211b41db34c, CONNECTION = http-incoming-377360
TID: [-1234] [] [2024-08-11 06:23:35,851]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11526, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 4d6467f4-2a4d-41b1-b4a3-4211b41db34c
TID: [-1234] [] [2024-08-11 06:23:35,852]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 06:23:35,867]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-377360, CORRELATION_ID = 4d6467f4-2a4d-41b1-b4a3-4211b41db34c
TID: [-1234] [] [2024-08-11 06:23:37,783]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 06:23:37,784]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:43238, CORRELATION_ID = 5dd7a567-e7b8-4817-8f23-9877ade97f92, CONNECTION = http-incoming-377398
TID: [-1234] [] [2024-08-11 06:23:37,915]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11532, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 5dd7a567-e7b8-4817-8f23-9877ade97f92
TID: [-1234] [] [2024-08-11 06:23:37,916]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 06:23:37,933]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-377398, CORRELATION_ID = 5dd7a567-e7b8-4817-8f23-9877ade97f92
TID: [-1234] [] [2024-08-11 06:23:38,315]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 06:23:38,316]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:43296, CORRELATION_ID = 9fc27667-610a-475b-9a93-733fcfdc7e8a, CONNECTION = http-incoming-377406
TID: [-1234] [] [2024-08-11 06:23:38,319]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11525, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 9fc27667-610a-475b-9a93-733fcfdc7e8a
TID: [-1234] [] [2024-08-11 06:23:38,320]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 06:23:38,336]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-377406, CORRELATION_ID = 9fc27667-610a-475b-9a93-733fcfdc7e8a
TID: [-1234] [] [2024-08-11 06:26:50,261]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11543, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 83d04000-72fb-4f33-8508-2cfedf8ac3f5
TID: [-1234] [] [2024-08-11 06:26:50,262]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 06:29:51,773]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 06:29:51,774]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:41068, CORRELATION_ID = 75458434-ebdf-4838-9367-1cca8012cb0c, CONNECTION = http-incoming-377593
TID: [-1234] [] [2024-08-11 06:29:51,824]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11550, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 75458434-ebdf-4838-9367-1cca8012cb0c
TID: [-1234] [] [2024-08-11 06:29:51,825]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 06:29:51,842]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-377593, CORRELATION_ID = 75458434-ebdf-4838-9367-1cca8012cb0c
TID: [-1234] [] [2024-08-11 06:32:54,731]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 0ec50d46-499b-4a74-a7bc-66bc255465b6
TID: [-1234] [] [2024-08-11 06:32:54,733]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11569, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 0ec50d46-499b-4a74-a7bc-66bc255465b6
TID: [-1234] [] [2024-08-11 06:32:54,734]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 06:32:55,992]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11575, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 029cfced-e4ba-4713-90e7-1107d41ee7d3
TID: [-1234] [] [2024-08-11 06:32:55,994]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 06:32:58,553]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-11 06:37:41,640]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c716dc5d-14b4-489e-b7b9-cd382720b40c
TID: [-1234] [] [2024-08-11 06:37:42,383]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 77554dee-c706-4f11-9227-f64154fc50ef
TID: [-1234] [] [2024-08-11 06:37:42,397]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a60edf07-9d24-4a80-b5b7-b5f3f271b897
TID: [-1234] [] [2024-08-11 06:37:42,455]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ac2ff334-c26b-4a46-a8ef-21bc047d632c
TID: [-1234] [] [2024-08-11 06:47:57,379]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20240811&denNgay=20240811&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20240811&denNgay=20240811&maTthc=
TID: [-1234] [] [2024-08-11 06:47:57,426]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-08-11 07:02:58,895]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-11 07:11:34,589]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 155ba5fb-24b9-4cda-972b-45276395bf2d
TID: [-1234] [] [2024-08-11 07:11:34,590]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11609, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 155ba5fb-24b9-4cda-972b-45276395bf2d
TID: [-1234] [] [2024-08-11 07:11:34,591]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 07:15:06,621]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 54bec1e3-c570-4934-bfa2-0aaef3459fa7
TID: [-1234] [] [2024-08-11 07:15:06,622]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11616, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 54bec1e3-c570-4934-bfa2-0aaef3459fa7
TID: [-1234] [] [2024-08-11 07:15:06,623]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 07:15:06,623]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 07:15:06,623]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:39426, CORRELATION_ID = 54bec1e3-c570-4934-bfa2-0aaef3459fa7, CONNECTION = http-incoming-380006
TID: [-1234] [] [2024-08-11 07:15:06,639]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-380006, CORRELATION_ID = 54bec1e3-c570-4934-bfa2-0aaef3459fa7
TID: [-1234] [] [2024-08-11 07:16:37,380]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : HoSoIGATE--v1.0.0_APIproductionEndpoint with address http://************:8095/savis/categories/api/v1/document has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-08-11 07:16:37,382]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : HoSoIGATE--v1.0.0_APIproductionEndpoint with address http://************:8095/savis/categories/api/v1/document - current suspend duration is : 30000ms - Next retry after : Sun Aug 11 07:17:07 ICT 2024
TID: [-1234] [] [2024-08-11 07:16:37,383]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-08-11 07:16:37,394]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:faf1f4b8-a72d-4bcf-a842-927a3533e575; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8095/savis/categories/api/v1/document/api_thongke_dvc, Received through API : admin--HoSoIGATE:v1.0.0, CORRELATION_ID = 41068f7a-ebc1-4a98-94e1-d87f2bc821bb
TID: [-1234] [] [2024-08-11 07:16:38,031]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : HoSoIGATE--v1.0.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-08-11 07:16:38,076]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : HoSoIGATE--v1.0.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-08-11 07:16:38,111]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : HoSoIGATE--v1.0.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-08-11 07:16:38,150]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : HoSoIGATE--v1.0.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-08-11 07:17:31,611]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 41068f7a-ebc1-4a98-94e1-d87f2bc821bb
TID: [-1234] [] [2024-08-11 07:17:31,612]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8095, TARGET_CONTEXT = http://************:8095/savis/categories/api/v1/document/api_thongke_dvc, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--HoSoIGATE:v1.0.0, REMOTE_ADDRESS = /************:8095, CONNECTION = http-outgoing-11622, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 41068f7a-ebc1-4a98-94e1-d87f2bc821bb
TID: [-1234] [] [2024-08-11 07:20:34,317]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 2f67bad4-9e43-4015-a110-3883b5ad6d1e
TID: [-1234] [] [2024-08-11 07:20:34,319]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11624, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 2f67bad4-9e43-4015-a110-3883b5ad6d1e
TID: [-1234] [] [2024-08-11 07:20:34,319]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 07:24:06,432]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = f13c58cb-3630-44ed-ba26-b6469ae806b7
TID: [-1234] [] [2024-08-11 07:24:06,433]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11645, SOCKET_TIMEOUT = 180000, CORRELATION_ID = f13c58cb-3630-44ed-ba26-b6469ae806b7
TID: [-1234] [] [2024-08-11 07:24:06,433]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 07:24:06,484]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:43820, CORRELATION_ID = f13c58cb-3630-44ed-ba26-b6469ae806b7, CONNECTION = http-incoming-380584
TID: [-1234] [] [2024-08-11 07:24:08,896]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 00f903ef-6ddb-4c6b-b1d4-91eedb6a724a
TID: [-1234] [] [2024-08-11 07:24:08,897]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11637, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 00f903ef-6ddb-4c6b-b1d4-91eedb6a724a
TID: [-1234] [] [2024-08-11 07:24:08,898]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 07:24:08,898]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 07:24:08,899]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:44274, CORRELATION_ID = 00f903ef-6ddb-4c6b-b1d4-91eedb6a724a, CONNECTION = http-incoming-380632
TID: [-1234] [] [2024-08-11 07:24:08,915]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-380632, CORRELATION_ID = 00f903ef-6ddb-4c6b-b1d4-91eedb6a724a
TID: [-1234] [] [2024-08-11 07:24:08,930]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 4f0f1713-fae4-4c7e-a871-d2d844b918da
TID: [-1234] [] [2024-08-11 07:24:08,931]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11636, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 4f0f1713-fae4-4c7e-a871-d2d844b918da
TID: [-1234] [] [2024-08-11 07:24:08,931]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 07:24:08,934]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 07:24:08,935]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:44282, CORRELATION_ID = 4f0f1713-fae4-4c7e-a871-d2d844b918da, CONNECTION = http-incoming-380634
TID: [-1234] [] [2024-08-11 07:24:08,945]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-380634, CORRELATION_ID = 4f0f1713-fae4-4c7e-a871-d2d844b918da
TID: [-1234] [] [2024-08-11 07:27:16,632]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 0f6d1e02-3a0d-4e9c-90f7-e25a912ec52b
TID: [-1234] [] [2024-08-11 07:27:16,633]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11662, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 0f6d1e02-3a0d-4e9c-90f7-e25a912ec52b
TID: [-1234] [] [2024-08-11 07:27:16,635]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 07:29:46,236]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : HoSoIGATE--v1.0.0_APIproductionEndpoint with address http://************:8095/savis/categories/api/v1/document currently SUSPENDED will now be marked active since it processed its last message
TID: [-1234] [] [2024-08-11 07:30:18,501]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 45503b19-2c18-4419-a6ea-3486ca77477f
TID: [-1234] [] [2024-08-11 07:30:18,502]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11677, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 45503b19-2c18-4419-a6ea-3486ca77477f
TID: [-1234] [] [2024-08-11 07:30:18,503]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 07:33:21,928]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 07:33:21,929]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:59786, CORRELATION_ID = aebbf787-0998-48a8-82cc-2d02e479cbc8, CONNECTION = http-incoming-380911
TID: [-1234] [] [2024-08-11 07:33:22,016]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = aebbf787-0998-48a8-82cc-2d02e479cbc8
TID: [-1234] [] [2024-08-11 07:33:22,017]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11693, SOCKET_TIMEOUT = 180000, CORRELATION_ID = aebbf787-0998-48a8-82cc-2d02e479cbc8
TID: [-1234] [] [2024-08-11 07:33:22,018]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 07:33:22,064]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-380911, CORRELATION_ID = aebbf787-0998-48a8-82cc-2d02e479cbc8
TID: [-1234] [] [2024-08-11 07:33:23,744]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11686, SOCKET_TIMEOUT = 180000, CORRELATION_ID = abb40e70-2f7a-4e83-a58d-b8b46a245517
TID: [-1234] [] [2024-08-11 07:33:23,745]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 07:33:24,658]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-11 07:38:03,533]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b476f975-4d83-4a34-84a2-a60d0ccb2593
TID: [-1234] [] [2024-08-11 07:38:03,533]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d220262c-7877-4914-980c-cd9f43ef6abf
TID: [-1234] [] [2024-08-11 07:38:03,592]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4738d3dd-b16b-4280-822d-6686e9ae8f02
TID: [-1234] [] [2024-08-11 07:38:03,593]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b5ddd808-b4be-496f-92a9-84e48573f9bd
TID: [-1234] [] [2024-08-11 07:38:03,608]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 00fcc907-ba40-4ffa-85b3-d1e12d7e8669
TID: [-1234] [] [2024-08-11 07:38:03,632]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8e1c0b09-1749-4433-8324-667020a70fe1
TID: [-1234] [] [2024-08-11 07:38:04,281]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f3e3921f-99bc-49e4-b655-895f546f5d5b
TID: [-1234] [] [2024-08-11 07:48:24,178]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ec8331e2-3cec-46ff-bd55-493ad64a7ad8
TID: [-1234] [] [2024-08-11 08:03:25,055]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-11 08:11:18,045]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11727, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 60be70a5-cc02-4cf9-8c7d-f0e38bedf37c
TID: [-1234] [] [2024-08-11 08:11:18,048]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 08:14:57,345]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 95e31a4e-e586-4eb7-80cb-539d38b5421b
TID: [-1234] [] [2024-08-11 08:14:57,346]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11720, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 95e31a4e-e586-4eb7-80cb-539d38b5421b
TID: [-1234] [] [2024-08-11 08:14:57,347]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 08:20:02,651]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 08:20:02,652]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:55864, CORRELATION_ID = 0cfe52ff-8d17-4393-ba18-4b6708d1f97a, CONNECTION = http-incoming-383674
TID: [-1234] [] [2024-08-11 08:20:02,702]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 0cfe52ff-8d17-4393-ba18-4b6708d1f97a
TID: [-1234] [] [2024-08-11 08:20:02,702]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11736, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 0cfe52ff-8d17-4393-ba18-4b6708d1f97a
TID: [-1234] [] [2024-08-11 08:20:02,704]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 08:20:02,721]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-383674, CORRELATION_ID = 0cfe52ff-8d17-4393-ba18-4b6708d1f97a
TID: [-1234] [] [2024-08-11 08:23:41,860]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 8170cdac-f0f6-4aaa-9c43-41b657d930b9
TID: [-1234] [] [2024-08-11 08:23:41,861]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11756, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 8170cdac-f0f6-4aaa-9c43-41b657d930b9
TID: [-1234] [] [2024-08-11 08:23:41,862]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 08:23:42,765]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = db6fc75c-d03a-4f0e-8b42-000779f2e1eb
TID: [-1234] [] [2024-08-11 08:23:42,767]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11748, SOCKET_TIMEOUT = 180000, CORRELATION_ID = db6fc75c-d03a-4f0e-8b42-000779f2e1eb
TID: [-1234] [] [2024-08-11 08:23:42,768]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 08:23:44,139]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11746, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 40c690e1-d474-4b46-be5c-dd78e0511829
TID: [-1234] [] [2024-08-11 08:23:44,141]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 08:26:59,060]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11760, SOCKET_TIMEOUT = 180000, CORRELATION_ID = fbb9b737-cd03-4ee6-8935-003ee09ad32a
TID: [-1234] [] [2024-08-11 08:26:59,063]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 08:30:00,441]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 08:30:00,442]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:56032, CORRELATION_ID = 6a5fdf5e-2072-455b-b9b8-4bbf029414f4, CONNECTION = http-incoming-384101
TID: [-1234] [] [2024-08-11 08:30:00,504]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 6a5fdf5e-2072-455b-b9b8-4bbf029414f4
TID: [-1234] [] [2024-08-11 08:30:00,504]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11766, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 6a5fdf5e-2072-455b-b9b8-4bbf029414f4
TID: [-1234] [] [2024-08-11 08:30:00,505]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 08:30:00,524]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-384101, CORRELATION_ID = 6a5fdf5e-2072-455b-b9b8-4bbf029414f4
TID: [-1234] [] [2024-08-11 08:33:01,775]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11783, SOCKET_TIMEOUT = 180000, CORRELATION_ID = ed645de8-b5af-482b-913b-df2bfaa58761
TID: [-1234] [] [2024-08-11 08:33:01,776]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 08:33:05,395]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11782, SOCKET_TIMEOUT = 180000, CORRELATION_ID = d3dee577-54b4-44d6-a212-21da1daf79a5
TID: [-1234] [] [2024-08-11 08:33:05,396]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 08:33:25,967]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-11 08:38:02,715]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 63d34f1d-30c1-4fa9-ab65-00d468c4f762
TID: [-1234] [] [2024-08-11 08:38:07,281]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fe4f6e91-3f37-4263-bfbf-d084e44c563d
TID: [-1234] [] [2024-08-11 09:03:26,670]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-11 09:10:51,154]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 09:10:51,155]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:47662, CORRELATION_ID = 696e3dc8-1f13-4218-beec-66ccd70c7cde, CONNECTION = http-incoming-386374
TID: [-1234] [] [2024-08-11 09:10:51,270]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 696e3dc8-1f13-4218-beec-66ccd70c7cde
TID: [-1234] [] [2024-08-11 09:10:51,271]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11822, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 696e3dc8-1f13-4218-beec-66ccd70c7cde
TID: [-1234] [] [2024-08-11 09:10:51,272]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 09:10:51,291]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-386374, CORRELATION_ID = 696e3dc8-1f13-4218-beec-66ccd70c7cde
TID: [-1234] [] [2024-08-11 09:14:28,076]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 09:14:28,077]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:44050, CORRELATION_ID = 52d2bc04-0ad9-4f82-aeed-1a36a3d92bd4, CONNECTION = http-incoming-386613
TID: [-1234] [] [2024-08-11 09:14:28,518]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11818, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 52d2bc04-0ad9-4f82-aeed-1a36a3d92bd4
TID: [-1234] [] [2024-08-11 09:14:28,520]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 09:14:28,538]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-386613, CORRELATION_ID = 52d2bc04-0ad9-4f82-aeed-1a36a3d92bd4
TID: [-1234] [] [2024-08-11 09:19:47,812]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 09:19:47,813]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:37880, CORRELATION_ID = 69130f7c-be96-4924-8ee7-d36e5d48d1d2, CONNECTION = http-incoming-387043
TID: [-1234] [] [2024-08-11 09:19:47,974]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 69130f7c-be96-4924-8ee7-d36e5d48d1d2
TID: [-1234] [] [2024-08-11 09:19:47,974]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11826, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 69130f7c-be96-4924-8ee7-d36e5d48d1d2
TID: [-1234] [] [2024-08-11 09:19:47,976]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 09:19:47,994]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-387043, CORRELATION_ID = 69130f7c-be96-4924-8ee7-d36e5d48d1d2
TID: [-1234] [] [2024-08-11 09:23:34,264]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 09:23:34,265]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:59996, CORRELATION_ID = 07ee3052-bb7f-4eb6-be73-e84f7d8afea3, CONNECTION = http-incoming-387194
TID: [-1234] [] [2024-08-11 09:23:34,455]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11830, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 07ee3052-bb7f-4eb6-be73-e84f7d8afea3
TID: [-1234] [] [2024-08-11 09:23:34,457]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 09:23:34,474]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-387194, CORRELATION_ID = 07ee3052-bb7f-4eb6-be73-e84f7d8afea3
TID: [-1234] [] [2024-08-11 09:23:35,723]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11838, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 507f0125-249d-4797-b1ed-7e332da170b8
TID: [-1234] [] [2024-08-11 09:23:35,724]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 09:23:36,969]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11847, SOCKET_TIMEOUT = 180000, CORRELATION_ID = f62781d8-f9b5-405f-89ca-a486f46746a5
TID: [-1234] [] [2024-08-11 09:23:36,970]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 09:26:58,385]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11856, SOCKET_TIMEOUT = 180000, CORRELATION_ID = abede1ce-4403-44c3-9542-09e2813cb197
TID: [-1234] [] [2024-08-11 09:26:58,388]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 09:30:02,088]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11866, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 02d16b1a-64f8-4367-9433-32eed903f407
TID: [-1234] [] [2024-08-11 09:30:02,098]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 09:33:06,262]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 9f26b4c5-f94e-4eee-9d69-fc058917c1ca
TID: [-1234] [] [2024-08-11 09:33:06,263]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11890, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 9f26b4c5-f94e-4eee-9d69-fc058917c1ca
TID: [-1234] [] [2024-08-11 09:33:06,264]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 09:33:08,612]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 09:33:08,613]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:52518, CORRELATION_ID = d88fa3db-9b05-4724-af74-a6a0e2d431c5, CONNECTION = http-incoming-387594
TID: [-1234] [] [2024-08-11 09:33:08,627]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11891, SOCKET_TIMEOUT = 180000, CORRELATION_ID = d88fa3db-9b05-4724-af74-a6a0e2d431c5
TID: [-1234] [] [2024-08-11 09:33:08,628]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 09:33:08,646]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-387594, CORRELATION_ID = d88fa3db-9b05-4724-af74-a6a0e2d431c5
TID: [-1234] [] [2024-08-11 09:33:08,967]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fb58eeba-3050-43c4-8710-409a2330dc15
TID: [-1234] [] [2024-08-11 09:33:09,002]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f29f7ec4-31f5-4509-87c1-11324c7ae4d6
TID: [-1234] [] [2024-08-11 09:33:09,475]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 901796b6-5cd7-4061-b36c-ab1ce60132a5
TID: [-1234] [] [2024-08-11 09:33:27,709]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-11 09:39:19,957]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c79cdff6-4b14-4300-9688-da84c9b1363c
TID: [-1234] [] [2024-08-11 09:39:24,335]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2968be6f-7c59-4ed2-a1e0-fc4130ab0b8e
TID: [-1234] [] [2024-08-11 10:03:28,220]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-11 10:12:36,783]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11917, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 1694d304-75b3-49be-84f2-b0291919e00e
TID: [-1234] [] [2024-08-11 10:12:36,785]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 10:16:05,746]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 10:16:05,747]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:45178, CORRELATION_ID = 97935d4d-3bd0-4e4c-a3fd-6d62d6cdafe0, CONNECTION = http-incoming-389901
TID: [-1234] [] [2024-08-11 10:16:05,757]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11928, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 97935d4d-3bd0-4e4c-a3fd-6d62d6cdafe0
TID: [-1234] [] [2024-08-11 10:16:05,758]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 10:16:05,790]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-389901, CORRELATION_ID = 97935d4d-3bd0-4e4c-a3fd-6d62d6cdafe0
TID: [-1234] [] [2024-08-11 10:21:05,880]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11935, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 7b7d37d5-c18d-4810-b69e-16238ae82f5c
TID: [-1234] [] [2024-08-11 10:21:05,882]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 10:24:34,940]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = b4ff95d6-89b8-469f-89f6-673351980b45
TID: [-1234] [] [2024-08-11 10:24:34,941]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11949, SOCKET_TIMEOUT = 180000, CORRELATION_ID = b4ff95d6-89b8-469f-89f6-673351980b45
TID: [-1234] [] [2024-08-11 10:24:34,942]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 10:24:36,574]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11941, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 8832c1ec-9d74-4be8-95c8-15bea969c097
TID: [-1234] [] [2024-08-11 10:24:36,575]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 10:24:39,835]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 81fc31ae-28d8-44e7-9583-413eb07693b4
TID: [-1234] [] [2024-08-11 10:24:40,073]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11940, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 7e1108c5-a08c-4d2c-aeb6-e9f6a5186bc2
TID: [-1234] [] [2024-08-11 10:24:40,074]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 10:24:40,074]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 10:24:40,075]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:56952, CORRELATION_ID = 7e1108c5-a08c-4d2c-aeb6-e9f6a5186bc2, CONNECTION = http-incoming-390572
TID: [-1234] [] [2024-08-11 10:24:40,105]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-390572, CORRELATION_ID = 7e1108c5-a08c-4d2c-aeb6-e9f6a5186bc2
TID: [-1234] [] [2024-08-11 10:27:59,614]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fa487c44-fa92-446e-98ad-f6bb48cbade0
TID: [-1234] [] [2024-08-11 10:27:59,844]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5c82964c-7665-48c9-a4f3-9d2857d900fc
TID: [-1234] [] [2024-08-11 10:27:59,927]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ed9f523b-4aaa-44da-9aaa-05c8ac391bfc
TID: [-1234] [] [2024-08-11 10:28:00,312]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11963, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 06cd7dee-137b-4c3d-af3e-5233fdb64c36
TID: [-1234] [] [2024-08-11 10:28:00,313]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 10:28:00,323]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 10:28:00,324]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:49224, CORRELATION_ID = 06cd7dee-137b-4c3d-af3e-5233fdb64c36, CONNECTION = http-incoming-390809
TID: [-1234] [] [2024-08-11 10:28:00,330]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-390809, CORRELATION_ID = 06cd7dee-137b-4c3d-af3e-5233fdb64c36
TID: [-1234] [] [2024-08-11 10:31:06,747]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 10:31:06,748]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:56080, CORRELATION_ID = 6e80363b-f276-4b67-916b-8d4460f654a3, CONNECTION = http-incoming-390838
TID: [-1234] [] [2024-08-11 10:31:06,749]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11968, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 0ba6ec01-346d-444e-b7b6-7b3d51076ee0
TID: [-1234] [] [2024-08-11 10:31:06,750]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 10:31:06,754]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 6e80363b-f276-4b67-916b-8d4460f654a3
TID: [-1234] [] [2024-08-11 10:31:06,755]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11975, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 6e80363b-f276-4b67-916b-8d4460f654a3
TID: [-1234] [] [2024-08-11 10:31:06,755]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 10:31:06,779]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-390838, CORRELATION_ID = 6e80363b-f276-4b67-916b-8d4460f654a3
TID: [-1234] [] [2024-08-11 10:34:55,005]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-11 10:35:49,626]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b6d83c3e-5521-4539-94e0-e1157a162423
TID: [-1234] [] [2024-08-11 10:35:49,670]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c03b5804-b001-4d97-b7a2-c3c529f745af
TID: [-1234] [] [2024-08-11 10:35:50,151]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 52b45977-03f7-4205-a802-84636def6f47
TID: [-1234] [] [2024-08-11 10:51:32,207]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 80b902b0-64c7-4feb-aaa6-3b2623608173
TID: [-1234] [] [2024-08-11 11:04:55,180]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-11 11:12:17,623]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12019, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 40707ee9-3a80-474f-b395-84e0c38f01d2
TID: [-1234] [] [2024-08-11 11:12:17,626]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 11:15:47,326]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12010, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 0fe5da08-30b0-4af0-86a8-79e0a639d505
TID: [-1234] [] [2024-08-11 11:15:47,328]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 11:15:47,344]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 11:15:47,345]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:59086, CORRELATION_ID = 0fe5da08-30b0-4af0-86a8-79e0a639d505, CONNECTION = http-incoming-393204
TID: [-1234] [] [2024-08-11 11:20:57,252]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12012, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 92f81e61-8025-4010-9f51-0af70f48db4f
TID: [-1234] [] [2024-08-11 11:20:57,254]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 11:24:48,858]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12030, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 0bdf3046-b9ab-42be-ae5e-912ec5f74717
TID: [-1234] [] [2024-08-11 11:24:48,860]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 11:24:49,605]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12041, SOCKET_TIMEOUT = 180000, CORRELATION_ID = a4f749d5-27d0-4a31-bfaa-2e01c1e8ee08
TID: [-1234] [] [2024-08-11 11:24:49,606]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 11:24:51,003]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12037, SOCKET_TIMEOUT = 180000, CORRELATION_ID = a81093c4-fe3d-438d-be91-9d1c00fca845
TID: [-1234] [] [2024-08-11 11:24:51,003]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 11:24:51,004]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:40920, CORRELATION_ID = a81093c4-fe3d-438d-be91-9d1c00fca845, CONNECTION = http-incoming-393867
TID: [-1234] [] [2024-08-11 11:24:51,004]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 11:24:51,046]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-393867, CORRELATION_ID = a81093c4-fe3d-438d-be91-9d1c00fca845
TID: [-1234] [] [2024-08-11 11:28:00,079]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = cf90c281-62bc-4678-b189-6f1f6d77d69c
TID: [-1234] [] [2024-08-11 11:28:00,080]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12056, SOCKET_TIMEOUT = 180000, CORRELATION_ID = cf90c281-62bc-4678-b189-6f1f6d77d69c
TID: [-1234] [] [2024-08-11 11:28:00,081]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 11:28:02,908]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3ee1da4a-17aa-4395-af63-1a413af883a2
TID: [-1234] [] [2024-08-11 11:31:07,142]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12065, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 4f5a0993-0eff-4c0d-8f23-41e6f87b983f
TID: [-1234] [] [2024-08-11 11:31:07,143]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 11:34:08,656]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12082, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 9c5c97ae-a37c-4291-8691-ca76c7e2be77
TID: [-1234] [] [2024-08-11 11:34:08,658]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 11:34:09,503]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = b693f3f5-7961-4075-99bd-af30ea87fc95
TID: [-1234] [] [2024-08-11 11:34:09,504]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12076, SOCKET_TIMEOUT = 180000, CORRELATION_ID = b693f3f5-7961-4075-99bd-af30ea87fc95
TID: [-1234] [] [2024-08-11 11:34:09,505]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 11:35:53,076]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-11 11:38:53,436]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 924ae5f4-3e1e-4ccd-86bf-40674b67eede
TID: [-1234] [] [2024-08-11 11:38:54,187]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e053964e-d9d7-40b8-8bb2-803d79a586ad
TID: [-1234] [] [2024-08-11 12:05:53,719]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-11 12:10:42,062]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12123, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 6e33af77-921d-4a56-9f05-f16e2c581d97
TID: [-1234] [] [2024-08-11 12:10:42,064]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 12:14:10,366]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 12:14:10,367]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:59516, CORRELATION_ID = 0724787c-f78a-4c17-854d-841327bb3393, CONNECTION = http-incoming-396437
TID: [-1234] [] [2024-08-11 12:14:10,372]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12122, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 0724787c-f78a-4c17-854d-841327bb3393
TID: [-1234] [] [2024-08-11 12:14:10,374]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 12:14:10,392]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-396437, CORRELATION_ID = 0724787c-f78a-4c17-854d-841327bb3393
TID: [-1234] [] [2024-08-11 12:19:21,631]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12131, SOCKET_TIMEOUT = 180000, CORRELATION_ID = dda563d8-5cdb-4d5c-abd2-6f9e3d4a7a65
TID: [-1234] [] [2024-08-11 12:19:21,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 12:23:15,957]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = b2c83405-b294-4c75-abb0-68627f687bd6
TID: [-1234] [] [2024-08-11 12:23:15,958]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12127, SOCKET_TIMEOUT = 180000, CORRELATION_ID = b2c83405-b294-4c75-abb0-68627f687bd6
TID: [-1234] [] [2024-08-11 12:23:15,959]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 12:23:17,350]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12143, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 09af7923-e63c-4a24-bd7a-9734b8027efa
TID: [-1234] [] [2024-08-11 12:23:17,352]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 12:23:17,358]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 12:23:17,358]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:33430, CORRELATION_ID = 09af7923-e63c-4a24-bd7a-9734b8027efa, CONNECTION = http-incoming-397086
TID: [-1234] [] [2024-08-11 12:23:17,369]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-397086, CORRELATION_ID = 09af7923-e63c-4a24-bd7a-9734b8027efa
TID: [-1234] [] [2024-08-11 12:23:17,959]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 12:23:17,960]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:33470, CORRELATION_ID = b53ab257-8f37-4070-8cf8-669d2bcd52a2, CONNECTION = http-incoming-397091
TID: [-1234] [] [2024-08-11 12:23:17,961]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = b53ab257-8f37-4070-8cf8-669d2bcd52a2
TID: [-1234] [] [2024-08-11 12:23:17,961]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12147, SOCKET_TIMEOUT = 180000, CORRELATION_ID = b53ab257-8f37-4070-8cf8-669d2bcd52a2
TID: [-1234] [] [2024-08-11 12:23:17,962]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 12:23:18,009]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-397091, CORRELATION_ID = b53ab257-8f37-4070-8cf8-669d2bcd52a2
TID: [-1234] [] [2024-08-11 12:26:34,795]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12165, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 6e5c61b4-5fc0-4b76-a4ee-184339972ecb
TID: [-1234] [] [2024-08-11 12:26:34,796]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 12:29:37,298]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12184, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 46310350-ee16-47a7-b80b-1a8e7ea436a4
TID: [-1234] [] [2024-08-11 12:29:37,300]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 12:32:38,714]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 9b367cc1-b34d-4ff9-8fb0-93c915ecdeff
TID: [-1234] [] [2024-08-11 12:32:38,715]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12186, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 9b367cc1-b34d-4ff9-8fb0-93c915ecdeff
TID: [-1234] [] [2024-08-11 12:32:38,716]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 12:32:45,340]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12199, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 96dc5d82-746f-4223-89f6-4495e51bed3e
TID: [-1234] [] [2024-08-11 12:32:45,341]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 12:36:09,938]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-11 12:37:01,751]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 537d9fed-88d3-4921-906c-90b9dd6e90e8
TID: [-1234] [] [2024-08-11 12:37:04,875]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 66d461db-c016-4136-a29a-a922904ef2c5
TID: [-1234] [] [2024-08-11 12:37:12,484]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9184cc30-d8e9-4922-b3be-44f3fa9996f2
TID: [-1234] [] [2024-08-11 12:40:21,776]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 120b373d-1f28-4c3c-b8fd-05e628b9ef3e
TID: [-1234] [] [2024-08-11 12:40:21,776]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 20452f54-4d1b-4294-a10c-ec5c47156e51
TID: [-1234] [] [2024-08-11 12:40:21,805]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 045e3859-5fc4-4245-abb9-fc603ac09781
TID: [-1234] [] [2024-08-11 13:06:10,156]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-11 13:10:58,211]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12238, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 12a84f20-5aac-42e3-87be-56c2ea6bf098
TID: [-1234] [] [2024-08-11 13:10:58,213]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 13:14:19,197]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 6312de97-917a-4682-b23f-34483b5d9528
TID: [-1234] [] [2024-08-11 13:14:19,198]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12246, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 6312de97-917a-4682-b23f-34483b5d9528
TID: [-1234] [] [2024-08-11 13:14:19,200]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 13:19:13,890]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = b525b3a3-4f8d-4024-b444-ccbb1ef74e0e
TID: [-1234] [] [2024-08-11 13:19:13,891]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12257, SOCKET_TIMEOUT = 180000, CORRELATION_ID = b525b3a3-4f8d-4024-b444-ccbb1ef74e0e
TID: [-1234] [] [2024-08-11 13:19:13,892]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 13:22:43,295]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 820135e6-b0f8-423e-be31-f60a9181388b
TID: [-1234] [] [2024-08-11 13:22:43,296]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12261, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 820135e6-b0f8-423e-be31-f60a9181388b
TID: [-1234] [] [2024-08-11 13:22:43,297]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 13:22:43,702]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 13:22:43,702]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:34520, CORRELATION_ID = ee5e2bb5-63f4-431c-849a-4255d3dece89, CONNECTION = http-incoming-400279
TID: [-1234] [] [2024-08-11 13:22:43,852]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12265, SOCKET_TIMEOUT = 180000, CORRELATION_ID = ee5e2bb5-63f4-431c-849a-4255d3dece89
TID: [-1234] [] [2024-08-11 13:22:43,853]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 13:22:43,869]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-400279, CORRELATION_ID = ee5e2bb5-63f4-431c-849a-4255d3dece89
TID: [-1234] [] [2024-08-11 13:22:47,875]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 13:22:47,876]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:34764, CORRELATION_ID = 55960f82-0cce-4ce9-b44d-baa7f274b196, CONNECTION = http-incoming-400308
TID: [-1234] [] [2024-08-11 13:22:47,878]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12263, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 55960f82-0cce-4ce9-b44d-baa7f274b196
TID: [-1234] [] [2024-08-11 13:22:47,879]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 13:22:47,896]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-400308, CORRELATION_ID = 55960f82-0cce-4ce9-b44d-baa7f274b196
TID: [-1234] [] [2024-08-11 13:25:56,847]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12287, SOCKET_TIMEOUT = 180000, CORRELATION_ID = c37f6240-278e-4580-adda-df6d467b40e5
TID: [-1234] [] [2024-08-11 13:25:56,848]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 13:29:01,734]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12279, SOCKET_TIMEOUT = 180000, CORRELATION_ID = c59c7cfd-89fd-4929-8943-f685012bb68b
TID: [-1234] [] [2024-08-11 13:29:01,735]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 13:32:04,518]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12293, SOCKET_TIMEOUT = 180000, CORRELATION_ID = f778d61a-5eed-4ecb-91d9-0ad3046cadab
TID: [-1234] [] [2024-08-11 13:32:04,520]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 13:32:10,274]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 1f92e3c1-630a-4de4-b558-501e57716f99
TID: [-1234] [] [2024-08-11 13:32:10,275]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12290, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 1f92e3c1-630a-4de4-b558-501e57716f99
TID: [-1234] [] [2024-08-11 13:32:10,277]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 13:36:51,281]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2da0018b-2f53-4db5-952f-67ff3a9ba6a6
TID: [-1234] [] [2024-08-11 13:36:51,329]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = adaa3d2e-1798-407a-9cd0-6844fe8713eb
TID: [-1234] [] [2024-08-11 13:44:26,899]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-11 14:10:24,581]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 14:10:24,583]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:38208, CORRELATION_ID = 3201d299-1002-4192-8d95-5c6e9fc7b123, CONNECTION = http-incoming-402655
TID: [-1234] [] [2024-08-11 14:10:24,680]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 3201d299-1002-4192-8d95-5c6e9fc7b123
TID: [-1234] [] [2024-08-11 14:10:24,680]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12351, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 3201d299-1002-4192-8d95-5c6e9fc7b123
TID: [-1234] [] [2024-08-11 14:10:24,682]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 14:10:24,702]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-402655, CORRELATION_ID = 3201d299-1002-4192-8d95-5c6e9fc7b123
TID: [-1234] [] [2024-08-11 14:13:50,818]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4e9bc5fc-bc1d-42ee-b251-33a325c44b75
TID: [-1234] [] [2024-08-11 14:13:50,930]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e01eda3b-0862-47e1-ae37-b136d6b68853
TID: [-1234] [] [2024-08-11 14:13:51,119]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0aa571e4-4619-4d53-bf84-0a3315eff84b
TID: [-1234] [] [2024-08-11 14:13:51,134]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 14:13:51,135]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:46688, CORRELATION_ID = 4cf255a5-fe88-41e0-926d-d0c8ec4aa321, CONNECTION = http-incoming-402890
TID: [-1234] [] [2024-08-11 14:13:51,160]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 4cf255a5-fe88-41e0-926d-d0c8ec4aa321
TID: [-1234] [] [2024-08-11 14:13:51,160]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12337, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 4cf255a5-fe88-41e0-926d-d0c8ec4aa321
TID: [-1234] [] [2024-08-11 14:13:51,161]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 14:13:51,181]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-402890, CORRELATION_ID = 4cf255a5-fe88-41e0-926d-d0c8ec4aa321
TID: [-1234] [] [2024-08-11 14:14:27,040]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-11 14:18:31,164]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 14:18:31,165]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:43536, CORRELATION_ID = 966bf2ac-1151-453b-b721-f7e0a4671002, CONNECTION = http-incoming-403292
TID: [-1234] [] [2024-08-11 14:18:31,177]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12363, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 966bf2ac-1151-453b-b721-f7e0a4671002
TID: [-1234] [] [2024-08-11 14:18:31,178]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 14:18:31,205]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-403292, CORRELATION_ID = 966bf2ac-1151-453b-b721-f7e0a4671002
TID: [-1234] [] [2024-08-11 14:22:20,320]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 8f7c15da-b38e-4cf9-8f49-10a04902c313
TID: [-1234] [] [2024-08-11 14:22:20,321]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12381, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 8f7c15da-b38e-4cf9-8f49-10a04902c313
TID: [-1234] [] [2024-08-11 14:22:20,322]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 14:22:20,448]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 14:22:20,449]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:54470, CORRELATION_ID = 80857bfa-d495-441e-8a4a-3bb40db9efc2, CONNECTION = http-incoming-403496
TID: [-1234] [] [2024-08-11 14:22:20,622]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12357, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 80857bfa-d495-441e-8a4a-3bb40db9efc2
TID: [-1234] [] [2024-08-11 14:22:20,623]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 14:22:20,636]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-403496, CORRELATION_ID = 80857bfa-d495-441e-8a4a-3bb40db9efc2
TID: [-1234] [] [2024-08-11 14:22:23,899]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 14:22:23,900]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:37084, CORRELATION_ID = cd2eca43-bb7c-4be0-ba28-a0d5cd65e923, CONNECTION = http-incoming-403530
TID: [-1234] [] [2024-08-11 14:22:24,627]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12359, SOCKET_TIMEOUT = 180000, CORRELATION_ID = cd2eca43-bb7c-4be0-ba28-a0d5cd65e923
TID: [-1234] [] [2024-08-11 14:22:24,628]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 14:22:24,645]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-403530, CORRELATION_ID = cd2eca43-bb7c-4be0-ba28-a0d5cd65e923
TID: [-1234] [] [2024-08-11 14:25:34,467]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = b80818dd-5eee-4c10-a875-5b3069e30607
TID: [-1234] [] [2024-08-11 14:25:34,468]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12386, SOCKET_TIMEOUT = 180000, CORRELATION_ID = b80818dd-5eee-4c10-a875-5b3069e30607
TID: [-1234] [] [2024-08-11 14:25:34,469]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 14:28:40,095]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 946f47e0-07c1-41e1-a236-bf0ae5257426
TID: [-1234] [] [2024-08-11 14:28:40,096]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12399, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 946f47e0-07c1-41e1-a236-bf0ae5257426
TID: [-1234] [] [2024-08-11 14:28:40,097]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 14:31:40,697]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12412, SOCKET_TIMEOUT = 180000, CORRELATION_ID = b03b5583-6262-4dd4-978a-c563d464908a
TID: [-1234] [] [2024-08-11 14:31:40,698]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 14:31:42,877]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = c5ce75cb-7684-4edf-8962-2b133cf7f2e8
TID: [-1234] [] [2024-08-11 14:31:42,878]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12397, SOCKET_TIMEOUT = 180000, CORRELATION_ID = c5ce75cb-7684-4edf-8962-2b133cf7f2e8
TID: [-1234] [] [2024-08-11 14:31:42,879]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 14:36:17,160]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 81c70ea4-2c1f-47ac-a640-c5ff959e4152
TID: [-1234] [] [2024-08-11 14:36:17,167]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 80087a26-ef85-4fff-a428-9de2c78786cc
TID: [-1234] [] [2024-08-11 14:36:18,565]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e8b850f9-fd7b-423a-b56f-d61a50a52542
TID: [-1234] [] [2024-08-11 14:36:18,639]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0c643d21-94f7-4f18-800b-ebc4ca0e1e3d
TID: [-1234] [] [2024-08-11 14:36:18,645]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 59100106-17f1-4b97-9010-d9a2fe1e4106
TID: [-1234] [] [2024-08-11 14:44:28,066]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-11 15:12:00,167]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 15:12:00,168]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:46870, CORRELATION_ID = 2f6a7130-ac80-44ea-a25c-9e1ef081ebea, CONNECTION = http-incoming-405869
TID: [-1234] [] [2024-08-11 15:12:00,358]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12436, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 2f6a7130-ac80-44ea-a25c-9e1ef081ebea
TID: [-1234] [] [2024-08-11 15:12:00,360]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 15:12:00,405]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-405869, CORRELATION_ID = 2f6a7130-ac80-44ea-a25c-9e1ef081ebea
TID: [-1234] [] [2024-08-11 15:14:28,302]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-11 15:15:34,831]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12447, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 9fd0824f-7ba3-466c-b79c-3dfc917a14ee
TID: [-1234] [] [2024-08-11 15:15:34,832]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 15:15:34,867]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ee903d76-49ae-4907-9306-777c3a71921a
TID: [-1234] [] [2024-08-11 15:18:05,274]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20240811&denNgay=20240811&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20240811&denNgay=20240811&maTthc=
TID: [-1234] [] [2024-08-11 15:18:05,369]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2024-08-11 15:20:44,092]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12458, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 78af33fb-3766-4673-aa17-3219da03f18f
TID: [-1234] [] [2024-08-11 15:20:44,093]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 15:24:22,046]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12457, SOCKET_TIMEOUT = 180000, CORRELATION_ID = a5cf00b8-8a19-4de2-a941-a65b44802d46
TID: [-1234] [] [2024-08-11 15:24:22,048]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 15:24:23,049]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12477, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 60f14498-7d22-41e3-8e6b-9af263f695ce
TID: [-1234] [] [2024-08-11 15:24:23,050]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 15:24:24,616]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12476, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 012362d7-cc8f-4e36-a5b7-a6e5f160ac65
TID: [-1234] [] [2024-08-11 15:24:24,617]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 15:27:32,537]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 60477730-582a-4336-bf1b-f9a4890747e0
TID: [-1234] [] [2024-08-11 15:27:32,539]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12487, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 60477730-582a-4336-bf1b-f9a4890747e0
TID: [-1234] [] [2024-08-11 15:27:32,540]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 15:30:08,128]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20240811&denNgay=20240811&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20240811&denNgay=20240811&maTthc=
TID: [-1234] [] [2024-08-11 15:30:08,176]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-08-11 15:30:35,307]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12500, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 71c2ef28-1868-4ab4-b0c0-0259d00b2c35
TID: [-1234] [] [2024-08-11 15:30:35,308]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 15:33:36,750]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 8957cfa4-3615-4a98-8e45-10500a02ad12
TID: [-1234] [] [2024-08-11 15:33:36,751]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12506, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 8957cfa4-3615-4a98-8e45-10500a02ad12
TID: [-1234] [] [2024-08-11 15:33:36,752]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 15:33:42,306]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 34b87807-ed35-42df-bb20-eda41d8642f9
TID: [-1234] [] [2024-08-11 15:33:42,306]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12528, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 34b87807-ed35-42df-bb20-eda41d8642f9
TID: [-1234] [] [2024-08-11 15:33:42,307]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 15:38:12,688]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 46b77848-fbf3-49df-9c91-3a546664fb62
TID: [-1234] [] [2024-08-11 15:38:12,690]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fe341e3d-dde4-4ccf-a3bf-d20750817bba
TID: [-1234] [] [2024-08-11 15:38:12,724]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 05ffc782-63f5-4e57-ab2c-6f9bfc59f2a4
TID: [-1234] [] [2024-08-11 15:38:12,729]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 74ca75f8-3169-43fd-9ac3-a6d214407172
TID: [-1234] [] [2024-08-11 15:44:28,492]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-11 16:11:41,423]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12561, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 4206c833-ac7f-40da-84d3-0ae4c908c1c6
TID: [-1234] [] [2024-08-11 16:11:41,426]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 16:14:28,878]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-11 16:15:05,698]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12554, SOCKET_TIMEOUT = 180000, CORRELATION_ID = b5376ff4-b2fa-4198-9d40-bfdc925238ed
TID: [-1234] [] [2024-08-11 16:15:05,699]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 16:20:04,075]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 16:20:04,076]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:56294, CORRELATION_ID = cf896bd9-0a64-4191-9045-e9e8b5edbcb9, CONNECTION = http-incoming-409761
TID: [-1234] [] [2024-08-11 16:20:04,250]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12574, SOCKET_TIMEOUT = 180000, CORRELATION_ID = cf896bd9-0a64-4191-9045-e9e8b5edbcb9
TID: [-1234] [] [2024-08-11 16:20:04,252]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 16:20:04,276]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-409761, CORRELATION_ID = cf896bd9-0a64-4191-9045-e9e8b5edbcb9
TID: [-1234] [] [2024-08-11 16:23:40,423]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12576, SOCKET_TIMEOUT = 180000, CORRELATION_ID = e647e9d7-d734-4190-a2c6-ac7709590137
TID: [-1234] [] [2024-08-11 16:23:40,425]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 16:23:41,407]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12585, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 6bb9b58a-212d-4e73-8f99-388b2711a063
TID: [-1234] [] [2024-08-11 16:23:41,408]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 16:23:43,070]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12588, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 8762c381-5db4-478f-99dc-e2df92f2d501
TID: [-1234] [] [2024-08-11 16:23:43,071]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 16:26:52,389]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12601, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 1af13d1c-087f-4234-b05b-6b3985a80fe3
TID: [-1234] [] [2024-08-11 16:26:52,390]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 16:29:55,487]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12610, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 62b1e653-cf15-4327-9a7a-655618f7b4c8
TID: [-1234] [] [2024-08-11 16:29:55,488]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 16:29:57,475]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7a68c570-0e4e-4384-b5da-354dfc66935e
TID: [-1234] [] [2024-08-11 16:32:10,391]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Include/AdminAjax.ashx?op=UploadOptimize&folder=, HEALTH CHECK URL = /Include/AdminAjax.ashx?op=UploadOptimize&folder=
TID: [-1234] [] [2024-08-11 16:32:58,902]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 26e98742-8040-40ab-8795-2fdc7abfe308
TID: [-1234] [] [2024-08-11 16:32:58,903]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12624, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 26e98742-8040-40ab-8795-2fdc7abfe308
TID: [-1234] [] [2024-08-11 16:32:58,904]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 16:33:01,924]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 35526edd-49b2-4ab4-8085-3326b8bb39c2
TID: [-1234] [] [2024-08-11 16:33:01,925]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12635, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 35526edd-49b2-4ab4-8085-3326b8bb39c2
TID: [-1234] [] [2024-08-11 16:33:01,926]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 16:37:31,872]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1ac569b3-8a18-4ffc-a265-cbdced3d8255
TID: [-1234] [] [2024-08-11 16:37:31,881]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cb5e8194-4bec-444d-a0ac-3151c2219296
TID: [-1234] [] [2024-08-11 16:37:33,322]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 666773c0-acc8-424c-ab22-aa7cff71ea7c
TID: [-1234] [] [2024-08-11 16:37:34,361]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7dbc21be-8fdc-4c9f-84ac-21c404c5d1fa
TID: [-1234] [] [2024-08-11 16:37:34,362]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4c9ca6ea-fb1d-4ee4-ab36-b87abe456a80
TID: [-1234] [] [2024-08-11 16:37:34,409]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 37ef7c8b-029d-4b89-93a1-5a08937823c5
TID: [-1234] [] [2024-08-11 16:37:34,448]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 06bbb11a-3d76-4ec9-b931-9088044d761a
TID: [-1234] [] [2024-08-11 16:44:29,261]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-11 17:12:01,982]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 86c9076f-21a3-4ea2-b3d7-dc569dd3acfd
TID: [-1234] [] [2024-08-11 17:12:01,983]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12657, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 86c9076f-21a3-4ea2-b3d7-dc569dd3acfd
TID: [-1234] [] [2024-08-11 17:12:01,984]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 17:14:34,569]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-11 17:15:26,532]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12658, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 4c9675fc-7ab9-41f9-87c9-8b861a91bc80
TID: [-1234] [] [2024-08-11 17:15:26,534]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 17:20:10,983]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 2e72798c-9326-476f-8901-27b1dd27b6d9
TID: [-1234] [] [2024-08-11 17:20:10,984]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12680, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 2e72798c-9326-476f-8901-27b1dd27b6d9
TID: [-1234] [] [2024-08-11 17:20:10,987]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 17:23:31,744]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 17:23:31,745]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:54614, CORRELATION_ID = 6635348a-a147-43fa-aeea-9a21ba845c5e, CONNECTION = http-incoming-413160
TID: [-1234] [] [2024-08-11 17:23:31,828]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12688, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 6635348a-a147-43fa-aeea-9a21ba845c5e
TID: [-1234] [] [2024-08-11 17:23:31,830]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 17:23:31,847]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-413160, CORRELATION_ID = 6635348a-a147-43fa-aeea-9a21ba845c5e
TID: [-1234] [] [2024-08-11 17:23:35,200]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12694, SOCKET_TIMEOUT = 180000, CORRELATION_ID = d9187a81-b7a1-42ca-b195-ad2a1219448b
TID: [-1234] [] [2024-08-11 17:23:35,201]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 17:23:35,755]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 17:23:35,756]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:35366, CORRELATION_ID = dd62e541-a8b3-4a89-8915-623ff221bdc6, CONNECTION = http-incoming-413221
TID: [-1234] [] [2024-08-11 17:23:35,780]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12696, SOCKET_TIMEOUT = 180000, CORRELATION_ID = dd62e541-a8b3-4a89-8915-623ff221bdc6
TID: [-1234] [] [2024-08-11 17:23:35,782]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 17:23:35,799]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-413221, CORRELATION_ID = dd62e541-a8b3-4a89-8915-623ff221bdc6
TID: [-1234] [] [2024-08-11 17:26:42,678]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12710, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 1f2a78da-ada0-49cb-bc73-e3902aec14b2
TID: [-1234] [] [2024-08-11 17:26:42,680]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 17:29:54,177]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 2aa12526-bd02-41c6-8264-5d1536d5d33a
TID: [-1234] [] [2024-08-11 17:29:54,179]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12726, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 2aa12526-bd02-41c6-8264-5d1536d5d33a
TID: [-1234] [] [2024-08-11 17:29:54,180]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 17:29:55,383]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 91242b10-6a73-4334-aab0-4509e6878eb7
TID: [-1234] [] [2024-08-11 17:32:59,254]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = ca35cfef-7688-4f7f-a906-a80200ce6b32
TID: [-1234] [] [2024-08-11 17:32:59,255]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12730, SOCKET_TIMEOUT = 180000, CORRELATION_ID = ca35cfef-7688-4f7f-a906-a80200ce6b32
TID: [-1234] [] [2024-08-11 17:32:59,256]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 17:33:12,450]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 17:33:12,451]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:52156, CORRELATION_ID = b0b80429-031e-4090-9489-a5e0d2524e29, CONNECTION = http-incoming-413547
TID: [-1234] [] [2024-08-11 17:33:12,450]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12737, SOCKET_TIMEOUT = 180000, CORRELATION_ID = b0b80429-031e-4090-9489-a5e0d2524e29
TID: [-1234] [] [2024-08-11 17:33:12,452]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 17:33:12,469]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-413547, CORRELATION_ID = b0b80429-031e-4090-9489-a5e0d2524e29
TID: [-1234] [] [2024-08-11 17:33:12,664]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d9c06b6a-1214-4f07-a090-6293dda4f400
TID: [-1234] [] [2024-08-11 17:38:06,529]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3a9f1287-a134-45c2-b3f5-f1ee36426a05
TID: [-1234] [] [2024-08-11 17:38:06,532]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 834a58a3-fff4-447a-94d8-3aadc69cb087
TID: [-1234] [] [2024-08-11 17:38:06,532]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 13c64232-cbb3-4bce-bc60-025bc2f93bab
TID: [-1234] [] [2024-08-11 17:38:06,540]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b67aa87e-7401-43b6-99f1-469f0db0fa75
TID: [-1234] [] [2024-08-11 17:38:06,566]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 972059e2-2f26-4fa0-ac92-be95a8da1b77
TID: [-1234] [] [2024-08-11 17:38:07,304]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d7b92ae4-8730-4136-b84c-5eaa1f217742
TID: [-1234] [] [2024-08-11 17:38:07,309]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ddf5460c-a345-4bf7-bc28-a0722bff5257
TID: [-1234] [] [2024-08-11 17:38:07,337]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c87ed7f5-65f7-495b-a41e-c0e208adb1b6
TID: [-1234] [] [2024-08-11 17:38:07,342]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 220c5bd6-c950-4ab6-a64e-3f2ce3e71e96
TID: [-1234] [] [2024-08-11 17:38:07,352]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0d86a48b-0a36-4d05-9506-7ce056271be8
TID: [-1234] [] [2024-08-11 17:38:07,356]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9f50d3e9-5e9f-469d-8341-72fc80198947
TID: [-1234] [] [2024-08-11 17:45:25,281]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-11 18:11:02,224]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 12d0e354-2e42-4948-9e9b-e70a05284e07
TID: [-1234] [] [2024-08-11 18:11:02,225]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12767, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 12d0e354-2e42-4948-9e9b-e70a05284e07
TID: [-1234] [] [2024-08-11 18:11:02,227]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 18:14:36,459]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12770, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 52dd8930-5c6e-403a-8cb0-2a287c28fac7
TID: [-1234] [] [2024-08-11 18:14:36,461]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 18:14:43,439]  WARN {org.wso2.carbon.ui.transports.fileupload.AbstractFileUploadExecutor} - ToolsAnyFileUpload method is not supported
TID: [-1234] [] [2024-08-11 18:15:25,703]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-11 18:19:21,386]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = b73825b5-2561-474c-93ad-490bbcee706a
TID: [-1234] [] [2024-08-11 18:19:21,387]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12792, SOCKET_TIMEOUT = 180000, CORRELATION_ID = b73825b5-2561-474c-93ad-490bbcee706a
TID: [-1234] [] [2024-08-11 18:19:21,388]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 18:23:02,500]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 18:23:02,501]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:57260, CORRELATION_ID = 5d914f3f-cf2c-4f98-920a-baabda4e73df, CONNECTION = http-incoming-416461
TID: [-1234] [] [2024-08-11 18:23:02,676]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 5d914f3f-cf2c-4f98-920a-baabda4e73df
TID: [-1234] [] [2024-08-11 18:23:02,677]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12796, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 5d914f3f-cf2c-4f98-920a-baabda4e73df
TID: [-1234] [] [2024-08-11 18:23:02,678]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 18:23:02,697]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-416461, CORRELATION_ID = 5d914f3f-cf2c-4f98-920a-baabda4e73df
TID: [-1234] [] [2024-08-11 18:23:02,883]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12797, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 5a28eadc-d275-43da-8e9e-bb8c494a93b1
TID: [-1234] [] [2024-08-11 18:23:02,884]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 18:23:04,595]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12801, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 9380fa8f-bdc0-4045-97bd-3c883b935c34
TID: [-1234] [] [2024-08-11 18:23:04,596]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 18:23:04,612]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 18:23:04,613]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:46262, CORRELATION_ID = 9380fa8f-bdc0-4045-97bd-3c883b935c34, CONNECTION = http-incoming-416495
TID: [-1234] [] [2024-08-11 18:26:18,790]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12811, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 8747188c-9a7f-4f67-acdd-c139e4169e99
TID: [-1234] [] [2024-08-11 18:26:18,792]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 18:29:21,292]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 18:29:21,294]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:34786, CORRELATION_ID = f101d79d-7368-41d0-9a8d-f99cc348dae4, CONNECTION = http-incoming-416704
TID: [-1234] [] [2024-08-11 18:29:21,390]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12824, SOCKET_TIMEOUT = 180000, CORRELATION_ID = f101d79d-7368-41d0-9a8d-f99cc348dae4
TID: [-1234] [] [2024-08-11 18:29:21,391]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 18:29:21,409]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-416704, CORRELATION_ID = f101d79d-7368-41d0-9a8d-f99cc348dae4
TID: [-1234] [] [2024-08-11 18:32:21,941]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = ff28a396-b59c-473a-a736-013352a55478
TID: [-1234] [] [2024-08-11 18:32:21,943]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12838, SOCKET_TIMEOUT = 180000, CORRELATION_ID = ff28a396-b59c-473a-a736-013352a55478
TID: [-1234] [] [2024-08-11 18:32:21,943]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 18:32:23,300]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 18:32:23,301]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:59208, CORRELATION_ID = 19bf5399-f4a8-4ce9-8903-afff7f30b3b5, CONNECTION = http-incoming-416779
TID: [-1234] [] [2024-08-11 18:32:23,317]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 19bf5399-f4a8-4ce9-8903-afff7f30b3b5
TID: [-1234] [] [2024-08-11 18:32:23,318]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12850, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 19bf5399-f4a8-4ce9-8903-afff7f30b3b5
TID: [-1234] [] [2024-08-11 18:32:23,318]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 18:32:23,336]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-416779, CORRELATION_ID = 19bf5399-f4a8-4ce9-8903-afff7f30b3b5
TID: [-1234] [] [2024-08-11 18:37:00,620]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b61f0fe2-8beb-41e5-84ba-65a54cf78b31
TID: [-1234] [] [2024-08-11 18:37:00,623]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4cb8bf6c-4f45-46d4-b37e-26f3bee00a50
TID: [-1234] [] [2024-08-11 18:37:01,477]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 723d04d2-007c-4557-880f-40d2d2f1e135
TID: [-1234] [] [2024-08-11 18:59:45,961]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-11 19:13:36,195]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12889, SOCKET_TIMEOUT = 180000, CORRELATION_ID = a623bb76-d7e5-4ed2-9b1f-8dded802259d
TID: [-1234] [] [2024-08-11 19:13:36,197]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 19:17:13,618]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c153437c-d6fb-4948-a8e2-21661dc4812d
TID: [-1234] [] [2024-08-11 19:17:13,877]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12887, SOCKET_TIMEOUT = 180000, CORRELATION_ID = abd97b4b-f35e-40c0-9408-b18908c6e310
TID: [-1234] [] [2024-08-11 19:17:13,878]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 19:23:09,432]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12894, SOCKET_TIMEOUT = 180000, CORRELATION_ID = db4de66b-3a67-4c5b-a473-4879935a922d
TID: [-1234] [] [2024-08-11 19:23:09,433]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 19:26:53,818]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12908, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 211e0ded-58c4-4621-8ba6-13c57e144b65
TID: [-1234] [] [2024-08-11 19:26:53,820]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 19:26:54,288]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = f3c40ba5-afd0-4a35-aa00-4ade114b4248
TID: [-1234] [] [2024-08-11 19:26:54,289]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12901, SOCKET_TIMEOUT = 180000, CORRELATION_ID = f3c40ba5-afd0-4a35-aa00-4ade114b4248
TID: [-1234] [] [2024-08-11 19:26:54,289]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 19:26:58,525]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 19:26:58,526]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:35332, CORRELATION_ID = d463e3e5-6c7d-4b1c-8568-604db0b605b4, CONNECTION = http-incoming-419767
TID: [-1234] [] [2024-08-11 19:26:58,538]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12907, SOCKET_TIMEOUT = 180000, CORRELATION_ID = d463e3e5-6c7d-4b1c-8568-604db0b605b4
TID: [-1234] [] [2024-08-11 19:26:58,539]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 19:26:58,557]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-419767, CORRELATION_ID = d463e3e5-6c7d-4b1c-8568-604db0b605b4
TID: [-1234] [] [2024-08-11 19:30:02,055]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-11 19:30:21,285]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12917, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 53afdc60-e193-417b-9bc7-5881e654d2cb
TID: [-1234] [] [2024-08-11 19:30:21,287]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 19:33:28,750]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12930, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 0c6b925d-f37f-446f-8e29-9c18b13c8f11
TID: [-1234] [] [2024-08-11 19:33:28,751]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 19:33:28,758]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 19:33:28,758]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:39868, CORRELATION_ID = 0c6b925d-f37f-446f-8e29-9c18b13c8f11, CONNECTION = http-incoming-419955
TID: [-1234] [] [2024-08-11 19:33:28,769]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-419955, CORRELATION_ID = 0c6b925d-f37f-446f-8e29-9c18b13c8f11
TID: [-1234] [] [2024-08-11 19:36:33,568]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 7c22fee5-9c4a-4b0b-bfdc-cdbbd9c34e6a
TID: [-1234] [] [2024-08-11 19:36:33,569]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12931, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 7c22fee5-9c4a-4b0b-bfdc-cdbbd9c34e6a
TID: [-1234] [] [2024-08-11 19:36:33,569]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 19:36:37,591]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 19:36:37,592]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:44104, CORRELATION_ID = 81e77551-79fd-4932-b6d9-161ab8c1d528, CONNECTION = http-incoming-420059
TID: [-1234] [] [2024-08-11 19:36:37,840]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12943, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 81e77551-79fd-4932-b6d9-161ab8c1d528
TID: [-1234] [] [2024-08-11 19:36:37,841]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 19:36:37,856]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-420059, CORRELATION_ID = 81e77551-79fd-4932-b6d9-161ab8c1d528
TID: [-1234] [] [2024-08-11 19:41:52,618]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0e7c18aa-52e1-4991-870a-b3ac2d62bda2
TID: [-1234] [] [2024-08-11 19:47:25,505]  INFO {org.wso2.carbon.identity.application.authentication.framework.store.SessionCleanUpService} - Session Data cleanup task is running successfully for removing expired Data
TID: [-1234] [] [2024-08-11 20:00:02,428]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-11 20:00:18,868]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Common/UploadMultipleFiles, HEALTH CHECK URL = /Common/UploadMultipleFiles
TID: [-1234] [] [2024-08-11 20:11:39,977]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12963, SOCKET_TIMEOUT = 180000, CORRELATION_ID = d5de4357-4f97-43e2-8df9-99cd8ffa2cfa
TID: [-1234] [] [2024-08-11 20:11:39,979]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 20:15:10,540]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 20:15:10,540]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12975, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 6150e585-1e16-43c1-810b-07081cb8ff9e
TID: [-1234] [] [2024-08-11 20:15:10,540]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:39742, CORRELATION_ID = 6150e585-1e16-43c1-810b-07081cb8ff9e, CONNECTION = http-incoming-422378
TID: [-1234] [] [2024-08-11 20:15:10,542]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 20:15:10,560]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-422378, CORRELATION_ID = 6150e585-1e16-43c1-810b-07081cb8ff9e
TID: [-1234] [] [2024-08-11 20:15:10,592]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fe314f52-465c-4596-9dd0-5f5fb862d9c6
TID: [-1234] [] [2024-08-11 20:20:17,153]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 318acd7b-0230-47d7-a9bd-fb614c930e4b
TID: [-1234] [] [2024-08-11 20:20:17,154]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12989, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 318acd7b-0230-47d7-a9bd-fb614c930e4b
TID: [-1234] [] [2024-08-11 20:20:17,155]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 20:20:17,192]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 20:20:17,192]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:38080, CORRELATION_ID = 318acd7b-0230-47d7-a9bd-fb614c930e4b, CONNECTION = http-incoming-422785
TID: [-1234] [] [2024-08-11 20:24:04,634]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 20:24:04,635]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:57544, CORRELATION_ID = d1e6b593-9d5e-44ed-8029-dc3c62dc71b3, CONNECTION = http-incoming-422975
TID: [-1234] [] [2024-08-11 20:24:05,251]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = d1e6b593-9d5e-44ed-8029-dc3c62dc71b3
TID: [-1234] [] [2024-08-11 20:24:05,252]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12981, SOCKET_TIMEOUT = 180000, CORRELATION_ID = d1e6b593-9d5e-44ed-8029-dc3c62dc71b3
TID: [-1234] [] [2024-08-11 20:24:05,253]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 20:24:05,268]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-422975, CORRELATION_ID = d1e6b593-9d5e-44ed-8029-dc3c62dc71b3
TID: [-1234] [] [2024-08-11 20:24:05,400]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 20:24:05,401]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:57682, CORRELATION_ID = d1083147-fad5-429c-89a9-db73d99c2e87, CONNECTION = http-incoming-422992
TID: [-1234] [] [2024-08-11 20:24:05,579]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-13000, SOCKET_TIMEOUT = 180000, CORRELATION_ID = d1083147-fad5-429c-89a9-db73d99c2e87
TID: [-1234] [] [2024-08-11 20:24:05,580]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 20:24:05,598]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-422992, CORRELATION_ID = d1083147-fad5-429c-89a9-db73d99c2e87
TID: [-1234] [] [2024-08-11 20:24:07,101]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-12998, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 3f44c23b-fea7-4611-a393-1b6fb6f19d1f
TID: [-1234] [] [2024-08-11 20:24:07,102]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 20:24:07,113]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:57894, CORRELATION_ID = 3f44c23b-fea7-4611-a393-1b6fb6f19d1f, CONNECTION = http-incoming-423013
TID: [-1234] [] [2024-08-11 20:27:28,881]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = bb38aa87-b2dd-4a58-9254-b5157de4016c
TID: [-1234] [] [2024-08-11 20:27:28,883]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-13012, SOCKET_TIMEOUT = 180000, CORRELATION_ID = bb38aa87-b2dd-4a58-9254-b5157de4016c
TID: [-1234] [] [2024-08-11 20:27:28,884]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 20:27:28,885]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 20:27:28,886]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:33228, CORRELATION_ID = bb38aa87-b2dd-4a58-9254-b5157de4016c, CONNECTION = http-incoming-423134
TID: [-1234] [] [2024-08-11 20:27:28,900]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-423134, CORRELATION_ID = bb38aa87-b2dd-4a58-9254-b5157de4016c
TID: [-1234] [] [2024-08-11 20:30:04,238]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-11 20:30:33,734]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 20:30:33,735]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:58610, CORRELATION_ID = a9b4b119-9edd-4005-9154-10be47e2d7fa, CONNECTION = http-incoming-423210
TID: [-1234] [] [2024-08-11 20:30:33,779]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = a9b4b119-9edd-4005-9154-10be47e2d7fa
TID: [-1234] [] [2024-08-11 20:30:33,780]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-13031, SOCKET_TIMEOUT = 180000, CORRELATION_ID = a9b4b119-9edd-4005-9154-10be47e2d7fa
TID: [-1234] [] [2024-08-11 20:30:33,781]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 20:30:33,798]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-423210, CORRELATION_ID = a9b4b119-9edd-4005-9154-10be47e2d7fa
TID: [-1234] [] [2024-08-11 20:33:35,154]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-13028, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 88bc7f8d-e15e-479f-b7aa-5e06f3a5cef7
TID: [-1234] [] [2024-08-11 20:33:35,155]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 20:33:38,159]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 9127f807-6362-491e-bb5c-b5fe7f129e02
TID: [-1234] [] [2024-08-11 20:33:38,160]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-13048, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 9127f807-6362-491e-bb5c-b5fe7f129e02
TID: [-1234] [] [2024-08-11 20:33:38,160]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 20:34:00,423]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Common/UploadMultipleTempFiles, HEALTH CHECK URL = /Common/UploadMultipleTempFiles
TID: [-1234] [] [2024-08-11 20:38:18,364]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d5a5df5b-a4f7-478e-811e-f03e0a753897
TID: [-1234] [] [2024-08-11 20:38:18,365]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 588a9e74-032f-480b-9c85-82ad87fe7660
TID: [-1234] [] [2024-08-11 20:38:18,402]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 935a9a86-f372-4e87-aa03-7dd6d05e101a
TID: [-1234] [] [2024-08-11 21:00:07,194]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-11 21:12:12,938]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-13086, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 48d5ac3e-9d86-4c3e-a5c8-93ea34e9c516
TID: [-1234] [] [2024-08-11 21:12:12,940]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 21:12:12,941]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 21:12:12,941]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:42888, CORRELATION_ID = 48d5ac3e-9d86-4c3e-a5c8-93ea34e9c516, CONNECTION = http-incoming-425380
TID: [-1234] [] [2024-08-11 21:12:12,957]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-425380, CORRELATION_ID = 48d5ac3e-9d86-4c3e-a5c8-93ea34e9c516
TID: [-1234] [] [2024-08-11 21:15:54,674]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-13087, SOCKET_TIMEOUT = 180000, CORRELATION_ID = de53a339-e525-4b14-b5d6-b3c890b129af
TID: [-1234] [] [2024-08-11 21:15:54,675]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 21:15:56,286]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cee69825-390b-43ec-84f6-75004cdb6bc9
TID: [-1234] [] [2024-08-11 21:15:56,744]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f76dce6a-372a-4b15-958a-de61477ebfce
TID: [-1234] [] [2024-08-11 21:15:57,344]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0197d2fc-51cc-4c12-8025-d4409e4c4e36
TID: [-1234] [] [2024-08-11 21:15:57,530]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c2436769-fc04-4d04-bfb2-016f30ed93c2
TID: [-1234] [] [2024-08-11 21:15:57,925]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b45dc5bd-9a7a-48e7-aafd-d50c7cd6efbb
TID: [-1234] [] [2024-08-11 21:20:53,679]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-13099, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 17b60802-8ad0-459b-bcb7-4e56b38bada7
TID: [-1234] [] [2024-08-11 21:20:53,680]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 21:24:41,971]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-13115, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 331fed3e-e806-478f-b2f2-dc75f1d7cdab
TID: [-1234] [] [2024-08-11 21:24:41,972]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 21:24:42,717]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 3b00cd1d-9d6b-4351-8f15-d485ccdb1903
TID: [-1234] [] [2024-08-11 21:24:42,718]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-13114, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 3b00cd1d-9d6b-4351-8f15-d485ccdb1903
TID: [-1234] [] [2024-08-11 21:24:42,719]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 21:24:44,044]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 21:24:44,045]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:40560, CORRELATION_ID = 8dc1dc5f-3883-43d8-ad1f-1e6723b01665, CONNECTION = http-incoming-426271
TID: [-1234] [] [2024-08-11 21:24:44,086]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-13117, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 8dc1dc5f-3883-43d8-ad1f-1e6723b01665
TID: [-1234] [] [2024-08-11 21:24:44,087]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 21:24:44,107]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-426271, CORRELATION_ID = 8dc1dc5f-3883-43d8-ad1f-1e6723b01665
TID: [-1234] [] [2024-08-11 21:27:53,109]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-13132, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 09626f03-0044-4a68-a00e-c42efe0084b0
TID: [-1234] [] [2024-08-11 21:27:53,111]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 21:30:28,773]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-11 21:30:55,869]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 47f2977a-0057-4eb4-866d-33698e931b57
TID: [-1234] [] [2024-08-11 21:30:55,870]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-13140, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 47f2977a-0057-4eb4-866d-33698e931b57
TID: [-1234] [] [2024-08-11 21:30:55,872]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 21:33:56,519]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 777f54d9-363f-4d1f-9bb3-0edb01bf770f
TID: [-1234] [] [2024-08-11 21:33:56,520]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-13153, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 777f54d9-363f-4d1f-9bb3-0edb01bf770f
TID: [-1234] [] [2024-08-11 21:33:56,521]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 21:33:57,475]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-13152, SOCKET_TIMEOUT = 180000, CORRELATION_ID = df77981c-bfec-49e0-813e-6383792b1743
TID: [-1234] [] [2024-08-11 21:33:57,476]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 21:38:33,651]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0780fd1c-12bc-4898-a34b-50fd63a6eaaa
TID: [-1234] [] [2024-08-11 21:38:33,652]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 242fbdb9-56d7-4a8d-98f7-609e4037e2ba
TID: [-1234] [] [2024-08-11 21:38:33,677]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d7a7c0b5-db46-417e-8e2f-e3c795feb75c
TID: [-1234] [] [2024-08-11 21:38:33,677]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 00bc7833-8901-4126-9e77-a7dc33b04a5c
TID: [-1234] [] [2024-08-11 21:39:36,871]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e5e57bac-1282-45a7-a043-3cda9d1c8204
TID: [-1234] [] [2024-08-11 22:00:28,955]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-11 22:08:58,964]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20240811&denNgay=20240811&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20240811&denNgay=20240811&maTthc=
TID: [-1234] [] [2024-08-11 22:08:59,130]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-08-11 22:09:37,673]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20240811&denNgay=20240811&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20240811&denNgay=20240811&maTthc=
TID: [-1234] [] [2024-08-11 22:09:37,714]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-08-11 22:09:48,391]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20240811&denNgay=20240811&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20240811&denNgay=20240811&maTthc=
TID: [-1234] [] [2024-08-11 22:09:48,554]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-08-11 22:10:49,312]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 20cbab28-15a5-46c1-ba4c-98dd7bbd6e2c
TID: [-1234] [] [2024-08-11 22:10:49,313]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-13192, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 20cbab28-15a5-46c1-ba4c-98dd7bbd6e2c
TID: [-1234] [] [2024-08-11 22:10:49,314]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 22:14:15,640]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 22:14:15,641]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:57174, CORRELATION_ID = f60a7299-885e-4f74-a3cf-d7e49aadd5ce, CONNECTION = http-incoming-428904
TID: [-1234] [] [2024-08-11 22:14:15,644]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-13184, SOCKET_TIMEOUT = 180000, CORRELATION_ID = f60a7299-885e-4f74-a3cf-d7e49aadd5ce
TID: [-1234] [] [2024-08-11 22:14:15,645]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 22:14:15,662]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-428904, CORRELATION_ID = f60a7299-885e-4f74-a3cf-d7e49aadd5ce
TID: [-1234] [] [2024-08-11 22:19:12,753]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-13200, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 376acd63-8548-4827-bba2-7a544c6fc708
TID: [-1234] [] [2024-08-11 22:19:12,755]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 22:21:11,634]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20240811&denNgay=20240811&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20240811&denNgay=20240811&maTthc=
TID: [-1234] [] [2024-08-11 22:21:11,673]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-08-11 22:22:56,934]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 592429d1-2a0f-4189-82a2-e10fc1405b17
TID: [-1234] [] [2024-08-11 22:22:56,935]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-13217, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 592429d1-2a0f-4189-82a2-e10fc1405b17
TID: [-1234] [] [2024-08-11 22:22:56,936]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 22:22:57,498]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 22:22:57,499]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:39584, CORRELATION_ID = bba1c8a0-341a-4e9c-8f5d-9e9d31ee0645, CONNECTION = http-incoming-429518
TID: [-1234] [] [2024-08-11 22:22:57,515]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = bba1c8a0-341a-4e9c-8f5d-9e9d31ee0645
TID: [-1234] [] [2024-08-11 22:22:57,516]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-13215, SOCKET_TIMEOUT = 180000, CORRELATION_ID = bba1c8a0-341a-4e9c-8f5d-9e9d31ee0645
TID: [-1234] [] [2024-08-11 22:22:57,517]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 22:22:57,533]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-429518, CORRELATION_ID = bba1c8a0-341a-4e9c-8f5d-9e9d31ee0645
TID: [-1234] [] [2024-08-11 22:22:59,528]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 22:22:59,528]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:39848, CORRELATION_ID = ecc8b65a-f87d-4899-8977-524166efc5be, CONNECTION = http-incoming-429543
TID: [-1234] [] [2024-08-11 22:22:59,535]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-13221, SOCKET_TIMEOUT = 180000, CORRELATION_ID = ecc8b65a-f87d-4899-8977-524166efc5be
TID: [-1234] [] [2024-08-11 22:22:59,536]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 22:22:59,555]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-429543, CORRELATION_ID = ecc8b65a-f87d-4899-8977-524166efc5be
TID: [-1234] [] [2024-08-11 22:26:08,967]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-13241, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 86c06d19-d537-4f63-8fa5-cdf45fe9d4a4
TID: [-1234] [] [2024-08-11 22:26:08,969]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 22:29:14,371]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 3b489da8-f036-4992-a20d-e03b53368497
TID: [-1234] [] [2024-08-11 22:29:14,372]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-13247, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 3b489da8-f036-4992-a20d-e03b53368497
TID: [-1234] [] [2024-08-11 22:29:14,373]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 22:29:14,390]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 22:29:14,391]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:47792, CORRELATION_ID = 3b489da8-f036-4992-a20d-e03b53368497, CONNECTION = http-incoming-429792
TID: [-1234] [] [2024-08-11 22:32:20,200]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-13272, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 57eb2ca8-ff6f-4b7e-a1c4-dae45dc95286
TID: [-1234] [] [2024-08-11 22:32:20,202]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 22:32:20,270]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-13268, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 77d8716c-fdab-4d54-901c-4dced27c7304
TID: [-1234] [] [2024-08-11 22:32:20,271]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 22:32:20,811]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-11 22:35:35,218]  WARN {org.wso2.carbon.ui.transports.fileupload.AbstractFileUploadExecutor} - ToolsAnyFileUpload method is not supported
TID: [-1234] [] [2024-08-11 22:35:36,510]  WARN {org.wso2.carbon.ui.transports.fileupload.AbstractFileUploadExecutor} - ToolsAnyFileUpload method is not supported
TID: [-1234] [] [2024-08-11 22:35:37,861]  WARN {org.wso2.carbon.ui.transports.fileupload.AbstractFileUploadExecutor} - ToolsAnyFileUpload method is not supported
TID: [-1234] [] [2024-08-11 22:37:02,511]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fa3cef73-b21d-4452-b59b-a60ed7229000
TID: [-1234] [] [2024-08-11 22:37:02,513]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7d569a75-7159-49df-947c-137dffd9f4a5
TID: [-1234] [] [2024-08-11 22:37:02,516]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0915502f-1d28-404a-bed8-b4edc3491983
TID: [-1234] [] [2024-08-11 22:37:02,528]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c5bc43a7-6324-472e-818f-027b9a0a34f5
TID: [-1234] [] [2024-08-11 22:37:02,536]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 15d0a385-beab-4a5f-8928-498bc72d45ab
TID: [-1234] [] [2024-08-11 22:46:29,929]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20240811&denNgay=20240811&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20240811&denNgay=20240811&maTthc=
TID: [-1234] [] [2024-08-11 22:46:29,973]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-08-11 22:46:39,602]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20240811&denNgay=20240811&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20240811&denNgay=20240811&maTthc=
TID: [-1234] [] [2024-08-11 22:46:39,650]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2024-08-11 23:02:21,404]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-11 23:11:25,155]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 23:11:25,156]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:49540, CORRELATION_ID = d73aa463-64c0-490e-a5de-927a1d962b19, CONNECTION = http-incoming-431899
TID: [-1234] [] [2024-08-11 23:11:25,216]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = d73aa463-64c0-490e-a5de-927a1d962b19
TID: [-1234] [] [2024-08-11 23:11:25,217]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-13308, SOCKET_TIMEOUT = 180000, CORRELATION_ID = d73aa463-64c0-490e-a5de-927a1d962b19
TID: [-1234] [] [2024-08-11 23:11:25,218]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 23:11:25,235]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-431899, CORRELATION_ID = d73aa463-64c0-490e-a5de-927a1d962b19
TID: [-1234] [] [2024-08-11 23:13:16,406]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /js/rnsite.js, HEALTH CHECK URL = /js/rnsite.js
TID: [-1234] [] [2024-08-11 23:14:46,331]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 23:14:46,332]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:34380, CORRELATION_ID = 46092da5-da73-427c-b2ff-5d86873ff380, CONNECTION = http-incoming-432128
TID: [-1234] [] [2024-08-11 23:14:46,440]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 46092da5-da73-427c-b2ff-5d86873ff380
TID: [-1234] [] [2024-08-11 23:14:46,441]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-13303, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 46092da5-da73-427c-b2ff-5d86873ff380
TID: [-1234] [] [2024-08-11 23:14:46,442]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 23:14:46,458]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-432128, CORRELATION_ID = 46092da5-da73-427c-b2ff-5d86873ff380
TID: [-1234] [] [2024-08-11 23:16:35,277]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20240811&denNgay=20240811&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20240811&denNgay=20240811&maTthc=
TID: [-1234] [] [2024-08-11 23:16:35,317]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-08-11 23:20:10,042]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 21b90d40-8273-4e55-ac5d-8768ec4eed09
TID: [-1234] [] [2024-08-11 23:20:10,044]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-13314, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 21b90d40-8273-4e55-ac5d-8768ec4eed09
TID: [-1234] [] [2024-08-11 23:20:10,045]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 23:23:55,884]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-13336, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 02eeb0f4-6bae-43dc-9b6b-5d73064814dc
TID: [-1234] [] [2024-08-11 23:23:55,886]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 23:23:56,228]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-13328, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 50b78c4e-6746-41e5-a62e-a6394ab89466
TID: [-1234] [] [2024-08-11 23:23:56,230]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 23:23:58,019]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 7bc2358d-7955-4216-8325-c75d8fbc6fc6
TID: [-1234] [] [2024-08-11 23:23:58,020]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-13324, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 7bc2358d-7955-4216-8325-c75d8fbc6fc6
TID: [-1234] [] [2024-08-11 23:23:58,021]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 23:23:58,051]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 23:23:58,052]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:44716, CORRELATION_ID = 7bc2358d-7955-4216-8325-c75d8fbc6fc6, CONNECTION = http-incoming-432779
TID: [-1234] [] [2024-08-11 23:23:58,064]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-432779, CORRELATION_ID = 7bc2358d-7955-4216-8325-c75d8fbc6fc6
TID: [-1234] [] [2024-08-11 23:27:14,661]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-13341, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 8b5b0ed7-38e6-4dd2-a20c-3c6891358cf4
TID: [-1234] [] [2024-08-11 23:27:14,662]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 23:27:14,696]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 23:27:14,697]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:51606, CORRELATION_ID = 8b5b0ed7-38e6-4dd2-a20c-3c6891358cf4, CONNECTION = http-incoming-432880
TID: [-1234] [] [2024-08-11 23:27:14,705]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-432880, CORRELATION_ID = 8b5b0ed7-38e6-4dd2-a20c-3c6891358cf4
TID: [-1234] [] [2024-08-11 23:30:19,618]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-13354, SOCKET_TIMEOUT = 180000, CORRELATION_ID = d33f632a-58a8-4fc8-9cd7-42935ec39ee5
TID: [-1234] [] [2024-08-11 23:30:19,619]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 23:33:22,410]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-11 23:33:22,411]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:54034, CORRELATION_ID = 793a931f-83c3-4184-8b3c-366d95246d30, CONNECTION = http-incoming-433045
TID: [-1234] [] [2024-08-11 23:33:22,445]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-13362, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 793a931f-83c3-4184-8b3c-366d95246d30
TID: [-1234] [] [2024-08-11 23:33:22,446]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 23:33:22,463]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-433045, CORRELATION_ID = 793a931f-83c3-4184-8b3c-366d95246d30
TID: [-1234] [] [2024-08-11 23:33:29,932]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-13358, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 177733fa-56b3-4f3b-bdb7-e137b8b541be
TID: [-1234] [] [2024-08-11 23:33:29,934]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-11 23:33:31,311]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-11 23:38:25,040]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d42a648d-c5ee-4fc3-ba6d-0511ba915b94
TID: [-1234] [] [2024-08-11 23:38:25,065]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5bf0177c-ccbb-436d-9ae8-c12b6b8b7001
TID: [-1234] [] [2024-08-11 23:38:25,124]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e02af5e3-4049-47c7-aed3-4fdf22bb2081
TID: [-1234] [] [2024-08-11 23:38:25,161]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fbddcd8d-9c20-4479-9a74-a9b80882d8b3
TID: [-1234] [] [2024-08-11 23:44:27,829]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20240811&denNgay=20240811&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20240811&denNgay=20240811&maTthc=
TID: [-1234] [] [2024-08-11 23:44:27,928]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-08-11 23:50:05,718]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /js/admin/rnet.js, HEALTH CHECK URL = /js/admin/rnet.js
TID: [-1234] [] [2024-08-11 23:59:56,212]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20240812&denNgay=20240812&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20240812&denNgay=20240812&maTthc=
TID: [-1234] [] [2024-08-11 23:59:56,250]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
