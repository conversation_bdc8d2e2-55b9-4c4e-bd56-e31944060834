TID: [-1234] [] [2024-12-30 00:00:18,679]  WARN {org.apache.synapse.commons.util.MiscellaneousUtil} - Error loading properties from a file at from the System defined location: access-log.properties
TID: [-1234] [] [2024-12-30 00:01:26,995]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-30 00:05:11,699]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4b54cb7d-9a09-49f3-9927-dba73638531c
TID: [-1234] [] [2024-12-30 00:05:14,238]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 200070d9-3f5c-4576-a9a7-00e73077382e
TID: [-1234] [] [2024-12-30 00:31:27,233]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-30 01:01:27,472]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-30 01:26:00,564]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241230&denNgay=20241230&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241230&denNgay=20241230&maTthc=
TID: [-1234] [] [2024-12-30 01:26:00,605]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-12-30 01:31:28,193]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-30 02:01:28,564]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-30 02:05:32,640]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = db1f17e9-7bc6-4f55-860c-77328f91f9f6
TID: [-1234] [] [2024-12-30 02:05:35,241]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 31ceabf7-c603-4bee-9c0d-40e0a6cf67a8
TID: [-1234] [] [2024-12-30 02:06:36,873]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5fd36536-2b60-49e3-85c7-c7e0f84cd2a6
TID: [-1234] [] [2024-12-30 02:31:28,708]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-30 02:36:37,807]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [AuthenticationAdmin] [2024-12-30 02:36:52,510]  INFO {org.wso2.carbon.core.services.util.CarbonAuthenticationUtil} - '<EMAIL> [-1234]' logged in at [2024-12-30 02:36:52,510+0700]
TID: [-1234] [AuthenticationAdmin] [2024-12-30 02:36:53,014]  INFO {org.wso2.carbon.core.services.util.CarbonAuthenticationUtil} - '<EMAIL> [-1234]' logged in at [2024-12-30 02:36:53,014+0700]
TID: [-1234] [AuthenticationAdmin] [2024-12-30 02:39:17,431]  INFO {org.wso2.carbon.core.services.util.CarbonAuthenticationUtil} - '<EMAIL> [-1234]' logged in at [2024-12-30 02:39:17,431+0700]
TID: [-1234] [AuthenticationAdmin] [2024-12-30 02:39:17,942]  INFO {org.wso2.carbon.core.services.util.CarbonAuthenticationUtil} - '<EMAIL> [-1234]' logged in at [2024-12-30 02:39:17,942+0700]
TID: [-1234] [AuthenticationAdmin] [2024-12-30 02:39:27,217]  INFO {org.wso2.carbon.core.services.util.CarbonAuthenticationUtil} - '<EMAIL> [-1234]' logged in at [2024-12-30 02:39:27,217+0700]
TID: [-1234] [APIGatewayAdmin] [2024-12-30 02:39:27,248]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Local entry : fcffe066-4401-4a4f-b52c-0e227a2f782e was added to the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-30 02:39:27,257]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : GPLX2--v1.0_APIsandboxEndpoint was added to the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-30 02:39:27,265]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : GPLX2--v1.0_APIproductionEndpoint was added to the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-30 02:39:27,270]  INFO {org.apache.synapse.rest.API} - {api:admin--GPLX2:v1.0} Initializing API: admin--GPLX2:v1.0
TID: [-1234] [APIGatewayAdmin] [2024-12-30 02:39:27,271]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : admin--GPLX2:v1.0 was added to the Synapse configuration successfully
TID: [-1234] [AuthenticationAdmin] [2024-12-30 02:39:27,353]  INFO {org.wso2.carbon.core.services.util.CarbonAuthenticationUtil} - '<EMAIL> [-1234]' logged in at [2024-12-30 02:39:27,353+0700]
TID: [-1234] [] [2024-12-30 02:52:49,431]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a17b7b95-ccd5-4207-ab17-93a6a8030e2a
TID: [-1234] [] [2024-12-30 02:58:22,076]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /1.0/giaypheplaixe/login, HEALTH CHECK URL = /1.0/giaypheplaixe/login
TID: [-1234] [] [2024-12-30 02:58:31,859]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /giaypheplaixe1/login, HEALTH CHECK URL = /giaypheplaixe1/login
TID: [-1234] [] [2024-12-30 03:01:29,228]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [AuthenticationAdmin] [2024-12-30 03:04:53,408]  INFO {org.wso2.carbon.core.services.util.CarbonAuthenticationUtil} - '<EMAIL> [-1234]' logged in at [2024-12-30 03:04:53,408+0700]
TID: [-1234] [AuthenticationAdmin] [2024-12-30 03:04:53,782]  INFO {org.wso2.carbon.core.services.util.CarbonAuthenticationUtil} - '<EMAIL> [-1234]' logged in at [2024-12-30 03:04:53,782+0700]
TID: [-1234] [AuthenticationAdmin] [2024-12-30 03:04:53,942]  INFO {org.wso2.carbon.core.services.util.CarbonAuthenticationUtil} - '<EMAIL> [-1234]' logged in at [2024-12-30 03:04:53,942+0700]
TID: [-1234] [APIGatewayAdmin] [2024-12-30 03:04:53,952]  INFO {org.apache.synapse.rest.API} - {api:admin--GPLX2:v1.0} Destroying API: admin--GPLX2:v1.0
TID: [-1234] [APIGatewayAdmin] [2024-12-30 03:04:53,988]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : admin--GPLX2:v1.0 was removed from the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-30 03:04:53,990]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Local entry : fcffe066-4401-4a4f-b52c-0e227a2f782e was removed from the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-30 03:04:53,992]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Local entry : fcffe066-4401-4a4f-b52c-0e227a2f782e was added to the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-30 03:04:54,001]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : GPLX2--v1.0_APIproductionEndpoint was added to the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-30 03:04:54,008]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : GPLX2--v1.0_APIsandboxEndpoint was added to the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-30 03:04:54,014]  INFO {org.apache.synapse.rest.API} - {api:admin--GPLX2:v1.0} Initializing API: admin--GPLX2:v1.0
TID: [-1234] [APIGatewayAdmin] [2024-12-30 03:04:54,016]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : admin--GPLX2:v1.0 was added to the Synapse configuration successfully
TID: [-1234] [AuthenticationAdmin] [2024-12-30 03:04:54,061]  INFO {org.wso2.carbon.core.services.util.CarbonAuthenticationUtil} - '<EMAIL> [-1234]' logged in at [2024-12-30 03:04:54,061+0700]
TID: [-1234] [] [2024-12-30 03:05:36,782]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 22451141-229f-4660-95e9-a91410b026d8
TID: [-1234] [] [2024-12-30 03:05:38,041]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b9f05282-791d-4f62-bc93-617aaed017b7
TID: [-1234] [] [2024-12-30 03:05:39,349]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 195c6985-bed9-448f-939b-ba87dbbd12c8
TID: [-1234] [AuthenticationAdmin] [2024-12-30 03:10:12,201]  INFO {org.wso2.carbon.core.services.util.CarbonAuthenticationUtil} - '<EMAIL> [-1234]' logged in at [2024-12-30 03:10:12,201+0700]
TID: [-1234] [APIGatewayAdmin] [2024-12-30 03:10:12,247]  INFO {org.apache.synapse.rest.API} - {api:admin--GPLX2:v1.0} Destroying API: admin--GPLX2:v1.0
TID: [-1234] [APIGatewayAdmin] [2024-12-30 03:10:12,288]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : admin--GPLX2:v1.0 was removed from the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-30 03:10:12,289]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Local entry : fcffe066-4401-4a4f-b52c-0e227a2f782e was removed from the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-30 03:10:12,291]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Local entry : fcffe066-4401-4a4f-b52c-0e227a2f782e was added to the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-30 03:10:12,298]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : GPLX2--v1.0_APIproductionEndpoint was added to the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-30 03:10:12,304]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : GPLX2--v1.0_APIsandboxEndpoint was added to the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-30 03:10:12,309]  INFO {org.apache.synapse.rest.API} - {api:admin--GPLX2:v1.0} Initializing API: admin--GPLX2:v1.0
TID: [-1234] [APIGatewayAdmin] [2024-12-30 03:10:12,309]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : admin--GPLX2:v1.0 was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:10:12,373]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [AuthenticationAdmin] [2024-12-30 03:10:12,931]  INFO {org.wso2.carbon.core.services.util.CarbonAuthenticationUtil} - '<EMAIL> [-1234]' logged in at [2024-12-30 03:10:12,931+0700]
TID: [-1234] [AuthenticationAdmin] [2024-12-30 03:10:13,350]  INFO {org.wso2.carbon.core.services.util.CarbonAuthenticationUtil} - '<EMAIL> [-1234]' logged in at [2024-12-30 03:10:13,350+0700]
TID: [-1234] [AuthenticationAdmin] [2024-12-30 03:10:13,454]  INFO {org.wso2.carbon.core.services.util.CarbonAuthenticationUtil} - '<EMAIL> [-1234]' logged in at [2024-12-30 03:10:13,454+0700]
TID: [-1234] [APIGatewayAdmin] [2024-12-30 03:10:13,465]  INFO {org.apache.synapse.rest.API} - {api:admin--GPLX2:v1.0} Destroying API: admin--GPLX2:v1.0
TID: [-1234] [APIGatewayAdmin] [2024-12-30 03:10:13,495]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : admin--GPLX2:v1.0 was removed from the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-30 03:10:13,496]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Local entry : fcffe066-4401-4a4f-b52c-0e227a2f782e was removed from the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-30 03:10:13,498]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Local entry : fcffe066-4401-4a4f-b52c-0e227a2f782e was added to the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-30 03:10:13,505]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : GPLX2--v1.0_APIproductionEndpoint was added to the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-30 03:10:13,511]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : GPLX2--v1.0_APIsandboxEndpoint was added to the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-30 03:10:13,515]  INFO {org.apache.synapse.rest.API} - {api:admin--GPLX2:v1.0} Initializing API: admin--GPLX2:v1.0
TID: [-1234] [APIGatewayAdmin] [2024-12-30 03:10:13,515]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : admin--GPLX2:v1.0 was added to the Synapse configuration successfully
TID: [-1234] [AuthenticationAdmin] [2024-12-30 03:10:13,554]  INFO {org.wso2.carbon.core.services.util.CarbonAuthenticationUtil} - '<EMAIL> [-1234]' logged in at [2024-12-30 03:10:13,553+0700]
TID: [-1234] [] [2024-12-30 03:13:25,323]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = de722526-2b4a-47e8-bb1e-9bb4807e095f
TID: [-1234] [] [2024-12-30 03:21:39,856]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ad136454-2362-4292-86d8-d2be38b0796f
TID: [-1234] [] [2024-12-30 03:28:50,081]  INFO {org.wso2.carbon.core.init.CarbonServerManager} - Shutdown hook triggered....
TID: [-1234] [] [2024-12-30 03:28:50,113]  INFO {org.wso2.carbon.core.init.CarbonServerManager} - Gracefully shutting down WSO2 API Manager...
TID: [-1234] [] [2024-12-30 03:28:50,120]  INFO {org.wso2.carbon.core.ServerManagement} - Starting to switch to maintenance mode...
TID: [-1234] [] [2024-12-30 03:28:50,121]  INFO {org.apache.synapse.transport.passthru.PassThroughHttpListener} - Stopping Pass-through HTTP Listener..
TID: [-1234] [] [2024-12-30 03:28:50,122]  INFO {org.apache.synapse.transport.passthru.core.PassThroughListeningIOReactorManager} - Pausing  IO Reactor bind for port 8280
TID: [-1234] [] [2024-12-30 03:28:50,126]  INFO {org.apache.synapse.transport.passthru.core.PassThroughListeningIOReactorManager} - Shutting down IO Reactor bind for port 8280
TID: [-1234] [] [2024-12-30 03:28:50,156]  INFO {org.apache.synapse.transport.passthru.core.PassThroughListeningIOReactorManager} - HTTP-Listener I/O dispatcher Listener shutdown.
TID: [-1234] [] [2024-12-30 03:28:50,856]  INFO {org.apache.synapse.transport.passthru.PassThroughHttpMultiSSLListener} - Stopping Pass-through HTTPS Listener..
TID: [-1234] [] [2024-12-30 03:28:50,857]  INFO {org.apache.synapse.transport.passthru.core.PassThroughListeningIOReactorManager} - Pausing  IO Reactor bind for port 8243
TID: [-1234] [] [2024-12-30 03:28:50,865]  INFO {org.apache.synapse.transport.passthru.core.PassThroughListeningIOReactorManager} - Waiting to cleanup active connections on port 8243: 7
TID: [-1234] [] [2024-12-30 03:30:28,659]  INFO {org.wso2.carbon.core.internal.CarbonCoreActivator} - Starting WSO2 Carbon...
TID: [-1234] [] [2024-12-30 03:30:28,663]  INFO {org.wso2.carbon.core.internal.CarbonCoreActivator} - Operating System : Linux 4.18.0-425.3.1.el8.x86_64, amd64
TID: [-1234] [] [2024-12-30 03:30:28,663]  INFO {org.wso2.carbon.core.internal.CarbonCoreActivator} - Java Home        : /usr/java/jdk1.8.0_301-amd64/jre
TID: [-1234] [] [2024-12-30 03:30:28,663]  INFO {org.wso2.carbon.core.internal.CarbonCoreActivator} - Java Version     : 1.8.0_301
TID: [-1234] [] [2024-12-30 03:30:28,663]  INFO {org.wso2.carbon.core.internal.CarbonCoreActivator} - Java VM          : Java HotSpot(TM) 64-Bit Server VM 25.301-b09,Oracle Corporation
TID: [-1234] [] [2024-12-30 03:30:28,664]  INFO {org.wso2.carbon.core.internal.CarbonCoreActivator} - Carbon Home      : /home/<USER>/wso2am-3.2.0
TID: [-1234] [] [2024-12-30 03:30:28,664]  INFO {org.wso2.carbon.core.internal.CarbonCoreActivator} - Java Temp Dir    : /home/<USER>/wso2am-3.2.0/tmp
TID: [-1234] [] [2024-12-30 03:30:28,664]  INFO {org.wso2.carbon.core.internal.CarbonCoreActivator} - User             : root, en-US, Asia/Ho_Chi_Minh
TID: [-1234] [] [2024-12-30 03:30:28,804]  INFO {org.wso2.carbon.crypto.provider.internal.DefaultCryptoProviderComponent} - 'CryptoService.Secret' property has not been set. 'org.wso2.carbon.crypto.provider.SymmetricKeyInternalCryptoProvider' won't be registered as an internal crypto provider. Please set the secret if the provider needs to be registered.
TID: [-1234] [] [2024-12-30 03:30:29,130]  INFO {org.wso2.carbon.event.output.adapter.kafka.internal.ds.KafkaEventAdapterServiceDS} - Successfully deployed the Kafka output event adaptor service
TID: [-1234] [] [2024-12-30 03:30:29,277]  INFO {org.wso2.carbon.event.template.manager.core.internal.ds.TemplateDeployerServiceTrackerDS} - Successfully deployed the execution manager tracker service
TID: [-1234] [] [2024-12-30 03:30:31,197]  WARN {org.apache.tomcat.util.digester.Digester} - Match [Server/Service/Engine/Host/Valve] failed to set property [maxDays] to []
TID: [-1234] [] [2024-12-30 03:30:32,377]  INFO {org.wso2.carbon.consent.mgt.core.internal.ConsentManagerComponent} - ConsentManagerComponent is activated.
TID: [-1234] [] [2024-12-30 03:30:32,497]  INFO {org.wso2.carbon.databridge.receiver.binary.internal.BinaryDataReceiverServiceComponent} - Binary Data Receiver server activated
TID: [-1234] [] [2024-12-30 03:30:32,512]  INFO {org.wso2.carbon.databridge.core.internal.DataBridgeDS} - Successfully deployed Agent Server 
TID: [-1234] [] [2024-12-30 03:30:33,279]  INFO {org.wso2.carbon.registry.core.jdbc.EmbeddedRegistryService} - Configured Registry in 99ms
TID: [-1234] [] [2024-12-30 03:30:33,334]  INFO {org.wso2.carbon.registry.core.jdbc.EmbeddedRegistryService} - Connected to mount at configregistry in 4ms
TID: [-1234] [] [2024-12-30 03:30:33,335]  INFO {org.wso2.carbon.registry.core.jdbc.EmbeddedRegistryService} - Connected to mount at govregistry in 5ms
TID: [-1234] [] [2024-12-30 03:30:33,440]  INFO {org.wso2.carbon.registry.core.jdbc.EmbeddedRegistryService} - Connected to mount at configregistry in 1ms
TID: [-1234] [] [2024-12-30 03:30:33,441]  INFO {org.wso2.carbon.registry.core.jdbc.EmbeddedRegistryService} - Connected to mount at govregistry in 2ms
TID: [-1234] [] [2024-12-30 03:30:33,454]  INFO {org.wso2.carbon.registry.core.internal.RegistryCoreServiceComponent} - Registry Mode    : READ-WRITE
TID: [-1234] [] [2024-12-30 03:30:33,698]  INFO {org.wso2.carbon.metrics.impl.util.JmxReporterBuilder} - Creating JMX reporter for Metrics with domain 'org.wso2.carbon.metrics'
TID: [-1234] [] [2024-12-30 03:30:33,701]  INFO {org.wso2.carbon.metrics.impl.reporter.AbstractReporter} - Started JMX reporter for Metrics
TID: [-1234] [] [2024-12-30 03:30:35,213]  INFO {org.wso2.carbon.registry.indexing.solr.SolrClient} - Default Embedded Solr Server Initialized
TID: [-1234] [] [2024-12-30 03:30:35,658]  INFO {org.wso2.carbon.user.core.internal.UserStoreMgtDSComponent} - Carbon UserStoreMgtDSComponent activated successfully.
TID: [-1234] [] [2024-12-30 03:30:37,787]  INFO {org.wso2.carbon.identity.user.store.configuration.deployer.UserStoreConfigurationDeployer} - User Store Configuration Deployer initiated.
TID: [-1234] [] [2024-12-30 03:30:37,788]  INFO {org.wso2.carbon.identity.user.store.configuration.deployer.UserStoreConfigurationDeployer} - User Store Configuration Deployer initiated.
TID: [-1234] [] [2024-12-30 03:30:38,301]  INFO {org.wso2.carbon.websocket.transport.WebsocketTransportSender} - WSS Sender started
TID: [-1234] [] [2024-12-30 03:30:38,305]  INFO {org.apache.synapse.transport.passthru.PassThroughHttpSender} - Initializing Pass-through HTTP/S Sender...
TID: [-1234] [] [2024-12-30 03:30:38,337]  INFO {org.apache.synapse.transport.passthru.PassThroughHttpSender} - Pass-through HTTP Sender started...
TID: [-1234] [] [2024-12-30 03:30:38,337]  INFO {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Initializing Pass-through HTTP/S Sender...
TID: [-1234] [] [2024-12-30 03:30:38,347]  INFO {org.apache.synapse.transport.nhttp.config.ClientConnFactoryBuilder} - customSSLProfiles configuration is loaded from path: /home/<USER>/wso2am-3.2.0/repository/resources/security/sslprofiles.xml
TID: [-1234] [] [2024-12-30 03:30:38,353]  INFO {org.apache.synapse.transport.nhttp.config.ClientConnFactoryBuilder} - HTTPS Loading custom SSL profiles for the HTTPS sender
TID: [-1234] [] [2024-12-30 03:30:38,358]  INFO {org.apache.synapse.transport.nhttp.config.ClientConnFactoryBuilder} - HTTPS Custom SSL profiles initialized for 1 servers
TID: [-1234] [] [2024-12-30 03:30:38,360]  INFO {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Pass-through HTTPS Sender started...
TID: [-1234] [] [2024-12-30 03:30:38,364]  INFO {org.wso2.carbon.websocket.transport.WebsocketTransportSender} - WS Sender started
TID: [-1234] [] [2024-12-30 03:30:38,383]  INFO {org.apache.synapse.transport.passthru.PassThroughHttpListener} - Initializing Pass-through HTTP/S Listener...
TID: [-1234] [] [2024-12-30 03:30:38,404]  INFO {org.apache.synapse.transport.passthru.PassThroughHttpMultiSSLListener} - Initializing Pass-through HTTP/S Listener...
TID: [-1234] [] [2024-12-30 03:30:38,415]  INFO {org.apache.synapse.transport.nhttp.config.ServerConnFactoryBuilder} - SSLProfile configuration is loaded from path: /home/<USER>/wso2am-3.2.0/repository/resources/security/listenerprofiles.xml
TID: [-1234] [] [2024-12-30 03:30:39,854]  INFO {org.wso2.carbon.core.deployment.DeploymentInterceptor} - Deploying Axis2 service: I18nEmailMgtConfigService {super-tenant}
TID: [-1234] [] [2024-12-30 03:30:41,688]  INFO {org.wso2.carbon.core.deployment.DeploymentInterceptor} - Deploying Axis2 service: AccountCredentialMgtConfigService {super-tenant}
TID: [-1234] [] [2024-12-30 03:30:41,694]  INFO {org.wso2.carbon.core.deployment.DeploymentInterceptor} - Deploying Axis2 service: UserIdentityManagementAdminService {super-tenant}
TID: [-1234] [] [2024-12-30 03:30:41,699]  INFO {org.wso2.carbon.core.deployment.DeploymentInterceptor} - Deploying Axis2 service: UserInformationRecoveryService {super-tenant}
TID: [-1234] [] [2024-12-30 03:30:44,145]  WARN {org.apache.axis2.description.java2wsdl.DefaultSchemaGenerator} - We don't support method overloading. Ignoring [generateUpdatedAPIFromSwagger]
TID: [-1234] [] [2024-12-30 03:30:45,083]  INFO {org.wso2.carbon.core.init.CarbonServerManager} - Repository       : /home/<USER>/wso2am-3.2.0/repository/deployment/server/
TID: [-1234] [] [2024-12-30 03:30:45,089]  INFO {org.wso2.carbon.event.input.adapter.kafka.internal.ds.KafkaEventAdapterServiceHolder} - Kafka input event adaptor waiting for dependent configurations to load
TID: [-1234] [] [2024-12-30 03:30:45,213]  INFO {org.wso2.carbon.andes.core.internal.ds.QueueManagerServiceDS} - Successfully created the queue manager service
TID: [-1234] [] [2024-12-30 03:30:45,217]  INFO {org.wso2.carbon.andes.core.internal.ds.SubscriptionManagerServiceDS} - Successfully created the subscription manager service
TID: [-1234] [] [2024-12-30 03:30:45,289]  INFO {org.wso2.carbon.core.multitenancy.eager.TenantLoadingConfig} - Using tenant lazy loading policy...
TID: [-1234] [] [2024-12-30 03:30:45,316]  INFO {org.wso2.carbon.core.internal.permission.update.PermissionUpdater} - Permission cache updated for tenant -1234
TID: [-1234] [] [2024-12-30 03:30:45,968]  WARN {org.wso2.carbon.identity.application.mgt.internal.ApplicationManagementServiceComponent} - Templates directory not found at /home/<USER>/wso2am-3.2.0/repository/resources/identity/authntemplates
TID: [-1234] [] [2024-12-30 03:30:46,581]  WARN {org.wso2.carbon.identity.auth.service.internal.AuthenticationServiceComponent} - 

##################################  ALERT  ##################################
[WARNING]: Internal authentication is utilizing default credentials,
which may expose the environment to potential security risks.
If this is a production environment, change the credentials immediately.
Please refer to the WSO2 security advisory: https://security.docs.wso2.com/en/latest/security-announcements/security-advisories/2023/WSO2-2023-2617/
#############################################################################

TID: [-1234] [] [2024-12-30 03:30:46,602]  INFO {org.wso2.carbon.user.core.internal.UserStoreMgtDSComponent} - Claim manager set for class org.wso2.carbon.user.core.common.DefaultRealm
TID: [-1234] [] [2024-12-30 03:30:46,607]  INFO {org.wso2.carbon.user.core.internal.UserStoreMgtDSComponent} - Claim manager set for class org.wso2.carbon.user.core.jdbc.UniqueIDJDBCUserStoreManager
TID: [-1234] [] [2024-12-30 03:30:46,724]  INFO {org.wso2.carbon.identity.entitlement.internal.SchemaBuilder} - XACML policy schema loaded successfully.
TID: [-1234] [] [2024-12-30 03:30:47,329]  WARN {org.wso2.carbon.identity.oauth.config.OAuthServerConfiguration} - 'SupportedResponseModes' element not configured in identity.xml. Therefore instantiating default response mode providers
TID: [-1234] [] [2024-12-30 03:30:48,433]  INFO {org.wso2.carbon.identity.oauth2.internal.OAuth2ServiceComponent} - PKCE Support enabled.
TID: [-1234] [] [2024-12-30 03:30:48,474]  WARN {org.wso2.carbon.identity.event.internal.IdentityEventServiceComponent} - Properties for TokenBindingExpiryEventHandler is not configured. This event handler will not be activated
TID: [-1234] [] [2024-12-30 03:30:48,519]  WARN {org.wso2.carbon.identity.event.internal.IdentityEventServiceComponent} - Properties for IdentityOauthEventHandler is not configured. This event handler will not be activated
TID: [-1234] [] [2024-12-30 03:30:48,524]  INFO {org.wso2.carbon.identity.oauth.config.OAuthServerConfiguration} - The default OAuth token issuer will be used. No custom token generator is set.
TID: [-1234] [] [2024-12-30 03:30:49,287]  WARN {org.wso2.carbon.identity.event.internal.IdentityEventServiceComponent} - Properties for userMobileVerification is not configured. This event handler will not be activated
TID: [-1234] [] [2024-12-30 03:30:49,292]  WARN {org.wso2.carbon.identity.event.internal.IdentityEventServiceComponent} - Properties for identityUserMetadataMgtHandler is not configured. This event handler will not be activated
TID: [-1234] [] [2024-12-30 03:30:49,449]  WARN {org.wso2.carbon.identity.event.internal.IdentityEventServiceComponent} - Properties for challengeAnswerValidation is not configured. This event handler will not be activated
TID: [-1234] [] [2024-12-30 03:30:49,735]  INFO {org.wso2.carbon.identity.application.authentication.framework.store.SessionDataStore} - Thread pool size for temporary authentication context data delete task: 20
TID: [-1234] [] [2024-12-30 03:30:53,141]  INFO {org.wso2.carbon.mediation.initializer.ServiceBusInitializer} - Starting ESB...
TID: [-1234] [] [2024-12-30 03:30:53,185]  INFO {org.wso2.carbon.mediation.initializer.ServiceBusInitializer} - Initializing Apache Synapse...
TID: [-1234] [] [2024-12-30 03:30:53,190]  INFO {org.apache.synapse.SynapseControllerFactory} - Using Synapse home : /home/<USER>/wso2am-3.2.0/.
TID: [-1234] [] [2024-12-30 03:30:53,191]  INFO {org.apache.synapse.SynapseControllerFactory} - Using synapse.xml location : /home/<USER>/wso2am-3.2.0/././repository/deployment/server/synapse-configs/default
TID: [-1234] [] [2024-12-30 03:30:53,191]  INFO {org.apache.synapse.SynapseControllerFactory} - Using server name : localhost
TID: [-1234] [] [2024-12-30 03:30:53,224]  INFO {org.apache.synapse.SynapseControllerFactory} - The timeout handler will run every : 15s
TID: [-1234] [] [2024-12-30 03:30:53,350]  INFO {org.apache.synapse.Axis2SynapseController} - Initializing Synapse at : Mon Dec 30 03:30:53 ICT 2024
TID: [-1234] [] [2024-12-30 03:30:53,358]  INFO {org.wso2.carbon.mediation.initializer.CarbonSynapseController} - Loading the mediation configuration from the file system
TID: [-1234] [] [2024-12-30 03:30:53,361]  INFO {org.apache.synapse.config.xml.MultiXMLConfigurationBuilder} - Building synapse configuration from the synapse artifact repository at : ././repository/deployment/server/synapse-configs/default
TID: [-1234] [] [2024-12-30 03:30:53,362]  INFO {org.apache.synapse.config.xml.XMLConfigurationBuilder} - Generating the Synapse configuration model by parsing the XML configuration
TID: [-1234] [] [2024-12-30 03:30:53,444]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Local entry : 9e9cc2bc-c6c2-4eac-ba3c-dc9be00b18a7 was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,445]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Local entry : 87e571e9-5035-4302-8434-1c47382e486b was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,446]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Local entry : a8385c6e-a2f6-417f-a334-effaf0ef26de was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,447]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Local entry : 53acb56f-3653-4150-b62d-2696ef1a8d71 was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,447]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Local entry : a08a032e-acc2-4200-a9cd-1c4c9fea075b was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,448]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Local entry : cbc3c381-5735-4d11-81da-5dde73fca457 was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,448]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Local entry : 67467d67-3d33-4b11-986c-d1680795caf9 was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,449]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Local entry : fcffe066-4401-4a4f-b52c-0e227a2f782e was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,450]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Local entry : 4dc51345-1cce-4970-861c-1e3d2e1c20ce was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,450]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Local entry : 2fc2021d-595b-49d1-9b37-d726f8683445 was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,451]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Local entry : d052547d-b580-4536-893d-16d8516698dc was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,452]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Local entry : d731dfca-d905-42e2-b947-fa8ac1b4d1bd was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,453]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Local entry : ga-config-key was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,453]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Local entry : 7db7fe72-7514-4ae6-9470-babbef18dbfa was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,454]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Local entry : 4e665118-7b23-4689-b0dc-839c00625c88 was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,455]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Local entry : f0ff8865-b5f4-4de0-a31d-f79059fc7f8a was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,456]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Local entry : baa56eb5-0bec-43e4-a063-7fcb26999c39 was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,457]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Local entry : 7624f5f8-29d2-495d-a1a5-e3b7ca500f29 was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,457]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Local entry : 616fcad3-729b-49e2-af7f-91b3307d4b70 was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,458]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Local entry : 9eb01848-f40c-428c-b554-3435d231b665 was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,459]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Local entry : 695139eb-0523-4b13-9f18-c5371fdb6410 was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,460]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Local entry : c13aba21-0abf-4063-b180-3f7d0cb5b752 was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,460]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Local entry : a8e5df8f-43e5-4b60-b467-9ccf9ecae43f was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,461]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Local entry : f838825a-bf77-49aa-9de0-a27700d4d05c was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,487]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : HoSoIGATE--v1.0.0_APIsandboxEndpoint was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,489]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : DTNN--v1.0.0_APIsandboxEndpoint was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,490]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : GPLX--v1.0.0_APIproductionEndpoint was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,491]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : QuanLyTaiSanCong--v1.0_APIsandboxEndpoint was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,492]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : IGATE-TKHS--v1.0.0_APIsandboxEndpoint was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,493]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,494]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : LyLichTuPhap--v1.0_APIsandboxEndpoint was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,495]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : HoSoIGATE--v1.0.0_APIproductionEndpoint was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,496]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : LGSP-VNPOST--v1.0.0_APIproductionEndpoint was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,497]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : MAIL--v1.0.0_APIproductionEndpoint was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,498]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : PHBH--v1.0.0_APIproductionEndpoint was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,499]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : LD-TBXH--v1.0.0_APIsandboxEndpoint was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,500]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : LGSP-HTTP--v1.0.0_APIproductionEndpoint was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,502]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,503]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : LGSP-VBQPPL--v1.0.0_APIproductionEndpoint was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,504]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : DanhMucDC--v1.0.0_APIproductionEndpoint was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,505]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : LGSP_LLTP--v1.0_APIsandboxEndpoint was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,506]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : IGATE-TKHS--v1.0.0_APIproductionEndpoint was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,507]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : GPLX--v1.0.0_APIsandboxEndpoint was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,508]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : LGSP_LLTP--v1.0_APIproductionEndpoint was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,508]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : MSNS01--v1.0.0_APIsandboxEndpoint was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,509]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : DTNN--v1.0.0_APIproductionEndpoint was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,511]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : LGSP-GTVT--v1.0.0_APIproductionEndpoint was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,512]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : HNY--v1.0.0_APIsandboxEndpoint was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,513]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : GPLX2--v1.0_APIproductionEndpoint was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,513]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : HNY--v1.0.0_APIproductionEndpoint was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,514]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : DanhMucDungChungHD--v1.0.0_APIproductionEndpoint was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,515]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : LGSP-HTTP--v1.0.0_APIsandboxEndpoint was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,517]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : DanhMucDungChungHD--v1.0.0_APIsandboxEndpoint was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,518]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : DKDN01--v1.0.0_APIsandboxEndpoint was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,519]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : IGATE-VBDLIS--v1.0.0_APIsandboxEndpoint was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,521]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : MAIL--v1.0.0_APIsandboxEndpoint was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,522]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : GPLX2--v1.0_APIsandboxEndpoint was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,523]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : LGSP-VBQPPL--v1.0.0_APIsandboxEndpoint was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,524]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : LGSP-GTVT--v1.0.0_APIsandboxEndpoint was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,524]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : DVCLienthong--v1.0_APIproductionEndpoint was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,525]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : VDBLIS--v1.0.0_APIproductionEndpoint was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,526]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : LGSP-VNPOST--v1.0.0_APIsandboxEndpoint was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,527]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : VDBLIS--v1.0.0_APIsandboxEndpoint was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,528]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : LD-TBXH--v1.0.0_APIproductionEndpoint was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,528]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : DVCLienthong--v1.0_APIsandboxEndpoint was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,529]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : DKDN01--v1.0.0_APIproductionEndpoint was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,530]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : IGATE-VBDLIS--v1.0.0_APIproductionEndpoint was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,531]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : DanhMucDC--v1.0.0_APIsandboxEndpoint was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,531]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : PHBH--v1.0.0_APIsandboxEndpoint was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,532]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : MSNS01--v1.0.0_APIproductionEndpoint was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,571]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Sequence : _graphql_failure_handler was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,572]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Sequence : outDispatchSeq was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,573]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Sequence : _resource_mismatch_handler_ was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,600]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Sequence : _token_fault_ was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,610]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Sequence : _threat_fault_ was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,626]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Sequence : main was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,628]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Sequence : _production_key_error_ was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,628]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Sequence : _auth_failure_handler_ was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,630]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Sequence : dispatchSeq was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,631]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Sequence : _sandbox_key_error_ was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,656]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Sequence : _throttle_out_handler_ was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,661]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Sequence : _build_ was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,671]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Sequence : fault was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,674]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Sequence : _cors_request_handler_ was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,687]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Proxy service : WorkflowCallbackService was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,821]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : admin--GPLX2:v1.0 was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,829]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : admin--IGATE-TKHS:v1.0.0 was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,834]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : _WSO2AMTokenAPI_ was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,858]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : _OpenService_ was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,869]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : admin--VDBLIS:v1.0.0 was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,873]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : admin--MAIL:v1.0.0 was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,874]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : _WSO2AMLoginContextAPI_ was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,876]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : _WSO2AMUserInfoAPI_ was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,878]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : admin--DKDN01:v1.0.0 was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,899]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : admin--DanhMucDC:v1.0.0 was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,900]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : _WSO2AMCommonAuthAPI_ was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,901]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : _WSO2AMAuthorizeAPI_ was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,905]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : admin--LGSP-VNPOST:v1.0.0 was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,908]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : admin--LGSP-GTVT:v1.0.0 was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,919]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : admin--IGATE-VBDLIS:v1.0.0 was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,922]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : admin--DTNN:v1.0.0 was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,926]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : admin--LD-TBXH:v1.0.0 was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,928]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : admin--PHBH:v1.0.0 was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,931]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : admin--DVCLienthong:v1.0 was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,933]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : admin--MSNS01:v1.0.0 was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,935]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : admin--GPLX:v1.0.0 was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,943]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : admin--HoSoIGATE:v1.0.0 was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,947]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : admin--DanhMucDungChungHD:v1.0.0 was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,949]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : _WSO2OIDCAPI_ was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,953]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : admin--LGSP-HTTP:v1.0.0 was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,961]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : admin--LyLichTuPhap:v1.0 was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,965]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : admin--LGSP_LLTP:v1.0 was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,975]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : admin--QuanLyTaiSanCong:v1.0 was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,978]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : admin--HNY:v1.0.0 was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,979]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : _WSO2AMRevokeAPI_ was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,984]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : admin--LGSP-VBQPPL:v1.0.0 was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,985]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : _WSO2AMAuthenticationEPAPI_ was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,986]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : _WSO2AMOpenIDConfigAPI_ was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,989]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Inbound Endpoint : SecureWebSocketEP was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,990]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Inbound Endpoint : WebSocketInboundEndpoint was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,990]  INFO {org.apache.synapse.config.SynapseConfigurationBuilder} - Loaded Synapse configuration from the artifact repository at : ././repository/deployment/server/synapse-configs/default
TID: [-1234] [] [2024-12-30 03:30:53,991]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Local entry : SERVER_HOST was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:53,991]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Local entry : SERVER_IP was added to the Synapse configuration successfully
TID: [-1234] [] [2024-12-30 03:30:54,047]  INFO {org.apache.synapse.Axis2SynapseController} - Loading mediator extensions...
TID: [-1234] [] [2024-12-30 03:30:54,180]  INFO {org.wso2.carbon.core.deployment.DeploymentInterceptor} - Deploying Axis2 service: echo {super-tenant}
TID: [-1234] [] [2024-12-30 03:30:54,216]  INFO {org.wso2.carbon.core.deployment.DeploymentInterceptor} - Deploying Axis2 service: Version {super-tenant}
TID: [-1234] [] [2024-12-30 03:30:54,230]  INFO {org.wso2.carbon.event.stream.core.EventStreamDeployer} - Stream definition is deployed successfully  : cache.invalidation.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:54,231]  INFO {org.wso2.carbon.event.stream.core.EventStreamDeployer} - Stream definition is deployed successfully  : id_gov_notify_stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:54,232]  INFO {org.wso2.carbon.event.stream.core.EventStreamDeployer} - Stream definition is deployed successfully  : org.wso2.apimgt.cache.invalidation.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:54,233]  INFO {org.wso2.carbon.event.stream.core.EventStreamDeployer} - Stream definition is deployed successfully  : org.wso2.apimgt.keymgt.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:54,234]  INFO {org.wso2.carbon.event.stream.core.EventStreamDeployer} - Stream definition is deployed successfully  : org.wso2.apimgt.notification.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:54,235]  INFO {org.wso2.carbon.event.stream.core.EventStreamDeployer} - Stream definition is deployed successfully  : org.wso2.apimgt.perapi.log.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:54,235]  INFO {org.wso2.carbon.event.stream.core.EventStreamDeployer} - Stream definition is deployed successfully  : org.wso2.apimgt.recommendation.event.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:54,236]  INFO {org.wso2.carbon.event.stream.core.EventStreamDeployer} - Stream definition is deployed successfully  : org.wso2.apimgt.token.revocation.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:54,237]  INFO {org.wso2.carbon.event.stream.core.EventStreamDeployer} - Stream definition is deployed successfully  : org.wso2.blocking.request.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:54,238]  INFO {org.wso2.carbon.event.stream.core.EventStreamDeployer} - Stream definition is deployed successfully  : org.wso2.botDetectionData.request.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:54,239]  INFO {org.wso2.carbon.event.stream.core.EventStreamDeployer} - Stream definition is deployed successfully  : org.wso2.keytemplate.request.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:54,240]  INFO {org.wso2.carbon.event.stream.core.EventStreamDeployer} - Stream definition is deployed successfully  : org.wso2.throttle.globalThrottle.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:54,241]  INFO {org.wso2.carbon.event.stream.core.EventStreamDeployer} - Stream definition is deployed successfully  : org.wso2.throttle.globalThrottle.stream:1.1.0
TID: [-1234] [] [2024-12-30 03:30:54,242]  INFO {org.wso2.carbon.event.stream.core.EventStreamDeployer} - Stream definition is deployed successfully  : org.wso2.throttle.processed.request.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:54,243]  INFO {org.wso2.carbon.event.stream.core.EventStreamDeployer} - Stream definition is deployed successfully  : org.wso2.throttle.request.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:54,255]  INFO {org.wso2.carbon.event.stream.core.internal.EventJunction} - WSO2EventConsumer added to the junction. Stream:id_gov_notify_stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:54,258]  INFO {org.wso2.carbon.event.publisher.core.EventPublisherDeployer} - Event Publisher configuration successfully deployed and in active state : EmailPublisher
TID: [-1234] [] [2024-12-30 03:30:54,262]  INFO {org.wso2.carbon.event.stream.core.internal.EventJunction} - WSO2EventConsumer added to the junction. Stream:org.wso2.apimgt.recommendation.event.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:54,264]  INFO {org.wso2.carbon.event.publisher.core.EventPublisherDeployer} - Event Publisher configuration successfully deployed and in active state : HTTP-recommendationEventPublisher
TID: [-1234] [] [2024-12-30 03:30:54,266]  INFO {org.wso2.carbon.event.stream.core.internal.EventJunction} - WSO2EventConsumer added to the junction. Stream:org.wso2.blocking.request.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:54,286]  INFO {org.wso2.carbon.event.publisher.core.EventPublisherDeployer} - Event Publisher configuration successfully deployed and in active state : blockingEventJMSPublisher
TID: [-1234] [] [2024-12-30 03:30:54,287]  INFO {org.wso2.carbon.event.stream.core.internal.EventJunction} - WSO2EventConsumer added to the junction. Stream:org.wso2.apimgt.cache.invalidation.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:54,288]  INFO {org.wso2.carbon.event.publisher.core.EventPublisherDeployer} - Event Publisher configuration successfully deployed and in active state : cacheInvalidationJMSPublisher
TID: [-1234] [] [2024-12-30 03:30:54,289]  INFO {org.wso2.carbon.event.stream.core.internal.EventJunction} - WSO2EventConsumer added to the junction. Stream:cache.invalidation.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:54,289]  INFO {org.wso2.carbon.event.publisher.core.EventPublisherDeployer} - Event Publisher configuration successfully deployed and in active state : globalCacheInvalidationJMSPublisher
TID: [-1234] [] [2024-12-30 03:30:54,290]  INFO {org.wso2.carbon.event.stream.core.internal.EventJunction} - WSO2EventConsumer added to the junction. Stream:org.wso2.throttle.globalThrottle.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:54,290]  INFO {org.wso2.carbon.event.publisher.core.EventPublisherDeployer} - Event Publisher configuration successfully deployed and in active state : jmsEventPublisher
TID: [-1234] [] [2024-12-30 03:30:54,291]  INFO {org.wso2.carbon.event.stream.core.internal.EventJunction} - WSO2EventConsumer added to the junction. Stream:org.wso2.throttle.globalThrottle.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:54,291]  INFO {org.wso2.carbon.event.publisher.core.EventPublisherDeployer} - Event Publisher configuration successfully deployed and in active state : jmsEventPublisher2
TID: [-1234] [] [2024-12-30 03:30:54,292]  INFO {org.wso2.carbon.event.stream.core.internal.EventJunction} - WSO2EventConsumer added to the junction. Stream:org.wso2.throttle.globalThrottle.stream:1.1.0
TID: [-1234] [] [2024-12-30 03:30:54,292]  INFO {org.wso2.carbon.event.publisher.core.EventPublisherDeployer} - Event Publisher configuration successfully deployed and in active state : jmsEventPublisher-1.0.0
TID: [-1234] [] [2024-12-30 03:30:54,294]  INFO {org.wso2.carbon.event.stream.core.internal.EventJunction} - WSO2EventConsumer added to the junction. Stream:org.wso2.throttle.globalThrottle.stream:1.1.0
TID: [-1234] [] [2024-12-30 03:30:54,294]  INFO {org.wso2.carbon.event.publisher.core.EventPublisherDeployer} - Event Publisher configuration successfully deployed and in active state : jmsEventPublisher-1.0.0-2
TID: [-1234] [] [2024-12-30 03:30:54,295]  INFO {org.wso2.carbon.event.stream.core.internal.EventJunction} - WSO2EventConsumer added to the junction. Stream:org.wso2.keytemplate.request.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:54,295]  INFO {org.wso2.carbon.event.publisher.core.EventPublisherDeployer} - Event Publisher configuration successfully deployed and in active state : keyTemplateEventPublisher
TID: [-1234] [] [2024-12-30 03:30:54,296]  INFO {org.wso2.carbon.event.stream.core.internal.EventJunction} - WSO2EventConsumer added to the junction. Stream:org.wso2.apimgt.keymgt.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:54,296]  INFO {org.wso2.carbon.event.publisher.core.EventPublisherDeployer} - Event Publisher configuration successfully deployed and in active state : keymgtEventJMSEventPublisher
TID: [-1234] [] [2024-12-30 03:30:54,297]  INFO {org.wso2.carbon.event.stream.core.internal.EventJunction} - WSO2EventConsumer added to the junction. Stream:org.wso2.apimgt.notification.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:54,298]  INFO {org.wso2.carbon.event.publisher.core.EventPublisherDeployer} - Event Publisher configuration successfully deployed and in active state : notificationJMSPublisher
TID: [-1234] [] [2024-12-30 03:30:54,298]  INFO {org.wso2.carbon.event.stream.core.internal.EventJunction} - WSO2EventConsumer added to the junction. Stream:org.wso2.apimgt.perapi.log.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:54,299]  INFO {org.wso2.carbon.event.publisher.core.EventPublisherDeployer} - Event Publisher configuration successfully deployed and in active state : perAPILogDataPublisher
TID: [-1234] [] [2024-12-30 03:30:54,299]  INFO {org.wso2.carbon.event.stream.core.internal.EventJunction} - WSO2EventConsumer added to the junction. Stream:org.wso2.apimgt.token.revocation.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:54,300]  INFO {org.wso2.carbon.event.publisher.core.EventPublisherDeployer} - Event Publisher configuration successfully deployed and in active state : tokenRevocationJMSPublisher
TID: [-1234] [] [2024-12-30 03:30:54,301]  INFO {org.wso2.carbon.event.stream.core.internal.EventJunction} - WSO2EventConsumer added to the junction. Stream:org.wso2.apimgt.token.revocation.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:54,301]  INFO {org.wso2.carbon.event.publisher.core.EventPublisherDeployer} - Event Publisher configuration successfully deployed and in active state : tokenRevocationJMSPublisher2
TID: [-1234] [] [2024-12-30 03:30:54,313]  INFO {org.wso2.carbon.event.input.adapter.core.internal.InputAdapterRuntime} - Connecting receiver blockingWso2EventReceiver
TID: [-1234] [] [2024-12-30 03:30:54,314]  INFO {org.wso2.carbon.event.stream.core.internal.EventJunction} - Producer added to the junction. Stream:org.wso2.blocking.request.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:54,315]  INFO {org.wso2.carbon.event.receiver.core.EventReceiverDeployer} - Event Receiver configuration successfully deployed and in active state: blockingWso2EventReceiver
TID: [-1234] [] [2024-12-30 03:30:54,316]  INFO {org.wso2.carbon.event.input.adapter.core.internal.InputAdapterRuntime} - Connecting receiver cacheInvalidationRequestReceiver
TID: [-1234] [] [2024-12-30 03:30:54,317]  INFO {org.wso2.carbon.event.stream.core.internal.EventJunction} - Producer added to the junction. Stream:cache.invalidation.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:54,317]  INFO {org.wso2.carbon.event.receiver.core.EventReceiverDeployer} - Event Receiver configuration successfully deployed and in active state: cacheInvalidationRequestReceiver
TID: [-1234] [] [2024-12-30 03:30:54,318]  INFO {org.wso2.carbon.event.input.adapter.core.internal.InputAdapterRuntime} - Connecting receiver cacheInvalidationWso2EventReceiver
TID: [-1234] [] [2024-12-30 03:30:54,319]  INFO {org.wso2.carbon.event.stream.core.internal.EventJunction} - Producer added to the junction. Stream:org.wso2.apimgt.cache.invalidation.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:54,319]  INFO {org.wso2.carbon.event.receiver.core.EventReceiverDeployer} - Event Receiver configuration successfully deployed and in active state: cacheInvalidationWso2EventReceiver
TID: [-1234] [] [2024-12-30 03:30:54,320]  INFO {org.wso2.carbon.event.input.adapter.core.internal.InputAdapterRuntime} - Connecting receiver keyMgtWso2EventReceiver
TID: [-1234] [] [2024-12-30 03:30:54,320]  INFO {org.wso2.carbon.event.stream.core.internal.EventJunction} - Producer added to the junction. Stream:org.wso2.apimgt.keymgt.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:54,321]  INFO {org.wso2.carbon.event.receiver.core.EventReceiverDeployer} - Event Receiver configuration successfully deployed and in active state: keyMgtWso2EventReceiver
TID: [-1234] [] [2024-12-30 03:30:54,322]  INFO {org.wso2.carbon.event.input.adapter.core.internal.InputAdapterRuntime} - Connecting receiver keyTemplateWso2EventReceiver
TID: [-1234] [] [2024-12-30 03:30:54,322]  INFO {org.wso2.carbon.event.stream.core.internal.EventJunction} - Producer added to the junction. Stream:org.wso2.keytemplate.request.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:54,323]  INFO {org.wso2.carbon.event.receiver.core.EventReceiverDeployer} - Event Receiver configuration successfully deployed and in active state: keyTemplateWso2EventReceiver
TID: [-1234] [] [2024-12-30 03:30:54,324]  INFO {org.wso2.carbon.event.input.adapter.core.internal.InputAdapterRuntime} - Connecting receiver notificationsWSO2EventReceiver
TID: [-1234] [] [2024-12-30 03:30:54,324]  INFO {org.wso2.carbon.event.stream.core.internal.EventJunction} - Producer added to the junction. Stream:org.wso2.apimgt.notification.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:54,325]  INFO {org.wso2.carbon.event.receiver.core.EventReceiverDeployer} - Event Receiver configuration successfully deployed and in active state: notificationsWSO2EventReceiver
TID: [-1234] [] [2024-12-30 03:30:54,325]  INFO {org.wso2.carbon.event.input.adapter.core.internal.InputAdapterRuntime} - Connecting receiver perapilogeventreceiver
TID: [-1234] [] [2024-12-30 03:30:54,326]  INFO {org.wso2.carbon.event.stream.core.internal.EventJunction} - Producer added to the junction. Stream:org.wso2.apimgt.perapi.log.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:54,326]  INFO {org.wso2.carbon.event.receiver.core.EventReceiverDeployer} - Event Receiver configuration successfully deployed and in active state: perapilogeventreceiver
TID: [-1234] [] [2024-12-30 03:30:54,327]  INFO {org.wso2.carbon.event.input.adapter.core.internal.InputAdapterRuntime} - Connecting receiver recommendationWso2EventReceiver
TID: [-1234] [] [2024-12-30 03:30:54,327]  INFO {org.wso2.carbon.event.stream.core.internal.EventJunction} - Producer added to the junction. Stream:org.wso2.apimgt.recommendation.event.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:54,328]  INFO {org.wso2.carbon.event.receiver.core.EventReceiverDeployer} - Event Receiver configuration successfully deployed and in active state: recommendationWso2EventReceiver
TID: [-1234] [] [2024-12-30 03:30:54,333]  INFO {org.wso2.carbon.event.input.adapter.core.internal.InputAdapterRuntime} - Connecting receiver throttleEventReceiver
TID: [-1234] [] [2024-12-30 03:30:54,334]  INFO {org.wso2.carbon.event.stream.core.internal.EventJunction} - Producer added to the junction. Stream:org.wso2.throttle.request.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:54,335]  INFO {org.wso2.carbon.event.receiver.core.EventReceiverDeployer} - Event Receiver configuration successfully deployed and in active state: throttleEventReceiver
TID: [-1234] [] [2024-12-30 03:30:54,335]  INFO {org.wso2.carbon.event.input.adapter.core.internal.InputAdapterRuntime} - Connecting receiver throttleWSO2EventReceiver
TID: [-1234] [] [2024-12-30 03:30:54,336]  INFO {org.wso2.carbon.event.stream.core.internal.EventJunction} - Producer added to the junction. Stream:org.wso2.throttle.request.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:54,336]  INFO {org.wso2.carbon.event.receiver.core.EventReceiverDeployer} - Event Receiver configuration successfully deployed and in active state: throttleWSO2EventReceiver
TID: [-1234] [] [2024-12-30 03:30:54,337]  INFO {org.wso2.carbon.event.input.adapter.core.internal.InputAdapterRuntime} - Connecting receiver tokenRevocationWso2EventReceiver
TID: [-1234] [] [2024-12-30 03:30:54,337]  INFO {org.wso2.carbon.event.stream.core.internal.EventJunction} - Producer added to the junction. Stream:org.wso2.apimgt.token.revocation.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:54,338]  INFO {org.wso2.carbon.event.receiver.core.EventReceiverDeployer} - Event Receiver configuration successfully deployed and in active state: tokenRevocationWso2EventReceiver
TID: [-1234] [] [2024-12-30 03:30:54,812]  INFO {org.wso2.carbon.event.stream.core.internal.EventJunction} - Producer added to the junction. Stream:org.wso2.throttle.processed.request.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:54,815]  INFO {org.wso2.carbon.event.stream.core.internal.EventJunction} - Consumer added to the junction. Stream:org.wso2.throttle.request.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:54,816]  INFO {org.wso2.carbon.event.processor.core.EventProcessorDeployer} - Execution plan is deployed successfully and in active state  : requestPreProcessorExecutionPlan
TID: [-1234] [] [2024-12-30 03:30:54,892]  INFO {org.wso2.carbon.event.stream.core.internal.EventJunction} - Producer added to the junction. Stream:org.wso2.throttle.globalThrottle.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:54,893]  INFO {org.wso2.carbon.event.stream.core.internal.EventJunction} - Consumer added to the junction. Stream:org.wso2.throttle.processed.request.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:54,894]  INFO {org.wso2.carbon.event.processor.core.EventProcessorDeployer} - Execution plan is deployed successfully and in active state  : carbon.super_app_10PerMin
TID: [-1234] [] [2024-12-30 03:30:54,954]  INFO {org.wso2.carbon.event.stream.core.internal.EventJunction} - Producer added to the junction. Stream:org.wso2.throttle.globalThrottle.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:54,954]  INFO {org.wso2.carbon.event.stream.core.internal.EventJunction} - Consumer added to the junction. Stream:org.wso2.throttle.processed.request.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:54,955]  INFO {org.wso2.carbon.event.processor.core.EventProcessorDeployer} - Execution plan is deployed successfully and in active state  : carbon.super_app_20PerMin
TID: [-1234] [] [2024-12-30 03:30:54,987]  INFO {org.wso2.carbon.event.stream.core.internal.EventJunction} - Producer added to the junction. Stream:org.wso2.throttle.globalThrottle.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:54,988]  INFO {org.wso2.carbon.event.stream.core.internal.EventJunction} - Consumer added to the junction. Stream:org.wso2.throttle.processed.request.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:54,989]  INFO {org.wso2.carbon.event.processor.core.EventProcessorDeployer} - Execution plan is deployed successfully and in active state  : carbon.super_app_50PerMin
TID: [-1234] [] [2024-12-30 03:30:55,020]  INFO {org.wso2.carbon.event.stream.core.internal.EventJunction} - Producer added to the junction. Stream:org.wso2.throttle.globalThrottle.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:55,021]  INFO {org.wso2.carbon.event.stream.core.internal.EventJunction} - Consumer added to the junction. Stream:org.wso2.throttle.processed.request.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:55,021]  INFO {org.wso2.carbon.event.processor.core.EventProcessorDeployer} - Execution plan is deployed successfully and in active state  : carbon.super_resource_10KPerMin_default
TID: [-1234] [] [2024-12-30 03:30:55,051]  INFO {org.wso2.carbon.event.stream.core.internal.EventJunction} - Producer added to the junction. Stream:org.wso2.throttle.globalThrottle.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:55,052]  INFO {org.wso2.carbon.event.stream.core.internal.EventJunction} - Consumer added to the junction. Stream:org.wso2.throttle.processed.request.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:55,053]  INFO {org.wso2.carbon.event.processor.core.EventProcessorDeployer} - Execution plan is deployed successfully and in active state  : carbon.super_resource_20KPerMin_default
TID: [-1234] [] [2024-12-30 03:30:55,082]  INFO {org.wso2.carbon.event.stream.core.internal.EventJunction} - Producer added to the junction. Stream:org.wso2.throttle.globalThrottle.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:55,083]  INFO {org.wso2.carbon.event.stream.core.internal.EventJunction} - Consumer added to the junction. Stream:org.wso2.throttle.processed.request.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:55,084]  INFO {org.wso2.carbon.event.processor.core.EventProcessorDeployer} - Execution plan is deployed successfully and in active state  : carbon.super_resource_50KPerMin_default
TID: [-1234] [] [2024-12-30 03:30:55,112]  INFO {org.wso2.carbon.event.stream.core.internal.EventJunction} - Producer added to the junction. Stream:org.wso2.throttle.globalThrottle.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:55,113]  INFO {org.wso2.carbon.event.stream.core.internal.EventJunction} - Consumer added to the junction. Stream:org.wso2.throttle.processed.request.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:55,113]  INFO {org.wso2.carbon.event.processor.core.EventProcessorDeployer} - Execution plan is deployed successfully and in active state  : carbon.super_sub_Bronze
TID: [-1234] [] [2024-12-30 03:30:55,146]  INFO {org.wso2.carbon.event.stream.core.internal.EventJunction} - Producer added to the junction. Stream:org.wso2.throttle.globalThrottle.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:55,146]  INFO {org.wso2.carbon.event.stream.core.internal.EventJunction} - Consumer added to the junction. Stream:org.wso2.throttle.processed.request.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:55,147]  INFO {org.wso2.carbon.event.processor.core.EventProcessorDeployer} - Execution plan is deployed successfully and in active state  : carbon.super_sub_Gold
TID: [-1234] [] [2024-12-30 03:30:55,175]  INFO {org.wso2.carbon.event.stream.core.internal.EventJunction} - Producer added to the junction. Stream:org.wso2.throttle.globalThrottle.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:55,176]  INFO {org.wso2.carbon.event.stream.core.internal.EventJunction} - Consumer added to the junction. Stream:org.wso2.throttle.processed.request.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:55,176]  INFO {org.wso2.carbon.event.processor.core.EventProcessorDeployer} - Execution plan is deployed successfully and in active state  : carbon.super_sub_Silver
TID: [-1234] [] [2024-12-30 03:30:55,204]  INFO {org.wso2.carbon.event.stream.core.internal.EventJunction} - Producer added to the junction. Stream:org.wso2.throttle.globalThrottle.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:55,205]  INFO {org.wso2.carbon.event.stream.core.internal.EventJunction} - Consumer added to the junction. Stream:org.wso2.throttle.processed.request.stream:1.0.0
TID: [-1234] [] [2024-12-30 03:30:55,205]  INFO {org.wso2.carbon.event.processor.core.EventProcessorDeployer} - Execution plan is deployed successfully and in active state  : carbon.super_sub_Unauthenticated
TID: [-1234] [] [2024-12-30 03:30:55,972]  INFO {org.wso2.carbon.webapp.mgt.TomcatGenericWebappsDeployer} - Deployed webapp: StandardEngine[Catalina].StandardHost[localhost].StandardContext[/accountrecoveryendpoint].File[/home/<USER>/wso2am-3.2.0/repository/deployment/server/webapps/accountrecoveryendpoint]
TID: [-1234] [] [2024-12-30 03:30:57,254]  INFO {org.wso2.carbon.webapp.mgt.TomcatGenericWebappsDeployer} - Deployed webapp: StandardEngine[Catalina].StandardHost[localhost].StandardContext[/am/sample/calculator/v1].File[/home/<USER>/wso2am-3.2.0/repository/deployment/server/webapps/am#sample#calculator#v1.war]
TID: [-1234] [] [2024-12-30 03:30:58,352]  INFO {org.wso2.carbon.webapp.mgt.TomcatGenericWebappsDeployer} - Deployed webapp: StandardEngine[Catalina].StandardHost[localhost].StandardContext[/am/sample/pizzashack/v1].File[/home/<USER>/wso2am-3.2.0/repository/deployment/server/webapps/am#sample#pizzashack#v1.war]
TID: [-1234] [] [2024-12-30 03:30:58,593]  INFO {org.wso2.carbon.webapp.mgt.TomcatGenericWebappsDeployer} - Deployed webapp: StandardEngine[Catalina].StandardHost[localhost].StandardContext[/api].File[/home/<USER>/wso2am-3.2.0/repository/deployment/server/webapps/api]
TID: [-1234] [] [2024-12-30 03:31:00,557]  INFO {org.wso2.carbon.webapp.mgt.TomcatGenericWebappsDeployer} - Deployed webapp: StandardEngine[Catalina].StandardHost[localhost].StandardContext[/api/am/admin/v0.17].File[/home/<USER>/wso2am-3.2.0/repository/deployment/server/webapps/api#am#admin#v0.17.war]
TID: [-1234] [] [2024-12-30 03:31:02,070]  INFO {org.wso2.carbon.webapp.mgt.TomcatGenericWebappsDeployer} - Deployed webapp: StandardEngine[Catalina].StandardHost[localhost].StandardContext[/api/am/admin].File[/home/<USER>/wso2am-3.2.0/repository/deployment/server/webapps/api#am#admin.war]
TID: [-1234] [] [2024-12-30 03:31:03,380]  INFO {org.wso2.carbon.webapp.mgt.TomcatGenericWebappsDeployer} - Deployed webapp: StandardEngine[Catalina].StandardHost[localhost].StandardContext[/api/am/devops/v0].File[/home/<USER>/wso2am-3.2.0/repository/deployment/server/webapps/api#am#devops#v0.war]
TID: [-1234] [] [2024-12-30 03:31:04,413]  INFO {org.wso2.carbon.webapp.mgt.TomcatGenericWebappsDeployer} - Deployed webapp: StandardEngine[Catalina].StandardHost[localhost].StandardContext[/api/am/gateway/v0.9].File[/home/<USER>/wso2am-3.2.0/repository/deployment/server/webapps/api#am#gateway#v0.9.war]
TID: [-1234] [] [2024-12-30 03:31:05,799]  INFO {org.wso2.carbon.webapp.mgt.TomcatGenericWebappsDeployer} - Deployed webapp: StandardEngine[Catalina].StandardHost[localhost].StandardContext[/api/am/gateway].File[/home/<USER>/wso2am-3.2.0/repository/deployment/server/webapps/api#am#gateway.war]
TID: [-1234] [] [2024-12-30 03:31:07,341]  INFO {org.wso2.carbon.webapp.mgt.TomcatGenericWebappsDeployer} - Deployed webapp: StandardEngine[Catalina].StandardHost[localhost].StandardContext[/api/am/publisher].File[/home/<USER>/wso2am-3.2.0/repository/deployment/server/webapps/api#am#publisher.war]
TID: [-1234] [] [2024-12-30 03:31:08,726]  INFO {org.wso2.carbon.webapp.mgt.TomcatGenericWebappsDeployer} - Deployed webapp: StandardEngine[Catalina].StandardHost[localhost].StandardContext[/api/am/store].File[/home/<USER>/wso2am-3.2.0/repository/deployment/server/webapps/api#am#store.war]
TID: [-1234] [] [2024-12-30 03:31:09,655]  INFO {org.wso2.carbon.webapp.mgt.TomcatGenericWebappsDeployer} - Deployed webapp: StandardEngine[Catalina].StandardHost[localhost].StandardContext[/api/identity/consent-mgt/v1.0].File[/home/<USER>/wso2am-3.2.0/repository/deployment/server/webapps/api#identity#consent-mgt#v1.0.war]
TID: [-1234] [] [2024-12-30 03:31:10,744]  INFO {org.wso2.carbon.webapp.mgt.TomcatGenericWebappsDeployer} - Deployed webapp: StandardEngine[Catalina].StandardHost[localhost].StandardContext[/api/identity/oauth2/dcr/v1.1].File[/home/<USER>/wso2am-3.2.0/repository/deployment/server/webapps/api#identity#oauth2#dcr#v1.1.war]
TID: [-1234] [] [2024-12-30 03:31:11,673]  INFO {org.wso2.carbon.webapp.mgt.TomcatGenericWebappsDeployer} - Deployed webapp: StandardEngine[Catalina].StandardHost[localhost].StandardContext[/api/identity/oauth2/v1.0].File[/home/<USER>/wso2am-3.2.0/repository/deployment/server/webapps/api#identity#oauth2#v1.0.war]
TID: [-1234] [] [2024-12-30 03:31:12,612]  INFO {org.wso2.carbon.webapp.mgt.TomcatGenericWebappsDeployer} - Deployed webapp: StandardEngine[Catalina].StandardHost[localhost].StandardContext[/api/identity/recovery/v0.9].File[/home/<USER>/wso2am-3.2.0/repository/deployment/server/webapps/api#identity#recovery#v0.9.war]
TID: [-1234] [] [2024-12-30 03:31:14,034]  INFO {org.wso2.carbon.webapp.mgt.TomcatGenericWebappsDeployer} - Deployed webapp: StandardEngine[Catalina].StandardHost[localhost].StandardContext[/api/identity/user/v1.0].File[/home/<USER>/wso2am-3.2.0/repository/deployment/server/webapps/api#identity#user#v1.0.war]
TID: [-1234] [] [2024-12-30 03:31:14,667]  INFO {org.wso2.carbon.identity.application.authentication.endpoint.util.TenantDataManager} - EndpointConfig.properties file loaded from ./repository/conf/identity/EndpointConfig.properties
TID: [-1234] [] [2024-12-30 03:31:14,670]  INFO {org.wso2.carbon.identity.application.authentication.endpoint.util.MutualSSLManager} - EndpointConfig.properties file loaded from ./repository/conf/identity/EndpointConfig.properties
TID: [-1234] [] [2024-12-30 03:31:14,687]  INFO {org.wso2.carbon.identity.application.authentication.endpoint.util.EndpointConfigManager} - EndpointConfig.properties file loaded from ./repository/conf/identity/EndpointConfig.properties
TID: [-1234] [] [2024-12-30 03:31:14,863]  INFO {org.wso2.carbon.webapp.mgt.TomcatGenericWebappsDeployer} - Deployed webapp: StandardEngine[Catalina].StandardHost[localhost].StandardContext[/authenticationendpoint].File[/home/<USER>/wso2am-3.2.0/repository/deployment/server/webapps/authenticationendpoint]
TID: [-1234] [] [2024-12-30 03:31:16,133]  INFO {org.wso2.carbon.webapp.mgt.TomcatGenericWebappsDeployer} - Deployed webapp: StandardEngine[Catalina].StandardHost[localhost].StandardContext[/client-registration/v0.17].File[/home/<USER>/wso2am-3.2.0/repository/deployment/server/webapps/client-registration#v0.17.war]
TID: [-1234] [] [2024-12-30 03:31:17,163]  INFO {org.wso2.carbon.webapp.mgt.TomcatGenericWebappsDeployer} - Deployed webapp: StandardEngine[Catalina].StandardHost[localhost].StandardContext[/internal/data/v1].File[/home/<USER>/wso2am-3.2.0/repository/deployment/server/webapps/internal#data#v1.war]
TID: [-1234] [] [2024-12-30 03:31:18,207]  INFO {org.wso2.carbon.webapp.mgt.TomcatGenericWebappsDeployer} - Deployed webapp: StandardEngine[Catalina].StandardHost[localhost].StandardContext[/keymanager-operations].File[/home/<USER>/wso2am-3.2.0/repository/deployment/server/webapps/keymanager-operations.war]
TID: [-1234] [] [2024-12-30 03:31:20,126]  INFO {org.wso2.carbon.webapp.mgt.TomcatGenericWebappsDeployer} - Deployed webapp: StandardEngine[Catalina].StandardHost[localhost].StandardContext[/oauth2].File[/home/<USER>/wso2am-3.2.0/repository/deployment/server/webapps/oauth2.war]
TID: [-1234] [] [2024-12-30 03:31:21,363]  INFO {org.apache.synapse.Axis2SynapseController} - Deploying the Synapse service...
TID: [-1234] [] [2024-12-30 03:31:21,368]  INFO {org.apache.synapse.Axis2SynapseController} - Deploying Proxy services...
TID: [-1234] [] [2024-12-30 03:31:21,369]  INFO {org.apache.synapse.core.axis2.ProxyService} - {proxy:WorkflowCallbackService} Building Axis service for Proxy service : WorkflowCallbackService
TID: [-1234] [] [2024-12-30 03:31:21,380]  INFO {org.apache.synapse.core.axis2.ProxyService} - {proxy:WorkflowCallbackService} Adding service WorkflowCallbackService to the Axis2 configuration
TID: [-1234] [] [2024-12-30 03:31:21,385]  INFO {org.wso2.carbon.core.deployment.DeploymentInterceptor} - Deploying Axis2 service: WorkflowCallbackService {super-tenant}
TID: [-1234] [] [2024-12-30 03:31:21,385]  INFO {org.apache.synapse.core.axis2.ProxyService} - {proxy:WorkflowCallbackService} Successfully created the Axis2 service for Proxy service : WorkflowCallbackService
TID: [-1234] [] [2024-12-30 03:31:21,386]  INFO {org.apache.synapse.Axis2SynapseController} - Deployed Proxy service : WorkflowCallbackService
TID: [-1234] [] [2024-12-30 03:31:21,386]  INFO {org.apache.synapse.Axis2SynapseController} - Deploying EventSources...
TID: [-1234] [] [2024-12-30 03:31:21,431]  INFO {org.apache.synapse.inbound.InboundEndpoint} - Initializing Inbound Endpoint: WebSocketInboundEndpoint
TID: [-1234] [] [2024-12-30 03:31:21,794]  INFO {org.wso2.carbon.inbound.endpoint.protocol.websocket.management.WebsocketEndpointManager} - Netty Listener starting on port 9099
TID: [-1234] [] [2024-12-30 03:31:21,796]  INFO {org.apache.synapse.inbound.InboundEndpoint} - Initializing Inbound Endpoint: SecureWebSocketEP
TID: [-1234] [] [2024-12-30 03:31:21,809]  INFO {org.wso2.carbon.inbound.endpoint.protocol.websocket.management.WebsocketEndpointManager} - Netty SSL Listener starting on port 8099
TID: [-1234] [] [2024-12-30 03:31:21,810]  INFO {org.apache.synapse.rest.API} - {api:_WSO2AMOpenIDConfigAPI_} Initializing API: _WSO2AMOpenIDConfigAPI_
TID: [-1234] [] [2024-12-30 03:31:21,811]  INFO {org.apache.synapse.rest.API} - {api:admin--DVCLienthong:v1.0} Initializing API: admin--DVCLienthong:v1.0
TID: [-1234] [] [2024-12-30 03:31:21,821]  INFO {org.apache.synapse.rest.API} - {api:_WSO2AMAuthenticationEPAPI_} Initializing API: _WSO2AMAuthenticationEPAPI_
TID: [-1234] [] [2024-12-30 03:31:21,821]  INFO {org.apache.synapse.rest.API} - {api:admin--LyLichTuPhap:v1.0} Initializing API: admin--LyLichTuPhap:v1.0
TID: [-1234] [] [2024-12-30 03:31:21,830]  INFO {org.apache.synapse.rest.API} - {api:admin--HoSoIGATE:v1.0.0} Initializing API: admin--HoSoIGATE:v1.0.0
TID: [-1234] [] [2024-12-30 03:31:21,830]  INFO {org.apache.synapse.rest.API} - {api:admin--MAIL:v1.0.0} Initializing API: admin--MAIL:v1.0.0
TID: [-1234] [] [2024-12-30 03:31:21,831]  INFO {org.apache.synapse.rest.API} - {api:admin--IGATE-VBDLIS:v1.0.0} Initializing API: admin--IGATE-VBDLIS:v1.0.0
TID: [-1234] [] [2024-12-30 03:31:21,831]  INFO {org.apache.synapse.rest.API} - {api:admin--LGSP-VBQPPL:v1.0.0} Initializing API: admin--LGSP-VBQPPL:v1.0.0
TID: [-1234] [] [2024-12-30 03:31:21,832]  INFO {org.apache.synapse.rest.API} - {api:admin--DanhMucDungChungHD:v1.0.0} Initializing API: admin--DanhMucDungChungHD:v1.0.0
TID: [-1234] [] [2024-12-30 03:31:21,833]  INFO {org.apache.synapse.rest.API} - {api:admin--VDBLIS:v1.0.0} Initializing API: admin--VDBLIS:v1.0.0
TID: [-1234] [] [2024-12-30 03:31:21,833]  INFO {org.apache.synapse.rest.API} - {api:admin--GPLX2:v1.0} Initializing API: admin--GPLX2:v1.0
TID: [-1234] [] [2024-12-30 03:31:21,834]  INFO {org.apache.synapse.rest.API} - {api:admin--IGATE-TKHS:v1.0.0} Initializing API: admin--IGATE-TKHS:v1.0.0
TID: [-1234] [] [2024-12-30 03:31:21,834]  INFO {org.apache.synapse.rest.API} - {api:admin--LGSP-HTTP:v1.0.0} Initializing API: admin--LGSP-HTTP:v1.0.0
TID: [-1234] [] [2024-12-30 03:31:21,834]  INFO {org.apache.synapse.rest.API} - {api:admin--GPLX:v1.0.0} Initializing API: admin--GPLX:v1.0.0
TID: [-1234] [] [2024-12-30 03:31:21,835]  INFO {org.apache.synapse.rest.API} - {api:admin--MSNS01:v1.0.0} Initializing API: admin--MSNS01:v1.0.0
TID: [-1234] [] [2024-12-30 03:31:21,835]  INFO {org.apache.synapse.rest.API} - {api:admin--PHBH:v1.0.0} Initializing API: admin--PHBH:v1.0.0
TID: [-1234] [] [2024-12-30 03:31:21,836]  INFO {org.apache.synapse.rest.API} - {api:admin--LD-TBXH:v1.0.0} Initializing API: admin--LD-TBXH:v1.0.0
TID: [-1234] [] [2024-12-30 03:31:21,836]  INFO {org.apache.synapse.rest.API} - {api:admin--DTNN:v1.0.0} Initializing API: admin--DTNN:v1.0.0
TID: [-1234] [] [2024-12-30 03:31:21,836]  INFO {org.apache.synapse.rest.API} - {api:admin--LGSP-GTVT:v1.0.0} Initializing API: admin--LGSP-GTVT:v1.0.0
TID: [-1234] [] [2024-12-30 03:31:21,837]  INFO {org.apache.synapse.rest.API} - {api:admin--LGSP-VNPOST:v1.0.0} Initializing API: admin--LGSP-VNPOST:v1.0.0
TID: [-1234] [] [2024-12-30 03:31:21,837]  INFO {org.apache.synapse.rest.API} - {api:admin--DanhMucDC:v1.0.0} Initializing API: admin--DanhMucDC:v1.0.0
TID: [-1234] [] [2024-12-30 03:31:21,838]  INFO {org.apache.synapse.rest.API} - {api:admin--DKDN01:v1.0.0} Initializing API: admin--DKDN01:v1.0.0
TID: [-1234] [] [2024-12-30 03:31:21,838]  INFO {org.apache.synapse.rest.API} - {api:admin--HNY:v1.0.0} Initializing API: admin--HNY:v1.0.0
TID: [-1234] [] [2024-12-30 03:31:21,838]  INFO {org.apache.synapse.rest.API} - {api:admin--LGSP_LLTP:v1.0} Initializing API: admin--LGSP_LLTP:v1.0
TID: [-1234] [] [2024-12-30 03:31:21,839]  INFO {org.apache.synapse.rest.API} - {api:_WSO2AMLoginContextAPI_} Initializing API: _WSO2AMLoginContextAPI_
TID: [-1234] [] [2024-12-30 03:31:21,839]  INFO {org.apache.synapse.rest.API} - {api:_WSO2AMCommonAuthAPI_} Initializing API: _WSO2AMCommonAuthAPI_
TID: [-1234] [] [2024-12-30 03:31:21,840]  INFO {org.apache.synapse.rest.API} - {api:admin--QuanLyTaiSanCong:v1.0} Initializing API: admin--QuanLyTaiSanCong:v1.0
TID: [-1234] [] [2024-12-30 03:31:21,840]  INFO {org.apache.synapse.rest.API} - {api:_WSO2AMAuthorizeAPI_} Initializing API: _WSO2AMAuthorizeAPI_
TID: [-1234] [] [2024-12-30 03:31:21,841]  INFO {org.apache.synapse.rest.API} - {api:_WSO2AMUserInfoAPI_} Initializing API: _WSO2AMUserInfoAPI_
TID: [-1234] [] [2024-12-30 03:31:21,841]  INFO {org.apache.synapse.rest.API} - {api:_OpenService_} Initializing API: _OpenService_
TID: [-1234] [] [2024-12-30 03:31:21,841]  INFO {org.apache.synapse.rest.API} - {api:_WSO2AMRevokeAPI_} Initializing API: _WSO2AMRevokeAPI_
TID: [-1234] [] [2024-12-30 03:31:21,842]  INFO {org.apache.synapse.rest.API} - {api:_WSO2AMTokenAPI_} Initializing API: _WSO2AMTokenAPI_
TID: [-1234] [] [2024-12-30 03:31:21,842]  INFO {org.apache.synapse.rest.API} - {api:_WSO2OIDCAPI_} Initializing API: _WSO2OIDCAPI_
TID: [-1234] [] [2024-12-30 03:31:21,843]  INFO {org.apache.synapse.ServerManager} - Server ready for processing...
TID: [-1234] [] [2024-12-30 03:31:22,683]  INFO {org.wso2.carbon.apimgt.jms.listener.utils.JMSConnectionFactory} - JMS ConnectionFactory : Siddhi-JMS-Consumer initialized
TID: [-1234] [] [2024-12-30 03:31:22,685]  INFO {org.wso2.carbon.apimgt.jms.listener.utils.JMSConnectionFactory} - JMS ConnectionFactory : Siddhi-JMS-Consumer initialized
TID: [-1234] [] [2024-12-30 03:31:22,708]  INFO {org.wso2.carbon.apimgt.jms.listener.utils.JMSConnectionFactory} - JMS ConnectionFactory : Siddhi-JMS-Consumer initialized
TID: [-1234] [] [2024-12-30 03:31:22,744]  INFO {org.wso2.carbon.inbound.endpoint.protocol.websocket.management.WebsocketEndpointManager} - Netty Listener already started on port 8099
TID: [-1234] [] [2024-12-30 03:31:22,745]  INFO {org.wso2.carbon.inbound.endpoint.protocol.websocket.management.WebsocketEndpointManager} - Netty Listener already started on port 9099
TID: [-1234] [] [2024-12-30 03:31:22,846]  INFO {org.wso2.carbon.andes.internal.QpidServiceComponent} - Activating Andes Message Broker Engine...
TID: [-1234] [] [2024-12-30 03:31:23,447]  INFO {qpid.message.broker.listening} - [Broker] BRK-1002 : Starting : Listening on TCP port 5672
TID: [-1234] [] [2024-12-30 03:31:23,453]  INFO {qpid.message.broker.listening} - [Broker] BRK-1002 : Starting : Listening on TCP/SSL port 8672
TID: [-1234] [] [2024-12-30 03:31:23,460]  INFO {org.wso2.carbon.andes.internal.QpidServiceComponent} - AMQP Host Address : 0.0.0.0 Port : 5672
TID: [-1234] [] [2024-12-30 03:31:23,460]  INFO {org.wso2.carbon.andes.internal.QpidServiceComponent} - Successfully connected to AMQP server on port 5672
TID: [-1234] [] [2024-12-30 03:31:23,461]  WARN {org.wso2.carbon.andes.internal.QpidServiceComponent} - MQTT Transport is disabled as per configuration.
TID: [-1234] [] [2024-12-30 03:31:23,462]  INFO {org.wso2.carbon.andes.internal.QpidServiceComponent} - WSO2 Message Broker is started.
TID: [-1234] [] [2024-12-30 03:31:23,819]  INFO {org.wso2.carbon.databridge.receiver.binary.internal.BinaryDataReceiver} - Started Binary SSL Transport on port : 9711
TID: [-1234] [] [2024-12-30 03:31:23,821]  INFO {org.wso2.carbon.databridge.receiver.binary.internal.BinaryDataReceiver} - Started Binary TCP Transport on port : 9611
TID: [-1234] [] [2024-12-30 03:31:23,928]  INFO {org.wso2.carbon.application.deployer.internal.ApplicationManager} - Deploying Carbon Application : config.car...
TID: [-1234] [] [2024-12-30 03:31:23,932] ERROR {org.wso2.carbon.application.deployer.internal.ApplicationManager} - Error occurred while deploying Carbon Application org.wso2.carbon.CarbonException: artifacts.xml file not found at : /home/<USER>/wso2am-3.2.0/tmp/carbonapps/-1234/1735504283930config.car/artifacts.xml
	at org.wso2.carbon.application.deployer.config.ApplicationConfiguration.<init>(ApplicationConfiguration.java:64)
	at org.wso2.carbon.application.deployer.internal.ApplicationManager.deployCarbonApp(ApplicationManager.java:222)
	at org.wso2.carbon.application.deployer.CappAxis2Deployer.deploy(CappAxis2Deployer.java:72)
	at org.apache.axis2.deployment.repository.util.DeploymentFileData.deploy(DeploymentFileData.java:136)
	at org.apache.axis2.deployment.DeploymentEngine.doDeploy(DeploymentEngine.java:807)
	at org.apache.axis2.deployment.repository.util.WSInfoList.update(WSInfoList.java:163)
	at org.apache.axis2.deployment.RepositoryListener.update(RepositoryListener.java:384)
	at org.apache.axis2.deployment.RepositoryListener.checkServices(RepositoryListener.java:251)
	at org.apache.axis2.deployment.DeploymentEngine.loadServices(DeploymentEngine.java:135)
	at org.wso2.carbon.core.CarbonAxisConfigurator.deployServices(CarbonAxisConfigurator.java:568)
	at org.wso2.carbon.core.internal.DeploymentServerStartupObserver.completingServerStartup(DeploymentServerStartupObserver.java:51)
	at org.wso2.carbon.core.internal.CarbonCoreServiceComponent.notifyBefore(CarbonCoreServiceComponent.java:258)
	at org.wso2.carbon.core.internal.StartupFinalizerServiceComponent.completeInitialization(StartupFinalizerServiceComponent.java:166)
	at org.wso2.carbon.core.internal.StartupFinalizerServiceComponent.serviceChanged(StartupFinalizerServiceComponent.java:323)
	at org.eclipse.osgi.internal.serviceregistry.FilteredServiceListener.serviceChanged(FilteredServiceListener.java:113)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:985)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.serviceregistry.ServiceRegistry.publishServiceEventPrivileged(ServiceRegistry.java:866)
	at org.eclipse.osgi.internal.serviceregistry.ServiceRegistry.publishServiceEvent(ServiceRegistry.java:804)
	at org.eclipse.osgi.internal.serviceregistry.ServiceRegistrationImpl.register(ServiceRegistrationImpl.java:130)
	at org.eclipse.osgi.internal.serviceregistry.ServiceRegistry.registerService(ServiceRegistry.java:228)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.registerService(BundleContextImpl.java:525)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.registerService(BundleContextImpl.java:544)
	at org.wso2.carbon.throttling.agent.internal.ThrottlingAgentServiceComponent.registerThrottlingAgent(ThrottlingAgentServiceComponent.java:118)
	at org.wso2.carbon.throttling.agent.internal.ThrottlingAgentServiceComponent.activate(ThrottlingAgentServiceComponent.java:96)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.eclipse.equinox.internal.ds.model.ServiceComponent.activate(ServiceComponent.java:260)
	at org.eclipse.equinox.internal.ds.model.ServiceComponentProp.activate(ServiceComponentProp.java:146)
	at org.eclipse.equinox.internal.ds.model.ServiceComponentProp.build(ServiceComponentProp.java:345)
	at org.eclipse.equinox.internal.ds.InstanceProcess.buildComponent(InstanceProcess.java:620)
	at org.eclipse.equinox.internal.ds.InstanceProcess.buildComponents(InstanceProcess.java:197)
	at org.eclipse.equinox.internal.ds.Resolver.getEligible(Resolver.java:343)
	at org.eclipse.equinox.internal.ds.SCRManager.serviceChanged(SCRManager.java:222)
	at org.eclipse.osgi.internal.serviceregistry.FilteredServiceListener.serviceChanged(FilteredServiceListener.java:113)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:985)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.serviceregistry.ServiceRegistry.publishServiceEventPrivileged(ServiceRegistry.java:866)
	at org.eclipse.osgi.internal.serviceregistry.ServiceRegistry.publishServiceEvent(ServiceRegistry.java:804)
	at org.eclipse.osgi.internal.serviceregistry.ServiceRegistrationImpl.register(ServiceRegistrationImpl.java:130)
	at org.eclipse.osgi.internal.serviceregistry.ServiceRegistry.registerService(ServiceRegistry.java:228)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.registerService(BundleContextImpl.java:525)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.registerService(BundleContextImpl.java:544)
	at org.wso2.carbon.core.init.CarbonServerManager.initializeCarbon(CarbonServerManager.java:529)
	at org.wso2.carbon.core.init.CarbonServerManager.removePendingItem(CarbonServerManager.java:305)
	at org.wso2.carbon.core.init.PreAxis2ConfigItemListener.bundleChanged(PreAxis2ConfigItemListener.java:118)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:973)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)

TID: [-1234] [] [2024-12-30 03:31:23,940] ERROR {org.wso2.carbon.application.deployer.CappAxis2Deployer} - Error while deploying carbon application /home/<USER>/wso2am-3.2.0/repository/deployment/server/carbonapps/config.car java.lang.NullPointerException
	at org.wso2.carbon.application.deployer.internal.ApplicationManager.revertDeployedArtifacts(ApplicationManager.java:323)
	at org.wso2.carbon.application.deployer.internal.ApplicationManager.deployCarbonApp(ApplicationManager.java:296)
	at org.wso2.carbon.application.deployer.CappAxis2Deployer.deploy(CappAxis2Deployer.java:72)
	at org.apache.axis2.deployment.repository.util.DeploymentFileData.deploy(DeploymentFileData.java:136)
	at org.apache.axis2.deployment.DeploymentEngine.doDeploy(DeploymentEngine.java:807)
	at org.apache.axis2.deployment.repository.util.WSInfoList.update(WSInfoList.java:163)
	at org.apache.axis2.deployment.RepositoryListener.update(RepositoryListener.java:384)
	at org.apache.axis2.deployment.RepositoryListener.checkServices(RepositoryListener.java:251)
	at org.apache.axis2.deployment.DeploymentEngine.loadServices(DeploymentEngine.java:135)
	at org.wso2.carbon.core.CarbonAxisConfigurator.deployServices(CarbonAxisConfigurator.java:568)
	at org.wso2.carbon.core.internal.DeploymentServerStartupObserver.completingServerStartup(DeploymentServerStartupObserver.java:51)
	at org.wso2.carbon.core.internal.CarbonCoreServiceComponent.notifyBefore(CarbonCoreServiceComponent.java:258)
	at org.wso2.carbon.core.internal.StartupFinalizerServiceComponent.completeInitialization(StartupFinalizerServiceComponent.java:166)
	at org.wso2.carbon.core.internal.StartupFinalizerServiceComponent.serviceChanged(StartupFinalizerServiceComponent.java:323)
	at org.eclipse.osgi.internal.serviceregistry.FilteredServiceListener.serviceChanged(FilteredServiceListener.java:113)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:985)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.serviceregistry.ServiceRegistry.publishServiceEventPrivileged(ServiceRegistry.java:866)
	at org.eclipse.osgi.internal.serviceregistry.ServiceRegistry.publishServiceEvent(ServiceRegistry.java:804)
	at org.eclipse.osgi.internal.serviceregistry.ServiceRegistrationImpl.register(ServiceRegistrationImpl.java:130)
	at org.eclipse.osgi.internal.serviceregistry.ServiceRegistry.registerService(ServiceRegistry.java:228)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.registerService(BundleContextImpl.java:525)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.registerService(BundleContextImpl.java:544)
	at org.wso2.carbon.throttling.agent.internal.ThrottlingAgentServiceComponent.registerThrottlingAgent(ThrottlingAgentServiceComponent.java:118)
	at org.wso2.carbon.throttling.agent.internal.ThrottlingAgentServiceComponent.activate(ThrottlingAgentServiceComponent.java:96)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.eclipse.equinox.internal.ds.model.ServiceComponent.activate(ServiceComponent.java:260)
	at org.eclipse.equinox.internal.ds.model.ServiceComponentProp.activate(ServiceComponentProp.java:146)
	at org.eclipse.equinox.internal.ds.model.ServiceComponentProp.build(ServiceComponentProp.java:345)
	at org.eclipse.equinox.internal.ds.InstanceProcess.buildComponent(InstanceProcess.java:620)
	at org.eclipse.equinox.internal.ds.InstanceProcess.buildComponents(InstanceProcess.java:197)
	at org.eclipse.equinox.internal.ds.Resolver.getEligible(Resolver.java:343)
	at org.eclipse.equinox.internal.ds.SCRManager.serviceChanged(SCRManager.java:222)
	at org.eclipse.osgi.internal.serviceregistry.FilteredServiceListener.serviceChanged(FilteredServiceListener.java:113)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:985)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.osgi.internal.serviceregistry.ServiceRegistry.publishServiceEventPrivileged(ServiceRegistry.java:866)
	at org.eclipse.osgi.internal.serviceregistry.ServiceRegistry.publishServiceEvent(ServiceRegistry.java:804)
	at org.eclipse.osgi.internal.serviceregistry.ServiceRegistrationImpl.register(ServiceRegistrationImpl.java:130)
	at org.eclipse.osgi.internal.serviceregistry.ServiceRegistry.registerService(ServiceRegistry.java:228)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.registerService(BundleContextImpl.java:525)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.registerService(BundleContextImpl.java:544)
	at org.wso2.carbon.core.init.CarbonServerManager.initializeCarbon(CarbonServerManager.java:529)
	at org.wso2.carbon.core.init.CarbonServerManager.removePendingItem(CarbonServerManager.java:305)
	at org.wso2.carbon.core.init.PreAxis2ConfigItemListener.bundleChanged(PreAxis2ConfigItemListener.java:118)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:973)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)

TID: [-1234] [] [2024-12-30 03:31:23,948]  INFO {org.wso2.carbon.databridge.receiver.thrift.internal.ThriftServerStartupObserver} -  Thrift Data Receiver is disabled.
TID: [-1234] [] [2024-12-30 03:31:23,964]  INFO {org.apache.synapse.transport.passthru.PassThroughHttpListener} - Starting Pass-through HTTP Listener...
TID: [-1234] [] [2024-12-30 03:31:24,007]  INFO {org.apache.synapse.transport.passthru.core.PassThroughListeningIOReactorManager} - Pass-through HTTP Listener started on 0.0.0.0:8280
TID: [-1234] [] [2024-12-30 03:31:24,008]  INFO {org.apache.synapse.transport.passthru.PassThroughHttpMultiSSLListener} - Starting Pass-through HTTPS Listener...
TID: [-1234] [] [2024-12-30 03:31:24,038]  INFO {org.apache.synapse.transport.passthru.core.PassThroughListeningIOReactorManager} - Pass-through HTTPS Listener started on 0.0.0.0:8243
TID: [-1234] [] [2024-12-30 03:31:24,129]  WARN {org.apache.tomcat.util.net.SSLUtilBase} - The trusted certificate with alias [thawtepremiumserverca] and DN [EMAILADDRESS=<EMAIL>, CN=Thawte Premium Server CA, OU=Certification Services Division, O=Thawte Consulting cc, L=Cape Town, ST=Western Cape, C=ZA] is not valid due to [NotAfter: Sat Jan 02 06:59:59 ICT 2021]. Certificates signed by this trusted certificate WILL be accepted
TID: [-1234] [] [2024-12-30 03:31:24,132]  WARN {org.apache.tomcat.util.net.SSLUtilBase} - The trusted certificate with alias [identrustdstx3] and DN [CN=DST Root CA X3, O=Digital Signature Trust Co.] is not valid due to [NotAfter: Thu Sep 30 21:01:15 ICT 2021]. Certificates signed by this trusted certificate WILL be accepted
TID: [-1234] [] [2024-12-30 03:31:24,133]  WARN {org.apache.tomcat.util.net.SSLUtilBase} - The trusted certificate with alias [addtrustclass1ca] and DN [CN=AddTrust Class 1 CA Root, OU=AddTrust TTP Network, O=AddTrust AB, C=SE] is not valid due to [NotAfter: Sat May 30 17:38:31 ICT 2020]. Certificates signed by this trusted certificate WILL be accepted
TID: [-1234] [] [2024-12-30 03:31:24,133]  WARN {org.apache.tomcat.util.net.SSLUtilBase} - The trusted certificate with alias [wso2carbon] and DN [CN=localhost, OU=WSO2, O=WSO2, L=Mountain View, ST=CA, C=US] is not valid due to [NotAfter: Tue Jan 25 14:30:43 ICT 2022]. Certificates signed by this trusted certificate WILL be accepted
TID: [-1234] [] [2024-12-30 03:31:24,134]  WARN {org.apache.tomcat.util.net.SSLUtilBase} - The trusted certificate with alias [mzyxmmfkogywmwi0zwnmndcxngywymm4zta3mwi2ndazzgqzngm0zgrlnjjkodfkzdriotfkmwfhmzu2zgvlng_rs256] and DN [CN=localhost, OU=WSO2, O=WSO2, L=Mountain View, ST=CA, C=US] is not valid due to [NotAfter: Tue Jan 25 14:30:43 ICT 2022]. Certificates signed by this trusted certificate WILL be accepted
TID: [-1234] [] [2024-12-30 03:31:24,134]  WARN {org.apache.tomcat.util.net.SSLUtilBase} - The trusted certificate with alias [gateway_certificate_alias] and DN [CN=localhost, OU=WSO2, O=WSO2, L=Mountain View, ST=CA, C=US] is not valid due to [NotAfter: Tue Jan 25 14:30:43 ICT 2022]. Certificates signed by this trusted certificate WILL be accepted
TID: [-1234] [] [2024-12-30 03:31:24,135]  WARN {org.apache.tomcat.util.net.SSLUtilBase} - The trusted certificate with alias [secomscrootca1] and DN [OU=Security Communication RootCA1, O=SECOM Trust.net, C=JP] is not valid due to [NotAfter: Sat Sep 30 11:20:49 ICT 2023]. Certificates signed by this trusted certificate WILL be accepted
TID: [-1234] [] [2024-12-30 03:31:24,135]  WARN {org.apache.tomcat.util.net.SSLUtilBase} - The trusted certificate with alias [globalsignr2ca] and DN [CN=GlobalSign, O=GlobalSign, OU=GlobalSign Root CA - R2] is not valid due to [NotAfter: Wed Dec 15 15:00:00 ICT 2021]. Certificates signed by this trusted certificate WILL be accepted
TID: [-1234] [] [2024-12-30 03:31:24,136]  WARN {org.apache.tomcat.util.net.SSLUtilBase} - The trusted certificate with alias [geotrustglobalca] and DN [CN=GeoTrust Global CA, O=GeoTrust Inc., C=US] is not valid due to [NotAfter: Sat May 21 11:00:00 ICT 2022]. Certificates signed by this trusted certificate WILL be accepted
TID: [-1234] [] [2024-12-30 03:31:24,136]  WARN {org.apache.tomcat.util.net.SSLUtilBase} - The trusted certificate with alias [soneraclass2ca] and DN [CN=Sonera Class2 CA, O=Sonera, C=FI] is not valid due to [NotAfter: Tue Apr 06 14:29:40 ICT 2021]. Certificates signed by this trusted certificate WILL be accepted
TID: [-1234] [] [2024-12-30 03:31:24,137]  WARN {org.apache.tomcat.util.net.SSLUtilBase} - The trusted certificate with alias [verisigntsaca] and DN [CN=Thawte Timestamping CA, OU=Thawte Certification, O=Thawte, L=Durbanville, ST=Western Cape, C=ZA] is not valid due to [NotAfter: Sat Jan 02 06:59:59 ICT 2021]. Certificates signed by this trusted certificate WILL be accepted
TID: [-1234] [] [2024-12-30 03:31:24,137]  WARN {org.apache.tomcat.util.net.SSLUtilBase} - The trusted certificate with alias [quovadisrootca] and DN [CN=QuoVadis Root Certification Authority, OU=Root Certification Authority, O=QuoVadis Limited, C=BM] is not valid due to [NotAfter: Thu Mar 18 01:33:33 ICT 2021]. Certificates signed by this trusted certificate WILL be accepted
TID: [-1234] [] [2024-12-30 03:31:24,137]  WARN {org.apache.tomcat.util.net.SSLUtilBase} - The trusted certificate with alias [addtrustqualifiedca] and DN [CN=AddTrust Qualified CA Root, OU=AddTrust TTP Network, O=AddTrust AB, C=SE] is not valid due to [NotAfter: Sat May 30 17:44:50 ICT 2020]. Certificates signed by this trusted certificate WILL be accepted
TID: [-1234] [] [2024-12-30 03:31:24,138]  WARN {org.apache.tomcat.util.net.SSLUtilBase} - The trusted certificate with alias [keynectisrootca] and DN [CN=KEYNECTIS ROOT CA, OU=ROOT, O=KEYNECTIS, C=FR] is not valid due to [NotAfter: Tue May 26 07:00:00 ICT 2020]. Certificates signed by this trusted certificate WILL be accepted
TID: [-1234] [] [2024-12-30 03:31:24,138]  WARN {org.apache.tomcat.util.net.SSLUtilBase} - The trusted certificate with alias [addtrustexternalca] and DN [CN=AddTrust External CA Root, OU=AddTrust External TTP Network, O=AddTrust AB, C=SE] is not valid due to [NotAfter: Sat May 30 17:48:38 ICT 2020]. Certificates signed by this trusted certificate WILL be accepted
TID: [-1234] [] [2024-12-30 03:31:24,139]  WARN {org.apache.tomcat.util.net.SSLUtilBase} - The trusted certificate with alias [luxtrustglobalrootca] and DN [CN=LuxTrust Global Root, O=LuxTrust s.a., C=LU] is not valid due to [NotAfter: Wed Mar 17 16:51:37 ICT 2021]. Certificates signed by this trusted certificate WILL be accepted
TID: [-1234] [] [2024-12-30 03:31:24,267]  INFO {org.wso2.carbon.ntask.core.service.impl.TaskServiceImpl} - Task service starting in STANDALONE mode...
TID: [-1234] [] [2024-12-30 03:31:24,316]  INFO {org.wso2.carbon.registry.eventing.internal.RegistryEventingServiceComponent} - Successfully Initialized Eventing on Registry
TID: [-1234] [] [2024-12-30 03:31:24,442]  INFO {org.wso2.carbon.core.init.JMXServerManager} - JMX Service URL  : service:jmx:rmi://localhost:11111/jndi/rmi://localhost:9999/jmxrmi
TID: [-1234] [] [2024-12-30 03:31:24,520]  INFO {org.wso2.carbon.apimgt.jms.listener.utils.JMSTransportHandler} - Starting jms topic consumer thread for the throttleData topic...
TID: [-1234] [] [2024-12-30 03:31:24,521]  INFO {org.wso2.carbon.apimgt.jms.listener.utils.JMSTransportHandler} - Starting jms topic consumer thread for the tokenRevocation topic...
TID: [-1234] [] [2024-12-30 03:31:24,523]  INFO {org.wso2.carbon.apimgt.jms.listener.utils.JMSTransportHandler} - Starting jms topic consumer thread for the cacheInvalidation topic...
TID: [-1234] [] [2024-12-30 03:31:24,531]  INFO {org.wso2.carbon.apimgt.jms.listener.utils.JMSTransportHandler} - Starting jms topic consumer thread for the notification topic...
TID: [-1234] [] [2024-12-30 03:31:24,533]  INFO {org.wso2.carbon.apimgt.jms.listener.utils.JMSTransportHandler} - Starting jms topic consumer thread for the perAPILog topic...
TID: [-1234] [] [2024-12-30 03:31:24,805]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-30 03:31:25,090]  INFO {org.wso2.carbon.event.processor.manager.core.internal.CarbonEventManagementService} - Starting polling event receivers
TID: [-1234] [] [2024-12-30 03:31:25,662]  INFO {org.wso2.carbon.apimgt.jms.listener.utils.JMSListener} - Connection attempt: 1 for JMS Provider for listener: Siddhi-JMS-Consumer#throttleData was successful!
TID: [-1234] [] [2024-12-30 03:31:25,662]  INFO {org.wso2.carbon.apimgt.jms.listener.utils.JMSListener} - Connection attempt: 1 for JMS Provider for listener: Siddhi-JMS-Consumer#notification was successful!
TID: [-1234] [] [2024-12-30 03:31:25,662]  INFO {org.wso2.carbon.apimgt.jms.listener.utils.JMSListener} - Connection attempt: 1 for JMS Provider for listener: Siddhi-JMS-Consumer#cacheInvalidation was successful!
TID: [-1234] [] [2024-12-30 03:31:25,663]  INFO {org.wso2.carbon.apimgt.jms.listener.utils.JMSListener} - Connection attempt: 1 for JMS Provider for listener: Siddhi-JMS-Consumer#tokenRevocation was successful!
TID: [-1234] [] [2024-12-30 03:31:25,664]  INFO {org.wso2.carbon.apimgt.jms.listener.utils.JMSListener} - Connection attempt: 1 for JMS Provider for listener: Siddhi-JMS-Consumer#perAPILog was successful!
TID: [-1234] [] [2024-12-30 03:31:25,670]  INFO {org.wso2.carbon.apimgt.jms.listener.utils.JMSTaskManager} - Task manager for Siddhi-JMS-Consumer [re-]initialized
TID: [-1234] [] [2024-12-30 03:31:25,670]  INFO {org.wso2.carbon.apimgt.jms.listener.utils.JMSTaskManager} - Task manager for Siddhi-JMS-Consumer [re-]initialized
TID: [-1234] [] [2024-12-30 03:31:25,672]  INFO {org.wso2.carbon.apimgt.jms.listener.utils.JMSTaskManager} - Task manager for Siddhi-JMS-Consumer [re-]initialized
TID: [-1234] [] [2024-12-30 03:31:25,670]  INFO {org.wso2.carbon.apimgt.jms.listener.utils.JMSTaskManager} - Task manager for Siddhi-JMS-Consumer [re-]initialized
TID: [-1234] [] [2024-12-30 03:31:25,672]  INFO {org.wso2.carbon.apimgt.jms.listener.utils.JMSTaskManager} - Task manager for Siddhi-JMS-Consumer [re-]initialized
TID: [-1234] [] [2024-12-30 03:31:25,704]  INFO {org.wso2.carbon.apimgt.jms.listener.utils.JMSTransportHandler} - Starting jms topic consumer thread for the keyManager topic...
TID: [-1234] [] [2024-12-30 03:31:25,710]  INFO {org.wso2.carbon.apimgt.jms.listener.utils.JMSTransportHandler} - Starting jms topic consumer thread for the notification topic...
TID: [-1234] [] [2024-12-30 03:31:25,743]  INFO {org.wso2.carbon.core.internal.StartupFinalizerServiceComponent} - Server           :  WSO2 API Manager-3.2.0
TID: [-1234] [] [2024-12-30 03:31:25,747]  INFO {org.wso2.carbon.core.internal.StartupFinalizerServiceComponent} - WSO2 Carbon started in 62 sec
TID: [-1234] [] [2024-12-30 03:31:25,802]  INFO {org.wso2.carbon.apimgt.jms.listener.utils.JMSListener} - Connection attempt: 1 for JMS Provider for listener: Siddhi-JMS-Consumer#keyManager was successful!
TID: [-1234] [] [2024-12-30 03:31:25,803]  INFO {org.wso2.carbon.apimgt.jms.listener.utils.JMSTaskManager} - Task manager for Siddhi-JMS-Consumer [re-]initialized
TID: [-1234] [] [2024-12-30 03:31:25,821]  INFO {org.wso2.carbon.apimgt.jms.listener.utils.JMSListener} - Connection attempt: 1 for JMS Provider for listener: Siddhi-JMS-Consumer#notification was successful!
TID: [-1234] [] [2024-12-30 03:31:25,823]  INFO {org.wso2.carbon.apimgt.jms.listener.utils.JMSTaskManager} - Task manager for Siddhi-JMS-Consumer [re-]initialized
TID: [-1234] [] [2024-12-30 03:31:25,895]  WARN {org.wso2.carbon.apimgt.jms.listener.utils.JMSUtils} - Cannot locate destination : throttleData
TID: [-1234] [] [2024-12-30 03:31:25,895]  WARN {org.wso2.carbon.apimgt.jms.listener.utils.JMSUtils} - Cannot locate destination : notification
TID: [-1234] [] [2024-12-30 03:31:25,895]  WARN {org.wso2.carbon.apimgt.jms.listener.utils.JMSUtils} - Cannot locate destination : tokenRevocation
TID: [-1234] [] [2024-12-30 03:31:25,895]  WARN {org.wso2.carbon.apimgt.jms.listener.utils.JMSUtils} - Cannot locate destination : cacheInvalidation
TID: [-1234] [] [2024-12-30 03:31:25,895]  WARN {org.wso2.carbon.apimgt.jms.listener.utils.JMSUtils} - Cannot locate destination : notification
TID: [-1234] [] [2024-12-30 03:31:25,896]  WARN {org.wso2.carbon.apimgt.jms.listener.utils.JMSUtils} - Cannot locate destination : keyManager
TID: [-1234] [] [2024-12-30 03:31:25,897]  WARN {org.wso2.carbon.apimgt.jms.listener.utils.JMSUtils} - Cannot locate destination : perAPILog
TID: [-1234] [] [2024-12-30 03:31:25,996]  WARN {org.apache.synapse.transport.http.access.AccessConfiguration} - Error loading properties from file: access-log.properties
TID: [-1234] [] [2024-12-30 03:31:26,006]  WARN {org.apache.synapse.commons.util.MiscellaneousUtil} - Error loading properties from a file at from the System defined location: access-log.properties
TID: [-1234] [] [2024-12-30 03:31:26,009]  WARN {org.apache.synapse.commons.util.MiscellaneousUtil} - Error loading properties from a file at from the System defined location: access-log.properties
TID: [-1234] [] [2024-12-30 03:31:26,143]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /QLTSC/GetAllQuocGias, HEALTH CHECK URL = /QLTSC/GetAllQuocGias
TID: [-1234] [] [2024-12-30 03:31:26,538]  INFO {org.wso2.carbon.ui.internal.CarbonUIServiceComponent} - Mgt Console URL  : https://172.16.20.31:9443/carbon/
TID: [-1234] [] [2024-12-30 03:31:26,539]  INFO {org.wso2.carbon.ui.internal.CarbonUIServiceComponent} - API Developer Portal Default Context : https://172.16.20.31:9443/devportal
TID: [-1234] [] [2024-12-30 03:31:26,539]  INFO {org.wso2.carbon.ui.internal.CarbonUIServiceComponent} - API Publisher Default Context : https://172.16.20.31:9443/publisher
TID: [-1234] [] [2024-12-30 03:31:26,672]  INFO {org.wso2.carbon.apimgt.jms.listener.utils.JMSListener} - Started to listen on destination : tokenRevocation of type topic for listener Siddhi-JMS-Consumer#tokenRevocation
TID: [-1234] [] [2024-12-30 03:31:26,682]  INFO {org.wso2.carbon.apimgt.jms.listener.utils.JMSListener} - Started to listen on destination : perAPILog of type topic for listener Siddhi-JMS-Consumer#perAPILog
TID: [-1234] [] [2024-12-30 03:31:26,682]  INFO {org.wso2.carbon.apimgt.jms.listener.utils.JMSListener} - Started to listen on destination : notification of type topic for listener Siddhi-JMS-Consumer#notification
TID: [-1234] [] [2024-12-30 03:31:26,683]  INFO {org.wso2.carbon.apimgt.jms.listener.utils.JMSListener} - Started to listen on destination : throttleData of type topic for listener Siddhi-JMS-Consumer#throttleData
TID: [-1234] [] [2024-12-30 03:31:26,685]  INFO {org.wso2.carbon.apimgt.jms.listener.utils.JMSListener} - Started to listen on destination : cacheInvalidation of type topic for listener Siddhi-JMS-Consumer#cacheInvalidation
TID: [-1234] [] [2024-12-30 03:31:26,805]  INFO {org.wso2.carbon.apimgt.jms.listener.utils.JMSListener} - Started to listen on destination : keyManager of type topic for listener Siddhi-JMS-Consumer#keyManager
TID: [-1234] [] [2024-12-30 03:31:26,827]  INFO {org.wso2.carbon.apimgt.jms.listener.utils.JMSListener} - Started to listen on destination : notification of type topic for listener Siddhi-JMS-Consumer#notification
TID: [-1234] [] [2024-12-30 03:31:28,652]  INFO {org.apache.synapse.core.axis2.TimeoutHandler} - This engine will expire all callbacks after GLOBAL_TIMEOUT: 120 seconds, irrespective of the timeout action, after the specified or optional timeout
TID: [-1234] [] [2024-12-30 03:31:28,797]  INFO {org.wso2.carbon.event.output.adapter.jms.internal.util.JMSConnectionFactory} - JMS ConnectionFactory : jmsEventPublisher initialized
TID: [-1234] [api/am/publisher] [2024-12-30 03:33:29,243] ERROR {org.wso2.carbon.apimgt.rest.api.util.impl.WebAppAuthenticatorImpl} - Invalid OAuth Token : Invalid Access Token. ACTIVE access token is not found.
TID: [-1234] [api/am/publisher] [2024-12-30 03:33:29,244] ERROR {org.wso2.carbon.apimgt.rest.api.util.impl.WebAppAuthenticatorImpl} - Provided access token is invalid
TID: [-1234] [oauth2] [2024-12-30 03:33:41,377]  INFO {org.wso2.carbon.identity.oauth.config.OAuthServerConfiguration} - An instance of org.wso2.carbon.identity.oauth2.token.OauthTokenIssuerImpl is created for Identity OAuth token generation.
TID: [-1234] [internal/data/v1] [2024-12-30 04:01:14,854]  INFO {org.wso2.carbon.event.output.adapter.jms.internal.util.JMSConnectionFactory} - JMS ConnectionFactory : tokenRevocationJMSPublisher initialized
TID: [-1234] [] [2024-12-30 04:01:25,905]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-30 04:04:43,687]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-30 04:04:43,689]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe - current suspend duration is : 30000ms - Next retry after : Mon Dec 30 04:05:13 ICT 2024
TID: [-1234] [] [2024-12-30 04:04:43,692]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-30 04:04:43,741]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:9941da83-8dac-4fac-b987-3ecbf3b75ff4; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/giaypheplaixe/login, Received through API : admin--GPLX2:v1.0, CORRELATION_ID = d299eb5b-009a-4331-a171-7cb7b4f7d32a
TID: [-1234] [] [2024-12-30 04:04:44,848]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : GPLX2--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-30 04:05:42,226]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = d299eb5b-009a-4331-a171-7cb7b4f7d32a
TID: [-1234] [] [2024-12-30 04:05:42,227]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/giaypheplaixe/login, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--GPLX2:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-7, SOCKET_TIMEOUT = 180000, CORRELATION_ID = d299eb5b-009a-4331-a171-7cb7b4f7d32a
TID: [-1234] [] [2024-12-30 04:05:46,109]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3b67d2de-1ea8-405b-b70e-cecb7aa17ebf
TID: [-1234] [] [2024-12-30 04:05:48,309]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b4522880-09ca-40dd-93d8-1d00a148255e
TID: [-1234] [] [2024-12-30 04:06:43,683]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-30 04:06:43,684]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 30 04:07:13 ICT 2024
TID: [-1234] [] [2024-12-30 04:06:43,685]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-30 04:06:43,698]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:0dbb1347-b5cb-4f30-8f9f-f4f1cbd59263; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/giaypheplaixe/login, Received through API : admin--GPLX2:v1.0, CORRELATION_ID = 318732db-b74f-4a52-a712-2d247ca173be
TID: [-1234] [] [2024-12-30 04:07:36,590]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/giaypheplaixe/login, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--GPLX2:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-11, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 318732db-b74f-4a52-a712-2d247ca173be
TID: [-1234] [] [2024-12-30 04:07:58,682]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-30 04:07:58,683]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 30 04:08:28 ICT 2024
TID: [-1234] [] [2024-12-30 04:07:58,684]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-30 04:07:58,698]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:e16a0b5f-543c-4711-92c4-35af2f614c80; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/giaypheplaixe/tracuuhoso, Received through API : admin--GPLX2:v1.0, CORRELATION_ID = bd0e4c01-6378-4b9e-8a42-c90a25273c6d
TID: [-1234] [] [2024-12-30 04:08:54,611]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/giaypheplaixe/tracuuhoso, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--GPLX2:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-9, SOCKET_TIMEOUT = 180000, CORRELATION_ID = bd0e4c01-6378-4b9e-8a42-c90a25273c6d
TID: [-1234] [] [2024-12-30 04:11:43,686]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-30 04:11:43,688]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 30 04:12:13 ICT 2024
TID: [-1234] [] [2024-12-30 04:11:43,689]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-30 04:11:43,704]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:51ac2bc3-68bc-423d-9d59-bc2785c373ef; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/giaypheplaixe/login, Received through API : admin--GPLX2:v1.0, CORRELATION_ID = 911821fd-ed4c-4f05-9264-c418785fa43b
TID: [-1234] [] [2024-12-30 04:12:38,001]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 911821fd-ed4c-4f05-9264-c418785fa43b
TID: [-1234] [] [2024-12-30 04:12:38,003]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/giaypheplaixe/login, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--GPLX2:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-14, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 911821fd-ed4c-4f05-9264-c418785fa43b
TID: [-1234] [] [2024-12-30 04:24:13,696]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-30 04:24:13,697]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 30 04:24:43 ICT 2024
TID: [-1234] [] [2024-12-30 04:24:13,698]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-30 04:24:13,712]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:8ccb6d76-bdfa-4b75-91a0-bdf8c8df65f1; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/giaypheplaixe/login, Received through API : admin--GPLX2:v1.0, CORRELATION_ID = b6356ef2-483a-41a4-923c-e5c0283e4e05
TID: [-1234] [] [2024-12-30 04:25:13,845]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = b6356ef2-483a-41a4-923c-e5c0283e4e05
TID: [-1234] [] [2024-12-30 04:25:13,847]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/giaypheplaixe/login, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--GPLX2:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-16, SOCKET_TIMEOUT = 180000, CORRELATION_ID = b6356ef2-483a-41a4-923c-e5c0283e4e05
TID: [-1234] [] [2024-12-30 04:31:28,241]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-30 04:39:43,709]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-30 04:39:43,711]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 30 04:40:13 ICT 2024
TID: [-1234] [] [2024-12-30 04:39:43,712]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-30 04:39:43,727]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:2b302126-05ee-4a3f-af7f-46f67d9267fd; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/giaypheplaixe/login, Received through API : admin--GPLX2:v1.0, CORRELATION_ID = f70e38c5-6d77-47ba-9602-80c3b08ac3ad
TID: [-1234] [] [2024-12-30 04:39:49,196]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : GPLX2--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-30 04:40:35,694]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = f70e38c5-6d77-47ba-9602-80c3b08ac3ad
TID: [-1234] [] [2024-12-30 04:40:35,696]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/giaypheplaixe/login, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--GPLX2:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-20, SOCKET_TIMEOUT = 180000, CORRELATION_ID = f70e38c5-6d77-47ba-9602-80c3b08ac3ad
TID: [-1234] [] [2024-12-30 04:42:28,708]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-30 04:42:28,709]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 30 04:42:58 ICT 2024
TID: [-1234] [] [2024-12-30 04:42:28,710]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-30 04:42:28,722]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:152e24f9-d423-4656-aa77-d3872486bea8; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/giaypheplaixe/login, Received through API : admin--GPLX2:v1.0, CORRELATION_ID = 656e4c94-3c04-42fc-8cb5-80834f2c0d2f
TID: [-1234] [] [2024-12-30 04:42:58,709]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-30 04:42:58,710]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 30 04:43:28 ICT 2024
TID: [-1234] [] [2024-12-30 04:42:58,712]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-30 04:42:58,722]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:5e6c0cbc-4e3f-4212-98d2-9cbbf1b7c3e5; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/giaypheplaixe/login, Received through API : admin--GPLX2:v1.0, CORRELATION_ID = 77772ff8-aa5d-44f3-87c7-6fdef437dc2b
TID: [-1234] [] [2024-12-30 04:43:16,404]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 656e4c94-3c04-42fc-8cb5-80834f2c0d2f
TID: [-1234] [] [2024-12-30 04:43:16,405]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/giaypheplaixe/login, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--GPLX2:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-21, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 656e4c94-3c04-42fc-8cb5-80834f2c0d2f
TID: [-1234] [] [2024-12-30 04:43:47,222]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 77772ff8-aa5d-44f3-87c7-6fdef437dc2b
TID: [-1234] [] [2024-12-30 04:43:47,224]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/giaypheplaixe/login, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--GPLX2:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-23, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 77772ff8-aa5d-44f3-87c7-6fdef437dc2b
TID: [-1234] [] [2024-12-30 04:44:13,710]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-30 04:44:13,711]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 30 04:44:43 ICT 2024
TID: [-1234] [] [2024-12-30 04:44:13,712]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-30 04:44:13,723]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:4fae6489-e615-42e5-a442-c1d6eda2150f; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/giaypheplaixe/login, Received through API : admin--GPLX2:v1.0, CORRELATION_ID = 30446bc1-abc8-4d58-a0e6-59a312bcb4ae
TID: [-1234] [] [2024-12-30 04:45:12,164]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 30446bc1-abc8-4d58-a0e6-59a312bcb4ae
TID: [-1234] [] [2024-12-30 04:45:12,165]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/giaypheplaixe/login, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--GPLX2:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-24, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 30446bc1-abc8-4d58-a0e6-59a312bcb4ae
TID: [-1234] [] [2024-12-30 04:55:43,742]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-30 04:55:43,744]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 30 04:56:13 ICT 2024
TID: [-1234] [] [2024-12-30 04:55:43,745]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-30 04:55:43,759]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:69165be4-113c-443e-9106-e6d1cffa8944; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/giaypheplaixe/login, Received through API : admin--GPLX2:v1.0, CORRELATION_ID = a48270f5-b3e3-459c-bc1c-826b30617d23
TID: [-1234] [] [2024-12-30 04:55:44,341]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : GPLX2--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-30 04:55:46,231]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : GPLX2--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-30 04:56:38,712]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = a48270f5-b3e3-459c-bc1c-826b30617d23
TID: [-1234] [] [2024-12-30 04:56:38,714]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/giaypheplaixe/login, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--GPLX2:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-26, SOCKET_TIMEOUT = 180000, CORRELATION_ID = a48270f5-b3e3-459c-bc1c-826b30617d23
TID: [-1234] [] [2024-12-30 04:56:58,744]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-30 04:56:58,745]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 30 04:57:28 ICT 2024
TID: [-1234] [] [2024-12-30 04:56:58,746]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-30 04:56:58,760]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:283c901a-5f9c-4bb8-af95-642a3ca4ff73; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/giaypheplaixe/tracuuhoso, Received through API : admin--GPLX2:v1.0, CORRELATION_ID = 161bbc61-b232-40dd-a086-df777cb47d2a
TID: [-1234] [] [2024-12-30 04:57:13,743]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-30 04:57:13,744]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 30 04:57:43 ICT 2024
TID: [-1234] [] [2024-12-30 04:57:13,745]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-30 04:57:13,760]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:3d3f1bf1-d0eb-48f4-9edc-801a7fc256d6; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/giaypheplaixe/login, Received through API : admin--GPLX2:v1.0, CORRELATION_ID = 974d2bcb-2853-4b50-9956-988b21e9ba84
TID: [-1234] [] [2024-12-30 04:57:43,744]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-30 04:57:43,746]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 30 04:58:13 ICT 2024
TID: [-1234] [] [2024-12-30 04:57:43,747]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-30 04:57:43,762]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:c8db65c4-6433-48b9-9334-5f2dd2a08231; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/giaypheplaixe/login, Received through API : admin--GPLX2:v1.0, CORRELATION_ID = 6e3671bf-864e-4588-a67d-31ae46cd0f02
TID: [-1234] [] [2024-12-30 04:57:50,610]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 161bbc61-b232-40dd-a086-df777cb47d2a
TID: [-1234] [] [2024-12-30 04:57:50,611]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/giaypheplaixe/tracuuhoso, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--GPLX2:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-27, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 161bbc61-b232-40dd-a086-df777cb47d2a
TID: [-1234] [] [2024-12-30 04:58:02,299]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 974d2bcb-2853-4b50-9956-988b21e9ba84
TID: [-1234] [] [2024-12-30 04:58:02,300]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/giaypheplaixe/login, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--GPLX2:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-28, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 974d2bcb-2853-4b50-9956-988b21e9ba84
TID: [-1234] [] [2024-12-30 04:58:35,781]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 6e3671bf-864e-4588-a67d-31ae46cd0f02
TID: [-1234] [] [2024-12-30 04:58:35,782]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/giaypheplaixe/login, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--GPLX2:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-29, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 6e3671bf-864e-4588-a67d-31ae46cd0f02
TID: [-1234] [] [2024-12-30 05:01:28,549]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-30 05:05:55,380]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b0f3557c-a64d-4533-87c2-64011939ef72
TID: [-1234] [] [2024-12-30 05:05:58,751]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-30 05:05:58,752]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 30 05:06:28 ICT 2024
TID: [-1234] [] [2024-12-30 05:05:58,753]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-30 05:05:58,766]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:edd0f7c0-7062-489e-8871-39b0279766cc; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/giaypheplaixe/login, Received through API : admin--GPLX2:v1.0, CORRELATION_ID = c04daefc-cdc6-4336-9e9c-fdaee372e28f
TID: [-1234] [] [2024-12-30 05:06:28,752]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-30 05:06:28,753]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 30 05:06:58 ICT 2024
TID: [-1234] [] [2024-12-30 05:06:28,753]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-30 05:06:28,765]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:1460827c-65c8-4a20-9ac9-ff86d5161681; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/giaypheplaixe/login, Received through API : admin--GPLX2:v1.0, CORRELATION_ID = ac475b57-e3b0-48f8-87f1-5653fe1a6585
TID: [-1234] [] [2024-12-30 05:06:51,350]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/giaypheplaixe/login, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--GPLX2:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-32, SOCKET_TIMEOUT = 180000, CORRELATION_ID = c04daefc-cdc6-4336-9e9c-fdaee372e28f
TID: [-1234] [] [2024-12-30 05:07:18,989]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/giaypheplaixe/login, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--GPLX2:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-40, SOCKET_TIMEOUT = 180000, CORRELATION_ID = ac475b57-e3b0-48f8-87f1-5653fe1a6585
TID: [-1234] [] [2024-12-30 05:13:28,759]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-30 05:13:28,761]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 30 05:13:58 ICT 2024
TID: [-1234] [] [2024-12-30 05:13:28,762]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-30 05:13:28,774]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:0b9819e1-1167-4ab4-bd40-7e3cde997a26; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/giaypheplaixe/login, Received through API : admin--GPLX2:v1.0, CORRELATION_ID = 9a94f53f-4e77-404f-8d43-290748afdb85
TID: [-1234] [] [2024-12-30 05:14:25,223]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 9a94f53f-4e77-404f-8d43-290748afdb85
TID: [-1234] [] [2024-12-30 05:14:25,224]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/giaypheplaixe/login, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--GPLX2:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-42, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 9a94f53f-4e77-404f-8d43-290748afdb85
TID: [-1234] [] [2024-12-30 05:17:58,762]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-30 05:17:58,764]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 30 05:18:28 ICT 2024
TID: [-1234] [] [2024-12-30 05:17:58,765]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-30 05:17:58,779]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:82103b6a-b8c4-46f5-a465-a77623c0755e; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/giaypheplaixe/login, Received through API : admin--GPLX2:v1.0, CORRELATION_ID = 29d4f9a2-e865-4d04-a47f-c6db3bdec940
TID: [-1234] [] [2024-12-30 05:18:52,059]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 29d4f9a2-e865-4d04-a47f-c6db3bdec940
TID: [-1234] [] [2024-12-30 05:18:52,060]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/giaypheplaixe/login, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--GPLX2:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-44, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 29d4f9a2-e865-4d04-a47f-c6db3bdec940
TID: [-1234] [] [2024-12-30 05:31:29,970]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-30 06:01:30,099]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-30 06:31:30,574]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-30 06:40:06,158]  WARN {org.wso2.carbon.ui.transports.fileupload.AbstractFileUploadExecutor} - ToolsAnyFileUpload method is not supported
TID: [-1234] [] [2024-12-30 06:51:17,082]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 34758b4b-2f73-48b3-9778-113cf24a081b
TID: [-1234] [] [2024-12-30 07:01:30,753]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-30 07:05:04,427]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8953ba74-bc25-484f-8aca-153a0a3bbfbc
TID: [-1234] [] [2024-12-30 07:05:06,109]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f7758609-4b26-4b3b-bc01-ec7b5a0d3358
TID: [-1234] [] [2024-12-30 07:05:06,893]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 48356838-7af3-4379-aa76-cbbf9610e18d
TID: [-1234] [] [2024-12-30 07:06:08,750]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8a964f8f-d216-4988-8ea3-90a8c9e6267e
TID: [-1234] [] [2024-12-30 07:31:31,374]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-30 08:01:31,662]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-30 08:12:33,514]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8e747257-a9b3-4696-be16-bae609fedd03
TID: [-1234] [] [2024-12-30 08:21:04,533] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-30 08:21:04,537]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-30 08:30:20,067]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 13b935d5-e6d4-4665-84b9-02586f571aef
TID: [-1234] [] [2024-12-30 08:31:36,767]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-30 08:34:29,431] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-30 08:34:29,433]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-30 09:01:36,976]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-30 09:30:49,772]  INFO {org.wso2.carbon.identity.application.authentication.framework.store.SessionCleanUpService} - Session Data cleanup task is running successfully for removing expired Data
TID: [-1234] [] [2024-12-30 09:31:40,163]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-30 09:55:07,863]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d3a3e4a6-a080-424f-89d1-01400403960a
TID: [-1234] [] [2024-12-30 10:01:41,190]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-30 10:05:37,725]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 79fba1a4-c7b8-423a-bbff-e5dc2bb10d7e
TID: [-1234] [] [2024-12-30 10:05:38,818]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e38df6c9-16df-4016-bfae-d542277eaaa8
TID: [-1234] [] [2024-12-30 10:20:44,003]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-30 10:20:44,006]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 30 10:21:14 ICT 2024
TID: [-1234] [] [2024-12-30 10:20:44,007]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-30 10:20:44,023]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:e5dc11ef-9c53-4214-9e01-0c0b52d60891; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/giaypheplaixe/login, Received through API : admin--GPLX2:v1.0, CORRELATION_ID = c036e7a4-51fc-444c-809a-e4132aeef433
TID: [-1234] [] [2024-12-30 10:21:38,044]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/giaypheplaixe/login, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--GPLX2:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-222, SOCKET_TIMEOUT = 180000, CORRELATION_ID = c036e7a4-51fc-444c-809a-e4132aeef433
TID: [-1234] [] [2024-12-30 10:23:01,411]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241230&denNgay=20241230&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241230&denNgay=20241230&maTthc=
TID: [-1234] [] [2024-12-30 10:23:01,453]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-12-30 10:32:23,648]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-30 10:39:14,015]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-30 10:39:14,016]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 30 10:39:44 ICT 2024
TID: [-1234] [] [2024-12-30 10:39:14,017]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-30 10:39:14,030]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:1a9647c7-80d3-4314-b741-3c368585488c; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/giaypheplaixe/login, Received through API : admin--GPLX2:v1.0, CORRELATION_ID = 7ac0e996-d512-4f56-80ba-8542d0c09df5
TID: [-1234] [] [2024-12-30 10:39:45,545]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241230&denNgay=20241230&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241230&denNgay=20241230&maTthc=
TID: [-1234] [] [2024-12-30 10:39:45,592]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-30 10:40:03,229]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/giaypheplaixe/login, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--GPLX2:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-207, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 7ac0e996-d512-4f56-80ba-8542d0c09df5
TID: [-1234] [] [2024-12-30 10:40:59,016]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-30 10:40:59,017]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 30 10:41:29 ICT 2024
TID: [-1234] [] [2024-12-30 10:40:59,017]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-30 10:40:59,032]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:2ca6813b-4f01-4c46-8ba2-5981e2ef8912; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/giaypheplaixe/tracuuhoso, Received through API : admin--GPLX2:v1.0, CORRELATION_ID = de94aa22-75b3-4621-ac6a-f6a81a96bf2f
TID: [-1234] [] [2024-12-30 10:41:56,903]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = de94aa22-75b3-4621-ac6a-f6a81a96bf2f
TID: [-1234] [] [2024-12-30 10:41:56,905]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/giaypheplaixe/tracuuhoso, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--GPLX2:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-235, SOCKET_TIMEOUT = 180000, CORRELATION_ID = de94aa22-75b3-4621-ac6a-f6a81a96bf2f
TID: [-1234] [] [2024-12-30 10:52:49,374]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241230&denNgay=20241230&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241230&denNgay=20241230&maTthc=
TID: [-1234] [] [2024-12-30 10:52:49,418]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-30 10:52:51,316]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241230&denNgay=20241230&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241230&denNgay=20241230&maTthc=
TID: [-1234] [] [2024-12-30 10:52:51,357]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-12-30 10:53:19,557]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241230&denNgay=20241230&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241230&denNgay=20241230&maTthc=
TID: [-1234] [] [2024-12-30 10:53:19,608]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-30 10:53:32,710]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241230&denNgay=20241230&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241230&denNgay=20241230&maTthc=
TID: [-1234] [] [2024-12-30 10:53:32,792]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-30 10:53:44,353]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241230&denNgay=20241230&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241230&denNgay=20241230&maTthc=
TID: [-1234] [] [2024-12-30 10:53:44,398]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-30 10:54:03,067]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241230&denNgay=20241230&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241230&denNgay=20241230&maTthc=
TID: [-1234] [] [2024-12-30 10:54:03,111]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-30 10:56:14,029]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-30 10:56:14,030]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 30 10:56:44 ICT 2024
TID: [-1234] [] [2024-12-30 10:56:14,031]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-30 10:56:14,043]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:a6b38e38-da55-44eb-b906-741586fadd39; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/giaypheplaixe/login, Received through API : admin--GPLX2:v1.0, CORRELATION_ID = 8e0edd08-a6b6-41ff-be2a-bedfc4f48696
TID: [-1234] [] [2024-12-30 10:57:08,333]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 8e0edd08-a6b6-41ff-be2a-bedfc4f48696
TID: [-1234] [] [2024-12-30 10:57:08,334]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/giaypheplaixe/login, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--GPLX2:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-252, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 8e0edd08-a6b6-41ff-be2a-bedfc4f48696
TID: [-1234] [] [2024-12-30 11:02:54,123]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-30 11:05:29,052]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-30 11:05:29,054]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 30 11:05:59 ICT 2024
TID: [-1234] [] [2024-12-30 11:05:29,055]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-30 11:05:29,076]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:068a3f86-9c55-4bcb-91bd-ec67d4b144da; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/giaypheplaixe/login, Received through API : admin--GPLX2:v1.0, CORRELATION_ID = 670b1a14-8b64-47ec-b320-9cc2aebd6326
TID: [-1234] [] [2024-12-30 11:06:25,476]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/giaypheplaixe/login, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--GPLX2:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-259, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 670b1a14-8b64-47ec-b320-9cc2aebd6326
TID: [-1234] [] [2024-12-30 11:10:41,404]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 405, ERROR_MESSAGE = Method not allowed for given API resource
TID: [-1234] [] [2024-12-30 11:14:04,931]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 405, ERROR_MESSAGE = Method not allowed for given API resource
TID: [-1234] [] [2024-12-30 11:14:25,482]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 405, ERROR_MESSAGE = Method not allowed for given API resource
TID: [-1234] [] [2024-12-30 11:23:14,066]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-30 11:23:14,067]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 30 11:23:44 ICT 2024
TID: [-1234] [] [2024-12-30 11:23:14,068]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-30 11:23:14,083]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:6c8e7696-6933-4e01-bbe2-ddd381737872; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/giaypheplaixe/login, Received through API : admin--GPLX2:v1.0, CORRELATION_ID = 9df9fffc-3be4-4862-84b3-1bbdbffe1f93
TID: [-1234] [] [2024-12-30 11:24:00,559]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 9df9fffc-3be4-4862-84b3-1bbdbffe1f93
TID: [-1234] [] [2024-12-30 11:24:00,560]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/giaypheplaixe/login, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--GPLX2:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-282, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 9df9fffc-3be4-4862-84b3-1bbdbffe1f93
TID: [-1234] [] [2024-12-30 11:24:59,067]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-30 11:24:59,068]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 30 11:25:29 ICT 2024
TID: [-1234] [] [2024-12-30 11:24:59,069]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-30 11:24:59,081]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:6544712b-556c-48a5-b02a-fd2c505fda16; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/giaypheplaixe/login, Received through API : admin--GPLX2:v1.0, CORRELATION_ID = c11a09ed-2265-4583-aa58-16e3c00a92b5
TID: [-1234] [] [2024-12-30 11:25:44,751]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = c11a09ed-2265-4583-aa58-16e3c00a92b5
TID: [-1234] [] [2024-12-30 11:25:44,752]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/giaypheplaixe/login, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--GPLX2:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-283, SOCKET_TIMEOUT = 180000, CORRELATION_ID = c11a09ed-2265-4583-aa58-16e3c00a92b5
TID: [-1234] [] [2024-12-30 11:26:31,851]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 405, ERROR_MESSAGE = Method not allowed for given API resource
TID: [-1234] [] [2024-12-30 11:28:29,070]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-30 11:28:29,071]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 30 11:28:59 ICT 2024
TID: [-1234] [] [2024-12-30 11:28:29,072]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-30 11:28:29,085]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:d87a81aa-a3f2-4e65-a61c-12ab5d7b2a41; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/giaypheplaixe/login, Received through API : admin--GPLX2:v1.0, CORRELATION_ID = 2b75b022-65b6-44b9-a6b3-51a5be3f1a5f
TID: [-1234] [] [2024-12-30 11:28:44,071]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-30 11:28:44,072]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 30 11:29:14 ICT 2024
TID: [-1234] [] [2024-12-30 11:28:44,073]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-30 11:28:44,087]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:468935da-ee37-4730-9fbd-6cb556f88572; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/giaypheplaixe/login, Received through API : admin--GPLX2:v1.0, CORRELATION_ID = 823e07bb-cc0e-40c7-bcd1-2af6c05b76b6
TID: [-1234] [] [2024-12-30 11:29:20,163]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/giaypheplaixe/login, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--GPLX2:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-285, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 2b75b022-65b6-44b9-a6b3-51a5be3f1a5f
TID: [-1234] [] [2024-12-30 11:29:39,415]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/giaypheplaixe/login, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--GPLX2:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-287, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 823e07bb-cc0e-40c7-bcd1-2af6c05b76b6
TID: [-1234] [] [2024-12-30 11:31:44,090]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-30 11:31:44,091]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 30 11:32:14 ICT 2024
TID: [-1234] [] [2024-12-30 11:31:44,091]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-30 11:31:44,105]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:f0a9abba-391d-49b3-94ee-d6a4f29f7fe7; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/giaypheplaixe/danhsachhoso, Received through API : admin--GPLX2:v1.0, CORRELATION_ID = c8a6a63c-0676-4e12-94a0-3e6b2ac14650
TID: [-1234] [] [2024-12-30 11:32:30,871]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = c8a6a63c-0676-4e12-94a0-3e6b2ac14650
TID: [-1234] [] [2024-12-30 11:32:30,872]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/giaypheplaixe/danhsachhoso, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--GPLX2:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-289, SOCKET_TIMEOUT = 180000, CORRELATION_ID = c8a6a63c-0676-4e12-94a0-3e6b2ac14650
TID: [-1234] [] [2024-12-30 11:32:56,804]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-30 11:36:44,077]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-30 11:36:44,079]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 30 11:37:14 ICT 2024
TID: [-1234] [] [2024-12-30 11:36:44,079]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-30 11:36:44,092]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:ce0f4081-dca9-488c-8a49-1fea1542e823; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/giaypheplaixe/login, Received through API : admin--GPLX2:v1.0, CORRELATION_ID = 3a4fe2a3-105c-4d00-a480-8700a0c2f236
TID: [-1234] [] [2024-12-30 11:37:40,061]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/giaypheplaixe/login, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--GPLX2:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-292, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 3a4fe2a3-105c-4d00-a480-8700a0c2f236
TID: [-1234] [] [2024-12-30 11:38:44,079]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-30 11:38:44,080]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 30 11:39:14 ICT 2024
TID: [-1234] [] [2024-12-30 11:38:44,081]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-30 11:38:44,094]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:181364c1-3402-4c9e-976e-31246008d173; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/giaypheplaixe/login, Received through API : admin--GPLX2:v1.0, CORRELATION_ID = 8c40a96b-b433-49ac-abe7-dd925a764ad2
TID: [-1234] [] [2024-12-30 11:39:34,267]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/giaypheplaixe/login, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--GPLX2:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-290, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 8c40a96b-b433-49ac-abe7-dd925a764ad2
TID: [-1234] [] [2024-12-30 11:40:14,079]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-30 11:40:14,080]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 30 11:40:44 ICT 2024
TID: [-1234] [] [2024-12-30 11:40:14,081]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-30 11:40:14,093]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:5d621044-608a-4c22-8bfd-c1265384ed42; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/giaypheplaixe/login, Received through API : admin--GPLX2:v1.0, CORRELATION_ID = 8e4a8137-c569-40f0-bcef-e60af01b6022
TID: [-1234] [] [2024-12-30 11:41:13,158]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 8e4a8137-c569-40f0-bcef-e60af01b6022
TID: [-1234] [] [2024-12-30 11:41:13,159]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/giaypheplaixe/login, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--GPLX2:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-295, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 8e4a8137-c569-40f0-bcef-e60af01b6022
TID: [-1234] [] [2024-12-30 11:46:44,078]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-30 11:46:44,079]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap - current suspend duration is : 30000ms - Next retry after : Mon Dec 30 11:47:14 ICT 2024
TID: [-1234] [] [2024-12-30 11:46:44,080]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-30 11:46:44,093]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:d62f1b30-440a-438a-8044-231050d9bbbd; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/lylichtuphap/nhanHoSo, Received through API : admin--LyLichTuPhap:v1.0, CORRELATION_ID = 0eccacee-9092-4feb-85b7-abafb5a68943
TID: [-1234] [] [2024-12-30 11:46:51,074]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-30 11:46:55,347]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-30 11:47:28,537]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 043be5e6-fd85-4f65-b465-b9f7dd847c81
TID: [-1234] [] [2024-12-30 11:47:31,139]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/lylichtuphap/nhanHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--LyLichTuPhap:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-297, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 0eccacee-9092-4feb-85b7-abafb5a68943
TID: [-1234] [] [2024-12-30 11:49:41,326]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap currently SUSPENDED will now be marked active since it processed its last message
TID: [-1234] [] [2024-12-30 11:52:41,537]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bc8d723a-921a-4141-b71e-6f3ce8900dae
TID: [-1234] [] [2024-12-30 12:00:44,094]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-30 12:00:44,095]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 30 12:01:14 ICT 2024
TID: [-1234] [] [2024-12-30 12:00:44,096]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-30 12:00:44,107]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:83f59ada-1899-4f89-91cb-f24cbaeff518; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/giaypheplaixe/login, Received through API : admin--GPLX2:v1.0, CORRELATION_ID = dfd8dd64-56ff-4f8c-b945-b440e6394e55
TID: [-1234] [] [2024-12-30 12:01:39,254]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = dfd8dd64-56ff-4f8c-b945-b440e6394e55
TID: [-1234] [] [2024-12-30 12:01:39,255]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/giaypheplaixe/login, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--GPLX2:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-304, SOCKET_TIMEOUT = 180000, CORRELATION_ID = dfd8dd64-56ff-4f8c-b945-b440e6394e55
TID: [-1234] [] [2024-12-30 12:03:36,145]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-30 12:05:32,308]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2ea8efb8-1d9b-489d-b1c6-a9e2b0251972
TID: [-1234] [] [2024-12-30 12:33:36,524]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-30 12:51:36,524]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 758d0e26-8576-4d15-a242-e699d51b9336
TID: [-1234] [] [2024-12-30 13:03:47,796]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-30 13:05:42,796]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9584456a-f27d-4767-ba21-9b62cbd691c4
TID: [-1234] [api/am/publisher] [2024-12-30 13:14:24,103] ERROR {org.wso2.carbon.apimgt.rest.api.util.impl.WebAppAuthenticatorImpl} - Invalid OAuth Token : Invalid Access Token. ACTIVE access token is not found.
TID: [-1234] [api/am/publisher] [2024-12-30 13:14:24,104] ERROR {org.wso2.carbon.apimgt.rest.api.util.impl.WebAppAuthenticatorImpl} - Provided access token is invalid
TID: [-1234] [api/am/publisher] [2024-12-30 13:14:24,105] ERROR {org.wso2.carbon.apimgt.rest.api.util.impl.WebAppAuthenticatorImpl} - Invalid OAuth Token : Invalid Access Token. ACTIVE access token is not found.
TID: [-1234] [api/am/publisher] [2024-12-30 13:14:24,111] ERROR {org.wso2.carbon.apimgt.rest.api.util.impl.WebAppAuthenticatorImpl} - Provided access token is invalid
TID: [-1234] [] [2024-12-30 13:20:29,149]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-30 13:20:29,150]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 30 13:20:59 ICT 2024
TID: [-1234] [] [2024-12-30 13:20:29,150]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-30 13:20:29,165]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:c9570f70-87df-4bca-ab02-764d6c77d1bd; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/giaypheplaixe/login, Received through API : admin--GPLX2:v1.0, CORRELATION_ID = 462f28e3-ccb4-493d-bcb1-cb62cf8e88e5
TID: [-1234] [] [2024-12-30 13:21:14,984]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 462f28e3-ccb4-493d-bcb1-cb62cf8e88e5
TID: [-1234] [] [2024-12-30 13:21:14,985]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/giaypheplaixe/login, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--GPLX2:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-343, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 462f28e3-ccb4-493d-bcb1-cb62cf8e88e5
TID: [-1234] [] [2024-12-30 13:34:57,664]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-30 13:39:14,161]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-30 13:39:14,162]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 30 13:39:44 ICT 2024
TID: [-1234] [] [2024-12-30 13:39:14,163]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-30 13:39:14,176]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:cf93a671-8fbf-481a-b31c-cff3d2cf0de5; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/giaypheplaixe/danhsachhoso, Received through API : admin--GPLX2:v1.0, CORRELATION_ID = f8ce2e9d-a7ad-419b-87de-973dee0f4d1e
TID: [-1234] [] [2024-12-30 13:40:09,906]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/giaypheplaixe/danhsachhoso, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--GPLX2:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-356, SOCKET_TIMEOUT = 180000, CORRELATION_ID = f8ce2e9d-a7ad-419b-87de-973dee0f4d1e
TID: [-1234] [] [2024-12-30 14:00:14,176]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-30 14:00:14,177]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 30 14:00:44 ICT 2024
TID: [-1234] [] [2024-12-30 14:00:14,177]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-30 14:00:14,191]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:ee02eb1d-cb66-4cef-959f-7b2e2b62a1c8; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/giaypheplaixe/danhsachhoso, Received through API : admin--GPLX2:v1.0, CORRELATION_ID = 081a6580-a36a-40be-b574-494d26ab4d45
TID: [-1234] [] [2024-12-30 14:01:06,595]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 081a6580-a36a-40be-b574-494d26ab4d45
TID: [-1234] [] [2024-12-30 14:01:06,596]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/giaypheplaixe/danhsachhoso, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--GPLX2:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-375, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 081a6580-a36a-40be-b574-494d26ab4d45
TID: [-1234] [] [2024-12-30 14:01:29,177]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-30 14:01:29,178]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 30 14:01:59 ICT 2024
TID: [-1234] [] [2024-12-30 14:01:29,179]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-30 14:01:29,191]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:991b08e2-cd73-48d9-bbc6-a827a98a7c0e; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/giaypheplaixe/login, Received through API : admin--GPLX2:v1.0, CORRELATION_ID = 0f390bdd-50bd-4204-8523-3dad00f6bcad
TID: [-1234] [] [2024-12-30 14:02:17,270]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 0f390bdd-50bd-4204-8523-3dad00f6bcad
TID: [-1234] [] [2024-12-30 14:02:17,271]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/giaypheplaixe/login, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--GPLX2:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-381, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 0f390bdd-50bd-4204-8523-3dad00f6bcad
TID: [-1234] [] [2024-12-30 14:05:30,043]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e2c75b4e-ea4c-4a3a-955e-36e99102f502
TID: [-1234] [] [2024-12-30 14:05:30,427]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 25c6a643-d221-4455-b37d-c7bab923d1f2
TID: [-1234] [] [2024-12-30 14:05:30,536]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0283ae64-f83e-47ca-abda-64632a8fc521
TID: [-1234] [] [2024-12-30 14:05:35,674]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = abe2e1ed-c6e1-4a19-80cd-54f50524e790
TID: [-1234] [] [2024-12-30 14:05:35,986]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ef8794d7-9691-41fb-bd39-98af067d07e6
TID: [-1234] [] [2024-12-30 14:05:37,705]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d803c588-62f3-4f9b-98c8-a9d119dd8c81
TID: [-1234] [] [2024-12-30 14:05:38,334]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 836a2eaf-9db0-4903-b862-d2c651b25924
TID: [-1234] [] [2024-12-30 14:05:39,917]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d98bd69e-e978-4a28-b414-284a9c6d05b8
TID: [-1234] [] [2024-12-30 14:05:40,068]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4118678e-f91e-4d3b-ba42-a7034e369f39
TID: [-1234] [] [2024-12-30 14:05:45,221]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 82cf2577-3b52-4c08-bb88-2918cc6e9869
TID: [-1234] [] [2024-12-30 14:05:51,602]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-30 14:08:55,831]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5e42a524-1b49-4b42-99ee-56899d04df5b
TID: [-1234] [] [2024-12-30 14:37:06,044]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-30 15:06:58,806]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3c4578c6-4754-40b8-a472-a4a3cb12eb50
TID: [-1234] [] [2024-12-30 15:06:59,221]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 80fcc338-6639-408b-ac01-9db5c0039df9
TID: [-1234] [] [2024-12-30 15:07:03,271]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 71c9f197-f1f2-4884-9d82-5e771ae154fd
TID: [-1234] [] [2024-12-30 15:07:04,132]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0d2e4dd0-a82b-4ddd-b195-da3561a7d3d0
TID: [-1234] [] [2024-12-30 15:07:09,822]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7f89a207-4a3a-461c-b144-a50bcfc9ffe8
TID: [-1234] [] [2024-12-30 15:07:39,850]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-30 15:12:18,665]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2f817305-8ee8-4079-9c24-f8d69fe64411
TID: [-1234] [] [2024-12-30 15:21:14,241]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f1b055dc-0403-4da5-beab-3d24dfe6478a
TID: [-1234] [] [2024-12-30 15:39:44,539]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-30 16:07:06,693]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 04e6a3a1-116c-4f89-8634-b8a6efc25eb7
TID: [-1234] [] [2024-12-30 16:07:07,410]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d0a496e8-3d5c-44a9-af40-b42a44447526
TID: [-1234] [] [2024-12-30 16:07:14,998]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e9c41e2e-1c2a-4fd3-b3e0-7fcae2036d50
TID: [-1234] [] [2024-12-30 16:07:16,566]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b5da1ba8-28a4-4bbb-bbea-05cfcf9e27b8
TID: [-1234] [] [2024-12-30 16:10:39,809]  WARN {org.wso2.carbon.ui.transports.fileupload.AbstractFileUploadExecutor} - ToolsAnyFileUpload method is not supported
TID: [-1234] [] [2024-12-30 16:11:09,055]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-30 16:13:06,741]  WARN {org.wso2.carbon.ui.transports.fileupload.AbstractFileUploadExecutor} - ToolsAnyFileUpload method is not supported
TID: [-1234] [] [2024-12-30 16:17:39,571]  WARN {org.wso2.carbon.ui.transports.fileupload.AbstractFileUploadExecutor} - ToolsAnyFileUpload method is not supported
TID: [-1234] [] [2024-12-30 16:18:48,188]  WARN {org.wso2.carbon.ui.transports.fileupload.AbstractFileUploadExecutor} - ToolsAnyFileUpload method is not supported
TID: [-1234] [] [2024-12-30 16:23:53,467]  WARN {org.wso2.carbon.ui.transports.fileupload.AbstractFileUploadExecutor} - ToolsAnyFileUpload method is not supported
TID: [-1234] [] [2024-12-30 16:41:11,391]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-30 16:57:23,261]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 530b5b1c-dff2-4e09-ae1b-0be1ac67c9da
TID: [-1234] [] [2024-12-30 17:05:52,238]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2f1d2c44-1dae-4d39-80cf-77bd100f7248
TID: [-1234] [] [2024-12-30 17:05:52,772]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = daacb00a-e5be-4290-8b80-6933bceb945b
TID: [-1234] [] [2024-12-30 17:05:58,890]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 064ed6d4-b0b9-417c-a3e9-200f8238c740
TID: [-1234] [] [2024-12-30 17:06:00,204]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bb26b751-5f17-4359-a628-7cb3575cb076
TID: [-1234] [] [2024-12-30 17:07:04,311]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ed9419fb-40bf-48c6-97b0-47ade1d2cdee
TID: [-1234] [] [2024-12-30 17:11:21,021]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-30 17:16:37,113]  WARN {org.wso2.carbon.ui.transports.fileupload.AbstractFileUploadExecutor} - ToolsAnyFileUpload method is not supported
TID: [-1234] [] [2024-12-30 17:22:36,924]  WARN {org.wso2.carbon.ui.transports.fileupload.AbstractFileUploadExecutor} - ToolsAnyFileUpload method is not supported
TID: [-1234] [] [2024-12-30 17:32:53,613]  WARN {org.wso2.carbon.ui.transports.fileupload.AbstractFileUploadExecutor} - ToolsAnyFileUpload method is not supported
TID: [-1234] [] [2024-12-30 17:43:06,846]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-30 18:01:30,211]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-30 18:01:30,213]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap - current suspend duration is : 30000ms - Next retry after : Mon Dec 30 18:02:00 ICT 2024
TID: [-1234] [] [2024-12-30 18:01:30,214]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-30 18:01:30,227]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:313fead9-91ef-43e7-aa83-db22a363ad4d; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/lylichtuphap/nhanHoSo, Received through API : admin--LyLichTuPhap:v1.0, CORRELATION_ID = 2b125a18-b6b4-4c27-a6e3-c9a444001b8b
TID: [-1234] [] [2024-12-30 18:01:39,098]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-30 18:01:57,338]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-30 18:02:28,146]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/lylichtuphap/nhanHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--LyLichTuPhap:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-544, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 2b125a18-b6b4-4c27-a6e3-c9a444001b8b
TID: [-1234] [] [2024-12-30 18:06:15,481]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7415cda7-28e5-48e6-844b-1c9b9f06901b
TID: [-1234] [] [2024-12-30 18:06:19,115]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a1a52173-2025-4df7-9c7a-48038b0d1a93
TID: [-1234] [] [2024-12-30 18:06:21,099]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 18aa51dc-1275-4d5b-8818-6d7e3144bb00
TID: [-1234] [] [2024-12-30 18:06:22,709]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5fe862f8-16d5-4deb-b2a5-32f2713fabfd
TID: [-1234] [] [2024-12-30 18:13:07,196]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-30 18:20:09,333]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241230&denNgay=20241230&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241230&denNgay=20241230&maTthc=
TID: [-1234] [] [2024-12-30 18:20:09,372]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-30 18:43:07,446]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-30 18:58:13,765]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap currently SUSPENDED will now be marked active since it processed its last message
TID: [-1234] [] [2024-12-30 19:09:22,248]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e5f7602c-6b4b-4c89-81c9-140036bac867
TID: [-1234] [] [2024-12-30 19:09:28,043]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 39ad27f4-a34b-4c5f-baee-0783bb77d71b
TID: [-1234] [] [2024-12-30 19:13:08,039]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-30 19:43:08,162]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-30 20:05:21,676]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0932c0f5-0102-4e33-bcb1-1b4d05565a19
TID: [-1234] [] [2024-12-30 20:05:25,232]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b5c9a750-62a5-4708-a0a3-cdc5df35cccb
TID: [-1234] [] [2024-12-30 20:05:28,026]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = dd1a2021-0bb6-4d62-be06-0829287fa53f
TID: [-1234] [] [2024-12-30 20:05:28,706]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7e7bdb94-be3f-4873-a15d-201b961e1b40
TID: [-1234] [] [2024-12-30 20:05:33,198]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fa19e81f-cee8-4486-84d2-4d340f4b8e17
TID: [-1234] [] [2024-12-30 20:06:35,257]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 954fa059-2e29-4510-afbb-27094cd0d6f4
TID: [-1234] [] [2024-12-30 20:11:28,703]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241230&denNgay=20241230&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241230&denNgay=20241230&maTthc=
TID: [-1234] [] [2024-12-30 20:11:28,767]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-30 20:11:28,931]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241230&denNgay=20241230&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241230&denNgay=20241230&maTthc=
TID: [-1234] [] [2024-12-30 20:11:28,973]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-30 20:11:34,405]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241230&denNgay=20241230&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241230&denNgay=20241230&maTthc=
TID: [-1234] [] [2024-12-30 20:11:34,443]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-12-30 20:12:53,870]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241230&denNgay=20241230&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241230&denNgay=20241230&maTthc=
TID: [-1234] [] [2024-12-30 20:12:53,913]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-30 20:13:26,436]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-30 20:14:21,888]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241230&denNgay=20241230&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241230&denNgay=20241230&maTthc=
TID: [-1234] [] [2024-12-30 20:14:21,929]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-30 20:15:47,555]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241230&denNgay=20241230&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241230&denNgay=20241230&maTthc=
TID: [-1234] [] [2024-12-30 20:15:47,593]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-12-30 20:43:27,730]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-30 20:44:12,437]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241230&denNgay=20241230&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241230&denNgay=20241230&maTthc=
TID: [-1234] [] [2024-12-30 20:44:12,481]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-30 20:49:50,102]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ab162cb5-6d83-4583-a3ec-a4cd88733ba2
TID: [-1234] [] [2024-12-30 21:06:05,476]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f2fdf76a-7d5e-4be0-ab0d-13d8d893da8c
TID: [-1234] [] [2024-12-30 21:06:06,262]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 670a3b14-8e31-4482-bb2d-2f79eaa08105
TID: [-1234] [] [2024-12-30 21:06:07,870]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3e6ed645-0247-4f5a-9e7c-0edfebcacbbb
TID: [-1234] [] [2024-12-30 21:06:11,471]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1c34ff3e-b9f3-482f-ae0e-b4237f9af049
TID: [-1234] [] [2024-12-30 21:06:12,350]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a0ff5b76-2988-454a-9a7a-08a500fdaf49
TID: [-1234] [] [2024-12-30 21:06:23,889]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 539bebc5-0743-4e07-b659-611038b10565
TID: [-1234] [] [2024-12-30 21:07:26,462]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8e679517-3897-446b-a1d6-e878a740ae68
TID: [-1234] [] [2024-12-30 21:13:29,388]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-30 21:19:49,685]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 89fa3202-8997-4e2a-ae1e-fa244c24dce9
TID: [-1234] [] [2024-12-30 21:43:31,219]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-30 22:06:16,087]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b6a5a727-90da-473a-a179-cd0115cf307e
TID: [-1234] [] [2024-12-30 22:06:18,709]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 090240ee-06d9-4b88-a731-34a470740165
TID: [-1234] [] [2024-12-30 22:06:22,022]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0978abef-d415-4d60-ae7b-ba5eb57039bd
TID: [-1234] [] [2024-12-30 22:06:23,212]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 753796a7-753c-47cd-b4eb-382251a331a2
TID: [-1234] [] [2024-12-30 22:06:23,541]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2707282a-bb78-4904-90f5-1e8f568f2abc
TID: [-1234] [] [2024-12-30 22:06:24,681]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 56e30688-81fb-4db2-bc82-33283ec092f8
TID: [-1234] [] [2024-12-30 22:06:34,694]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2bbd1df5-c2ec-42dd-9c41-6e17f4e9deb3
TID: [-1234] [] [2024-12-30 22:16:14,060]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-30 22:28:09,181]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241230&denNgay=20241230&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241230&denNgay=20241230&maTthc=
TID: [-1234] [] [2024-12-30 22:28:09,222]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-12-30 22:30:22,047]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241230&denNgay=20241230&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241230&denNgay=20241230&maTthc=
TID: [-1234] [] [2024-12-30 22:30:22,089]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-12-30 22:39:26,963]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a9c989a5-8ea2-4c86-8dc2-f28a49e13ec5
TID: [-1234] [] [2024-12-30 22:46:14,597]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-30 23:06:03,200]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 83b6f669-1e08-45e4-ade1-fef06234430e
TID: [-1234] [] [2024-12-30 23:06:06,375]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 30f090f6-6999-4b15-affb-ce12aacd7059
TID: [-1234] [] [2024-12-30 23:06:06,555]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c12f7b4a-afbb-4b69-9384-5f27b5909a37
TID: [-1234] [] [2024-12-30 23:06:08,727]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = da832543-624e-41f2-9757-08e55fcf8432
TID: [-1234] [] [2024-12-30 23:06:14,681]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 432115b1-bf85-4fad-bd28-c6859ce2e048
TID: [-1234] [] [2024-12-30 23:07:16,606]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8479e086-a5da-4849-acb4-be1efb56ecd0
TID: [-1234] [] [2024-12-30 23:16:15,616]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-30 23:46:15,790]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-30 23:49:46,989]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241230&denNgay=20241230&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241230&denNgay=20241230&maTthc=
TID: [-1234] [] [2024-12-30 23:49:47,032]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-30 23:50:40,416]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241230&denNgay=20241230&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241230&denNgay=20241230&maTthc=
TID: [-1234] [] [2024-12-30 23:50:40,466]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
