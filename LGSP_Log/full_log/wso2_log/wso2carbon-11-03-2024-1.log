TID: [-1234] [] [2024-11-03 00:00:21,677]  WARN {org.apache.synapse.commons.util.MiscellaneousUtil} - Error loading properties from a file at from the System defined location: access-log.properties
TID: [-1234] [] [2024-11-03 00:01:54,611]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-03 00:06:33,554]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2fd4c437-ec71-4c66-98d5-d1777d2f7b34
TID: [-1234] [] [2024-11-03 00:06:34,069]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6ad3d645-91ea-4ab7-ae7a-8d3296b822a3
TID: [-1234] [] [2024-11-03 00:06:46,412]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5983aebc-8c06-4eed-8425-6bb10dc74ca7
TID: [-1234] [] [2024-11-03 00:06:46,432]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 31525783-1936-4b75-a997-a5cb5c7ce127
TID: [-1234] [] [2024-11-03 00:06:47,135]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2b651bc1-f03a-46d3-9b86-6a2bc6c1023d
TID: [-1234] [] [2024-11-03 00:10:56,320]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 666c6c26-48c1-4c09-b14b-886599f82630
TID: [-1234] [] [2024-11-03 00:23:43,673]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 303178d6-c310-47a0-b615-129206cc118a
TID: [-1234] [] [2024-11-03 00:28:55,772]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0c777702-2296-4812-bacd-a73863bf0f09
TID: [-1234] [] [2024-11-03 00:31:56,635]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-03 00:57:28,821]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 40c56671-a98c-4379-be6a-b7dacefa2b7e
TID: [-1234] [] [2024-11-03 01:01:57,088]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-03 01:07:10,253]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 94bc5216-7c49-4170-b3b8-b7f1a49a2279
TID: [-1234] [] [2024-11-03 01:07:15,700]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 61aa46ca-7207-4a9a-ade8-911932652aad
TID: [-1234] [] [2024-11-03 01:07:19,101]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a2a916a8-15c2-417e-9e12-d8ba309b982b
TID: [-1234] [] [2024-11-03 01:07:24,074]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0dc30a68-3829-4de2-9608-986288277df0
TID: [-1234] [] [2024-11-03 01:07:24,183]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c2f2ebfc-1581-4027-8092-f096fc7449f9
TID: [-1234] [] [2024-11-03 01:12:49,651]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 02b3a0ba-2cc8-47ee-a01d-bab30b6fa9ba
TID: [-1234] [] [2024-11-03 01:19:41,848]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6e172d69-10a7-4c66-8bfc-cbeaf2053854
TID: [-1234] [] [2024-11-03 01:31:57,228]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-03 01:40:53,172]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 814138c8-a4a2-4f05-a115-6ed135a7fd68
TID: [-1234] [] [2024-11-03 02:01:57,810]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-03 02:06:53,192]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4c5aaf22-61c4-4c19-972c-ab441fe8cdf7
TID: [-1234] [] [2024-11-03 02:06:53,253]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6a2b8854-8f29-48cc-a57f-bf0f6d21e91b
TID: [-1234] [] [2024-11-03 02:06:58,264]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5a532beb-a4be-47c1-99cb-5160019e3883
TID: [-1234] [] [2024-11-03 02:07:03,348]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c955631a-7fe6-4e70-a662-f55c7b2afe24
TID: [-1234] [] [2024-11-03 02:07:08,811]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 14b76287-1169-49a9-b261-9c28590a241f
TID: [-1234] [] [2024-11-03 02:07:08,822]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d5de6023-269e-4473-ba21-699ac9d8c6e7
TID: [-1234] [] [2024-11-03 02:07:09,629]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 428bc32d-4a90-4669-ba0b-f04714027971
TID: [-1234] [] [2024-11-03 02:31:58,029]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-03 03:01:58,236]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-03 03:06:01,978]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4550d2de-4c6f-495c-ac89-ce338a11b8b7
TID: [-1234] [] [2024-11-03 03:06:08,017]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2bb07ad4-d80c-46e5-a5de-9a51dd0dd133
TID: [-1234] [] [2024-11-03 03:06:08,332]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1efbd918-2b73-4274-aa55-9bf27c6d51b7
TID: [-1234] [] [2024-11-03 03:31:58,848]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-03 03:35:07,851]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 179bcc8d-0fb2-403a-8787-df8b8a3c8dc2
TID: [-1234] [] [2024-11-03 04:01:59,049]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-03 04:06:10,897]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6e6a5a01-2ded-4ad0-9e82-fb015b8ee89c
TID: [-1234] [] [2024-11-03 04:06:16,573]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f2ef8d9c-71c0-4cb1-a4d7-004a42a1c2bb
TID: [-1234] [] [2024-11-03 04:06:27,859]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 20dd7510-0d18-4f91-9ee9-22542b8940fb
TID: [-1234] [] [2024-11-03 04:31:59,647]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-03 05:01:59,933]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-03 05:06:09,136]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6ebae71f-25e1-40f8-b273-4b535b2d7cda
TID: [-1234] [] [2024-11-03 05:06:11,449]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 98c0f2aa-e281-448e-b8b6-1e4033e554e6
TID: [-1234] [] [2024-11-03 05:06:12,005]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8a9da04e-eb38-408b-9cb1-7885a0e13060
TID: [-1234] [] [2024-11-03 05:06:14,171]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a730e5ce-d29c-4c31-b5a8-bf9ea38652a7
TID: [-1234] [] [2024-11-03 05:06:26,422]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 76601a89-922b-43f4-bdaf-d097aac52a17
TID: [-1234] [] [2024-11-03 05:06:27,100]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f763c26c-ad37-4d5a-9ac5-0432e98444b3
TID: [-1234] [] [2024-11-03 05:32:00,467]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-03 06:02:05,661]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-03 06:07:15,984]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7e06b054-8fe1-4e3b-9999-86f999ec2997
TID: [-1234] [] [2024-11-03 06:07:16,764]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a146558b-3265-49bd-b783-b5251fc3c345
TID: [-1234] [] [2024-11-03 06:07:17,542]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4e069cab-7ee2-4be5-a765-121fad116aa8
TID: [-1234] [] [2024-11-03 06:07:23,226]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 097a70e7-47cd-441e-9df8-5d047795bfcf
TID: [-1234] [] [2024-11-03 06:07:29,919]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ecbc4ee7-7c1e-489b-be62-a769eeaf07e9
TID: [-1234] [] [2024-11-03 06:32:06,055]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-03 06:35:09,353]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = aada1062-4bcb-4567-9d35-9481f3abd555
TID: [-1234] [] [2024-11-03 06:36:16,534]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241103&denNgay=20241103&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241103&denNgay=20241103&maTthc=
TID: [-1234] [] [2024-11-03 06:36:16,576]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-03 06:36:18,226]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241103&denNgay=20241103&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241103&denNgay=20241103&maTthc=
TID: [-1234] [] [2024-11-03 06:36:18,267]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-11-03 06:36:28,553]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241103&denNgay=20241103&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241103&denNgay=20241103&maTthc=
TID: [-1234] [] [2024-11-03 06:36:28,591]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-03 06:36:29,635]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241103&denNgay=20241103&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241103&denNgay=20241103&maTthc=
TID: [-1234] [] [2024-11-03 06:36:29,675]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-03 06:36:31,803]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241103&denNgay=20241103&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241103&denNgay=20241103&maTthc=
TID: [-1234] [] [2024-11-03 06:36:31,842]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-03 07:02:06,161]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-03 07:06:46,573]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9914efa4-3a29-48e6-805d-7582dc247b63
TID: [-1234] [] [2024-11-03 07:06:47,627]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c5229a74-32a0-472d-8ded-31729244cbb1
TID: [-1234] [] [2024-11-03 07:06:55,567]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 01154b32-9996-46f4-9a33-1b67b2c9cde1
TID: [-1234] [] [2024-11-03 07:07:00,091]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2ec61adf-3ed5-4386-a6eb-132763305c34
TID: [-1234] [] [2024-11-03 07:07:00,369]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7ac616f5-0483-4e2d-a10f-3a1cfbd45c6c
TID: [-1234] [] [2024-11-03 07:32:06,254]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-03 07:33:49,745]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241103&denNgay=20241103&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241103&denNgay=20241103&maTthc=
TID: [-1234] [] [2024-11-03 07:33:49,787]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-03 07:33:52,665]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241103&denNgay=20241103&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241103&denNgay=20241103&maTthc=
TID: [-1234] [] [2024-11-03 07:33:52,705]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-03 07:34:00,654]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241103&denNgay=20241103&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241103&denNgay=20241103&maTthc=
TID: [-1234] [] [2024-11-03 07:34:00,693]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-03 07:34:01,420]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241103&denNgay=20241103&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241103&denNgay=20241103&maTthc=
TID: [-1234] [] [2024-11-03 07:34:01,458]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-11-03 07:34:01,860]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241103&denNgay=20241103&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241103&denNgay=20241103&maTthc=
TID: [-1234] [] [2024-11-03 07:34:01,900]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-03 08:02:07,485]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-03 08:07:19,953]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 64131c5c-66e2-40af-9425-72c2550be9bd
TID: [-1234] [] [2024-11-03 08:07:23,611]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9736341e-969e-41b0-baa5-ca1098c90ac4
TID: [-1234] [] [2024-11-03 08:07:24,616]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f28ab198-0389-4cc5-ba04-9a1756a3d9ec
TID: [-1234] [] [2024-11-03 08:07:25,390]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5a4870a7-d779-474f-b300-fde7bff93aae
TID: [-1234] [] [2024-11-03 08:07:33,964]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = adbe853f-f6ac-4489-be7d-cf4edaed49b6
TID: [-1234] [] [2024-11-03 08:21:55,231]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3c57fe6b-f3db-43dc-8a76-ac8b9f299995
TID: [-1234] [] [2024-11-03 08:37:51,935]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-03 08:57:35,480]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241103&denNgay=20241103&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241103&denNgay=20241103&maTthc=
TID: [-1234] [] [2024-11-03 08:57:35,518]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-03 08:57:43,735]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241103&denNgay=20241103&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241103&denNgay=20241103&maTthc=
TID: [-1234] [] [2024-11-03 08:57:43,774]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-03 08:57:44,074]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241103&denNgay=20241103&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241103&denNgay=20241103&maTthc=
TID: [-1234] [] [2024-11-03 08:57:44,114]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-03 08:57:44,859]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241103&denNgay=20241103&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241103&denNgay=20241103&maTthc=
TID: [-1234] [] [2024-11-03 08:57:44,900]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-03 08:57:55,924]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241103&denNgay=20241103&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241103&denNgay=20241103&maTthc=
TID: [-1234] [] [2024-11-03 08:57:55,964]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-11-03 09:07:27,263]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2c26c83d-ccff-4d50-b26c-ca57958c592c
TID: [-1234] [] [2024-11-03 09:07:27,968]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = aca72a73-c3da-407c-831c-fe26d33c6f9f
TID: [-1234] [] [2024-11-03 09:07:29,759]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2d776898-3f5f-47b6-8cb8-5ee5d0d1e200
TID: [-1234] [] [2024-11-03 09:07:33,908]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f6f7b62f-f515-43b2-9a4c-2529e210d9b3
TID: [-1234] [] [2024-11-03 09:13:39,360]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-03 09:43:39,670]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-03 10:07:06,934]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2e7a67ca-40c3-4c59-a719-27770991a040
TID: [-1234] [] [2024-11-03 10:07:08,732]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ee09633e-56a8-47e7-b199-c31429cc6683
TID: [-1234] [] [2024-11-03 10:07:13,617]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e01dcf71-9191-4dd4-b891-abf183516130
TID: [-1234] [] [2024-11-03 10:07:17,509]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2813b886-1b30-4261-af2f-4a7d5ad567ef
TID: [-1234] [] [2024-11-03 10:13:40,010]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-03 10:16:52,651]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = dbd850f2-49f1-4dd8-bc10-56ad78b589c2
TID: [-1234] [] [2024-11-03 10:35:05,365]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 892373e9-fd3a-495d-9ce0-2218a5097a0e
TID: [-1234] [] [2024-11-03 10:43:40,370]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-03 10:50:25,233]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2841fe8d-2eee-4752-ac9d-63af7a507814
TID: [-1234] [] [2024-11-03 11:06:41,231]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 22c7c037-c39e-4955-805f-ccc69b2ba971
TID: [-1234] [] [2024-11-03 11:06:43,270]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = de782d38-b685-4686-abdd-24c4b01c1c4c
TID: [-1234] [] [2024-11-03 11:06:49,657]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5af89e8e-8610-4eac-be9b-e1563b706f12
TID: [-1234] [] [2024-11-03 11:06:54,615]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 78e1b7e6-6f8c-4d45-b260-71b7ca509c58
TID: [-1234] [] [2024-11-03 11:13:40,568]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-03 11:43:40,803]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-03 11:47:04,837]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2166873a-fd5c-4aaa-8b55-2bb4eb5278ab
TID: [-1234] [] [2024-11-03 12:07:42,266]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = aa75c405-a085-4131-a164-5b1414de5b3c
TID: [-1234] [] [2024-11-03 12:07:48,210]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d7d7ba06-aac7-439c-aa09-323ec8b47fa4
TID: [-1234] [] [2024-11-03 12:08:02,006]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 27e04e23-6bb4-4bc9-9f6c-07a9f4336bf3
TID: [-1234] [] [2024-11-03 12:08:05,189]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ed172c32-9ed7-4562-8644-82bc57856f93
TID: [-1234] [] [2024-11-03 12:13:41,251]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-03 12:43:41,509]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-03 13:07:17,678]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fe5c3e41-30df-4263-a931-9bab04272316
TID: [-1234] [] [2024-11-03 13:07:17,790]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f7eee5e6-333c-4cff-977b-3b700e619156
TID: [-1234] [] [2024-11-03 13:07:27,735]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f60af06c-d1e3-43a2-a9df-b0c10b28837a
TID: [-1234] [] [2024-11-03 13:07:28,351]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d58fa8d0-3ddd-4f25-a9a0-8471420a9450
TID: [-1234] [] [2024-11-03 13:07:29,579]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e6124bda-8a2d-4565-8697-4bae18dc5c41
TID: [-1234] [] [2024-11-03 13:07:33,678]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f5278042-bd3b-4eb2-b66b-621e095fa2fe
TID: [-1234] [] [2024-11-03 13:13:43,367]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-03 13:45:35,517]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-03 13:55:49,098]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fa91d420-284f-42cf-af20-527d52c6346f
TID: [-1234] [] [2024-11-03 14:06:54,551]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b775f468-371e-4aaa-a14e-e5159f188a88
TID: [-1234] [] [2024-11-03 14:06:59,694]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6766734e-44a3-4980-89fa-a1554ae96923
TID: [-1234] [] [2024-11-03 14:07:03,600]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 390b86cd-136b-4c2b-9fea-eb47e0dc1914
TID: [-1234] [] [2024-11-03 14:16:12,509]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-03 14:19:13,781]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 11347698-0a9b-4785-a31b-1cf8d728bb22
TID: [-1234] [] [2024-11-03 14:39:43,117]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241103&denNgay=20241103&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241103&denNgay=20241103&maTthc=
TID: [-1234] [] [2024-11-03 14:39:43,171]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-03 14:46:14,949]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-03 14:49:15,592]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7f4f27f1-986e-4e9b-9495-1a835abdeb19
TID: [-1234] [] [2024-11-03 15:01:57,544]  WARN {org.wso2.carbon.ui.transports.fileupload.AbstractFileUploadExecutor} - ToolsAnyFileUpload method is not supported
TID: [-1234] [] [2024-11-03 15:06:39,732]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e5c48e8c-c975-46ef-a7da-97398127b6eb
TID: [-1234] [] [2024-11-03 15:06:40,416]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5254b6d4-7c13-4132-a18c-684d8d4f2413
TID: [-1234] [] [2024-11-03 15:06:45,145]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 550eb86d-f222-4d05-82c6-30904a84efed
TID: [-1234] [] [2024-11-03 15:06:51,206]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b153b3bb-c64b-40ea-9175-eecfb579d4cc
TID: [-1234] [] [2024-11-03 15:06:51,874]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6b990b2a-3fef-4e00-9674-0deb35bc2b25
TID: [-1234] [] [2024-11-03 15:06:53,515]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2733459d-7173-48f5-a8ce-91b8fc144664
TID: [-1234] [] [2024-11-03 15:06:58,782]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = dee787f1-76eb-419f-801a-9fb67f93fc89
TID: [-1234] [] [2024-11-03 15:21:54,185]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-03 15:30:36,245]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 53212c5e-c655-4981-9966-3233d85bc0f7
TID: [-1234] [] [2024-11-03 15:35:07,295]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5eff24fa-7a96-484a-88e4-9ad5dd65110d
TID: [-1234] [] [2024-11-03 15:38:51,401]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5e6d5781-118f-457a-a3b2-b366a1dbcc69
TID: [-1234] [] [2024-11-03 15:46:18,890]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /robots.txt, HEALTH CHECK URL = /robots.txt
TID: [-1234] [] [2024-11-03 15:46:22,355]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sitemap.xml, HEALTH CHECK URL = /sitemap.xml
TID: [-1234] [] [2024-11-03 15:46:38,215]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0622adbe-ff18-4cbf-a03d-8fb83bcb627d
TID: [-1234] [] [2024-11-03 15:52:19,644]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-03 16:06:50,435]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c24145c4-c61a-4dd3-806d-ac5524567dcc
TID: [-1234] [] [2024-11-03 16:06:50,935]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6a4d9485-9958-4f1a-be01-d31a3e840a0c
TID: [-1234] [] [2024-11-03 16:06:53,329]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 49839325-d5aa-4a2e-ad45-63a96c3d6ba3
TID: [-1234] [] [2024-11-03 16:06:53,338]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e3576a61-a823-4fb2-a3f6-9983d1fba0f9
TID: [-1234] [] [2024-11-03 16:06:53,357]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9cc5ca98-096b-4946-af93-b88a969bcaf7
TID: [-1234] [] [2024-11-03 16:06:53,363]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9d6c70c3-9951-4f34-9475-d19ac4baaa7b
TID: [-1234] [] [2024-11-03 16:06:53,406]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 112256ab-1b40-4b85-837f-1962d6035266
TID: [-1234] [] [2024-11-03 16:06:55,794]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bb5041b1-099a-4808-a71b-0494384e79b0
TID: [-1234] [] [2024-11-03 16:07:00,729]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 86ca02e1-0543-4955-b041-f6e005567231
TID: [-1234] [] [2024-11-03 16:12:12,008]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1d5b4cd6-f261-486c-bbf0-fb20734df165
TID: [-1234] [] [2024-11-03 16:25:38,537]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-03 16:32:41,393]  INFO {org.wso2.carbon.identity.application.authentication.framework.store.SessionCleanUpService} - Session Data cleanup task is running successfully for removing expired Data
TID: [-1234] [] [2024-11-03 16:35:08,874]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cee50133-b167-447e-8e6d-27172374e1d0
TID: [-1234] [] [2024-11-03 16:35:09,444]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8fe3805a-c786-4259-a642-450683f97297
TID: [-1234] [] [2024-11-03 16:43:14,558]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3041fa65-a740-417d-8fad-35d028ea95ee
TID: [-1234] [] [2024-11-03 16:57:38,190]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-03 17:05:58,449]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 98587e9a-4b2f-403e-b6ab-7bddeec00d3d
TID: [-1234] [] [2024-11-03 17:06:01,239]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3f9fecfa-bc21-4f09-813d-f7baff1333cf
TID: [-1234] [] [2024-11-03 17:06:01,345]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f7dc0a2d-7ac0-4021-b4c3-8eb88d15be79
TID: [-1234] [] [2024-11-03 17:06:03,305]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cb18621b-24be-43c3-9176-5a1693f64b41
TID: [-1234] [] [2024-11-03 17:06:08,112]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 38a36ffa-2ca2-44f0-8cf0-6c124a67ed97
TID: [-1234] [] [2024-11-03 17:29:24,708]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-03 17:42:31,711]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sitemap.xml, HEALTH CHECK URL = /sitemap.xml
TID: [-1234] [] [2024-11-03 17:59:25,000]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-03 18:07:00,541]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7987b75b-1be5-4422-9653-dae8eb8fb6e3
TID: [-1234] [] [2024-11-03 18:07:09,364]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7d6ae448-f7ca-42fc-8353-015dd146f0b4
TID: [-1234] [] [2024-11-03 18:07:09,365]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7e1deff6-daa5-41e9-b35e-457a83e91975
TID: [-1234] [] [2024-11-03 18:07:09,369]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fa54789f-96f6-494f-aaba-c14a10c7357b
TID: [-1234] [] [2024-11-03 18:07:09,604]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6af869a7-7949-4f23-8bc3-e8a5875dc446
TID: [-1234] [] [2024-11-03 18:07:11,290]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = df5d8bba-7b0c-48de-b6b4-334e2520e0b9
TID: [-1234] [] [2024-11-03 18:07:16,176]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 57e7f9a8-1d2a-449f-8f99-05cd481dfb1b
TID: [-1234] [] [2024-11-03 18:29:25,339]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-03 18:59:25,485]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-03 19:06:10,350]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 11cfff03-0f9b-4059-a88b-a892052f534e
TID: [-1234] [] [2024-11-03 19:06:10,957]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 13a7fdab-44a7-4884-a24a-524e4037b49e
TID: [-1234] [] [2024-11-03 19:06:19,501]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4a5393fb-65d7-495e-9300-dbfcc2319493
TID: [-1234] [] [2024-11-03 19:06:19,580]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e0ad86e5-4cb9-4c3f-b5c7-1011795e821f
TID: [-1234] [] [2024-11-03 19:06:19,641]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ea72d073-6795-4cb6-a011-1158cd2f8866
TID: [-1234] [] [2024-11-03 19:06:22,683]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e28b022e-f6e4-4e99-9163-af300938a631
TID: [-1234] [] [2024-11-03 19:06:26,449]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9a72c6b6-6fda-45d9-af1b-12207d2814d5
TID: [-1234] [] [2024-11-03 19:29:25,819]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-03 19:59:26,183]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-03 20:06:38,565]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = dee643e1-8050-4999-a8c1-1f323fc31823
TID: [-1234] [] [2024-11-03 20:06:47,372]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d470d193-eee0-44d5-8255-fc8a2c4aad18
TID: [-1234] [] [2024-11-03 20:06:48,096]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0815d01e-2f51-497f-820e-5ad9295954ba
TID: [-1234] [] [2024-11-03 20:06:48,586]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cbee46b4-f170-491a-ad4d-2087ebde9566
TID: [-1234] [] [2024-11-03 20:06:51,450]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 66f3ae63-8c41-4c80-a52c-f4e8a8f84c03
TID: [-1234] [] [2024-11-03 20:06:56,074]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 823fbd39-6f20-4e0f-834b-6e0d219ce664
TID: [-1234] [] [2024-11-03 20:24:06,091]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241103&denNgay=20241103&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241103&denNgay=20241103&maTthc=
TID: [-1234] [] [2024-11-03 20:24:06,130]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-11-03 20:29:26,599]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-03 20:30:35,988]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241103&denNgay=20241103&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241103&denNgay=20241103&maTthc=
TID: [-1234] [] [2024-11-03 20:30:36,032]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-03 20:42:16,644]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a70f58fe-9879-4b90-8826-af23fda156cb
TID: [-1234] [] [2024-11-03 20:53:17,760]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b8a30824-3b77-4d2b-8705-9d4ae527eb2a
TID: [-1234] [] [2024-11-03 20:59:26,773]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-03 21:06:53,568]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c47226a8-b02e-440b-8651-daeccec9858e
TID: [-1234] [] [2024-11-03 21:06:59,836]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = caadb851-66ea-4be0-b9f1-50eae1356854
TID: [-1234] [] [2024-11-03 21:10:04,786]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 09bdf403-fba4-4e73-91a8-a443b103c63e
TID: [-1234] [] [2024-11-03 21:29:27,088]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-03 21:31:35,271]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fc7aa47b-6c9d-4a19-beee-9d699ddba29a
TID: [-1234] [] [2024-11-03 21:35:07,659]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 175eb777-7dc1-4643-aaf8-338387227202
TID: [-1234] [] [2024-11-03 21:44:22,940]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2c4d9926-ea82-427b-95ea-c283d08295a9
TID: [-1234] [] [2024-11-03 21:59:28,356]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-03 22:06:16,191]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1165791b-2f19-45ba-acec-3627aef027f2
TID: [-1234] [] [2024-11-03 22:06:30,186]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ab5e9e81-bd48-4877-91d3-ac33d8fd013a
TID: [-1234] [] [2024-11-03 22:06:31,360]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9e013221-d417-4aa5-bcd8-241d1d45194f
TID: [-1234] [] [2024-11-03 22:06:33,936]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9393a69f-6f11-46ef-bcdc-bb2f15efbb20
TID: [-1234] [] [2024-11-03 22:09:18,799]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1d0b8745-4cdc-4a2d-ae39-2d1a31e9eae2
TID: [-1234] [] [2024-11-03 22:22:59,210]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f8680a29-f37e-4130-af1e-a837e694c96e
TID: [-1234] [] [2024-11-03 22:28:36,555]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241103&denNgay=20241103&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241103&denNgay=20241103&maTthc=
TID: [-1234] [] [2024-11-03 22:28:36,594]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-11-03 22:31:45,943]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-03 22:37:31,460]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ecbff097-cbac-439f-a099-80c898f06c4d
TID: [-1234] [] [2024-11-03 23:01:46,715]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-03 23:06:56,378]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d0e8ab23-63c3-4915-bcad-a8ac5b1a1130
TID: [-1234] [] [2024-11-03 23:07:01,844]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f52e8584-7670-4cce-87ee-c15680b67803
TID: [-1234] [] [2024-11-03 23:07:02,794]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6c15cc4b-fedc-474f-8e0f-594dbf24eec8
TID: [-1234] [] [2024-11-03 23:07:16,803]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d5133eae-a10a-43d0-ad82-14ac6a76573a
TID: [-1234] [] [2024-11-03 23:07:17,036]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = eda9df7a-43fd-44de-b28d-894521dd2433
TID: [-1234] [] [2024-11-03 23:07:18,934]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b782d3c5-b35d-4e0d-96bf-64a1075cca95
TID: [-1234] [] [2024-11-03 23:07:21,172]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4732d1e3-92a0-4010-ac37-a66adc4171cc
TID: [-1234] [] [2024-11-03 23:31:48,349]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-03 23:42:10,916]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 737b6f56-2007-4002-8d1e-691bfd0dfd18
TID: [-1234] [] [2024-11-03 23:53:13,432]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9f94e8b4-b60e-4261-97eb-64ef9b953e6a
