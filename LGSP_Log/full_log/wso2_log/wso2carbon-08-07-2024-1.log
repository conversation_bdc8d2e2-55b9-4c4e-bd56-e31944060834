TID: [-1234] [] [2024-08-07 00:00:01,640]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-07 00:00:23,384]  WARN {org.apache.synapse.commons.util.MiscellaneousUtil} - Error loading properties from a file at from the System defined location: access-log.properties
TID: [-1234] [] [2024-08-07 00:11:21,861]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 7a1d4e17-67e4-4888-8765-acc4c2a9422b
TID: [-1234] [] [2024-08-07 00:11:21,863]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-798, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 7a1d4e17-67e4-4888-8765-acc4c2a9422b
TID: [-1234] [] [2024-08-07 00:11:21,865]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 00:17:07,913]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = efb307f1-a280-4244-bb94-3df769b23018
TID: [-1234] [] [2024-08-07 00:17:07,915]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-794, SOCKET_TIMEOUT = 180000, CORRELATION_ID = efb307f1-a280-4244-bb94-3df769b23018
TID: [-1234] [] [2024-08-07 00:17:07,917]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 00:20:42,508]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-808, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 96b7f50c-a6bd-4919-82e3-d4b6ab6a5abf
TID: [-1234] [] [2024-08-07 00:20:42,511]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 00:20:44,025]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 2b41f800-06f8-49da-bc21-780a191c5353
TID: [-1234] [] [2024-08-07 00:20:44,026]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-810, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 2b41f800-06f8-49da-bc21-780a191c5353
TID: [-1234] [] [2024-08-07 00:20:44,027]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 00:20:45,317]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-811, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 935d89a2-9d89-45af-9416-6f75eb04eff6
TID: [-1234] [] [2024-08-07 00:20:45,318]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 00:20:45,321]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 00:20:45,322]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:39700, CORRELATION_ID = 935d89a2-9d89-45af-9416-6f75eb04eff6, CONNECTION = http-incoming-34493
TID: [-1234] [] [2024-08-07 00:20:45,363]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-34493, CORRELATION_ID = 935d89a2-9d89-45af-9416-6f75eb04eff6
TID: [-1234] [] [2024-08-07 00:20:45,629]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = db4cee34-14fc-44fd-9ff4-926b9eec8973
TID: [-1234] [] [2024-08-07 00:24:02,337]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-827, SOCKET_TIMEOUT = 180000, CORRELATION_ID = aa564aee-9901-4437-868d-1bd0ac755715
TID: [-1234] [] [2024-08-07 00:24:02,338]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 00:28:30,241]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4d89f257-4581-4875-9811-6ee47f06509a
TID: [-1234] [] [2024-08-07 00:28:30,561]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 677d2a4f-1f1a-4d61-b978-b6f386639992
TID: [-1234] [] [2024-08-07 00:44:29,309]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-07 01:10:12,277]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 01:10:12,279]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:50836, CORRELATION_ID = 1e2de2f3-b5f8-456b-bc04-f28d233e2519, CONNECTION = http-incoming-36660
TID: [-1234] [] [2024-08-07 01:10:12,359]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 1e2de2f3-b5f8-456b-bc04-f28d233e2519
TID: [-1234] [] [2024-08-07 01:10:12,360]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-849, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 1e2de2f3-b5f8-456b-bc04-f28d233e2519
TID: [-1234] [] [2024-08-07 01:10:12,363]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 01:10:12,380]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-36660, CORRELATION_ID = 1e2de2f3-b5f8-456b-bc04-f28d233e2519
TID: [-1234] [] [2024-08-07 01:14:29,562]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-07 01:15:55,553]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 01:15:55,554]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:38770, CORRELATION_ID = 97580e84-55c9-4a25-bb97-41f9c0e59aba, CONNECTION = http-incoming-37342
TID: [-1234] [] [2024-08-07 01:15:55,872]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 97580e84-55c9-4a25-bb97-41f9c0e59aba
TID: [-1234] [] [2024-08-07 01:15:55,873]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-857, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 97580e84-55c9-4a25-bb97-41f9c0e59aba
TID: [-1234] [] [2024-08-07 01:15:55,874]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 01:15:55,892]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-37342, CORRELATION_ID = 97580e84-55c9-4a25-bb97-41f9c0e59aba
TID: [-1234] [] [2024-08-07 01:19:39,205]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 01:19:39,207]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:59334, CORRELATION_ID = d52d98c3-e79c-4c96-b2c9-9a7698bb89c0, CONNECTION = http-incoming-37509
TID: [-1234] [] [2024-08-07 01:19:39,276]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-868, SOCKET_TIMEOUT = 180000, CORRELATION_ID = d52d98c3-e79c-4c96-b2c9-9a7698bb89c0
TID: [-1234] [] [2024-08-07 01:19:39,278]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 01:19:39,296]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-37509, CORRELATION_ID = d52d98c3-e79c-4c96-b2c9-9a7698bb89c0
TID: [-1234] [] [2024-08-07 01:19:40,585]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 3e000e50-fb4c-4eec-9b9b-3854b17e12df
TID: [-1234] [] [2024-08-07 01:19:40,586]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-869, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 3e000e50-fb4c-4eec-9b9b-3854b17e12df
TID: [-1234] [] [2024-08-07 01:19:40,587]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 01:19:41,494]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 01:19:41,495]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:59772, CORRELATION_ID = 7c6d697d-a314-4736-aaa9-554b79657222, CONNECTION = http-incoming-37551
TID: [-1234] [] [2024-08-07 01:19:41,508]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-873, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 7c6d697d-a314-4736-aaa9-554b79657222
TID: [-1234] [] [2024-08-07 01:19:41,510]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 01:19:41,528]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-37551, CORRELATION_ID = 7c6d697d-a314-4736-aaa9-554b79657222
TID: [-1234] [] [2024-08-07 01:19:41,843]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1466b718-0f25-4367-9e96-67a79136ce7c
TID: [-1234] [] [2024-08-07 01:23:02,189]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 8c45a290-2c45-4570-a265-ee1067e60eb0
TID: [-1234] [] [2024-08-07 01:23:02,190]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-877, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 8c45a290-2c45-4570-a265-ee1067e60eb0
TID: [-1234] [] [2024-08-07 01:23:02,191]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 01:27:19,570]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d9b2d14b-6c3d-447e-b462-eefef58f5ba9
TID: [-1234] [] [2024-08-07 01:44:30,162]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-07 02:11:29,008]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-913, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 3268ad08-284c-490e-ba6c-9a3e321bd1f6
TID: [-1234] [] [2024-08-07 02:11:29,011]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 02:14:30,300]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-07 02:16:57,874]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-923, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 6356f77b-f010-44f9-a0b8-957479bd3f5b
TID: [-1234] [] [2024-08-07 02:16:57,876]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 02:20:33,516]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-934, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 6db6427d-12bd-432c-be13-f3c5ce77a84f
TID: [-1234] [] [2024-08-07 02:20:33,519]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 02:20:35,130]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 02:20:35,131]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:54554, CORRELATION_ID = 45ea5d21-bdfa-420e-a1d3-1c6a501a5d83, CONNECTION = http-incoming-40603
TID: [-1234] [] [2024-08-07 02:20:35,162]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-938, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 371074ff-bb83-471c-9fb3-9f2d79b10a57
TID: [-1234] [] [2024-08-07 02:20:35,163]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 02:20:35,166]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-936, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 45ea5d21-bdfa-420e-a1d3-1c6a501a5d83
TID: [-1234] [] [2024-08-07 02:20:35,167]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 02:20:35,194]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-40603, CORRELATION_ID = 45ea5d21-bdfa-420e-a1d3-1c6a501a5d83
TID: [-1234] [] [2024-08-07 02:20:35,472]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1683414e-f5b8-41bb-a825-ae96f1d479b6
TID: [-1234] [] [2024-08-07 02:24:01,929]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 7500b0db-d995-4eea-ba28-fffdec13df2b
TID: [-1234] [] [2024-08-07 02:24:01,931]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-954, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 7500b0db-d995-4eea-ba28-fffdec13df2b
TID: [-1234] [] [2024-08-07 02:24:01,932]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 02:28:30,320]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fed94e3f-602b-417d-8235-baca3a4faa53
TID: [-1234] [] [2024-08-07 02:28:30,321]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f38f5f24-9d8c-473a-ae47-684e5bcc3bab
TID: [-1234] [] [2024-08-07 02:28:30,321]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 585fc300-d6c7-4471-aff7-a69fb1ecda9d
TID: [-1234] [] [2024-08-07 02:28:32,310]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c0df7a5b-e733-49b0-97d1-0b42676721a3
TID: [-1234] [] [2024-08-07 02:28:32,373]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6442b958-bf20-4c0d-bb66-0215822b8bdb
TID: [-1234] [] [2024-08-07 02:28:32,379]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8d63cfdb-1fc9-4ea9-b469-ccf0a9f70b5e
TID: [-1234] [] [2024-08-07 02:44:31,051]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-07 03:00:39,953]  WARN {org.wso2.carbon.ui.transports.fileupload.AbstractFileUploadExecutor} - ToolsAnyFileUpload method is not supported
TID: [-1234] [] [2024-08-07 03:09:43,861]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 03:09:43,863]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:58730, CORRELATION_ID = e69ee048-07a9-4cb1-8ee1-5cb5bda0cdaa, CONNECTION = http-incoming-42746
TID: [-1234] [] [2024-08-07 03:09:43,936]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-976, SOCKET_TIMEOUT = 180000, CORRELATION_ID = e69ee048-07a9-4cb1-8ee1-5cb5bda0cdaa
TID: [-1234] [] [2024-08-07 03:09:43,939]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 03:09:43,957]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-42746, CORRELATION_ID = e69ee048-07a9-4cb1-8ee1-5cb5bda0cdaa
TID: [-1234] [] [2024-08-07 03:14:31,209]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-07 03:15:05,592]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 50b025e1-e114-4f10-a69a-a0e0ef795a76
TID: [-1234] [] [2024-08-07 03:15:05,593]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-984, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 50b025e1-e114-4f10-a69a-a0e0ef795a76
TID: [-1234] [] [2024-08-07 03:15:05,595]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 03:18:39,729]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-995, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 8f4be0de-5f90-4f6f-9250-50b13b0ce268
TID: [-1234] [] [2024-08-07 03:18:39,731]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 03:18:41,042]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 03:18:41,043]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:54244, CORRELATION_ID = e194506b-18f5-4ca7-97f3-a297bd912fa5, CONNECTION = http-incoming-43624
TID: [-1234] [] [2024-08-07 03:18:41,079]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = e194506b-18f5-4ca7-97f3-a297bd912fa5
TID: [-1234] [] [2024-08-07 03:18:41,080]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-992, SOCKET_TIMEOUT = 180000, CORRELATION_ID = e194506b-18f5-4ca7-97f3-a297bd912fa5
TID: [-1234] [] [2024-08-07 03:18:41,081]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 03:18:41,098]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-43624, CORRELATION_ID = e194506b-18f5-4ca7-97f3-a297bd912fa5
TID: [-1234] [] [2024-08-07 03:18:42,175]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6638636e-515e-4851-a36f-84eab10b3e2f
TID: [-1234] [] [2024-08-07 03:18:44,613]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 03:18:44,614]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:54360, CORRELATION_ID = 5609d67d-ae75-4d99-b796-3a205ef85e4a, CONNECTION = http-incoming-43638
TID: [-1234] [] [2024-08-07 03:18:44,615]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1001, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 5609d67d-ae75-4d99-b796-3a205ef85e4a
TID: [-1234] [] [2024-08-07 03:18:44,616]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 03:18:44,633]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-43638, CORRELATION_ID = 5609d67d-ae75-4d99-b796-3a205ef85e4a
TID: [-1234] [] [2024-08-07 03:22:12,745]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1018, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 6e324d28-a63e-4709-a981-dae407807e33
TID: [-1234] [] [2024-08-07 03:22:12,747]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 03:28:53,018]  WARN {org.wso2.carbon.ui.transports.fileupload.AbstractFileUploadExecutor} - ToolsAnyFileUpload method is not supported
TID: [-1234] [] [2024-08-07 03:44:31,489]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-07 04:11:13,717]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1051, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 58b021c7-f55d-4446-96c7-68f31c8bdaa7
TID: [-1234] [] [2024-08-07 04:11:13,720]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 04:14:31,818]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-07 04:16:51,865]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1057, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 346e0dbe-68e9-43c8-9924-5452b6941d05
TID: [-1234] [] [2024-08-07 04:16:51,867]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 04:20:25,240]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1067, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 0500f320-b851-4d6d-8882-5aa3d89b8f6c
TID: [-1234] [] [2024-08-07 04:20:25,243]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 04:20:27,767]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 04:20:27,768]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:47000, CORRELATION_ID = 4d20bf9f-a17f-4473-a0a3-67a01cb891b3, CONNECTION = http-incoming-46659
TID: [-1234] [] [2024-08-07 04:20:27,787]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1072, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 4d20bf9f-a17f-4473-a0a3-67a01cb891b3
TID: [-1234] [] [2024-08-07 04:20:27,789]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 04:20:27,806]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-46659, CORRELATION_ID = 4d20bf9f-a17f-4473-a0a3-67a01cb891b3
TID: [-1234] [] [2024-08-07 04:20:28,573]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8810bec6-1340-4be0-a2d6-02f2714b303d
TID: [-1234] [] [2024-08-07 04:20:28,711]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 04:20:28,712]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:47072, CORRELATION_ID = 53cd6f6b-0377-42be-85ab-094f618c4e98, CONNECTION = http-incoming-46670
TID: [-1234] [] [2024-08-07 04:20:28,712]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 53cd6f6b-0377-42be-85ab-094f618c4e98
TID: [-1234] [] [2024-08-07 04:20:28,713]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1062, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 53cd6f6b-0377-42be-85ab-094f618c4e98
TID: [-1234] [] [2024-08-07 04:20:28,714]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 04:20:28,731]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-46670, CORRELATION_ID = 53cd6f6b-0377-42be-85ab-094f618c4e98
TID: [-1234] [] [2024-08-07 04:20:29,526]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 682a9466-cb05-4039-80a0-0f185999149a
TID: [-1234] [] [2024-08-07 04:23:54,623]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 90d245e6-b23a-4bef-8343-2e540c4b19cd
TID: [-1234] [] [2024-08-07 04:23:54,625]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1077, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 90d245e6-b23a-4bef-8343-2e540c4b19cd
TID: [-1234] [] [2024-08-07 04:23:54,627]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 04:28:18,955]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cc6e33df-45e3-4f6a-9e01-e62489d4bdec
TID: [-1234] [] [2024-08-07 04:28:18,978]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ca0ce71b-b33d-457f-ae5a-641b50e9d14c
TID: [-1234] [] [2024-08-07 04:28:21,428]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f6f158eb-8244-427e-8c97-2191ba004da3
TID: [-1234] [] [2024-08-07 04:28:21,485]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = aca0b0cb-0156-425c-b790-04f9f8a169d3
TID: [-1234] [] [2024-08-07 04:44:32,024]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-07 05:12:08,261]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1115, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 82495c70-4486-4433-9173-43c82e173f6b
TID: [-1234] [] [2024-08-07 05:12:08,264]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 05:14:32,167]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-07 05:17:33,159]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1113, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 8b54d4c5-dd1c-4149-9215-d60b97b7b15e
TID: [-1234] [] [2024-08-07 05:17:33,162]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 05:21:14,138]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1131, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 8b5e8e94-b677-4a4b-b523-e22ad5b6c705
TID: [-1234] [] [2024-08-07 05:21:14,141]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 05:21:15,909]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1136, SOCKET_TIMEOUT = 180000, CORRELATION_ID = fd8ea546-c264-4fc7-a229-9d4a7ee7fde5
TID: [-1234] [] [2024-08-07 05:21:15,911]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 05:21:20,556]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 05:21:20,556]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1109, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 56baf850-6aa7-42e6-a785-bbceba532d8f
TID: [-1234] [] [2024-08-07 05:21:20,557]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:49472, CORRELATION_ID = 56baf850-6aa7-42e6-a785-bbceba532d8f, CONNECTION = http-incoming-49715
TID: [-1234] [] [2024-08-07 05:21:20,557]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 05:21:20,564]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-49715, CORRELATION_ID = 56baf850-6aa7-42e6-a785-bbceba532d8f
TID: [-1234] [] [2024-08-07 05:24:44,441]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1150, SOCKET_TIMEOUT = 180000, CORRELATION_ID = f7953a59-22b0-4cac-a376-2194934b1107
TID: [-1234] [] [2024-08-07 05:24:44,443]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 05:24:46,774]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 99847d42-01b5-4961-8ac7-87e35e2bf17a
TID: [-1234] [] [2024-08-07 05:24:47,162]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 142770a4-d1ca-4b01-b1ef-3430fe815165
TID: [-1234] [] [2024-08-07 05:29:24,307]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d088ff49-a313-4a24-9472-948cbd819853
TID: [-1234] [] [2024-08-07 05:29:31,313]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ad77594d-75b1-4f78-808c-4292a592e5f1
TID: [-1234] [] [2024-08-07 05:44:36,883]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-07 06:10:18,842]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1177, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 57f4a016-0e40-4d8c-9272-2eab2090b4b2
TID: [-1234] [] [2024-08-07 06:10:18,846]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 06:16:13,807]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 06:16:13,809]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:37300, CORRELATION_ID = 5c543fe7-25e9-430d-9fed-dddeea5f97bb, CONNECTION = http-incoming-52551
TID: [-1234] [] [2024-08-07 06:16:13,951]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1180, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 5c543fe7-25e9-430d-9fed-dddeea5f97bb
TID: [-1234] [] [2024-08-07 06:16:13,954]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 06:16:13,973]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-52551, CORRELATION_ID = 5c543fe7-25e9-430d-9fed-dddeea5f97bb
TID: [-1234] [] [2024-08-07 06:16:14,237]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-07 06:19:43,422]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 06:19:43,424]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:34646, CORRELATION_ID = 9c21602e-c374-4831-8a8d-a2f62ac866eb, CONNECTION = http-incoming-52732
TID: [-1234] [] [2024-08-07 06:19:43,577]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1192, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 19f21653-6709-434b-884a-2898ba4e507a
TID: [-1234] [] [2024-08-07 06:19:43,579]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 06:19:43,593]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1195, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 9c21602e-c374-4831-8a8d-a2f62ac866eb
TID: [-1234] [] [2024-08-07 06:19:43,599]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 06:19:43,615]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-52732, CORRELATION_ID = 9c21602e-c374-4831-8a8d-a2f62ac866eb
TID: [-1234] [] [2024-08-07 06:19:45,606]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1202, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 05ae6291-fde8-4222-8cdd-0d14a9584ccf
TID: [-1234] [] [2024-08-07 06:19:45,607]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 06:23:07,219]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 06:23:07,220]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:38514, CORRELATION_ID = 86f8b020-b3a6-4e95-860c-74d31eec560b, CONNECTION = http-incoming-52991
TID: [-1234] [] [2024-08-07 06:23:07,428]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1218, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 86f8b020-b3a6-4e95-860c-74d31eec560b
TID: [-1234] [] [2024-08-07 06:23:07,430]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 06:23:07,464]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-52991, CORRELATION_ID = 86f8b020-b3a6-4e95-860c-74d31eec560b
TID: [-1234] [] [2024-08-07 06:23:10,856]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3f0b652e-f8d5-41a2-ba54-498ad18053ff
TID: [-1234] [] [2024-08-07 06:27:40,033]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e18fe661-b933-46ce-b17b-cfe35dfb260e
TID: [-1234] [] [2024-08-07 06:44:06,675]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1234, SOCKET_TIMEOUT = 180000, CORRELATION_ID = bfa17cb9-1550-4dd1-8275-f668b8e20a7e
TID: [-1234] [] [2024-08-07 06:44:06,677]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 06:44:06,678]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 06:44:06,678]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:49112, CORRELATION_ID = bfa17cb9-1550-4dd1-8275-f668b8e20a7e, CONNECTION = http-incoming-53210
TID: [-1234] [] [2024-08-07 06:44:06,693]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-53210, CORRELATION_ID = bfa17cb9-1550-4dd1-8275-f668b8e20a7e
TID: [-1234] [] [2024-08-07 06:54:21,856]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-07 07:00:48,766]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20240807&denNgay=20240807&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20240807&denNgay=20240807&maTthc=
TID: [-1234] [] [2024-08-07 07:00:51,327]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-08-07 07:00:57,038]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20240807&denNgay=20240807&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20240807&denNgay=20240807&maTthc=
TID: [-1234] [] [2024-08-07 07:00:57,098]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-08-07 07:01:36,968]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20240807&denNgay=20240807&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20240807&denNgay=20240807&maTthc=
TID: [-1234] [] [2024-08-07 07:01:37,007]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2024-08-07 07:01:40,779]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20240807&denNgay=20240807&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20240807&denNgay=20240807&maTthc=
TID: [-1234] [] [2024-08-07 07:01:40,818]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2024-08-07 07:01:41,892]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20240807&denNgay=20240807&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20240807&denNgay=20240807&maTthc=
TID: [-1234] [] [2024-08-07 07:01:41,959]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-08-07 07:01:44,557]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20240807&denNgay=20240807&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20240807&denNgay=20240807&maTthc=
TID: [-1234] [] [2024-08-07 07:01:44,596]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-08-07 07:01:44,829]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20240807&denNgay=20240807&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20240807&denNgay=20240807&maTthc=
TID: [-1234] [] [2024-08-07 07:01:44,867]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-08-07 07:01:50,463]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20240807&denNgay=20240807&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20240807&denNgay=20240807&maTthc=
TID: [-1234] [] [2024-08-07 07:01:50,516]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-08-07 07:10:10,298]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20240807&denNgay=20240807&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20240807&denNgay=20240807&maTthc=
TID: [-1234] [] [2024-08-07 07:10:10,336]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-08-07 07:10:58,296]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1251, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 0c111b4a-7578-4f76-b29e-3ab48761e1c7
TID: [-1234] [] [2024-08-07 07:10:58,299]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 07:14:28,797]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1254, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 8d8d3e5b-ba73-4fad-a3e6-8b4409836cc2
TID: [-1234] [] [2024-08-07 07:14:28,799]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 07:19:41,934]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 07:19:41,936]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:57442, CORRELATION_ID = 725bc8fe-f8d2-44b8-80a3-5d32252b0053, CONNECTION = http-incoming-55650
TID: [-1234] [] [2024-08-07 07:19:42,276]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1247, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 725bc8fe-f8d2-44b8-80a3-5d32252b0053
TID: [-1234] [] [2024-08-07 07:19:42,278]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 07:19:42,296]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-55650, CORRELATION_ID = 725bc8fe-f8d2-44b8-80a3-5d32252b0053
TID: [-1234] [] [2024-08-07 07:23:35,581]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1270, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 62d97a99-6a8a-43fa-aafd-2fff34355075
TID: [-1234] [] [2024-08-07 07:23:35,584]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 07:23:37,779]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 07:23:37,780]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:49196, CORRELATION_ID = eea8ab51-bae2-4aa1-9a82-8654ff35af5e, CONNECTION = http-incoming-55888
TID: [-1234] [] [2024-08-07 07:23:38,478]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1271, SOCKET_TIMEOUT = 180000, CORRELATION_ID = eea8ab51-bae2-4aa1-9a82-8654ff35af5e
TID: [-1234] [] [2024-08-07 07:23:38,479]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 07:23:38,497]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-55888, CORRELATION_ID = eea8ab51-bae2-4aa1-9a82-8654ff35af5e
TID: [-1234] [] [2024-08-07 07:23:38,544]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 07:23:38,545]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:49256, CORRELATION_ID = 0ab0c497-1def-4dbf-9d43-2727de85bf8c, CONNECTION = http-incoming-55896
TID: [-1234] [] [2024-08-07 07:23:39,534]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1278, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 0ab0c497-1def-4dbf-9d43-2727de85bf8c
TID: [-1234] [] [2024-08-07 07:23:39,535]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 07:23:39,553]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-55896, CORRELATION_ID = 0ab0c497-1def-4dbf-9d43-2727de85bf8c
TID: [-1234] [] [2024-08-07 07:27:06,208]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1317, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 45b08da0-be02-4b65-9018-c97a606ecc03
TID: [-1234] [] [2024-08-07 07:27:06,210]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 07:27:07,524]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-07 07:32:03,551]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 64870cb7-f670-41af-80e5-caa08bb8f212
TID: [-1234] [] [2024-08-07 07:32:03,848]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 45da5f00-972e-4845-8ff0-50d82eb512d1
TID: [-1234] [] [2024-08-07 07:33:16,717]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 25c315c0-3fab-454f-b28b-d633ed993765
TID: [-1234] [] [2024-08-07 07:57:24,668]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-07 08:12:13,222]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1345, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 6640dadc-89c2-4a54-9053-d0a397835e2f
TID: [-1234] [] [2024-08-07 08:12:13,226]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 08:15:43,611]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = f7de1e38-1028-4a85-b9f9-59a106ea8002
TID: [-1234] [] [2024-08-07 08:15:43,613]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1347, SOCKET_TIMEOUT = 180000, CORRELATION_ID = f7de1e38-1028-4a85-b9f9-59a106ea8002
TID: [-1234] [] [2024-08-07 08:15:43,615]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 08:20:24,331]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1377, SOCKET_TIMEOUT = 180000, CORRELATION_ID = f4c8c91e-f579-400e-95f0-da76cfeab997
TID: [-1234] [] [2024-08-07 08:20:24,334]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 08:23:50,806]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1381, SOCKET_TIMEOUT = 180000, CORRELATION_ID = a7d4d3c4-4535-4c66-af73-e11dea8f7b4f
TID: [-1234] [] [2024-08-07 08:23:50,808]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 08:23:51,143]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1384, SOCKET_TIMEOUT = 180000, CORRELATION_ID = fe81102a-b783-4577-85eb-8208e668b865
TID: [-1234] [] [2024-08-07 08:23:51,145]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 08:23:53,453]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1389, SOCKET_TIMEOUT = 180000, CORRELATION_ID = eed235c7-550d-4307-adc8-4f5b10f66250
TID: [-1234] [] [2024-08-07 08:23:53,454]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 08:27:34,144]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1405, SOCKET_TIMEOUT = 180000, CORRELATION_ID = f24823ac-8a9d-4ed9-b9f6-69d00095bf2a
TID: [-1234] [] [2024-08-07 08:27:34,146]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 08:27:35,933]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-07 08:39:59,584]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a7f27213-cf22-48eb-a656-ec2dd0771616
TID: [-1234] [] [2024-08-07 08:40:00,293]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4f13342b-f184-425c-baaa-8a6ed5b99dc0
TID: [-1234] [] [2024-08-07 08:41:02,401]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = be427816-9dec-4a69-b054-451b9b0fce53
TID: [-1234] [] [2024-08-07 08:46:18,369]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 08:46:18,371]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:54330, CORRELATION_ID = 3fe765db-46d6-4bce-903d-15dcf0567d86, CONNECTION = http-incoming-59747
TID: [-1234] [] [2024-08-07 08:46:18,375]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1396, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 3fe765db-46d6-4bce-903d-15dcf0567d86
TID: [-1234] [] [2024-08-07 08:46:18,378]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 08:46:18,398]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-59747, CORRELATION_ID = 3fe765db-46d6-4bce-903d-15dcf0567d86
TID: [-1234] [] [2024-08-07 08:53:43,769]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 08:53:43,769]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1420, SOCKET_TIMEOUT = 180000, CORRELATION_ID = b9f043e7-3907-42f2-a25d-9cb5ca6ec0c2
TID: [-1234] [] [2024-08-07 08:53:43,770]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:52266, CORRELATION_ID = b9f043e7-3907-42f2-a25d-9cb5ca6ec0c2, CONNECTION = http-incoming-59809
TID: [-1234] [] [2024-08-07 08:53:43,771]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 08:53:43,790]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-59809, CORRELATION_ID = b9f043e7-3907-42f2-a25d-9cb5ca6ec0c2
TID: [-1234] [] [2024-08-07 08:58:21,060]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-07 08:58:33,624]  WARN {org.wso2.carbon.ui.transports.fileupload.AbstractFileUploadExecutor} - ToolsAnyFileUpload method is not supported
TID: [-1234] [] [2024-08-07 08:58:37,021]  WARN {org.wso2.carbon.ui.transports.fileupload.AbstractFileUploadExecutor} - ToolsAnyFileUpload method is not supported
TID: [-1234] [] [2024-08-07 09:12:29,187]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 09:12:29,189]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:58042, CORRELATION_ID = 084a57bd-f70a-4da0-86d7-d615e4d04895, CONNECTION = http-incoming-61602
TID: [-1234] [] [2024-08-07 09:12:29,352]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1432, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 084a57bd-f70a-4da0-86d7-d615e4d04895
TID: [-1234] [] [2024-08-07 09:12:29,354]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 09:12:29,374]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-61602, CORRELATION_ID = 084a57bd-f70a-4da0-86d7-d615e4d04895
TID: [-1234] [] [2024-08-07 09:16:09,573]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 09:16:09,574]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:50968, CORRELATION_ID = 2a959944-337c-4e27-8db9-aad77af276e7, CONNECTION = http-incoming-61829
TID: [-1234] [] [2024-08-07 09:16:09,595]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 2a959944-337c-4e27-8db9-aad77af276e7
TID: [-1234] [] [2024-08-07 09:16:09,596]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1429, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 2a959944-337c-4e27-8db9-aad77af276e7
TID: [-1234] [] [2024-08-07 09:16:09,598]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 09:16:09,616]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-61829, CORRELATION_ID = 2a959944-337c-4e27-8db9-aad77af276e7
TID: [-1234] [] [2024-08-07 09:21:14,717]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1450, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 1e17f6db-bada-4642-ba7d-1b489fd25544
TID: [-1234] [] [2024-08-07 09:21:14,719]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 09:24:41,808]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1464, SOCKET_TIMEOUT = 180000, CORRELATION_ID = b3832621-1f66-406c-94e7-a19b56ca46c9
TID: [-1234] [] [2024-08-07 09:24:41,811]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 09:24:45,681]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1474, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 8b09a3f8-db9e-4d99-b3e8-984e2160180f
TID: [-1234] [] [2024-08-07 09:24:45,683]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 09:24:49,268]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 09:24:49,269]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:42778, CORRELATION_ID = 234db10e-50d6-4b7d-88c1-3b55491c7b7a, CONNECTION = http-incoming-62488
TID: [-1234] [] [2024-08-07 09:24:49,269]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1466, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 234db10e-50d6-4b7d-88c1-3b55491c7b7a
TID: [-1234] [] [2024-08-07 09:24:49,270]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 09:24:49,310]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-62488, CORRELATION_ID = 234db10e-50d6-4b7d-88c1-3b55491c7b7a
TID: [-1234] [] [2024-08-07 09:28:32,902]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1465, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 3f174cd0-f204-4340-b115-44804841f35e
TID: [-1234] [] [2024-08-07 09:28:32,905]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 09:28:32,917]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 09:28:32,917]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:59106, CORRELATION_ID = 3f174cd0-f204-4340-b115-44804841f35e, CONNECTION = http-incoming-62729
TID: [-1234] [] [2024-08-07 09:28:32,923]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-62729, CORRELATION_ID = 3f174cd0-f204-4340-b115-44804841f35e
TID: [-1234] [] [2024-08-07 09:28:33,502]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-07 09:33:59,657]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 35621781-cd15-4bf4-884c-8a0365b04a9f
TID: [-1234] [] [2024-08-07 09:37:14,552]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6aa25bb2-0697-477e-a041-1d420a75032c
TID: [-1234] [] [2024-08-07 09:37:17,207]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3f9d0fbf-ebd1-40a0-878b-92f07e032b28
TID: [-1234] [] [2024-08-07 09:45:54,129]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1490, SOCKET_TIMEOUT = 180000, CORRELATION_ID = dd1a2e6e-7ab3-453a-ab5f-deb5c7eebc92
TID: [-1234] [] [2024-08-07 09:45:54,131]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 09:59:00,916]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-07 10:14:17,781]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 10:14:17,782]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:50230, CORRELATION_ID = c849a59e-e4dc-4279-9f83-9f7a5b20ee99, CONNECTION = http-incoming-64976
TID: [-1234] [] [2024-08-07 10:14:17,875]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1521, SOCKET_TIMEOUT = 180000, CORRELATION_ID = c849a59e-e4dc-4279-9f83-9f7a5b20ee99
TID: [-1234] [] [2024-08-07 10:14:17,877]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 10:14:17,914]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-64976, CORRELATION_ID = c849a59e-e4dc-4279-9f83-9f7a5b20ee99
TID: [-1234] [] [2024-08-07 10:17:52,540]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1522, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 3d4abf5b-a38e-42ea-afc9-2de80954b6d8
TID: [-1234] [] [2024-08-07 10:17:52,542]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 10:22:57,850]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 10:22:57,852]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:45580, CORRELATION_ID = a77550c3-18a4-4664-951a-4bc7875563cc, CONNECTION = http-incoming-65628
TID: [-1234] [] [2024-08-07 10:22:57,942]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1541, SOCKET_TIMEOUT = 180000, CORRELATION_ID = a77550c3-18a4-4664-951a-4bc7875563cc
TID: [-1234] [] [2024-08-07 10:22:57,944]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 10:22:57,962]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-65628, CORRELATION_ID = a77550c3-18a4-4664-951a-4bc7875563cc
TID: [-1234] [] [2024-08-07 10:26:41,978]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 10:26:41,979]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:56616, CORRELATION_ID = fd8172cb-e2d9-47a9-be90-b9fcd50dab7f, CONNECTION = http-incoming-65822
TID: [-1234] [] [2024-08-07 10:26:42,474]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1547, SOCKET_TIMEOUT = 180000, CORRELATION_ID = fd8172cb-e2d9-47a9-be90-b9fcd50dab7f
TID: [-1234] [] [2024-08-07 10:26:42,476]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 10:26:42,494]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-65822, CORRELATION_ID = fd8172cb-e2d9-47a9-be90-b9fcd50dab7f
TID: [-1234] [] [2024-08-07 10:26:45,526]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 10:26:45,527]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:56872, CORRELATION_ID = 6ec74d65-1f48-4c49-8c97-5f7cdd5597d0, CONNECTION = http-incoming-65853
TID: [-1234] [] [2024-08-07 10:26:45,545]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 6ec74d65-1f48-4c49-8c97-5f7cdd5597d0
TID: [-1234] [] [2024-08-07 10:26:45,546]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1519, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 6ec74d65-1f48-4c49-8c97-5f7cdd5597d0
TID: [-1234] [] [2024-08-07 10:26:45,547]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 10:26:45,565]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-65853, CORRELATION_ID = 6ec74d65-1f48-4c49-8c97-5f7cdd5597d0
TID: [-1234] [] [2024-08-07 10:26:46,687]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1542, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 158d28fe-0a83-46b7-a6de-d2b230079667
TID: [-1234] [] [2024-08-07 10:26:46,689]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 10:29:48,658]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-07 10:29:58,606]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 10:29:58,607]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:52860, CORRELATION_ID = 97e5b2be-5334-4791-a01a-2c0b14d2cc28, CONNECTION = http-incoming-65980
TID: [-1234] [] [2024-08-07 10:29:58,998]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1520, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 97e5b2be-5334-4791-a01a-2c0b14d2cc28
TID: [-1234] [] [2024-08-07 10:29:59,000]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 10:29:59,017]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-65980, CORRELATION_ID = 97e5b2be-5334-4791-a01a-2c0b14d2cc28
TID: [-1234] [] [2024-08-07 10:33:16,552]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1557, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 5a932650-7513-4ff8-b61c-a4645c2230ed
TID: [-1234] [] [2024-08-07 10:33:16,554]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 10:33:16,564]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 10:33:16,564]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:46046, CORRELATION_ID = 5a932650-7513-4ff8-b61c-a4645c2230ed, CONNECTION = http-incoming-66159
TID: [-1234] [] [2024-08-07 10:33:16,573]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-66159, CORRELATION_ID = 5a932650-7513-4ff8-b61c-a4645c2230ed
TID: [-1234] [] [2024-08-07 10:38:30,250]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 55b2e440-dd1d-4b16-9f23-47f422e3df8e
TID: [-1234] [] [2024-08-07 10:38:31,863]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cc32a2cf-1272-4536-a533-077456183fad
TID: [-1234] [] [2024-08-07 10:38:33,755]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 44382d51-44a8-4603-ab7a-906764e77392
TID: [-1234] [] [2024-08-07 10:40:49,404]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20240807&denNgay=20240807&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20240807&denNgay=20240807&maTthc=
TID: [-1234] [] [2024-08-07 10:40:49,505]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-08-07 10:42:57,899]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5980a16a-7f3c-40ad-85f1-dcb0d3147b47
TID: [-1234] [] [2024-08-07 10:59:48,867]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-07 11:11:03,067]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 11:11:03,068]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /lgsp-vdblis/1.0.0/tiepnhanhosomotcua, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:54510, CORRELATION_ID = 0a23ba41-07e3-4701-9981-e823bf427d5d, CONNECTION = http-incoming-68100
TID: [-1234] [] [2024-08-07 11:11:03,112]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 0a23ba41-07e3-4701-9981-e823bf427d5d
TID: [-1234] [] [2024-08-07 11:11:03,113]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/ngsp-vbdlis-sb/tiepnhanhosomotcua, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--VDBLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1602, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 0a23ba41-07e3-4701-9981-e823bf427d5d
TID: [-1234] [] [2024-08-07 11:11:03,115]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--VDBLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 11:11:03,144]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-68100, CORRELATION_ID = 0a23ba41-07e3-4701-9981-e823bf427d5d
TID: [-1234] [] [2024-08-07 11:11:50,198]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1604, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 7cf90a26-42e9-410b-91c5-4bf39ffa8baf
TID: [-1234] [] [2024-08-07 11:11:50,200]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 11:15:17,473]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1596, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 9d701246-bb67-482a-a7d0-cf473cadbb01
TID: [-1234] [] [2024-08-07 11:15:17,476]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 11:20:17,136]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 11:20:17,138]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:44206, CORRELATION_ID = 81c03271-91c9-4bcb-8874-43018dda55cb, CONNECTION = http-incoming-68953
TID: [-1234] [] [2024-08-07 11:20:17,249]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 81c03271-91c9-4bcb-8874-43018dda55cb
TID: [-1234] [] [2024-08-07 11:20:17,250]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1600, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 81c03271-91c9-4bcb-8874-43018dda55cb
TID: [-1234] [] [2024-08-07 11:20:17,252]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 11:20:17,269]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-68953, CORRELATION_ID = 81c03271-91c9-4bcb-8874-43018dda55cb
TID: [-1234] [] [2024-08-07 11:24:04,735]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1624, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 9fe7d7dc-bf91-4135-9379-161bc3bf8b94
TID: [-1234] [] [2024-08-07 11:24:04,738]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 11:24:07,313]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 3253871d-71e8-490f-95b0-acbd059a945e
TID: [-1234] [] [2024-08-07 11:24:07,314]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1631, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 3253871d-71e8-490f-95b0-acbd059a945e
TID: [-1234] [] [2024-08-07 11:24:07,315]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 11:24:07,353]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1626, SOCKET_TIMEOUT = 180000, CORRELATION_ID = df90f298-dee8-4aac-a607-c77cb6d2ba68
TID: [-1234] [] [2024-08-07 11:24:07,354]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 11:27:21,696]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 11:27:21,697]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:46690, CORRELATION_ID = 078b917d-f68d-4558-85eb-2353262cd81b, CONNECTION = http-incoming-69359
TID: [-1234] [] [2024-08-07 11:27:21,704]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1648, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 078b917d-f68d-4558-85eb-2353262cd81b
TID: [-1234] [] [2024-08-07 11:27:21,706]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 11:27:21,723]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-69359, CORRELATION_ID = 078b917d-f68d-4558-85eb-2353262cd81b
TID: [-1234] [] [2024-08-07 11:29:49,265]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-07 11:30:22,404]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = fd332686-2623-4c29-9c5d-34222a838f24
TID: [-1234] [] [2024-08-07 11:30:22,405]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1667, SOCKET_TIMEOUT = 180000, CORRELATION_ID = fd332686-2623-4c29-9c5d-34222a838f24
TID: [-1234] [] [2024-08-07 11:30:22,407]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 11:37:22,918]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e4ee31b1-ae7e-4439-9f7f-36b83f0df548
TID: [-1234] [] [2024-08-07 11:38:26,102]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6b1ae952-c6be-4789-a7d6-7bd19d9e9b68
TID: [-1234] [] [2024-08-07 11:38:26,689]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 707fa526-b0f1-440f-b4c1-77605b31f858
TID: [-1234] [] [2024-08-07 11:39:30,302]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 806b53ec-645d-4f78-bf0c-d395e76c551f
TID: [-1234] [] [2024-08-07 11:43:12,221]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /GiayPhep/pGPThamDoGiaHan.aspx, HEALTH CHECK URL = /GiayPhep/pGPThamDoGiaHan.aspx
TID: [-1234] [] [2024-08-07 11:59:49,575]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-07 12:00:16,296]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8bca8aa7-00db-4be9-bf90-b9f519debd32
TID: [-1234] [] [2024-08-07 12:13:15,225]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 12:13:15,226]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:59498, CORRELATION_ID = 13f84dd5-23bf-452c-81fd-7653f5f86f13, CONNECTION = http-incoming-71716
TID: [-1234] [] [2024-08-07 12:13:15,261]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1709, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 13f84dd5-23bf-452c-81fd-7653f5f86f13
TID: [-1234] [] [2024-08-07 12:13:15,263]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 12:13:15,281]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-71716, CORRELATION_ID = 13f84dd5-23bf-452c-81fd-7653f5f86f13
TID: [-1234] [] [2024-08-07 12:16:34,312]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1724, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 5fbb891c-437b-48f8-9782-69bd9b8272a4
TID: [-1234] [] [2024-08-07 12:16:34,313]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 12:21:33,163]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1736, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 5e00f538-a1e7-4d7f-8525-c73ea4b60acf
TID: [-1234] [] [2024-08-07 12:21:33,166]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 12:25:18,427]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 12:25:18,428]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:57878, CORRELATION_ID = d38a21e6-cad7-4577-b8ad-b139534b51d3, CONNECTION = http-incoming-72493
TID: [-1234] [] [2024-08-07 12:25:18,581]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 890d54ff-a912-4575-b227-14a97b98dca3
TID: [-1234] [] [2024-08-07 12:25:18,582]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1751, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 890d54ff-a912-4575-b227-14a97b98dca3
TID: [-1234] [] [2024-08-07 12:25:18,584]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = d38a21e6-cad7-4577-b8ad-b139534b51d3
TID: [-1234] [] [2024-08-07 12:25:18,584]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 12:25:18,585]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1752, SOCKET_TIMEOUT = 180000, CORRELATION_ID = d38a21e6-cad7-4577-b8ad-b139534b51d3
TID: [-1234] [] [2024-08-07 12:25:18,586]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 12:25:18,611]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-72493, CORRELATION_ID = d38a21e6-cad7-4577-b8ad-b139534b51d3
TID: [-1234] [] [2024-08-07 12:25:20,547]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 12:25:20,548]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:58248, CORRELATION_ID = 07b48edb-08c9-41db-adaa-1c3e559d1336, CONNECTION = http-incoming-72530
TID: [-1234] [] [2024-08-07 12:25:20,722]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 07b48edb-08c9-41db-adaa-1c3e559d1336
TID: [-1234] [] [2024-08-07 12:25:20,723]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1754, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 07b48edb-08c9-41db-adaa-1c3e559d1336
TID: [-1234] [] [2024-08-07 12:25:20,724]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 12:25:20,742]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-72530, CORRELATION_ID = 07b48edb-08c9-41db-adaa-1c3e559d1336
TID: [-1234] [] [2024-08-07 12:28:49,242]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 12:28:49,244]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:33256, CORRELATION_ID = 7de6c72c-7dd5-4dbf-b97a-d99d4f32b73e, CONNECTION = http-incoming-72723
TID: [-1234] [] [2024-08-07 12:28:49,246]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 7de6c72c-7dd5-4dbf-b97a-d99d4f32b73e
TID: [-1234] [] [2024-08-07 12:28:49,247]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1758, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 7de6c72c-7dd5-4dbf-b97a-d99d4f32b73e
TID: [-1234] [] [2024-08-07 12:28:49,248]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 12:28:49,264]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-72723, CORRELATION_ID = 7de6c72c-7dd5-4dbf-b97a-d99d4f32b73e
TID: [-1234] [] [2024-08-07 12:28:49,297]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4ea4969d-d409-431f-8d81-6569b43ff604
TID: [-1234] [] [2024-08-07 12:28:50,242]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 95fab14a-7b28-4658-9e0b-f9ef0901802e
TID: [-1234] [] [2024-08-07 12:28:50,324]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8d201476-5de8-4b6b-b64b-1fac4b0dfa48
TID: [-1234] [] [2024-08-07 12:29:49,843]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-07 12:32:04,534]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3a3f2a0e-2308-49ff-948c-a37dbabc59d4
TID: [-1234] [] [2024-08-07 12:32:04,625]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7123770e-b801-4a90-8aa0-91199b08d400
TID: [-1234] [] [2024-08-07 12:32:05,552]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ca3915b8-5ed5-43ec-a2e6-e4a76098d47f
TID: [-1234] [] [2024-08-07 12:32:05,580]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 12:32:05,581]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:51438, CORRELATION_ID = 93f14a20-08c1-4c5f-bc8b-f70711dbf3e0, CONNECTION = http-incoming-72818
TID: [-1234] [] [2024-08-07 12:32:05,596]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1775, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 93f14a20-08c1-4c5f-bc8b-f70711dbf3e0
TID: [-1234] [] [2024-08-07 12:32:05,598]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 12:32:05,616]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-72818, CORRELATION_ID = 93f14a20-08c1-4c5f-bc8b-f70711dbf3e0
TID: [-1234] [] [2024-08-07 12:32:21,250]  WARN {org.wso2.carbon.ui.transports.fileupload.AbstractFileUploadExecutor} - ToolsAnyFileUpload method is not supported
TID: [-1234] [] [2024-08-07 12:36:47,261]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3b35a45a-c459-40e3-a5e7-10b3994207bb
TID: [-1234] [] [2024-08-07 12:36:47,740]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b11de3e0-01b8-4fd2-b6df-9415540e90d2
TID: [-1234] [] [2024-08-07 12:36:47,766]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f64ac796-ec13-4bc7-90c6-fbf9ebd157a8
TID: [-1234] [] [2024-08-07 12:36:47,787]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = da733a7f-911d-4f94-a845-ce6a4c4d74d4
TID: [-1234] [] [2024-08-07 12:44:03,293]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1827d7c8-5020-4de3-b185-ff528b53ab67
TID: [-1234] [] [2024-08-07 12:59:50,043]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-07 13:01:53,911]  WARN {org.wso2.carbon.ui.transports.fileupload.AbstractFileUploadExecutor} - ToolsAnyFileUpload method is not supported
TID: [-1234] [] [2024-08-07 13:10:56,774]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 13:10:56,776]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:40674, CORRELATION_ID = 40f79e9f-d4df-48ed-8e7a-13a0061b4351, CONNECTION = http-incoming-74850
TID: [-1234] [] [2024-08-07 13:10:56,960]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1823, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 40f79e9f-d4df-48ed-8e7a-13a0061b4351
TID: [-1234] [] [2024-08-07 13:10:56,962]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 13:10:56,987]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-74850, CORRELATION_ID = 40f79e9f-d4df-48ed-8e7a-13a0061b4351
TID: [-1234] [] [2024-08-07 13:14:15,119]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 13:14:15,121]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:50212, CORRELATION_ID = a4ec5bcd-884a-4e85-8de2-061099c231e9, CONNECTION = http-incoming-74978
TID: [-1234] [] [2024-08-07 13:14:15,291]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1829, SOCKET_TIMEOUT = 180000, CORRELATION_ID = a4ec5bcd-884a-4e85-8de2-061099c231e9
TID: [-1234] [] [2024-08-07 13:14:15,294]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 13:14:15,313]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-74978, CORRELATION_ID = a4ec5bcd-884a-4e85-8de2-061099c231e9
TID: [-1234] [] [2024-08-07 13:19:20,942]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1816, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 4c0c06fe-44bb-419f-9fcf-1e068840aa25
TID: [-1234] [] [2024-08-07 13:19:20,946]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 13:22:56,432]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = d646ac3c-e631-48c3-8120-24bb37b80fcc
TID: [-1234] [] [2024-08-07 13:22:56,433]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1860, SOCKET_TIMEOUT = 180000, CORRELATION_ID = d646ac3c-e631-48c3-8120-24bb37b80fcc
TID: [-1234] [] [2024-08-07 13:22:56,435]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 13:22:59,692]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = d6f2ad19-13a7-4007-b5c3-c9cc0887974e
TID: [-1234] [] [2024-08-07 13:22:59,693]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1856, SOCKET_TIMEOUT = 180000, CORRELATION_ID = d6f2ad19-13a7-4007-b5c3-c9cc0887974e
TID: [-1234] [] [2024-08-07 13:22:59,694]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 13:23:01,579]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1849, SOCKET_TIMEOUT = 180000, CORRELATION_ID = c99c55b6-5347-461e-af4c-055332187719
TID: [-1234] [] [2024-08-07 13:23:01,580]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 13:26:23,816]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 13:26:23,817]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:39514, CORRELATION_ID = 102d1b4a-df84-426e-bc81-43fe50a0b573, CONNECTION = http-incoming-75851
TID: [-1234] [] [2024-08-07 13:26:23,844]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1868, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 102d1b4a-df84-426e-bc81-43fe50a0b573
TID: [-1234] [] [2024-08-07 13:26:23,846]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 13:26:23,864]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-75851, CORRELATION_ID = 102d1b4a-df84-426e-bc81-43fe50a0b573
TID: [-1234] [] [2024-08-07 13:26:24,669]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8c7bb731-2bba-4b04-bd98-a32b0982b344
TID: [-1234] [] [2024-08-07 13:26:25,227]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 494c6331-d403-48ee-86b7-d0e9313ec8ee
TID: [-1234] [] [2024-08-07 13:26:25,682]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ff98467f-28df-4272-9a99-8a4d1c9501bd
TID: [-1234] [] [2024-08-07 13:29:27,318]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 13:29:27,319]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:55394, CORRELATION_ID = 1f392100-87fa-48d7-9282-14f4a29f3bf3, CONNECTION = http-incoming-75887
TID: [-1234] [] [2024-08-07 13:29:27,456]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 1f392100-87fa-48d7-9282-14f4a29f3bf3
TID: [-1234] [] [2024-08-07 13:29:27,458]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1892, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 1f392100-87fa-48d7-9282-14f4a29f3bf3
TID: [-1234] [] [2024-08-07 13:29:27,459]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 13:29:27,498]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-75887, CORRELATION_ID = 1f392100-87fa-48d7-9282-14f4a29f3bf3
TID: [-1234] [] [2024-08-07 13:29:50,345]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-07 13:33:55,944]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6c030811-1c4b-4308-90db-af0ad7511d53
TID: [-1234] [] [2024-08-07 13:33:55,950]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 82e136d9-906f-41f9-8bbf-2382567daca2
TID: [-1234] [] [2024-08-07 13:35:04,126]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 505f6dfd-2f7a-460f-9081-c70879432d83
TID: [-1234] [] [2024-08-07 13:59:50,729]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-07 14:13:08,237]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1929, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 30871a82-6501-4d11-b324-07741fe86e4e
TID: [-1234] [] [2024-08-07 14:13:08,239]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 14:16:34,132]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 14:16:34,136]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:56506, CORRELATION_ID = 468c1fe4-d274-4e44-9781-7f9d1eea96b5, CONNECTION = http-incoming-78201
TID: [-1234] [] [2024-08-07 14:16:34,352]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1912, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 468c1fe4-d274-4e44-9781-7f9d1eea96b5
TID: [-1234] [] [2024-08-07 14:16:34,354]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 14:16:34,372]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-78201, CORRELATION_ID = 468c1fe4-d274-4e44-9781-7f9d1eea96b5
TID: [-1234] [] [2024-08-07 14:22:13,482]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1937, SOCKET_TIMEOUT = 180000, CORRELATION_ID = c0f6be61-fdf4-4f08-9e53-f1b191b1152d
TID: [-1234] [] [2024-08-07 14:22:13,485]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 14:26:04,555]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1963, SOCKET_TIMEOUT = 180000, CORRELATION_ID = d4d6756b-d171-40eb-891d-c98a2b2fe0c4
TID: [-1234] [] [2024-08-07 14:26:04,557]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 14:26:09,126]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1953, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 4bfeceef-0961-445b-ae50-ac50c0a8af12
TID: [-1234] [] [2024-08-07 14:26:09,129]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 14:26:09,146]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:41402, CORRELATION_ID = 4bfeceef-0961-445b-ae50-ac50c0a8af12, CONNECTION = http-incoming-78901
TID: [-1234] [] [2024-08-07 14:26:09,906]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 14:26:09,907]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:41438, CORRELATION_ID = 39438477-66c3-4169-90fd-e1e5870cc0f3, CONNECTION = http-incoming-78907
TID: [-1234] [] [2024-08-07 14:26:09,908]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1956, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 39438477-66c3-4169-90fd-e1e5870cc0f3
TID: [-1234] [] [2024-08-07 14:26:09,909]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 14:26:09,926]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-78907, CORRELATION_ID = 39438477-66c3-4169-90fd-e1e5870cc0f3
TID: [-1234] [] [2024-08-07 14:29:38,365]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 14:29:38,367]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:41284, CORRELATION_ID = 7b6da57a-405e-4df4-b484-ba77c4b1d0f6, CONNECTION = http-incoming-79079
TID: [-1234] [] [2024-08-07 14:29:38,488]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1955, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 7b6da57a-405e-4df4-b484-ba77c4b1d0f6
TID: [-1234] [] [2024-08-07 14:29:38,490]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 14:29:38,507]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-79079, CORRELATION_ID = 7b6da57a-405e-4df4-b484-ba77c4b1d0f6
TID: [-1234] [] [2024-08-07 14:29:50,947]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-07 14:32:46,412]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1991, SOCKET_TIMEOUT = 180000, CORRELATION_ID = f18f4800-21db-428d-a805-e43c74d155de
TID: [-1234] [] [2024-08-07 14:32:46,415]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 14:32:46,426]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 14:32:46,427]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:34698, CORRELATION_ID = f18f4800-21db-428d-a805-e43c74d155de, CONNECTION = http-incoming-79151
TID: [-1234] [] [2024-08-07 14:32:46,436]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-79151, CORRELATION_ID = f18f4800-21db-428d-a805-e43c74d155de
TID: [-1234] [] [2024-08-07 14:38:12,122]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a4dcb31f-2ea1-49da-b9a7-99f4f6298a64
TID: [-1234] [] [2024-08-07 14:49:30,879]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 90391d33-e847-4cd5-b4ab-69cb245efcb5
TID: [-1234] [] [2024-08-07 14:59:51,739]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-07 15:14:13,955]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 15:14:13,957]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:49246, CORRELATION_ID = ee13175a-626e-4356-bfe7-c7811b0df8a6, CONNECTION = http-incoming-81410
TID: [-1234] [] [2024-08-07 15:14:14,034]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = ee13175a-626e-4356-bfe7-c7811b0df8a6
TID: [-1234] [] [2024-08-07 15:14:14,035]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2014, SOCKET_TIMEOUT = 180000, CORRELATION_ID = ee13175a-626e-4356-bfe7-c7811b0df8a6
TID: [-1234] [] [2024-08-07 15:14:14,037]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 15:14:14,099]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-81410, CORRELATION_ID = ee13175a-626e-4356-bfe7-c7811b0df8a6
TID: [-1234] [] [2024-08-07 15:17:51,265]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 15:17:51,266]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:47096, CORRELATION_ID = 2ca272ef-d64a-4e2e-ba08-3ed4e60fa822, CONNECTION = http-incoming-81630
TID: [-1234] [] [2024-08-07 15:17:51,292]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2021, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 2ca272ef-d64a-4e2e-ba08-3ed4e60fa822
TID: [-1234] [] [2024-08-07 15:17:51,294]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 15:17:51,312]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-81630, CORRELATION_ID = 2ca272ef-d64a-4e2e-ba08-3ed4e60fa822
TID: [-1234] [] [2024-08-07 15:23:32,890]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = e8acf133-41a7-4626-8786-a7f66af18b91
TID: [-1234] [] [2024-08-07 15:23:32,892]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2026, SOCKET_TIMEOUT = 180000, CORRELATION_ID = e8acf133-41a7-4626-8786-a7f66af18b91
TID: [-1234] [] [2024-08-07 15:23:32,894]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 15:25:01,731]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20240807&denNgay=20240807&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20240807&denNgay=20240807&maTthc=
TID: [-1234] [] [2024-08-07 15:25:01,801]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-08-07 15:27:00,416]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20240807&denNgay=20240807&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20240807&denNgay=20240807&maTthc=
TID: [-1234] [] [2024-08-07 15:27:00,458]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-08-07 15:27:17,378]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 15:27:17,379]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:60182, CORRELATION_ID = 01fe8168-782b-477e-8a43-7562d8040082, CONNECTION = http-incoming-82287
TID: [-1234] [] [2024-08-07 15:27:17,838]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2024, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 01fe8168-782b-477e-8a43-7562d8040082
TID: [-1234] [] [2024-08-07 15:27:17,840]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 15:27:17,858]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-82287, CORRELATION_ID = 01fe8168-782b-477e-8a43-7562d8040082
TID: [-1234] [] [2024-08-07 15:27:57,487]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20240807&denNgay=20240807&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20240807&denNgay=20240807&maTthc=
TID: [-1234] [] [2024-08-07 15:27:57,530]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-08-07 15:28:01,596]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20240807&denNgay=20240807&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20240807&denNgay=20240807&maTthc=
TID: [-1234] [] [2024-08-07 15:28:01,637]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-08-07 15:28:24,624]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20240807&denNgay=20240807&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20240807&denNgay=20240807&maTthc=
TID: [-1234] [] [2024-08-07 15:28:24,663]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2024-08-07 15:29:52,515]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-07 15:30:34,175]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2041, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 694934f4-e951-45ac-9645-8f77788e8b91
TID: [-1234] [] [2024-08-07 15:30:34,177]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 15:30:34,186]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 15:30:34,187]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:60028, CORRELATION_ID = 694934f4-e951-45ac-9645-8f77788e8b91, CONNECTION = http-incoming-82389
TID: [-1234] [] [2024-08-07 15:30:34,207]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-82389, CORRELATION_ID = 694934f4-e951-45ac-9645-8f77788e8b91
TID: [-1234] [] [2024-08-07 15:33:33,584]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /robots.txt, HEALTH CHECK URL = /robots.txt
TID: [-1234] [] [2024-08-07 15:33:42,571]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sitemap.xml.gz, HEALTH CHECK URL = /sitemap.xml.gz
TID: [-1234] [] [2024-08-07 15:34:31,622]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2043, SOCKET_TIMEOUT = 180000, CORRELATION_ID = e274c822-c663-424b-9dc8-a7a9f2a966e4
TID: [-1234] [] [2024-08-07 15:34:31,624]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 15:37:45,644]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2055, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 5c924372-2911-45a4-a3af-fe6749509ccd
TID: [-1234] [] [2024-08-07 15:37:45,644]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 15:37:45,646]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:55356, CORRELATION_ID = 5c924372-2911-45a4-a3af-fe6749509ccd, CONNECTION = http-incoming-82723
TID: [-1234] [] [2024-08-07 15:37:45,647]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 15:37:45,663]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-82723, CORRELATION_ID = 5c924372-2911-45a4-a3af-fe6749509ccd
TID: [-1234] [] [2024-08-07 15:59:53,290]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-07 16:16:37,014]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 0ae73f13-5adf-4576-927f-9a5f9512a6ba
TID: [-1234] [] [2024-08-07 16:16:37,016]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2087, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 0ae73f13-5adf-4576-927f-9a5f9512a6ba
TID: [-1234] [] [2024-08-07 16:16:37,018]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 16:20:24,508]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2083, SOCKET_TIMEOUT = 180000, CORRELATION_ID = d25f8ad0-f9c8-4783-97c8-beabbbaf44c1
TID: [-1234] [] [2024-08-07 16:20:24,509]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 16:27:57,780]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sitemap.xml.gz, HEALTH CHECK URL = /sitemap.xml.gz
TID: [-1234] [] [2024-08-07 16:28:22,568]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 16:28:22,570]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:38584, CORRELATION_ID = 4b234d96-09e2-4877-8d42-2d1a9697359b, CONNECTION = http-incoming-85690
TID: [-1234] [] [2024-08-07 16:28:22,733]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 4b234d96-09e2-4877-8d42-2d1a9697359b
TID: [-1234] [] [2024-08-07 16:28:22,734]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2082, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 4b234d96-09e2-4877-8d42-2d1a9697359b
TID: [-1234] [] [2024-08-07 16:28:22,736]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 16:28:22,754]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-85690, CORRELATION_ID = 4b234d96-09e2-4877-8d42-2d1a9697359b
TID: [-1234] [] [2024-08-07 16:28:29,731]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 16:28:29,732]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:55182, CORRELATION_ID = dd860a21-cbc5-4564-a3b9-4f60a18d72be, CONNECTION = http-incoming-85719
TID: [-1234] [] [2024-08-07 16:28:29,755]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2090, SOCKET_TIMEOUT = 180000, CORRELATION_ID = dd860a21-cbc5-4564-a3b9-4f60a18d72be
TID: [-1234] [] [2024-08-07 16:28:29,757]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 16:28:29,772]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-85719, CORRELATION_ID = dd860a21-cbc5-4564-a3b9-4f60a18d72be
TID: [-1234] [] [2024-08-07 16:29:57,696]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-07 16:31:42,800]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2096, SOCKET_TIMEOUT = 180000, CORRELATION_ID = d07404c5-48bd-46cf-99e2-95264b58e6c3
TID: [-1234] [] [2024-08-07 16:31:42,801]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 16:35:04,690]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 91d79acb-9c8f-4bda-aa0e-d5fa137d3c9a
TID: [-1234] [] [2024-08-07 16:35:04,691]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2105, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 91d79acb-9c8f-4bda-aa0e-d5fa137d3c9a
TID: [-1234] [] [2024-08-07 16:35:04,693]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 16:35:04,708]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 16:35:04,709]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:33358, CORRELATION_ID = 91d79acb-9c8f-4bda-aa0e-d5fa137d3c9a, CONNECTION = http-incoming-85932
TID: [-1234] [] [2024-08-07 16:38:12,179]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 385f6da3-9684-464c-8bb9-7669ec76bfd6
TID: [-1234] [] [2024-08-07 16:38:12,181]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2118, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 385f6da3-9684-464c-8bb9-7669ec76bfd6
TID: [-1234] [] [2024-08-07 16:38:12,183]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 16:41:14,735]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 505d9447-11d1-4c7d-bab1-462cafa64fec
TID: [-1234] [] [2024-08-07 16:41:14,737]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2123, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 505d9447-11d1-4c7d-bab1-462cafa64fec
TID: [-1234] [] [2024-08-07 16:41:14,739]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 16:53:57,552]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4d9f1f70-a459-4964-a0d2-f54ab2e50e9d
TID: [-1234] [] [2024-08-07 16:59:58,402]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-07 17:11:48,911]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 17:11:48,912]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:46462, CORRELATION_ID = 5063e207-611d-4672-83d6-f26d9064d39c, CONNECTION = http-incoming-88206
TID: [-1234] [] [2024-08-07 17:11:49,690]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 5063e207-611d-4672-83d6-f26d9064d39c
TID: [-1234] [] [2024-08-07 17:11:49,691]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2120, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 5063e207-611d-4672-83d6-f26d9064d39c
TID: [-1234] [] [2024-08-07 17:11:49,693]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 17:11:49,711]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-88206, CORRELATION_ID = 5063e207-611d-4672-83d6-f26d9064d39c
TID: [-1234] [] [2024-08-07 17:15:26,772]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2138, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 723b9521-6f25-4d52-96bd-12d9d69511c7
TID: [-1234] [] [2024-08-07 17:15:26,776]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 17:20:28,230]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2151, SOCKET_TIMEOUT = 180000, CORRELATION_ID = c829fc04-6b50-47be-b83c-b15638cd9a27
TID: [-1234] [] [2024-08-07 17:20:28,233]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 17:24:31,626]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = b15494ca-f45b-4de8-bef1-6e0bc7359267
TID: [-1234] [] [2024-08-07 17:24:31,627]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2170, SOCKET_TIMEOUT = 180000, CORRELATION_ID = b15494ca-f45b-4de8-bef1-6e0bc7359267
TID: [-1234] [] [2024-08-07 17:24:31,629]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 17:24:34,084]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 17:24:34,085]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:52494, CORRELATION_ID = c38fba80-1ce4-4604-a2b3-8a9806aa5bde, CONNECTION = http-incoming-89098
TID: [-1234] [] [2024-08-07 17:24:34,086]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2162, SOCKET_TIMEOUT = 180000, CORRELATION_ID = c38fba80-1ce4-4604-a2b3-8a9806aa5bde
TID: [-1234] [] [2024-08-07 17:24:34,087]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 17:24:34,112]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-89098, CORRELATION_ID = c38fba80-1ce4-4604-a2b3-8a9806aa5bde
TID: [-1234] [] [2024-08-07 17:27:35,719]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 17:27:35,721]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:50854, CORRELATION_ID = 2b22bdae-f9fa-4fd3-b5df-639769dc664b, CONNECTION = http-incoming-89129
TID: [-1234] [] [2024-08-07 17:27:35,728]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2173, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 2b22bdae-f9fa-4fd3-b5df-639769dc664b
TID: [-1234] [] [2024-08-07 17:27:35,729]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 17:27:35,748]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-89129, CORRELATION_ID = 2b22bdae-f9fa-4fd3-b5df-639769dc664b
TID: [-1234] [] [2024-08-07 17:29:59,261]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-07 17:30:42,616]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2194, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 17183cfe-5f99-4252-a70e-05962e5e4d17
TID: [-1234] [] [2024-08-07 17:30:42,618]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 17:33:55,422]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 17:33:55,424]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:52414, CORRELATION_ID = 92f8de29-0dfa-452a-9205-fe28f3cedd8b, CONNECTION = http-incoming-89378
TID: [-1234] [] [2024-08-07 17:33:55,424]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 92f8de29-0dfa-452a-9205-fe28f3cedd8b
TID: [-1234] [] [2024-08-07 17:33:55,425]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2210, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 92f8de29-0dfa-452a-9205-fe28f3cedd8b
TID: [-1234] [] [2024-08-07 17:33:55,427]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 17:33:55,479]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-89378, CORRELATION_ID = 92f8de29-0dfa-452a-9205-fe28f3cedd8b
TID: [-1234] [] [2024-08-07 17:37:00,709]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2220, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 2c9fd1ee-7694-4f8f-88c9-aa49429fc0c8
TID: [-1234] [] [2024-08-07 17:37:00,711]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 17:41:27,153]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1847a50b-bf95-4ca1-9de5-5aed37122b84
TID: [-1234] [] [2024-08-07 17:41:33,248]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ba1744f0-2553-4fc7-856b-3cc674cbd79a
TID: [-1234] [] [2024-08-07 17:59:59,433]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-07 18:11:51,271]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 18:11:51,273]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:36246, CORRELATION_ID = 741b1b80-396a-4785-94fb-2093e50363bf, CONNECTION = http-incoming-91492
TID: [-1234] [] [2024-08-07 18:11:51,328]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2250, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 741b1b80-396a-4785-94fb-2093e50363bf
TID: [-1234] [] [2024-08-07 18:11:51,332]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 18:11:51,351]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-91492, CORRELATION_ID = 741b1b80-396a-4785-94fb-2093e50363bf
TID: [-1234] [] [2024-08-07 18:15:20,782]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2258, SOCKET_TIMEOUT = 180000, CORRELATION_ID = fb1c4bb9-c445-47a9-ace4-e0fb316cd564
TID: [-1234] [] [2024-08-07 18:15:20,786]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 18:15:21,535]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2fa75ed1-be55-49f7-b085-90e53dbe570b
TID: [-1234] [] [2024-08-07 18:15:21,628]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 86096eca-1524-4b66-928e-4db4b9d0e644
TID: [-1234] [] [2024-08-07 18:21:12,015]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 525ba601-d2e0-4ef6-9630-2e87a7a4a3ed
TID: [-1234] [] [2024-08-07 18:21:12,017]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2279, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 525ba601-d2e0-4ef6-9630-2e87a7a4a3ed
TID: [-1234] [] [2024-08-07 18:21:12,018]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 18:21:14,111]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 18:21:14,112]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:60160, CORRELATION_ID = 860017dc-1a82-4a2f-833e-66d0e9868df7, CONNECTION = http-incoming-92362
TID: [-1234] [] [2024-08-07 18:21:14,238]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2270, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 860017dc-1a82-4a2f-833e-66d0e9868df7
TID: [-1234] [] [2024-08-07 18:21:14,240]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 18:21:14,256]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-92362, CORRELATION_ID = 860017dc-1a82-4a2f-833e-66d0e9868df7
TID: [-1234] [] [2024-08-07 18:24:19,985]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2282, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 95094dab-c085-474c-a3e9-ae187fb0df06
TID: [-1234] [] [2024-08-07 18:24:19,987]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 18:24:35,175]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Manage/SummernoteUploadImage, HEALTH CHECK URL = /Manage/SummernoteUploadImage
TID: [-1234] [] [2024-08-07 18:27:29,699]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 18:27:29,700]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:41994, CORRELATION_ID = b6a67e85-8d01-4a96-8497-402e9e6e36bc, CONNECTION = http-incoming-92503
TID: [-1234] [] [2024-08-07 18:27:29,801]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = b6a67e85-8d01-4a96-8497-402e9e6e36bc
TID: [-1234] [] [2024-08-07 18:27:29,802]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2304, SOCKET_TIMEOUT = 180000, CORRELATION_ID = b6a67e85-8d01-4a96-8497-402e9e6e36bc
TID: [-1234] [] [2024-08-07 18:27:29,803]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 18:27:29,821]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-92503, CORRELATION_ID = b6a67e85-8d01-4a96-8497-402e9e6e36bc
TID: [-1234] [] [2024-08-07 18:30:01,186]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-07 18:30:44,646]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2314, SOCKET_TIMEOUT = 180000, CORRELATION_ID = f4e1a1b0-141d-4c72-857b-a88555689563
TID: [-1234] [] [2024-08-07 18:30:44,648]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 18:33:47,017]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = de2d7e18-0a80-48c2-91b5-5cc999d63872
TID: [-1234] [] [2024-08-07 18:33:47,018]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2322, SOCKET_TIMEOUT = 180000, CORRELATION_ID = de2d7e18-0a80-48c2-91b5-5cc999d63872
TID: [-1234] [] [2024-08-07 18:33:47,020]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 18:40:27,216]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2cab7a29-38ed-441a-9235-5b46c2193d08
TID: [-1234] [] [2024-08-07 19:00:03,607]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-07 19:15:59,936]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 9ee57bfe-8cf6-4ebf-91fb-9bbe38de8d63
TID: [-1234] [] [2024-08-07 19:15:59,938]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2352, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 9ee57bfe-8cf6-4ebf-91fb-9bbe38de8d63
TID: [-1234] [] [2024-08-07 19:15:59,941]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 19:20:11,523]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5280ad0b-8d9b-4fc5-83d8-2c0bd9f594a6
TID: [-1234] [] [2024-08-07 19:20:12,763]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5dfbe32b-e600-4ab5-aad1-7ac3b08b2f93
TID: [-1234] [] [2024-08-07 19:20:13,037]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4980a37c-4cce-49d0-aa6d-c5fc9fb54697
TID: [-1234] [] [2024-08-07 19:20:14,839]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2355, SOCKET_TIMEOUT = 180000, CORRELATION_ID = b99123c4-1614-416c-bf9c-09dd29890047
TID: [-1234] [] [2024-08-07 19:20:14,840]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 19:20:14,841]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:42272, CORRELATION_ID = b99123c4-1614-416c-bf9c-09dd29890047, CONNECTION = http-incoming-94984
TID: [-1234] [] [2024-08-07 19:20:14,841]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 19:20:14,860]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-94984, CORRELATION_ID = b99123c4-1614-416c-bf9c-09dd29890047
TID: [-1234] [] [2024-08-07 19:25:51,359]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 19:25:51,360]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:34300, CORRELATION_ID = ef17a3be-ff75-4948-ac45-5386cf68d133, CONNECTION = http-incoming-95378
TID: [-1234] [] [2024-08-07 19:25:51,661]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2376, SOCKET_TIMEOUT = 180000, CORRELATION_ID = ef17a3be-ff75-4948-ac45-5386cf68d133
TID: [-1234] [] [2024-08-07 19:25:51,662]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 19:25:51,680]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-95378, CORRELATION_ID = ef17a3be-ff75-4948-ac45-5386cf68d133
TID: [-1234] [] [2024-08-07 19:29:35,332]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2368, SOCKET_TIMEOUT = 180000, CORRELATION_ID = d8d8ef7a-0a27-4d02-9dde-462c804ac15a
TID: [-1234] [] [2024-08-07 19:29:35,334]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 19:29:40,490]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = f23be45e-411f-42ff-a294-7c1765d43145
TID: [-1234] [] [2024-08-07 19:29:40,491]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2367, SOCKET_TIMEOUT = 180000, CORRELATION_ID = f23be45e-411f-42ff-a294-7c1765d43145
TID: [-1234] [] [2024-08-07 19:29:40,493]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 19:30:04,340]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-07 19:32:50,691]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2378, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 8a3f68b4-a1d3-40b6-87e2-67898a0a1da7
TID: [-1234] [] [2024-08-07 19:32:50,693]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 19:35:54,562]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 19:35:54,563]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:43990, CORRELATION_ID = fbd4c44c-dac1-4cf3-9d32-894d067526e1, CONNECTION = http-incoming-95741
TID: [-1234] [] [2024-08-07 19:35:54,733]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2390, SOCKET_TIMEOUT = 180000, CORRELATION_ID = fbd4c44c-dac1-4cf3-9d32-894d067526e1
TID: [-1234] [] [2024-08-07 19:35:54,735]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 19:35:54,751]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-95741, CORRELATION_ID = fbd4c44c-dac1-4cf3-9d32-894d067526e1
TID: [-1234] [] [2024-08-07 19:39:08,702]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2406, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 4743fe23-3568-48f7-930f-4f4a9ba1b924
TID: [-1234] [] [2024-08-07 19:39:08,703]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 19:42:31,438]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 19:42:31,439]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:51092, CORRELATION_ID = 635897ae-b086-4712-9dc2-032295522b08, CONNECTION = http-incoming-95951
TID: [-1234] [] [2024-08-07 19:42:31,713]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 635897ae-b086-4712-9dc2-032295522b08
TID: [-1234] [] [2024-08-07 19:42:31,713]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2395, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 635897ae-b086-4712-9dc2-032295522b08
TID: [-1234] [] [2024-08-07 19:42:31,715]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 19:42:31,732]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-95951, CORRELATION_ID = 635897ae-b086-4712-9dc2-032295522b08
TID: [-1234] [] [2024-08-07 19:47:25,254]  INFO {org.wso2.carbon.identity.application.authentication.framework.store.SessionCleanUpService} - Session Data cleanup task is running successfully for removing expired Data
TID: [-1234] [] [2024-08-07 19:47:35,224]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 41a99f80-4227-4d10-9600-b662bfc3e8c3
TID: [-1234] [] [2024-08-07 19:47:35,518]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 01d1fa8f-fa3f-496d-bf8d-6c2bd5e9efd6
TID: [-1234] [] [2024-08-07 19:47:35,537]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c9f547fc-63ef-4735-8d1b-84664b3efd92
TID: [-1234] [] [2024-08-07 19:47:35,854]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0e81faeb-6948-44f5-ac6f-f3d03bd30b7e
TID: [-1234] [] [2024-08-07 19:47:40,705]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bc595ef3-c3d4-43b4-80f0-981e3e59a239
TID: [-1234] [] [2024-08-07 20:00:05,313]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-07 20:12:30,837]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = a9257819-16bc-46ab-b007-717461eccdf0
TID: [-1234] [] [2024-08-07 20:12:30,839]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2445, SOCKET_TIMEOUT = 180000, CORRELATION_ID = a9257819-16bc-46ab-b007-717461eccdf0
TID: [-1234] [] [2024-08-07 20:12:30,841]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 20:15:58,180]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2435, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 80bb93b9-7ac0-4ae5-9d31-9ce6690be427
TID: [-1234] [] [2024-08-07 20:15:58,182]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 20:15:59,169]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8758cda3-9790-40f4-b385-57b2a5ecb7c2
TID: [-1234] [] [2024-08-07 20:19:47,098]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20240807&denNgay=20240807&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20240807&denNgay=20240807&maTthc=
TID: [-1234] [] [2024-08-07 20:19:47,151]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2024-08-07 20:21:06,235]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 20:21:06,236]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:55442, CORRELATION_ID = 6d11d466-9e23-4609-9f60-e142e67f4d54, CONNECTION = http-incoming-98584
TID: [-1234] [] [2024-08-07 20:21:06,408]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2458, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 6d11d466-9e23-4609-9f60-e142e67f4d54
TID: [-1234] [] [2024-08-07 20:21:06,410]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 20:21:06,427]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-98584, CORRELATION_ID = 6d11d466-9e23-4609-9f60-e142e67f4d54
TID: [-1234] [] [2024-08-07 20:24:38,108]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2473, SOCKET_TIMEOUT = 180000, CORRELATION_ID = fff22bad-9a02-47df-b95e-3c02d582065a
TID: [-1234] [] [2024-08-07 20:24:38,110]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 20:24:39,949]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = baa1bed6-bf9e-4ee3-afc5-3bc1d93093b1
TID: [-1234] [] [2024-08-07 20:24:39,950]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2467, SOCKET_TIMEOUT = 180000, CORRELATION_ID = baa1bed6-bf9e-4ee3-afc5-3bc1d93093b1
TID: [-1234] [] [2024-08-07 20:24:39,952]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 20:26:19,091]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20240807&denNgay=20240807&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20240807&denNgay=20240807&maTthc=
TID: [-1234] [] [2024-08-07 20:26:19,129]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-08-07 20:26:55,158]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20240807&denNgay=20240807&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20240807&denNgay=20240807&maTthc=
TID: [-1234] [] [2024-08-07 20:26:55,198]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-08-07 20:27:43,827]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 20:27:43,828]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:42186, CORRELATION_ID = d185db82-06b5-46b5-8666-d512b7e1c4d9, CONNECTION = http-incoming-98867
TID: [-1234] [] [2024-08-07 20:27:43,898]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = d185db82-06b5-46b5-8666-d512b7e1c4d9
TID: [-1234] [] [2024-08-07 20:27:43,899]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2489, SOCKET_TIMEOUT = 180000, CORRELATION_ID = d185db82-06b5-46b5-8666-d512b7e1c4d9
TID: [-1234] [] [2024-08-07 20:27:43,900]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 20:27:43,919]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-98867, CORRELATION_ID = d185db82-06b5-46b5-8666-d512b7e1c4d9
TID: [-1234] [] [2024-08-07 20:30:37,529]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-07 20:30:45,662]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2498, SOCKET_TIMEOUT = 180000, CORRELATION_ID = dc6f6418-251f-4347-91ee-41439249681d
TID: [-1234] [] [2024-08-07 20:30:45,663]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 20:30:54,676]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20240807&denNgay=20240807&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20240807&denNgay=20240807&maTthc=
TID: [-1234] [] [2024-08-07 20:30:54,775]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-08-07 20:33:48,984]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 44c9d4b5-fe5a-4e5b-8e84-e2be68ca124c
TID: [-1234] [] [2024-08-07 20:33:48,986]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2512, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 44c9d4b5-fe5a-4e5b-8e84-e2be68ca124c
TID: [-1234] [] [2024-08-07 20:33:48,987]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 20:36:50,489]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = a9db85d6-fd21-49ec-b88d-69fa6e8a08a9
TID: [-1234] [] [2024-08-07 20:36:50,491]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2526, SOCKET_TIMEOUT = 180000, CORRELATION_ID = a9db85d6-fd21-49ec-b88d-69fa6e8a08a9
TID: [-1234] [] [2024-08-07 20:36:50,492]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 20:41:24,756]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 145e4f86-3ac2-4132-aa2e-5e9aa5d9598b
TID: [-1234] [] [2024-08-07 20:41:26,132]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ae61a705-a5e6-4fa4-91aa-451e21c90929
TID: [-1234] [] [2024-08-07 20:41:26,277]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = dd7c4c60-49ff-4138-842e-58ce19981f82
TID: [-1234] [] [2024-08-07 20:41:26,681]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2e7c0163-1eac-47a1-9db8-2b3dbf3a1f79
TID: [-1234] [] [2024-08-07 21:00:39,745]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-07 21:01:05,609]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Manage/SummernoteUploadImage, HEALTH CHECK URL = /Manage/SummernoteUploadImage
TID: [-1234] [] [2024-08-07 21:03:32,983]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20240807&denNgay=20240807&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20240807&denNgay=20240807&maTthc=
TID: [-1234] [] [2024-08-07 21:03:33,084]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-08-07 21:12:07,329]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 21:12:07,331]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:49322, CORRELATION_ID = 4902fa3f-dd2e-442b-a4de-e5ddd31a5477, CONNECTION = http-incoming-101118
TID: [-1234] [] [2024-08-07 21:12:07,332]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 4902fa3f-dd2e-442b-a4de-e5ddd31a5477
TID: [-1234] [] [2024-08-07 21:12:07,333]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2564, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 4902fa3f-dd2e-442b-a4de-e5ddd31a5477
TID: [-1234] [] [2024-08-07 21:12:07,336]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 21:12:07,351]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-101118, CORRELATION_ID = 4902fa3f-dd2e-442b-a4de-e5ddd31a5477
TID: [-1234] [] [2024-08-07 21:15:42,592]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2580, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 501b00c1-6024-4655-8e0e-6753f07e4ef2
TID: [-1234] [] [2024-08-07 21:15:42,594]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 21:21:09,278]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2591, SOCKET_TIMEOUT = 180000, CORRELATION_ID = df1898ac-f695-45f7-ba6d-050aa8db1c10
TID: [-1234] [] [2024-08-07 21:21:09,281]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 21:24:43,777]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 2848e59f-d35d-4897-a295-c9b4eb5a55a7
TID: [-1234] [] [2024-08-07 21:24:43,778]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2603, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 2848e59f-d35d-4897-a295-c9b4eb5a55a7
TID: [-1234] [] [2024-08-07 21:24:43,780]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 21:24:45,317]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2595, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 63f1abee-1d74-451e-9c4a-d188e5811e17
TID: [-1234] [] [2024-08-07 21:24:45,319]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 21:25:50,983]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20240807&denNgay=20240807&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20240807&denNgay=20240807&maTthc=
TID: [-1234] [] [2024-08-07 21:25:51,031]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-08-07 21:27:48,998]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2609, SOCKET_TIMEOUT = 180000, CORRELATION_ID = de1cd7c3-1a0d-43fc-b56c-f59608b23d7e
TID: [-1234] [] [2024-08-07 21:27:49,000]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 21:30:53,433]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2622, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 333e3300-d677-4fad-959f-da1aea3e4792
TID: [-1234] [] [2024-08-07 21:30:53,436]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 21:30:53,960]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-07 21:33:58,551]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2bc05048-d565-4615-85ba-9311a73bcd68
TID: [-1234] [] [2024-08-07 21:33:58,685]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 21:33:58,685]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2639, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 94fd1736-297b-461f-88a9-5683ca1e9b79
TID: [-1234] [] [2024-08-07 21:33:58,686]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:32876, CORRELATION_ID = 94fd1736-297b-461f-88a9-5683ca1e9b79, CONNECTION = http-incoming-102258
TID: [-1234] [] [2024-08-07 21:33:58,687]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 21:33:58,704]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-102258, CORRELATION_ID = 94fd1736-297b-461f-88a9-5683ca1e9b79
TID: [-1234] [] [2024-08-07 21:37:05,872]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 21:37:05,874]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:38694, CORRELATION_ID = 1658eac5-fa01-4960-9c00-9034d089dca1, CONNECTION = http-incoming-102345
TID: [-1234] [] [2024-08-07 21:37:05,881]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2652, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 1658eac5-fa01-4960-9c00-9034d089dca1
TID: [-1234] [] [2024-08-07 21:37:05,883]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 21:37:05,897]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-102345, CORRELATION_ID = 1658eac5-fa01-4960-9c00-9034d089dca1
TID: [-1234] [] [2024-08-07 21:41:35,665]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1079638c-fde7-4f18-bb98-d31d60de5014
TID: [-1234] [] [2024-08-07 21:41:38,310]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c810929f-41ca-492f-973d-d8565675f6f9
TID: [-1234] [] [2024-08-07 21:41:38,904]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8c9433b9-a0f2-4d5d-afcb-ee194199a33e
TID: [-1234] [] [2024-08-07 21:41:40,628]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f1df5db3-6094-4e8f-8bf4-6ce8150b45b4
TID: [-1234] [] [2024-08-07 21:41:41,821]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ea47b631-d248-47f9-b049-84b8e9f8903d
TID: [-1234] [] [2024-08-07 21:41:42,375]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 168c329b-df6f-48f3-9eda-099e950e7f1e
TID: [-1234] [] [2024-08-07 21:50:10,060]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20240807&denNgay=20240807&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20240807&denNgay=20240807&maTthc=
TID: [-1234] [] [2024-08-07 21:50:10,160]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-08-07 22:00:54,171]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-07 22:02:03,825]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : HoSoIGATE--v1.0.0_APIproductionEndpoint with address http://************:8095/savis/categories/api/v1/document has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-08-07 22:02:03,827]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : HoSoIGATE--v1.0.0_APIproductionEndpoint with address http://************:8095/savis/categories/api/v1/document - current suspend duration is : 30000ms - Next retry after : Wed Aug 07 22:02:33 ICT 2024
TID: [-1234] [] [2024-08-07 22:02:03,828]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-08-07 22:02:03,842]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : HoSoIGATE--v1.0.0_APIproductionEndpoint with address http://************:8095/savis/categories/api/v1/document has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-08-07 22:02:03,843]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : HoSoIGATE--v1.0.0_APIproductionEndpoint with address http://************:8095/savis/categories/api/v1/document - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Wed Aug 07 22:02:33 ICT 2024
TID: [-1234] [] [2024-08-07 22:02:03,843]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-08-07 22:02:03,850]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:90718b9d-3765-41fe-987a-92a915821806; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8095/savis/categories/api/v1/document/api_thongke_dvc, Received through API : admin--HoSoIGATE:v1.0.0, CORRELATION_ID = *************-4c29-9b71-c24e00c08b77
TID: [-1234] [] [2024-08-07 22:02:03,850]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:57d71002-6cf9-4391-9103-88a92feafe5c; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8095/savis/categories/api/v1/document/api_thongke_dvc, Received through API : admin--HoSoIGATE:v1.0.0, CORRELATION_ID = 2fa7de95-95ed-477c-9cc2-e9a9be8439fc
TID: [-1234] [] [2024-08-07 22:02:04,783]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : HoSoIGATE--v1.0.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-08-07 22:02:04,910]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : HoSoIGATE--v1.0.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-08-07 22:02:04,971]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : HoSoIGATE--v1.0.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-08-07 22:02:05,017]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : HoSoIGATE--v1.0.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-08-07 22:02:06,057]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : HoSoIGATE--v1.0.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-08-07 22:02:06,104]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : HoSoIGATE--v1.0.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-08-07 22:02:06,142]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : HoSoIGATE--v1.0.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-08-07 22:02:06,196]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : HoSoIGATE--v1.0.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-08-07 22:02:51,745]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8095, TARGET_CONTEXT = http://************:8095/savis/categories/api/v1/document/api_thongke_dvc, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--HoSoIGATE:v1.0.0, REMOTE_ADDRESS = /************:8095, CONNECTION = http-outgoing-2685, SOCKET_TIMEOUT = 180000, CORRELATION_ID = *************-4c29-9b71-c24e00c08b77
TID: [-1234] [] [2024-08-07 22:03:01,537]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8095, TARGET_CONTEXT = http://************:8095/savis/categories/api/v1/document/api_thongke_dvc, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--HoSoIGATE:v1.0.0, REMOTE_ADDRESS = /************:8095, CONNECTION = http-outgoing-2686, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 2fa7de95-95ed-477c-9cc2-e9a9be8439fc
TID: [-1234] [] [2024-08-07 22:11:54,992]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2689, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 9b963fa6-9627-4c55-bc4e-831504f9277f
TID: [-1234] [] [2024-08-07 22:11:54,995]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 22:14:29,005]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : HoSoIGATE--v1.0.0_APIproductionEndpoint with address http://************:8095/savis/categories/api/v1/document currently SUSPENDED will now be marked active since it processed its last message
TID: [-1234] [] [2024-08-07 22:15:22,524]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 22:15:22,525]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:54352, CORRELATION_ID = 964d75f4-958b-4e16-94a2-79dc7d144fdd, CONNECTION = http-incoming-104601
TID: [-1234] [] [2024-08-07 22:15:22,644]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2703, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 964d75f4-958b-4e16-94a2-79dc7d144fdd
TID: [-1234] [] [2024-08-07 22:15:22,646]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 22:15:22,675]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-104601, CORRELATION_ID = 964d75f4-958b-4e16-94a2-79dc7d144fdd
TID: [-1234] [] [2024-08-07 22:16:33,855]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : HoSoIGATE--v1.0.0_APIproductionEndpoint with address http://************:8095/savis/categories/api/v1/document has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-08-07 22:16:33,856]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : HoSoIGATE--v1.0.0_APIproductionEndpoint with address http://************:8095/savis/categories/api/v1/document - current suspend duration is : 30000ms - Next retry after : Wed Aug 07 22:17:03 ICT 2024
TID: [-1234] [] [2024-08-07 22:16:33,857]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-08-07 22:16:33,871]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:f61daf71-1f07-49f0-950f-91a1b264c437; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8095/savis/categories/api/v1/document/api_thongke_dvc, Received through API : admin--HoSoIGATE:v1.0.0, CORRELATION_ID = 53ea9fca-2e1b-4ce7-aae8-f15554aac2e8
TID: [-1234] [] [2024-08-07 22:16:34,358]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : HoSoIGATE--v1.0.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-08-07 22:16:34,409]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : HoSoIGATE--v1.0.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-08-07 22:16:34,457]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : HoSoIGATE--v1.0.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-08-07 22:16:34,501]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : HoSoIGATE--v1.0.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-08-07 22:16:48,856]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : HoSoIGATE--v1.0.0_APIproductionEndpoint with address http://************:8095/savis/categories/api/v1/document has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-08-07 22:16:48,857]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : HoSoIGATE--v1.0.0_APIproductionEndpoint with address http://************:8095/savis/categories/api/v1/document - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Wed Aug 07 22:17:18 ICT 2024
TID: [-1234] [] [2024-08-07 22:16:48,857]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-08-07 22:16:48,869]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:f141fe14-8bd4-4322-a211-bc07d5864f44; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8095/savis/categories/api/v1/document/api_thongke_dvc, Received through API : admin--HoSoIGATE:v1.0.0, CORRELATION_ID = 463426af-5623-4277-959e-1f04f9f7fa84
TID: [-1234] [] [2024-08-07 22:16:52,735]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : HoSoIGATE--v1.0.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-08-07 22:16:52,877]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : HoSoIGATE--v1.0.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-08-07 22:16:52,967]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : HoSoIGATE--v1.0.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-08-07 22:16:53,029]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--HoSoIGATE:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : HoSoIGATE--v1.0.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-08-07 22:17:29,812]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8095, TARGET_CONTEXT = http://************:8095/savis/categories/api/v1/document/api_thongke_dvc, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--HoSoIGATE:v1.0.0, REMOTE_ADDRESS = /************:8095, CONNECTION = http-outgoing-2706, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 53ea9fca-2e1b-4ce7-aae8-f15554aac2e8
TID: [-1234] [] [2024-08-07 22:17:35,084]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8095, TARGET_CONTEXT = http://************:8095/savis/categories/api/v1/document/api_thongke_dvc, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--HoSoIGATE:v1.0.0, REMOTE_ADDRESS = /************:8095, CONNECTION = http-outgoing-2707, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 463426af-5623-4277-959e-1f04f9f7fa84
TID: [-1234] [] [2024-08-07 22:20:02,265]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 3a19d2da-9d93-4f59-8468-68383c67543c
TID: [-1234] [] [2024-08-07 22:20:02,266]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2696, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 3a19d2da-9d93-4f59-8468-68383c67543c
TID: [-1234] [] [2024-08-07 22:20:02,267]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 22:23:42,938]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2715, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 7187b3e5-e00b-409d-8b40-64e895779cd2
TID: [-1234] [] [2024-08-07 22:23:42,940]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 22:23:44,819]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2726, SOCKET_TIMEOUT = 180000, CORRELATION_ID = ad8c944f-e8c1-40af-aca3-7522590713d0
TID: [-1234] [] [2024-08-07 22:23:44,821]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 22:26:48,408]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2732, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 8f83536d-9d86-4a7d-b2e1-c10610038402
TID: [-1234] [] [2024-08-07 22:26:48,412]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 22:29:48,194]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : HoSoIGATE--v1.0.0_APIproductionEndpoint with address http://************:8095/savis/categories/api/v1/document currently SUSPENDED will now be marked active since it processed its last message
TID: [-1234] [] [2024-08-07 22:29:52,977]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 22:29:52,978]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:39732, CORRELATION_ID = 7519b2ce-40b7-40f5-81d3-0525be95f293, CONNECTION = http-incoming-105399
TID: [-1234] [] [2024-08-07 22:29:52,988]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 7519b2ce-40b7-40f5-81d3-0525be95f293
TID: [-1234] [] [2024-08-07 22:29:52,989]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2749, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 7519b2ce-40b7-40f5-81d3-0525be95f293
TID: [-1234] [] [2024-08-07 22:29:52,991]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 22:29:53,008]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-105399, CORRELATION_ID = 7519b2ce-40b7-40f5-81d3-0525be95f293
TID: [-1234] [] [2024-08-07 22:32:59,275]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a5f427c4-2626-467e-a6b5-ec2b33184e67
TID: [-1234] [] [2024-08-07 22:32:59,636]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2764, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 0a768e45-b0a1-46f8-a82c-7db7be8ad9c1
TID: [-1234] [] [2024-08-07 22:32:59,637]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 22:32:59,654]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:40516, CORRELATION_ID = 0a768e45-b0a1-46f8-a82c-7db7be8ad9c1, CONNECTION = http-incoming-105498
TID: [-1234] [] [2024-08-07 22:32:59,750]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b7e5081d-25cd-4bf6-8cd0-a28e9936e36b
TID: [-1234] [] [2024-08-07 22:33:00,900]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8968c9a4-e4f0-483b-9d61-625b9e63322e
TID: [-1234] [] [2024-08-07 22:33:01,050]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-07 22:36:06,211]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2786, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 345919b9-83cf-440a-a40f-af6dbd533f4b
TID: [-1234] [] [2024-08-07 22:36:06,213]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 22:41:53,448]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c14b9195-3b51-4417-bc32-238626faaf37
TID: [-1234] [] [2024-08-07 22:43:59,931]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 655291be-110b-45ed-af0b-d65656067325
TID: [-1234] [] [2024-08-07 23:03:02,850]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-08-07 23:10:38,526]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2821, SOCKET_TIMEOUT = 180000, CORRELATION_ID = f9a30fbb-3c1b-47fc-a241-5e75b9088b36
TID: [-1234] [] [2024-08-07 23:10:38,529]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 23:14:05,816]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 23:14:05,817]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:47192, CORRELATION_ID = bd7d343b-c8df-411e-bc80-2076fac94844, CONNECTION = http-incoming-107844
TID: [-1234] [] [2024-08-07 23:14:05,823]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = bd7d343b-c8df-411e-bc80-2076fac94844
TID: [-1234] [] [2024-08-07 23:14:05,824]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2824, SOCKET_TIMEOUT = 180000, CORRELATION_ID = bd7d343b-c8df-411e-bc80-2076fac94844
TID: [-1234] [] [2024-08-07 23:14:05,825]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 23:14:05,844]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-107844, CORRELATION_ID = bd7d343b-c8df-411e-bc80-2076fac94844
TID: [-1234] [] [2024-08-07 23:14:06,401]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 546158f2-4aee-4761-827b-cd489f9630b5
TID: [-1234] [] [2024-08-07 23:19:08,440]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2844, SOCKET_TIMEOUT = 180000, CORRELATION_ID = bb9167f2-813a-4380-8229-86566dd08364
TID: [-1234] [] [2024-08-07 23:19:08,442]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 23:22:44,291]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 23:22:44,292]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:38954, CORRELATION_ID = ec990486-1f58-42c3-be99-b1bbeabb1b72, CONNECTION = http-incoming-108409
TID: [-1234] [] [2024-08-07 23:22:44,295]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = ec990486-1f58-42c3-be99-b1bbeabb1b72
TID: [-1234] [] [2024-08-07 23:22:44,296]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2857, SOCKET_TIMEOUT = 180000, CORRELATION_ID = ec990486-1f58-42c3-be99-b1bbeabb1b72
TID: [-1234] [] [2024-08-07 23:22:44,298]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 23:22:44,315]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-108409, CORRELATION_ID = ec990486-1f58-42c3-be99-b1bbeabb1b72
TID: [-1234] [] [2024-08-07 23:22:48,423]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2853, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 72c615c6-7db2-42b0-ac4e-efa3f0b46b24
TID: [-1234] [] [2024-08-07 23:22:48,425]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 23:25:52,388]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 0d489e38-092e-4b1a-bf85-385219a57484
TID: [-1234] [] [2024-08-07 23:25:52,389]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2868, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 0d489e38-092e-4b1a-bf85-385219a57484
TID: [-1234] [] [2024-08-07 23:25:52,391]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 23:28:54,906]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2883, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 177a924d-5175-450c-a37a-720e04cd4477
TID: [-1234] [] [2024-08-07 23:28:54,908]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 23:31:59,219]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-08-07 23:31:59,221]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:46706, CORRELATION_ID = 7c7ccc4c-bbee-47ab-b5cc-30d088aa4e68, CONNECTION = http-incoming-108658
TID: [-1234] [] [2024-08-07 23:31:59,339]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2899, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 7c7ccc4c-bbee-47ab-b5cc-30d088aa4e68
TID: [-1234] [] [2024-08-07 23:31:59,341]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 23:31:59,360]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-108658, CORRELATION_ID = 7c7ccc4c-bbee-47ab-b5cc-30d088aa4e68
TID: [-1234] [] [2024-08-07 23:35:00,633]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-2910, SOCKET_TIMEOUT = 180000, CORRELATION_ID = a7ff5478-7aea-469f-83a7-7d9a59eefd11
TID: [-1234] [] [2024-08-07 23:35:00,635]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-08-07 23:35:01,175]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
