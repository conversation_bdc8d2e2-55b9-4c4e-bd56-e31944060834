TID: [-1234] [] [2024-11-11 00:00:23,391]  WARN {org.apache.synapse.commons.util.MiscellaneousUtil} - Error loading properties from a file at from the System defined location: access-log.properties
TID: [-1234] [] [2024-11-11 00:02:23,245]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-11 00:06:39,646]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d1b6189c-619e-4487-a8a9-deec8aa5ef40
TID: [-1234] [] [2024-11-11 00:06:39,999]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b9ca1e16-eacb-40fb-8fda-f244bc809ef2
TID: [-1234] [] [2024-11-11 00:06:40,079]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a8c9b1dd-6a67-487f-89e6-243f91ace6af
TID: [-1234] [] [2024-11-11 00:06:44,003]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7a45525e-f932-4632-acba-d33c36963222
TID: [-1234] [] [2024-11-11 00:06:46,301]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 87f01609-02a8-4da6-954d-e1e1fff59cc9
TID: [-1234] [] [2024-11-11 00:06:46,746]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cf93b072-eef9-41c0-a8f4-5fbe2ff6f1bc
TID: [-1234] [] [2024-11-11 00:06:48,390]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5750786c-5e1f-47e7-991b-f2bb264d65d2
TID: [-1234] [] [2024-11-11 00:06:50,246]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f90648bc-b667-44e4-a69f-9ef0d0e7322f
TID: [-1234] [] [2024-11-11 00:06:51,546]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5939a0fa-64d8-4c76-bb93-44f8ae6588c4
TID: [-1234] [] [2024-11-11 00:06:52,783]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2cfab7e4-a612-4c16-9a44-5c933ed66a48
TID: [-1234] [] [2024-11-11 00:06:54,044]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c4b9c981-38d7-4bec-87ea-113d09410a99
TID: [-1234] [] [2024-11-11 00:35:08,302]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 00a96a8a-17ba-4fc7-bc8c-d0c04b0ef270
TID: [-1234] [] [2024-11-11 00:43:35,349]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-11 01:06:57,164]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 57df57a4-6f0a-456f-a0e8-ca09386c581a
TID: [-1234] [] [2024-11-11 01:06:59,046]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b31facc5-4304-4766-976c-6df0fab1897f
TID: [-1234] [] [2024-11-11 01:07:03,681]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 644242d0-0edd-4804-b653-73bf0a991caa
TID: [-1234] [] [2024-11-11 01:07:05,007]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6023b432-853f-4d3c-8481-9656b026cfc6
TID: [-1234] [] [2024-11-11 01:07:07,354]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 32dfe54b-6f6d-4665-8da4-f426c7602470
TID: [-1234] [] [2024-11-11 01:07:07,710]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ebdbe285-9fba-4119-a5b9-dac3017a0994
TID: [-1234] [] [2024-11-11 01:07:13,547]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d8fcc017-1836-4d89-827f-697209fec8ec
TID: [-1234] [] [2024-11-11 01:13:35,518]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-11 01:35:01,315]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1df43fbe-b39d-4d19-8ad5-0c0b886fec32
TID: [-1234] [] [2024-11-11 01:43:35,748]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-11 02:01:37,748]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-11-11 02:01:37,749]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/ChuyenHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:47920, CORRELATION_ID = 41d60619-3312-4790-affa-029bf5bcf497, CONNECTION = http-incoming-1007902
TID: [-1234] [] [2024-11-11 02:01:37,784]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-11-11 02:01:37,785]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/ChuyenHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:47894, CORRELATION_ID = 3ddad0b2-2bc0-4c8a-9d7f-756ee3755055, CONNECTION = http-incoming-1007899
TID: [-1234] [] [2024-11-11 02:01:37,864]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-11-11 02:01:37,865]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/ChuyenHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:47928, CORRELATION_ID = 8b577f0e-8a0b-44b1-a670-8ffd162c9568, CONNECTION = http-incoming-1007904
TID: [-1234] [] [2024-11-11 02:01:37,868]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 3ddad0b2-2bc0-4c8a-9d7f-756ee3755055
TID: [-1234] [] [2024-11-11 02:01:37,869]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 84b2dc4e-e355-4544-91da-59c9c6970c9a
TID: [-1234] [] [2024-11-11 02:01:37,869]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/ChuyenHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-54205, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 3ddad0b2-2bc0-4c8a-9d7f-756ee3755055
TID: [-1234] [] [2024-11-11 02:01:37,869]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/KetThucHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-54210, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 84b2dc4e-e355-4544-91da-59c9c6970c9a
TID: [-1234] [] [2024-11-11 02:01:37,870]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-11-11 02:01:37,870]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-11-11 02:01:37,873]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = e298c8dc-978d-4f95-892c-ff84e3ee96e7
TID: [-1234] [] [2024-11-11 02:01:37,873]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 09b2fbfa-8d82-48d9-b6b7-b9974fc957a6
TID: [-1234] [] [2024-11-11 02:01:37,874]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/ChuyenHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-54207, SOCKET_TIMEOUT = 180000, CORRELATION_ID = e298c8dc-978d-4f95-892c-ff84e3ee96e7
TID: [-1234] [] [2024-11-11 02:01:37,874]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/ChuyenHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-54209, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 09b2fbfa-8d82-48d9-b6b7-b9974fc957a6
TID: [-1234] [] [2024-11-11 02:01:37,875]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-11-11 02:01:37,875]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-11-11 02:01:37,876]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 8b577f0e-8a0b-44b1-a670-8ffd162c9568
TID: [-1234] [] [2024-11-11 02:01:37,876]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/ChuyenHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-54211, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 8b577f0e-8a0b-44b1-a670-8ffd162c9568
TID: [-1234] [] [2024-11-11 02:01:37,876]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = a8099995-0129-4e57-acb5-a21d9f7c6dc1
TID: [-1234] [] [2024-11-11 02:01:37,877]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/ChuyenHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-54206, SOCKET_TIMEOUT = 180000, CORRELATION_ID = a8099995-0129-4e57-acb5-a21d9f7c6dc1
TID: [-1234] [] [2024-11-11 02:01:37,877]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-11-11 02:01:37,878]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-11-11 02:01:37,882]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 41d60619-3312-4790-affa-029bf5bcf497
TID: [-1234] [] [2024-11-11 02:01:37,883]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/ChuyenHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-54208, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 41d60619-3312-4790-affa-029bf5bcf497
TID: [-1234] [] [2024-11-11 02:01:37,884]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-11-11 02:01:37,922]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-1007899, CORRELATION_ID = 3ddad0b2-2bc0-4c8a-9d7f-756ee3755055
TID: [-1234] [] [2024-11-11 02:01:37,934]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-1007904, CORRELATION_ID = 8b577f0e-8a0b-44b1-a670-8ffd162c9568
TID: [-1234] [] [2024-11-11 02:01:37,942]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-1007902, CORRELATION_ID = 41d60619-3312-4790-affa-029bf5bcf497
TID: [-1234] [] [2024-11-11 02:07:43,338]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d6893f65-350a-40f5-976d-8567e50606f1
TID: [-1234] [] [2024-11-11 02:07:43,782]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 84967866-1d69-4801-ad8d-ee1e26493ffc
TID: [-1234] [] [2024-11-11 02:07:43,949]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e8ded436-31f4-4a95-b488-0b001bfdd953
TID: [-1234] [] [2024-11-11 02:07:46,453]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 69c819fe-589b-4475-b0ed-32aac6d87039
TID: [-1234] [] [2024-11-11 02:07:48,298]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9e2a7ba9-8ba1-493d-b837-9e1f413b6758
TID: [-1234] [] [2024-11-11 02:07:51,635]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9ec997cc-0371-4aa5-94d5-3f0d2357c9b7
TID: [-1234] [] [2024-11-11 02:07:51,853]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cbdabb16-76a9-45bd-85fa-8c3ded6b5c56
TID: [-1234] [] [2024-11-11 02:07:52,741]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a0368975-47ef-4c6c-b0a6-4dbfc7e9050e
TID: [-1234] [] [2024-11-11 02:07:54,593]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2d3b3270-76fc-485a-bb8b-f9b7bce5c851
TID: [-1234] [] [2024-11-11 02:13:35,998]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-11 02:34:59,386]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 77320278-c628-4ff9-a1df-43b1b9c01157
TID: [-1234] [] [2024-11-11 02:43:36,271]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-11 03:06:57,577]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0a657973-31f2-4c19-9de5-3fae230485e3
TID: [-1234] [] [2024-11-11 03:06:58,892]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d112b5c4-b4be-43e3-8bcf-d2ba9545fe1e
TID: [-1234] [] [2024-11-11 03:07:00,465]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ddd3ae5b-8d55-4fea-aba6-9a6776f95679
TID: [-1234] [] [2024-11-11 03:07:00,860]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 90bb224f-5b29-49fe-ac8a-0b5c9fae8c1c
TID: [-1234] [] [2024-11-11 03:07:03,315]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 60749f13-aebe-44b6-81ee-72ef842a48cb
TID: [-1234] [] [2024-11-11 03:07:08,408]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 360b98a8-3d29-4a55-8f75-a50cc482455d
TID: [-1234] [] [2024-11-11 03:07:09,079]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9526494f-68eb-4595-b92e-e5a84e22b08d
TID: [-1234] [] [2024-11-11 03:07:09,086]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ed4c5e94-77e8-4264-8c1e-cdf2d1fd331e
TID: [-1234] [] [2024-11-11 03:07:09,682]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d5fd8d15-0881-4fbe-b814-b52cd5ab04a6
TID: [-1234] [] [2024-11-11 03:07:09,776]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5e137290-877a-4ef7-bdbd-cf78763b83a0
TID: [-1234] [] [2024-11-11 03:13:36,551]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-11 03:43:36,815]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-11 04:06:17,157]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9ba27f17-f7a8-4c7b-9823-07770fee14c4
TID: [-1234] [] [2024-11-11 04:06:17,524]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 01283390-df7b-4802-978f-b14dd01ec8bb
TID: [-1234] [] [2024-11-11 04:06:18,624]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 02fc3408-2da6-4d3c-bbc9-228653f77aa1
TID: [-1234] [] [2024-11-11 04:06:19,028]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ffe75c07-9000-4112-84ff-a0f00f49cb0b
TID: [-1234] [] [2024-11-11 04:06:19,321]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5197280e-6653-4e9c-bfd5-36604ab930a4
TID: [-1234] [] [2024-11-11 04:06:23,590]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5a00a6f8-75b4-4941-8c56-df91dfbeeaa6
TID: [-1234] [] [2024-11-11 04:06:24,264]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 92487e70-705c-4751-8138-cffc093b76fc
TID: [-1234] [] [2024-11-11 04:06:25,972]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fc2d24b6-1f7b-4bea-9d99-1896a041f838
TID: [-1234] [] [2024-11-11 04:06:27,976]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ff2502e7-29da-407d-8a0c-b6d16a158de0
TID: [-1234] [] [2024-11-11 04:06:33,814]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5db77a4f-a1fe-4c70-8cb8-3784a717ba80
TID: [-1234] [] [2024-11-11 04:13:36,930]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-11 04:34:59,622]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 568a4acd-c7d7-49ee-b3e4-59bdf82ab5e6
TID: [-1234] [] [2024-11-11 04:59:04,925]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-11 05:06:29,661]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0b09bf15-e6ed-41de-bab0-bc970f95bb14
TID: [-1234] [] [2024-11-11 05:06:30,184]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 52af4e0a-e03c-425e-b389-778e17c69b99
TID: [-1234] [] [2024-11-11 05:06:30,888]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d1911246-9069-4894-bb5b-2b214716c02d
TID: [-1234] [] [2024-11-11 05:06:31,996]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cc1f5e22-c90e-4aed-adc1-8bc037e88d27
TID: [-1234] [] [2024-11-11 05:06:34,208]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 940ed7af-ed95-4867-8067-eeb39256e615
TID: [-1234] [] [2024-11-11 05:06:36,570]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b0834a27-e689-4b3c-89dc-06d7100634f2
TID: [-1234] [] [2024-11-11 05:06:38,349]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = df167be2-3b5e-4953-8b40-3c81a09194f5
TID: [-1234] [] [2024-11-11 05:06:39,081]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8fdc7400-40a1-443d-894d-c3be56699426
TID: [-1234] [] [2024-11-11 05:06:41,967]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f2701a45-5078-4ed5-8edd-87aed490a694
TID: [-1234] [] [2024-11-11 05:29:26,630]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-11 05:59:27,008]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-11 06:06:29,643]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7dec15bb-b011-4642-86dd-8e0a1bd08b34
TID: [-1234] [] [2024-11-11 06:06:29,833]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6b11a61e-82ae-4724-ae1f-d754c0dc1b6a
TID: [-1234] [] [2024-11-11 06:06:32,393]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b634beb7-cc5b-4648-9de3-dfd659605c89
TID: [-1234] [] [2024-11-11 06:06:32,880]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 83f15370-d264-47da-8be4-8e42ede49a61
TID: [-1234] [] [2024-11-11 06:06:33,814]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b62c7cc2-2519-4482-a574-bdfce085fe86
TID: [-1234] [] [2024-11-11 06:06:34,462]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c9192d0f-c867-4f1e-92f2-b0dae4a91afb
TID: [-1234] [] [2024-11-11 06:06:38,009]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0d52858d-c0b6-426d-ab98-47148f84fa06
TID: [-1234] [] [2024-11-11 06:06:40,012]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 84f6085e-fc51-4766-bb02-b16e9bd57523
TID: [-1234] [] [2024-11-11 06:06:43,686]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 57e5c90a-88ad-4ad0-b762-5986096fb274
TID: [-1234] [] [2024-11-11 06:06:43,728]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a607fa37-0de6-4a04-84d9-592e704d4844
TID: [-1234] [] [2024-11-11 06:29:27,222]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-11 06:59:27,465]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-11 07:06:41,177]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7c566c4d-01a2-465b-92b8-e27ed839cc21
TID: [-1234] [] [2024-11-11 07:06:41,625]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 600de3e2-4259-4412-bd49-dc0133c9666a
TID: [-1234] [] [2024-11-11 07:06:43,962]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7f34857c-4b53-4643-953f-3bd460b00725
TID: [-1234] [] [2024-11-11 07:06:45,813]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 10bf7e03-4059-44c7-bbb7-384376ac56df
TID: [-1234] [] [2024-11-11 07:06:46,971]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a8728f03-744e-4e12-b70a-84f3d9d6c82c
TID: [-1234] [] [2024-11-11 07:06:49,252]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 99ac152a-b5d2-412e-b9de-3d5a957b9fa9
TID: [-1234] [] [2024-11-11 07:06:51,054]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 479f3bca-a632-4b45-8641-b0af6aa596f0
TID: [-1234] [] [2024-11-11 07:06:51,236]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 15c57431-3e77-4eb7-9340-eaa6788a3feb
TID: [-1234] [] [2024-11-11 07:06:54,084]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 02a9ca11-240d-46ce-91f1-c56d4879131d
TID: [-1234] [] [2024-11-11 07:29:27,757]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-11 07:35:59,445]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241111&denNgay=20241111&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241111&denNgay=20241111&maTthc=
TID: [-1234] [] [2024-11-11 07:35:59,488]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-11 07:59:28,130]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-11 08:07:13,564]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f1798bc5-f156-46fa-a19d-dd5fab18d3c1
TID: [-1234] [] [2024-11-11 08:07:14,374]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 874b0127-4094-4f42-a3e0-05fc030959a5
TID: [-1234] [] [2024-11-11 08:07:15,439]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 15774243-c89b-41c4-848e-fac385e7fdd4
TID: [-1234] [] [2024-11-11 08:07:15,867]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c635c9a0-248b-4f35-ab0f-ac5f0c9fc494
TID: [-1234] [] [2024-11-11 08:07:15,907]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 144680d3-d97d-40f5-b415-7de22736d38d
TID: [-1234] [] [2024-11-11 08:07:19,319]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6f9c5824-c41d-4f88-9a9f-ac9cc42388cc
TID: [-1234] [] [2024-11-11 08:07:19,373]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6d217b88-3b40-4846-9613-cebb044c30e7
TID: [-1234] [] [2024-11-11 08:22:50,636]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0302eecc-cd14-475e-aace-25e470c9e8e4
TID: [-1234] [] [2024-11-11 08:29:28,443]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-11 08:59:28,757]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-11 09:07:03,178]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f0722ef8-3135-4088-b889-c42b5253258b
TID: [-1234] [] [2024-11-11 09:07:03,282]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f2bef9cf-f7d7-479c-8cc0-f948e126eb70
TID: [-1234] [] [2024-11-11 09:07:04,230]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 21e4d0cb-b41f-4e9e-8180-655527579941
TID: [-1234] [] [2024-11-11 09:07:04,338]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0ac55bda-eccf-4099-b4aa-86a7b7dd3150
TID: [-1234] [] [2024-11-11 09:07:10,517]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f814f8a6-20d6-4c5c-ae1b-a8145d9e29bb
TID: [-1234] [] [2024-11-11 09:07:10,808]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5b975084-4158-43b8-b4a0-de2767bda1d8
TID: [-1234] [] [2024-11-11 09:07:13,519]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 028b454c-f987-4c0a-86df-ef91f07ad2c1
TID: [-1234] [] [2024-11-11 09:07:13,731]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6c2f5198-fb95-4ef7-b2a1-defa00e811ab
TID: [-1234] [] [2024-11-11 09:15:50,520]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6649f50a-02d0-47b4-9d4b-5bbd0553dc70
TID: [-1234] [] [2024-11-11 09:16:56,362]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241111&denNgay=20241111&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241111&denNgay=20241111&maTthc=
TID: [-1234] [] [2024-11-11 09:16:56,402]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-11-11 09:29:30,090]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241111&denNgay=20241111&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241111&denNgay=20241111&maTthc=
TID: [-1234] [] [2024-11-11 09:29:30,138]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-11 09:29:56,141]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-11 09:48:51,352]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 04dbf25a-c7c0-4ee7-a1db-93d92261ea4c
TID: [-1234] [] [2024-11-11 09:59:56,334]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-11 10:06:45,737]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9c720c6e-4e48-480b-99b5-ca8efd211cf6
TID: [-1234] [] [2024-11-11 10:06:54,809]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 76449a1e-e608-44ac-ac2c-b353aa5d32ad
TID: [-1234] [] [2024-11-11 10:07:00,002]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 63983f0c-057a-4fd0-9156-0e7852c0b37c
TID: [-1234] [] [2024-11-11 10:07:02,152]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f4eab0b3-a409-41db-a0a9-cfe9808d3e12
TID: [-1234] [] [2024-11-11 10:07:04,922]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 17502208-e873-44ad-96ee-7516a633a764
TID: [-1234] [] [2024-11-11 10:07:08,686]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f7291460-8c60-493d-91d8-cba02e1e7364
TID: [-1234] [] [2024-11-11 10:29:57,822]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-11 10:41:50,480]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f46e048f-8671-4027-a45a-5e8d19abacea
TID: [-1234] [] [2024-11-11 10:59:58,061]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-11 11:03:56,771]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241111&denNgay=20241111&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241111&denNgay=20241111&maTthc=
TID: [-1234] [] [2024-11-11 11:03:56,814]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-11 11:03:57,405]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241111&denNgay=20241111&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241111&denNgay=20241111&maTthc=
TID: [-1234] [] [2024-11-11 11:03:57,445]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-11-11 11:04:00,451]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241111&denNgay=20241111&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241111&denNgay=20241111&maTthc=
TID: [-1234] [] [2024-11-11 11:04:00,493]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-11 11:04:04,944]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241111&denNgay=20241111&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241111&denNgay=20241111&maTthc=
TID: [-1234] [] [2024-11-11 11:04:04,987]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-11 11:04:09,780]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241111&denNgay=20241111&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241111&denNgay=20241111&maTthc=
TID: [-1234] [] [2024-11-11 11:04:09,826]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-11 11:05:33,937]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241111&denNgay=20241111&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241111&denNgay=20241111&maTthc=
TID: [-1234] [] [2024-11-11 11:05:33,977]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-11 11:07:05,657]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5f8d19a6-76ba-4ff9-96ba-7a30cff5365d
TID: [-1234] [] [2024-11-11 11:07:22,906]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = eabcabdf-e7bf-410c-a584-8c4b6cb081bc
TID: [-1234] [] [2024-11-11 11:07:24,317]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9b7d847a-10c3-4cbf-8720-2999edf93a5a
TID: [-1234] [] [2024-11-11 11:28:38,227]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a8b7bcab-4aca-4d75-8cd3-8a17ec8b3054
TID: [-1234] [] [2024-11-11 11:31:41,817]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-11 11:33:08,839]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pvucnh, HEALTH CHECK URL = /pvucnh
TID: [-1234] [] [2024-11-11 11:41:42,072]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /hqkmil, HEALTH CHECK URL = /hqkmil
TID: [-1234] [] [2024-11-11 12:01:42,354]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-11 12:07:18,872]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 99f59b48-301c-42f3-95d7-7dc674270a48
TID: [-1234] [] [2024-11-11 12:07:19,399]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 99859a03-b76f-480c-94aa-f23674508055
TID: [-1234] [] [2024-11-11 12:07:20,006]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5f6851dc-fb76-44f5-ac06-3d53ee58cd4a
TID: [-1234] [] [2024-11-11 12:07:20,969]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9ba9fb79-2326-4a68-b4f3-5d0db0e429c3
TID: [-1234] [] [2024-11-11 12:07:22,219]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8ded63d2-23cc-4e67-9d34-ef53f41d6f4d
TID: [-1234] [] [2024-11-11 12:07:23,205]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 38e11b04-e4f8-487e-b67c-5c6af6358dec
TID: [-1234] [] [2024-11-11 12:07:23,609]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 644107d6-e67b-4f0b-99ab-2535d1c7d24a
TID: [-1234] [] [2024-11-11 12:07:26,318]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4dd5df19-4656-4586-a343-015cb38bdecb
TID: [-1234] [] [2024-11-11 12:27:35,540]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241111&denNgay=20241111&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241111&denNgay=20241111&maTthc=
TID: [-1234] [] [2024-11-11 12:27:35,589]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-11 12:30:21,998]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = aaa52bad-3d53-4aeb-8ea0-73d892ec4ab7
TID: [-1234] [] [2024-11-11 12:31:42,672]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-11 12:35:17,810]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d8d71457-bcdc-4c1e-98f3-84a4f94bbfeb
TID: [-1234] [] [2024-11-11 12:50:32,823]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 64c3043f-4d18-48ea-85a9-70f57a411e3c
TID: [-1234] [] [2024-11-11 13:01:42,986]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-11 13:06:25,656]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 54e326c8-7ad7-43e3-bde6-e7c3dfbe98f8
TID: [-1234] [] [2024-11-11 13:06:26,088]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0d486fa3-549f-473c-b40d-370e40389052
TID: [-1234] [] [2024-11-11 13:06:26,692]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a414fc56-4035-4703-8cd2-f27501e82d3a
TID: [-1234] [] [2024-11-11 13:06:28,583]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 29e5ddfb-2d89-4e74-8363-8ebc2674db59
TID: [-1234] [] [2024-11-11 13:06:30,902]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b6cd0507-1ff9-4da3-8258-c86fcc918646
TID: [-1234] [] [2024-11-11 13:06:31,592]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 994d1543-ab1f-47cd-a1b5-9558c9e076d3
TID: [-1234] [] [2024-11-11 13:06:32,217]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0864aceb-4426-4621-a7b2-1fc2192e8142
TID: [-1234] [] [2024-11-11 13:06:33,759]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 876f1295-457b-4b9b-840c-20e628122afd
TID: [-1234] [] [2024-11-11 13:06:36,226]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4a4565a8-a601-4ed6-a5d9-22f2a5b25c66
TID: [-1234] [] [2024-11-11 13:15:58,309]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1946fc98-cc68-4b5d-bcab-6b30c84e564c
TID: [-1234] [] [2024-11-11 13:31:43,339]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-11 13:41:42,618]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1dabc5e2-e98a-4507-8345-1b3b2b3b381c
TID: [-1234] [] [2024-11-11 14:01:47,298]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-11 14:07:06,329]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 36a615cb-da62-490f-968c-5185d4b887f9
TID: [-1234] [] [2024-11-11 14:07:08,303]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d3c99620-bffd-42b6-8ac7-9de5e346a71a
TID: [-1234] [] [2024-11-11 14:07:09,843]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8224a4d5-3492-4418-8443-57a9775b0449
TID: [-1234] [] [2024-11-11 14:07:10,405]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ad749e34-0d9b-44bc-8aab-d725dcc0cd65
TID: [-1234] [] [2024-11-11 14:07:10,746]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 986e2318-6a1c-46d6-ae40-557d4364fbd8
TID: [-1234] [] [2024-11-11 14:07:11,598]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0d16feb0-5699-46cc-889e-306b413f157d
TID: [-1234] [] [2024-11-11 14:07:13,721]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f2ec44f4-cf45-4128-ab65-bddf5ff1e697
TID: [-1234] [] [2024-11-11 14:07:22,835]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 436f96d2-2c91-4bf8-a312-7afd8c30d3b9
TID: [-1234] [] [2024-11-11 14:31:47,661]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-11 15:01:47,886]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-11 15:10:20,518]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 05af567f-3d4a-4b94-9ba4-edd9dbe7d00e
TID: [-1234] [] [2024-11-11 15:19:47,954]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3b07d146-29c8-4d04-a0a4-9a06353abd88
TID: [-1234] [] [2024-11-11 15:24:04,554]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c9690164-c52d-4500-8703-6e082f7db37e
TID: [-1234] [] [2024-11-11 15:31:48,675]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-11 16:01:49,544]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-11 16:07:14,718]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c6d6938a-87b0-4759-9b9a-1e4d172e662f
TID: [-1234] [] [2024-11-11 16:07:18,517]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8ed095a1-11d5-4063-8c6e-c0ee351acfc3
TID: [-1234] [] [2024-11-11 16:07:22,437]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2fc9fb3d-66b3-44d5-b8b7-4e5d66dccd22
TID: [-1234] [] [2024-11-11 16:07:23,019]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8f6292f0-d16f-444d-ad20-15c3bdaaa6e8
TID: [-1234] [] [2024-11-11 16:08:56,066]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5530b9fd-d0a1-4430-a312-a06e39853400
TID: [-1234] [] [2024-11-11 16:22:56,342]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1460d4d2-1d9a-4da1-8c9e-d4b6dfdf1494
TID: [-1234] [] [2024-11-11 16:31:50,143]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-11 16:32:41,595]  INFO {org.wso2.carbon.identity.application.authentication.framework.store.SessionCleanUpService} - Session Data cleanup task is running successfully for removing expired Data
TID: [-1234] [] [2024-11-11 17:01:52,013]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-11 17:07:33,514]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f26a857b-3937-412d-9473-0de32711d0cd
TID: [-1234] [] [2024-11-11 17:07:34,328]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 297f15f9-6b87-4eac-959e-5edb861afa2b
TID: [-1234] [] [2024-11-11 17:07:36,877]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1e187cac-806b-4a31-a9a5-9de99b231cf9
TID: [-1234] [] [2024-11-11 17:07:39,940]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2d34df6e-a8c4-4a78-b8f1-239bcc4c1df1
TID: [-1234] [] [2024-11-11 17:07:40,948]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8d47cc0a-8d4a-47f7-ae3a-ad86081b52f4
TID: [-1234] [] [2024-11-11 17:07:41,427]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = db14d6b6-5e8b-4857-8e7f-52c5553f6109
TID: [-1234] [] [2024-11-11 17:07:42,069]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 11812a91-93bf-4e25-b561-cb3a4a6d35ba
TID: [-1234] [] [2024-11-11 17:07:42,641]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8e8ccfff-74e2-45bb-b5b1-0e980e34762d
TID: [-1234] [] [2024-11-11 17:31:52,563]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-11 17:58:38,714]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241111&denNgay=20241111&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241111&denNgay=20241111&maTthc=
TID: [-1234] [] [2024-11-11 17:58:38,754]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-11 18:01:52,835]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-11 18:07:16,820]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7bd55293-825d-4873-970a-c1dfa942e132
TID: [-1234] [] [2024-11-11 18:07:17,437]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7f8d02db-0776-41b8-9a2a-64bae6ea318b
TID: [-1234] [] [2024-11-11 18:07:17,780]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5107cc91-9e94-4aad-9306-c802b9f026ee
TID: [-1234] [] [2024-11-11 18:07:18,340]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 68fa8d05-e23e-4ee4-839f-ca0fe81b09df
TID: [-1234] [] [2024-11-11 18:07:19,507]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3e4846cd-47c3-41f0-b7dd-faa041471862
TID: [-1234] [] [2024-11-11 18:07:21,008]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f94be3b3-9003-411f-85bc-eab1534c7750
TID: [-1234] [] [2024-11-11 18:07:24,141]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1fdf25fc-1fb7-400b-b9be-5faab7ab99de
TID: [-1234] [] [2024-11-11 18:07:29,378]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 58c68f46-518d-4656-ab9e-563510521bd5
TID: [-1234] [] [2024-11-11 18:13:39,835]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4e983f96-d0f0-431b-840a-adde1fbb3b3a
TID: [-1234] [] [2024-11-11 18:22:37,087]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241111&denNgay=20241111&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241111&denNgay=20241111&maTthc=
TID: [-1234] [] [2024-11-11 18:22:37,129]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-11 18:31:53,036]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-11 18:35:19,340]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 201211f2-22b1-489a-90d1-bf9afa45c378
TID: [-1234] [] [2024-11-11 19:01:53,323]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-11 19:07:08,623]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a3f4fc69-1839-4b4e-8718-628c864fea4c
TID: [-1234] [] [2024-11-11 19:07:09,811]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7d49f08f-9b3f-48c4-9433-77310ca5c24a
TID: [-1234] [] [2024-11-11 19:07:11,190]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6bf0ed26-4890-40b0-9548-fc6ddf14632f
TID: [-1234] [] [2024-11-11 19:07:12,557]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 94d80cb8-0cc3-445b-b9d6-1ea753090eb2
TID: [-1234] [] [2024-11-11 19:07:12,829]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f0198348-1e93-4292-8441-ca68c9558636
TID: [-1234] [] [2024-11-11 19:07:17,043]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7a34765a-ec9b-423f-a89c-d2593ca0e99b
TID: [-1234] [] [2024-11-11 19:07:18,439]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 619d21d8-73f0-4d20-b5b3-2e2c7f9bef8e
TID: [-1234] [] [2024-11-11 19:07:19,125]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3aef9eb5-c84e-49d4-9bca-9f1f1aa2f9f6
TID: [-1234] [] [2024-11-11 19:25:28,081]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c34a092e-30ce-4bda-a31f-070ed454bc99
TID: [-1234] [] [2024-11-11 19:31:53,590]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-11 20:01:53,769]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-11 20:04:03,002]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241111&denNgay=20241111&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241111&denNgay=20241111&maTthc=
TID: [-1234] [] [2024-11-11 20:04:03,044]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-11 20:06:35,305]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d7c16129-a7ac-4957-b4e0-1ed52d033789
TID: [-1234] [] [2024-11-11 20:06:35,576]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7beca817-354c-4fef-b7e3-7eb62295d62b
TID: [-1234] [] [2024-11-11 20:06:36,390]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 34c18953-7c90-4a95-b646-ed810669eee2
TID: [-1234] [] [2024-11-11 20:06:37,271]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a270c7b9-1056-4eb0-bdd0-0204fda4ae32
TID: [-1234] [] [2024-11-11 20:06:40,269]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 097d7a40-41e6-4c00-bf15-77557b4b18ea
TID: [-1234] [] [2024-11-11 20:06:41,277]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 006b3695-918e-403d-a4d2-082915c8408f
TID: [-1234] [] [2024-11-11 20:06:41,514]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6e2a02e6-7d5a-4333-b13d-c17c7ae3c3cf
TID: [-1234] [] [2024-11-11 20:06:45,089]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 632c1709-7065-452d-9009-56d5a99ee087
TID: [-1234] [] [2024-11-11 20:31:54,801]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-11 21:01:54,898]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-11 21:07:05,966]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 54af657f-259f-4c4d-bee5-cfbf91962260
TID: [-1234] [] [2024-11-11 21:07:08,422]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4e7f97cd-db21-4bdf-a542-1945d8cab9b4
TID: [-1234] [] [2024-11-11 21:07:09,330]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 073b869a-a14e-4df4-8936-ad947a69f175
TID: [-1234] [] [2024-11-11 21:07:10,446]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 77f6b832-df28-4843-9233-c65da5934871
TID: [-1234] [] [2024-11-11 21:07:11,276]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 80a2fc5f-32a0-4768-b606-871c5b354a35
TID: [-1234] [] [2024-11-11 21:07:12,505]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0a18effc-542c-4286-994c-db89f6e7b9b5
TID: [-1234] [] [2024-11-11 21:07:13,498]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 07093168-7151-42b3-b76a-1f10691bf574
TID: [-1234] [] [2024-11-11 21:07:13,724]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6f4ca6cb-04a0-42ab-ac2b-c8f441091533
TID: [-1234] [] [2024-11-11 21:07:13,757]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 212cc6d5-248e-4616-a388-67eb780c897a
TID: [-1234] [] [2024-11-11 21:07:16,712]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 72ee04ef-c850-4d9b-98db-0ae8670e3102
TID: [-1234] [] [2024-11-11 21:07:19,005]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fe43d861-b2ef-4248-ba21-240282b31225
TID: [-1234] [] [2024-11-11 21:29:15,145]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241111&denNgay=20241111&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241111&denNgay=20241111&maTthc=
TID: [-1234] [] [2024-11-11 21:29:15,186]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-11 21:31:55,009]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-11 21:35:13,744]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7e2e9e75-e0a0-4b00-b142-8c4fc8e5c266
TID: [-1234] [] [2024-11-11 22:01:55,535]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-11 22:06:56,080]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 02dfa9d2-2d97-4359-98fa-bfa42b4b6046
TID: [-1234] [] [2024-11-11 22:06:56,355]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fb94bd18-9028-4f68-908c-c0f12ac06105
TID: [-1234] [] [2024-11-11 22:06:58,701]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d96966cf-afe8-4ad3-bfb5-4b2df0bf1140
TID: [-1234] [] [2024-11-11 22:06:59,217]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c3ce71b9-5812-4b9e-bf51-10e7307080a9
TID: [-1234] [] [2024-11-11 22:07:01,093]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ba38e3e3-2ff1-41dc-af0c-1ba1be4a4ccb
TID: [-1234] [] [2024-11-11 22:07:01,259]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 363673f8-d3a1-4802-8839-3b71cfea6423
TID: [-1234] [] [2024-11-11 22:07:02,308]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bf049080-435e-4236-a71d-823cf217fdcd
TID: [-1234] [] [2024-11-11 22:07:02,334]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e8aadf23-29c0-4a0c-803b-90a06cef22aa
TID: [-1234] [] [2024-11-11 22:07:08,033]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 00abf884-ee16-4861-984c-6590c5a52d27
TID: [-1234] [] [2024-11-11 22:31:56,067]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-11 22:58:07,683]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = abcbf1d5-4d6b-4ac2-afc0-e46d04515cf0
TID: [-1234] [] [2024-11-11 23:01:56,172]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-11 23:06:31,580]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7d2fcaa3-11f9-462f-b3ef-0a203267bbaf
TID: [-1234] [] [2024-11-11 23:06:32,235]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = aec5a435-dab4-4b24-a7e3-65ae18b67387
TID: [-1234] [] [2024-11-11 23:06:34,550]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 01950099-cd03-406d-9a98-37fc4e8b0872
TID: [-1234] [] [2024-11-11 23:06:35,298]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0aef94dd-9dc8-4852-9026-a2c916cb3a00
TID: [-1234] [] [2024-11-11 23:06:36,333]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 510ce60e-b0df-4721-8c22-63d50dfd519c
TID: [-1234] [] [2024-11-11 23:06:36,610]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 139c193f-7272-4df4-86c9-612533a7688c
TID: [-1234] [] [2024-11-11 23:06:38,023]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 227a57b7-168a-4cc1-afad-d18e70b005e6
TID: [-1234] [] [2024-11-11 23:06:38,197]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 344c3c9e-b6e0-4c9a-8914-41ce2b39d187
TID: [-1234] [] [2024-11-11 23:06:40,361]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5caad9e4-2201-4990-a835-fc93a48dc7ad
TID: [-1234] [] [2024-11-11 23:06:43,051]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 39e79c41-4029-4ee0-baba-cd92a4951d15
TID: [-1234] [] [2024-11-11 23:31:56,343]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
