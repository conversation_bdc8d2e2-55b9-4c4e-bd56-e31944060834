TID: [-1234] [] [2024-11-14 00:00:26,241]  WARN {org.apache.synapse.commons.util.MiscellaneousUtil} - Error loading properties from a file at from the System defined location: access-log.properties
TID: [-1234] [] [2024-11-14 00:01:39,680]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-14 00:07:05,961]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = da5819c7-87cc-43ab-9a27-32f90f5740d4
TID: [-1234] [] [2024-11-14 00:07:07,311]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 17783cbf-8495-4d8a-8638-0f7681c75a65
TID: [-1234] [] [2024-11-14 00:07:08,579]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9dfbd531-a5b7-49d3-b71c-09d1b7e155ba
TID: [-1234] [] [2024-11-14 00:07:10,954]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cedbdd75-d8c8-4c6a-89ae-9ed7f28b5e49
TID: [-1234] [] [2024-11-14 00:07:14,524]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8dfc8923-397e-4be5-a1ae-54220cbe450b
TID: [-1234] [] [2024-11-14 00:07:14,869]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 64421af7-4cbe-4bcc-bb50-dcdf01b63e67
TID: [-1234] [] [2024-11-14 00:07:15,370]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d4e45fbd-f86f-4a54-9519-c8534b93f68e
TID: [-1234] [] [2024-11-14 00:07:19,469]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1eeb643e-0f6d-4cf3-8eae-96980ed91223
TID: [-1234] [] [2024-11-14 00:07:25,207]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 17d54455-b2da-4fbc-af6a-b7d5a962c75c
TID: [-1234] [] [2024-11-14 00:07:27,322]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7736c9dc-5ddf-466b-b2df-1c1dbed2f613
TID: [-1234] [] [2024-11-14 00:31:39,905]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-14 01:01:39,986]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-14 01:06:34,471]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 40aff8e0-eaa2-4ec7-8e1e-badb2df14776
TID: [-1234] [] [2024-11-14 01:06:36,015]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d643f11e-214f-4f16-93ad-075d0314a89e
TID: [-1234] [] [2024-11-14 01:06:36,026]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2a576e67-e9e4-42a1-9b33-c586e2ede980
TID: [-1234] [] [2024-11-14 01:06:36,846]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 232dbb70-8c97-4bdd-83f8-217dcdaf7766
TID: [-1234] [] [2024-11-14 01:06:38,530]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 393475ae-6dd4-4e36-9690-401624609a1c
TID: [-1234] [] [2024-11-14 01:06:41,288]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 75b9f243-2623-48d7-9a78-34d9c68b84f4
TID: [-1234] [] [2024-11-14 01:06:43,980]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5fc9ad46-b7ed-4ecb-b79e-c2f3564c56aa
TID: [-1234] [] [2024-11-14 01:06:44,942]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b13433b4-6231-4140-9df2-967a857f002f
TID: [-1234] [] [2024-11-14 01:06:50,373]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bbde140a-b2a4-40bf-be8b-377bc2767ecc
TID: [-1234] [] [2024-11-14 01:31:40,498]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-14 02:01:40,742]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-14 02:06:43,469]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d0196a74-f641-4c19-9fe9-af4c455512cd
TID: [-1234] [] [2024-11-14 02:06:43,777]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ed696c1a-f616-42a0-8c0a-9e6c4b4e649d
TID: [-1234] [] [2024-11-14 02:06:44,355]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0f63ff85-09ba-4037-b863-362cf42c6a60
TID: [-1234] [] [2024-11-14 02:06:44,518]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fa77e38d-8e18-4672-84dc-95419470ae9b
TID: [-1234] [] [2024-11-14 02:06:45,476]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 54963268-3a53-4be4-9d94-b4385877fa0a
TID: [-1234] [] [2024-11-14 02:06:47,982]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 45f321f4-4d92-42e2-8cb8-f57f14eba3e9
TID: [-1234] [] [2024-11-14 02:06:57,581]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0c83ab49-a0dc-44af-9ff9-13a9836fce1d
TID: [-1234] [] [2024-11-14 02:06:58,317]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 630d9102-b3db-4c8c-883c-f9cf9c1bb48f
TID: [-1234] [] [2024-11-14 02:31:41,388]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-14 03:01:41,884]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-14 03:06:50,915]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4db1f421-f89e-4a01-b003-1c4225a58440
TID: [-1234] [] [2024-11-14 03:06:53,034]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 50039662-62cc-4c32-8dfc-73a437b6b9ae
TID: [-1234] [] [2024-11-14 03:06:53,745]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 27b45c0d-a5d1-463b-8d8a-07e07c0ec20f
TID: [-1234] [] [2024-11-14 03:06:54,898]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c4ce6ffc-a66d-42df-89b8-5107bbea9a9e
TID: [-1234] [] [2024-11-14 03:06:55,601]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6faea5e1-f5d3-4372-a6d7-6ea37dcba585
TID: [-1234] [] [2024-11-14 03:06:57,516]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 341fa9c3-a694-4cca-b0e4-ee6546e69cf6
TID: [-1234] [] [2024-11-14 03:06:57,620]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f4d3138b-3c58-466e-848a-b1ba3965982e
TID: [-1234] [] [2024-11-14 03:07:02,443]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 93b2a850-c972-4dbc-84fd-2bf1ffc88dfd
TID: [-1234] [] [2024-11-14 03:31:42,054]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-14 03:34:56,588]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 503a1d46-30c5-4b00-96db-a20c8af80d5f
TID: [-1234] [] [2024-11-14 04:01:43,594]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-14 04:07:15,173]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bce3be48-ae13-442e-8750-7d6927774da0
TID: [-1234] [] [2024-11-14 04:07:17,517]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f58b1aa5-d644-474b-94fc-bbd87d288060
TID: [-1234] [] [2024-11-14 04:07:17,984]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5e217117-d2a7-43fd-816c-db094ebf1e07
TID: [-1234] [] [2024-11-14 04:07:18,500]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 76b60f44-85ca-4408-841d-3fa34916989c
TID: [-1234] [] [2024-11-14 04:07:23,320]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 741c52e2-f1ce-4595-baa8-83f4cef74f42
TID: [-1234] [] [2024-11-14 04:07:23,539]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c4759275-dc03-44ba-b1e5-12f9f0560468
TID: [-1234] [] [2024-11-14 04:07:26,205]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 801977d1-c6e9-4bf8-86ba-65ea938cf46b
TID: [-1234] [] [2024-11-14 04:07:26,365]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f40f3207-57c3-4b7c-a476-86381faa8e3f
TID: [-1234] [] [2024-11-14 04:07:31,323]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d4184824-30c4-437d-b46c-1932d387b28b
TID: [-1234] [] [2024-11-14 04:14:32,339] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2024-11-14 04:31:43,827]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-14 05:01:43,942]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-14 05:06:19,919]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 77f6239b-5242-44fc-b727-c2947b01079c
TID: [-1234] [] [2024-11-14 05:06:22,870]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ca27bbd1-d4f8-4749-bee0-e6ff05b3384b
TID: [-1234] [] [2024-11-14 05:06:22,889]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4e18add1-b343-41ef-ae23-a30651bfb086
TID: [-1234] [] [2024-11-14 05:06:23,316]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 549471a2-f3ab-45b7-acad-8d4e55d6e549
TID: [-1234] [] [2024-11-14 05:06:27,307]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 842c870b-a57b-42b0-862c-d7ead498cf7b
TID: [-1234] [] [2024-11-14 05:06:27,954]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2cd585af-7ec4-4794-916c-de8772fd24a7
TID: [-1234] [] [2024-11-14 05:06:34,361]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 368252b8-911b-423f-a8d1-22a1cc3e8f32
TID: [-1234] [] [2024-11-14 05:31:49,357]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-14 05:40:02,816]  WARN {org.wso2.carbon.ui.transports.fileupload.AbstractFileUploadExecutor} - ToolsAnyFileUpload method is not supported
TID: [-1234] [] [2024-11-14 06:00:30,214]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-11-14 06:01:50,178]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-14 06:06:18,472]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6fd20548-b69f-40e1-bc50-b7aad933908d
TID: [-1234] [] [2024-11-14 06:06:19,029]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b6ca8df2-3723-4505-a2ef-8ddecdc6fb9d
TID: [-1234] [] [2024-11-14 06:06:23,119]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 43a904f1-b96d-4890-8aff-b6ed5286a791
TID: [-1234] [] [2024-11-14 06:06:24,250]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a988f8df-7035-40f2-a32a-de789ad191c9
TID: [-1234] [] [2024-11-14 06:06:24,421]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bfe230a9-c46a-436e-a829-24e4674e8c4b
TID: [-1234] [] [2024-11-14 06:06:25,553]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6a58a89c-655d-43f2-b92e-165278275b13
TID: [-1234] [] [2024-11-14 06:06:31,732]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 524f1d28-f18d-4d8a-ad3f-1487281d81b6
TID: [-1234] [] [2024-11-14 06:06:42,062]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e650affd-d3bc-47e4-9d25-cc79a4bdd30a
TID: [-1234] [] [2024-11-14 06:31:50,771]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-14 07:01:51,024]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-14 07:07:24,876]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b69ed392-2fc8-4a48-8f13-eb0415318554
TID: [-1234] [] [2024-11-14 07:07:25,470]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 87d2d679-559e-4488-b5f5-79f4100fa409
TID: [-1234] [] [2024-11-14 07:07:27,834]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 56a2a307-0ddf-4a32-9df0-f46ebbad6ce8
TID: [-1234] [] [2024-11-14 07:07:28,078]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2a393477-15c1-442c-81ec-9366c7d212b2
TID: [-1234] [] [2024-11-14 07:07:29,882]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 37593221-35e1-4647-8bce-94ffb239811a
TID: [-1234] [] [2024-11-14 07:07:31,806]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3ce688c7-3e26-4622-a9b6-3482c70d316f
TID: [-1234] [] [2024-11-14 07:07:41,374]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8c201e1b-4d03-4112-8fc6-bac8a3f4209a
TID: [-1234] [] [2024-11-14 07:07:43,205]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6b02e4ae-2170-4f9c-8732-fbe641dc5f54
TID: [-1234] [] [2024-11-14 07:07:50,033]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 63611d24-2600-4b13-a620-8c573921e418
TID: [-1234] [] [2024-11-14 07:26:09,312]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241114&denNgay=20241114&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241114&denNgay=20241114&maTthc=
TID: [-1234] [] [2024-11-14 07:26:09,361]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-14 07:29:24,894]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b82547ad-7ea2-43ed-b1eb-305494c463bd
TID: [-1234] [] [2024-11-14 07:31:51,170]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-14 08:01:51,554]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-14 08:01:53,872]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241114&denNgay=20241114&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241114&denNgay=20241114&maTthc=
TID: [-1234] [] [2024-11-14 08:05:41,259]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 895dfd93-15bd-43a3-a068-99b5a1976526
TID: [-1234] [] [2024-11-14 08:06:00,891]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9c8d0159-2458-4615-a77d-a8dc2b3002ff
TID: [-1234] [] [2024-11-14 08:13:51,007]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f29c34e8-3753-4ada-833b-d36290713721
TID: [-1234] [] [2024-11-14 08:13:54,030]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a1bc3458-4817-4bbd-b40f-b3e19585a7fe
TID: [-1234] [] [2024-11-14 08:14:00,341]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f3c9e764-db43-48f1-817f-30e698e80d31
TID: [-1234] [] [2024-11-14 08:14:08,372]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 31a6dece-3d78-4018-ae02-0b5732d695ae
TID: [-1234] [] [2024-11-14 08:14:20,273]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9f13d62f-12bf-4f10-8207-33889f920416
TID: [-1234] [] [2024-11-14 08:14:33,744]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0d887910-af62-4509-a84f-9d2eaeb16e62
TID: [-1234] [] [2024-11-14 08:25:27,786]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a880d526-6ff6-420c-b7be-e0ff60434a91
TID: [-1234] [] [2024-11-14 08:31:52,022]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-14 09:02:06,150]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-14 09:13:03,895]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3cbb442a-3052-466c-8d2d-22128752e735
TID: [-1234] [] [2024-11-14 09:13:06,584]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f61c0330-dc80-41d7-b84b-a821ad83f7fb
TID: [-1234] [] [2024-11-14 09:13:08,324]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2a48839e-7297-4ee9-a9a5-d01d7c54b23f
TID: [-1234] [] [2024-11-14 09:13:19,242]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ad52f131-7700-470b-a71f-902b3911216c
TID: [-1234] [] [2024-11-14 09:13:49,215]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9fe91930-e6a7-4452-b4dd-876e9a91e817
TID: [-1234] [] [2024-11-14 09:14:15,712]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b7e8e765-3ac8-4353-a3a0-f0142729bb18
TID: [-1234] [] [2024-11-14 09:15:54,134]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3aad9434-a519-4e89-bdfc-cb1cf8cb3250
TID: [-1234] [] [2024-11-14 09:32:55,315]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-14 09:49:13,098]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241114&denNgay=20241114&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241114&denNgay=20241114&maTthc=
TID: [-1234] [] [2024-11-14 09:49:13,142]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-14 09:49:19,190]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241111&denNgay=20241114&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241111&denNgay=20241114&maTthc=
TID: [-1234] [] [2024-11-14 09:49:19,231]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-14 09:49:32,036]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241111&denNgay=20241114&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241111&denNgay=20241114&maTthc=
TID: [-1234] [] [2024-11-14 09:49:32,087]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-14 09:49:39,095]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241114&denNgay=20241114&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241114&denNgay=20241114&maTthc=
TID: [-1234] [] [2024-11-14 09:49:39,140]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-14 09:49:46,952]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241111&denNgay=20241114&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241111&denNgay=20241114&maTthc=
TID: [-1234] [] [2024-11-14 09:49:47,004]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-14 09:49:58,695]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241114&denNgay=20241114&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241114&denNgay=20241114&maTthc=
TID: [-1234] [] [2024-11-14 09:49:58,742]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-11-14 09:50:03,429]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241111&denNgay=20241114&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241111&denNgay=20241114&maTthc=
TID: [-1234] [] [2024-11-14 09:50:03,477]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-11-14 09:50:29,241]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241114&denNgay=20241114&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241114&denNgay=20241114&maTthc=
TID: [-1234] [] [2024-11-14 09:50:40,076]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241111&denNgay=20241114&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241111&denNgay=20241114&maTthc=
TID: [-1234] [] [2024-11-14 09:50:40,120]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-11-14 10:02:55,531]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-14 10:07:12,093]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7c30fa49-6311-4392-8867-f3fabe3b6a25
TID: [-1234] [] [2024-11-14 10:07:14,095]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a64f4444-a764-4b3e-8519-ec919f4e40dc
TID: [-1234] [] [2024-11-14 10:07:14,196]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = da329281-ee6b-4096-8ff9-d523c500693a
TID: [-1234] [] [2024-11-14 10:07:17,477]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 57cb55f3-969b-48b3-8fd0-94515fff567a
TID: [-1234] [] [2024-11-14 10:07:18,994]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 36ba0130-1e60-4f50-aa0b-ccd964fe3c01
TID: [-1234] [] [2024-11-14 10:07:23,066]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 55d1b882-7a22-47a9-95d6-fb0be448f216
TID: [-1234] [] [2024-11-14 10:07:23,671]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4e9bb8d2-9563-4045-9c79-3a626668b3e7
TID: [-1234] [] [2024-11-14 10:11:35,280]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4659df2a-1a9e-4e57-8ad1-31189090a205
TID: [-1234] [] [2024-11-14 10:21:33,989]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241111&denNgay=20241114&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241111&denNgay=20241114&maTthc=
TID: [-1234] [] [2024-11-14 10:21:34,034]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-11-14 10:32:55,809]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-14 10:59:27,627]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241114&denNgay=20241114&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241114&denNgay=20241114&maTthc=
TID: [-1234] [] [2024-11-14 10:59:27,671]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-14 10:59:27,716]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241114&denNgay=20241114&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241114&denNgay=20241114&maTthc=
TID: [-1234] [] [2024-11-14 10:59:27,758]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-11-14 10:59:30,213]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241114&denNgay=20241114&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241114&denNgay=20241114&maTthc=
TID: [-1234] [] [2024-11-14 10:59:30,281]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-14 10:59:36,677]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241114&denNgay=20241114&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241114&denNgay=20241114&maTthc=
TID: [-1234] [] [2024-11-14 10:59:36,731]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-14 10:59:41,342]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241114&denNgay=20241114&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241114&denNgay=20241114&maTthc=
TID: [-1234] [] [2024-11-14 10:59:41,450]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-14 11:02:56,057]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-14 11:06:54,278]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 54caa9bd-c148-4c76-b50f-c4d8ed86e2a3
TID: [-1234] [] [2024-11-14 11:06:54,572]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3a6252e1-10e1-4f80-a621-e1a147019c89
TID: [-1234] [] [2024-11-14 11:06:55,503]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 550606c6-fa9b-46c5-9802-df4d5ddaa32d
TID: [-1234] [] [2024-11-14 11:06:56,549]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b7b05b1b-c1b1-48d1-a466-1cffe881a00a
TID: [-1234] [] [2024-11-14 11:07:01,750]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a74a6ce8-0eb1-4c89-ba2a-c0088dfd5561
TID: [-1234] [] [2024-11-14 11:07:04,700]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ef01a1fb-0ddf-4b73-aeb8-8fdbce32ce64
TID: [-1234] [] [2024-11-14 11:35:08,865]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2703076e-9f1b-49e4-a342-d6464549255c
TID: [-1234] [] [2024-11-14 11:36:23,415]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-14 11:39:26,524]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f345ddb6-168c-45f1-8d17-89a6332e1643
TID: [-1234] [] [2024-11-14 12:06:29,518]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a8b4a1e8-8dff-4711-b401-d224588a215b
TID: [-1234] [] [2024-11-14 12:06:31,185]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 120320e3-c413-4b9d-a338-3fea558ec47e
TID: [-1234] [] [2024-11-14 12:06:33,677]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e8b3b5d0-ed26-47b0-9013-5bc5b3905774
TID: [-1234] [] [2024-11-14 12:06:34,175]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8bc7b45d-2bcd-4b30-8d14-97ed1be0da96
TID: [-1234] [] [2024-11-14 12:06:35,309]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e9b0ae79-103b-43f0-930f-1cd82445981c
TID: [-1234] [] [2024-11-14 12:06:38,005]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 28a03fa6-e218-4516-aa33-b8d6ca59e3ed
TID: [-1234] [] [2024-11-14 12:06:38,854]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-14 12:06:39,680]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 62d325a5-de46-4774-a750-da2e759a1a71
TID: [-1234] [] [2024-11-14 12:06:43,553]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3f475178-ea4e-4111-902a-ebe10e1c4c54
TID: [-1234] [] [2024-11-14 12:34:58,792]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 106665bd-3803-480a-a23b-4a7946b9b4a5
TID: [-1234] [] [2024-11-14 12:36:53,737]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-14 13:07:09,779]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4d2e0531-ced0-429f-a9e7-fd95a1ceda63
TID: [-1234] [] [2024-11-14 13:07:10,695]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e2f3b196-6e93-4050-a2ab-b544f6ba9f4d
TID: [-1234] [] [2024-11-14 13:07:11,005]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e68b124d-cba4-4aee-bf4e-1c09a092b110
TID: [-1234] [] [2024-11-14 13:07:11,678]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 489ceee2-731b-48c4-bd4d-40d5d373f1aa
TID: [-1234] [] [2024-11-14 13:07:16,729]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d408319c-81ba-4208-828a-cc1e9d0820e9
TID: [-1234] [] [2024-11-14 13:07:19,350]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ca5c270f-8a1b-47c7-86a9-c7e7095f3705
TID: [-1234] [] [2024-11-14 13:07:20,548]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 83074241-790d-47fe-97cb-0c614f4c6ff6
TID: [-1234] [] [2024-11-14 13:13:33,356]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-14 13:21:54,729]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9c4857f3-ec76-4b2b-aece-21b928bfebce
TID: [-1234] [] [2024-11-14 13:29:08,163]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4b435801-7621-42f3-a8b2-442d6bc12a5f
TID: [-1234] [] [2024-11-14 13:43:33,767]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-14 14:07:05,472]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d2d38da5-379b-41b2-8eac-71fd77bf53e5
TID: [-1234] [] [2024-11-14 14:07:06,543]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 51b69328-2491-44c3-a403-80872de65658
TID: [-1234] [] [2024-11-14 14:07:11,083]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7fee3746-d4ad-4f21-a064-becccd033483
TID: [-1234] [] [2024-11-14 14:07:12,392]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = aeff7229-ba84-40fc-b342-38c1521418d4
TID: [-1234] [] [2024-11-14 14:07:16,611]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4340ce4a-b1c2-410d-a0a6-5854981260f1
TID: [-1234] [] [2024-11-14 14:08:26,807]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4917bae3-8cbf-4f7e-b934-91a79f635716
TID: [-1234] [] [2024-11-14 14:13:34,040]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-14 14:35:17,901]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4af0abb5-deb8-42e9-a8dc-c8e8f063dd0e
TID: [-1234] [] [2024-11-14 14:35:18,582]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6ae65c76-9b52-41f7-866f-74d091f5ab99
TID: [-1234] [] [2024-11-14 14:43:34,242]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-14 15:09:17,195]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8498d5e3-7e44-4175-a747-424061ac8191
TID: [-1234] [] [2024-11-14 15:10:20,607]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6e157a87-a73e-4322-8910-eecf6c85951e
TID: [-1234] [] [2024-11-14 15:11:22,696]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 37faeb33-1563-4f4c-a6b9-c79d918e7c5c
TID: [-1234] [] [2024-11-14 15:13:34,601]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-14 15:43:34,911]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-14 15:55:08,883]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-11-14 15:59:17,590]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 61bc0563-bb7b-4e90-852d-f5bbc01bcedb
TID: [-1234] [] [2024-11-14 16:08:06,167]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5b9dd807-6153-448e-a79c-ad0c4b0ac01c
TID: [-1234] [] [2024-11-14 16:08:10,235]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b94c68a9-020a-4071-9ea1-f9161c1af7f7
TID: [-1234] [] [2024-11-14 16:08:10,298]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2ed1ed32-b210-4050-88eb-b7b1f81f5578
TID: [-1234] [] [2024-11-14 16:13:35,180]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-14 16:18:18,685]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2b50a174-35cb-476c-9180-7c37e339288c
TID: [-1234] [] [2024-11-14 16:32:41,676]  INFO {org.wso2.carbon.identity.application.authentication.framework.store.SessionCleanUpService} - Session Data cleanup task is running successfully for removing expired Data
TID: [-1234] [] [2024-11-14 16:43:35,906]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-14 17:06:23,089]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ab5d7549-875e-417d-a0a1-84069457a238
TID: [-1234] [] [2024-11-14 17:06:29,299]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fd9291cc-87ac-4219-a18e-6a91566eb71a
TID: [-1234] [] [2024-11-14 17:06:30,599]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 79d632ce-33a9-4d4f-a8c5-d6bd761a234e
TID: [-1234] [] [2024-11-14 17:06:31,987]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c3d2360b-5e57-4529-aada-d0b55ce58fcd
TID: [-1234] [] [2024-11-14 17:06:32,593]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d4694dd8-1673-4e05-8f19-affc022863a7
TID: [-1234] [] [2024-11-14 17:06:38,778]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2918c8e1-3b20-465e-b04f-b52ac41335f3
TID: [-1234] [] [2024-11-14 17:06:40,000]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c3265b8e-2799-467e-9092-4dc83dadcab0
TID: [-1234] [] [2024-11-14 17:29:26,504]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-14 17:58:02,353]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241114&denNgay=20241114&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241114&denNgay=20241114&maTthc=
TID: [-1234] [] [2024-11-14 17:58:02,393]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-14 17:59:26,826]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-14 18:06:36,490]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8b318ef1-e220-4914-ba99-18a2aa1a7537
TID: [-1234] [] [2024-11-14 18:06:42,985]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fc956a3f-2436-4b17-b404-ca14b8142ca2
TID: [-1234] [] [2024-11-14 18:06:43,275]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4b975c45-27d5-4dfe-aabb-6c76ddf72b06
TID: [-1234] [] [2024-11-14 18:06:44,421]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d2e42676-215f-44bd-aae9-3092a3bef9d3
TID: [-1234] [] [2024-11-14 18:06:44,561]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = da551368-7a3d-4bf8-bf6c-37504441e9a9
TID: [-1234] [] [2024-11-14 18:06:50,882]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 97136e49-0124-464f-a2aa-11f07bc1757d
TID: [-1234] [] [2024-11-14 18:06:51,199]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bbc5bb7d-d025-47b7-9bed-f106100b5c34
TID: [-1234] [] [2024-11-14 18:06:57,669]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ad8981e9-12fc-4b83-81f6-81ec9a2866d3
TID: [-1234] [] [2024-11-14 18:06:59,444]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6562ea81-49ea-42ad-910d-a29b85f2ad61
TID: [-1234] [] [2024-11-14 18:29:31,260]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-14 18:59:31,398]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-14 19:07:04,481]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4fc3e100-f942-48dc-80af-4845cb5c8a0a
TID: [-1234] [] [2024-11-14 19:07:04,807]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5f9e4790-ab7d-45ac-8903-458445d0de7c
TID: [-1234] [] [2024-11-14 19:07:06,866]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6b03413f-1a7e-4631-a643-fa82ca0fa20a
TID: [-1234] [] [2024-11-14 19:07:07,679]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5992b497-27e3-42a2-a8dd-da6fcb5292ff
TID: [-1234] [] [2024-11-14 19:07:08,354]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3d6d44b9-d28e-4ad8-b01f-9f9f2f6bb3ae
TID: [-1234] [] [2024-11-14 19:07:12,512]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ddbbc57e-85f7-4b68-b015-aef276f6e9b2
TID: [-1234] [] [2024-11-14 19:07:15,812]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 09b8b8fc-8334-49cc-be18-c891c303c541
TID: [-1234] [] [2024-11-14 19:07:18,228]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 01996b03-f884-4294-8ce0-323cff8e8311
TID: [-1234] [] [2024-11-14 19:29:38,719]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-14 19:59:43,568]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-14 20:06:43,201]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c4489fd0-c938-41db-96a5-6f79a0d7c8c7
TID: [-1234] [] [2024-11-14 20:06:43,572]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6cafa9a6-ddac-41bf-ad78-43e6f48ae677
TID: [-1234] [] [2024-11-14 20:06:44,295]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 756ab0d0-a898-41a1-84dc-5b985223d23e
TID: [-1234] [] [2024-11-14 20:06:46,284]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8b6cfe57-5542-4a2e-aeda-b9ba86c7930d
TID: [-1234] [] [2024-11-14 20:06:48,081]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a5b22fdb-cea2-4442-aed8-250f07e90e09
TID: [-1234] [] [2024-11-14 20:06:52,276]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4a1ee296-fbe5-40d1-a842-985806f83a2b
TID: [-1234] [] [2024-11-14 20:06:57,104]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9f1c3104-2b1b-4f90-891f-b16f08d1c659
TID: [-1234] [] [2024-11-14 20:06:58,218]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a2b06bae-aab6-4413-93fa-41b6611492ad
TID: [-1234] [] [2024-11-14 20:29:03,575]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a6163827-0c82-4785-a727-9a8ef2e04415
TID: [-1234] [] [2024-11-14 20:31:36,071]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-14 20:42:18,027]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241114&denNgay=20241114&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241114&denNgay=20241114&maTthc=
TID: [-1234] [] [2024-11-14 20:42:18,067]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-14 20:50:30,039]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a3f665d6-c293-49b9-a204-612023ac2cb7
TID: [-1234] [] [2024-11-14 21:01:36,368]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-14 21:06:07,870]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241114&denNgay=20241114&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241114&denNgay=20241114&maTthc=
TID: [-1234] [] [2024-11-14 21:06:07,910]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-11-14 21:06:30,465]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3e29236b-d69c-4159-b76a-c4dfb42989c9
TID: [-1234] [] [2024-11-14 21:06:30,944]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 857731cd-d344-4617-9854-20336ea76477
TID: [-1234] [] [2024-11-14 21:06:31,349]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4fecd29d-80c7-4622-a6dd-afc3a535a9ef
TID: [-1234] [] [2024-11-14 21:06:34,180]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 646c0633-d91f-4b94-98cc-654748f635f8
TID: [-1234] [] [2024-11-14 21:06:36,257]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 848a528a-295c-40bf-bc42-a4b1044a31a4
TID: [-1234] [] [2024-11-14 21:06:37,803]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1df3ca41-c93f-47dc-80fd-f9276dfc17b9
TID: [-1234] [] [2024-11-14 21:06:40,755]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2670f06a-a39e-460f-90b6-7d6a2f2d389e
TID: [-1234] [] [2024-11-14 21:06:45,341]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 46229f9d-1355-4ed2-a98e-c2d3369ef0ad
TID: [-1234] [] [2024-11-14 21:06:45,757]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 74ede9dd-9df8-45a9-b57c-ddcbef3c1238
TID: [-1234] [] [2024-11-14 21:31:36,559]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-14 21:34:54,135]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ffae0856-8f90-4416-bba6-0aef74d0ee4c
TID: [-1234] [] [2024-11-14 22:01:36,796]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-14 22:06:34,196]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5ba99cf0-9d0f-4a69-9af3-188208fc393e
TID: [-1234] [] [2024-11-14 22:06:37,773]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 50e94414-3234-45bc-b8ce-d559b029ddb3
TID: [-1234] [] [2024-11-14 22:06:39,016]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4b289b1e-a898-4aad-95ec-81a20f63bf39
TID: [-1234] [] [2024-11-14 22:06:42,590]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 80bedd53-2ec8-448b-8259-382149701113
TID: [-1234] [] [2024-11-14 22:06:43,334]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d10437dd-7155-4c17-8e37-805a7fbaf2a6
TID: [-1234] [] [2024-11-14 22:06:46,436]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 718bfff0-d4c8-43bd-8088-9391bda7d5a5
TID: [-1234] [] [2024-11-14 22:06:49,235]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 53bc9d14-b101-4beb-80f4-be45c0905476
TID: [-1234] [] [2024-11-14 22:22:18,699]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241114&denNgay=20241114&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241114&denNgay=20241114&maTthc=
TID: [-1234] [] [2024-11-14 22:22:18,741]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-14 22:31:36,956]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-14 23:01:37,182]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-14 23:06:59,344]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2f02616b-3a24-43a0-877a-4fd45dcab711
TID: [-1234] [] [2024-11-14 23:06:59,692]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6ccbb803-5be7-4e6f-819f-9418fcd2ded4
TID: [-1234] [] [2024-11-14 23:07:00,619]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5b83a273-4071-48a4-8161-e046cfc96696
TID: [-1234] [] [2024-11-14 23:07:03,321]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fa9d4fbc-2c72-43f2-b100-5498deedc0f2
TID: [-1234] [] [2024-11-14 23:07:07,568]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e8f67988-e104-4411-907b-bc68e99b9017
TID: [-1234] [] [2024-11-14 23:07:07,604]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f303cb75-db55-4883-b195-4c66b61754d0
TID: [-1234] [] [2024-11-14 23:07:09,531]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a72a7f6d-5d03-4e9a-bd6b-47d09c54a7d0
TID: [-1234] [] [2024-11-14 23:07:10,410]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 549f52fb-cdb2-4f68-98a3-50e71866e5da
TID: [-1234] [] [2024-11-14 23:07:13,315]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c066b5b0-9347-4661-9f1f-761261bd82a9
TID: [-1234] [] [2024-11-14 23:07:14,237]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 533dc5fc-be16-499c-be4f-0934ddfc9b5b
TID: [-1234] [] [2024-11-14 23:31:37,256]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
