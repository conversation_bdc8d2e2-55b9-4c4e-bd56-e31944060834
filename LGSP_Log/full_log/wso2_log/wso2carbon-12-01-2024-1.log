TID: [-1234] [] [2024-12-01 00:00:11,375]  WARN {org.apache.synapse.commons.util.MiscellaneousUtil} - Error loading properties from a file at from the System defined location: access-log.properties
TID: [-1234] [] [2024-12-01 00:01:36,576]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-01 00:06:24,737]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 77e8f770-5ba5-4b4d-afd7-bfb72285f097
TID: [-1234] [] [2024-12-01 00:06:28,656]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = db5bac45-b9d8-4ceb-bd2b-095e6590653c
TID: [-1234] [] [2024-12-01 00:06:30,782]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 670259e5-623a-4241-a908-8c5f3d02d2c6
TID: [-1234] [] [2024-12-01 00:06:31,571]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = dd723540-b04e-4fe4-bc67-3d456309e8dd
TID: [-1234] [] [2024-12-01 00:06:31,900]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 73ee5d16-02e3-4f74-8e17-79ef02b301dd
TID: [-1234] [] [2024-12-01 00:06:32,735]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b7a5aa0c-1fb5-425f-b963-b35f9b89b94f
TID: [-1234] [] [2024-12-01 00:06:33,389]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 044d7836-928e-4492-bc5f-922164595072
TID: [-1234] [] [2024-12-01 00:06:35,543]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9bb75a87-2bcb-46ec-adb0-81b9f187044e
TID: [-1234] [] [2024-12-01 00:06:35,591]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 602491ab-0a01-414b-be08-5f1e28a75a9b
TID: [-1234] [] [2024-12-01 00:06:38,367]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3a810cd9-5cb1-4c29-b581-0e11650883e7
TID: [-1234] [] [2024-12-01 00:31:37,074]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-01 01:01:37,728]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-01 01:06:35,051]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 258d9689-b709-45bc-ab2a-631ca0dd7644
TID: [-1234] [] [2024-12-01 01:06:35,323]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = aea1b739-569e-4f34-8b60-6a6725bf3133
TID: [-1234] [] [2024-12-01 01:06:35,370]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a3164cb1-b52f-4228-9b86-342306f27cab
TID: [-1234] [] [2024-12-01 01:06:36,510]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 964cde3f-3940-4a2f-b713-8c27307f0949
TID: [-1234] [] [2024-12-01 01:06:41,818]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4271c17c-4d65-4e54-a709-69942f61658c
TID: [-1234] [] [2024-12-01 01:06:43,150]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9820d853-3b65-4af0-860f-556594a6e2d5
TID: [-1234] [] [2024-12-01 01:06:43,909]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c67ba92f-3cce-4aef-a909-7f4e2d545668
TID: [-1234] [] [2024-12-01 01:06:46,686]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 781edc9a-ca36-40bd-a724-a9eec60c6f2f
TID: [-1234] [] [2024-12-01 01:31:38,059]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-01 02:01:38,276]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-01 02:06:31,332]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2ae85b2f-1f21-4ec4-9398-1debddaf15ba
TID: [-1234] [] [2024-12-01 02:06:32,539]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e85cdee3-574e-41c0-9602-6562f66202d7
TID: [-1234] [] [2024-12-01 02:06:32,633]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fa194448-5108-49a7-bef0-4e342ab368af
TID: [-1234] [] [2024-12-01 02:06:34,338]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 250c69c9-8ba4-4813-8c8d-1b7d288b738c
TID: [-1234] [] [2024-12-01 02:06:37,220]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 262af20e-df4b-4cb3-87a7-0d34ade211ce
TID: [-1234] [] [2024-12-01 02:06:37,462]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 401a829f-236b-4f01-879f-5f083413f92f
TID: [-1234] [] [2024-12-01 02:06:40,126]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4736eab0-c155-4ea7-8847-91086a00a77a
TID: [-1234] [] [2024-12-01 02:06:42,702]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 28bada71-d6ef-443b-87a3-f91c950b8d71
TID: [-1234] [] [2024-12-01 02:06:44,316]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f960daab-e27b-43ce-b664-b70331ebce74
TID: [-1234] [] [2024-12-01 02:31:38,606]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-01 02:35:36,442]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ejournal/default.aspx?oia=xhw&mnuid=32&max=wlo&browse_value=E&awd=uty, HEALTH CHECK URL = /ejournal/default.aspx?oia=xhw&mnuid=32&max=wlo&browse_value=E&awd=uty
TID: [-1234] [] [2024-12-01 02:43:44,122]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ejournal/default.aspx?vnw=vtn&mnuid=32&gsn=prx&browse_value=E&emt=xkt, HEALTH CHECK URL = /ejournal/default.aspx?vnw=vtn&mnuid=32&gsn=prx&browse_value=E&emt=xkt
TID: [-1234] [] [2024-12-01 03:01:38,966]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-01 03:06:42,636]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 79ed2e7a-ed48-4f33-81a2-4abf10507da9
TID: [-1234] [] [2024-12-01 03:06:43,677]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 694a81a3-4a7e-412c-8eb4-c7181a848db5
TID: [-1234] [] [2024-12-01 03:06:45,565]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 32cc6a6a-7c07-4043-a500-380b4a22e63a
TID: [-1234] [] [2024-12-01 03:06:45,894]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f73abbd9-ac5f-4ff7-8c91-aa6480ee4765
TID: [-1234] [] [2024-12-01 03:06:50,985]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c8f7ebef-bb4c-4577-8ff2-e0a5f007f5c3
TID: [-1234] [] [2024-12-01 03:06:53,646]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c487b5b3-24f5-4b48-a4be-49f7b6638b77
TID: [-1234] [] [2024-12-01 03:31:40,294]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-01 03:34:47,328]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 668d2efc-3197-4f9f-a766-19983bc3eed0
TID: [-1234] [] [2024-12-01 04:01:40,705]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-01 04:06:35,646]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c36a9acd-7283-455b-8c75-48f56848a670
TID: [-1234] [] [2024-12-01 04:06:37,911]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5ee8748a-3e4b-4ebe-b5f2-616b9debf5bb
TID: [-1234] [] [2024-12-01 04:06:38,936]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 83e7d0b7-301f-4817-9b69-1a7a90638f11
TID: [-1234] [] [2024-12-01 04:06:41,128]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a43435f9-e7a0-4b4b-a218-738fe55b9dda
TID: [-1234] [] [2024-12-01 04:06:41,483]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ad1ce2c2-29b4-4bd8-b27e-6b0c051808af
TID: [-1234] [] [2024-12-01 04:06:41,804]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6190e770-62e1-41c7-938d-50108f2b01b4
TID: [-1234] [] [2024-12-01 04:06:43,900]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f6e41c49-d44c-473b-8f71-d0b35fd09b73
TID: [-1234] [] [2024-12-01 04:06:45,029]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c22ba1db-1d93-4486-a43c-cb40337fc772
TID: [-1234] [] [2024-12-01 04:06:46,348]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 72f4c45a-fefc-4b2b-93c2-20bcdf2d3aea
TID: [-1234] [] [2024-12-01 04:31:41,304]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-01 05:01:43,512]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-01 05:06:48,139]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d9fd92dd-05d3-4194-9225-3356edb4a4f6
TID: [-1234] [] [2024-12-01 05:06:49,635]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d78ceb4f-fe2d-448b-9d79-816bb05a4449
TID: [-1234] [] [2024-12-01 05:06:52,281]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 239f3aa2-a3da-441f-b81b-e0665d14e468
TID: [-1234] [] [2024-12-01 05:06:53,203]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 48d15700-211f-4a8c-b94d-2c0cdcb187a8
TID: [-1234] [] [2024-12-01 05:06:55,667]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 28b1b6f7-3cd5-4732-a75f-90fe6bfe1885
TID: [-1234] [] [2024-12-01 05:06:57,077]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2e9bd836-8be6-4cb4-8ef7-2068ea21d6ba
TID: [-1234] [] [2024-12-01 05:06:58,736]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9513558a-3e87-4f77-9da2-c4cb032687e0
TID: [-1234] [] [2024-12-01 05:07:00,452]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 68ab4667-319a-4cfd-a5c8-9f2bd7bacd25
TID: [-1234] [] [2024-12-01 05:31:44,161]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-01 06:01:45,249]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-01 06:06:33,489]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4134921f-85e6-45ba-9adc-c45bf897f8b6
TID: [-1234] [] [2024-12-01 06:06:33,838]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ace72971-ae6d-4281-9474-a8724f33b4ae
TID: [-1234] [] [2024-12-01 06:06:38,292]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1c602450-083e-4dbd-a19d-f97e644fa2ba
TID: [-1234] [] [2024-12-01 06:06:39,906]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c70fb70b-f80f-47c6-9e03-76ca8f3740ca
TID: [-1234] [] [2024-12-01 06:06:39,939]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 986d48e9-8328-4a7e-a735-5a5471db790d
TID: [-1234] [] [2024-12-01 06:06:40,672]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 57038969-3f8f-4207-8699-90fa5066e20d
TID: [-1234] [] [2024-12-01 06:06:44,608]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 35033006-024e-4838-8cd3-e2d25b48dbc6
TID: [-1234] [] [2024-12-01 06:06:44,846]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 10f2b221-a017-4879-a0f2-8eddee2def14
TID: [-1234] [] [2024-12-01 06:06:47,311]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = eec86a72-2032-4bc6-a8dd-c060b3894b88
TID: [-1234] [] [2024-12-01 06:06:48,821]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = eb51b189-e4b8-4711-8324-bcf164c65681
TID: [-1234] [] [2024-12-01 06:43:23,473]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-01 06:43:39,729] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2024-12-01 07:06:16,051]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0757d997-af2a-4fef-b1fc-29bdd678641b
TID: [-1234] [] [2024-12-01 07:06:18,209]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cddd1475-2e63-4a6e-a983-1d4864708630
TID: [-1234] [] [2024-12-01 07:06:18,948]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 699c8c6e-9c32-4d36-8854-95ba9cb62efa
TID: [-1234] [] [2024-12-01 07:06:19,410]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a4f43c97-e679-47b0-88da-31607d2eff0c
TID: [-1234] [] [2024-12-01 07:06:22,843]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9c8b9c86-084c-488c-9942-226986c9f61b
TID: [-1234] [] [2024-12-01 07:06:26,058]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1ae8cfc6-4229-47c9-8f19-f265bc0ce6ce
TID: [-1234] [] [2024-12-01 07:06:26,241]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 16f51bab-baf4-4287-8a11-ee47276c9d44
TID: [-1234] [] [2024-12-01 07:13:23,807]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-01 07:34:47,954]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e518e630-b10e-4db3-9eb6-dab340c49fba
TID: [-1234] [] [2024-12-01 07:43:24,054]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-01 08:06:22,903]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 46ff1b0b-702b-412c-9315-bdf119bd30fb
TID: [-1234] [] [2024-12-01 08:06:23,014]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 80ed3fcd-844e-404e-9213-1b782780fec3
TID: [-1234] [] [2024-12-01 08:06:23,383]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 11d637ab-ffd3-45a3-ba20-3457758d1f10
TID: [-1234] [] [2024-12-01 08:06:28,898]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3fcf18b3-c299-4736-838f-ed67942cdb28
TID: [-1234] [] [2024-12-01 08:06:29,879]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d9776dcb-bccc-4926-aa07-1970039976e9
TID: [-1234] [] [2024-12-01 08:06:29,924]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6c0c30bf-d6d0-4011-856a-f636a017742b
TID: [-1234] [] [2024-12-01 08:06:30,817]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0f7cae20-7c6f-41ed-8ddd-7be2764996b5
TID: [-1234] [] [2024-12-01 08:06:30,831]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2424f71f-87fa-4391-98dc-2d0eeaa39b5e
TID: [-1234] [] [2024-12-01 08:13:24,308]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-01 08:43:24,546]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-01 09:06:49,022]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1990f317-77ad-479d-ae7b-2aca2ec2e535
TID: [-1234] [] [2024-12-01 09:06:49,350]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4d532d91-2d6e-4038-9bdf-5613f463bfc0
TID: [-1234] [] [2024-12-01 09:06:52,568]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2f42d2ea-67f7-41ad-ba35-0e7c9ffc2247
TID: [-1234] [] [2024-12-01 09:06:55,820]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2396cb95-5757-481f-a74e-2f0df4fd90ce
TID: [-1234] [] [2024-12-01 09:06:56,817]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ac04a23b-7eb1-4674-aceb-e284a4a01867
TID: [-1234] [] [2024-12-01 09:06:58,940]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c71af846-f5da-453a-9d2b-aa9eb030a54a
TID: [-1234] [] [2024-12-01 09:06:59,948]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 24df2faa-adff-4107-9b80-d9994863d1b5
TID: [-1234] [] [2024-12-01 09:13:24,763]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-01 09:43:24,918]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-01 10:06:53,820]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7b8cf16d-e5f7-417d-99fc-0ed3ca9cead5
TID: [-1234] [] [2024-12-01 10:06:56,503]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4d781a89-374a-49f3-804f-359ba025dec1
TID: [-1234] [] [2024-12-01 10:06:59,020]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f410bdee-f61e-40aa-94a5-8a887989bec9
TID: [-1234] [] [2024-12-01 10:06:59,070]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5588a882-27ec-4482-ad8d-af67e6d12e8b
TID: [-1234] [] [2024-12-01 10:07:00,232]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 521def6f-2cbb-4462-b4cd-2c920aab333a
TID: [-1234] [] [2024-12-01 10:07:02,593]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 11d675b6-1c73-4b00-826c-9de951addd68
TID: [-1234] [] [2024-12-01 10:13:25,064]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-01 10:24:49,860]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241201&denNgay=20241201&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241201&denNgay=20241201&maTthc=
TID: [-1234] [] [2024-12-01 10:24:49,899]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-01 10:43:25,236]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-01 11:06:43,584]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b1740368-9f60-4728-a780-d4b7b511e441
TID: [-1234] [] [2024-12-01 11:06:44,494]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ccfc578b-3210-403f-a49d-7651f3e9150d
TID: [-1234] [] [2024-12-01 11:06:46,491]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2a4f16f9-db13-4ce2-b50e-b57f7c1c0336
TID: [-1234] [] [2024-12-01 11:06:48,933]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d2b2ae16-77e9-4c6b-a544-70e13ee8277f
TID: [-1234] [] [2024-12-01 11:06:50,300]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 747344a1-428a-49be-8041-b631d98710b7
TID: [-1234] [] [2024-12-01 11:06:50,306]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fc86e9f0-00ab-49a9-b518-d94a7271ab95
TID: [-1234] [] [2024-12-01 11:06:52,035]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = eb97e105-c92e-46c7-b3cd-695c2e78c326
TID: [-1234] [] [2024-12-01 11:06:53,746]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3dc62db3-933e-4336-8ff6-23d66b1dad7d
TID: [-1234] [] [2024-12-01 11:06:56,475]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 477022fd-9696-4eac-9af0-ce36e3c70d4b
TID: [-1234] [] [2024-12-01 11:29:09,315]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-01 11:59:09,641]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-01 12:06:14,298]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 820b11cf-96e2-4eee-a7bd-8fb2ccdb0086
TID: [-1234] [] [2024-12-01 12:06:14,755]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7bd55062-0450-4377-bdac-f100012b4026
TID: [-1234] [] [2024-12-01 12:06:15,435]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1887ac35-d2aa-47b9-a80d-3bf89d2e9a16
TID: [-1234] [] [2024-12-01 12:06:16,784]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5b2b6028-280d-4c17-bd80-ed19dd6d0b4a
TID: [-1234] [] [2024-12-01 12:06:18,240]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 46c7f10f-6673-43a8-b7b1-c7927769a2f3
TID: [-1234] [] [2024-12-01 12:06:22,290]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 62b01fc4-0b5f-45ce-9579-03c469d5aa26
TID: [-1234] [] [2024-12-01 12:06:22,464]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 66c0abd1-de31-4c3f-91a7-5afc40514389
TID: [-1234] [] [2024-12-01 12:06:23,335]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 88735dc5-b9e5-4694-9db7-02553c523ea0
TID: [-1234] [] [2024-12-01 12:06:24,232]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8b9421bb-3ce9-461a-8882-69223310118a
TID: [-1234] [] [2024-12-01 12:29:09,943]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-01 12:37:11,572]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 54ad293f-46ba-43ea-8e55-2769c4fb5579
TID: [-1234] [] [2024-12-01 12:59:10,027]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-01 13:06:29,088]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 531ec16f-bb97-4b3f-ae67-35a1eda12a3e
TID: [-1234] [] [2024-12-01 13:06:33,566]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a2ea1d4a-c35a-49c4-8da4-027ec1e3041f
TID: [-1234] [] [2024-12-01 13:06:35,663]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a683e5f6-710b-4893-bfb2-cf4b680434a6
TID: [-1234] [] [2024-12-01 13:06:35,943]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b701afcb-4e50-44a7-b5ed-ba4c3446344d
TID: [-1234] [] [2024-12-01 13:06:36,980]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 11929552-3b3d-4766-b86e-c0696d1666ab
TID: [-1234] [] [2024-12-01 13:06:39,798]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fc0801b7-06bf-419a-ba93-cc637c2dfa1e
TID: [-1234] [] [2024-12-01 13:29:10,175]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-01 13:34:43,885]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = efddfd05-6a0f-475e-b96f-9e61ed9e5c55
TID: [-1234] [] [2024-12-01 13:59:10,434]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-01 14:06:36,120]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = edb213dd-f7b7-4bbf-9533-c444bed05fba
TID: [-1234] [] [2024-12-01 14:06:36,311]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c65c8bd0-f458-4ded-b862-e2fa540fa9b7
TID: [-1234] [] [2024-12-01 14:06:36,318]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1a84244a-1477-4197-abe3-c3da6f372aab
TID: [-1234] [] [2024-12-01 14:06:36,958]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ed3ad285-923e-4918-be0a-3ff84a1b33db
TID: [-1234] [] [2024-12-01 14:06:39,903]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0a20d588-b6c3-4fd7-b036-3a95283f3744
TID: [-1234] [] [2024-12-01 14:06:41,508]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ccba2f6b-c69a-44cf-b78b-d3dedcc72823
TID: [-1234] [] [2024-12-01 14:29:10,575]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-01 14:59:11,230]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-01 15:06:37,081]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3bbf4720-81c3-4ad2-b91a-dea3b458a893
TID: [-1234] [] [2024-12-01 15:06:37,542]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = eb4ed5b4-7862-4133-a73b-ee4fc0c96bb6
TID: [-1234] [] [2024-12-01 15:06:38,942]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a1c23ca4-eb5a-45e9-b377-a2af3065f55a
TID: [-1234] [] [2024-12-01 15:06:40,626]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e6a3e6c3-e972-44c8-9392-718e23021dab
TID: [-1234] [] [2024-12-01 15:06:41,797]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7d156b7e-cc83-497a-923f-6f0c180f3f2d
TID: [-1234] [] [2024-12-01 15:06:44,710]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 34d8138b-3a38-421c-b36c-a9eeec1051f7
TID: [-1234] [] [2024-12-01 15:06:44,861]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1a51598e-dc79-4246-bf0c-8145e5369ca0
TID: [-1234] [] [2024-12-01 15:06:45,631]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 23ef48cb-782a-4578-b06d-d9338fe7600d
TID: [-1234] [] [2024-12-01 15:06:47,728]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 09bad8e7-f514-4b54-adb7-317112cb6cca
TID: [-1234] [] [2024-12-01 15:06:49,190]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8192cda4-95d7-4a0a-9063-5d1628fe13bb
TID: [-1234] [] [2024-12-01 15:29:11,449]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-01 15:59:11,587]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-01 16:06:48,672]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 44987844-548c-4264-9c20-986eaa7947c6
TID: [-1234] [] [2024-12-01 16:06:49,236]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 25250c36-9fdc-40c7-a52e-8e0ee69e3f92
TID: [-1234] [] [2024-12-01 16:06:49,855]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b6f17156-1389-4ec9-91c9-09810db1d13e
TID: [-1234] [] [2024-12-01 16:06:50,739]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 41f08cf2-fcb0-419f-a0b0-61dde01375ad
TID: [-1234] [] [2024-12-01 16:06:55,961]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0b3fa56c-6a71-4695-b385-c98ecfceee75
TID: [-1234] [] [2024-12-01 16:06:56,229]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = accb1351-e4a1-4d17-a420-5d323c1aec4f
TID: [-1234] [] [2024-12-01 16:06:57,053]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7993fed7-6214-4c06-95a2-269cd11188f9
TID: [-1234] [] [2024-12-01 16:07:00,745]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 92f6900a-5ef3-4e6d-82eb-584df0639a53
TID: [-1234] [] [2024-12-01 16:07:00,902]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bbbc9e85-e82c-4652-921f-0745eed6160f
TID: [-1234] [] [2024-12-01 16:07:01,810]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5814ad45-f84c-4ccd-ab5e-ed2ad2d42687
TID: [-1234] [] [2024-12-01 16:31:30,512]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-01 16:32:42,179]  INFO {org.wso2.carbon.identity.application.authentication.framework.store.SessionCleanUpService} - Session Data cleanup task is running successfully for removing expired Data
TID: [-1234] [] [2024-12-01 16:56:41,271]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sitemap.txt, HEALTH CHECK URL = /sitemap.txt
TID: [-1234] [] [2024-12-01 17:01:31,102]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-01 17:06:40,955]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 651bc7a3-70a2-4288-b736-2e13f3bb5fe2
TID: [-1234] [] [2024-12-01 17:06:44,620]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8e41026c-1d4b-4e63-9ab3-7ef0c5e5819b
TID: [-1234] [] [2024-12-01 17:06:44,672]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b9fb8959-86a9-4e53-a241-4b53d4bb44c3
TID: [-1234] [] [2024-12-01 17:06:44,994]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 727ff14b-28d2-47a9-be32-4eb677df437a
TID: [-1234] [] [2024-12-01 17:06:45,696]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 20969708-b1de-4c65-b71c-4467b090d742
TID: [-1234] [] [2024-12-01 17:06:45,863]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f0f923de-8d42-4cd0-a335-08b7c39c3a0b
TID: [-1234] [] [2024-12-01 17:06:46,396]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ec61dbd4-a575-4c85-851c-93c67fa76b7c
TID: [-1234] [] [2024-12-01 17:06:51,268]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d796fdfb-7f2a-481b-9540-185f2dd0c695
TID: [-1234] [] [2024-12-01 17:31:31,233]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-01 17:46:27,029]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sitemap.txt, HEALTH CHECK URL = /sitemap.txt
TID: [-1234] [] [2024-12-01 18:01:31,608]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-01 18:06:44,307]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c4799534-0413-48f1-a6e6-1509fbd9f1ac
TID: [-1234] [] [2024-12-01 18:06:44,458]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d9f4cfa7-89c5-47cb-a1a8-b2e789721b23
TID: [-1234] [] [2024-12-01 18:06:45,061]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5f34e2d7-302c-4385-bba8-ed1c0c0c21cc
TID: [-1234] [] [2024-12-01 18:06:45,409]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6f10c961-bb01-44c3-b247-6792fc6434d8
TID: [-1234] [] [2024-12-01 18:06:46,607]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6917c5ba-d394-459c-80c1-70e966cd3a9c
TID: [-1234] [] [2024-12-01 18:06:48,402]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d0d97c32-8796-4e66-a464-f414521e7039
TID: [-1234] [] [2024-12-01 18:06:51,029]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3282639b-882f-444c-be20-c54d9fc6b876
TID: [-1234] [] [2024-12-01 18:06:53,857]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fe39e38d-4acb-4e2b-b0d1-2f5fe9467613
TID: [-1234] [] [2024-12-01 18:27:38,408]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241201&denNgay=20241201&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241201&denNgay=20241201&maTthc=
TID: [-1234] [] [2024-12-01 18:27:38,448]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-01 18:31:31,757]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-01 19:01:32,056]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-01 19:07:57,058]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 60e7e6ed-f0a1-4af5-b669-3a6373952b21
TID: [-1234] [] [2024-12-01 19:07:57,380]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 29073406-9ce7-438f-9c6d-bc11db17aaaf
TID: [-1234] [] [2024-12-01 19:07:57,681]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 86d09663-57d2-41ca-a7da-aff6218d741f
TID: [-1234] [] [2024-12-01 19:31:32,336]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-01 19:38:23,958]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1f713c4b-8e6c-4c33-a517-ed8103c0342b
TID: [-1234] [] [2024-12-01 20:01:32,814]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-01 20:06:56,521]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 76bd3e3b-9318-4b98-91a8-9c4b045b2a7d
TID: [-1234] [] [2024-12-01 20:06:58,895]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = dcdbc651-0657-4b9d-b7e5-eea75e8c8c00
TID: [-1234] [] [2024-12-01 20:06:59,822]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b92e6c1d-9e24-4ffd-91fe-a18721b074df
TID: [-1234] [] [2024-12-01 20:07:03,129]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3c22e057-d6cd-40b8-9923-a6ed835f5768
TID: [-1234] [] [2024-12-01 20:07:04,685]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4f5ba26e-233f-462f-bb10-41d3b5d1ee6b
TID: [-1234] [] [2024-12-01 20:07:05,045]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 00251cbe-aa0f-442b-be59-0782eddba5c8
TID: [-1234] [] [2024-12-01 20:07:05,137]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5ed18ad3-5372-4c94-80df-93b37657ad93
TID: [-1234] [] [2024-12-01 20:07:07,667]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 613bb41a-c6ef-49eb-9806-e7138f492cad
TID: [-1234] [] [2024-12-01 20:31:33,180]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-01 21:01:33,325]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-01 21:06:07,071]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 80b97287-3d74-4b20-b5a5-bffd24920e81
TID: [-1234] [] [2024-12-01 21:06:07,731]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 062c34b7-29ef-4eed-9ae1-9f5d610fcbf3
TID: [-1234] [] [2024-12-01 21:06:09,236]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5c5c7af0-927e-493a-a525-e15dc6a40548
TID: [-1234] [] [2024-12-01 21:06:10,653]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d912051a-1274-46a2-a318-dfd669fca702
TID: [-1234] [] [2024-12-01 21:06:16,613]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8d641749-eff8-40c6-bb16-41332b84e856
TID: [-1234] [] [2024-12-01 21:06:17,526]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 465823b3-14df-4564-85ee-d86ee97e7d8f
TID: [-1234] [] [2024-12-01 21:06:17,821]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 41ffb6d9-6d20-4fba-941b-8175b99d2e02
TID: [-1234] [] [2024-12-01 21:06:18,867]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 61dc0608-60a7-4721-afb8-bcd2783b2b70
TID: [-1234] [] [2024-12-01 21:06:19,089]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 65d6fa66-ede1-493a-a0ea-4773745c0316
TID: [-1234] [] [2024-12-01 21:31:33,474]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-01 22:01:33,899]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-01 22:04:59,059] ERROR {org.apache.catalina.core.ContainerBase.[Catalina].[localhost].[/].[bridgeservlet]} - Servlet.service() for servlet [bridgeservlet] in context with path [/] threw exception java.lang.IllegalArgumentException: An invalid character [44] was present in the Cookie value
	at org.apache.tomcat.util.http.Rfc6265CookieProcessor.validateCookieValue(Rfc6265CookieProcessor.java:197)
	at org.apache.tomcat.util.http.Rfc6265CookieProcessor.generateHeader(Rfc6265CookieProcessor.java:123)
	at org.apache.catalina.connector.Response.generateCookieString(Response.java:1003)
	at org.apache.catalina.connector.Response.addCookie(Response.java:955)
	at org.apache.catalina.connector.ResponseFacade.addCookie(ResponseFacade.java:385)
	at org.wso2.carbon.ui.CarbonUILoginUtil.saveOriginalUrl(CarbonUILoginUtil.java:125)
	at org.wso2.carbon.ui.CarbonSecuredHttpContext.handleSecurity(CarbonSecuredHttpContext.java:275)
	at org.eclipse.equinox.http.servlet.internal.ServletRegistration.service(ServletRegistration.java:60)
	at org.eclipse.equinox.http.servlet.internal.ProxyServlet.processAlias(ProxyServlet.java:128)
	at org.eclipse.equinox.http.servlet.internal.ProxyServlet.service(ProxyServlet.java:76)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779)
	at org.wso2.carbon.tomcat.ext.servlet.DelegationServlet.service(DelegationServlet.java:68)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.owasp.csrfguard.CsrfGuardFilter.doFilter(CsrfGuardFilter.java:72)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.wso2.carbon.tomcat.ext.filter.CharacterSetFilter.doFilter(CharacterSetFilter.java:65)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.filters.HttpHeaderSecurityFilter.doFilter(HttpHeaderSecurityFilter.java:126)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:177)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.wso2.carbon.identity.context.rewrite.valve.TenantContextRewriteValve.invoke(TenantContextRewriteValve.java:86)
	at org.wso2.carbon.identity.authz.valve.AuthorizationValve.invoke(AuthorizationValve.java:110)
	at org.wso2.carbon.identity.auth.valve.AuthenticationValve.invoke(AuthenticationValve.java:111)
	at org.wso2.carbon.tomcat.ext.valves.CompositeValve.continueInvocation(CompositeValve.java:101)
	at org.wso2.carbon.tomcat.ext.valves.TomcatValveContainer.invokeValves(TomcatValveContainer.java:49)
	at org.wso2.carbon.tomcat.ext.valves.CompositeValve.invoke(CompositeValve.java:62)
	at org.wso2.carbon.tomcat.ext.valves.CarbonStuckThreadDetectionValve.invoke(CarbonStuckThreadDetectionValve.java:145)
	at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:687)
	at org.wso2.carbon.tomcat.ext.valves.CarbonContextCreatorValve.invoke(CarbonContextCreatorValve.java:59)
	at org.wso2.carbon.tomcat.ext.valves.RequestCorrelationIdValve.invoke(RequestCorrelationIdValve.java:124)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:891)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1784)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2024-12-01 22:06:19,687]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6609e611-55bb-4663-b217-1415bf404d84
TID: [-1234] [] [2024-12-01 22:06:20,296]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 631bd8a6-be0a-43e4-a63d-94dfb8b444b5
TID: [-1234] [] [2024-12-01 22:06:23,446]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = aa21af12-3a91-4a4b-8016-6809b79150a7
TID: [-1234] [] [2024-12-01 22:06:25,829]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ac58727f-a839-429b-9e43-cfe9c29d322c
TID: [-1234] [] [2024-12-01 22:06:27,745]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3a621eb7-3ee2-4155-8072-c46acdebc170
TID: [-1234] [] [2024-12-01 22:06:29,095]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 68bb553a-11b5-4104-96dc-e084eb49bd9c
TID: [-1234] [] [2024-12-01 22:06:29,386]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 74c4bb15-caa1-4adb-94b6-df0981590c24
TID: [-1234] [] [2024-12-01 22:31:34,454]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-01 23:01:34,572]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-01 23:06:01,698]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fa93fab6-7229-4661-b30d-c667a6d41615
TID: [-1234] [] [2024-12-01 23:06:02,500]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7ca71b1c-188e-4b43-8324-e872c2d6aee4
TID: [-1234] [] [2024-12-01 23:06:04,360]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 16a9fb0c-53b3-40c2-810e-4d38894bc51e
TID: [-1234] [] [2024-12-01 23:06:06,761]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5a568cba-e7f4-443b-938b-090172fb65a5
TID: [-1234] [] [2024-12-01 23:06:07,985]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 77a71acd-f59c-4896-b108-13f16706e830
TID: [-1234] [] [2024-12-01 23:06:10,048]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cf6db176-11f7-4b81-a09c-56d2812abb2f
TID: [-1234] [] [2024-12-01 23:06:10,438]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9a2c94ce-6bfd-4329-96b8-1e011fe86938
TID: [-1234] [] [2024-12-01 23:06:11,071]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3cce8018-5820-4aeb-ac82-547b8f6ddbf5
TID: [-1234] [] [2024-12-01 23:31:34,925]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-01 23:53:26,494] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

