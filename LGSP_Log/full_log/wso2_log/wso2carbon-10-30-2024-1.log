TID: [-1234] [] [2024-10-30 00:00:07,020]  WARN {org.apache.synapse.commons.util.MiscellaneousUtil} - Error loading properties from a file at from the System defined location: access-log.properties
TID: [-1234] [] [2024-10-30 00:05:47,552]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after <PERSON> written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45800, SOCKET_TIMEOUT = 180000, CORRELATION_ID = ff0dfe1c-56d3-4fd0-90d7-09a768b64df0
TID: [-1234] [] [2024-10-30 00:05:47,553]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 00:05:54,227]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45796, SOCKET_TIMEOUT = 180000, CORRELATION_ID = e95856cc-85dd-4ef5-9f74-c8aed16b164b
TID: [-1234] [] [2024-10-30 00:05:54,228]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 00:09:01,997]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = a03ea67f-1712-4b39-ad16-4db42e0a7bde
TID: [-1234] [] [2024-10-30 00:09:01,999]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45806, SOCKET_TIMEOUT = 180000, CORRELATION_ID = a03ea67f-1712-4b39-ad16-4db42e0a7bde
TID: [-1234] [] [2024-10-30 00:09:01,999]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 00:09:02,389]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 085d382f-d519-4dd2-8dde-7bab2ece04d5
TID: [-1234] [] [2024-10-30 00:09:02,390]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45811, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 085d382f-d519-4dd2-8dde-7bab2ece04d5
TID: [-1234] [] [2024-10-30 00:09:02,391]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 00:09:02,778]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45812, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 2c62b318-e1e5-4bb7-8f8b-2fb739817e24
TID: [-1234] [] [2024-10-30 00:09:02,779]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 00:09:07,372]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 1240e183-9042-449b-8223-bfde0f27e4ac
TID: [-1234] [] [2024-10-30 00:09:07,372]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 00:09:07,373]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45815, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 1240e183-9042-449b-8223-bfde0f27e4ac
TID: [-1234] [] [2024-10-30 00:09:07,373]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:50744, CORRELATION_ID = 1240e183-9042-449b-8223-bfde0f27e4ac, CONNECTION = http-incoming-856487
TID: [-1234] [] [2024-10-30 00:09:07,374]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 00:09:07,385]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-856487, CORRELATION_ID = 1240e183-9042-449b-8223-bfde0f27e4ac
TID: [-1234] [] [2024-10-30 00:10:56,581]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Account/Login, HEALTH CHECK URL = /Account/Login
TID: [-1234] [] [2024-10-30 00:12:03,527]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 00:12:03,528]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:43002, CORRELATION_ID = 4e7137a8-e1fc-4daa-b11a-3717a073d5f5, CONNECTION = http-incoming-856498
TID: [-1234] [] [2024-10-30 00:12:03,531]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45818, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 4e7137a8-e1fc-4daa-b11a-3717a073d5f5
TID: [-1234] [] [2024-10-30 00:12:03,532]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 00:12:03,545]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-856498, CORRELATION_ID = 4e7137a8-e1fc-4daa-b11a-3717a073d5f5
TID: [-1234] [] [2024-10-30 00:12:07,778]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 00:12:07,780]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:43014, CORRELATION_ID = 7dab326a-99c6-4bd7-ae91-566650bc7a58, CONNECTION = http-incoming-856499
TID: [-1234] [] [2024-10-30 00:12:07,784]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45813, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 7dab326a-99c6-4bd7-ae91-566650bc7a58
TID: [-1234] [] [2024-10-30 00:12:07,785]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 00:12:07,795]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-856499, CORRELATION_ID = 7dab326a-99c6-4bd7-ae91-566650bc7a58
TID: [-1234] [] [2024-10-30 00:15:03,990]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 73e672d5-2488-4f08-9f91-bf5c4897467a
TID: [-1234] [] [2024-10-30 00:15:03,991]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45821, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 73e672d5-2488-4f08-9f91-bf5c4897467a
TID: [-1234] [] [2024-10-30 00:15:03,991]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 00:15:04,001]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 00:15:04,002]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:39590, CORRELATION_ID = 73e672d5-2488-4f08-9f91-bf5c4897467a, CONNECTION = http-incoming-856501
TID: [-1234] [] [2024-10-30 00:15:04,004]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-856501, CORRELATION_ID = 73e672d5-2488-4f08-9f91-bf5c4897467a
TID: [-1234] [] [2024-10-30 00:17:20,283]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241030&denNgay=20241030&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241030&denNgay=20241030&maTthc=
TID: [-1234] [] [2024-10-30 00:17:20,324]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-10-30 00:18:04,726]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 00:18:04,727]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:56808, CORRELATION_ID = 065cf303-65c5-4bda-a7c7-bedd84a732bf, CONNECTION = http-incoming-856517
TID: [-1234] [] [2024-10-30 00:18:04,734]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 065cf303-65c5-4bda-a7c7-bedd84a732bf
TID: [-1234] [] [2024-10-30 00:18:04,735]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45830, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 065cf303-65c5-4bda-a7c7-bedd84a732bf
TID: [-1234] [] [2024-10-30 00:18:04,736]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 00:18:04,777]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-856517, CORRELATION_ID = 065cf303-65c5-4bda-a7c7-bedd84a732bf
TID: [-1234] [] [2024-10-30 00:18:05,691]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 00:18:05,691]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:56946, CORRELATION_ID = 1aabfc36-051a-4d2d-b7f5-9fb8c18c0667, CONNECTION = http-incoming-856529
TID: [-1234] [] [2024-10-30 00:18:05,859]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45832, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 1aabfc36-051a-4d2d-b7f5-9fb8c18c0667
TID: [-1234] [] [2024-10-30 00:18:05,859]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 00:18:05,871]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-856529, CORRELATION_ID = 1aabfc36-051a-4d2d-b7f5-9fb8c18c0667
TID: [-1234] [] [2024-10-30 00:18:05,921]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 00525959-bf4f-46e7-8a11-4fde4a778cec
TID: [-1234] [] [2024-10-30 00:18:05,922]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45833, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 00525959-bf4f-46e7-8a11-4fde4a778cec
TID: [-1234] [] [2024-10-30 00:18:05,923]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 00:18:06,537]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 00:18:06,538]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:57026, CORRELATION_ID = 2c9f71d8-a7ed-4127-b798-47dd0a2abdbb, CONNECTION = http-incoming-856536
TID: [-1234] [] [2024-10-30 00:18:06,568]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 2c9f71d8-a7ed-4127-b798-47dd0a2abdbb
TID: [-1234] [] [2024-10-30 00:18:06,569]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45824, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 2c9f71d8-a7ed-4127-b798-47dd0a2abdbb
TID: [-1234] [] [2024-10-30 00:18:06,569]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 00:18:06,581]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-856536, CORRELATION_ID = 2c9f71d8-a7ed-4127-b798-47dd0a2abdbb
TID: [-1234] [] [2024-10-30 00:21:14,452]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 35b9d029-a955-4506-a055-27739e9a57ed
TID: [-1234] [] [2024-10-30 00:21:15,545]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 28b23868-cbe0-41cf-acba-a403fd761a19
TID: [-1234] [] [2024-10-30 00:31:48,865]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-10-30 01:01:49,418]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-10-30 01:06:13,534]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45844, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 27658e76-656a-4abe-a56d-796455131e8f
TID: [-1234] [] [2024-10-30 01:06:13,535]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 01:06:15,488]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 01:06:15,489]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:49882, CORRELATION_ID = 6940da33-9a6c-4210-8b15-50d70ab1a9da, CONNECTION = http-incoming-857017
TID: [-1234] [] [2024-10-30 01:06:15,490]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 6940da33-9a6c-4210-8b15-50d70ab1a9da
TID: [-1234] [] [2024-10-30 01:06:15,491]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45846, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 6940da33-9a6c-4210-8b15-50d70ab1a9da
TID: [-1234] [] [2024-10-30 01:06:15,492]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 01:06:15,503]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-857017, CORRELATION_ID = 6940da33-9a6c-4210-8b15-50d70ab1a9da
TID: [-1234] [] [2024-10-30 01:09:27,706]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 620ee7ae-bae8-4b32-a332-7246f1e3e702
TID: [-1234] [] [2024-10-30 01:09:27,707]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45855, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 620ee7ae-bae8-4b32-a332-7246f1e3e702
TID: [-1234] [] [2024-10-30 01:09:27,707]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 01:09:27,887]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = ee423ad7-b5c4-4096-84aa-5048dd44e99c
TID: [-1234] [] [2024-10-30 01:09:27,887]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45864, SOCKET_TIMEOUT = 180000, CORRELATION_ID = ee423ad7-b5c4-4096-84aa-5048dd44e99c
TID: [-1234] [] [2024-10-30 01:09:27,888]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 01:09:28,578]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45851, SOCKET_TIMEOUT = 180000, CORRELATION_ID = a42720f7-9b37-4e45-92d7-8061411b7c0e
TID: [-1234] [] [2024-10-30 01:09:28,579]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 01:09:31,966]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 01:09:31,966]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:33566, CORRELATION_ID = 2ed60888-3861-427f-9dd4-dfd3046f13b5, CONNECTION = http-incoming-857086
TID: [-1234] [] [2024-10-30 01:09:31,988]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45863, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 2ed60888-3861-427f-9dd4-dfd3046f13b5
TID: [-1234] [] [2024-10-30 01:09:31,989]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 01:09:32,001]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-857086, CORRELATION_ID = 2ed60888-3861-427f-9dd4-dfd3046f13b5
TID: [-1234] [] [2024-10-30 01:12:30,005]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45860, SOCKET_TIMEOUT = 180000, CORRELATION_ID = c1576f2e-6e28-4df9-a804-d0f30fdb4931
TID: [-1234] [] [2024-10-30 01:12:30,007]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 01:12:30,010]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 01:12:30,011]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:58026, CORRELATION_ID = c1576f2e-6e28-4df9-a804-d0f30fdb4931, CONNECTION = http-incoming-857103
TID: [-1234] [] [2024-10-30 01:12:30,021]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-857103, CORRELATION_ID = c1576f2e-6e28-4df9-a804-d0f30fdb4931
TID: [-1234] [] [2024-10-30 01:12:32,385]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 01:12:32,386]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:60002, CORRELATION_ID = 04f1f487-3019-4d84-8909-23af59d8cd17, CONNECTION = http-incoming-857104
TID: [-1234] [] [2024-10-30 01:12:32,386]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 04f1f487-3019-4d84-8909-23af59d8cd17
TID: [-1234] [] [2024-10-30 01:12:32,386]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45850, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 04f1f487-3019-4d84-8909-23af59d8cd17
TID: [-1234] [] [2024-10-30 01:12:32,387]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 01:12:32,400]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-857104, CORRELATION_ID = 04f1f487-3019-4d84-8909-23af59d8cd17
TID: [-1234] [] [2024-10-30 01:15:30,428]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 01:15:30,429]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:33804, CORRELATION_ID = be992634-9dca-4191-b6f4-acf4ae6279ff, CONNECTION = http-incoming-857105
TID: [-1234] [] [2024-10-30 01:15:30,440]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = be992634-9dca-4191-b6f4-acf4ae6279ff
TID: [-1234] [] [2024-10-30 01:15:30,441]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45866, SOCKET_TIMEOUT = 180000, CORRELATION_ID = be992634-9dca-4191-b6f4-acf4ae6279ff
TID: [-1234] [] [2024-10-30 01:15:30,442]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 01:15:30,454]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-857105, CORRELATION_ID = be992634-9dca-4191-b6f4-acf4ae6279ff
TID: [-1234] [] [2024-10-30 01:18:31,032]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 6adca6e9-c06c-433c-b654-36d1adac1ac1
TID: [-1234] [] [2024-10-30 01:18:31,033]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45871, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 6adca6e9-c06c-433c-b654-36d1adac1ac1
TID: [-1234] [] [2024-10-30 01:18:31,034]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 01:18:31,110]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = a8f2ad01-2944-4d69-be59-e3f9f9e63dcf
TID: [-1234] [] [2024-10-30 01:18:31,110]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45874, SOCKET_TIMEOUT = 180000, CORRELATION_ID = a8f2ad01-2944-4d69-be59-e3f9f9e63dcf
TID: [-1234] [] [2024-10-30 01:18:31,111]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 01:18:32,319]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45877, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 38480149-7698-415a-8441-9b689f32ccce
TID: [-1234] [] [2024-10-30 01:18:32,320]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 01:18:32,819]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45880, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 1c89586f-71ee-4b3e-bd5d-26e5c40af933
TID: [-1234] [] [2024-10-30 01:18:32,820]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 01:18:33,553]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 01:18:33,554]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:53758, CORRELATION_ID = 27d7f6d5-10b6-40df-8ade-edefc1395d11, CONNECTION = http-incoming-857139
TID: [-1234] [] [2024-10-30 01:18:33,677]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45872, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 27d7f6d5-10b6-40df-8ade-edefc1395d11
TID: [-1234] [] [2024-10-30 01:18:33,678]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 01:18:33,690]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-857139, CORRELATION_ID = 27d7f6d5-10b6-40df-8ade-edefc1395d11
TID: [-1234] [] [2024-10-30 01:21:38,672]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3d3e6496-0a05-47ce-91a2-d46335ffd770
TID: [-1234] [] [2024-10-30 01:21:41,530]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2328a473-e9a4-4a7f-9e62-a842c4326ed9
TID: [-1234] [] [2024-10-30 01:21:43,778]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6980069b-13e8-459a-9f8a-ef10462549fc
TID: [-1234] [] [2024-10-30 01:31:49,779]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-10-30 01:48:27,669]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cpanel/page/login.aspx, HEALTH CHECK URL = /cpanel/page/login.aspx
TID: [-1234] [] [2024-10-30 02:01:50,241]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-10-30 02:05:52,453]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 7cf744c2-e8d8-4abe-aeb3-b229ccf4f0a0
TID: [-1234] [] [2024-10-30 02:05:52,454]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45888, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 7cf744c2-e8d8-4abe-aeb3-b229ccf4f0a0
TID: [-1234] [] [2024-10-30 02:05:52,455]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 02:05:59,447]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45887, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 12b572f6-68bc-4ee9-8b75-42934fa142c0
TID: [-1234] [] [2024-10-30 02:05:59,448]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 02:05:59,458]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 02:05:59,459]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:45590, CORRELATION_ID = 12b572f6-68bc-4ee9-8b75-42934fa142c0, CONNECTION = http-incoming-857630
TID: [-1234] [] [2024-10-30 02:05:59,460]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-857630, CORRELATION_ID = 12b572f6-68bc-4ee9-8b75-42934fa142c0
TID: [-1234] [] [2024-10-30 02:09:11,213]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 02:09:11,214]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:57476, CORRELATION_ID = 6155bef6-f090-4181-8f5f-ed560cce026a, CONNECTION = http-incoming-857652
TID: [-1234] [] [2024-10-30 02:09:11,364]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 6155bef6-f090-4181-8f5f-ed560cce026a
TID: [-1234] [] [2024-10-30 02:09:11,365]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45902, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 6155bef6-f090-4181-8f5f-ed560cce026a
TID: [-1234] [] [2024-10-30 02:09:11,366]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 02:09:11,377]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-857652, CORRELATION_ID = 6155bef6-f090-4181-8f5f-ed560cce026a
TID: [-1234] [] [2024-10-30 02:09:13,390]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45903, SOCKET_TIMEOUT = 180000, CORRELATION_ID = c9e52712-e89a-4478-8d17-c9d9da5dede7
TID: [-1234] [] [2024-10-30 02:09:13,391]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 02:09:14,201]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 02:09:14,202]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:57652, CORRELATION_ID = 90abd0ed-c4d2-4f26-86b0-c4e0b71fcf2f, CONNECTION = http-incoming-857671
TID: [-1234] [] [2024-10-30 02:09:14,365]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45901, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 90abd0ed-c4d2-4f26-86b0-c4e0b71fcf2f
TID: [-1234] [] [2024-10-30 02:09:14,366]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 02:09:14,377]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-857671, CORRELATION_ID = 90abd0ed-c4d2-4f26-86b0-c4e0b71fcf2f
TID: [-1234] [] [2024-10-30 02:09:16,500]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = e4e48fdd-cda0-4cfb-bf88-5587053d2f34
TID: [-1234] [] [2024-10-30 02:09:16,501]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45910, SOCKET_TIMEOUT = 180000, CORRELATION_ID = e4e48fdd-cda0-4cfb-bf88-5587053d2f34
TID: [-1234] [] [2024-10-30 02:09:16,502]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 02:09:17,579]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 02:09:17,580]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:57862, CORRELATION_ID = 71e10f4f-1821-4401-a798-b54d8e70fb13, CONNECTION = http-incoming-857694
TID: [-1234] [] [2024-10-30 02:09:17,596]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 71e10f4f-1821-4401-a798-b54d8e70fb13
TID: [-1234] [] [2024-10-30 02:09:17,597]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45893, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 71e10f4f-1821-4401-a798-b54d8e70fb13
TID: [-1234] [] [2024-10-30 02:09:17,597]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 02:09:17,609]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-857694, CORRELATION_ID = 71e10f4f-1821-4401-a798-b54d8e70fb13
TID: [-1234] [] [2024-10-30 02:09:32,945]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2ef9633b-1672-4d3e-8ce0-d88ce95b3116
TID: [-1234] [] [2024-10-30 02:12:14,599]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 8a5971d5-2411-463d-98ce-d26c75fe6ffc
TID: [-1234] [] [2024-10-30 02:12:14,600]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45890, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 8a5971d5-2411-463d-98ce-d26c75fe6ffc
TID: [-1234] [] [2024-10-30 02:12:14,600]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 02:12:17,974]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 02:12:17,974]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:39022, CORRELATION_ID = 7b045007-de15-4b83-a66c-4b5c92a75110, CONNECTION = http-incoming-857711
TID: [-1234] [] [2024-10-30 02:12:17,979]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45900, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 7b045007-de15-4b83-a66c-4b5c92a75110
TID: [-1234] [] [2024-10-30 02:12:17,980]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 02:12:17,993]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-857711, CORRELATION_ID = 7b045007-de15-4b83-a66c-4b5c92a75110
TID: [-1234] [] [2024-10-30 02:15:19,324]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 02:15:19,325]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:44592, CORRELATION_ID = d44aa597-e2ee-4cf5-a4d8-143865e898cb, CONNECTION = http-incoming-857716
TID: [-1234] [] [2024-10-30 02:15:19,352]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45911, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 8aef86df-bf87-4387-9a9d-a5ea1e8fd3fb
TID: [-1234] [] [2024-10-30 02:15:19,353]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 02:15:19,379]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = d44aa597-e2ee-4cf5-a4d8-143865e898cb
TID: [-1234] [] [2024-10-30 02:15:19,380]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45914, SOCKET_TIMEOUT = 180000, CORRELATION_ID = d44aa597-e2ee-4cf5-a4d8-143865e898cb
TID: [-1234] [] [2024-10-30 02:15:19,380]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 02:15:19,392]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-857716, CORRELATION_ID = d44aa597-e2ee-4cf5-a4d8-143865e898cb
TID: [-1234] [] [2024-10-30 02:15:26,784]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 02:15:26,785]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:55026, CORRELATION_ID = f0546c16-6208-4a40-9a95-0b77d756448c, CONNECTION = http-incoming-857741
TID: [-1234] [] [2024-10-30 02:15:26,859]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45915, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 5f839cb4-5b2e-46ae-8e9b-a5d3ab251fb8
TID: [-1234] [] [2024-10-30 02:15:26,860]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 02:15:26,882]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = f0546c16-6208-4a40-9a95-0b77d756448c
TID: [-1234] [] [2024-10-30 02:15:26,883]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45919, SOCKET_TIMEOUT = 180000, CORRELATION_ID = f0546c16-6208-4a40-9a95-0b77d756448c
TID: [-1234] [] [2024-10-30 02:15:26,883]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 02:15:26,896]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-857741, CORRELATION_ID = f0546c16-6208-4a40-9a95-0b77d756448c
TID: [-1234] [] [2024-10-30 02:15:27,707]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45912, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 9b8590ce-6654-42d0-a1dc-a57288123324
TID: [-1234] [] [2024-10-30 02:15:27,708]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 02:18:32,708]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 279bec6e-bae1-4518-a096-2ca8333ba141
TID: [-1234] [] [2024-10-30 02:18:32,927]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = abf8f3dd-0a1e-47be-a637-fd06adc7f7cc
TID: [-1234] [] [2024-10-30 02:31:50,660]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-10-30 03:01:50,921]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-10-30 03:05:33,699]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 03:05:33,700]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:34400, CORRELATION_ID = 35e63e35-a98c-4837-b5a6-2963e7935743, CONNECTION = http-incoming-858231
TID: [-1234] [] [2024-10-30 03:05:33,701]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 67336553-01d6-43dd-827f-a7871bfffab0
TID: [-1234] [] [2024-10-30 03:05:33,705]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45937, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 35e63e35-a98c-4837-b5a6-2963e7935743
TID: [-1234] [] [2024-10-30 03:05:33,707]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 03:05:33,723]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-858231, CORRELATION_ID = 35e63e35-a98c-4837-b5a6-2963e7935743
TID: [-1234] [] [2024-10-30 03:05:34,748]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1b8ba8ad-9ade-427b-b735-909db4a020ab
TID: [-1234] [] [2024-10-30 03:05:34,984]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b5dc9dd9-4c91-4540-a78f-c3745d176f1a
TID: [-1234] [] [2024-10-30 03:05:39,552]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45934, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 519327d7-00dc-477e-be23-8f823a1c34df
TID: [-1234] [] [2024-10-30 03:05:39,553]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 03:05:39,561]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 03:05:39,562]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:34460, CORRELATION_ID = 519327d7-00dc-477e-be23-8f823a1c34df, CONNECTION = http-incoming-858237
TID: [-1234] [] [2024-10-30 03:05:39,564]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-858237, CORRELATION_ID = 519327d7-00dc-477e-be23-8f823a1c34df
TID: [-1234] [] [2024-10-30 03:08:48,818]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 03:08:48,819]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:33064, CORRELATION_ID = 57a5d0d0-9630-4557-9a3f-027b6d2ce583, CONNECTION = http-incoming-858260
TID: [-1234] [] [2024-10-30 03:08:49,182]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45929, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 57a5d0d0-9630-4557-9a3f-027b6d2ce583
TID: [-1234] [] [2024-10-30 03:08:49,183]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 03:08:49,196]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-858260, CORRELATION_ID = 57a5d0d0-9630-4557-9a3f-027b6d2ce583
TID: [-1234] [] [2024-10-30 03:08:52,037]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45944, SOCKET_TIMEOUT = 180000, CORRELATION_ID = e5a8dea8-da01-493a-9611-be213916df9c
TID: [-1234] [] [2024-10-30 03:08:52,038]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 03:08:52,983]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 03:08:52,985]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:55476, CORRELATION_ID = 1b87e1e9-a436-49bd-a0ed-27f12db789f7, CONNECTION = http-incoming-858292
TID: [-1234] [] [2024-10-30 03:08:53,382]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45930, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 1b87e1e9-a436-49bd-a0ed-27f12db789f7
TID: [-1234] [] [2024-10-30 03:08:53,383]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 03:08:53,398]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-858292, CORRELATION_ID = 1b87e1e9-a436-49bd-a0ed-27f12db789f7
TID: [-1234] [] [2024-10-30 03:09:06,864]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 292a6033-5167-44bf-a518-8249c5f15746
TID: [-1234] [] [2024-10-30 03:11:52,913]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 03:11:52,914]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45948, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 20c3c19e-fc40-4fb7-a2ac-f681628edfd6
TID: [-1234] [] [2024-10-30 03:11:52,914]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:50962, CORRELATION_ID = 20c3c19e-fc40-4fb7-a2ac-f681628edfd6, CONNECTION = http-incoming-858316
TID: [-1234] [] [2024-10-30 03:11:52,915]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 03:11:52,926]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-858316, CORRELATION_ID = 20c3c19e-fc40-4fb7-a2ac-f681628edfd6
TID: [-1234] [] [2024-10-30 03:11:53,412]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 03:11:53,413]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:50974, CORRELATION_ID = 40aa078c-1a23-428c-bd6c-c859938e6925, CONNECTION = http-incoming-858317
TID: [-1234] [] [2024-10-30 03:11:53,423]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45950, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 40aa078c-1a23-428c-bd6c-c859938e6925
TID: [-1234] [] [2024-10-30 03:11:53,424]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 03:11:53,436]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-858317, CORRELATION_ID = 40aa078c-1a23-428c-bd6c-c859938e6925
TID: [-1234] [] [2024-10-30 03:14:54,811]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45953, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 21601fb5-2bc2-4aee-a468-9c0426f88b23
TID: [-1234] [] [2024-10-30 03:14:54,813]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 03:14:54,917]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 31d5c1c3-4a70-4074-bc8d-90d9b7d7904e
TID: [-1234] [] [2024-10-30 03:14:54,918]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45961, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 31d5c1c3-4a70-4074-bc8d-90d9b7d7904e
TID: [-1234] [] [2024-10-30 03:14:54,919]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 03:14:56,199]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45958, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 4c76bd89-f917-421e-ab5c-36cf39f98f3f
TID: [-1234] [] [2024-10-30 03:14:56,200]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 03:14:56,967]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 03:14:56,968]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:60178, CORRELATION_ID = 4bb5e265-eaf3-42e8-aafc-c4d8d5282dbb, CONNECTION = http-incoming-858345
TID: [-1234] [] [2024-10-30 03:14:57,094]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 4bb5e265-eaf3-42e8-aafc-c4d8d5282dbb
TID: [-1234] [] [2024-10-30 03:14:57,094]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45954, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 4bb5e265-eaf3-42e8-aafc-c4d8d5282dbb
TID: [-1234] [] [2024-10-30 03:14:57,095]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 03:14:57,106]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-858345, CORRELATION_ID = 4bb5e265-eaf3-42e8-aafc-c4d8d5282dbb
TID: [-1234] [] [2024-10-30 03:14:57,497]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 32693182-22dd-4449-898b-89f58000277a
TID: [-1234] [] [2024-10-30 03:14:57,497]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45965, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 32693182-22dd-4449-898b-89f58000277a
TID: [-1234] [] [2024-10-30 03:14:57,498]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 03:31:51,250]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-10-30 04:01:51,589]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-10-30 04:02:45,001] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2024-10-30 04:06:05,257]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45976, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 98653e1f-a1eb-48af-b21d-98e95ab16ede
TID: [-1234] [] [2024-10-30 04:06:05,258]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 04:06:12,597]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45982, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 2e6de6d7-68d7-4a83-97b8-1e15b1093e5c
TID: [-1234] [] [2024-10-30 04:06:12,598]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 04:09:20,743]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45989, SOCKET_TIMEOUT = 180000, CORRELATION_ID = c5a19aea-e499-41b9-944e-cd509e9eb133
TID: [-1234] [] [2024-10-30 04:09:20,745]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 04:09:21,003]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 04:09:21,004]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:45936, CORRELATION_ID = 0159c860-f200-432b-8844-5bfc66af667f, CONNECTION = http-incoming-858876
TID: [-1234] [] [2024-10-30 04:09:21,060]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45984, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 0159c860-f200-432b-8844-5bfc66af667f
TID: [-1234] [] [2024-10-30 04:09:21,061]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 04:09:21,072]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-858876, CORRELATION_ID = 0159c860-f200-432b-8844-5bfc66af667f
TID: [-1234] [] [2024-10-30 04:09:21,503]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45988, SOCKET_TIMEOUT = 180000, CORRELATION_ID = f182fb2a-606f-4e2b-a38e-822438085539
TID: [-1234] [] [2024-10-30 04:09:21,504]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 04:09:21,516]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:45986, CORRELATION_ID = f182fb2a-606f-4e2b-a38e-822438085539, CONNECTION = http-incoming-858880
TID: [-1234] [] [2024-10-30 04:09:21,838]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 4bd0fa7e-0c8b-4470-949a-0ea7a15c3f12
TID: [-1234] [] [2024-10-30 04:09:21,839]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45994, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 4bd0fa7e-0c8b-4470-949a-0ea7a15c3f12
TID: [-1234] [] [2024-10-30 04:09:21,839]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 04:09:24,745]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45993, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 09ec2d5f-a8a7-443e-84e2-c6976d9367d6
TID: [-1234] [] [2024-10-30 04:09:24,746]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 04:12:21,492]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45990, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 3b3311e4-0df8-45a2-aa63-8419595ce81f
TID: [-1234] [] [2024-10-30 04:12:21,494]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 04:12:25,293]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 04:12:25,294]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:57320, CORRELATION_ID = a85b5dc4-4914-42bb-ac23-b5cab76e031f, CONNECTION = http-incoming-858921
TID: [-1234] [] [2024-10-30 04:12:25,310]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = a85b5dc4-4914-42bb-ac23-b5cab76e031f
TID: [-1234] [] [2024-10-30 04:12:25,311]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45991, SOCKET_TIMEOUT = 180000, CORRELATION_ID = a85b5dc4-4914-42bb-ac23-b5cab76e031f
TID: [-1234] [] [2024-10-30 04:12:25,311]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 04:12:25,323]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-858921, CORRELATION_ID = a85b5dc4-4914-42bb-ac23-b5cab76e031f
TID: [-1234] [] [2024-10-30 04:15:25,926]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 04:15:25,927]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:60404, CORRELATION_ID = 9b6832dd-cd40-4c14-84fc-d18c48719868, CONNECTION = http-incoming-858929
TID: [-1234] [] [2024-10-30 04:15:25,936]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 6f98de53-5fa1-465d-9c4a-8e66ac7e58a8
TID: [-1234] [] [2024-10-30 04:15:25,936]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45999, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 6f98de53-5fa1-465d-9c4a-8e66ac7e58a8
TID: [-1234] [] [2024-10-30 04:15:25,937]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 04:15:26,014]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 9b6832dd-cd40-4c14-84fc-d18c48719868
TID: [-1234] [] [2024-10-30 04:15:26,015]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46003, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 9b6832dd-cd40-4c14-84fc-d18c48719868
TID: [-1234] [] [2024-10-30 04:15:26,017]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 04:15:26,027]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-858929, CORRELATION_ID = 9b6832dd-cd40-4c14-84fc-d18c48719868
TID: [-1234] [] [2024-10-30 04:15:26,941]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45998, SOCKET_TIMEOUT = 180000, CORRELATION_ID = cea74d9d-a2ae-498c-a0dc-d44daee1e0f9
TID: [-1234] [] [2024-10-30 04:15:26,942]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 04:15:27,860]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-45997, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 7ca98033-347f-4050-9819-f9765233ac16
TID: [-1234] [] [2024-10-30 04:15:27,861]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 04:15:28,768]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46001, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 58e2008a-7958-43e0-8a4e-ab94565a5c74
TID: [-1234] [] [2024-10-30 04:15:28,769]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 04:18:35,464]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ce795b3c-476e-45a8-85e5-598137237b2f
TID: [-1234] [] [2024-10-30 04:18:37,509]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 940fe7fc-89eb-4af2-af5f-05c848310d8a
TID: [-1234] [] [2024-10-30 04:18:38,447]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d161f394-7906-4955-8619-af1ff89e0c88
TID: [-1234] [] [2024-10-30 04:31:51,828]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-10-30 05:01:52,009]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-10-30 05:05:47,203]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 05:05:47,205]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:38834, CORRELATION_ID = 3ac1054e-fbc3-4c99-8b5b-ece764a2ae91, CONNECTION = http-incoming-859440
TID: [-1234] [] [2024-10-30 05:05:47,271]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 3ac1054e-fbc3-4c99-8b5b-ece764a2ae91
TID: [-1234] [] [2024-10-30 05:05:47,272]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46021, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 3ac1054e-fbc3-4c99-8b5b-ece764a2ae91
TID: [-1234] [] [2024-10-30 05:05:47,273]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 05:05:47,288]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-859440, CORRELATION_ID = 3ac1054e-fbc3-4c99-8b5b-ece764a2ae91
TID: [-1234] [] [2024-10-30 05:05:55,230]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = bbb19eda-aeee-4690-860c-f58cdae0f920
TID: [-1234] [] [2024-10-30 05:05:55,231]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46014, SOCKET_TIMEOUT = 180000, CORRELATION_ID = bbb19eda-aeee-4690-860c-f58cdae0f920
TID: [-1234] [] [2024-10-30 05:05:55,232]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 05:08:59,564]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 42192fcf-5079-4ec2-bbca-52e97834e8d0
TID: [-1234] [] [2024-10-30 05:08:59,565]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46039, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 42192fcf-5079-4ec2-bbca-52e97834e8d0
TID: [-1234] [] [2024-10-30 05:08:59,566]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 05:08:59,577]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 05:08:59,578]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:52010, CORRELATION_ID = 42192fcf-5079-4ec2-bbca-52e97834e8d0, CONNECTION = http-incoming-859472
TID: [-1234] [] [2024-10-30 05:08:59,579]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-859472, CORRELATION_ID = 42192fcf-5079-4ec2-bbca-52e97834e8d0
TID: [-1234] [] [2024-10-30 05:09:01,825]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 05:09:01,827]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:53326, CORRELATION_ID = 7a7168d4-0d8b-4ec3-8166-96a1abc08d7f, CONNECTION = http-incoming-859494
TID: [-1234] [] [2024-10-30 05:09:02,275]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 7a7168d4-0d8b-4ec3-8166-96a1abc08d7f
TID: [-1234] [] [2024-10-30 05:09:02,276]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46016, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 7a7168d4-0d8b-4ec3-8166-96a1abc08d7f
TID: [-1234] [] [2024-10-30 05:09:02,276]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 05:09:02,288]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-859494, CORRELATION_ID = 7a7168d4-0d8b-4ec3-8166-96a1abc08d7f
TID: [-1234] [] [2024-10-30 05:09:02,607]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = b6f0e320-081a-4425-8455-21330ff6691e
TID: [-1234] [] [2024-10-30 05:09:02,608]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46035, SOCKET_TIMEOUT = 180000, CORRELATION_ID = b6f0e320-081a-4425-8455-21330ff6691e
TID: [-1234] [] [2024-10-30 05:09:02,608]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 05:09:03,330]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46031, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 0e6728bd-12e3-4a58-9fc2-40c8285f88b4
TID: [-1234] [] [2024-10-30 05:09:03,331]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 05:09:05,376]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46040, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 582c9ddd-a322-4eae-804c-bdef861f8c39
TID: [-1234] [] [2024-10-30 05:09:05,376]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 05:09:05,377]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 05:09:05,377]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:53552, CORRELATION_ID = 582c9ddd-a322-4eae-804c-bdef861f8c39, CONNECTION = http-incoming-859513
TID: [-1234] [] [2024-10-30 05:09:05,389]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-859513, CORRELATION_ID = 582c9ddd-a322-4eae-804c-bdef861f8c39
TID: [-1234] [] [2024-10-30 05:12:03,038]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 05:12:03,039]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:44996, CORRELATION_ID = 9142ed17-a0d8-46e8-83a6-15568ed7fd35, CONNECTION = http-incoming-859526
TID: [-1234] [] [2024-10-30 05:12:03,054]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 9142ed17-a0d8-46e8-83a6-15568ed7fd35
TID: [-1234] [] [2024-10-30 05:12:03,055]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46036, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 9142ed17-a0d8-46e8-83a6-15568ed7fd35
TID: [-1234] [] [2024-10-30 05:12:03,055]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 05:12:03,067]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-859526, CORRELATION_ID = 9142ed17-a0d8-46e8-83a6-15568ed7fd35
TID: [-1234] [] [2024-10-30 05:12:05,806]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 05:12:05,807]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = e8a66558-fbd0-4d89-8d24-5ae0197b3edb
TID: [-1234] [] [2024-10-30 05:12:05,807]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:45012, CORRELATION_ID = e8a66558-fbd0-4d89-8d24-5ae0197b3edb, CONNECTION = http-incoming-859527
TID: [-1234] [] [2024-10-30 05:12:05,807]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46032, SOCKET_TIMEOUT = 180000, CORRELATION_ID = e8a66558-fbd0-4d89-8d24-5ae0197b3edb
TID: [-1234] [] [2024-10-30 05:12:05,808]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 05:12:05,820]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-859527, CORRELATION_ID = e8a66558-fbd0-4d89-8d24-5ae0197b3edb
TID: [-1234] [] [2024-10-30 05:15:07,336]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 1258cf77-5709-4405-a7ad-3adc9fb82e33
TID: [-1234] [] [2024-10-30 05:15:07,336]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46044, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 1258cf77-5709-4405-a7ad-3adc9fb82e33
TID: [-1234] [] [2024-10-30 05:15:07,337]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 05:15:07,355]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = deac1882-2ede-4331-917e-0fc805cf340d
TID: [-1234] [] [2024-10-30 05:15:07,356]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46050, SOCKET_TIMEOUT = 180000, CORRELATION_ID = deac1882-2ede-4331-917e-0fc805cf340d
TID: [-1234] [] [2024-10-30 05:15:07,356]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 05:15:08,294]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46047, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 336d4af7-edaf-43cb-8f39-c2571fcd2008
TID: [-1234] [] [2024-10-30 05:15:08,295]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 05:15:08,757]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 05:15:08,757]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:42190, CORRELATION_ID = bd298091-413b-4604-a986-7f68856fb7a7, CONNECTION = http-incoming-859551
TID: [-1234] [] [2024-10-30 05:15:08,906]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46049, SOCKET_TIMEOUT = 180000, CORRELATION_ID = bd298091-413b-4604-a986-7f68856fb7a7
TID: [-1234] [] [2024-10-30 05:15:08,907]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 05:15:08,920]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-859551, CORRELATION_ID = bd298091-413b-4604-a986-7f68856fb7a7
TID: [-1234] [] [2024-10-30 05:15:09,553]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46045, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 4a8a0abc-dac8-4987-92d5-46929d1b46a2
TID: [-1234] [] [2024-10-30 05:15:09,554]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 05:18:14,202]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1c114055-86cf-43c8-a3bb-b4a83fb1d0c8
TID: [-1234] [] [2024-10-30 05:18:16,358]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a173cc0f-1287-4091-a5eb-f45ceceb6e3b
TID: [-1234] [] [2024-10-30 05:18:16,956]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f86897be-0dd8-449b-9550-a54f7181f353
TID: [-1234] [] [2024-10-30 05:31:52,318]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-10-30 06:01:52,862]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-10-30 06:09:47,872]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 4b6015ce-3d49-4e55-885f-2eb4bbf7120c
TID: [-1234] [] [2024-10-30 06:09:47,874]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46061, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 4b6015ce-3d49-4e55-885f-2eb4bbf7120c
TID: [-1234] [] [2024-10-30 06:09:47,874]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 06:09:50,462]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 06:09:50,463]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:42032, CORRELATION_ID = 5a3abe80-fe17-4187-bb62-9f8cb5e2f095, CONNECTION = http-incoming-860043
TID: [-1234] [] [2024-10-30 06:09:50,477]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46063, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 5a3abe80-fe17-4187-bb62-9f8cb5e2f095
TID: [-1234] [] [2024-10-30 06:09:50,478]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 06:09:50,489]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-860043, CORRELATION_ID = 5a3abe80-fe17-4187-bb62-9f8cb5e2f095
TID: [-1234] [] [2024-10-30 06:13:06,286]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46064, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 9efb8d6b-2c57-4953-b9e1-427a832cd0b8
TID: [-1234] [] [2024-10-30 06:13:06,287]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 06:13:06,287]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46062, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 61e3ca7d-2f56-4ca4-a15f-f4cd03f05230
TID: [-1234] [] [2024-10-30 06:13:06,288]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 06:13:06,288]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 06:13:06,288]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:40628, CORRELATION_ID = 61e3ca7d-2f56-4ca4-a15f-f4cd03f05230, CONNECTION = http-incoming-860091
TID: [-1234] [] [2024-10-30 06:13:06,301]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 06:13:06,302]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:40612, CORRELATION_ID = 9efb8d6b-2c57-4953-b9e1-427a832cd0b8, CONNECTION = http-incoming-860090
TID: [-1234] [] [2024-10-30 06:13:06,304]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-860090, CORRELATION_ID = 9efb8d6b-2c57-4953-b9e1-427a832cd0b8
TID: [-1234] [] [2024-10-30 06:13:06,307]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-860091, CORRELATION_ID = 61e3ca7d-2f56-4ca4-a15f-f4cd03f05230
TID: [-1234] [] [2024-10-30 06:13:08,722]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 5e5a705c-926e-4b40-b3c5-2219360f722a
TID: [-1234] [] [2024-10-30 06:13:08,723]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46073, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 5e5a705c-926e-4b40-b3c5-2219360f722a
TID: [-1234] [] [2024-10-30 06:13:08,724]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 06:13:12,688]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 06:13:12,689]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:49274, CORRELATION_ID = defc8f05-d994-4408-a88c-50cd3cc52150, CONNECTION = http-incoming-860115
TID: [-1234] [] [2024-10-30 06:13:12,698]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = defc8f05-d994-4408-a88c-50cd3cc52150
TID: [-1234] [] [2024-10-30 06:13:12,698]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46076, SOCKET_TIMEOUT = 180000, CORRELATION_ID = defc8f05-d994-4408-a88c-50cd3cc52150
TID: [-1234] [] [2024-10-30 06:13:12,699]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 06:13:12,711]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-860115, CORRELATION_ID = defc8f05-d994-4408-a88c-50cd3cc52150
TID: [-1234] [] [2024-10-30 06:13:27,799]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2915868f-7272-4ae5-83be-acd3706d1cbb
TID: [-1234] [] [2024-10-30 06:13:32,603]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fe93d00d-292e-4071-8f2a-6dd10fe83fdb
TID: [-1234] [] [2024-10-30 06:13:47,328]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ae917eea-1146-46e9-9f39-d37e01ce16f9
TID: [-1234] [] [2024-10-30 06:16:07,531]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46071, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 42c2345b-8eb9-4c33-ad72-d41f116ec41b
TID: [-1234] [] [2024-10-30 06:16:07,532]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 06:16:07,538]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 06:16:07,539]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:52994, CORRELATION_ID = 42c2345b-8eb9-4c33-ad72-d41f116ec41b, CONNECTION = http-incoming-860128
TID: [-1234] [] [2024-10-30 06:16:07,545]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-860128, CORRELATION_ID = 42c2345b-8eb9-4c33-ad72-d41f116ec41b
TID: [-1234] [] [2024-10-30 06:16:13,169]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 93f77900-6822-4de9-98f5-9272e92c877a
TID: [-1234] [] [2024-10-30 06:16:13,169]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 06:16:13,170]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:42820, CORRELATION_ID = 93f77900-6822-4de9-98f5-9272e92c877a, CONNECTION = http-incoming-860129
TID: [-1234] [] [2024-10-30 06:16:13,170]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46077, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 93f77900-6822-4de9-98f5-9272e92c877a
TID: [-1234] [] [2024-10-30 06:16:13,171]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 06:16:13,183]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-860129, CORRELATION_ID = 93f77900-6822-4de9-98f5-9272e92c877a
TID: [-1234] [] [2024-10-30 06:19:07,951]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 06:19:07,952]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:33202, CORRELATION_ID = af4a7402-cf60-4de6-9592-37e1f9033375, CONNECTION = http-incoming-860138
TID: [-1234] [] [2024-10-30 06:19:07,968]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = af4a7402-cf60-4de6-9592-37e1f9033375
TID: [-1234] [] [2024-10-30 06:19:07,969]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46079, SOCKET_TIMEOUT = 180000, CORRELATION_ID = af4a7402-cf60-4de6-9592-37e1f9033375
TID: [-1234] [] [2024-10-30 06:19:07,970]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 06:19:07,981]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-860138, CORRELATION_ID = af4a7402-cf60-4de6-9592-37e1f9033375
TID: [-1234] [] [2024-10-30 06:22:09,344]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 4876178c-e858-4af8-aec2-2b1495d1de0b
TID: [-1234] [] [2024-10-30 06:22:09,345]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46081, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 4876178c-e858-4af8-aec2-2b1495d1de0b
TID: [-1234] [] [2024-10-30 06:22:09,346]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 06:22:09,845]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46083, SOCKET_TIMEOUT = 180000, CORRELATION_ID = dbe86d76-0b25-416c-9799-bbf3c006d371
TID: [-1234] [] [2024-10-30 06:22:09,846]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 06:22:22,353]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46084, SOCKET_TIMEOUT = 180000, CORRELATION_ID = fb13f45b-b211-4734-bdf8-46e48ef14ca1
TID: [-1234] [] [2024-10-30 06:22:22,354]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 06:22:22,360]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 06:22:22,361]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:47684, CORRELATION_ID = fb13f45b-b211-4734-bdf8-46e48ef14ca1, CONNECTION = http-incoming-860174
TID: [-1234] [] [2024-10-30 06:22:22,367]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-860174, CORRELATION_ID = fb13f45b-b211-4734-bdf8-46e48ef14ca1
TID: [-1234] [] [2024-10-30 06:22:25,364]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 06:22:25,365]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:47754, CORRELATION_ID = 33f32df4-e6c8-4383-9d33-7401e0499902, CONNECTION = http-incoming-860183
TID: [-1234] [] [2024-10-30 06:22:25,368]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 33f32df4-e6c8-4383-9d33-7401e0499902
TID: [-1234] [] [2024-10-30 06:22:25,368]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46085, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 33f32df4-e6c8-4383-9d33-7401e0499902
TID: [-1234] [] [2024-10-30 06:22:25,369]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 06:22:25,380]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-860183, CORRELATION_ID = 33f32df4-e6c8-4383-9d33-7401e0499902
TID: [-1234] [] [2024-10-30 06:25:26,447]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ffe70287-1c0d-4cdb-a238-3f07cbb5b2b2
TID: [-1234] [] [2024-10-30 06:25:26,646]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4011edcc-597e-4db7-a6c1-d0d5db9e2e1c
TID: [-1234] [] [2024-10-30 06:25:29,733]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 111cd050-7762-401c-a9f7-f2f8f6504cde
TID: [-1234] [] [2024-10-30 06:25:31,446]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 24c78228-c2a7-4018-b857-4b2e54fe4b99
TID: [-1234] [] [2024-10-30 06:31:52,968]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-10-30 06:52:45,426]  WARN {org.wso2.carbon.ui.transports.fileupload.AbstractFileUploadExecutor} - ToolsAnyFileUpload method is not supported
TID: [-1234] [] [2024-10-30 06:58:09,305]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a82c0834-146b-4308-8361-502b1ed798a2
TID: [-1234] [] [2024-10-30 07:01:53,236]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-10-30 07:05:44,429]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6f0d4d6b-d95a-4d75-bae9-d1f00f83a63d
TID: [-1234] [] [2024-10-30 07:05:44,633]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 07:05:44,633]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:57186, CORRELATION_ID = a8bc6426-36a1-4ff7-8688-d969dd9e1e1c, CONNECTION = http-incoming-860653
TID: [-1234] [] [2024-10-30 07:05:44,635]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = a8bc6426-36a1-4ff7-8688-d969dd9e1e1c
TID: [-1234] [] [2024-10-30 07:05:44,636]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46109, SOCKET_TIMEOUT = 180000, CORRELATION_ID = a8bc6426-36a1-4ff7-8688-d969dd9e1e1c
TID: [-1234] [] [2024-10-30 07:05:44,637]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 07:05:44,652]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-860653, CORRELATION_ID = a8bc6426-36a1-4ff7-8688-d969dd9e1e1c
TID: [-1234] [] [2024-10-30 07:05:44,969]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 07:05:44,969]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:57192, CORRELATION_ID = 425bf908-ddaa-4922-9f1d-c2458fc2b5ad, CONNECTION = http-incoming-860655
TID: [-1234] [] [2024-10-30 07:05:45,000]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46110, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 425bf908-ddaa-4922-9f1d-c2458fc2b5ad
TID: [-1234] [] [2024-10-30 07:05:45,001]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 07:05:45,014]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-860655, CORRELATION_ID = 425bf908-ddaa-4922-9f1d-c2458fc2b5ad
TID: [-1234] [] [2024-10-30 07:08:59,944]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46120, SOCKET_TIMEOUT = 180000, CORRELATION_ID = daa6c384-9c80-4fc3-b6be-c83f6ecb259b
TID: [-1234] [] [2024-10-30 07:08:59,945]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 07:09:00,004]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 07:09:00,005]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:46888, CORRELATION_ID = fd4feb65-8bdb-4d3a-a676-a48b9bbadf7c, CONNECTION = http-incoming-860710
TID: [-1234] [] [2024-10-30 07:09:00,619]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = fd4feb65-8bdb-4d3a-a676-a48b9bbadf7c
TID: [-1234] [] [2024-10-30 07:09:00,620]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46124, SOCKET_TIMEOUT = 180000, CORRELATION_ID = fd4feb65-8bdb-4d3a-a676-a48b9bbadf7c
TID: [-1234] [] [2024-10-30 07:09:00,620]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 07:09:00,633]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-860710, CORRELATION_ID = fd4feb65-8bdb-4d3a-a676-a48b9bbadf7c
TID: [-1234] [] [2024-10-30 07:09:01,747]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46118, SOCKET_TIMEOUT = 180000, CORRELATION_ID = d75b77ba-9788-43f3-b2ae-1f22bc9cf2cf
TID: [-1234] [] [2024-10-30 07:09:01,748]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 07:09:01,751]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 07:09:01,752]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:44832, CORRELATION_ID = d75b77ba-9788-43f3-b2ae-1f22bc9cf2cf, CONNECTION = http-incoming-860717
TID: [-1234] [] [2024-10-30 07:09:01,760]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-860717, CORRELATION_ID = d75b77ba-9788-43f3-b2ae-1f22bc9cf2cf
TID: [-1234] [] [2024-10-30 07:09:03,741]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 07:09:03,742]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:44934, CORRELATION_ID = b13d8eaf-e5cd-4a7d-baae-91116d7b6ade, CONNECTION = http-incoming-860727
TID: [-1234] [] [2024-10-30 07:09:04,196]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = b13d8eaf-e5cd-4a7d-baae-91116d7b6ade
TID: [-1234] [] [2024-10-30 07:09:04,197]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46101, SOCKET_TIMEOUT = 180000, CORRELATION_ID = b13d8eaf-e5cd-4a7d-baae-91116d7b6ade
TID: [-1234] [] [2024-10-30 07:09:04,198]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 07:09:04,209]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-860727, CORRELATION_ID = b13d8eaf-e5cd-4a7d-baae-91116d7b6ade
TID: [-1234] [] [2024-10-30 07:12:01,135]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46122, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 8f58d1bb-0cad-48c7-a19f-9408b01aafd9
TID: [-1234] [] [2024-10-30 07:12:01,136]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 07:12:01,136]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 07:12:01,136]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:34286, CORRELATION_ID = 8f58d1bb-0cad-48c7-a19f-9408b01aafd9, CONNECTION = http-incoming-860788
TID: [-1234] [] [2024-10-30 07:12:01,146]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-860788, CORRELATION_ID = 8f58d1bb-0cad-48c7-a19f-9408b01aafd9
TID: [-1234] [] [2024-10-30 07:12:04,145]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46116, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 0007630d-1981-4928-b90f-b80796327659
TID: [-1234] [] [2024-10-30 07:12:04,146]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 07:15:01,532]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 07:15:01,533]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:60058, CORRELATION_ID = aa51ddee-f77a-41b2-8168-b304d1aea40c, CONNECTION = http-incoming-860798
TID: [-1234] [] [2024-10-30 07:15:01,541]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46123, SOCKET_TIMEOUT = 180000, CORRELATION_ID = aa51ddee-f77a-41b2-8168-b304d1aea40c
TID: [-1234] [] [2024-10-30 07:15:01,542]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 07:15:01,554]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-860798, CORRELATION_ID = aa51ddee-f77a-41b2-8168-b304d1aea40c
TID: [-1234] [] [2024-10-30 07:18:02,961]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = e075f017-cc1c-41e1-a43f-eafa8b1a9cb5
TID: [-1234] [] [2024-10-30 07:18:02,962]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46179, SOCKET_TIMEOUT = 180000, CORRELATION_ID = e075f017-cc1c-41e1-a43f-eafa8b1a9cb5
TID: [-1234] [] [2024-10-30 07:18:02,963]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 07:18:02,967]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 07:18:02,968]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:43186, CORRELATION_ID = a6bf6157-1d52-4259-b701-08855229c864, CONNECTION = http-incoming-860814
TID: [-1234] [] [2024-10-30 07:18:03,008]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = a6bf6157-1d52-4259-b701-08855229c864
TID: [-1234] [] [2024-10-30 07:18:03,009]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46174, SOCKET_TIMEOUT = 180000, CORRELATION_ID = a6bf6157-1d52-4259-b701-08855229c864
TID: [-1234] [] [2024-10-30 07:18:03,010]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 07:18:03,020]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-860814, CORRELATION_ID = a6bf6157-1d52-4259-b701-08855229c864
TID: [-1234] [] [2024-10-30 07:18:04,622]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46178, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 4469f3f0-c9bc-41af-9f42-15591d78be04
TID: [-1234] [] [2024-10-30 07:18:04,623]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 07:18:04,797]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46181, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 46e7dcfb-302e-4fdc-89bc-cc214a3b310c
TID: [-1234] [] [2024-10-30 07:18:04,798]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 07:18:04,857]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 4dc66b26-536d-4c6e-9d26-5bb6898e6d73
TID: [-1234] [] [2024-10-30 07:18:04,858]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46177, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 4dc66b26-536d-4c6e-9d26-5bb6898e6d73
TID: [-1234] [] [2024-10-30 07:18:04,859]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 07:21:13,413]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2dac334a-500f-4ec5-a2ad-fa629833e3cf
TID: [-1234] [] [2024-10-30 07:21:15,515]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 60ea67cf-49d6-4b36-97c2-cb701bdaabe2
TID: [-1234] [] [2024-10-30 07:31:53,374]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-10-30 07:35:20,746]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0b878c5b-2e3f-4455-a038-fda130b23d09
TID: [-1234] [] [2024-10-30 07:35:21,343]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7075de53-f5f1-478c-a55f-cfaf72075c1b
TID: [-1234] [] [2024-10-30 08:01:53,841]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-10-30 08:05:45,944]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46203, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 97ccf364-3511-4453-bc1e-174075af55cb
TID: [-1234] [] [2024-10-30 08:05:45,945]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 08:05:47,762]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7527badd-3f0c-4ef3-9d92-a8c592d5f230
TID: [-1234] [] [2024-10-30 08:05:51,659]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46205, SOCKET_TIMEOUT = 180000, CORRELATION_ID = a07f1bd9-fe4d-4c5b-a9d0-5a2a4b934853
TID: [-1234] [] [2024-10-30 08:05:51,660]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 08:05:51,666]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 08:05:51,666]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:40634, CORRELATION_ID = a07f1bd9-fe4d-4c5b-a9d0-5a2a4b934853, CONNECTION = http-incoming-861365
TID: [-1234] [] [2024-10-30 08:05:51,671]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-861365, CORRELATION_ID = a07f1bd9-fe4d-4c5b-a9d0-5a2a4b934853
TID: [-1234] [] [2024-10-30 08:09:00,919]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 2b346757-64c0-4702-bde4-390acd90689e
TID: [-1234] [] [2024-10-30 08:09:00,920]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46221, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 2b346757-64c0-4702-bde4-390acd90689e
TID: [-1234] [] [2024-10-30 08:09:00,920]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 08:09:03,874]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = f45e3924-8030-45d9-888f-d15295fad791
TID: [-1234] [] [2024-10-30 08:09:03,875]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46218, SOCKET_TIMEOUT = 180000, CORRELATION_ID = f45e3924-8030-45d9-888f-d15295fad791
TID: [-1234] [] [2024-10-30 08:09:03,875]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 08:09:25,075]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 08:09:25,076]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:49382, CORRELATION_ID = 17a31228-754f-490f-b571-f82302848aa5, CONNECTION = http-incoming-861474
TID: [-1234] [] [2024-10-30 08:09:25,085]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46223, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 17a31228-754f-490f-b571-f82302848aa5
TID: [-1234] [] [2024-10-30 08:09:25,086]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 08:09:25,097]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-861474, CORRELATION_ID = 17a31228-754f-490f-b571-f82302848aa5
TID: [-1234] [] [2024-10-30 08:12:26,223]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = c6afdbcb-ad30-4fad-afa5-7e07b782fd0a
TID: [-1234] [] [2024-10-30 08:12:26,224]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 08:12:26,224]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46234, SOCKET_TIMEOUT = 180000, CORRELATION_ID = c6afdbcb-ad30-4fad-afa5-7e07b782fd0a
TID: [-1234] [] [2024-10-30 08:12:26,224]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:34874, CORRELATION_ID = c6afdbcb-ad30-4fad-afa5-7e07b782fd0a, CONNECTION = http-incoming-861490
TID: [-1234] [] [2024-10-30 08:12:26,225]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 08:12:26,236]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-861490, CORRELATION_ID = c6afdbcb-ad30-4fad-afa5-7e07b782fd0a
TID: [-1234] [] [2024-10-30 08:12:27,221]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 08:12:27,222]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:34996, CORRELATION_ID = 1dda42a8-25f3-4917-9138-d76b25dd0f76, CONNECTION = http-incoming-861503
TID: [-1234] [] [2024-10-30 08:12:27,227]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46241, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 1dda42a8-25f3-4917-9138-d76b25dd0f76
TID: [-1234] [] [2024-10-30 08:12:27,228]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 08:12:27,239]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-861503, CORRELATION_ID = 1dda42a8-25f3-4917-9138-d76b25dd0f76
TID: [-1234] [] [2024-10-30 08:12:34,235]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 08:12:34,236]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:56146, CORRELATION_ID = 47070fab-0ab1-4f05-8cf4-56d931652076, CONNECTION = http-incoming-861530
TID: [-1234] [] [2024-10-30 08:12:34,236]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 47070fab-0ab1-4f05-8cf4-56d931652076
TID: [-1234] [] [2024-10-30 08:12:34,237]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46235, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 47070fab-0ab1-4f05-8cf4-56d931652076
TID: [-1234] [] [2024-10-30 08:12:34,238]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 08:12:34,251]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-861530, CORRELATION_ID = 47070fab-0ab1-4f05-8cf4-56d931652076
TID: [-1234] [] [2024-10-30 08:12:49,254]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 08:12:49,255]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:37498, CORRELATION_ID = 20e4e763-5e34-4a84-a0f5-8513b39e0ac2, CONNECTION = http-incoming-861563
TID: [-1234] [] [2024-10-30 08:12:49,256]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 20e4e763-5e34-4a84-a0f5-8513b39e0ac2
TID: [-1234] [] [2024-10-30 08:12:49,257]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46230, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 20e4e763-5e34-4a84-a0f5-8513b39e0ac2
TID: [-1234] [] [2024-10-30 08:12:49,257]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 08:12:49,270]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-861563, CORRELATION_ID = 20e4e763-5e34-4a84-a0f5-8513b39e0ac2
TID: [-1234] [] [2024-10-30 08:15:26,635]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = f7208ba8-520a-4c93-876f-8f598d00abc2
TID: [-1234] [] [2024-10-30 08:15:26,636]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46237, SOCKET_TIMEOUT = 180000, CORRELATION_ID = f7208ba8-520a-4c93-876f-8f598d00abc2
TID: [-1234] [] [2024-10-30 08:15:26,637]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 08:15:26,647]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 08:15:26,648]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:55870, CORRELATION_ID = f7208ba8-520a-4c93-876f-8f598d00abc2, CONNECTION = http-incoming-861577
TID: [-1234] [] [2024-10-30 08:15:26,650]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-861577, CORRELATION_ID = f7208ba8-520a-4c93-876f-8f598d00abc2
TID: [-1234] [] [2024-10-30 08:24:26,529]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 02e03031-ff18-4de6-a992-0df7d243c783
TID: [-1234] [] [2024-10-30 08:24:26,544]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5173254e-218d-4993-a9dc-8cd5109fc4cd
TID: [-1234] [] [2024-10-30 08:28:45,537]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241030&denNgay=20241030&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241030&denNgay=20241030&maTthc=
TID: [-1234] [] [2024-10-30 08:28:45,579]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-10-30 08:28:52,317]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241030&denNgay=20241030&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241030&denNgay=20241030&maTthc=
TID: [-1234] [] [2024-10-30 08:28:52,377]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-10-30 08:29:08,337]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241030&denNgay=20241030&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241030&denNgay=20241030&maTthc=
TID: [-1234] [] [2024-10-30 08:29:08,382]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-10-30 08:31:54,491]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-10-30 09:01:54,669]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-10-30 09:05:27,948]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46283, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 4e4965e9-0b3d-43e6-97d5-d40fc65319d7
TID: [-1234] [] [2024-10-30 09:05:27,950]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 09:05:33,918]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 09:05:33,919]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:34812, CORRELATION_ID = 486dd05a-55b2-454c-8a8a-6513fd6d495f, CONNECTION = http-incoming-862211
TID: [-1234] [] [2024-10-30 09:05:33,934]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46279, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 486dd05a-55b2-454c-8a8a-6513fd6d495f
TID: [-1234] [] [2024-10-30 09:05:33,935]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 09:05:33,947]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-862211, CORRELATION_ID = 486dd05a-55b2-454c-8a8a-6513fd6d495f
TID: [-1234] [] [2024-10-30 09:08:42,601]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46266, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 26fa523c-53ec-429f-bd95-3b7e2a43f560
TID: [-1234] [] [2024-10-30 09:08:42,602]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 09:08:46,013]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 5233ff32-9cc9-467b-bddd-d74919bae301
TID: [-1234] [] [2024-10-30 09:08:46,014]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46280, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 5233ff32-9cc9-467b-bddd-d74919bae301
TID: [-1234] [] [2024-10-30 09:08:46,015]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 09:08:49,827]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46286, SOCKET_TIMEOUT = 180000, CORRELATION_ID = f7781053-452e-4e43-869f-73a0494a57c0
TID: [-1234] [] [2024-10-30 09:08:49,828]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 09:11:54,599]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 7ae2a579-5ce6-460a-b7f1-65adb5f88eb1
TID: [-1234] [] [2024-10-30 09:11:54,600]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46288, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 7ae2a579-5ce6-460a-b7f1-65adb5f88eb1
TID: [-1234] [] [2024-10-30 09:11:54,601]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 09:11:54,612]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = b10600a6-7a7a-427a-838e-7a533f8ee947
TID: [-1234] [] [2024-10-30 09:11:54,613]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46293, SOCKET_TIMEOUT = 180000, CORRELATION_ID = b10600a6-7a7a-427a-838e-7a533f8ee947
TID: [-1234] [] [2024-10-30 09:11:54,613]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 09:11:54,613]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 09:11:54,614]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 09:11:54,614]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:57678, CORRELATION_ID = b10600a6-7a7a-427a-838e-7a533f8ee947, CONNECTION = http-incoming-862306
TID: [-1234] [] [2024-10-30 09:11:54,614]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:57706, CORRELATION_ID = 7ae2a579-5ce6-460a-b7f1-65adb5f88eb1, CONNECTION = http-incoming-862308
TID: [-1234] [] [2024-10-30 09:11:54,624]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-862306, CORRELATION_ID = b10600a6-7a7a-427a-838e-7a533f8ee947
TID: [-1234] [] [2024-10-30 09:11:59,616]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 09:11:59,617]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:57918, CORRELATION_ID = 2dd57ab8-5c87-4a5e-ac20-7784560860df, CONNECTION = http-incoming-862327
TID: [-1234] [] [2024-10-30 09:11:59,713]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 2dd57ab8-5c87-4a5e-ac20-7784560860df
TID: [-1234] [] [2024-10-30 09:11:59,713]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46269, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 2dd57ab8-5c87-4a5e-ac20-7784560860df
TID: [-1234] [] [2024-10-30 09:11:59,714]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 09:11:59,727]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-862327, CORRELATION_ID = 2dd57ab8-5c87-4a5e-ac20-7784560860df
TID: [-1234] [] [2024-10-30 09:12:00,617]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 09:12:00,618]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:37324, CORRELATION_ID = 6dc7fa21-27ec-489e-99e7-5df9b88bcd43, CONNECTION = http-incoming-862334
TID: [-1234] [] [2024-10-30 09:12:00,756]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 6dc7fa21-27ec-489e-99e7-5df9b88bcd43
TID: [-1234] [] [2024-10-30 09:12:00,757]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46295, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 6dc7fa21-27ec-489e-99e7-5df9b88bcd43
TID: [-1234] [] [2024-10-30 09:12:00,757]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 09:12:00,768]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-862334, CORRELATION_ID = 6dc7fa21-27ec-489e-99e7-5df9b88bcd43
TID: [-1234] [] [2024-10-30 09:12:14,596]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 55fe7a56-d062-43a6-b642-1ef038bbf431
TID: [-1234] [] [2024-10-30 09:12:15,635]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 19025e5a-79dd-46b5-9cfc-6a5a1d710712
TID: [-1234] [] [2024-10-30 09:12:15,635]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 31235136-859e-4e5c-8340-2034bd7454dc
TID: [-1234] [] [2024-10-30 09:12:16,636]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 97d542d6-ab35-4b3a-bf81-76756c15f092
TID: [-1234] [] [2024-10-30 09:12:31,749]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6d28a4e3-df07-4ce8-935f-b5688a6b37bc
TID: [-1234] [] [2024-10-30 09:15:01,046]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 3967387e-ff73-4c77-840e-70bb54249a62
TID: [-1234] [] [2024-10-30 09:15:01,046]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 09:15:01,047]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46291, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 3967387e-ff73-4c77-840e-70bb54249a62
TID: [-1234] [] [2024-10-30 09:15:01,047]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:60946, CORRELATION_ID = 3967387e-ff73-4c77-840e-70bb54249a62, CONNECTION = http-incoming-862358
TID: [-1234] [] [2024-10-30 09:15:01,048]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 09:15:01,062]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-862358, CORRELATION_ID = 3967387e-ff73-4c77-840e-70bb54249a62
TID: [-1234] [] [2024-10-30 09:19:19,396]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9d29eb44-087e-4e21-a033-c9d294187d87
TID: [-1234] [] [2024-10-30 09:19:23,208]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = af4da935-0042-4df0-9e6e-c06dc6525ce1
TID: [-1234] [] [2024-10-30 09:20:29,010]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7d03d24e-c012-4a3c-be46-c9de579ba97f
TID: [-1234] [] [2024-10-30 09:20:32,011]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1da038cf-a450-43a7-a71a-8fa41621d050
TID: [-1234] [] [2024-10-30 09:20:32,335]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1999254b-74d5-493b-8a52-11c2c54c84c1
TID: [-1234] [] [2024-10-30 09:32:00,004]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-10-30 09:37:42,819]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 76cbe06b-2705-4786-aaa5-1888b31cf6e0
TID: [-1234] [] [2024-10-30 09:37:44,467]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6eabaf15-da01-4df9-8767-f35c41e505da
TID: [-1234] [] [2024-10-30 09:42:04,326]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = da1accae-1d3f-45ce-8a53-4fe47eeea871
TID: [-1234] [] [2024-10-30 10:02:02,733]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-10-30 10:06:12,647]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46348, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 6a4d381a-c8d7-4dd6-9c56-5c64f53e894f
TID: [-1234] [] [2024-10-30 10:06:12,647]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 10:06:12,648]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:45368, CORRELATION_ID = 6a4d381a-c8d7-4dd6-9c56-5c64f53e894f, CONNECTION = http-incoming-862933
TID: [-1234] [] [2024-10-30 10:06:12,648]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 10:06:12,662]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-862933, CORRELATION_ID = 6a4d381a-c8d7-4dd6-9c56-5c64f53e894f
TID: [-1234] [] [2024-10-30 10:06:17,355]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 10:06:17,356]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:45480, CORRELATION_ID = f7824cb7-99c8-495b-9167-15ed6bec4b2f, CONNECTION = http-incoming-862947
TID: [-1234] [] [2024-10-30 10:06:17,362]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = f7824cb7-99c8-495b-9167-15ed6bec4b2f
TID: [-1234] [] [2024-10-30 10:06:17,363]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46344, SOCKET_TIMEOUT = 180000, CORRELATION_ID = f7824cb7-99c8-495b-9167-15ed6bec4b2f
TID: [-1234] [] [2024-10-30 10:06:17,363]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 10:06:17,375]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-862947, CORRELATION_ID = f7824cb7-99c8-495b-9167-15ed6bec4b2f
TID: [-1234] [] [2024-10-30 10:09:23,114]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46333, SOCKET_TIMEOUT = 180000, CORRELATION_ID = b6adbdbb-ed7d-44c3-8420-1719f7b0cf3c
TID: [-1234] [] [2024-10-30 10:09:23,115]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 10:09:24,782]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 10:09:24,783]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:45970, CORRELATION_ID = c6ba0317-f94b-4994-849d-eb86d1c33f16, CONNECTION = http-incoming-862964
TID: [-1234] [] [2024-10-30 10:09:24,971]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = c6ba0317-f94b-4994-849d-eb86d1c33f16
TID: [-1234] [] [2024-10-30 10:09:24,972]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46353, SOCKET_TIMEOUT = 180000, CORRELATION_ID = c6ba0317-f94b-4994-849d-eb86d1c33f16
TID: [-1234] [] [2024-10-30 10:09:24,973]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 10:09:24,985]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-862964, CORRELATION_ID = c6ba0317-f94b-4994-849d-eb86d1c33f16
TID: [-1234] [] [2024-10-30 10:09:29,249]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 10:09:29,249]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:46126, CORRELATION_ID = 201f5a0b-ea4c-4795-ad42-2bc5c295ce00, CONNECTION = http-incoming-862979
TID: [-1234] [] [2024-10-30 10:09:29,252]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 201f5a0b-ea4c-4795-ad42-2bc5c295ce00
TID: [-1234] [] [2024-10-30 10:09:29,253]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46355, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 201f5a0b-ea4c-4795-ad42-2bc5c295ce00
TID: [-1234] [] [2024-10-30 10:09:29,254]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 10:09:29,271]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-862979, CORRELATION_ID = 201f5a0b-ea4c-4795-ad42-2bc5c295ce00
TID: [-1234] [] [2024-10-30 10:12:32,207]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 10:12:32,208]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:50328, CORRELATION_ID = d3451bb7-d633-4d14-8df3-f4777402a815, CONNECTION = http-incoming-863036
TID: [-1234] [] [2024-10-30 10:12:32,355]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46351, SOCKET_TIMEOUT = 180000, CORRELATION_ID = d3451bb7-d633-4d14-8df3-f4777402a815
TID: [-1234] [] [2024-10-30 10:12:32,356]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 10:12:32,370]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-863036, CORRELATION_ID = d3451bb7-d633-4d14-8df3-f4777402a815
TID: [-1234] [] [2024-10-30 10:12:33,135]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 10:12:33,136]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:50464, CORRELATION_ID = fd9b721b-dbf3-432c-953d-7bb200e9ff24, CONNECTION = http-incoming-863047
TID: [-1234] [] [2024-10-30 10:12:33,213]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46358, SOCKET_TIMEOUT = 180000, CORRELATION_ID = fd9b721b-dbf3-432c-953d-7bb200e9ff24
TID: [-1234] [] [2024-10-30 10:12:33,214]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 10:12:33,226]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-863047, CORRELATION_ID = fd9b721b-dbf3-432c-953d-7bb200e9ff24
TID: [-1234] [] [2024-10-30 10:12:33,401]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 10:12:33,402]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:50468, CORRELATION_ID = 6b3b0c85-7c0c-42f0-84ba-f666d8fb6b93, CONNECTION = http-incoming-863048
TID: [-1234] [] [2024-10-30 10:12:33,404]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46341, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 6b3b0c85-7c0c-42f0-84ba-f666d8fb6b93
TID: [-1234] [] [2024-10-30 10:12:33,405]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 10:12:33,418]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-863048, CORRELATION_ID = 6b3b0c85-7c0c-42f0-84ba-f666d8fb6b93
TID: [-1234] [] [2024-10-30 10:12:38,468]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 37ecc9a0-c9ea-4de3-9de5-79e90d7d446c
TID: [-1234] [] [2024-10-30 10:12:38,469]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46354, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 37ecc9a0-c9ea-4de3-9de5-79e90d7d446c
TID: [-1234] [] [2024-10-30 10:12:38,469]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 10:13:01,174]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b47865c0-9e1c-4157-b52a-fe753d8ed788
TID: [-1234] [] [2024-10-30 10:13:47,298]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 86629396-3618-498d-af45-d522f0131d0d
TID: [-1234] [] [2024-10-30 10:13:49,223]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bf64fa4c-a572-4a40-9711-02c589d1c869
TID: [-1234] [] [2024-10-30 10:15:31,097]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d086fc30-0781-4d4c-bf32-0f0c50fcc9a7
TID: [-1234] [] [2024-10-30 10:15:38,986]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = eb5e3141-9ee5-4aeb-8aa2-5c7b859a85ba
TID: [-1234] [] [2024-10-30 10:15:38,986]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = e6fc0a69-aeac-4942-8a3e-cae640f5ffb3
TID: [-1234] [] [2024-10-30 10:15:38,987]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46365, SOCKET_TIMEOUT = 180000, CORRELATION_ID = e6fc0a69-aeac-4942-8a3e-cae640f5ffb3
TID: [-1234] [] [2024-10-30 10:15:38,987]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 10:15:38,988]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:47958, CORRELATION_ID = e6fc0a69-aeac-4942-8a3e-cae640f5ffb3, CONNECTION = http-incoming-863114
TID: [-1234] [] [2024-10-30 10:15:38,988]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 10:15:39,000]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-863114, CORRELATION_ID = e6fc0a69-aeac-4942-8a3e-cae640f5ffb3
TID: [-1234] [] [2024-10-30 10:25:08,392]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241030&denNgay=20241030&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241030&denNgay=20241030&maTthc=
TID: [-1234] [] [2024-10-30 10:25:08,490]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-10-30 10:32:03,423]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-10-30 10:41:50,677]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6b27539e-76c0-47b4-9393-f38477bb2f88
TID: [-1234] [] [2024-10-30 10:46:37,365]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241030&denNgay=20241030&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241030&denNgay=20241030&maTthc=
TID: [-1234] [] [2024-10-30 10:46:37,415]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-10-30 10:49:43,414]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5f994533-f6aa-44c5-807d-4c00c60f3c7f
TID: [-1234] [] [2024-10-30 10:49:51,781]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f389f057-6a92-4bca-bb87-4102e76018b6
TID: [-1234] [] [2024-10-30 11:02:03,514]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-10-30 11:05:37,731]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 11:05:37,732]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:34254, CORRELATION_ID = eef1b1f6-6a11-4deb-a71d-c95f45b9df2b, CONNECTION = http-incoming-863765
TID: [-1234] [] [2024-10-30 11:05:37,809]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46394, SOCKET_TIMEOUT = 180000, CORRELATION_ID = eef1b1f6-6a11-4deb-a71d-c95f45b9df2b
TID: [-1234] [] [2024-10-30 11:05:37,810]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 11:05:37,824]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-863765, CORRELATION_ID = eef1b1f6-6a11-4deb-a71d-c95f45b9df2b
TID: [-1234] [] [2024-10-30 11:05:44,187]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46399, SOCKET_TIMEOUT = 180000, CORRELATION_ID = afa7976b-acea-447f-a1ec-bde644f72887
TID: [-1234] [] [2024-10-30 11:05:44,188]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 11:08:50,286]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46402, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 8ac7270a-37d9-4e26-8b6d-8f8eb5c835f7
TID: [-1234] [] [2024-10-30 11:08:50,288]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 11:08:51,082]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 11:08:51,083]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:43876, CORRELATION_ID = 4c869ad9-26ef-4ee6-9083-f43f476571f1, CONNECTION = http-incoming-863830
TID: [-1234] [] [2024-10-30 11:08:51,239]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46396, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 4c869ad9-26ef-4ee6-9083-f43f476571f1
TID: [-1234] [] [2024-10-30 11:08:51,240]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 11:08:51,251]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-863830, CORRELATION_ID = 4c869ad9-26ef-4ee6-9083-f43f476571f1
TID: [-1234] [] [2024-10-30 11:08:55,940]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46404, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 8ba68b30-f142-42bd-9fb9-de7555642982
TID: [-1234] [] [2024-10-30 11:08:55,941]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 11:11:58,732]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = e95ac13e-301f-4304-9e98-d3da5d7a5d04
TID: [-1234] [] [2024-10-30 11:11:58,733]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46409, SOCKET_TIMEOUT = 180000, CORRELATION_ID = e95ac13e-301f-4304-9e98-d3da5d7a5d04
TID: [-1234] [] [2024-10-30 11:11:58,734]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 11:11:59,547]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 598eef41-ab6b-49b0-8a59-a5cfa8de5efa
TID: [-1234] [] [2024-10-30 11:11:59,548]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46395, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 598eef41-ab6b-49b0-8a59-a5cfa8de5efa
TID: [-1234] [] [2024-10-30 11:11:59,549]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 11:12:00,546]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46408, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 191cf59b-835d-4b22-9d17-a5f750233f4c
TID: [-1234] [] [2024-10-30 11:12:00,547]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 11:13:43,789]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2e438772-fc6b-4f22-8a91-75f0f38436c4
TID: [-1234] [] [2024-10-30 11:15:01,007]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 11:15:01,009]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:37204, CORRELATION_ID = ac483971-c831-4e3d-be92-ae8dcb04b7ae, CONNECTION = http-incoming-863943
TID: [-1234] [] [2024-10-30 11:15:01,040]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = ac483971-c831-4e3d-be92-ae8dcb04b7ae
TID: [-1234] [] [2024-10-30 11:15:01,041]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46405, SOCKET_TIMEOUT = 180000, CORRELATION_ID = ac483971-c831-4e3d-be92-ae8dcb04b7ae
TID: [-1234] [] [2024-10-30 11:15:01,042]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 11:15:01,056]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-863943, CORRELATION_ID = ac483971-c831-4e3d-be92-ae8dcb04b7ae
TID: [-1234] [] [2024-10-30 11:19:08,764]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d10cab48-b0c6-4601-ad95-8fa1b6644c15
TID: [-1234] [] [2024-10-30 11:29:39,150]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241030&denNgay=20241030&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241030&denNgay=20241030&maTthc=
TID: [-1234] [] [2024-10-30 11:29:39,190]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-10-30 11:29:39,919]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241030&denNgay=20241030&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241030&denNgay=20241030&maTthc=
TID: [-1234] [] [2024-10-30 11:29:39,960]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-10-30 11:29:40,773]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241030&denNgay=20241030&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241030&denNgay=20241030&maTthc=
TID: [-1234] [] [2024-10-30 11:29:40,812]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-10-30 11:29:41,620]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241030&denNgay=20241030&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241030&denNgay=20241030&maTthc=
TID: [-1234] [] [2024-10-30 11:29:41,660]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-10-30 11:29:42,392]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241030&denNgay=20241030&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241030&denNgay=20241030&maTthc=
TID: [-1234] [] [2024-10-30 11:29:42,434]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2024-10-30 11:32:03,680]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-10-30 12:02:04,176]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-10-30 12:05:58,236]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46437, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 73de039d-7968-4dc3-ad7a-ff02398b4ecd
TID: [-1234] [] [2024-10-30 12:05:58,238]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 12:06:02,377]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 12:06:02,377]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46442, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 57f730a9-30c3-40cf-9ab1-344409e0b99b
TID: [-1234] [] [2024-10-30 12:06:02,378]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:35856, CORRELATION_ID = 57f730a9-30c3-40cf-9ab1-344409e0b99b, CONNECTION = http-incoming-864502
TID: [-1234] [] [2024-10-30 12:06:02,378]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 12:06:02,392]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-864502, CORRELATION_ID = 57f730a9-30c3-40cf-9ab1-344409e0b99b
TID: [-1234] [] [2024-10-30 12:09:09,246]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = e5bb22d7-b8aa-44ff-9416-3ce53c00ec23
TID: [-1234] [] [2024-10-30 12:09:09,247]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46448, SOCKET_TIMEOUT = 180000, CORRELATION_ID = e5bb22d7-b8aa-44ff-9416-3ce53c00ec23
TID: [-1234] [] [2024-10-30 12:09:09,248]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 12:09:16,706]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46451, SOCKET_TIMEOUT = 180000, CORRELATION_ID = b23b339e-d3b1-460e-b96f-991636122963
TID: [-1234] [] [2024-10-30 12:09:16,707]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 12:09:20,691]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 26f7665c-cf9e-46d5-bd5d-f7e167c182d9
TID: [-1234] [] [2024-10-30 12:09:20,692]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46455, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 26f7665c-cf9e-46d5-bd5d-f7e167c182d9
TID: [-1234] [] [2024-10-30 12:09:20,693]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 12:12:24,065]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 12:12:24,066]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:57780, CORRELATION_ID = 7244ccde-67cb-4d1a-a4e8-42f8d86b4716, CONNECTION = http-incoming-864589
TID: [-1234] [] [2024-10-30 12:12:24,128]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 28793051-fc51-4b2c-8180-ad8063236d3b
TID: [-1234] [] [2024-10-30 12:12:24,129]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46459, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 28793051-fc51-4b2c-8180-ad8063236d3b
TID: [-1234] [] [2024-10-30 12:12:24,130]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 12:12:24,130]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 12:12:24,131]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:57756, CORRELATION_ID = 28793051-fc51-4b2c-8180-ad8063236d3b, CONNECTION = http-incoming-864588
TID: [-1234] [] [2024-10-30 12:12:24,142]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-864588, CORRELATION_ID = 28793051-fc51-4b2c-8180-ad8063236d3b
TID: [-1234] [] [2024-10-30 12:12:24,185]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46436, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 7244ccde-67cb-4d1a-a4e8-42f8d86b4716
TID: [-1234] [] [2024-10-30 12:12:24,186]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 12:12:24,197]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-864589, CORRELATION_ID = 7244ccde-67cb-4d1a-a4e8-42f8d86b4716
TID: [-1234] [] [2024-10-30 12:12:26,185]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 12:12:26,185]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:57854, CORRELATION_ID = f8c46716-0576-4cd4-8252-a86becb7507f, CONNECTION = http-incoming-864598
TID: [-1234] [] [2024-10-30 12:12:26,307]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = f8c46716-0576-4cd4-8252-a86becb7507f
TID: [-1234] [] [2024-10-30 12:12:26,307]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46460, SOCKET_TIMEOUT = 180000, CORRELATION_ID = f8c46716-0576-4cd4-8252-a86becb7507f
TID: [-1234] [] [2024-10-30 12:12:26,308]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 12:12:26,319]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-864598, CORRELATION_ID = f8c46716-0576-4cd4-8252-a86becb7507f
TID: [-1234] [] [2024-10-30 12:12:31,137]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46461, SOCKET_TIMEOUT = 180000, CORRELATION_ID = c513f80e-a42e-41fe-9957-6dc2070b58c3
TID: [-1234] [] [2024-10-30 12:12:31,138]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 12:12:31,189]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46450, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 080db2b4-633a-4423-8a78-eb30d7219b97
TID: [-1234] [] [2024-10-30 12:12:31,190]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 12:12:31,190]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:54928, CORRELATION_ID = 080db2b4-633a-4423-8a78-eb30d7219b97, CONNECTION = http-incoming-864616
TID: [-1234] [] [2024-10-30 12:12:31,191]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 12:12:31,203]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-864616, CORRELATION_ID = 080db2b4-633a-4423-8a78-eb30d7219b97
TID: [-1234] [] [2024-10-30 12:15:34,738]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b01e8b3a-93d0-41c3-8456-74c7394ae7c7
TID: [-1234] [] [2024-10-30 12:15:35,695]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ca258008-c1e1-4cbe-8582-2105d5185fa9
TID: [-1234] [] [2024-10-30 12:18:12,192]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ee374ca1-9566-4254-af6a-f4f75ae8db77
TID: [-1234] [] [2024-10-30 12:22:17,859]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241030&denNgay=20241030&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241030&denNgay=20241030&maTthc=
TID: [-1234] [] [2024-10-30 12:22:17,898]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-10-30 12:32:04,345]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-10-30 12:43:33,398] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2024-10-30 13:02:04,451]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-10-30 13:05:36,233]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46486, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 1866f0f3-dd84-4ba4-b5cb-ab02a2a6c6d7
TID: [-1234] [] [2024-10-30 13:05:36,234]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 13:05:37,301]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 04435a05-9ad7-4a02-afa3-f9e591c20dce
TID: [-1234] [] [2024-10-30 13:05:37,302]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46488, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 04435a05-9ad7-4a02-afa3-f9e591c20dce
TID: [-1234] [] [2024-10-30 13:05:37,303]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 13:08:40,925]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46483, SOCKET_TIMEOUT = 180000, CORRELATION_ID = e32aef7e-480e-4e5e-9934-a6d25ceaebf1
TID: [-1234] [] [2024-10-30 13:08:40,926]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 13:08:41,697]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46484, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 3256aa32-d9ba-4c6f-a554-141e093c8893
TID: [-1234] [] [2024-10-30 13:08:41,698]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 13:08:50,264]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 13:08:50,265]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:49792, CORRELATION_ID = adc828e6-a2d7-4bce-a233-fc7d178da2e0, CONNECTION = http-incoming-865171
TID: [-1234] [] [2024-10-30 13:08:50,398]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46482, SOCKET_TIMEOUT = 180000, CORRELATION_ID = adc828e6-a2d7-4bce-a233-fc7d178da2e0
TID: [-1234] [] [2024-10-30 13:08:50,399]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 13:08:50,413]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-865171, CORRELATION_ID = adc828e6-a2d7-4bce-a233-fc7d178da2e0
TID: [-1234] [] [2024-10-30 13:11:53,527]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = ac2369d1-8a7c-4788-8a6d-ab2b9a9ffd47
TID: [-1234] [] [2024-10-30 13:11:53,527]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = cc5254d1-7aa1-4b8c-88b6-5de6369d4f0b
TID: [-1234] [] [2024-10-30 13:11:53,528]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46504, SOCKET_TIMEOUT = 180000, CORRELATION_ID = ac2369d1-8a7c-4788-8a6d-ab2b9a9ffd47
TID: [-1234] [] [2024-10-30 13:11:53,528]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46503, SOCKET_TIMEOUT = 180000, CORRELATION_ID = cc5254d1-7aa1-4b8c-88b6-5de6369d4f0b
TID: [-1234] [] [2024-10-30 13:11:53,529]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 13:11:53,529]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 13:11:53,847]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 13:11:53,847]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:41810, CORRELATION_ID = ccdf0c65-4fdc-496f-bcd2-72b2e561aa00, CONNECTION = http-incoming-865205
TID: [-1234] [] [2024-10-30 13:11:53,849]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = ccdf0c65-4fdc-496f-bcd2-72b2e561aa00
TID: [-1234] [] [2024-10-30 13:11:53,850]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46485, SOCKET_TIMEOUT = 180000, CORRELATION_ID = ccdf0c65-4fdc-496f-bcd2-72b2e561aa00
TID: [-1234] [] [2024-10-30 13:11:53,851]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 13:11:53,862]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-865205, CORRELATION_ID = ccdf0c65-4fdc-496f-bcd2-72b2e561aa00
TID: [-1234] [] [2024-10-30 13:11:55,772]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 5976023f-10e4-4bf3-bf7b-82bd91530264
TID: [-1234] [] [2024-10-30 13:11:55,773]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46496, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 5976023f-10e4-4bf3-bf7b-82bd91530264
TID: [-1234] [] [2024-10-30 13:11:55,774]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 13:14:39,893]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c57d7d7b-08a1-425c-84b3-425a0832778d
TID: [-1234] [] [2024-10-30 13:14:43,896]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a55eb856-ba0e-4f48-a83e-9778972c917e
TID: [-1234] [] [2024-10-30 13:14:54,520]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 60cef313-cd94-4bb7-8976-b95dc2debedf
TID: [-1234] [] [2024-10-30 13:14:55,416]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8261d20d-99a5-46e2-bcb3-832acdf169a6
TID: [-1234] [] [2024-10-30 13:14:56,202]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 13:14:56,203]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:58388, CORRELATION_ID = d2df6cb4-212c-4628-a677-aa4005e0a23d, CONNECTION = http-incoming-865251
TID: [-1234] [] [2024-10-30 13:14:56,203]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = d2df6cb4-212c-4628-a677-aa4005e0a23d
TID: [-1234] [] [2024-10-30 13:14:56,203]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46500, SOCKET_TIMEOUT = 180000, CORRELATION_ID = d2df6cb4-212c-4628-a677-aa4005e0a23d
TID: [-1234] [] [2024-10-30 13:14:56,204]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 13:14:56,218]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-865251, CORRELATION_ID = d2df6cb4-212c-4628-a677-aa4005e0a23d
TID: [-1234] [] [2024-10-30 13:17:58,028]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d9bbe479-f7e6-4e54-ab68-85e4d1ee5827
TID: [-1234] [] [2024-10-30 13:18:00,344]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2fceef1f-3961-4a42-a2ab-fe952073c4e9
TID: [-1234] [] [2024-10-30 13:18:01,553]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 51d71271-e36c-464f-adbb-102fdaa1c482
TID: [-1234] [] [2024-10-30 13:32:04,967]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-10-30 14:02:07,746]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-10-30 14:06:32,002]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 07c21dc8-b8c0-4608-8cfe-ad9a30aeb225
TID: [-1234] [] [2024-10-30 14:06:32,003]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46524, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 07c21dc8-b8c0-4608-8cfe-ad9a30aeb225
TID: [-1234] [] [2024-10-30 14:06:32,004]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 14:06:36,094]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 224f564a-30b1-4b8a-a623-6171c4af26ce
TID: [-1234] [] [2024-10-30 14:06:36,095]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46526, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 224f564a-30b1-4b8a-a623-6171c4af26ce
TID: [-1234] [] [2024-10-30 14:06:36,096]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 14:06:36,102]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 14:06:36,103]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:33194, CORRELATION_ID = 224f564a-30b1-4b8a-a623-6171c4af26ce, CONNECTION = http-incoming-865714
TID: [-1234] [] [2024-10-30 14:06:36,107]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-865714, CORRELATION_ID = 224f564a-30b1-4b8a-a623-6171c4af26ce
TID: [-1234] [] [2024-10-30 14:09:41,928]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46527, SOCKET_TIMEOUT = 180000, CORRELATION_ID = d7d79639-cc94-434e-aeaa-cb075c96b46b
TID: [-1234] [] [2024-10-30 14:09:41,930]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 14:09:42,953]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 19faacad-9909-4f11-bc82-208712db61cb
TID: [-1234] [] [2024-10-30 14:09:42,954]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46539, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 19faacad-9909-4f11-bc82-208712db61cb
TID: [-1234] [] [2024-10-30 14:09:42,955]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 14:09:48,087]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46540, SOCKET_TIMEOUT = 180000, CORRELATION_ID = e7bc2875-09b5-48fe-95c0-8e65ba5e4cdc
TID: [-1234] [] [2024-10-30 14:09:48,088]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 14:12:51,001]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 14:12:51,002]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:59484, CORRELATION_ID = d42eceea-a9f0-43c7-9128-ec6422b1de1e, CONNECTION = http-incoming-865821
TID: [-1234] [] [2024-10-30 14:12:51,004]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 14:12:51,005]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:59444, CORRELATION_ID = ef71a780-ab6f-4e58-8d56-78bc48f20ab3, CONNECTION = http-incoming-865817
TID: [-1234] [] [2024-10-30 14:12:51,056]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = ef71a780-ab6f-4e58-8d56-78bc48f20ab3
TID: [-1234] [] [2024-10-30 14:12:51,056]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46544, SOCKET_TIMEOUT = 180000, CORRELATION_ID = ef71a780-ab6f-4e58-8d56-78bc48f20ab3
TID: [-1234] [] [2024-10-30 14:12:51,057]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 14:12:51,071]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-865817, CORRELATION_ID = ef71a780-ab6f-4e58-8d56-78bc48f20ab3
TID: [-1234] [] [2024-10-30 14:12:51,133]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = d42eceea-a9f0-43c7-9128-ec6422b1de1e
TID: [-1234] [] [2024-10-30 14:12:51,133]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46548, SOCKET_TIMEOUT = 180000, CORRELATION_ID = d42eceea-a9f0-43c7-9128-ec6422b1de1e
TID: [-1234] [] [2024-10-30 14:12:51,134]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 14:12:51,145]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-865821, CORRELATION_ID = d42eceea-a9f0-43c7-9128-ec6422b1de1e
TID: [-1234] [] [2024-10-30 14:12:52,229]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46545, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 549915ba-f7cf-41a5-acdf-cc1622996734
TID: [-1234] [] [2024-10-30 14:12:52,230]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 14:13:08,020]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 13e6c1b1-ca83-4030-8e11-e594330a8c64
TID: [-1234] [] [2024-10-30 14:13:08,305]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c1ceeea6-b5cb-4542-8d42-7142c8837e70
TID: [-1234] [] [2024-10-30 14:13:12,028]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 18659071-f838-43f6-85ae-e8a3a789c5f0
TID: [-1234] [] [2024-10-30 14:15:52,638]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 14:15:52,640]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:48234, CORRELATION_ID = a3c0f119-dad5-4e6f-80f0-88f9bd256c42, CONNECTION = http-incoming-865855
TID: [-1234] [] [2024-10-30 14:15:52,662]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46528, SOCKET_TIMEOUT = 180000, CORRELATION_ID = a3c0f119-dad5-4e6f-80f0-88f9bd256c42
TID: [-1234] [] [2024-10-30 14:15:52,663]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 14:15:52,677]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-865855, CORRELATION_ID = a3c0f119-dad5-4e6f-80f0-88f9bd256c42
TID: [-1234] [] [2024-10-30 14:15:52,974]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d12b1d60-8b9a-4793-ba59-0807b514638c
TID: [-1234] [] [2024-10-30 14:18:56,332]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3ecc52d2-e86a-46fb-88de-fcddb08bee96
TID: [-1234] [] [2024-10-30 14:18:58,051]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 42a19975-728d-4e1a-b45b-9a2ee3485eb7
TID: [-1234] [] [2024-10-30 14:18:58,092]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 52ba7fb8-a9a6-428d-9815-65b60f91b935
TID: [-1234] [] [2024-10-30 14:22:21,086]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 89b1731b-5de7-4ad1-b3fe-bac29070d4b0
TID: [-1234] [] [2024-10-30 14:32:10,054]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-10-30 15:02:11,227]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-10-30 15:06:36,027]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46583, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 8b905e73-9bfe-4cbe-aac8-69bdb9d65608
TID: [-1234] [] [2024-10-30 15:06:36,028]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 15:06:36,028]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 15:06:36,029]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:57580, CORRELATION_ID = 8b905e73-9bfe-4cbe-aac8-69bdb9d65608, CONNECTION = http-incoming-866388
TID: [-1234] [] [2024-10-30 15:06:36,044]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-866388, CORRELATION_ID = 8b905e73-9bfe-4cbe-aac8-69bdb9d65608
TID: [-1234] [] [2024-10-30 15:06:42,950]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = b83e5bba-5d2d-47f8-9e44-a032a77d04db
TID: [-1234] [] [2024-10-30 15:06:42,951]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46577, SOCKET_TIMEOUT = 180000, CORRELATION_ID = b83e5bba-5d2d-47f8-9e44-a032a77d04db
TID: [-1234] [] [2024-10-30 15:06:42,952]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 15:09:48,049]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46585, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 27f34d82-09a2-4b7c-bd40-14cdf99cfc07
TID: [-1234] [] [2024-10-30 15:09:48,050]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 15:09:48,102]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46576, SOCKET_TIMEOUT = 180000, CORRELATION_ID = c87dbcbe-2c6c-4510-bbb0-59298de4ed65
TID: [-1234] [] [2024-10-30 15:09:48,103]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 15:10:01,083]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 91031d61-b2b0-448c-ad8c-73f417c0c0c0
TID: [-1234] [] [2024-10-30 15:10:01,085]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46594, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 91031d61-b2b0-448c-ad8c-73f417c0c0c0
TID: [-1234] [] [2024-10-30 15:10:01,085]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 15:13:06,907]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46596, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 4121ea66-96aa-4863-8cfa-05ba845afcab
TID: [-1234] [] [2024-10-30 15:13:06,908]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 15:13:06,913]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46600, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 62b9122f-66f7-4b64-96e1-185232fe4d94
TID: [-1234] [] [2024-10-30 15:13:06,913]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 15:13:08,865]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46593, SOCKET_TIMEOUT = 180000, CORRELATION_ID = e44d821f-230b-4993-992f-6cbe735155d9
TID: [-1234] [] [2024-10-30 15:13:08,866]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 15:13:12,874]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 32cf0687-69f2-4e37-9efe-2477d4fd7326
TID: [-1234] [] [2024-10-30 15:13:12,875]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46595, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 32cf0687-69f2-4e37-9efe-2477d4fd7326
TID: [-1234] [] [2024-10-30 15:13:12,876]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 15:14:59,993]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 63ec48b4-f381-499c-9e28-83845743666c
TID: [-1234] [] [2024-10-30 15:16:11,834]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 107afef7-e37f-49c7-9e66-d33befc406e9
TID: [-1234] [] [2024-10-30 15:16:13,020]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c74770aa-5945-4d05-8de2-87c00b18b484
TID: [-1234] [] [2024-10-30 15:16:13,349]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 6bd36d93-5b51-4f6a-b3ba-e7ccebb36531
TID: [-1234] [] [2024-10-30 15:16:13,350]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46601, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 6bd36d93-5b51-4f6a-b3ba-e7ccebb36531
TID: [-1234] [] [2024-10-30 15:16:13,350]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 15:19:18,347]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2dbb90b9-f76a-4a2c-b172-73386a59ad2e
TID: [-1234] [] [2024-10-30 15:19:22,007]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f1c099c3-e430-4e33-a1ac-193a3622761d
TID: [-1234] [] [2024-10-30 15:23:51,743]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f37b527e-42ae-4b0e-917d-0a9b8c92820c
TID: [-1234] [] [2024-10-30 15:32:16,097]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-10-30 15:47:17,757]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a4d4b13c-9b30-48ea-9e82-21160b78d2b9
TID: [-1234] [] [2024-10-30 15:49:47,967]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241030&denNgay=20241030&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241030&denNgay=20241030&maTthc=
TID: [-1234] [] [2024-10-30 15:49:48,009]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2024-10-30 16:02:16,549]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-10-30 16:05:33,596]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 16:05:33,597]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:39098, CORRELATION_ID = e4007a8e-542b-4314-a489-19e0855861ae, CONNECTION = http-incoming-867116
TID: [-1234] [] [2024-10-30 16:05:33,610]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46639, SOCKET_TIMEOUT = 180000, CORRELATION_ID = e4007a8e-542b-4314-a489-19e0855861ae
TID: [-1234] [] [2024-10-30 16:05:33,611]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 16:05:33,627]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-867116, CORRELATION_ID = e4007a8e-542b-4314-a489-19e0855861ae
TID: [-1234] [] [2024-10-30 16:08:43,350]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 25c3bf29-f007-4de4-8d75-4eb6909ef2bb
TID: [-1234] [] [2024-10-30 16:08:43,351]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46652, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 25c3bf29-f007-4de4-8d75-4eb6909ef2bb
TID: [-1234] [] [2024-10-30 16:08:43,352]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 16:08:47,057]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 16:08:47,057]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:49972, CORRELATION_ID = 53b5239a-206c-4f3b-9303-6b949bfceaa8, CONNECTION = http-incoming-867161
TID: [-1234] [] [2024-10-30 16:08:47,590]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 53b5239a-206c-4f3b-9303-6b949bfceaa8
TID: [-1234] [] [2024-10-30 16:08:47,591]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46635, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 53b5239a-206c-4f3b-9303-6b949bfceaa8
TID: [-1234] [] [2024-10-30 16:08:47,592]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 16:08:47,604]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-867161, CORRELATION_ID = 53b5239a-206c-4f3b-9303-6b949bfceaa8
TID: [-1234] [] [2024-10-30 16:08:49,347]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46655, SOCKET_TIMEOUT = 180000, CORRELATION_ID = a724037f-17b4-448e-ac70-38c25e590be6
TID: [-1234] [] [2024-10-30 16:08:49,349]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 16:11:53,320]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46630, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 0cd0aa5a-ab13-48b0-9260-f779f5db2bd3
TID: [-1234] [] [2024-10-30 16:11:53,322]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 16:11:53,340]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 16:11:53,341]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:39800, CORRELATION_ID = 39dde956-538e-4726-8b8b-6418c14566aa, CONNECTION = http-incoming-867209
TID: [-1234] [] [2024-10-30 16:11:53,839]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46640, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 39dde956-538e-4726-8b8b-6418c14566aa
TID: [-1234] [] [2024-10-30 16:11:53,840]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 16:11:53,851]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-867209, CORRELATION_ID = 39dde956-538e-4726-8b8b-6418c14566aa
TID: [-1234] [] [2024-10-30 16:11:54,843]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-10-30 16:11:54,844]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:39862, CORRELATION_ID = ae2276b4-97c6-49b0-9b3e-6e8df56e367e, CONNECTION = http-incoming-867217
TID: [-1234] [] [2024-10-30 16:11:54,845]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = ae2276b4-97c6-49b0-9b3e-6e8df56e367e
TID: [-1234] [] [2024-10-30 16:11:54,845]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46657, SOCKET_TIMEOUT = 180000, CORRELATION_ID = ae2276b4-97c6-49b0-9b3e-6e8df56e367e
TID: [-1234] [] [2024-10-30 16:11:54,846]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 16:11:54,857]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-867217, CORRELATION_ID = ae2276b4-97c6-49b0-9b3e-6e8df56e367e
TID: [-1234] [] [2024-10-30 16:12:01,328]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46654, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 8271582b-df39-4f06-93d7-1426eec38a9a
TID: [-1234] [] [2024-10-30 16:12:01,329]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 16:12:14,864]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f871f57c-73d0-40ab-8a44-b0c319b128c8
TID: [-1234] [] [2024-10-30 16:12:19,868]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 618e19af-038b-4a17-bc0f-776b82f115b8
TID: [-1234] [] [2024-10-30 16:15:01,758]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = ead4c20a-7b9a-44e8-ba3f-d3ef25956407
TID: [-1234] [] [2024-10-30 16:15:01,759]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-46658, SOCKET_TIMEOUT = 180000, CORRELATION_ID = ead4c20a-7b9a-44e8-ba3f-d3ef25956407
TID: [-1234] [] [2024-10-30 16:15:01,759]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-10-30 16:18:33,482]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b28a13cb-bdb0-47ec-a3b7-2b27e043c0ef
TID: [-1234] [] [2024-10-30 16:18:34,006]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 53ac8cae-8d62-4487-a062-5e5e4ea0f4ab
TID: [-1234] [] [2024-10-30 16:21:54,638]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 65482464-dac2-4ce7-aa24-80903f71d024
TID: [-1234] [] [2024-10-30 16:30:47,439]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e8b866cd-a7dd-466c-9be9-2093a533bdf1
TID: [-1234] [] [2024-10-30 16:32:19,167]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-10-30 16:32:41,326]  INFO {org.wso2.carbon.identity.application.authentication.framework.store.SessionCleanUpService} - Session Data cleanup task is running successfully for removing expired Data
TID: [-1234] [] [2024-10-30 16:35:46,007]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 30ae888a-17e4-49e7-8856-ec33a31d8d79
TID: [-1234] [] [2024-10-30 17:02:20,761]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-10-30 17:07:16,862]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9b65a219-e6bc-4f00-a34c-47d6bd360fc6
TID: [-1234] [] [2024-10-30 17:07:16,938]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ac1ed00e-76ba-474b-ac65-1ef9740199bb
TID: [-1234] [] [2024-10-30 17:07:23,018]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b0a1021b-6c5e-4a3e-9b3b-67bbcc5f3db8
TID: [-1234] [] [2024-10-30 17:07:26,093]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 23dd0d4e-1a4f-41ea-9d1b-9182c8a9ade8
TID: [-1234] [] [2024-10-30 17:07:27,839]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f36d702d-c050-44b3-aae1-e5eb39c36cdd
TID: [-1234] [] [2024-10-30 17:20:39,926] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2024-10-30 17:37:39,311]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-10-30 18:07:15,089]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fbf2b258-1684-4749-a03a-408fbd15863e
TID: [-1234] [] [2024-10-30 18:07:15,447]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 10c2a0d0-9821-4d68-94de-453e03c25055
TID: [-1234] [] [2024-10-30 18:07:20,867]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 183d1cf5-ed65-4c36-8eb9-af95c6f53a91
TID: [-1234] [] [2024-10-30 18:07:21,437]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d37f8f48-5246-490e-ba82-2b8306a098e6
TID: [-1234] [] [2024-10-30 18:12:43,326]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-10-30 18:19:44,306]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f6caacc9-d76d-40a1-bf17-085b5a3cf6b4
TID: [-1234] [] [2024-10-30 18:23:22,569]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241030&denNgay=20241030&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241030&denNgay=20241030&maTthc=
TID: [-1234] [] [2024-10-30 18:23:22,668]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2024-10-30 18:27:19,329]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = dbe788f0-4064-4a39-b8d1-f5c1824bf527
TID: [-1234] [] [2024-10-30 18:43:41,229]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-10-30 18:44:31,876]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1c4ade3c-7c44-4d54-bea8-14663f6461a2
TID: [-1234] [] [2024-10-30 19:06:50,596]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4aaa41d1-beb8-4261-9b90-1fb1ec0b55c2
TID: [-1234] [] [2024-10-30 19:06:51,401]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1afebe89-0010-41e4-bebc-6dcaf3d0d224
TID: [-1234] [] [2024-10-30 19:06:53,070]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4cdbbba6-d30b-414f-9443-a746f6d9f607
TID: [-1234] [] [2024-10-30 19:06:56,798]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2c740e3c-9d47-47d1-98ac-ae70558d753c
TID: [-1234] [] [2024-10-30 19:06:59,961]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 26028324-bf98-4ed5-88a2-281fcd2b3a93
TID: [-1234] [] [2024-10-30 19:07:00,220]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7fa7fe29-f2f8-46f3-8955-e27b5f2885aa
TID: [-1234] [] [2024-10-30 19:07:05,816]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6df9ec78-567f-4877-ab33-7637b0457b7a
TID: [-1234] [] [2024-10-30 19:07:06,616]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b3811a24-0746-4a9f-b6fb-6970752c98c1
TID: [-1234] [] [2024-10-30 19:13:41,479]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-10-30 19:43:41,714]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-10-30 19:47:19,839]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e83c75d8-c307-4c77-ac6d-dd4f8c4d7ca5
TID: [-1234] [] [2024-10-30 20:06:45,014]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8b75fe30-f58a-4260-a292-df25544b87a2
TID: [-1234] [] [2024-10-30 20:06:45,800]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6e9d993d-2eb3-4ff6-b21d-6ec15d2f067e
TID: [-1234] [] [2024-10-30 20:06:47,754]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = aa7cd690-17af-46c2-a606-80c6fcb5a66c
TID: [-1234] [] [2024-10-30 20:06:47,756]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0e95827b-9b1b-49b2-b7f2-aaf89337afcc
TID: [-1234] [] [2024-10-30 20:06:51,266]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a30277df-6d0e-493d-9fa4-8ed92d7fdcd9
TID: [-1234] [] [2024-10-30 20:06:56,103]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ebfb862b-d8cd-4fca-9059-0e0e4d2092e5
TID: [-1234] [] [2024-10-30 20:06:57,692]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = daeff4df-86df-4f7c-82f0-5e6622e3920c
TID: [-1234] [] [2024-10-30 20:06:59,297]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 077cfd76-3726-410e-bd09-a0bc6a66d44f
TID: [-1234] [] [2024-10-30 20:06:59,606]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6a820a23-bb89-4a31-b13a-daae0a377d5c
TID: [-1234] [] [2024-10-30 20:13:59,497]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-10-30 20:44:03,483]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-10-30 21:07:25,706]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 96d08db9-da64-4381-be03-c883b58ebcf9
TID: [-1234] [] [2024-10-30 21:07:34,512]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f99679cd-01cf-426f-9d2e-ef01bba987fc
TID: [-1234] [] [2024-10-30 21:07:36,425]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d37aea69-808d-4075-93fe-baa129bbbab0
TID: [-1234] [] [2024-10-30 21:07:36,493]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 444462fc-4375-44ae-9613-6dd3b872aba6
TID: [-1234] [] [2024-10-30 21:07:36,998]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7e8c2976-4f60-44ba-b186-ac5d529d6247
TID: [-1234] [] [2024-10-30 21:07:39,529]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3ab14338-5297-42a4-b83a-c8e6df806e36
TID: [-1234] [] [2024-10-30 21:07:40,803]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f083c543-4384-4fc5-9cee-7a65e791441b
TID: [-1234] [] [2024-10-30 21:07:42,173]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e2e79640-4ad5-43ac-9867-999b77648473
TID: [-1234] [] [2024-10-30 21:08:13,810]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2ccb5ecc-e269-41f9-b12f-83ddeaade47f
TID: [-1234] [] [2024-10-30 21:20:13,952]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Manage/Account/Login, HEALTH CHECK URL = /Manage/Account/Login
TID: [-1234] [] [2024-10-30 21:29:24,219]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-10-30 21:59:24,817]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-10-30 22:07:12,319]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7a859338-23c1-49fb-8e9f-0344f671207b
TID: [-1234] [] [2024-10-30 22:07:17,117]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = eb8fb174-d2f0-4abf-a333-55f11eb56c32
TID: [-1234] [] [2024-10-30 22:07:17,649]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c9ac0f3c-3929-4ea3-b394-eaabf5ae54fe
TID: [-1234] [] [2024-10-30 22:07:17,679]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7feb5f9a-6e03-4e7d-a4f5-0f3dc581dfbf
TID: [-1234] [] [2024-10-30 22:07:18,145]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 62266626-a5cf-42c9-a1e5-3f818301816b
TID: [-1234] [] [2024-10-30 22:07:21,651]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = dba3d88e-61c7-4f27-b441-a83e169ebe58
TID: [-1234] [] [2024-10-30 22:07:22,935]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4cbf5d52-bd38-483c-bc45-7b1a7e8d9314
TID: [-1234] [] [2024-10-30 22:07:26,341]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4c446f9f-b13e-4033-bce1-464323d91d6f
TID: [-1234] [] [2024-10-30 22:07:26,693]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e34d331a-3bda-45b2-8c76-5d0665da6789
TID: [-1234] [] [2024-10-30 22:25:02,658]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 65093acb-0c5f-4ff0-baf0-1096aff4876e
TID: [-1234] [] [2024-10-30 22:29:25,039]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-10-30 22:39:18,670]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d610dadc-8804-4e63-ba35-2e050b52f684
TID: [-1234] [] [2024-10-30 22:59:25,228]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-10-30 23:06:20,876]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a10b8f9a-4766-45d1-a6b7-2c9ae0ad0588
TID: [-1234] [] [2024-10-30 23:06:23,005]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 007e5faf-2053-4c31-9871-f23c1893a402
TID: [-1234] [] [2024-10-30 23:06:23,246]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 241ceb1a-e34f-4586-9df9-664ce800201f
TID: [-1234] [] [2024-10-30 23:06:24,615]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c6c7352c-1968-4116-9717-ddd684b484f3
TID: [-1234] [] [2024-10-30 23:06:25,607]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 82c37dd5-e770-464c-9975-6fb357af0fc1
TID: [-1234] [] [2024-10-30 23:06:28,398]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = dc485b57-088d-46d4-8750-a0d88d0eaf0a
TID: [-1234] [] [2024-10-30 23:06:28,992]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 031375ac-3852-4645-a483-2b4c9132c28e
TID: [-1234] [] [2024-10-30 23:06:32,958]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 372b8445-345e-4504-b63a-e89bba1379a2
TID: [-1234] [] [2024-10-30 23:06:33,224]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c3a09cc3-4085-40aa-8e97-18622a0bf5db
TID: [-1234] [] [2024-10-30 23:29:25,612]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-10-30 23:35:06,077]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9730a9c6-80ad-4a39-a4a6-70fc64518707
TID: [-1234] [] [2024-10-30 23:59:25,837]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
