TID: [-1234] [] [2024-12-31 00:00:03,618]  WARN {org.apache.synapse.commons.util.MiscellaneousUtil} - Error loading properties from a file at from the System defined location: access-log.properties
TID: [-1234] [] [2024-12-31 00:03:20,241]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /adminer.php, HEALTH CHECK URL = /adminer.php
TID: [-1234] [] [2024-12-31 00:03:24,124]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_adminer.php, HEALTH CHECK URL = /_adminer.php
TID: [-1234] [] [2024-12-31 00:03:28,175]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /adminer/, HEALTH CHECK URL = /adminer/
TID: [-1234] [] [2024-12-31 00:03:32,250]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /editor.php, HEALTH CHECK URL = /editor.php
TID: [-1234] [] [2024-12-31 00:03:36,108]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mysql.php, HEALTH CHECK URL = /mysql.php
TID: [-1234] [] [2024-12-31 00:03:40,117]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sql.php, HEALTH CHECK URL = /sql.php
TID: [-1234] [] [2024-12-31 00:03:44,092]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/adminer/adminer.php, HEALTH CHECK URL = /wp-content/plugins/adminer/adminer.php
TID: [-1234] [] [2024-12-31 00:03:48,055]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin.php, HEALTH CHECK URL = /admin.php
TID: [-1234] [] [2024-12-31 00:03:52,049]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /modules/sfkdbmanage/adminer.php, HEALTH CHECK URL = /modules/sfkdbmanage/adminer.php
TID: [-1234] [] [2024-12-31 00:05:47,943]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2072d247-d297-4872-946a-385f5696e75e
TID: [-1234] [] [2024-12-31 00:05:48,668]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 55247f38-51d4-4d2d-aff3-4ac35db9e74d
TID: [-1234] [] [2024-12-31 00:05:50,949]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4f38ffa9-f9fb-4b69-8812-7827b8a570ca
TID: [-1234] [] [2024-12-31 00:05:53,155]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 18025c80-2a80-45ef-8874-8e401d80f23b
TID: [-1234] [] [2024-12-31 00:05:56,395]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7e4b623b-3075-4675-88d9-a9a6b17b3cb9
TID: [-1234] [] [2024-12-31 00:05:57,993]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 024f0a97-0c6e-4ab6-94c4-bcee32c43d3e
TID: [-1234] [] [2024-12-31 00:06:08,889]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a7561f5c-8522-4cab-bf9f-cadf119d0589
TID: [-1234] [] [2024-12-31 00:07:11,106]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a79701dd-4f01-4196-8a65-3f203c996c4c
TID: [-1234] [] [2024-12-31 00:16:15,976]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-31 00:46:17,299]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-31 01:05:57,344]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8e3cf607-08ba-4314-bc8c-065f9f256d84
TID: [-1234] [] [2024-12-31 01:06:00,707]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4cc19605-7275-490f-805b-9d67d09121ea
TID: [-1234] [] [2024-12-31 01:06:03,557]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9c842734-2f50-4a0e-9485-2dc9098770e2
TID: [-1234] [] [2024-12-31 01:06:05,165]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 15d80a2a-dee1-4750-a942-ebcfc74a0696
TID: [-1234] [] [2024-12-31 01:06:07,516]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1e01701f-312a-4c8b-81a9-449df1238ca7
TID: [-1234] [] [2024-12-31 01:06:11,497]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0c5982f8-117f-4548-aea1-aaaafe2aa776
TID: [-1234] [] [2024-12-31 01:07:13,357]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 56b260a5-880f-449f-86ce-a56dabdf1d97
TID: [-1234] [] [2024-12-31 01:16:17,884]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-31 01:46:18,056]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-31 02:05:56,291]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 187b6664-f89d-4871-885d-7563e0fbbae6
TID: [-1234] [] [2024-12-31 02:05:58,815]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 09513f8b-12e9-43dc-ae46-41e158d65c37
TID: [-1234] [] [2024-12-31 02:06:00,114]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = aa43abed-c06f-48f2-a45d-e9c87ac62d97
TID: [-1234] [] [2024-12-31 02:06:00,789]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 68ce4926-1ed5-4e32-bcd8-1f74084a40a9
TID: [-1234] [] [2024-12-31 02:06:02,931]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e4dfd76c-704f-48c6-9182-d49037a78486
TID: [-1234] [] [2024-12-31 02:06:06,662]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7c3dbc00-57bb-4b75-be77-54f577fca715
TID: [-1234] [] [2024-12-31 02:07:08,691]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 20468e64-df54-4d8a-a9e2-ff1752d3b5fd
TID: [-1234] [] [2024-12-31 02:16:19,837]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-31 02:46:20,030]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-31 02:55:37,404]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/phpMyAdmin, HEALTH CHECK URL = /admin/phpMyAdmin
TID: [-1234] [] [2024-12-31 02:55:37,544]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpma/, HEALTH CHECK URL = /phpma/
TID: [-1234] [] [2024-12-31 02:55:37,967]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /blog/phpmyadmin/, HEALTH CHECK URL = /blog/phpmyadmin/
TID: [-1234] [] [2024-12-31 02:55:38,337]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /forum/phpmyadmin/, HEALTH CHECK URL = /forum/phpmyadmin/
TID: [-1234] [] [2024-12-31 02:55:38,542]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /apache-default/phpmyadmin/, HEALTH CHECK URL = /apache-default/phpmyadmin/
TID: [-1234] [] [2024-12-31 02:55:38,744]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2phpmyadmin/, HEALTH CHECK URL = /2phpmyadmin/
TID: [-1234] [] [2024-12-31 02:55:38,745]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/phpMyAdmin/index.php, HEALTH CHECK URL = /admin/phpMyAdmin/index.php
TID: [-1234] [] [2024-12-31 02:55:38,746]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /web/phpmyadmin/, HEALTH CHECK URL = /web/phpmyadmin/
TID: [-1234] [] [2024-12-31 02:55:38,748]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.tools/phpMyAdmin/current/, HEALTH CHECK URL = /.tools/phpMyAdmin/current/
TID: [-1234] [] [2024-12-31 02:55:39,116]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_phpmyadmin/, HEALTH CHECK URL = /_phpmyadmin/
TID: [-1234] [] [2024-12-31 02:55:39,816]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpMyAdmin/, HEALTH CHECK URL = /phpMyAdmin/
TID: [-1234] [] [2024-12-31 02:55:40,093]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-31 02:55:40,346]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/phpmyadmin/index.php, HEALTH CHECK URL = /admin/phpmyadmin/index.php
TID: [-1234] [] [2024-12-31 02:55:40,347]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/pma/, HEALTH CHECK URL = /admin/pma/
TID: [-1234] [] [2024-12-31 02:55:40,521]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.tools/phpMyAdmin/, HEALTH CHECK URL = /.tools/phpMyAdmin/
TID: [-1234] [] [2024-12-31 02:55:40,524]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/phpmyadmin/, HEALTH CHECK URL = /admin/phpmyadmin/
TID: [-1234] [] [2024-12-31 02:55:40,537]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/phpmyadmin2/index.php, HEALTH CHECK URL = /admin/phpmyadmin2/index.php
TID: [-1234] [] [2024-12-31 02:55:40,582]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/phpMyAdmin/, HEALTH CHECK URL = /admin/phpMyAdmin/
TID: [-1234] [] [2024-12-31 02:55:49,025]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /phpmyadmin/, HEALTH CHECK URL = /phpmyadmin/
TID: [-1234] [] [2024-12-31 02:55:49,064]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /xampp/phpmyadmin/, HEALTH CHECK URL = /xampp/phpmyadmin/
TID: [-1234] [] [2024-12-31 02:55:49,068]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /blog/phpmyadmin/, HEALTH CHECK URL = /blog/phpmyadmin/
TID: [-1234] [] [2024-12-31 02:55:49,068]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /claroline/phpMyAdmin/index.php, HEALTH CHECK URL = /claroline/phpMyAdmin/index.php
TID: [-1234] [] [2024-12-31 03:05:56,833]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8d54da7b-ae7d-4d9b-9dc4-e3a9de24bb13
TID: [-1234] [] [2024-12-31 03:06:00,094]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d04a8872-fbaf-4778-b7ee-ec11f44d0519
TID: [-1234] [] [2024-12-31 03:06:00,920]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b000948b-ba43-4ae4-b3b8-d3fc554bea64
TID: [-1234] [] [2024-12-31 03:06:08,454]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 678b9a24-2f1e-412d-8493-ccfe4a56dca0
TID: [-1234] [] [2024-12-31 03:06:09,673]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 27aed52f-574f-407d-ba56-bab26a80eef0
TID: [-1234] [] [2024-12-31 03:07:11,662]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3433e5b2-9dcc-486b-a60f-0c2ac191382f
TID: [-1234] [] [2024-12-31 03:16:21,326]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-31 03:46:21,467]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-31 04:05:50,709]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0eef8d75-0323-4a7b-8928-402c37213f0c
TID: [-1234] [] [2024-12-31 04:05:55,688]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 83c8cdb2-edcd-44f7-829a-fa792e31a6f4
TID: [-1234] [] [2024-12-31 04:05:57,080]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6821fa0d-5247-4778-be7e-aa4df0bfba5b
TID: [-1234] [] [2024-12-31 04:05:57,310]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 93546ef1-7d22-4cb5-b96e-dd31306002a0
TID: [-1234] [] [2024-12-31 04:05:58,685]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3796233b-8984-4ebb-bf77-cdb777c89c7a
TID: [-1234] [] [2024-12-31 04:06:03,480]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 332aed01-d8d1-4b67-9506-374ff1ab9a8b
TID: [-1234] [] [2024-12-31 04:16:21,982]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-31 04:46:22,187]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-31 05:05:33,614]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3a47f317-beb1-4b2d-a4ad-eaebfdf71604
TID: [-1234] [] [2024-12-31 05:05:36,284]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7c529548-bcd3-404c-a48f-2c214ce26822
TID: [-1234] [] [2024-12-31 05:05:36,904]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d269bd2e-db60-4376-9028-55a1bdab7c6c
TID: [-1234] [] [2024-12-31 05:05:37,307]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a30b151e-092a-490f-a62b-7611fb2987ac
TID: [-1234] [] [2024-12-31 05:05:37,955]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9e1586f9-18af-4753-807c-e177e937b254
TID: [-1234] [] [2024-12-31 05:05:43,696]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1310ca17-92e0-4609-b0b1-894c4edcd2df
TID: [-1234] [] [2024-12-31 05:05:49,398]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 484e4861-99d1-4685-b9a9-12b978cd47d4
TID: [-1234] [] [2024-12-31 05:16:22,370]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-31 05:46:22,923]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-31 06:05:45,538]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ff802bc0-1a27-4e00-94ef-e01a7f0a06d7
TID: [-1234] [] [2024-12-31 06:05:46,319]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7cd91c1d-70be-4668-9420-46bc1a835a3a
TID: [-1234] [] [2024-12-31 06:05:48,151]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 747f87e1-9cb7-4a82-8bd6-7bd316993b02
TID: [-1234] [] [2024-12-31 06:05:49,826]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3a90fda1-06ca-4b01-afe8-872bedfa6385
TID: [-1234] [] [2024-12-31 06:05:53,429]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d3be1e98-de68-4427-a108-a65a8cedf12e
TID: [-1234] [] [2024-12-31 06:05:57,446]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7256276c-47fd-41d3-b860-340430f1a820
TID: [-1234] [] [2024-12-31 06:16:23,315]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-31 06:46:23,721]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-31 07:05:49,908]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9e43cb27-dcd7-4b6d-ae5f-d5603bf5532d
TID: [-1234] [] [2024-12-31 07:05:51,274]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8276c664-c26c-4321-af42-593907b96aef
TID: [-1234] [] [2024-12-31 07:05:51,773]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 47c087e0-6525-44cd-bd25-bc44d3d78ab5
TID: [-1234] [] [2024-12-31 07:05:57,481]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f16f99ba-066d-4ade-a1db-676a89993b8e
TID: [-1234] [] [2024-12-31 07:05:58,653]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ce4b9c44-6a33-4491-a7a5-331a1c97803c
TID: [-1234] [] [2024-12-31 07:06:04,315]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 04b24ae6-a33a-405d-840c-3b1cb14ad8e0
TID: [-1234] [] [2024-12-31 07:16:23,856]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-31 07:41:28,414]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241231&denNgay=20241231&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241231&denNgay=20241231&maTthc=
TID: [-1234] [] [2024-12-31 07:41:28,524]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-31 07:46:24,338]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-31 08:05:53,040]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6feaac64-fd83-415b-8b66-fc7030b8e95e
TID: [-1234] [] [2024-12-31 08:05:55,658]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4ac0eab7-3998-4384-8e1d-006185a6301c
TID: [-1234] [] [2024-12-31 08:05:59,928]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f8f6699f-871a-41c6-bd8e-d37b52b37f98
TID: [-1234] [] [2024-12-31 08:06:02,217]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a0467ac8-c3c1-4630-b788-f61f98e199b1
TID: [-1234] [] [2024-12-31 08:06:10,431]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fb96f6ba-dab3-4831-ad8f-6e96cce26ca4
TID: [-1234] [] [2024-12-31 08:09:52,531] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-31 08:09:52,533]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-31 08:16:24,884]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-31 08:34:34,776] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-31 08:34:34,778]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-31 08:46:25,602]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-31 09:06:14,701]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9652643d-b86f-4f7e-ac44-0469e9a36896
TID: [-1234] [] [2024-12-31 09:16:26,159]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-31 09:30:49,835]  INFO {org.wso2.carbon.identity.application.authentication.framework.store.SessionCleanUpService} - Session Data cleanup task is running successfully for removing expired Data
TID: [-1234] [] [2024-12-31 09:48:28,846]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-31 09:50:31,680]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241231&denNgay=20241231&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241231&denNgay=20241231&maTthc=
TID: [-1234] [] [2024-12-31 09:50:31,723]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-12-31 10:06:24,853]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-31 10:06:24,855]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Tue Dec 31 10:06:54 ICT 2024
TID: [-1234] [] [2024-12-31 10:06:24,856]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-31 10:06:24,875]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:7e1def14-d21e-4bf9-87e4-2c43c6a57f85; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/giaypheplaixe/login, Received through API : admin--GPLX2:v1.0, CORRELATION_ID = e1741469-17ad-4644-9e31-b4974f95cfc7
TID: [-1234] [] [2024-12-31 10:06:40,140]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1c9123c6-7cf8-496e-aa87-bc86bcc7b2a0
TID: [-1234] [] [2024-12-31 10:06:40,173]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = de561760-a7b9-40e2-9a94-84ee3520725d
TID: [-1234] [] [2024-12-31 10:06:42,748]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 84166821-2544-4e8e-9473-4f25436e4446
TID: [-1234] [] [2024-12-31 10:06:45,265]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 353385c4-45f8-4ca2-9d0a-398c7c36a63c
TID: [-1234] [] [2024-12-31 10:06:46,712]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 735c0cb4-48b3-4ce5-a2a8-ab121e6eeab0
TID: [-1234] [] [2024-12-31 10:07:12,672]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = e1741469-17ad-4644-9e31-b4974f95cfc7
TID: [-1234] [] [2024-12-31 10:07:12,674]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/giaypheplaixe/login, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--GPLX2:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-982, SOCKET_TIMEOUT = 180000, CORRELATION_ID = e1741469-17ad-4644-9e31-b4974f95cfc7
TID: [-1234] [] [2024-12-31 10:08:37,893] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-31 10:08:37,894]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-31 10:18:31,974]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-31 10:53:50,219]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-31 11:06:21,159]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 69d69d7e-32a5-4c93-bf5a-1390462acf5c
TID: [-1234] [] [2024-12-31 11:06:21,955]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 10ce275e-1c23-45a4-881b-27c55175afe6
TID: [-1234] [] [2024-12-31 11:06:27,293]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9b708165-d66a-41c3-a3ae-b1ba8edce325
TID: [-1234] [] [2024-12-31 11:06:27,698]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ca1067e2-0e00-499b-b854-2f0d339b6ffa
TID: [-1234] [] [2024-12-31 11:06:27,896]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 601ffc26-28fe-4d82-96ee-8cd0b059019a
TID: [-1234] [] [2024-12-31 11:06:28,699]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = be5e51fa-3acf-4b33-857a-5209c5f69d66
TID: [-1234] [] [2024-12-31 11:08:55,696]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 47e6792a-5555-48cd-8c64-22b1f9e28120
TID: [-1234] [] [2024-12-31 11:09:33,833]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 480616ed-ba6e-4b22-a7d7-20f71f459e70
TID: [-1234] [] [2024-12-31 11:10:27,612]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 272d06fa-1db9-47cf-94dd-c813270ec455
TID: [-1234] [] [2024-12-31 11:14:55,349]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-31 11:14:55,351]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Tue Dec 31 11:15:25 ICT 2024
TID: [-1234] [] [2024-12-31 11:14:55,351]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-31 11:14:55,368]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:b2b04aa3-5db4-4d6f-aa70-160ef721b906; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/giaypheplaixe/login, Received through API : admin--GPLX2:v1.0, CORRELATION_ID = 4bc00bbd-7e4d-4d0d-a344-6b9598ddd5c9
TID: [-1234] [] [2024-12-31 11:15:45,350]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 4bc00bbd-7e4d-4d0d-a344-6b9598ddd5c9
TID: [-1234] [] [2024-12-31 11:15:45,352]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/giaypheplaixe/login, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--GPLX2:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1041, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 4bc00bbd-7e4d-4d0d-a344-6b9598ddd5c9
TID: [-1234] [] [2024-12-31 11:23:55,355]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-31 11:23:55,356]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Tue Dec 31 11:24:25 ICT 2024
TID: [-1234] [] [2024-12-31 11:23:55,357]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-31 11:23:55,369]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:669c63e6-5b56-498e-8af9-4e92e82b46cc; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/giaypheplaixe/login, Received through API : admin--GPLX2:v1.0, CORRELATION_ID = 774dc50a-b761-4475-b3d0-9bdb1645cccb
TID: [-1234] [] [2024-12-31 11:24:32,574]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-31 11:24:47,432]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 774dc50a-b761-4475-b3d0-9bdb1645cccb
TID: [-1234] [] [2024-12-31 11:24:47,433]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/giaypheplaixe/login, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--GPLX2:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1044, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 774dc50a-b761-4475-b3d0-9bdb1645cccb
TID: [-1234] [] [2024-12-31 11:25:40,357]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-31 11:25:40,358]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Tue Dec 31 11:26:10 ICT 2024
TID: [-1234] [] [2024-12-31 11:25:40,358]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-31 11:25:40,370]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:c4200e99-3eb9-4538-a211-0cab9a05d39d; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/giaypheplaixe/login, Received through API : admin--GPLX2:v1.0, CORRELATION_ID = c92c6d72-099c-467a-b667-76fdfdb61a80
TID: [-1234] [] [2024-12-31 11:26:35,924]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = c92c6d72-099c-467a-b667-76fdfdb61a80
TID: [-1234] [] [2024-12-31 11:26:35,925]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/giaypheplaixe/login, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--GPLX2:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1046, SOCKET_TIMEOUT = 180000, CORRELATION_ID = c92c6d72-099c-467a-b667-76fdfdb61a80
TID: [-1234] [] [2024-12-31 11:26:40,358]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-31 11:26:40,359]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Tue Dec 31 11:27:10 ICT 2024
TID: [-1234] [] [2024-12-31 11:26:40,359]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-31 11:26:40,371]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:a3d6d8d3-ecd3-49ae-8ab7-0867ebff59e5; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/giaypheplaixe/tracuuhoso, Received through API : admin--GPLX2:v1.0, CORRELATION_ID = 7bba6aed-5097-457d-b248-640bcfbae587
TID: [-1234] [] [2024-12-31 11:27:15,418]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe currently SUSPENDED will now be marked active since it processed its last message
TID: [-1234] [] [2024-12-31 11:27:32,668]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 7bba6aed-5097-457d-b248-640bcfbae587
TID: [-1234] [] [2024-12-31 11:27:32,669]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/giaypheplaixe/tracuuhoso, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--GPLX2:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1047, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 7bba6aed-5097-457d-b248-640bcfbae587
TID: [-1234] [] [2024-12-31 11:46:40,372]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-31 11:46:40,373]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap - current suspend duration is : 30000ms - Next retry after : Tue Dec 31 11:47:10 ICT 2024
TID: [-1234] [] [2024-12-31 11:46:40,374]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-31 11:46:40,388]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:c71a939d-c408-4aeb-a20f-a2cf9df6fbc7; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/lylichtuphap/nhanHoSo, Received through API : admin--LyLichTuPhap:v1.0, CORRELATION_ID = 20ce2d3a-2e91-464c-a9cb-ad4fabccc5f5
TID: [-1234] [] [2024-12-31 11:46:43,670]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-31 11:46:48,179]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-31 11:47:31,353]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/lylichtuphap/nhanHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--LyLichTuPhap:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1059, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 20ce2d3a-2e91-464c-a9cb-ad4fabccc5f5
TID: [-1234] [] [2024-12-31 11:53:40,377]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-31 11:53:40,379]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe - current suspend duration is : 30000ms - Next retry after : Tue Dec 31 11:54:10 ICT 2024
TID: [-1234] [] [2024-12-31 11:53:40,380]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-31 11:53:40,393]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:d8b9ae6a-3060-451e-b867-dd4f3519a32d; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/giaypheplaixe/login, Received through API : admin--GPLX2:v1.0, CORRELATION_ID = a686e274-1b3d-49c4-94f0-1a6d134a26e8
TID: [-1234] [publisher] [2024-12-31 11:53:41,492]  WARN {JAGGERY.services.refresh.refresh:jag} - Something went wrong while refreshing the token
TID: [-1234] [publisher] [2024-12-31 11:53:41,493] ERROR {JAGGERY.services.refresh.refresh:jag} - {}
TID: [-1234] [api/am/publisher] [2024-12-31 11:53:41,516] ERROR {org.wso2.carbon.apimgt.rest.api.util.impl.WebAppAuthenticatorImpl} - Invalid OAuth Token : Invalid Access Token. ACTIVE access token is not found.
TID: [-1234] [api/am/publisher] [2024-12-31 11:53:41,517] ERROR {org.wso2.carbon.apimgt.rest.api.util.impl.WebAppAuthenticatorImpl} - Provided access token is invalid
TID: [-1234] [] [2024-12-31 11:54:36,400]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = a686e274-1b3d-49c4-94f0-1a6d134a26e8
TID: [-1234] [] [2024-12-31 11:54:36,401]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/giaypheplaixe/login, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--GPLX2:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1055, SOCKET_TIMEOUT = 180000, CORRELATION_ID = a686e274-1b3d-49c4-94f0-1a6d134a26e8
TID: [-1234] [] [2024-12-31 11:55:56,975]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-31 11:55:57,021]  INFO {org.wso2.carbon.event.output.adapter.jms.internal.util.JMSConnectionFactory} - JMS ConnectionFactory : notificationJMSPublisher initialized
TID: [-1234] [AuthenticationAdmin] [2024-12-31 11:55:58,162]  INFO {org.wso2.carbon.core.services.util.CarbonAuthenticationUtil} - '<EMAIL> [-1234]' logged in at [2024-12-31 11:55:58,161+0700]
TID: [-1234] [APIGatewayAdmin] [2024-12-31 11:55:58,344]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Local entry : fa3f961d-54ef-4550-ad2b-a8e522059ab1 was added to the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-31 11:55:58,357]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : GPLX3--v1.0_APIproductionEndpoint was added to the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-31 11:55:58,364]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : GPLX3--v1.0_APIsandboxEndpoint was added to the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-31 11:55:58,371]  INFO {org.apache.synapse.rest.API} - {api:admin--GPLX3:v1.0} Initializing API: admin--GPLX3:v1.0
TID: [-1234] [APIGatewayAdmin] [2024-12-31 11:55:58,380]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : admin--GPLX3:v1.0 was added to the Synapse configuration successfully
TID: [-1234] [AuthenticationAdmin] [2024-12-31 11:55:58,517]  INFO {org.wso2.carbon.core.services.util.CarbonAuthenticationUtil} - '<EMAIL> [-1234]' logged in at [2024-12-31 11:55:58,517+0700]
TID: [-1234] [AuthenticationAdmin] [2024-12-31 11:56:32,577]  INFO {org.wso2.carbon.core.services.util.CarbonAuthenticationUtil} - '<EMAIL> [-1234]' logged in at [2024-12-31 11:56:32,577+0700]
TID: [-1234] [AuthenticationAdmin] [2024-12-31 11:56:33,023]  INFO {org.wso2.carbon.core.services.util.CarbonAuthenticationUtil} - '<EMAIL> [-1234]' logged in at [2024-12-31 11:56:33,022+0700]
TID: [-1234] [AuthenticationAdmin] [2024-12-31 11:56:33,221]  INFO {org.wso2.carbon.core.services.util.CarbonAuthenticationUtil} - '<EMAIL> [-1234]' logged in at [2024-12-31 11:56:33,220+0700]
TID: [-1234] [APIGatewayAdmin] [2024-12-31 11:56:33,234]  INFO {org.apache.synapse.rest.API} - {api:admin--GPLX3:v1.0} Destroying API: admin--GPLX3:v1.0
TID: [-1234] [APIGatewayAdmin] [2024-12-31 11:56:33,273]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : admin--GPLX3:v1.0 was removed from the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-31 11:56:33,275]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Local entry : fa3f961d-54ef-4550-ad2b-a8e522059ab1 was removed from the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-31 11:56:33,276]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Local entry : fa3f961d-54ef-4550-ad2b-a8e522059ab1 was added to the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-31 11:56:33,283]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : GPLX3--v1.0_APIproductionEndpoint was added to the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-31 11:56:33,290]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : GPLX3--v1.0_APIsandboxEndpoint was added to the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-31 11:56:33,296]  INFO {org.apache.synapse.rest.API} - {api:admin--GPLX3:v1.0} Initializing API: admin--GPLX3:v1.0
TID: [-1234] [APIGatewayAdmin] [2024-12-31 11:56:33,297]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : admin--GPLX3:v1.0 was added to the Synapse configuration successfully
TID: [-1234] [AuthenticationAdmin] [2024-12-31 11:56:33,338]  INFO {org.wso2.carbon.core.services.util.CarbonAuthenticationUtil} - '<EMAIL> [-1234]' logged in at [2024-12-31 11:56:33,338+0700]
TID: [-1234] [] [2024-12-31 11:56:33,517]  INFO {org.wso2.carbon.event.output.adapter.jms.internal.util.JMSConnectionFactory} - JMS ConnectionFactory : cacheInvalidationJMSPublisher initialized
TID: [-1234] [] [2024-12-31 11:56:48,017]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /gplx/login, HEALTH CHECK URL = /gplx/login
TID: [-1234] [] [2024-12-31 11:56:59,534]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /gplx/login, HEALTH CHECK URL = /gplx/login
TID: [-1234] [] [2024-12-31 11:58:47,136]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [AuthenticationAdmin] [2024-12-31 11:58:48,885]  INFO {org.wso2.carbon.core.services.util.CarbonAuthenticationUtil} - '<EMAIL> [-1234]' logged in at [2024-12-31 11:58:48,884+0700]
TID: [-1234] [AuthenticationAdmin] [2024-12-31 11:58:49,279]  INFO {org.wso2.carbon.core.services.util.CarbonAuthenticationUtil} - '<EMAIL> [-1234]' logged in at [2024-12-31 11:58:49,279+0700]
TID: [-1234] [AuthenticationAdmin] [2024-12-31 11:58:49,441]  INFO {org.wso2.carbon.core.services.util.CarbonAuthenticationUtil} - '<EMAIL> [-1234]' logged in at [2024-12-31 11:58:49,441+0700]
TID: [-1234] [APIGatewayAdmin] [2024-12-31 11:58:49,488]  INFO {org.apache.synapse.rest.API} - {api:admin--GPLX3:v1.0} Destroying API: admin--GPLX3:v1.0
TID: [-1234] [APIGatewayAdmin] [2024-12-31 11:58:49,497]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : admin--GPLX3:v1.0 was removed from the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-31 11:58:49,498]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Local entry : fa3f961d-54ef-4550-ad2b-a8e522059ab1 was removed from the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-31 11:58:49,500]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Local entry : fa3f961d-54ef-4550-ad2b-a8e522059ab1 was added to the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-31 11:58:49,507]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : GPLX3--v1.0_APIproductionEndpoint was added to the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-31 11:58:49,513]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : GPLX3--v1.0_APIsandboxEndpoint was added to the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-31 11:58:49,515]  INFO {org.apache.synapse.rest.API} - {api:admin--GPLX3:v1.0} Initializing API: admin--GPLX3:v1.0
TID: [-1234] [APIGatewayAdmin] [2024-12-31 11:58:49,516]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : admin--GPLX3:v1.0 was added to the Synapse configuration successfully
TID: [-1234] [AuthenticationAdmin] [2024-12-31 11:58:49,568]  INFO {org.wso2.carbon.core.services.util.CarbonAuthenticationUtil} - '<EMAIL> [-1234]' logged in at [2024-12-31 11:58:49,568+0700]
TID: [-1234] [] [2024-12-31 12:01:10,383]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : GPLX3--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-31 12:01:10,384]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : GPLX3--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe - current suspend duration is : 30000ms - Next retry after : Tue Dec 31 12:01:40 ICT 2024
TID: [-1234] [] [2024-12-31 12:01:10,385]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX3:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-31 12:01:10,399]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:21bea12b-490e-4a42-a6b2-e84e8a4ba072; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/giaypheplaixe/login, Received through API : admin--GPLX3:v1.0, CORRELATION_ID = c5ee7019-5247-4f85-b09e-48c0da9b7b5c
TID: [-1234] [] [2024-12-31 12:01:59,413]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = c5ee7019-5247-4f85-b09e-48c0da9b7b5c
TID: [-1234] [] [2024-12-31 12:01:59,414]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/giaypheplaixe/login, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--GPLX3:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1063, SOCKET_TIMEOUT = 180000, CORRELATION_ID = c5ee7019-5247-4f85-b09e-48c0da9b7b5c
TID: [-1234] [] [2024-12-31 12:06:07,386]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5b5c04de-f2e7-418c-aaca-961e2b3f0e7b
TID: [-1234] [] [2024-12-31 12:06:08,295]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a7914792-5081-4563-b5f9-90cf526b8e19
TID: [-1234] [] [2024-12-31 12:06:10,670]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = db7bbf30-62b3-4535-82f4-e9562f7f217f
TID: [-1234] [] [2024-12-31 12:06:11,276]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = eabd2306-082f-4a8c-bbdb-20ed00e2a5c3
TID: [-1234] [] [2024-12-31 12:06:15,254]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 30e22c7e-ae15-4b57-9cf8-c6bba3d4e0eb
TID: [-1234] [] [2024-12-31 12:06:15,306]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d8273db2-a63e-4b70-ba39-4d9060256b57
TID: [-1234] [] [2024-12-31 12:06:20,651]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8683d6e6-cd0c-4e0f-ac14-82ec0e9b382c
TID: [-1234] [] [2024-12-31 12:06:25,641]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b75c4088-864f-40d1-834e-ad51a9a70d60
TID: [-1234] [] [2024-12-31 12:07:03,829]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : GPLX3--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe currently SUSPENDED will now be marked active since it processed its last message
TID: [-1234] [] [2024-12-31 12:07:28,001]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : GPLX2--v1.0_APIproductionEndpoint with address http://************:8290/giaypheplaixe currently SUSPENDED will now be marked active since it processed its last message
TID: [-1234] [] [2024-12-31 12:28:18,338]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap currently SUSPENDED will now be marked active since it processed its last message
TID: [-1234] [] [2024-12-31 12:28:47,384]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-31 12:40:09,320]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6f308642-f3f9-4752-afda-7535d2fcc007
TID: [-1234] [] [2024-12-31 12:40:09,700]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = afa605e7-4837-464a-9616-a9713931cfac
TID: [-1234] [] [2024-12-31 12:58:47,667]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-31 13:05:59,496]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b915e33f-f383-4912-acf4-83d063e555cb
TID: [-1234] [] [2024-12-31 13:05:59,690]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8599532c-d3b5-4cf5-8a85-5a922b554a58
TID: [-1234] [] [2024-12-31 13:06:03,530]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 059345cb-8346-44c2-a4f3-fb0d9d99aba5
TID: [-1234] [] [2024-12-31 13:06:04,222]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 95aa373a-6dbb-4b1a-bc47-2e4177eef0ed
TID: [-1234] [] [2024-12-31 13:06:04,290]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 799594ed-d1d8-44e7-b6d0-ca114b320e76
TID: [-1234] [] [2024-12-31 13:06:06,885]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5c65a390-1037-4a06-91f2-7ebdeb1a1e0b
TID: [-1234] [] [2024-12-31 13:06:12,769]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f16ef517-37db-4d26-a603-f6b903ef0912
TID: [-1234] [] [2024-12-31 13:06:15,676]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a402a057-c764-4c0e-b36e-ca65b0d715aa
TID: [-1234] [] [2024-12-31 13:21:41,243]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e0d172ee-0a39-4544-b318-9bdf470e0f5e
TID: [-1234] [] [2024-12-31 13:28:58,571]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-31 13:38:52,345]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 64a6c934-3486-4538-98d1-891f691baf27
TID: [-1234] [] [2024-12-31 13:48:40,540]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-31 13:48:40,543]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong - current suspend duration is : 30000ms - Next retry after : Tue Dec 31 13:49:10 ICT 2024
TID: [-1234] [] [2024-12-31 13:48:40,544]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--QuanLyTaiSanCong:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-31 13:48:40,557]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:460168d3-3a83-42a3-8c9d-08e91aa6908b; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/quanlytaisancong/GetTaiSanDonVi, Received through API : admin--QuanLyTaiSanCong:v1.0, CORRELATION_ID = 7be958d7-a4be-4265-ae75-11aef2143224
TID: [-1234] [] [2024-12-31 13:48:48,977]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241231&denNgay=20241231&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241231&denNgay=20241231&maTthc=
TID: [-1234] [] [2024-12-31 13:48:49,021]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-31 13:48:56,978]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241231&denNgay=20241231&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241231&denNgay=20241231&maTthc=
TID: [-1234] [] [2024-12-31 13:48:57,023]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-31 13:49:28,628]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 7be958d7-a4be-4265-ae75-11aef2143224
TID: [-1234] [] [2024-12-31 13:49:28,629]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/quanlytaisancong/GetTaiSanDonVi, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--QuanLyTaiSanCong:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1129, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 7be958d7-a4be-4265-ae75-11aef2143224
TID: [-1234] [] [2024-12-31 13:50:10,541]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-31 13:50:10,542]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Tue Dec 31 13:50:40 ICT 2024
TID: [-1234] [] [2024-12-31 13:50:10,543]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--QuanLyTaiSanCong:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-31 13:50:10,557]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:3ef1c751-abad-4214-b21b-6aa672be6569; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/quanlytaisancong/GetTaiSanDonVi, Received through API : admin--QuanLyTaiSanCong:v1.0, CORRELATION_ID = 3e17dc43-a73e-4ebf-ab23-c2d80a30232e
TID: [-1234] [] [2024-12-31 13:51:03,298]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/quanlytaisancong/GetTaiSanDonVi, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--QuanLyTaiSanCong:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1124, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 3e17dc43-a73e-4ebf-ab23-c2d80a30232e
TID: [-1234] [] [2024-12-31 13:58:01,094]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--GPLX2:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 405, ERROR_MESSAGE = Method not allowed for given API resource
TID: [-1234] [] [2024-12-31 13:58:58,760]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-31 14:06:46,425]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 30a2657f-f985-4861-998d-9a3e9169cacd
TID: [-1234] [] [2024-12-31 14:06:47,734]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 43044b30-f216-4da3-bce9-c82e987d9c4a
TID: [-1234] [] [2024-12-31 14:06:50,882]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3152349d-1671-439b-9f00-ca471806d471
TID: [-1234] [] [2024-12-31 14:06:51,738]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9d3dc21d-bba6-4cac-aff6-0d92f0dbcd3d
TID: [-1234] [] [2024-12-31 14:06:53,132]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 80925b2f-dd17-4298-958f-e37c3ca251ed
TID: [-1234] [] [2024-12-31 14:06:55,761]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8e586f2e-fd83-4b91-b997-5c940b740b18
TID: [-1234] [] [2024-12-31 14:07:57,774]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0e57e527-ff31-4f1e-b1d0-7d3106109495
TID: [-1234] [] [2024-12-31 14:16:15,682]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong currently SUSPENDED will now be marked active since it processed its last message
TID: [-1234] [] [2024-12-31 14:18:10,559]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-31 14:18:10,560]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong - current suspend duration is : 30000ms - Next retry after : Tue Dec 31 14:18:40 ICT 2024
TID: [-1234] [] [2024-12-31 14:18:10,560]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--QuanLyTaiSanCong:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-31 14:18:10,579]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:45f48af0-1d2e-4a68-8542-b0d692578448; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/quanlytaisancong/GetTaiSanDonVi, Received through API : admin--QuanLyTaiSanCong:v1.0, CORRELATION_ID = 12551c67-0122-49df-911f-fe7aad8647fc
TID: [-1234] [] [2024-12-31 14:18:56,225]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 12551c67-0122-49df-911f-fe7aad8647fc
TID: [-1234] [] [2024-12-31 14:18:56,227]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/quanlytaisancong/GetTaiSanDonVi, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--QuanLyTaiSanCong:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1153, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 12551c67-0122-49df-911f-fe7aad8647fc
TID: [-1234] [] [2024-12-31 14:19:37,197]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f598bc3c-9b54-4ff1-9c02-fc5e21813588
TID: [-1234] [] [2024-12-31 14:19:40,940]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7d7e22d1-882b-44ba-99d4-d902b16de6b8
TID: [-1234] [] [2024-12-31 14:28:58,879]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-31 14:38:53,291]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1d078fff-f2f9-4b88-8bfa-41389d73b013
TID: [-1234] [] [2024-12-31 14:58:59,210]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-31 15:06:55,299]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3f6a2cf8-c6dd-4e8c-8557-356227a9908a
TID: [-1234] [] [2024-12-31 15:07:00,837]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7842f2d6-4100-482b-967e-1cfd6fc88e3c
TID: [-1234] [] [2024-12-31 15:07:02,112]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 76f2df32-85dc-47c6-9cf0-a1034733c68b
TID: [-1234] [] [2024-12-31 15:07:02,183]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = afb7903f-6f81-4ce6-a1bd-b350c3bdc577
TID: [-1234] [] [2024-12-31 15:07:12,721]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 13f51d41-e048-440a-bca5-dc61575cb4a5
TID: [-1234] [] [2024-12-31 15:28:59,517]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-31 15:58:59,794]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-31 16:07:03,105]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 461d342c-2aeb-4ed9-b0c1-9f9a4f23e1a5
TID: [-1234] [] [2024-12-31 16:07:04,183]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 25f5760f-c443-43b1-be4a-59f193ce8674
TID: [-1234] [] [2024-12-31 16:07:10,993]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d4fd94a3-8164-4333-a81b-1eb5056817c2
TID: [-1234] [] [2024-12-31 16:07:20,232]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5e43d340-c2bb-409a-ab56-9dfdf342ac06
TID: [-1234] [] [2024-12-31 16:28:59,962]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-31 16:59:01,799]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-31 17:27:59,330]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7b03fc6e-7246-4ec9-a9da-173097a4892b
TID: [-1234] [] [2024-12-31 17:28:07,339]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 801d1923-280f-4fb6-9eee-fddb27e8a160
TID: [-1234] [] [2024-12-31 17:28:37,965]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0fd6e4d8-5a85-47fd-8e5c-2fff85eb29ea
TID: [-1234] [] [2024-12-31 17:29:09,631]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d556ad8a-b3ca-4610-80c7-d54f48f44485
TID: [-1234] [] [2024-12-31 17:30:48,093]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-31 17:40:07,218]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f15b871f-9005-4ae7-9cd4-c9619806712d
TID: [-1234] [] [2024-12-31 18:00:50,590]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-31 18:01:25,737]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-31 18:01:25,739]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap - current suspend duration is : 30000ms - Next retry after : Tue Dec 31 18:01:55 ICT 2024
TID: [-1234] [] [2024-12-31 18:01:25,740]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-31 18:01:25,753]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:af74337d-f254-444b-8067-57b5a43d0b36; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/lylichtuphap/nhanHoSo, Received through API : admin--LyLichTuPhap:v1.0, CORRELATION_ID = 9247d6a0-bc3e-4f7e-93f1-6670fdd91b3d
TID: [-1234] [] [2024-12-31 18:01:28,401]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-31 18:01:29,348]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-31 18:01:34,601]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-31 18:01:39,650]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-31 18:01:48,024]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-31 18:01:55,488]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-31 18:02:04,653]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap currently SUSPENDED will now be marked active since it processed its last message
TID: [-1234] [] [2024-12-31 18:02:13,136]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/lylichtuphap/nhanHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--LyLichTuPhap:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-1294, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 9247d6a0-bc3e-4f7e-93f1-6670fdd91b3d
TID: [-1234] [] [2024-12-31 18:10:52,966]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0a8760cd-ef88-4c4e-a713-504ebda6c9ce
TID: [-1234] [] [2024-12-31 18:11:00,308]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1b127e89-617a-4ba9-add6-d7eb82c34fca
TID: [-1234] [] [2024-12-31 18:11:08,001]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a10c2b36-5959-4c52-94c1-f20603b1791f
TID: [-1234] [] [2024-12-31 18:11:31,061]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = db70cbc8-3ebb-4ab5-b3dd-190eb33b9ef4
TID: [-1234] [] [2024-12-31 18:11:48,278]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = dbedfe35-0d17-4bac-9cb5-79e60c74ee57
TID: [-1234] [] [2024-12-31 18:11:57,586]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8f19a378-a4da-4bd3-b082-3edb7760de18
TID: [-1234] [] [2024-12-31 18:12:03,775]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2fb004c2-246d-4da6-8521-8c8ac0067adf
TID: [-1234] [] [2024-12-31 18:31:14,608]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-31 19:01:15,033]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-31 19:09:53,181]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9c03dc70-01a8-47af-98b6-af1c2459e213
TID: [-1234] [] [2024-12-31 19:31:15,582]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-31 19:34:51,381]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 37b85daf-d5fc-4a6f-922a-9048b349b91b
TID: [-1234] [] [2024-12-31 19:49:47,163]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 09494e87-3b92-448a-8ddf-a381403a1873
TID: [-1234] [] [2024-12-31 20:01:15,683]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-31 20:06:23,807]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c4d28855-4673-44ed-9718-0caf13d8cba2
TID: [-1234] [] [2024-12-31 20:06:25,767]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c758c854-0da0-47f0-898a-d02bad07401b
TID: [-1234] [] [2024-12-31 20:06:26,532]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9f368c27-4f08-4e1e-92b0-b29c0d6a781d
TID: [-1234] [] [2024-12-31 20:06:26,671]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 833dfa06-3e17-4665-a566-8b75df57e49b
TID: [-1234] [] [2024-12-31 20:06:29,085]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 93fc1c4c-d6a1-48fb-b80b-8baf16308abf
TID: [-1234] [] [2024-12-31 20:06:33,815]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e87e5e7f-5fe2-47ad-a622-f3905e2c810a
TID: [-1234] [] [2024-12-31 20:31:15,868]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-31 20:42:53,954]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241231&denNgay=20241231&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241231&denNgay=20241231&maTthc=
TID: [-1234] [] [2024-12-31 20:42:53,993]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-31 20:45:52,903]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241231&denNgay=20241231&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241231&denNgay=20241231&maTthc=
TID: [-1234] [] [2024-12-31 20:45:52,943]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-31 20:46:04,059]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241231&denNgay=20241231&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241231&denNgay=20241231&maTthc=
TID: [-1234] [] [2024-12-31 20:46:04,102]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-12-31 20:46:10,061]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241231&denNgay=20241231&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241231&denNgay=20241231&maTthc=
TID: [-1234] [] [2024-12-31 20:46:10,099]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-31 20:46:45,815]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241231&denNgay=20241231&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241231&denNgay=20241231&maTthc=
TID: [-1234] [] [2024-12-31 20:46:45,855]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-31 21:01:16,235]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-31 21:06:24,722]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 324e78b1-61c9-41f1-95e5-312b8503caf3
TID: [-1234] [] [2024-12-31 21:06:25,559]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 58ec14cd-b10b-458a-bad6-d41d26476766
TID: [-1234] [] [2024-12-31 21:06:27,174]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c1be1abd-e96c-4a40-b791-3417fcb47d30
TID: [-1234] [] [2024-12-31 21:06:31,643]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ec0ef83d-8ad3-4544-8cb8-fb82f725e380
TID: [-1234] [] [2024-12-31 21:06:31,936]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 97b7b133-1cca-4454-aa2c-d72d44870744
TID: [-1234] [] [2024-12-31 21:06:32,399]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 962f5654-835c-486c-a60d-e6c482b4a641
TID: [-1234] [] [2024-12-31 21:06:40,008]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c2bf2bc1-f251-405d-a8b8-020c0b792272
TID: [-1234] [] [2024-12-31 21:31:16,578]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-31 21:40:18,702]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 221db836-59b5-4b43-8800-feea5ed577ae
TID: [-1234] [] [2024-12-31 22:01:17,076]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-31 22:06:40,518]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 08e62445-e51f-4469-88ea-1a310bdb23d0
TID: [-1234] [] [2024-12-31 22:06:45,979]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a6f4a281-1c74-4db7-9490-0e660da1bc11
TID: [-1234] [] [2024-12-31 22:06:46,888]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2e4c8f05-2d1d-49ac-86c1-881c947eba6a
TID: [-1234] [] [2024-12-31 22:06:49,633]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0ba6f2df-e890-4aee-abaa-643bc5b761ed
TID: [-1234] [] [2024-12-31 22:06:58,421]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ab348e27-e1ce-450c-bdae-f8af5b597fd5
TID: [-1234] [] [2024-12-31 22:31:17,205]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-31 22:57:09,295]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-31 23:01:17,544]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-31 23:05:41,252]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b60d180d-40a4-4dca-860c-b85cbb9088d7
TID: [-1234] [] [2024-12-31 23:05:43,191]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 27b90a07-0e4d-4998-a1fd-ee8113fa7547
TID: [-1234] [] [2024-12-31 23:05:45,613]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 26ae87ec-cefc-4dde-be5b-eb57e50e460e
TID: [-1234] [] [2024-12-31 23:05:47,427]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6c4ccc17-6ebd-48d3-84b0-9dfdcb885e2d
TID: [-1234] [] [2024-12-31 23:05:48,095]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6cdc88b0-8a89-447c-8f9a-4935c5079239
TID: [-1234] [] [2024-12-31 23:05:58,835]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 79bf3c07-1c1e-45df-9e5f-4c36383ed6cb
TID: [-1234] [] [2024-12-31 23:07:00,744]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b7f69522-789a-473d-bd77-d631d5a89570
TID: [-1234] [] [2024-12-31 23:31:17,953]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
