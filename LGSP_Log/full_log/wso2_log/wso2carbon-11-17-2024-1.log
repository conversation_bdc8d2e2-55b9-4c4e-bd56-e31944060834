TID: [-1234] [] [2024-11-17 00:00:01,923]  WARN {org.apache.synapse.commons.util.MiscellaneousUtil} - Error loading properties from a file at from the System defined location: access-log.properties
TID: [-1234] [] [2024-11-17 00:06:20,060]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0d5170b8-3ece-4493-a715-a92e4aa82781
TID: [-1234] [] [2024-11-17 00:06:20,245]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4a485c7c-fb93-446e-9b81-0697ff5bec9f
TID: [-1234] [] [2024-11-17 00:06:22,422]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 916cae9d-9e1f-4ee6-b27b-cbc25701c92a
TID: [-1234] [] [2024-11-17 00:06:23,056]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ed08e4d6-d991-4e7a-a49d-d90ba4f9f1c6
TID: [-1234] [] [2024-11-17 00:06:23,968]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6d8b1906-7d98-4d8c-a63a-51493ecb1ec2
TID: [-1234] [] [2024-11-17 00:06:25,235]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4bb57384-5c97-4331-836a-665578ac45f8
TID: [-1234] [] [2024-11-17 00:06:28,199]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cbc2b18b-4120-44ce-9089-20f7ffe3dff1
TID: [-1234] [] [2024-11-17 00:06:29,946]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0dd49fa8-e128-49b5-8022-cb7d0f4299a5
TID: [-1234] [] [2024-11-17 00:13:31,945]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-17 00:20:30,097]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241117&denNgay=20241117&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241117&denNgay=20241117&maTthc=
TID: [-1234] [] [2024-11-17 00:20:30,139]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-17 00:20:32,313]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241117&denNgay=20241117&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241117&denNgay=20241117&maTthc=
TID: [-1234] [] [2024-11-17 00:20:32,354]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-17 00:23:37,243]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241117&denNgay=20241117&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241117&denNgay=20241117&maTthc=
TID: [-1234] [] [2024-11-17 00:23:37,284]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-17 00:23:40,442]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241117&denNgay=20241117&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241117&denNgay=20241117&maTthc=
TID: [-1234] [] [2024-11-17 00:23:40,481]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-17 00:24:42,326]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241117&denNgay=20241117&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241117&denNgay=20241117&maTthc=
TID: [-1234] [] [2024-11-17 00:24:42,379]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-11-17 00:31:30,561]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241117&denNgay=20241117&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241117&denNgay=20241117&maTthc=
TID: [-1234] [] [2024-11-17 00:31:30,602]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-17 00:42:31,609]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241117&denNgay=20241117&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241117&denNgay=20241117&maTthc=
TID: [-1234] [] [2024-11-17 00:42:31,647]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-17 00:43:32,203]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-17 01:06:33,139]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f27621b0-00dc-41b8-a60f-f66082bd5224
TID: [-1234] [] [2024-11-17 01:06:33,772]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6f72a4c7-e0cf-492a-8d0f-6d5672a211f1
TID: [-1234] [] [2024-11-17 01:06:34,722]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4374715d-0e4c-4a5a-b735-8843d5373a23
TID: [-1234] [] [2024-11-17 01:06:35,135]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 053a5881-50bb-4d4a-afb2-7a850de3aebf
TID: [-1234] [] [2024-11-17 01:06:35,425]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 17285ad7-a643-4d78-b539-c89ccb417864
TID: [-1234] [] [2024-11-17 01:06:40,981]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8662c16c-70be-4189-88e8-2cad6d0d6a50
TID: [-1234] [] [2024-11-17 01:06:42,049]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e5e9741f-76ef-487e-a497-c344b8e6fa4e
TID: [-1234] [] [2024-11-17 01:06:45,966]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 56cab52a-a382-4b1f-929d-f911a2a0a4d9
TID: [-1234] [] [2024-11-17 01:06:46,561]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cb39be7b-72c9-4d13-bd47-d66fe78c2451
TID: [-1234] [] [2024-11-17 01:13:32,340]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-17 01:27:26,910]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241117&denNgay=20241117&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241117&denNgay=20241117&maTthc=
TID: [-1234] [] [2024-11-17 01:27:26,951]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-17 01:27:27,232]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241117&denNgay=20241117&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241117&denNgay=20241117&maTthc=
TID: [-1234] [] [2024-11-17 01:27:27,270]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-17 01:27:27,458]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241117&denNgay=20241117&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241117&denNgay=20241117&maTthc=
TID: [-1234] [] [2024-11-17 01:27:27,467]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241117&denNgay=20241117&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241117&denNgay=20241117&maTthc=
TID: [-1234] [] [2024-11-17 01:27:27,496]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-11-17 01:27:27,503]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-17 01:27:27,531]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241117&denNgay=20241117&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241117&denNgay=20241117&maTthc=
TID: [-1234] [] [2024-11-17 01:27:27,570]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-17 01:43:32,638]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-17 02:07:04,072]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ff3c2bd4-a397-4a5b-96c7-bd0c49b6097b
TID: [-1234] [] [2024-11-17 02:07:05,723]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e304e22b-947a-449d-b5c8-03c668b76a0a
TID: [-1234] [] [2024-11-17 02:07:07,632]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7aa6fb1e-c5d0-4813-b353-b6ff269adbfc
TID: [-1234] [] [2024-11-17 02:07:09,363]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 58032263-f05f-4d48-8b54-133ee3875a9f
TID: [-1234] [] [2024-11-17 02:07:13,196]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d221859c-bdf7-4430-8041-6cf8e6fb853a
TID: [-1234] [] [2024-11-17 02:07:13,925]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cb2f12d8-4395-4c4a-82a3-09e623452ee3
TID: [-1234] [] [2024-11-17 02:13:32,826]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-17 02:34:55,159]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4975e16d-0d22-49a7-a74b-5fbcf219522d
TID: [-1234] [] [2024-11-17 02:38:35,200]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241117&denNgay=20241117&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241117&denNgay=20241117&maTthc=
TID: [-1234] [] [2024-11-17 02:38:35,239]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-11-17 02:43:33,087]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-17 03:03:55,476] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2024-11-17 03:03:56,285] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2024-11-17 03:06:13,984]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d66c9bf3-b99b-4d54-be58-528184f0b2ae
TID: [-1234] [] [2024-11-17 03:06:15,001]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 17bbecd3-0500-4867-8981-e1c7545865fb
TID: [-1234] [] [2024-11-17 03:06:15,241]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 983db996-63a7-4e49-958e-05720da07fa5
TID: [-1234] [] [2024-11-17 03:06:16,093]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 83ae069b-dc58-4da0-8064-97fcc7e4612a
TID: [-1234] [] [2024-11-17 03:06:18,731]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3ebdc088-27e3-4399-9248-24f17ea3ee39
TID: [-1234] [] [2024-11-17 03:06:18,796]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4e900702-cbfb-4c95-936a-d4fe007781f3
TID: [-1234] [] [2024-11-17 03:06:23,596]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 79458fdd-b2db-4487-bed6-00b0429766cd
TID: [-1234] [] [2024-11-17 03:06:23,928]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9f6f6f13-09d6-4833-9bfc-0318cfcb548a
TID: [-1234] [] [2024-11-17 03:29:05,070]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-17 03:59:05,398]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-17 04:06:47,368]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a487c68b-038e-4d70-ad45-c3c126575e46
TID: [-1234] [] [2024-11-17 04:06:48,095]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9c6ef6d8-7cc5-457c-bb9d-54872dca1147
TID: [-1234] [] [2024-11-17 04:06:48,885]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b9913607-feff-4de9-a7a3-bcc09014ad75
TID: [-1234] [] [2024-11-17 04:06:56,154]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9438c86b-b6b1-4689-aa42-5cc24ffca18c
TID: [-1234] [] [2024-11-17 04:06:59,793]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 65dfa04a-4adb-4825-9124-82a40a93599c
TID: [-1234] [] [2024-11-17 04:07:00,370]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ff7a0753-8d1e-4203-9b90-115d23ad44bf
TID: [-1234] [] [2024-11-17 04:29:05,579]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-17 04:59:05,906]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-17 05:06:56,506]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 862406ac-658b-4f5b-b589-b185ae9a762f
TID: [-1234] [] [2024-11-17 05:06:57,718]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ab88c735-9d59-4022-9da6-74b24641fbc0
TID: [-1234] [] [2024-11-17 05:07:02,604]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4d294c09-41a9-42e2-be47-5005cffee0c5
TID: [-1234] [] [2024-11-17 05:07:03,956]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e5ec558f-be84-4e09-878a-83bdd887a018
TID: [-1234] [] [2024-11-17 05:07:05,506]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = af06b281-d3ab-4d23-a04c-544f96838776
TID: [-1234] [] [2024-11-17 05:07:10,772]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3d20fd96-ff72-45fa-a57e-f51b2032c17d
TID: [-1234] [] [2024-11-17 05:07:12,468]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2174ac70-f270-48d5-911c-144eb4079ff5
TID: [-1234] [] [2024-11-17 05:29:06,071]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-17 05:59:06,554]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-17 06:06:14,042]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4c9add7f-fdeb-4424-b9e1-d72ab1fa1815
TID: [-1234] [] [2024-11-17 06:06:17,764]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1c5433d4-acdb-4169-8e14-e68ffbba122d
TID: [-1234] [] [2024-11-17 06:06:18,803]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6323e448-85dd-4aa0-8658-e78cd2708610
TID: [-1234] [] [2024-11-17 06:06:28,706]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b5be0515-d631-4a54-9432-94255fd0d0dc
TID: [-1234] [] [2024-11-17 06:06:31,751]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 60101869-1a69-4e51-8918-f88dae99badc
TID: [-1234] [] [2024-11-17 06:06:33,796]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5fc80f05-e04b-41d6-9238-275a45b3a38b
TID: [-1234] [] [2024-11-17 06:29:06,875]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-17 06:34:52,742]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6a5cb2e6-9711-490f-bf98-adc1f4794461
TID: [-1234] [] [2024-11-17 06:59:07,023]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-17 07:06:35,504]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f71c4ee6-5df4-4fb4-91a9-26ab37f90a30
TID: [-1234] [] [2024-11-17 07:06:35,932]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a072e3cc-4ce5-477e-9a3e-1b47323f23ab
TID: [-1234] [] [2024-11-17 07:06:37,013]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 56af6bee-73f8-4f54-a142-fcd74c93070f
TID: [-1234] [] [2024-11-17 07:06:38,200]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b41e5cd3-ecaf-4ddb-9cf2-8a6579ed1996
TID: [-1234] [] [2024-11-17 07:06:39,357]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9ee8ff7a-2371-4d84-9a77-dc9cc20ce10e
TID: [-1234] [] [2024-11-17 07:06:40,752]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1e87df68-6c5d-401c-824e-3595698193e6
TID: [-1234] [] [2024-11-17 07:06:45,668]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a47887e1-3e47-48ec-93ba-848488e0d8d7
TID: [-1234] [] [2024-11-17 07:06:47,837]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7a1b1983-b169-406e-b4ce-b05ccdf7821a
TID: [-1234] [] [2024-11-17 07:31:37,658]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-17 07:41:11,457]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241117&denNgay=20241117&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241117&denNgay=20241117&maTthc=
TID: [-1234] [] [2024-11-17 07:41:11,496]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-17 08:01:38,243]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-17 08:06:54,940]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6a3a2e8b-ede4-4e36-ad72-a71c1abb8a07
TID: [-1234] [] [2024-11-17 08:06:56,325]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1d1dcf79-fb1b-45d4-8331-91fab106f3ba
TID: [-1234] [] [2024-11-17 08:06:56,492]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b3464dc0-9441-4d8c-82ce-268ea9e49630
TID: [-1234] [] [2024-11-17 08:06:57,356]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a9c6dd45-32c7-4045-9eaf-9098d9d83eb8
TID: [-1234] [] [2024-11-17 08:07:03,180]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6f22b935-33bb-41d9-a2c7-2341f9259613
TID: [-1234] [] [2024-11-17 08:07:05,789]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c065ed12-616e-4630-813b-812177089dd6
TID: [-1234] [] [2024-11-17 08:07:07,349]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c650c12d-7205-4185-9a15-abb4e58366ff
TID: [-1234] [] [2024-11-17 08:31:38,616]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-17 08:57:59,141]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 09a1a7b2-20de-415b-a037-3b08f46a7535
TID: [-1234] [] [2024-11-17 09:01:39,318]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-17 09:07:14,043]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b9a3a51b-848c-4caa-bd19-370817c0870a
TID: [-1234] [] [2024-11-17 09:07:14,320]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c92bb724-8104-4554-89f2-37f7dd319e5d
TID: [-1234] [] [2024-11-17 09:07:14,368]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 843b5b6e-5cfd-458f-b36e-617b616e7a36
TID: [-1234] [] [2024-11-17 09:07:21,385]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1ffc62b0-54ea-4ce0-b6aa-b9c0baa5eab9
TID: [-1234] [] [2024-11-17 09:07:23,755]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b10accd5-c4d0-4db3-b0f1-ba99fb7f7f18
TID: [-1234] [] [2024-11-17 09:31:40,738]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-17 09:43:48,138]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241117&denNgay=20241117&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241117&denNgay=20241117&maTthc=
TID: [-1234] [] [2024-11-17 09:43:48,181]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-17 09:45:35,647]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241117&denNgay=20241117&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241117&denNgay=20241117&maTthc=
TID: [-1234] [] [2024-11-17 09:45:35,685]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-17 10:01:41,234]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-17 10:06:17,000]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9ecb9825-ca01-4b64-bd2f-e221d9d30f1e
TID: [-1234] [] [2024-11-17 10:06:18,809]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 89461402-ec28-407c-991c-3a916c54d00a
TID: [-1234] [] [2024-11-17 10:06:20,181]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4a0df964-3dbe-46fe-87dd-dbff73bbb081
TID: [-1234] [] [2024-11-17 10:06:20,505]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8585cd55-0f1a-4b84-bb6d-135f01ad3906
TID: [-1234] [] [2024-11-17 10:06:21,312]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e3d7117d-6b74-4c7e-8205-2bad7c9c6a6e
TID: [-1234] [] [2024-11-17 10:06:22,170]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9bb1efe2-f15c-446c-b5ed-90b055e18805
TID: [-1234] [] [2024-11-17 10:06:25,330]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b3254e1f-2838-4a40-96e7-0fbcb9a4d246
TID: [-1234] [] [2024-11-17 10:06:28,351]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a7bf29b8-769c-4a5a-94e9-c18cd709570a
TID: [-1234] [] [2024-11-17 10:06:29,830]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5d8a83b2-c786-446a-8b85-03095a0cdfea
TID: [-1234] [] [2024-11-17 10:31:41,802]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-17 10:34:54,426]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = afe1dfa7-3d0e-4352-941f-72cd0ef37700
TID: [-1234] [] [2024-11-17 11:01:42,591]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-17 11:06:34,673]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9ac32053-65c1-40c9-87b9-33d18a934853
TID: [-1234] [] [2024-11-17 11:06:36,075]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b879f776-7a93-4c34-bf07-46b447ac0469
TID: [-1234] [] [2024-11-17 11:06:37,494]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 53967e87-30ca-4a3a-b885-b4d92a1246e3
TID: [-1234] [] [2024-11-17 11:06:38,541]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b0b6fa20-c5c4-48ff-a194-d387687f494d
TID: [-1234] [] [2024-11-17 11:06:39,325]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ae4f8be0-6007-4497-bb15-acae02eccadb
TID: [-1234] [] [2024-11-17 11:06:39,545]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5c76506b-93e6-453f-ab62-e1810e316136
TID: [-1234] [] [2024-11-17 11:06:45,863]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b28da03b-435e-484f-bf4a-1306a35e4935
TID: [-1234] [] [2024-11-17 11:06:46,957]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5153f3f1-c037-4d7c-8e4a-17e84beed7f3
TID: [-1234] [] [2024-11-17 11:31:42,893]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-17 11:51:25,988]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241117&denNgay=20241117&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241117&denNgay=20241117&maTthc=
TID: [-1234] [] [2024-11-17 11:51:26,034]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-17 12:01:43,023]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-17 12:06:47,926]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 45961134-b2a5-4d91-b7be-2ec401c36d98
TID: [-1234] [] [2024-11-17 12:06:50,197]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7d10b16a-b9e2-429e-9ab1-14914126f92b
TID: [-1234] [] [2024-11-17 12:06:55,252]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ac7a2fdb-0cbd-451b-92d7-c3763edf3192
TID: [-1234] [] [2024-11-17 12:07:00,327]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9436201d-1e6c-4ce7-bf5f-42c3b9e4ab94
TID: [-1234] [] [2024-11-17 12:07:00,772]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b6c65055-3e6b-4db1-82ff-fbfe4cb29ced
TID: [-1234] [] [2024-11-17 12:25:24,609]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241117&denNgay=20241117&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241117&denNgay=20241117&maTthc=
TID: [-1234] [] [2024-11-17 12:25:24,768]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-17 12:31:46,528]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-17 13:01:46,683]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-17 13:07:05,327]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 345901ab-c817-4220-afc3-20ae062e85a4
TID: [-1234] [] [2024-11-17 13:07:05,947]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d03af9c2-3439-41b2-99aa-d2496564f0ab
TID: [-1234] [] [2024-11-17 13:07:06,974]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 88dcae5e-4702-4794-8c92-5425687f31d3
TID: [-1234] [] [2024-11-17 13:07:07,836]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7c72c09f-0559-4fdc-96be-493b6767e2b0
TID: [-1234] [] [2024-11-17 13:07:12,548]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 463d3de0-301d-4033-8c7f-c0a5cbaa67fb
TID: [-1234] [] [2024-11-17 13:07:17,018]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 00e25621-3315-49f1-928a-4243f870b8f2
TID: [-1234] [] [2024-11-17 13:07:17,458]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f2a27824-140e-46a2-a26c-7659384e5271
TID: [-1234] [] [2024-11-17 13:31:46,844]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-17 14:01:47,234]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-17 14:06:46,840]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8b65888f-320c-4a53-873c-dd2b181d9bcb
TID: [-1234] [] [2024-11-17 14:06:52,320]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3a286af4-8847-467f-bf14-f37a78c8d6d8
TID: [-1234] [] [2024-11-17 14:06:53,750]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2eab9362-18e9-4f97-bed1-63e3e0aad0f7
TID: [-1234] [] [2024-11-17 14:06:55,801]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 31fcc836-09b1-41f1-8337-0201ddbec135
TID: [-1234] [] [2024-11-17 14:07:00,611]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = aadfde21-5843-4135-b64c-c587b4001f6e
TID: [-1234] [] [2024-11-17 14:07:03,567]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3f63bb23-c293-4d4c-89f5-7c63ec43cf60
TID: [-1234] [] [2024-11-17 14:07:04,848]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3451373b-f001-494d-8c48-5d577538e4e6
TID: [-1234] [] [2024-11-17 14:31:47,483]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-17 15:01:48,979]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-17 15:07:06,865]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f23ffa78-d648-4284-ab98-be804746dbf3
TID: [-1234] [] [2024-11-17 15:07:13,961]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a01fb6f8-bfed-4078-b1e1-5502da5ba44e
TID: [-1234] [] [2024-11-17 15:07:15,633]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 62603f51-5eec-402a-9615-0f0873ac9a18
TID: [-1234] [] [2024-11-17 15:07:20,541]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 257e7e89-be3c-45f7-acc5-f8bfeb644e19
TID: [-1234] [] [2024-11-17 15:07:23,454]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 98589550-46da-4742-bd56-152127833dec
TID: [-1234] [] [2024-11-17 15:07:24,783]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a2aeaf63-7b85-441f-83af-3162b50d8497
TID: [-1234] [] [2024-11-17 15:31:49,230]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-17 16:01:49,938]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-17 16:06:21,989]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c74e99a9-a21d-4a18-a880-6b91a15dcd27
TID: [-1234] [] [2024-11-17 16:06:26,493]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ecf68777-f00b-4fb9-9816-84745206b017
TID: [-1234] [] [2024-11-17 16:06:27,505]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 393b48ea-26e9-4a40-b180-79569e6031ed
TID: [-1234] [] [2024-11-17 16:06:32,620]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f4cb3c48-fd73-43d4-b755-1370371db0ba
TID: [-1234] [] [2024-11-17 16:06:37,993]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 13b05433-0046-4e01-8506-e9b682f31683
TID: [-1234] [] [2024-11-17 16:06:41,716]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f0378431-5a7c-4496-834a-03a43cff85ce
TID: [-1234] [] [2024-11-17 16:06:43,777]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0d65b39e-fa55-4c39-921c-0a499190d20f
TID: [-1234] [] [2024-11-17 16:12:43,433] ERROR {org.apache.catalina.core.ContainerBase.[Catalina].[localhost].[/].[bridgeservlet]} - Servlet.service() for servlet [bridgeservlet] in context with path [/] threw exception java.lang.IllegalArgumentException: An invalid character [59] was present in the Cookie value
	at org.apache.tomcat.util.http.Rfc6265CookieProcessor.validateCookieValue(Rfc6265CookieProcessor.java:197)
	at org.apache.tomcat.util.http.Rfc6265CookieProcessor.generateHeader(Rfc6265CookieProcessor.java:123)
	at org.apache.catalina.connector.Response.generateCookieString(Response.java:1003)
	at org.apache.catalina.connector.Response.addCookie(Response.java:955)
	at org.apache.catalina.connector.ResponseFacade.addCookie(ResponseFacade.java:385)
	at org.wso2.carbon.ui.CarbonUILoginUtil.saveOriginalUrl(CarbonUILoginUtil.java:125)
	at org.wso2.carbon.ui.CarbonSecuredHttpContext.handleSecurity(CarbonSecuredHttpContext.java:275)
	at org.eclipse.equinox.http.servlet.internal.ServletRegistration.service(ServletRegistration.java:60)
	at org.eclipse.equinox.http.servlet.internal.ProxyServlet.processAlias(ProxyServlet.java:128)
	at org.eclipse.equinox.http.servlet.internal.ProxyServlet.service(ProxyServlet.java:76)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779)
	at org.wso2.carbon.tomcat.ext.servlet.DelegationServlet.service(DelegationServlet.java:68)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.owasp.csrfguard.CsrfGuardFilter.doFilter(CsrfGuardFilter.java:72)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.wso2.carbon.tomcat.ext.filter.CharacterSetFilter.doFilter(CharacterSetFilter.java:65)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.filters.HttpHeaderSecurityFilter.doFilter(HttpHeaderSecurityFilter.java:126)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:177)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.wso2.carbon.identity.context.rewrite.valve.TenantContextRewriteValve.invoke(TenantContextRewriteValve.java:86)
	at org.wso2.carbon.identity.authz.valve.AuthorizationValve.invoke(AuthorizationValve.java:110)
	at org.wso2.carbon.identity.auth.valve.AuthenticationValve.invoke(AuthenticationValve.java:111)
	at org.wso2.carbon.tomcat.ext.valves.CompositeValve.continueInvocation(CompositeValve.java:101)
	at org.wso2.carbon.tomcat.ext.valves.TomcatValveContainer.invokeValves(TomcatValveContainer.java:49)
	at org.wso2.carbon.tomcat.ext.valves.CompositeValve.invoke(CompositeValve.java:62)
	at org.wso2.carbon.tomcat.ext.valves.CarbonStuckThreadDetectionValve.invoke(CarbonStuckThreadDetectionValve.java:145)
	at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:687)
	at org.wso2.carbon.tomcat.ext.valves.CarbonContextCreatorValve.invoke(CarbonContextCreatorValve.java:59)
	at org.wso2.carbon.tomcat.ext.valves.RequestCorrelationIdValve.invoke(RequestCorrelationIdValve.java:124)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:891)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1784)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2024-11-17 16:31:50,052]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-17 16:32:41,753]  INFO {org.wso2.carbon.identity.application.authentication.framework.store.SessionCleanUpService} - Session Data cleanup task is running successfully for removing expired Data
TID: [-1234] [] [2024-11-17 17:01:50,254]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-17 17:06:52,482]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 34adaa31-0bec-400a-bd3e-fe271938608e
TID: [-1234] [] [2024-11-17 17:06:57,100]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1c29166e-5202-4d7e-b672-bdce1bc202ef
TID: [-1234] [] [2024-11-17 17:06:59,699]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9b452e14-0ca6-435d-a178-5b1a2107b86d
TID: [-1234] [] [2024-11-17 17:07:04,042]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = afd02244-dca5-4c75-bace-b866c0eb13ad
TID: [-1234] [] [2024-11-17 17:07:07,071]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d8687730-70fb-4317-a024-e7bc4be1b054
TID: [-1234] [] [2024-11-17 17:07:08,381]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 06bdc043-b5a4-4c07-9b8a-988293433f0b
TID: [-1234] [] [2024-11-17 17:43:31,007]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-17 18:07:16,955]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 96dc8d23-a2f1-4000-a6bc-05e1fffe5e91
TID: [-1234] [] [2024-11-17 18:07:18,677]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f3b49ea3-5bbe-4b02-9d00-42dbec6bcd96
TID: [-1234] [] [2024-11-17 18:07:20,596]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 66cc4c9f-6072-462d-967c-12f5ebf0de4f
TID: [-1234] [] [2024-11-17 18:07:22,858]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5eb4c60e-5615-4f1a-8e60-5596629f984e
TID: [-1234] [] [2024-11-17 18:07:27,582]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6da6c6de-2697-49c2-a4ae-5cbd21a7f3d9
TID: [-1234] [] [2024-11-17 18:07:30,459]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 483da2b6-7108-4a56-8d62-dab19115f4eb
TID: [-1234] [] [2024-11-17 18:07:32,986]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e2780fe4-03bd-476b-87c5-58a9f5f22337
TID: [-1234] [] [2024-11-17 18:13:31,323]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-17 18:43:31,692]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-17 18:46:11,142]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241117&denNgay=20241117&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241117&denNgay=20241117&maTthc=
TID: [-1234] [] [2024-11-17 18:46:11,181]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-17 19:01:34,846]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241117&denNgay=20241117&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241117&denNgay=20241117&maTthc=
TID: [-1234] [] [2024-11-17 19:01:35,305]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-17 19:01:54,245] ERROR {org.apache.catalina.core.ContainerBase.[Catalina].[localhost].[/].[bridgeservlet]} - Servlet.service() for servlet [bridgeservlet] in context with path [/] threw exception java.lang.IllegalArgumentException: An invalid character [59] was present in the Cookie value
	at org.apache.tomcat.util.http.Rfc6265CookieProcessor.validateCookieValue(Rfc6265CookieProcessor.java:197)
	at org.apache.tomcat.util.http.Rfc6265CookieProcessor.generateHeader(Rfc6265CookieProcessor.java:123)
	at org.apache.catalina.connector.Response.generateCookieString(Response.java:1003)
	at org.apache.catalina.connector.Response.addCookie(Response.java:955)
	at org.apache.catalina.connector.ResponseFacade.addCookie(ResponseFacade.java:385)
	at org.wso2.carbon.ui.CarbonUILoginUtil.saveOriginalUrl(CarbonUILoginUtil.java:125)
	at org.wso2.carbon.ui.CarbonSecuredHttpContext.handleSecurity(CarbonSecuredHttpContext.java:275)
	at org.eclipse.equinox.http.servlet.internal.ServletRegistration.service(ServletRegistration.java:60)
	at org.eclipse.equinox.http.servlet.internal.ProxyServlet.processAlias(ProxyServlet.java:128)
	at org.eclipse.equinox.http.servlet.internal.ProxyServlet.service(ProxyServlet.java:76)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779)
	at org.wso2.carbon.tomcat.ext.servlet.DelegationServlet.service(DelegationServlet.java:68)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.owasp.csrfguard.CsrfGuardFilter.doFilter(CsrfGuardFilter.java:72)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.wso2.carbon.tomcat.ext.filter.CharacterSetFilter.doFilter(CharacterSetFilter.java:65)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.wso2.carbon.ui.filters.cache.URLBasedCachePreventionFilter.doFilter(URLBasedCachePreventionFilter.java:57)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.filters.HttpHeaderSecurityFilter.doFilter(HttpHeaderSecurityFilter.java:126)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:177)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.wso2.carbon.identity.context.rewrite.valve.TenantContextRewriteValve.invoke(TenantContextRewriteValve.java:86)
	at org.wso2.carbon.identity.authz.valve.AuthorizationValve.invoke(AuthorizationValve.java:110)
	at org.wso2.carbon.identity.auth.valve.AuthenticationValve.invoke(AuthenticationValve.java:111)
	at org.wso2.carbon.tomcat.ext.valves.CompositeValve.continueInvocation(CompositeValve.java:101)
	at org.wso2.carbon.tomcat.ext.valves.TomcatValveContainer.invokeValves(TomcatValveContainer.java:49)
	at org.wso2.carbon.tomcat.ext.valves.CompositeValve.invoke(CompositeValve.java:62)
	at org.wso2.carbon.tomcat.ext.valves.CarbonStuckThreadDetectionValve.invoke(CarbonStuckThreadDetectionValve.java:145)
	at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:687)
	at org.wso2.carbon.tomcat.ext.valves.CarbonContextCreatorValve.invoke(CarbonContextCreatorValve.java:59)
	at org.wso2.carbon.tomcat.ext.valves.RequestCorrelationIdValve.invoke(RequestCorrelationIdValve.java:124)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:891)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1784)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2024-11-17 19:06:39,147]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 35b3424e-054e-4b41-b60a-b3413dec6b2f
TID: [-1234] [] [2024-11-17 19:06:40,018]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6c7eb380-208c-483f-bcb4-87e58e9fc844
TID: [-1234] [] [2024-11-17 19:06:40,668]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 050d1757-9a38-4e51-8a0a-634144fe30a9
TID: [-1234] [] [2024-11-17 19:06:40,819]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d6549181-ae1e-4e02-9b08-26146390e842
TID: [-1234] [] [2024-11-17 19:06:46,056]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3a2885f4-237f-4931-9b46-014dd045b4d4
TID: [-1234] [] [2024-11-17 19:06:46,408]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9a3b47ee-1810-469f-9fc3-fa46efdbdd55
TID: [-1234] [] [2024-11-17 19:06:46,561]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0d7feec7-d646-4506-93c6-198c66bcb4ba
TID: [-1234] [] [2024-11-17 19:06:46,943]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2b307b5c-050d-492f-ae4b-84f888284098
TID: [-1234] [] [2024-11-17 19:06:51,446]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e59a69ea-e156-49b0-a106-9157679d6b96
TID: [-1234] [] [2024-11-17 19:06:51,701]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6b84a9e1-b214-4b6d-8c04-b2c8c0ae2610
TID: [-1234] [] [2024-11-17 19:13:31,817]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-17 19:43:31,989]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-17 20:07:06,299]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 308a8302-dab2-409f-9d33-64d286fa300d
TID: [-1234] [] [2024-11-17 20:07:11,442]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b00fe287-1c86-4ae6-8b7c-c6facea5e24b
TID: [-1234] [] [2024-11-17 20:07:18,135]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 124473a1-56be-4569-a1c2-2a179b3108cd
TID: [-1234] [] [2024-11-17 20:07:21,383]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3e735f6f-0718-4707-9390-71feac084b59
TID: [-1234] [] [2024-11-17 20:07:22,238]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 200492f6-5d6f-4d7b-a6af-e1da3835d20f
TID: [-1234] [] [2024-11-17 20:13:32,241]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-17 20:43:32,357]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-17 21:06:16,740]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d5a5ebb3-8f30-4498-8238-baf5432d8e1e
TID: [-1234] [] [2024-11-17 21:06:18,605]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b34ffd7e-4dae-4391-a745-56a0d13b7d5d
TID: [-1234] [] [2024-11-17 21:06:20,136]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bb78f5fd-65d1-498b-bb79-b036882952da
TID: [-1234] [] [2024-11-17 21:06:20,518]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = eef5b09c-5ba5-4ec8-8abe-50826b267989
TID: [-1234] [] [2024-11-17 21:06:21,796]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 79d3f6e9-c013-4bd6-85c2-25f7708d0afc
TID: [-1234] [] [2024-11-17 21:06:22,266]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 905ac353-ebf1-4a04-b841-5842a50b02a4
TID: [-1234] [] [2024-11-17 21:06:23,640]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 08e5e1ab-96c8-468f-bfdb-278923c772b7
TID: [-1234] [] [2024-11-17 21:06:31,515]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 78870ab3-2e97-4f03-957b-51c1b7fc6563
TID: [-1234] [] [2024-11-17 21:06:32,503]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bec39a2d-6a52-412c-a443-491022e876ad
TID: [-1234] [] [2024-11-17 21:13:32,552]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-17 21:13:47,360]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241117&denNgay=20241117&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241117&denNgay=20241117&maTthc=
TID: [-1234] [] [2024-11-17 21:13:47,401]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-11-17 21:34:58,064]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8c9c6603-ceb2-4578-b851-e686b89c2141
TID: [-1234] [] [2024-11-17 21:43:32,911]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-17 22:06:06,367]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fe8d367d-a2a6-426a-81bd-5e248156ca0a
TID: [-1234] [] [2024-11-17 22:06:06,764]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9a071bbf-6903-4d0b-8c2c-68fd84629c34
TID: [-1234] [] [2024-11-17 22:06:07,604]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d43d854a-5cda-4ce7-8078-b9eee53024d6
TID: [-1234] [] [2024-11-17 22:06:09,785]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f85729b7-c734-435a-9267-a36d810665ce
TID: [-1234] [] [2024-11-17 22:06:10,505]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2eded581-0dd7-4e7d-b5c6-af8dd7653131
TID: [-1234] [] [2024-11-17 22:06:11,428]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 69ea5e59-9f6e-40ed-abdf-aa1b65a62c23
TID: [-1234] [] [2024-11-17 22:06:11,672]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e3cb2807-1b34-45af-bce8-10fcec98d25d
TID: [-1234] [] [2024-11-17 22:06:14,178]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 42b8d6f0-3c6d-47b5-96c8-ee6b864ced6f
TID: [-1234] [] [2024-11-17 22:06:15,940]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6bf9c19e-a2c9-4b3d-968f-28ad7303a702
TID: [-1234] [] [2024-11-17 22:19:34,499]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-17 22:34:53,144]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ba03d817-55d3-43da-a002-a2cf755c5559
TID: [-1234] [] [2024-11-17 22:58:37,726]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-17 23:06:27,262]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ed3291ee-28a5-49ab-8a57-5e747b03e19d
TID: [-1234] [] [2024-11-17 23:06:29,076]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e0930414-8e53-4e18-ac68-abfc9fb79705
TID: [-1234] [] [2024-11-17 23:06:30,615]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 31eb518f-970f-4a85-bb58-b67d32be8334
TID: [-1234] [] [2024-11-17 23:06:32,529]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 13203d73-5487-4164-8177-5830987b7dc7
TID: [-1234] [] [2024-11-17 23:06:33,296]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ebe8ce2a-0b07-4508-9d68-7f6a9cbae197
TID: [-1234] [] [2024-11-17 23:06:36,227]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 49c3569c-bb57-484b-93d9-6f7ce4dfc494
TID: [-1234] [] [2024-11-17 23:06:38,226]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1bdb820b-b804-4bb5-94b1-a3f3c9920928
TID: [-1234] [] [2024-11-17 23:29:05,439]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-11-17 23:59:06,067]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
