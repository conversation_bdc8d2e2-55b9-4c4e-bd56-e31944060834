TID: [-1234] [] [2024-12-27 00:00:13,535]  WARN {org.apache.synapse.commons.util.MiscellaneousUtil} - Error loading properties from a file at from the System defined location: access-log.properties
TID: [-1234] [] [2024-12-27 00:01:13,698]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-27 00:05:33,481]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6e444a58-08d7-45b3-9458-71c98b15c246
TID: [-1234] [] [2024-12-27 00:05:35,687]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = efd5a958-aad0-422e-8779-38033bb26908
TID: [-1234] [] [2024-12-27 00:05:38,042]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d15f8e2b-ba49-49b9-b622-e15f8604f2bf
TID: [-1234] [] [2024-12-27 00:05:40,525]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ebbf53c3-f8fa-438f-adbd-e0942f69a389
TID: [-1234] [] [2024-12-27 00:05:41,463]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a4f035c1-22fb-4b36-a67b-5607e45bd19a
TID: [-1234] [] [2024-12-27 00:05:45,127]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 17196ba4-a9ab-4b10-ba27-d576c2ca61d0
TID: [-1234] [] [2024-12-27 00:06:46,990]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0604b0a1-5488-4aab-adaa-f162a16731d2
TID: [-1234] [] [2024-12-27 00:31:14,220]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-27 01:01:14,399]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-27 01:07:12,772]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b57b5406-2d21-4d14-b252-b55918fb3acc
TID: [-1234] [] [2024-12-27 01:07:12,776]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a807276c-d952-4040-85ae-920579f088d1
TID: [-1234] [] [2024-12-27 01:07:14,404]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ea680250-bf8b-41c8-ae78-bd6e81e06a6e
TID: [-1234] [] [2024-12-27 01:07:14,549]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3255151a-c4ef-4455-8ddb-d23d23163e15
TID: [-1234] [] [2024-12-27 01:07:15,309]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f1766563-1898-4d18-bf16-ffeabffa9ceb
TID: [-1234] [] [2024-12-27 01:07:16,178]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2dd724fb-1ae5-40b8-9ab6-79041a015565
TID: [-1234] [] [2024-12-27 01:07:18,006]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d128f0be-9bd1-4fca-ba22-696960333ec3
TID: [-1234] [] [2024-12-27 01:31:15,284]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-27 02:01:16,777]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-27 02:06:23,649]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d148b0ce-367b-42b0-ac61-03854bdbe699
TID: [-1234] [] [2024-12-27 02:06:26,012]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 19771b85-a00a-42ba-a4b7-b0f13d668d48
TID: [-1234] [] [2024-12-27 02:06:26,387]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a6bf8a03-3f21-4e92-a59f-8c734ebaadfe
TID: [-1234] [] [2024-12-27 02:06:28,474]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e299dc4f-3867-41c4-b673-3a237f91e293
TID: [-1234] [] [2024-12-27 02:06:37,179]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3226c178-9c9c-4e84-ad0c-a68191a4ba8a
TID: [-1234] [] [2024-12-27 02:06:37,836]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 594f1806-23b7-43c4-aa65-90ad71b7632b
TID: [-1234] [] [2024-12-27 02:07:41,255]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7742d181-e564-43b4-8375-c6dc3423d2cb
TID: [-1234] [] [2024-12-27 02:07:41,265]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6e5a078f-3e02-4d6b-a0a3-980d65165cc0
TID: [-1234] [] [2024-12-27 02:31:17,082]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-27 03:01:18,245]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-27 03:06:43,371]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 73435dd0-c4bb-43c1-8bac-ef28921b13ff
TID: [-1234] [] [2024-12-27 03:06:44,150]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ae29daa8-c1b2-4047-b393-25ccafefca21
TID: [-1234] [] [2024-12-27 03:06:45,360]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bcc7a7c1-b5dc-4c22-9af1-707ea58d3ffa
TID: [-1234] [] [2024-12-27 03:06:46,612]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 351ff390-d6c5-4cbc-9904-6db6d4703701
TID: [-1234] [] [2024-12-27 03:06:49,019]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7b90b1f0-914a-4b47-bfec-3b0931ce45ab
TID: [-1234] [] [2024-12-27 03:06:52,805]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b59a6e67-e4a0-4c69-b588-64664e5260f8
TID: [-1234] [] [2024-12-27 03:07:54,808]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 33eaf17a-9459-48d9-8cac-8098cdb868dd
TID: [-1234] [] [2024-12-27 03:31:18,582]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-27 04:01:18,784]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-27 04:05:42,567]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bd5cb1b1-462c-42e6-ace8-cc484c7e4c9b
TID: [-1234] [] [2024-12-27 04:05:43,309]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8e70f14d-d9bc-493d-869d-11cb161d4597
TID: [-1234] [] [2024-12-27 04:05:49,131]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3540e11c-9dad-4c0b-9dd0-92f69a1c0912
TID: [-1234] [] [2024-12-27 04:05:49,440]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 618071eb-6081-498c-b820-219703ca955c
TID: [-1234] [] [2024-12-27 04:31:19,506]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-27 05:01:19,669]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-27 05:05:40,990]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 09bcb909-8997-4848-a590-2e540a81399a
TID: [-1234] [] [2024-12-27 05:05:45,689]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 116496fc-6e34-4b7f-be46-027c7e2f35b8
TID: [-1234] [] [2024-12-27 05:05:46,786]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 18444623-ba28-4c8f-9081-42853afd1cfe
TID: [-1234] [] [2024-12-27 05:05:47,301]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 576ee66e-a58a-42da-a5d3-c89d3b1317a5
TID: [-1234] [] [2024-12-27 05:05:55,523]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3a348b79-9711-4fa0-8a0e-10904803b1f0
TID: [-1234] [] [2024-12-27 05:31:20,032]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-27 05:34:35,376]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e90ebb6e-724a-4b7f-8abb-18fa06f7ae0d
TID: [-1234] [] [2024-12-27 06:01:21,566]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-27 06:05:37,958]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b036fd79-1836-4bfb-b666-092266c268db
TID: [-1234] [] [2024-12-27 06:05:39,341]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 893ceed2-7e11-41b3-9319-5d1e80587f4d
TID: [-1234] [] [2024-12-27 06:05:43,067]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = aa1a2027-22d3-46c8-9270-1d5b647fc6f2
TID: [-1234] [] [2024-12-27 06:05:44,853]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f999ea4a-2884-4baa-9c9a-5d6736161fee
TID: [-1234] [] [2024-12-27 06:05:54,810]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d021218f-eb4f-42fe-8bf4-43964f1b26a8
TID: [-1234] [] [2024-12-27 06:31:22,310]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-27 07:01:22,531]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-27 07:04:59,089]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e847df19-bd3c-480a-b16f-f54b1a44c149
TID: [-1234] [] [2024-12-27 07:04:59,418]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e8f7a5ab-6aa7-4e0c-b146-8aae5c75c07d
TID: [-1234] [] [2024-12-27 07:04:59,652]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4166b992-c597-4590-8fbc-143806350083
TID: [-1234] [] [2024-12-27 07:05:03,069]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 26005d59-bb11-4bde-adf2-18bb3f44fa19
TID: [-1234] [] [2024-12-27 07:05:04,081]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f43fbebd-de10-450d-878d-11fd680a05c3
TID: [-1234] [] [2024-12-27 07:05:06,988]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6a173e36-2d62-4ba9-adab-73386ec7756b
TID: [-1234] [] [2024-12-27 07:05:07,363]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 751ea148-3817-48b2-a646-b41fddd00e33
TID: [-1234] [] [2024-12-27 07:05:15,803]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bd129c44-2b21-4dbb-95ff-52e19cf9325d
TID: [-1234] [] [2024-12-27 07:06:17,684]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 31fcec5e-5c52-4e2d-8e78-5e4e755bc280
TID: [-1234] [] [2024-12-27 07:31:22,779]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-27 07:57:43,611]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c2eb9ff6-1142-44ad-b0b3-c9c29dcead8a
TID: [-1234] [] [2024-12-27 08:01:23,093]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-27 08:05:40,510]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b436d94e-e6be-4014-b667-7c13c8d5404f
TID: [-1234] [] [2024-12-27 08:05:45,561]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 35fc5581-bb50-476b-830e-21dbc697e6ba
TID: [-1234] [] [2024-12-27 08:05:45,601]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ac2f3edd-6521-4071-a0b3-27031429ca29
TID: [-1234] [] [2024-12-27 08:05:46,838]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 36ca472b-1eb7-4885-9f65-fb4c3e6ac47c
TID: [-1234] [] [2024-12-27 08:05:50,970]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ba392dfc-1952-4fe2-81c8-cf246ce3735b
TID: [-1234] [] [2024-12-27 08:05:56,254]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 07508e77-cf43-440c-9bf4-f8bd9142bd7b
TID: [-1234] [] [2024-12-27 08:31:23,286]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-27 08:41:42,758] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-27 08:41:42,760]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-27 08:47:35,174] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-27 08:47:35,175]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-27 08:52:41,130] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-27 08:52:41,131]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-27 08:55:03,343] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-27 08:55:03,344]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-27 09:01:23,683]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-27 09:03:07,969] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-27 09:03:07,970]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-27 09:06:24,874]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8b287578-ede4-4247-bef9-7c6a802e647d
TID: [-1234] [] [2024-12-27 09:06:25,337]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b3e3ea48-7205-4ba2-9112-039286d16d64
TID: [-1234] [] [2024-12-27 09:06:30,768]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 740be031-7037-4455-a4ca-7dcbfa1678ec
TID: [-1234] [] [2024-12-27 09:06:34,877]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d06d2c01-4861-4535-8e54-67a26aa7088c
TID: [-1234] [] [2024-12-27 09:09:53,385]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7bd4799d-ddbc-4dbd-90f1-cc156b65cf4a
TID: [-1234] [] [2024-12-27 09:31:26,240]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-27 09:31:50,445] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-27 09:31:50,446]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-27 09:38:36,877]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b43f0493-3d41-4926-8972-bb03bd145c30
TID: [-1234] [] [2024-12-27 09:47:13,804] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-27 09:47:13,805]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-27 09:56:15,735] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-27 09:56:15,737]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-27 09:59:38,155] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-27 09:59:38,156]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-27 10:01:27,523]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-27 10:06:55,461]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e0613924-b74f-458d-98de-d9c343146bd6
TID: [-1234] [] [2024-12-27 10:06:55,741]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 64bc9e9a-02f1-4630-8a88-5d38e1a4c025
TID: [-1234] [] [2024-12-27 10:06:58,194]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ce8ce9ea-a092-4dcd-9e54-6e8ad04e8415
TID: [-1234] [] [2024-12-27 10:07:04,846]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0bd4b085-3cdc-47ea-9bad-53f6569aeecf
TID: [-1234] [] [2024-12-27 10:07:09,787]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9dc915f5-f7b8-48d5-872f-39bad5b24de3
TID: [-1234] [] [2024-12-27 10:08:11,785]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8c7f937b-32cf-4a9c-a8f8-7418aaabd40c
TID: [-1234] [] [2024-12-27 10:09:10,807]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 47b80db9-ada8-4868-a218-43d2719ac4e5
TID: [-1234] [] [2024-12-27 10:31:27,840]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-27 10:36:13,782]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 442d9956-0fce-4e21-b9a4-f6a3fb53c091
TID: [-1234] [] [2024-12-27 10:41:47,816] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-27 10:41:47,818]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-27 10:58:17,590]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241227&denNgay=20241227&maTthc=, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/getDanhSachHoSoTheoNgay?tuNgay=20241227&denNgay=20241227&maTthc=
TID: [-1234] [] [2024-12-27 10:58:17,632]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS, HEALTH CHECK URL = /lgsp-HD-MSNS/1.0.0/DmThuTucMSNS
TID: [-1234] [] [2024-12-27 10:58:49,692]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241227&denNgay=20241227&maTthc=, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/ds_hoso/list?tuNgay=20241227&denNgay=20241227&maTthc=
TID: [-1234] [] [2024-12-27 10:58:49,766]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-dtnn/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-dtnn/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-27 11:01:28,217]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-27 11:02:53,707]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241227&denNgay=20241227&maTthc=, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/ds_hoso/list?tuNgay=20241227&denNgay=20241227&maTthc=
TID: [-1234] [] [2024-12-27 11:02:53,751]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gpkdvt-phx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-27 11:03:15,534]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241227&denNgay=20241227&maTthc=, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/ds_hoso/list?tuNgay=20241227&denNgay=20241227&maTthc=
TID: [-1234] [] [2024-12-27 11:03:15,572]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-gplx/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-gplx/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-27 11:06:35,062]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 604018cd-2447-47e9-b3c7-2e007032867d
TID: [-1234] [] [2024-12-27 11:06:36,709]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ef5cef5b-b09b-47f1-8b7c-01180b660010
TID: [-1234] [] [2024-12-27 11:06:36,781]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8ec6a50b-26b3-4f5c-821f-ad83c668dd56
TID: [-1234] [] [2024-12-27 11:06:42,482]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5a2c5638-2725-4033-88ea-8173e74423ea
TID: [-1234] [] [2024-12-27 11:07:22,214]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 83acdca8-dc4d-4035-8a00-324067477e61
TID: [-1234] [] [2024-12-27 11:07:44,659]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 95b021be-8bea-49cf-90dd-78f94c5019c4
TID: [-1234] [] [2024-12-27 11:08:10,810]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241227&denNgay=20241227&maTthc=, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/ds_hoso/list?tuNgay=20241227&denNgay=20241227&maTthc=
TID: [-1234] [] [2024-12-27 11:08:10,889]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - STATUS = Message dispatched to the main sequence. Invalid URL., RESOURCE = /lgsp-hd-hny/1.0.0/dm_tthc, HEALTH CHECK URL = /lgsp-hd-hny/1.0.0/dm_tthc
TID: [-1234] [] [2024-12-27 11:28:25,642]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d552bab0-a026-4987-b43e-846d2a07d91b
TID: [-1234] [] [2024-12-27 11:31:29,068]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-27 11:35:21,357]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 06746280-1183-4131-a615-651176206e1d
TID: [-1234] [] [2024-12-27 11:46:19,220]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-27 11:46:19,223]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap - current suspend duration is : 30000ms - Next retry after : Fri Dec 27 11:46:49 ICT 2024
TID: [-1234] [] [2024-12-27 11:46:19,223]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-27 11:46:19,235]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:47483ae8-9d3c-4fc2-b0b7-5749585a8530; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/lylichtuphap/nhanHoSo, Received through API : admin--LyLichTuPhap:v1.0, CORRELATION_ID = 70d7dbd6-72fc-48e3-b55f-6740f3eef75e
TID: [-1234] [] [2024-12-27 11:46:22,980]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-27 11:46:27,169]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-27 11:47:17,374]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/lylichtuphap/nhanHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--LyLichTuPhap:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-84458, SOCKET_TIMEOUT = 180000, CORRELATION_ID = 70d7dbd6-72fc-48e3-b55f-6740f3eef75e
TID: [-1234] [] [2024-12-27 11:51:56,997]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap currently SUSPENDED will now be marked active since it processed its last message
TID: [-1234] [] [2024-12-27 12:01:29,792]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-27 12:06:41,557]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a63bba9e-7cc4-4cdd-90e9-4df9ef7cad43
TID: [-1234] [] [2024-12-27 12:06:42,373]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 22bafe4d-0e7a-470f-a6e7-5033d8048141
TID: [-1234] [] [2024-12-27 12:06:45,281]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e057e29b-81c0-4b3f-814f-e3629c7f2be4
TID: [-1234] [] [2024-12-27 12:06:46,091]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e9a126c2-20b4-48fc-a08d-d8bcd90aa0c0
TID: [-1234] [] [2024-12-27 12:06:50,954]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c1ffe4ea-4b1f-4c43-80ca-200acddcf93b
TID: [-1234] [] [2024-12-27 12:07:52,970]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 67926867-5208-414a-bb48-5363b682f16d
TID: [-1234] [] [2024-12-27 12:22:54,304]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 09dde749-69ab-4893-9a29-254fd78c0de3
TID: [-1234] [] [2024-12-27 12:31:30,094]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-27 13:01:30,588]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-27 13:07:04,167]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2a3ffb46-b1a1-4b2e-9de3-df814ff730c4
TID: [-1234] [] [2024-12-27 13:08:07,628]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6e4074e7-24e5-4133-8f70-6c438f790291
TID: [-1234] [] [2024-12-27 13:08:07,897]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 28311a9b-7eff-4909-af2b-8742ab07cb5f
TID: [-1234] [] [2024-12-27 13:31:32,888]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-27 14:01:33,231]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-27 14:06:24,497]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d4fcaa6d-b35c-4b03-8752-3b97fa3f2b98
TID: [-1234] [] [2024-12-27 14:06:26,331]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 61cd6130-fab2-4814-aeca-611998fb5644
TID: [-1234] [] [2024-12-27 14:06:27,363]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = ddee7d07-ed23-4ded-ad1b-e5f5e3e28b95
TID: [-1234] [] [2024-12-27 14:06:28,452]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4750fc78-5657-4795-becf-27eba75f1542
TID: [-1234] [] [2024-12-27 14:06:35,529]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 18044650-e6ff-4f03-ae06-5dcdb45b6784
TID: [-1234] [] [2024-12-27 14:07:37,602]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 26278c70-f87e-4451-8776-7b66a8375662
TID: [-1234] [] [2024-12-27 14:31:33,790]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-27 14:32:22,048] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-27 14:32:22,050]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-27 14:38:44,047]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 45185acd-88d8-41b4-9a04-b577de14dfcb
TID: [-1234] [] [2024-12-27 14:42:12,323] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-27 14:42:12,325]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-27 14:48:34,189] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-27 14:48:34,191]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-27 14:55:13,636] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-27 14:55:13,637]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-27 15:01:34,120]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-27 15:02:30,171] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-27 15:02:30,172]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-27 15:06:38,498]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3a502486-4c26-4824-a6de-5233831ae795
TID: [-1234] [] [2024-12-27 15:06:41,143]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 962ef722-1855-4eb8-9157-bf55e3b74347
TID: [-1234] [] [2024-12-27 15:06:41,145]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 56d31e64-f480-41e5-86a3-1adb808d53e6
TID: [-1234] [] [2024-12-27 15:06:47,202]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5b9c86eb-1682-4481-8fb5-6d7939720b83
TID: [-1234] [] [2024-12-27 15:06:50,745]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 57cb4ac0-2866-4512-9ee3-de516e6a4bc3
TID: [-1234] [] [2024-12-27 15:08:41,235]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0809628b-e295-4061-997c-284ec978e96f
TID: [-1234] [] [2024-12-27 15:11:13,866] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-27 15:11:13,867]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-27 15:20:59,685] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-27 15:20:59,687]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-27 15:29:56,888] ERROR {org.wso2.carbon.apimgt.gateway.handlers.security.jwt.JWTValidator} - Invalid JWT token. XXXXX":"RS256"}
TID: [-1234] [] [2024-12-27 15:29:56,889]  WARN {org.wso2.carbon.apimgt.gateway.handlers.security.APIAuthenticationHandler} - API authentication failure due to Invalid Credentials
TID: [-1234] [] [2024-12-27 15:31:34,769]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-27 16:01:35,052]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-27 16:07:04,474]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 21622d37-0c47-4f1c-a09f-f96e55d7a2d2
TID: [-1234] [] [2024-12-27 16:07:16,675]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = b7e3e901-feaf-4d72-959c-aba11d78c633
TID: [-1234] [] [2024-12-27 16:07:26,912]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5346fd43-2262-4701-b0a7-db4c98535a1e
TID: [-1234] [] [2024-12-27 16:25:05,604]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c061f741-cdfb-48da-8a78-bfb5929f53aa
TID: [-1234] [] [2024-12-27 16:25:16,830]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 352849b1-4f6b-4481-8e23-e2019f8f650a
TID: [-1234] [] [2024-12-27 16:32:42,971]  INFO {org.wso2.carbon.identity.application.authentication.framework.store.SessionCleanUpService} - Session Data cleanup task is running successfully for removing expired Data
TID: [-1234] [] [2024-12-27 16:32:46,123]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-27 16:48:51,813]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 41985a7d-40d1-4a4d-8117-307b6a4e6c5d
TID: [-1234] [] [2024-12-27 17:02:46,308]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-27 17:06:08,093] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2024-12-27 17:06:41,446] ERROR {org.wso2.carbon.tomcat.ext.valves.CompositeValve} - Could not handle the request, could be due to the maxHttpHeaderSize limitation. java.lang.NullPointerException

TID: [-1234] [] [2024-12-27 17:06:53,438]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 3188613d-5737-4b5e-8a39-7d75904e8ddc
TID: [-1234] [] [2024-12-27 17:06:56,880]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9bc6cd74-d897-4300-bbb9-d750f1bcc8e7
TID: [-1234] [] [2024-12-27 17:06:59,579]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4e7e2c45-b6b8-405b-bb2d-fe72e1c8cfc0
TID: [-1234] [] [2024-12-27 17:07:01,559]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = aaee0f12-efa3-46c3-8747-91a972092f88
TID: [-1234] [] [2024-12-27 17:07:02,237]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0d98488d-556a-4a44-9362-cb74c013e9a0
TID: [-1234] [] [2024-12-27 17:13:50,657]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0d828ade-8892-411b-892f-352470ea9531
TID: [-1234] [] [2024-12-27 17:43:08,594]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-27 17:49:51,076]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = fd23f68a-6f0d-4948-8981-5ab4a6525385
TID: [-1234] [] [2024-12-27 18:01:37,734]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap has been marked for SUSPENSION, but no further retries remain. Thus it will be SUSPENDED.
TID: [-1234] [] [2024-12-27 18:01:37,737]  WARN {org.apache.synapse.endpoints.EndpointContext} - Suspending endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap - current suspend duration is : 30000ms - Next retry after : Fri Dec 27 18:02:07 ICT 2024
TID: [-1234] [] [2024-12-27 18:01:37,738]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 101504, ERROR_MESSAGE = Send timeout
TID: [-1234] [] [2024-12-27 18:01:37,748]  WARN {org.apache.synapse.core.axis2.TimeoutHandler} - Expiring message ID : urn:uuid:3316badb-f46f-44b1-87a9-096d288e2b3f; dropping message after GLOBAL_TIMEOUT of : 120 seconds for null, URI : http://************:8290/lylichtuphap/nhanHoSo, Received through API : admin--LyLichTuPhap:v1.0, CORRELATION_ID = dc2a6342-9daf-4eda-89a0-89080e0ee65b
TID: [-1234] [] [2024-12-27 18:01:46,751]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-27 18:01:57,241]  INFO {org.apache.synapse.mediators.builtin.LogMediator} - {api:admin--LyLichTuPhap:v1.0} STATUS = Executing default 'fault' sequence, ERROR_CODE = 303001, ERROR_MESSAGE = Currently , Address endpoint : [ Name : LyLichTuPhap--v1.0_APIproductionEndpoint ] [ State : SUSPENDED ]
TID: [-1234] [] [2024-12-27 18:02:27,237]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = REQUEST_DONE, CORRELATION_ID = dc2a6342-9daf-4eda-89a0-89080e0ee65b
TID: [-1234] [] [2024-12-27 18:02:27,238]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - ERROR_CODE = 101504, STATE_DESCRIPTION = Socket Timeout occurred after Server written the request headers and the request body to the backend, INTERNAL_STATE = REQUEST_DONE, DIRECTION = REQUEST, CAUSE_OF_ERROR = Connection between the EI and the BackEnd timeouts, TARGET_HOST = ************, TARGET_PORT = 8290, TARGET_CONTEXT = http://************:8290/lylichtuphap/nhanHoSo, HTTP_METHOD = POST, TRIGGER_TYPE = api, TRIGGER_NAME = admin--LyLichTuPhap:v1.0, REMOTE_ADDRESS = /************:8290, CONNECTION = http-outgoing-84694, SOCKET_TIMEOUT = 180000, CORRELATION_ID = dc2a6342-9daf-4eda-89a0-89080e0ee65b
TID: [-1234] [] [2024-12-27 18:06:21,730]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4df5cf92-e4a1-4aa8-979d-affb0a47ee15
TID: [-1234] [] [2024-12-27 18:06:23,624]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7a4900ad-2b6f-4858-803a-4316ec0bfd20
TID: [-1234] [] [2024-12-27 18:06:24,679]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0ff5c364-1b0c-4b12-a0d8-68d57f94cdd2
TID: [-1234] [] [2024-12-27 18:06:25,355]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4d9f0d8e-3247-4ace-ae74-2def6d5a626f
TID: [-1234] [] [2024-12-27 18:06:25,428]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = bd63161c-36e9-49c6-b440-e7c55ab32797
TID: [-1234] [] [2024-12-27 18:06:26,345]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e7066c85-de95-4dc7-a5ee-202a8e8385ff
TID: [-1234] [] [2024-12-27 18:06:36,288]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 6e6e9a7b-ce69-449f-b201-f7bd88bb1af8
TID: [-1234] [] [2024-12-27 18:10:35,234]  INFO {org.apache.synapse.endpoints.EndpointContext} - Endpoint : LyLichTuPhap--v1.0_APIproductionEndpoint with address http://************:8290/lylichtuphap currently SUSPENDED will now be marked active since it processed its last message
TID: [-1234] [] [2024-12-27 18:13:08,909]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-27 18:13:35,425]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = d8d92631-99bf-40db-b2d4-678ad43cec3f
TID: [-1234] [] [2024-12-27 18:27:14,695]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = f1e0b423-eba0-4848-bd1d-f9ee1bf108b8
TID: [-1234] [] [2024-12-27 18:43:09,226]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-27 19:09:41,978]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c0f4a708-a4c4-4dbb-894e-e4d910be6e08
TID: [-1234] [] [2024-12-27 19:09:43,303]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e0d3f032-9b1c-4007-81a1-ee19b10323fd
TID: [-1234] [] [2024-12-27 19:13:09,530]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-27 19:43:09,786]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-27 20:05:57,861]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 39ef1ef9-dd1e-44e6-aca5-b360eef68938
TID: [-1234] [] [2024-12-27 20:05:59,478]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 310ce7ec-5c08-4d4f-9e87-22a3572aa340
TID: [-1234] [] [2024-12-27 20:05:59,887]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = a8f8b6ae-fbef-41ad-bb4f-b23b1bab98bb
TID: [-1234] [] [2024-12-27 20:06:02,447]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 9b342105-9d11-4242-bd66-15b34b9a134d
TID: [-1234] [] [2024-12-27 20:06:06,050]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 41a7097f-7cab-4aeb-8725-c5da037fc89f
TID: [-1234] [] [2024-12-27 20:06:08,594]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8a4df985-bbe7-4f93-a743-cc478a011e1d
TID: [-1234] [] [2024-12-27 20:06:08,734]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 8f8eaae9-6015-40c4-8668-81a748319092
TID: [-1234] [] [2024-12-27 20:06:17,922]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = cbb14b57-5dc4-4ce8-b5f2-1021a08f0023
TID: [-1234] [] [2024-12-27 20:07:19,724]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = dcf20d9a-cff9-4b48-95e2-cd66095f7864
TID: [-1234] [] [2024-12-27 20:13:21,444]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-27 20:43:23,586]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-27 21:06:25,775]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 56c826a5-41ab-4558-bba0-246f3cf61914
TID: [-1234] [] [2024-12-27 21:06:26,940]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4234ce93-9b25-4452-896e-6a3fc21c9c81
TID: [-1234] [] [2024-12-27 21:06:28,269]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2db024e6-7e0f-4ed5-846e-a3e7d7743b00
TID: [-1234] [] [2024-12-27 21:06:28,493]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e2106f25-fff5-4023-8808-d3c46e97fe21
TID: [-1234] [] [2024-12-27 21:06:29,815]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 0906d5e5-4c41-4f1e-ac9f-4335a35c8755
TID: [-1234] [] [2024-12-27 21:06:32,514]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 310fea26-e4cd-42cc-82ff-460d18a6f7d9
TID: [-1234] [] [2024-12-27 21:06:33,899]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4a30d16e-1a78-4f87-b0e4-c7c13ba096a0
TID: [-1234] [] [2024-12-27 21:07:35,817]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 461c4cf2-de0a-4d6c-a006-802629687360
TID: [-1234] [] [2024-12-27 21:28:57,523]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-27 21:58:57,862]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-27 22:06:51,753]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 5a743ad5-5de1-42fc-94ba-bd4e552b4bed
TID: [-1234] [] [2024-12-27 22:06:56,827]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 990a5a8c-285b-4f9e-b816-22a4b508b7b5
TID: [-1234] [] [2024-12-27 22:06:57,895]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4c91ebcb-4e7e-47f5-9135-a13b949c589d
TID: [-1234] [] [2024-12-27 22:07:00,195]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 984b5655-d77e-4b45-aa73-b9b22476344b
TID: [-1234] [] [2024-12-27 22:07:01,020]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7d269eda-6e83-4840-94da-9db61d7814cc
TID: [-1234] [] [2024-12-27 22:07:01,185]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 1573fe9d-7162-4b4a-bf0c-0fad6f437e9c
TID: [-1234] [] [2024-12-27 22:07:08,940]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 7f3caed1-0369-4d6c-9e99-c2121d675615
TID: [-1234] [] [2024-12-27 22:08:10,781]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 09fca333-9308-4448-a0c5-430f9ad8aa06
TID: [-1234] [] [2024-12-27 22:28:58,136]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-27 22:39:13,313]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 524b83d9-d624-4399-bda4-27efe3edd99e
TID: [-1234] [] [2024-12-27 22:58:58,350]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-27 23:06:24,761]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4514f8c5-3488-45a8-bd45-335f4e32d765
TID: [-1234] [] [2024-12-27 23:06:28,742]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 141687c7-f57b-4bc7-a77e-6ca03c6a84a0
TID: [-1234] [] [2024-12-27 23:06:29,294]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = e8984f08-b34c-49f5-a5a5-b835255ee1bd
TID: [-1234] [] [2024-12-27 23:06:29,925]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 4adb3c60-5e94-4793-b635-df0bc664429c
TID: [-1234] [] [2024-12-27 23:06:30,636]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 783a93a1-0ad9-49d0-b9f1-699f736a3942
TID: [-1234] [] [2024-12-27 23:06:34,105]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = c1678e75-de0e-49c2-a4ca-bbba745da02b
TID: [-1234] [] [2024-12-27 23:06:34,233]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 649b4281-eda5-4a9b-86aa-ca2c7c5cdea5
TID: [-1234] [] [2024-12-27 23:07:38,853]  WARN {org.apache.synapse.transport.passthru.TargetHandler} - Target Handler Socket Timeout occurred while the worker pool exhausted, INTERNAL_STATE = RESPONSE_DONE, CORRELATION_ID = 2bf2c6ba-3981-48b7-b485-2bb89a3dc0b0
TID: [-1234] [] [2024-12-27 23:28:58,631]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
TID: [-1234] [] [2024-12-27 23:58:59,001]  INFO {org.wso2.carbon.databridge.core.DataBridge} - user admin connected
