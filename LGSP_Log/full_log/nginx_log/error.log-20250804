2025/08/03 09:47:00 [crit] 20297#20297: *8741656 SSL_do_handshake() failed (SSL: error:141CF06C:SSL routines:tls_parse_ctos_key_share:bad key share) while SSL handshaking, client: ***********, server: 0.0.0.0:443
2025/08/03 09:54:59 [crit] 20297#20297: *8741768 SSL_do_handshake() failed (SSL: error:141CF06C:SSL routines:tls_parse_ctos_key_share:bad key share) while SSL handshaking, client: ***********, server: 0.0.0.0:443
2025/08/03 10:17:10 [crit] 20295#20295: *8741835 SSL_do_handshake() failed (SSL: error:141CF06C:SSL routines:tls_parse_ctos_key_share:bad key share) while SSL handshaking, client: ***********, server: 0.0.0.0:443
2025/08/03 11:33:12 [warn] 20297#20297: *8742396 a client request body is buffered to a temporary file /var/cache/nginx/client_temp/0000101143, client: ***********, server: agm.haiduong.gov.vn, request: "POST /api/lylichtuphap/nhanHoSo HTTP/1.1", host: "agm.haiduong.gov.vn"
2025/08/03 11:33:44 [warn] 20295#20295: *8742436 a client request body is buffered to a temporary file /var/cache/nginx/client_temp/0000101144, client: ***********, server: agm.haiduong.gov.vn, request: "POST /api/lylichtuphap/nhanHoSo HTTP/1.1", host: "agm.haiduong.gov.vn"
2025/08/03 11:34:03 [warn] 20295#20295: *8742438 a client request body is buffered to a temporary file /var/cache/nginx/client_temp/0000101145, client: ***********, server: agm.haiduong.gov.vn, request: "POST /api/lylichtuphap/nhanHoSo HTTP/1.1", host: "agm.haiduong.gov.vn"
2025/08/03 11:34:21 [warn] 20295#20295: *8742440 a client request body is buffered to a temporary file /var/cache/nginx/client_temp/0000101146, client: ***********, server: agm.haiduong.gov.vn, request: "POST /api/lylichtuphap/nhanHoSo HTTP/1.1", host: "agm.haiduong.gov.vn"
2025/08/03 11:34:38 [warn] 20295#20295: *8742442 a client request body is buffered to a temporary file /var/cache/nginx/client_temp/0000101147, client: ***********, server: agm.haiduong.gov.vn, request: "POST /api/lylichtuphap/nhanHoSo HTTP/1.1", host: "agm.haiduong.gov.vn"
2025/08/03 11:34:52 [warn] 20295#20295: *8742444 a client request body is buffered to a temporary file /var/cache/nginx/client_temp/0000101148, client: ***********, server: agm.haiduong.gov.vn, request: "POST /api/lylichtuphap/nhanHoSo HTTP/1.1", host: "agm.haiduong.gov.vn"
2025/08/03 11:35:09 [warn] 20295#20295: *8742448 a client request body is buffered to a temporary file /var/cache/nginx/client_temp/0000101149, client: ***********, server: agm.haiduong.gov.vn, request: "POST /api/lylichtuphap/nhanHoSo HTTP/1.1", host: "agm.haiduong.gov.vn"
2025/08/03 11:35:24 [warn] 20295#20295: *8742450 a client request body is buffered to a temporary file /var/cache/nginx/client_temp/0000101150, client: ***********, server: agm.haiduong.gov.vn, request: "POST /api/lylichtuphap/nhanHoSo HTTP/1.1", host: "agm.haiduong.gov.vn"
2025/08/03 11:35:45 [warn] 20295#20295: *8742452 a client request body is buffered to a temporary file /var/cache/nginx/client_temp/0000101151, client: ***********, server: agm.haiduong.gov.vn, request: "POST /api/lylichtuphap/nhanHoSo HTTP/1.1", host: "agm.haiduong.gov.vn"
2025/08/03 11:36:00 [warn] 20295#20295: *8742454 a client request body is buffered to a temporary file /var/cache/nginx/client_temp/0000101152, client: ***********, server: agm.haiduong.gov.vn, request: "POST /api/lylichtuphap/nhanHoSo HTTP/1.1", host: "agm.haiduong.gov.vn"
2025/08/03 11:55:52 [crit] 20298#20298: *8742669 SSL_do_handshake() failed (SSL: error:141CF06C:SSL routines:tls_parse_ctos_key_share:bad key share) while SSL handshaking, client: ***********, server: 0.0.0.0:443
2025/08/03 14:08:35 [crit] 20298#20298: *8744362 SSL_do_handshake() failed (SSL: error:141CF06C:SSL routines:tls_parse_ctos_key_share:bad key share) while SSL handshaking, client: ***********, server: 0.0.0.0:443
2025/08/03 14:35:08 [crit] 20298#20298: *8744487 SSL_do_handshake() failed (SSL: error:141CF06C:SSL routines:tls_parse_ctos_key_share:bad key share) while SSL handshaking, client: ***********, server: 0.0.0.0:443
2025/08/03 16:05:21 [crit] 20298#20298: *8745209 SSL_do_handshake() failed (SSL: error:141CF06C:SSL routines:tls_parse_ctos_key_share:bad key share) while SSL handshaking, client: ***********, server: 0.0.0.0:443
2025/08/03 16:59:46 [crit] 20295#20295: *8745749 SSL_do_handshake() failed (SSL: error:141CF06C:SSL routines:tls_parse_ctos_key_share:bad key share) while SSL handshaking, client: ***********, server: 0.0.0.0:443
2025/08/03 17:48:19 [warn] 20296#20296: *8746043 a client request body is buffered to a temporary file /var/cache/nginx/client_temp/0000101153, client: ***********, server: agm.haiduong.gov.vn, request: "POST /api/lylichtuphap/nhanHoSo HTTP/1.1", host: "agm.haiduong.gov.vn"
2025/08/03 17:48:57 [warn] 20297#20297: *8746062 a client request body is buffered to a temporary file /var/cache/nginx/client_temp/0000101154, client: ***********, server: agm.haiduong.gov.vn, request: "POST /api/lylichtuphap/nhanHoSo HTTP/1.1", host: "agm.haiduong.gov.vn"
2025/08/03 17:49:18 [warn] 20297#20297: *8746064 a client request body is buffered to a temporary file /var/cache/nginx/client_temp/0000101155, client: ***********, server: agm.haiduong.gov.vn, request: "POST /api/lylichtuphap/nhanHoSo HTTP/1.1", host: "agm.haiduong.gov.vn"
2025/08/03 17:49:35 [warn] 20297#20297: *8746066 a client request body is buffered to a temporary file /var/cache/nginx/client_temp/0000101156, client: ***********, server: agm.haiduong.gov.vn, request: "POST /api/lylichtuphap/nhanHoSo HTTP/1.1", host: "agm.haiduong.gov.vn"
2025/08/03 17:49:48 [warn] 20298#20298: *8746084 a client request body is buffered to a temporary file /var/cache/nginx/client_temp/0000101157, client: ***********, server: agm.haiduong.gov.vn, request: "POST /api/lylichtuphap/nhanHoSo HTTP/1.1", host: "agm.haiduong.gov.vn"
2025/08/03 17:50:03 [warn] 20298#20298: *8746086 a client request body is buffered to a temporary file /var/cache/nginx/client_temp/0000101158, client: ***********, server: agm.haiduong.gov.vn, request: "POST /api/lylichtuphap/nhanHoSo HTTP/1.1", host: "agm.haiduong.gov.vn"
2025/08/03 17:50:17 [warn] 20298#20298: *8746088 a client request body is buffered to a temporary file /var/cache/nginx/client_temp/0000101159, client: ***********, server: agm.haiduong.gov.vn, request: "POST /api/lylichtuphap/nhanHoSo HTTP/1.1", host: "agm.haiduong.gov.vn"
2025/08/03 17:50:33 [warn] 20298#20298: *8746090 a client request body is buffered to a temporary file /var/cache/nginx/client_temp/0000101160, client: ***********, server: agm.haiduong.gov.vn, request: "POST /api/lylichtuphap/nhanHoSo HTTP/1.1", host: "agm.haiduong.gov.vn"
2025/08/03 17:50:58 [warn] 20298#20298: *8746094 a client request body is buffered to a temporary file /var/cache/nginx/client_temp/0000101161, client: ***********, server: agm.haiduong.gov.vn, request: "POST /api/lylichtuphap/nhanHoSo HTTP/1.1", host: "agm.haiduong.gov.vn"
2025/08/03 17:51:17 [warn] 20296#20296: *8746164 a client request body is buffered to a temporary file /var/cache/nginx/client_temp/0000101162, client: ***********, server: agm.haiduong.gov.vn, request: "POST /api/lylichtuphap/nhanHoSo HTTP/1.1", host: "agm.haiduong.gov.vn"
2025/08/03 19:14:46 [crit] 20297#20297: *8747455 SSL_do_handshake() failed (SSL: error:141CF06C:SSL routines:tls_parse_ctos_key_share:bad key share) while SSL handshaking, client: ***********, server: 0.0.0.0:443
2025/08/03 23:16:10 [crit] 20295#20295: *8751310 SSL_do_handshake() failed (SSL: error:141CF06C:SSL routines:tls_parse_ctos_key_share:bad key share) while SSL handshaking, client: ***********, server: 0.0.0.0:443
2025/08/04 01:45:55 [crit] 20298#20298: *8752468 SSL_do_handshake() failed (SSL: error:141CF06C:SSL routines:tls_parse_ctos_key_share:bad key share) while SSL handshaking, client: ***********, server: 0.0.0.0:443
2025/08/04 01:46:05 [crit] 20298#20298: *8752472 SSL_do_handshake() failed (SSL: error:141CF06C:SSL routines:tls_parse_ctos_key_share:bad key share) while SSL handshaking, client: ***********, server: 0.0.0.0:443
2025/08/04 03:29:26 [crit] 20295#20295: *8756184 SSL_do_handshake() failed (SSL: error:141CF06C:SSL routines:tls_parse_ctos_key_share:bad key share) while SSL handshaking, client: ***********, server: 0.0.0.0:443
2025/08/04 03:34:01 [notice] 1109#1109: signal 10 (SIGUSR1) received from 114670, reopening logs
2025/08/04 03:34:01 [notice] 1109#1109: reopening logs
2025/08/04 03:34:01 [notice] 20295#20295: reopening logs
2025/08/04 03:34:01 [notice] 20297#20297: reopening logs
2025/08/04 03:34:01 [notice] 20296#20296: reopening logs
2025/08/04 03:34:01 [notice] 20298#20298: reopening logs
