#!/usr/bin/env python3
"""
Tạo báo cáo HTML chi tiết cho thống kê API paths và domains
"""

import json
import os
from datetime import datetime

def load_api_domain_results():
    """Load kết quả phân tích API và domain"""
    with open('api_domain_statistics_report.json', 'r', encoding='utf-8') as f:
        return json.load(f)

def generate_api_domain_html_report(results):
    """Tạo báo cáo HTML chi tiết cho API và domain statistics"""
    
    summary = results['summary']
    api_stats = results['api_statistics']
    domain_stats = results['domain_statistics']
    detailed = results['detailed_analysis']
    security = results['security_analysis']
    performance = results['performance_metrics']
    
    html_content = f"""
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Báo cáo thống kê API Paths và Domains</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }}
        .container {{ max-width: 1800px; margin: 0 auto; background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.3); overflow: hidden; }}
        .header {{ background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); color: white; padding: 30px; text-align: center; }}
        .header h1 {{ margin: 0; font-size: 3em; font-weight: 300; }}
        .header .subtitle {{ font-size: 1.3em; margin: 10px 0; opacity: 0.9; }}
        .header .stats {{ font-size: 1.1em; margin: 15px 0; opacity: 0.8; }}
        .content {{ padding: 30px; }}
        .mega-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 30px 0; }}
        .metric-card {{ background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 20px; border-radius: 12px; text-align: center; border-left: 5px solid #3498db; transition: all 0.3s ease; }}
        .metric-card:hover {{ transform: translateY(-5px); box-shadow: 0 8px 25px rgba(0,0,0,0.15); }}
        .metric-value {{ font-size: 2.2em; font-weight: bold; color: #2c3e50; margin-bottom: 5px; }}
        .metric-label {{ color: #7f8c8d; font-size: 1em; }}
        .section {{ margin: 40px 0; padding: 30px; background: #f8f9fa; border-radius: 15px; border-left: 6px solid #e74c3c; }}
        .section-title {{ font-size: 2em; color: #2c3e50; margin-bottom: 25px; display: flex; align-items: center; }}
        .section-icon {{ font-size: 1.3em; margin-right: 15px; }}
        .chart-container {{ margin: 30px 0; padding: 25px; background: white; border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); }}
        .chart-title {{ font-size: 1.6em; color: #2c3e50; margin-bottom: 20px; text-align: center; font-weight: 600; }}
        .api-table {{ width: 100%; border-collapse: collapse; margin: 20px 0; background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        .api-table th, .api-table td {{ padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }}
        .api-table th {{ background: #34495e; color: white; font-weight: 600; }}
        .api-table tr:hover {{ background: #f8f9fa; }}
        .error {{ color: #e74c3c; font-weight: bold; }}
        .success {{ color: #27ae60; font-weight: bold; }}
        .warning {{ color: #f39c12; font-weight: bold; }}
        .info {{ color: #3498db; font-weight: bold; }}
        .highlight-box {{ background: linear-gradient(135deg, #ff7675 0%, #d63031 100%); color: white; padding: 20px; border-radius: 10px; margin: 20px 0; }}
        .success-box {{ background: linear-gradient(135deg, #00b894 0%, #00a085 100%); color: white; padding: 20px; border-radius: 10px; margin: 20px 0; }}
        .info-box {{ background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%); color: white; padding: 20px; border-radius: 10px; margin: 20px 0; }}
        .api-path {{ font-family: 'Courier New', monospace; background: #f1f2f6; padding: 2px 6px; border-radius: 4px; }}
        .percentage {{ font-size: 0.9em; color: #7f8c8d; }}
        .footer {{ background: #2c3e50; color: white; padding: 30px; text-align: center; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 Báo cáo thống kê API Paths & Domains</h1>
            <div class="subtitle">Phân tích toàn diện API usage từ LGSP, Nginx, WSO2</div>
            <div class="stats">
                📊 {summary['total_unique_apis']:,} API paths unique | 
                🌐 {summary['total_unique_domains']} domains | 
                📈 {summary['total_api_calls']:,} total calls
            </div>
        </div>
        
        <div class="content">
            <h2>🎯 Tổng quan thống kê</h2>
            <div class="mega-grid">
                <div class="metric-card">
                    <div class="metric-value">{summary['total_unique_apis']:,}</div>
                    <div class="metric-label">API Paths Unique</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{summary['total_unique_domains']}</div>
                    <div class="metric-label">Domains Unique</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{summary['total_api_calls']:,}</div>
                    <div class="metric-label">Total API Calls</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{len(api_stats['lgsp_apis'])}</div>
                    <div class="metric-label">LGSP APIs</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{len(api_stats['nginx_endpoints']):,}</div>
                    <div class="metric-label">Nginx Endpoints</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{len(api_stats['wso2_endpoints']):,}</div>
                    <div class="metric-label">WSO2 Endpoints</div>
                </div>
            </div>
            
            <div class="chart-container">
                <div class="chart-title">📈 Phân bố API calls theo hệ thống</div>
                <canvas id="systemApiChart" width="400" height="200"></canvas>
            </div>
            
            <div class="section">
                <div class="section-title"><span class="section-icon">🔝</span>Top 20 API Paths theo Traffic</div>
                <table class="api-table">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>API Path</th>
                            <th>Calls</th>
                            <th>%</th>
                            <th>Category</th>
                        </tr>
                    </thead>
                    <tbody>
    """
    
    # Top APIs table
    for i, (api, count) in enumerate(list(summary['top_apis_by_traffic'].items())[:20], 1):
        percentage = (count / summary['total_api_calls'] * 100)
        # Simple categorization
        if 'oauth' in api.lower() or 'token' in api.lower():
            category = 'Authentication'
        elif 'api' in api.lower():
            category = 'API'
        elif 'admin' in api.lower():
            category = 'Administration'
        else:
            category = 'Other'
            
        html_content += f"""
                        <tr>
                            <td>{i}</td>
                            <td><span class="api-path">{api[:80]}</span></td>
                            <td><strong>{count:,}</strong></td>
                            <td><span class="percentage">{percentage:.1f}%</span></td>
                            <td>{category}</td>
                        </tr>
        """
    
    html_content += f"""
                    </tbody>
                </table>
            </div>
            
            <div class="chart-container">
                <div class="chart-title">🌐 Top Domains Distribution</div>
                <canvas id="domainsChart" width="400" height="200"></canvas>
            </div>
            
            <div class="section">
                <div class="section-title"><span class="section-icon">📊</span>Phân loại API theo chức năng</div>
                <div class="chart-container">
                    <canvas id="categoryChart" width="400" height="200"></canvas>
                </div>
                <div class="mega-grid">
    """
    
    # API categories
    for category, count in summary['api_categories'].items():
        percentage = (count / summary['total_api_calls'] * 100)
        html_content += f"""
                    <div class="metric-card">
                        <div class="metric-value">{count:,}</div>
                        <div class="metric-label">{category} ({percentage:.1f}%)</div>
                    </div>
        """
    
    html_content += f"""
                </div>
            </div>
            
            <div class="section">
                <div class="section-title"><span class="section-icon">❌</span>APIs có nhiều lỗi nhất</div>
                <div class="highlight-box">
                    <h3>🚨 CẢNH BÁO</h3>
                    <p>Có {len(summary['top_apis_by_errors'])} APIs với tỷ lệ lỗi cao cần được kiểm tra!</p>
                </div>
                <table class="api-table">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>API Path</th>
                            <th>Errors</th>
                            <th>Error Rate</th>
                            <th>Total Calls</th>
                        </tr>
                    </thead>
                    <tbody>
    """
    
    # Error APIs table
    for i, (api, error_count) in enumerate(list(summary['top_apis_by_errors'].items())[:10], 1):
        total_calls = performance['highest_traffic_apis'].get(api, 0)
        error_rate = (error_count / total_calls * 100) if total_calls > 0 else 0
        
        html_content += f"""
                        <tr>
                            <td>{i}</td>
                            <td><span class="api-path">{api[:60]}</span></td>
                            <td><span class="error">{error_count:,}</span></td>
                            <td><span class="error">{error_rate:.1f}%</span></td>
                            <td>{total_calls:,}</td>
                        </tr>
        """
    
    html_content += f"""
                    </tbody>
                </table>
            </div>
            
            <div class="section">
                <div class="section-title"><span class="section-icon">🔒</span>Phân tích bảo mật</div>
                <div class="mega-grid">
                    <div class="metric-card">
                        <div class="metric-value error">{sum(security['failed_authentications'].values()):,}</div>
                        <div class="metric-label">Failed Authentications</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value warning">{sum(security['bot_detected_apis'].values()):,}</div>
                        <div class="metric-label">Bot Detections</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{len(security['failed_authentications'])}</div>
                        <div class="metric-label">APIs with Auth Issues</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{len(security['bot_detected_apis'])}</div>
                        <div class="metric-label">Bot Attack Types</div>
                    </div>
                </div>
                
                <div class="info-box">
                    <h3>🔍 Top APIs với Authentication Failures:</h3>
                    <ul>
    """
    
    # Top auth failure APIs
    for api, count in list(security['failed_authentications'].items())[:5]:
        html_content += f"<li><strong>{api[:60]}</strong>: {count:,} failures</li>"
    
    html_content += f"""
                    </ul>
                </div>
            </div>
            
            <div class="section">
                <div class="section-title"><span class="section-icon">⚡</span>Phân tích hiệu năng</div>
                <div class="mega-grid">
    """
    
    # Performance metrics
    if summary.get('avg_response_times'):
        slowest_apis = sorted(summary['avg_response_times'].items(), key=lambda x: x[1], reverse=True)[:3]
        for api, avg_time in slowest_apis:
            html_content += f"""
                    <div class="metric-card">
                        <div class="metric-value warning">{avg_time:.3f}s</div>
                        <div class="metric-label">Slowest: {api[:30]}...</div>
                    </div>
            """
    
    if summary.get('avg_response_sizes'):
        largest_apis = sorted(summary['avg_response_sizes'].items(), key=lambda x: x[1], reverse=True)[:3]
        for api, avg_size in largest_apis:
            html_content += f"""
                    <div class="metric-card">
                        <div class="metric-value info">{avg_size:,.0f} bytes</div>
                        <div class="metric-label">Largest: {api[:30]}...</div>
                    </div>
            """
    
    html_content += f"""
                </div>
            </div>
            
            <div class="chart-container">
                <div class="chart-title">📈 API Usage Trends</div>
                <canvas id="trendsChart" width="400" height="200"></canvas>
            </div>
            
            <div class="section">
                <div class="section-title"><span class="section-icon">💡</span>Khuyến nghị</div>
                <div class="success-box">
                    <h3>🎯 Khuyến nghị tối ưu hóa:</h3>
                    <ul>
                        <li><strong>API Optimization:</strong> Tối ưu hóa top 5 APIs có traffic cao nhất</li>
                        <li><strong>Error Handling:</strong> Xử lý {len(summary['top_apis_by_errors'])} APIs có tỷ lệ lỗi cao</li>
                        <li><strong>Security:</strong> Tăng cường bảo mật cho {sum(security['failed_authentications'].values()):,} failed authentications</li>
                        <li><strong>Performance:</strong> Caching cho APIs có response size lớn</li>
                        <li><strong>Monitoring:</strong> Thiết lập alerts cho top error-prone APIs</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>📅 Báo cáo được tạo vào {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}</p>
            <p>📊 Dữ liệu: {summary['total_unique_apis']:,} API paths, {summary['total_api_calls']:,} calls từ 3 hệ thống</p>
        </div>
    </div>
    
    <script>
        // System API Distribution Chart
        const systemApiCtx = document.getElementById('systemApiChart').getContext('2d');
        new Chart(systemApiCtx, {{
            type: 'doughnut',
            data: {{
                labels: ['Nginx ({len(api_stats['nginx_endpoints']):,})', 'WSO2 ({len(api_stats['wso2_endpoints']):,})', 'LGSP ({len(api_stats['lgsp_apis'])})'],
                datasets: [{{
                    data: [{len(api_stats['nginx_endpoints'])}, {len(api_stats['wso2_endpoints'])}, {len(api_stats['lgsp_apis'])}],
                    backgroundColor: ['#3498db', '#e74c3c', '#f39c12'],
                    borderWidth: 3,
                    borderColor: '#fff'
                }}]
            }},
            options: {{
                responsive: true,
                plugins: {{
                    legend: {{
                        position: 'bottom',
                        labels: {{
                            padding: 20
                        }}
                    }}
                }}
            }}
        }});
        
        // Domains Chart
        const domainsCtx = document.getElementById('domainsChart').getContext('2d');
        const topDomains = {list(domain_stats['all_domains_combined'].items())[:10]};
        new Chart(domainsCtx, {{
            type: 'bar',
            data: {{
                labels: {[domain for domain, count in list(domain_stats['all_domains_combined'].items())[:10]]},
                datasets: [{{
                    label: 'API Calls',
                    data: {[count for domain, count in list(domain_stats['all_domains_combined'].items())[:10]]},
                    backgroundColor: '#3498db',
                    borderWidth: 1
                }}]
            }},
            options: {{
                responsive: true,
                scales: {{
                    y: {{
                        beginAtZero: true,
                        type: 'logarithmic'
                    }}
                }}
            }}
        }});
        
        // Category Chart
        const categoryCtx = document.getElementById('categoryChart').getContext('2d');
        new Chart(categoryCtx, {{
            type: 'pie',
            data: {{
                labels: {list(summary['api_categories'].keys())},
                datasets: [{{
                    data: {list(summary['api_categories'].values())},
                    backgroundColor: ['#3498db', '#e74c3c', '#f39c12', '#2ecc71', '#9b59b6', '#1abc9c'],
                    borderWidth: 2,
                    borderColor: '#fff'
                }}]
            }},
            options: {{
                responsive: true,
                plugins: {{
                    legend: {{
                        position: 'right'
                    }}
                }}
            }}
        }});
        
        // Trends Chart (placeholder - would need time series data)
        const trendsCtx = document.getElementById('trendsChart').getContext('2d');
        new Chart(trendsCtx, {{
            type: 'line',
            data: {{
                labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
                datasets: [{{
                    label: 'API Calls',
                    data: [300000, 350000, 320000, 380000],
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    tension: 0.4,
                    fill: true
                }}]
            }},
            options: {{
                responsive: true,
                scales: {{
                    y: {{
                        beginAtZero: true
                    }}
                }}
            }}
        }});
    </script>
</body>
</html>
    """
    
    return html_content

def main():
    """Main function"""
    print("📊 Đang tạo báo cáo HTML cho API và Domain statistics...")
    
    # Load dữ liệu
    results = load_api_domain_results()
    
    # Tạo báo cáo HTML
    html_report = generate_api_domain_html_report(results)
    
    # Lưu báo cáo
    with open('api_domain_statistics_report.html', 'w', encoding='utf-8') as f:
        f.write(html_report)
    
    print("✅ Báo cáo HTML đã được tạo: api_domain_statistics_report.html")
    print(f"📊 Tổng kết:")
    print(f"   - API paths unique: {results['summary']['total_unique_apis']:,}")
    print(f"   - Domains unique: {results['summary']['total_unique_domains']}")
    print(f"   - Total API calls: {results['summary']['total_api_calls']:,}")

if __name__ == "__main__":
    main()
