#!/usr/bin/env python3
"""
Script phân tích chi tiết và tạo báo cáo HTML cho log tháng 7/2025
"""

import json
import os
from collections import Counter

def load_analysis_results():
    """Load kết quả phân tích từ file JSON"""
    with open('log_analysis_july_2025.json', 'r', encoding='utf-8') as f:
        return json.load(f)

def calculate_detailed_metrics(results):
    """Tính toán các metrics chi tiết"""
    metrics = {}
    
    # Tính tỷ lệ lỗi
    nginx_total = results['nginx']['requests']
    nginx_status = results['nginx']['status_codes']
    
    error_codes = [400, 401, 403, 404, 500, 501, 502, 503, 504]
    total_errors = sum(nginx_status.get(str(code), 0) for code in error_codes)
    success_requests = nginx_status.get('200', 0)
    
    metrics['error_rate'] = (total_errors / nginx_total * 100) if nginx_total > 0 else 0
    metrics['success_rate'] = (success_requests / nginx_total * 100) if nginx_total > 0 else 0
    
    # Tính băng thông trung bình per request
    total_bandwidth_mb = results['nginx']['bandwidth'] / (1024**2)  # MB
    metrics['avg_bandwidth_per_request'] = total_bandwidth_mb / nginx_total if nginx_total > 0 else 0
    
    # Phân tích theo ngày
    daily_analysis = {}
    for system in ['lgsp', 'nginx', 'wso2']:
        daily_stats = results[system]['daily_stats']
        daily_analysis[system] = {
            'peak_day': max(daily_stats.items(), key=lambda x: x[1].get('requests', 0)) if daily_stats else None,
            'min_day': min(daily_stats.items(), key=lambda x: x[1].get('requests', 0)) if daily_stats else None,
            'avg_daily': sum(day.get('requests', 0) for day in daily_stats.values()) / len(daily_stats) if daily_stats else 0
        }
    
    metrics['daily_analysis'] = daily_analysis
    
    return metrics

def generate_html_report(results, metrics):
    """Tạo báo cáo HTML chi tiết"""
    
    html_content = f"""
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Báo cáo phân tích Log tháng 7/2025</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        h1, h2, h3 {{ color: #2c3e50; }}
        .summary-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0; }}
        .metric-card {{ background: #ecf0f1; padding: 15px; border-radius: 8px; text-align: center; }}
        .metric-value {{ font-size: 2em; font-weight: bold; color: #3498db; }}
        .metric-label {{ color: #7f8c8d; margin-top: 5px; }}
        table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
        th, td {{ padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }}
        th {{ background-color: #34495e; color: white; }}
        .error {{ color: #e74c3c; }}
        .success {{ color: #27ae60; }}
        .warning {{ color: #f39c12; }}
        .chart-container {{ margin: 20px 0; padding: 20px; background: #f8f9fa; border-radius: 8px; }}
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 Báo cáo phân tích Log tháng 7/2025</h1>
        <p><strong>Thời gian phân tích:</strong> 01/07/2025 - 31/07/2025</p>
        <p><strong>Hệ thống:</strong> LGSP, WSO2, Nginx</p>
        
        <h2>🎯 Tổng quan</h2>
        <div class="summary-grid">
            <div class="metric-card">
                <div class="metric-value">{results['summary']['total_requests']:,}</div>
                <div class="metric-label">Tổng số Requests</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{results['summary']['total_bandwidth_gb']:.2f} GB</div>
                <div class="metric-label">Tổng băng thông</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{results['summary']['avg_daily_requests']:,.0f}</div>
                <div class="metric-label">Requests/ngày (TB)</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{metrics['success_rate']:.1f}%</div>
                <div class="metric-label">Tỷ lệ thành công</div>
            </div>
        </div>
        
        <h2>🔍 Chi tiết theo hệ thống</h2>
        
        <h3>🌐 Nginx (Web Server)</h3>
        <div class="summary-grid">
            <div class="metric-card">
                <div class="metric-value">{results['nginx']['requests']:,}</div>
                <div class="metric-label">Total Requests</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{results['nginx']['bandwidth'] / (1024**2):.1f} MB</div>
                <div class="metric-label">Băng thông</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{metrics['avg_bandwidth_per_request']:.2f} KB</div>
                <div class="metric-label">TB băng thông/request</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{metrics['error_rate']:.1f}%</div>
                <div class="metric-label">Tỷ lệ lỗi</div>
            </div>
        </div>
        
        <h4>📈 Status Codes</h4>
        <table>
            <thead>
                <tr><th>Status Code</th><th>Số lượng</th><th>Tỷ lệ (%)</th><th>Mô tả</th></tr>
            </thead>
            <tbody>
    """
    
    # Thêm bảng status codes
    status_descriptions = {
        '200': 'OK - Thành công',
        '400': 'Bad Request - Yêu cầu không hợp lệ', 
        '401': 'Unauthorized - Không có quyền',
        '403': 'Forbidden - Bị cấm',
        '404': 'Not Found - Không tìm thấy',
        '500': 'Internal Server Error - Lỗi server',
        '501': 'Not Implemented - Chưa được triển khai',
        '502': 'Bad Gateway - Gateway lỗi',
        '503': 'Service Unavailable - Dịch vụ không khả dụng'
    }
    
    total_nginx = results['nginx']['requests']
    for status, count in sorted(results['summary']['top_status_codes'].items()):
        percentage = (count / total_nginx * 100) if total_nginx > 0 else 0
        status_str = str(status)
        description = status_descriptions.get(status_str, 'Unknown')
        
        css_class = ""
        if status_str.startswith('2'):
            css_class = "success"
        elif status_str.startswith('4') or status_str.startswith('5'):
            css_class = "error"
        else:
            css_class = "warning"
            
        html_content += f"""
                <tr class="{css_class}">
                    <td>{status}</td>
                    <td>{count:,}</td>
                    <td>{percentage:.1f}%</td>
                    <td>{description}</td>
                </tr>
        """
    
    html_content += f"""
            </tbody>
        </table>
        
        <h3>🔐 WSO2 (API Gateway)</h3>
        <div class="summary-grid">
            <div class="metric-card">
                <div class="metric-value">{results['wso2']['requests']:,}</div>
                <div class="metric-label">Total Requests</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{len(results['wso2']['actions'])}</div>
                <div class="metric-label">Loại Actions</div>
            </div>
        </div>
        
        <h4>📊 Top Actions</h4>
        <table>
            <thead>
                <tr><th>Action</th><th>Số lượng</th><th>Tỷ lệ (%)</th></tr>
            </thead>
            <tbody>
    """
    
    total_wso2 = results['wso2']['requests']
    for action, count in results['summary']['top_wso2_actions'].items():
        percentage = (count / total_wso2 * 100) if total_wso2 > 0 else 0
        html_content += f"""
                <tr>
                    <td>{action}</td>
                    <td>{count:,}</td>
                    <td>{percentage:.1f}%</td>
                </tr>
        """
    
    html_content += f"""
            </tbody>
        </table>
        
        <h3>⚙️ LGSP (Core System)</h3>
        <div class="summary-grid">
            <div class="metric-card">
                <div class="metric-value">{results['lgsp']['requests']:,}</div>
                <div class="metric-label">Total Requests</div>
            </div>
            <div class="metric-card">
                <div class="metric-value error">{results['lgsp']['errors']:,}</div>
                <div class="metric-label">Errors</div>
            </div>
        </div>
        
        <h2>📈 Dự đoán và Khuyến nghị</h2>
        <div class="chart-container">
            <h3>🔮 Dự đoán tháng 7</h3>
            <ul>
                <li><strong>Tổng requests dự kiến:</strong> {results['summary']['predicted_monthly_requests']:,.0f}</li>
                <li><strong>Băng thông dự kiến:</strong> {results['summary']['predicted_monthly_bandwidth_gb']:.2f} GB</li>
                <li><strong>Peak load/ngày:</strong> ~{results['summary']['avg_daily_requests'] * 1.5:,.0f} requests</li>
            </ul>
            
            <h3>💡 Khuyến nghị</h3>
            <ul>
                <li><strong>Tối ưu hóa:</strong> Tỷ lệ lỗi 404 cao ({results['nginx']['status_codes'].get('404', 0):,} requests) - cần kiểm tra cấu hình routing</li>
                <li><strong>Bảo mật:</strong> Nhiều lỗi 401 ({results['nginx']['status_codes'].get('401', 0):,} requests) - cần review authentication</li>
                <li><strong>Hiệu năng:</strong> Băng thông trung bình {metrics['avg_bandwidth_per_request']:.2f} KB/request - trong mức bình thường</li>
                <li><strong>Monitoring:</strong> Thiết lập cảnh báo cho error rate > 10%</li>
            </ul>
        </div>
        
        <footer style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #7f8c8d;">
            <p>Báo cáo được tạo tự động từ log analysis script</p>
            <p>Thời gian tạo: {json.dumps(results['summary'], indent=2, default=str).count('2025-07')} ngày được phân tích</p>
        </footer>
    </div>
</body>
</html>
    """
    
    return html_content

def main():
    """Main function"""
    print("Đang tạo báo cáo chi tiết...")
    
    # Load kết quả phân tích
    results = load_analysis_results()
    
    # Tính toán metrics chi tiết
    metrics = calculate_detailed_metrics(results)
    
    # Tạo báo cáo HTML
    html_report = generate_html_report(results, metrics)
    
    # Lưu báo cáo
    with open('log_analysis_report_july_2025.html', 'w', encoding='utf-8') as f:
        f.write(html_report)
    
    print("✅ Báo cáo chi tiết đã được tạo: log_analysis_report_july_2025.html")
    print(f"📊 Tổng kết nhanh:")
    print(f"   - Tổng requests: {results['summary']['total_requests']:,}")
    print(f"   - Băng thông: {results['summary']['total_bandwidth_gb']:.2f} GB") 
    print(f"   - Tỷ lệ thành công: {metrics['success_rate']:.1f}%")
    print(f"   - Tỷ lệ lỗi: {metrics['error_rate']:.1f}%")

if __name__ == "__main__":
    main()
