
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>B<PERSON>o cáo phân tích Log tháng 7/2025 - SIÊU TOÀN DIỆN</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .container { max-width: 1600px; margin: 0 auto; background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.3); overflow: hidden; }
        .header { background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); color: white; padding: 30px; text-align: center; }
        .header h1 { margin: 0; font-size: 3em; font-weight: 300; }
        .header .subtitle { font-size: 1.3em; margin: 10px 0; opacity: 0.9; }
        .header .stats { font-size: 1.1em; margin: 15px 0; opacity: 0.8; }
        .content { padding: 30px; }
        .mega-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 30px 0; }
        .metric-card { background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 20px; border-radius: 12px; text-align: center; border-left: 5px solid #3498db; transition: all 0.3s ease; }
        .metric-card:hover { transform: translateY(-5px); box-shadow: 0 8px 25px rgba(0,0,0,0.15); }
        .metric-value { font-size: 2.2em; font-weight: bold; color: #2c3e50; margin-bottom: 5px; }
        .metric-label { color: #7f8c8d; font-size: 1em; }
        .system-section { margin: 40px 0; padding: 30px; background: #f8f9fa; border-radius: 15px; border-left: 6px solid #e74c3c; }
        .system-title { font-size: 2em; color: #2c3e50; margin-bottom: 25px; display: flex; align-items: center; }
        .system-icon { font-size: 1.3em; margin-right: 15px; }
        .wso2-subsection { margin: 25px 0; padding: 20px; background: white; border-radius: 10px; border-left: 4px solid #9b59b6; }
        .wso2-subsection h4 { color: #8e44ad; margin-bottom: 15px; font-size: 1.3em; }
        .chart-container { margin: 30px 0; padding: 25px; background: white; border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); }
        .chart-title { font-size: 1.6em; color: #2c3e50; margin-bottom: 20px; text-align: center; font-weight: 600; }
        .prediction-section { background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%); color: white; padding: 40px; border-radius: 15px; margin: 40px 0; }
        .prediction-title { font-size: 2.5em; margin-bottom: 30px; text-align: center; font-weight: 300; }
        .prediction-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 25px; }
        .prediction-card { background: rgba(255,255,255,0.15); padding: 25px; border-radius: 12px; text-align: center; backdrop-filter: blur(10px); }
        .prediction-value { font-size: 2.3em; font-weight: bold; margin-bottom: 8px; }
        .recommendation-section { background: linear-gradient(135deg, #00b894 0%, #00a085 100%); color: white; padding: 40px; border-radius: 15px; margin: 40px 0; }
        .recommendation-title { font-size: 2.5em; margin-bottom: 30px; text-align: center; font-weight: 300; }
        .recommendation-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .recommendation-card { background: rgba(255,255,255,0.15); padding: 20px; border-radius: 10px; backdrop-filter: blur(10px); }
        .recommendation-card h4 { margin-bottom: 15px; font-size: 1.2em; }
        .status-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        .status-table th, .status-table td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        .status-table th { background: #34495e; color: white; }
        .error { color: #e74c3c; font-weight: bold; }
        .success { color: #27ae60; font-weight: bold; }
        .warning { color: #f39c12; font-weight: bold; }
        .info { color: #3498db; font-weight: bold; }
        .footer { background: #2c3e50; color: white; padding: 30px; text-align: center; }
        .highlight-box { background: linear-gradient(135deg, #ff7675 0%, #d63031 100%); color: white; padding: 20px; border-radius: 10px; margin: 20px 0; }
        .success-box { background: linear-gradient(135deg, #00b894 0%, #00a085 100%); color: white; padding: 20px; border-radius: 10px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 Báo cáo phân tích Log tháng 7/2025</h1>
            <div class="subtitle">PHÂN TÍCH SIÊU TOÀN DIỆN - 3 hệ thống: LGSP, WSO2, Nginx</div>
            <div class="stats">
                📅 Thời gian: 01/07/2025 - 31/07/2025 | 
                📁 Files: 334 | 
                📊 Requests: 2,419,135 | 
                💾 Bandwidth: 4.46 GB
            </div>
        </div>
        
        <div class="content">
            <h2>🎯 Tổng quan siêu chi tiết</h2>
            <div class="mega-grid">
                <div class="metric-card">
                    <div class="metric-value">2,419,135</div>
                    <div class="metric-label">Tổng Requests</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">4.46 GB</div>
                    <div class="metric-label">Tổng băng thông</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">78,037</div>
                    <div class="metric-label">Requests/ngày (TB)</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">334</div>
                    <div class="metric-label">Files phân tích</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value error">28,601</div>
                    <div class="metric-label">Tổng Errors</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">1.2%</div>
                    <div class="metric-label">Error Rate</div>
                </div>
            </div>
            
            <div class="chart-container">
                <div class="chart-title">📈 Phân bố Requests theo hệ thống (Siêu chi tiết)</div>
                <canvas id="systemChart" width="400" height="200"></canvas>
            </div>
            
            <div class="system-section">
                <div class="system-title"><span class="system-icon">⚙️</span>LGSP (Core System)</div>
                <div class="highlight-box">
                    <h3>🚨 CẢNH BÁO NGHIÊM TRỌNG</h3>
                    <p>Hệ thống LGSP có tỷ lệ lỗi 100% - CẦN XỬ LÝ NGAY LẬP TỨC!</p>
                </div>
                <div class="mega-grid">
                    <div class="metric-card">
                        <div class="metric-value">124</div>
                        <div class="metric-label">Requests</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value error">124</div>
                        <div class="metric-label">Errors</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value error">100.0%</div>
                        <div class="metric-label">Error Rate</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">31</div>
                        <div class="metric-label">Log Files</div>
                    </div>
                </div>
            </div>
            
            <div class="system-section">
                <div class="system-title"><span class="system-icon">🌐</span>Nginx (Web Server)</div>
                <div class="success-box">
                    <h3>✅ HOẠT ĐỘNG ỔN ĐỊNH</h3>
                    <p>Nginx xử lý 1,584,232 requests (65.5% tổng traffic)</p>
                </div>
                <div class="mega-grid">
                    <div class="metric-card">
                        <div class="metric-value">1,584,232</div>
                        <div class="metric-label">Requests</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">4.46 GB</div>
                        <div class="metric-label">Băng thông</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">3021 bytes</div>
                        <div class="metric-label">TB Response Size</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">159</div>
                        <div class="metric-label">Log Files</div>
                    </div>
                </div>
                
                <div class="chart-container">
                    <div class="chart-title">📊 Nginx Status Codes Distribution</div>
                    <canvas id="nginxStatusChart" width="400" height="200"></canvas>
                </div>
            </div>
            
            <div class="system-section">
                <div class="system-title"><span class="system-icon">🔐</span>WSO2 (API Gateway) - SIÊU CHI TIẾT</div>
                <div class="success-box">
                    <h3>🔍 PHÂN TÍCH TOÀN DIỆN</h3>
                    <p>WSO2 với 144 files, 834,779 requests từ 6 loại log khác nhau</p>
                </div>
                
                <div class="mega-grid">
                    <div class="metric-card">
                        <div class="metric-value">834,779</div>
                        <div class="metric-label">Tổng Requests</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">144</div>
                        <div class="metric-label">Tổng Files</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value error">28,477</div>
                        <div class="metric-label">Tổng Errors</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">6</div>
                        <div class="metric-label">Loại Log</div>
                    </div>
                </div>
                
                <div class="chart-container">
                    <div class="chart-title">🔍 WSO2 File Types Breakdown</div>
                    <canvas id="wso2TypesChart" width="400" height="200"></canvas>
                </div>
                
                <div class="wso2-subsection">
                    <h4>📋 AUDIT LOGS (7 files, 9,261 requests)</h4>
                    <div class="mega-grid">
                        <div class="metric-card">
                            <div class="metric-value">9,261</div>
                            <div class="metric-label">Audit Requests</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">2</div>
                            <div class="metric-label">Unique Users</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">3</div>
                            <div class="metric-label">Action Types</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value success">9,241</div>
                            <div class="metric-label">Successful</div>
                        </div>
                    </div>
                </div>
                
                <div class="wso2-subsection">
                    <h4>🌐 HTTP ACCESS LOGS (62 files, 825,518 requests)</h4>
                    <div class="mega-grid">
                        <div class="metric-card">
                            <div class="metric-value">825,518</div>
                            <div class="metric-label">HTTP Requests</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">9</div>
                            <div class="metric-label">Status Types</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">1075</div>
                            <div class="metric-label">Endpoints</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">2</div>
                            <div class="metric-label">Unique IPs</div>
                        </div>
                    </div>
                </div>
                
                <div class="wso2-subsection">
                    <h4>❌ API GATEWAY ERRORS (31 files, 28,477 errors)</h4>
                    <div class="mega-grid">
                        <div class="metric-card">
                            <div class="metric-value error">28,477</div>
                            <div class="metric-label">Total Errors</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value error">1,827</div>
                            <div class="metric-label">ERROR Level</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value warning">771</div>
                            <div class="metric-label">WARN Level</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">9</div>
                            <div class="metric-label">Components</div>
                        </div>
                    </div>
                </div>
                
                <div class="wso2-subsection">
                    <h4>⚙️ CARBON LOGS (31 files, 44,068 logs)</h4>
                    <div class="mega-grid">
                        <div class="metric-card">
                            <div class="metric-value">44,068</div>
                            <div class="metric-label">Total Logs</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value info">18,222</div>
                            <div class="metric-label">INFO Level</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value error">1,827</div>
                            <div class="metric-label">ERROR Level</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">28</div>
                            <div class="metric-label">Components</div>
                        </div>
                    </div>
                </div>
                
                <div class="wso2-subsection">
                    <h4>🤖 BOT DETECTION (13 files, 40 detections)</h4>
                    <div class="mega-grid">
                        <div class="metric-card">
                            <div class="metric-value warning">40</div>
                            <div class="metric-label">Bot Detections</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">1</div>
                            <div class="metric-label">Unique IPs</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">12</div>
                            <div class="metric-label">POST Methods</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">12</div>
                            <div class="metric-label">GET Methods</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="prediction-section">
                <div class="prediction-title">🔮 Dự đoán tháng 8/2025</div>
                <div class="prediction-grid">
                    <div class="prediction-card">
                        <div class="prediction-value">2,854,579</div>
                        <div>Requests dự kiến</div>
                    </div>
                    <div class="prediction-card">
                        <div class="prediction-value">5.26 GB</div>
                        <div>Băng thông dự kiến</div>
                    </div>
                    <div class="prediction-card">
                        <div class="prediction-value">184,166</div>
                        <div>Peak load/ngày</div>
                    </div>
                    <div class="prediction-card">
                        <div class="prediction-value">+18%</div>
                        <div>Tăng trưởng dự kiến</div>
                    </div>
                    <div class="prediction-card">
                        <div class="prediction-value">985,039</div>
                        <div>WSO2 Requests</div>
                    </div>
                    <div class="prediction-card">
                        <div class="prediction-value">1,869,394</div>
                        <div>Nginx Requests</div>
                    </div>
                </div>
            </div>
            
            <div class="recommendation-section">
                <div class="recommendation-title">💡 Khuyến nghị hành động chi tiết</div>
                <div class="recommendation-grid">
                    <div class="recommendation-card">
                        <h4>🚨 KHẨN CẤP - LGSP</h4>
                        <ul>
                            <li>Kiểm tra ngay hệ thống LGSP (100% error rate)</li>
                            <li>Restart services và check logs</li>
                            <li>Verify database connections</li>
                            <li>Check network connectivity</li>
                        </ul>
                    </div>
                    <div class="recommendation-card">
                        <h4>🔧 Nginx Optimization</h4>
                        <ul>
                            <li>Tối ưu routing giảm 404 errors</li>
                            <li>Review authentication flow (401 errors)</li>
                            <li>Implement caching strategies</li>
                            <li>Setup load balancing</li>
                        </ul>
                    </div>
                    <div class="recommendation-card">
                        <h4>🔐 WSO2 Improvements</h4>
                        <ul>
                            <li>Monitor API Gateway errors</li>
                            <li>Optimize carbon logging</li>
                            <li>Enhance bot detection rules</li>
                            <li>Review audit policies</li>
                        </ul>
                    </div>
                    <div class="recommendation-card">
                        <h4>📈 Capacity Planning</h4>
                        <ul>
                            <li>CPU: 23 cores</li>
                            <li>RAM: 46 GB</li>
                            <li>Storage: 15.8 GB</li>
                            <li>Bandwidth: 50 Mbps</li>
                        </ul>
                    </div>
                    <div class="recommendation-card">
                        <h4>🏗️ Infrastructure</h4>
                        <ul>
                            <li>WSO2 instances: 3</li>
                            <li>Nginx instances: 3</li>
                            <li>Database scaling</li>
                            <li>Network optimization</li>
                        </ul>
                    </div>
                    <div class="recommendation-card">
                        <h4>📊 Monitoring</h4>
                        <ul>
                            <li>Setup alerts cho error rate > 5%</li>
                            <li>Real-time dashboard</li>
                            <li>Performance metrics</li>
                            <li>Automated reporting</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="chart-container">
                <div class="chart-title">📈 Dự đoán xu hướng tăng trưởng (3 tháng)</div>
                <canvas id="trendChart" width="400" height="200"></canvas>
            </div>
        </div>
        
        <div class="footer">
            <p>📅 Báo cáo được tạo tự động vào 05/08/2025 06:37:54</p>
            <p>🔧 Công cụ: Ultimate Log Analyzer | 📊 Dữ liệu: 334 files, 2,419,135 requests</p>
            <p>🎯 Phân tích: LGSP (31 files), Nginx (159 files), WSO2 (144 files - 6 loại log)</p>
        </div>
    </div>
    
    <script>
        // System Distribution Chart
        const systemCtx = document.getElementById('systemChart').getContext('2d');
        new Chart(systemCtx, {
            type: 'doughnut',
            data: {
                labels: ['Nginx (1,584,232)', 'WSO2 (834,779)', 'LGSP (124)'],
                datasets: [{
                    data: [1584232, 834779, 124],
                    backgroundColor: ['#3498db', '#e74c3c', '#f39c12'],
                    borderWidth: 3,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            font: {
                                size: 12
                            }
                        }
                    }
                }
            }
        });
        
        // WSO2 Types Chart
        const wso2TypesCtx = document.getElementById('wso2TypesChart').getContext('2d');
        new Chart(wso2TypesCtx, {
            type: 'bar',
            data: {
                labels: ['HTTP Access', 'Carbon', 'Errors', 'Audit', 'Bot Detection'],
                datasets: [{
                    label: 'Events/Requests',
                    data: [825518, 44068, 28477, 9261, 40],
                    backgroundColor: ['#3498db', '#2ecc71', '#e74c3c', '#f39c12', '#9b59b6'],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        type: 'logarithmic'
                    }
                }
            }
        });
        
        // Nginx Status Chart
        const nginxStatusCtx = document.getElementById('nginxStatusChart').getContext('2d');
        new Chart(nginxStatusCtx, {
            type: 'bar',
            data: {
                labels: ['200 (OK)', '404 (Not Found)', '401 (Unauthorized)', '400 (Bad Request)', 'Others'],
                datasets: [{
                    label: 'Requests',
                    data: [247706, 661892, 197312, 24912, 452410],
                    backgroundColor: ['#27ae60', '#e74c3c', '#f39c12', '#e67e22', '#95a5a6'],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
        
        // Trend Chart
        const trendCtx = document.getElementById('trendChart').getContext('2d');
        new Chart(trendCtx, {
            type: 'line',
            data: {
                labels: ['Tháng 7 (Thực tế)', 'Tháng 8 (Dự đoán)', 'Tháng 9 (Dự đoán)', 'Tháng 10 (Dự đoán)'],
                datasets: [{
                    label: 'Total Requests',
                    data: [2419135, 2854579, 3368404, 3967865],
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: 'WSO2 Requests',
                    data: [834779, 985039, 1162346, 1369205],
                    borderColor: '#e74c3c',
                    backgroundColor: 'rgba(231, 76, 60, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: 'Nginx Requests',
                    data: [1584232, 1869394, 2205885, 2598457],
                    borderColor: '#2ecc71',
                    backgroundColor: 'rgba(46, 204, 113, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    </script>
</body>
</html>
    