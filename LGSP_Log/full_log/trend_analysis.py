#!/usr/bin/env python3
"""
Script phân tích xu hướng và dự đoán cho log tháng 7/2025
"""

import json
import os
from datetime import datetime, timedelta
from collections import defaultdict

def load_analysis_results():
    """Load kết quả phân tích từ file JSON"""
    with open('log_analysis_july_2025.json', 'r', encoding='utf-8') as f:
        return json.load(f)

def analyze_daily_trends(results):
    """Phân tích xu hướng theo ngày"""
    trends = {
        'nginx': {'dates': [], 'requests': [], 'bandwidth': []},
        'wso2': {'dates': [], 'requests': []},
        'lgsp': {'dates': [], 'requests': []}
    }
    
    # Phân tích Nginx
    nginx_daily = results['nginx']['daily_stats']
    for date in sorted(nginx_daily.keys()):
        trends['nginx']['dates'].append(date)
        trends['nginx']['requests'].append(nginx_daily[date]['requests'])
        trends['nginx']['bandwidth'].append(nginx_daily[date]['bandwidth'])
    
    # Phân tích WSO2
    wso2_daily = results['wso2']['daily_stats']
    for date in sorted(wso2_daily.keys()):
        trends['wso2']['dates'].append(date)
        trends['wso2']['requests'].append(wso2_daily[date]['requests'])
    
    # Phân tích LGSP
    lgsp_daily = results['lgsp']['daily_stats']
    for date in sorted(lgsp_daily.keys()):
        trends['lgsp']['dates'].append(date)
        trends['lgsp']['requests'].append(lgsp_daily[date]['requests'])
    
    return trends

def calculate_growth_rate(data_points):
    """Tính tỷ lệ tăng trưởng"""
    if len(data_points) < 2:
        return 0
    
    # Tính tỷ lệ tăng trưởng trung bình
    growth_rates = []
    for i in range(1, len(data_points)):
        if data_points[i-1] > 0:
            growth_rate = (data_points[i] - data_points[i-1]) / data_points[i-1] * 100
            growth_rates.append(growth_rate)
    
    return sum(growth_rates) / len(growth_rates) if growth_rates else 0

def predict_future_load(trends, days_ahead=30):
    """Dự đoán tải trong tương lai"""
    predictions = {}
    
    for system, data in trends.items():
        if not data['requests']:
            continue
            
        requests = data['requests']
        
        # Tính trung bình và xu hướng
        avg_requests = sum(requests) / len(requests)
        growth_rate = calculate_growth_rate(requests)
        
        # Dự đoán cho 30 ngày tới
        predicted_daily = avg_requests * (1 + growth_rate/100)
        predicted_monthly = predicted_daily * days_ahead
        
        predictions[system] = {
            'current_avg_daily': avg_requests,
            'growth_rate_percent': growth_rate,
            'predicted_daily': predicted_daily,
            'predicted_monthly': predicted_monthly
        }
        
        if system == 'nginx' and data.get('bandwidth'):
            bandwidth = data['bandwidth']
            avg_bandwidth = sum(bandwidth) / len(bandwidth)
            bandwidth_growth = calculate_growth_rate(bandwidth)
            predicted_bandwidth_daily = avg_bandwidth * (1 + bandwidth_growth/100)
            predicted_bandwidth_monthly = predicted_bandwidth_daily * days_ahead
            
            predictions[system]['current_avg_bandwidth_daily'] = avg_bandwidth
            predictions[system]['bandwidth_growth_rate_percent'] = bandwidth_growth
            predictions[system]['predicted_bandwidth_daily'] = predicted_bandwidth_daily
            predictions[system]['predicted_bandwidth_monthly'] = predicted_bandwidth_monthly
    
    return predictions

def analyze_peak_hours(results):
    """Phân tích giờ cao điểm từ log"""
    hourly_stats = defaultdict(int)
    
    # Phân tích từ nginx log (có timestamp chi tiết nhất)
    nginx_daily = results['nginx']['daily_stats']
    
    # Giả định phân bố đều trong ngày (do không có dữ liệu giờ chi tiết)
    # Trong thực tế, cần parse timestamp từ log để có dữ liệu chính xác
    total_requests = sum(day['requests'] for day in nginx_daily.values())
    avg_requests_per_hour = total_requests / (len(nginx_daily) * 24)
    
    # Giả định pattern cao điểm (8-17h là giờ làm việc)
    peak_hours = {
        'work_hours': list(range(8, 18)),  # 8AM - 5PM
        'peak_multiplier': 1.5,  # Cao hơn 50% so với trung bình
        'off_hours_multiplier': 0.3  # Thấp hơn 70% so với trung bình
    }
    
    return {
        'avg_requests_per_hour': avg_requests_per_hour,
        'peak_hours': peak_hours,
        'estimated_peak_load': avg_requests_per_hour * peak_hours['peak_multiplier']
    }

def calculate_bandwidth_requirements(predictions):
    """Tính toán yêu cầu băng thông"""
    if 'nginx' not in predictions:
        return {}
    
    nginx_pred = predictions['nginx']
    
    # Chuyển đổi đơn vị
    daily_bandwidth_gb = nginx_pred.get('predicted_bandwidth_daily', 0) / (1024**3)
    monthly_bandwidth_gb = nginx_pred.get('predicted_bandwidth_monthly', 0) / (1024**3)
    
    # Tính băng thông peak (giả định peak cao hơn 200% so với trung bình)
    peak_bandwidth_gbps = (daily_bandwidth_gb / 24 / 3600) * 3  # GB/s
    
    return {
        'daily_bandwidth_gb': daily_bandwidth_gb,
        'monthly_bandwidth_gb': monthly_bandwidth_gb,
        'peak_bandwidth_gbps': peak_bandwidth_gbps,
        'recommended_bandwidth_gbps': peak_bandwidth_gbps * 1.2  # Buffer 20%
    }

def generate_trend_report(results, trends, predictions, peak_analysis, bandwidth_req):
    """Tạo báo cáo xu hướng chi tiết"""
    
    print("\n" + "="*100)
    print("📈 BÁO CÁO XU HƯỚNG VÀ DỰ ĐOÁN THÁNG 7/2025")
    print("="*100)
    
    print(f"\n🎯 TỔNG QUAN HIỆN TẠI:")
    print(f"   📊 Tổng requests đã xử lý: {results['summary']['total_requests']:,}")
    print(f"   💾 Tổng băng thông đã sử dụng: {results['summary']['total_bandwidth_gb']:.2f} GB")
    print(f"   📅 Trung bình requests/ngày: {results['summary']['avg_daily_requests']:,.0f}")
    
    print(f"\n📈 XU HƯỚNG TĂNG TRƯỞNG:")
    for system, pred in predictions.items():
        growth = pred['growth_rate_percent']
        status = "📈 Tăng" if growth > 0 else "📉 Giảm" if growth < 0 else "➡️ Ổn định"
        print(f"   {system.upper()}: {status} {abs(growth):.1f}%/ngày")
    
    print(f"\n🔮 DỰ ĐOÁN 30 NGÀY TỚI:")
    total_predicted = sum(pred['predicted_monthly'] for pred in predictions.values())
    print(f"   📊 Tổng requests dự kiến: {total_predicted:,.0f}")
    
    if 'nginx' in predictions:
        nginx_pred = predictions['nginx']
        print(f"   💾 Băng thông dự kiến: {bandwidth_req['monthly_bandwidth_gb']:.2f} GB")
        print(f"   ⚡ Peak load dự kiến: {nginx_pred['predicted_daily'] * 1.5:,.0f} requests/ngày")
    
    print(f"\n⏰ PHÂN TÍCH GIỜ CAO ĐIỂM:")
    print(f"   📈 Trung bình: {peak_analysis['avg_requests_per_hour']:,.0f} requests/giờ")
    print(f"   🔥 Giờ cao điểm (8-17h): {peak_analysis['estimated_peak_load']:,.0f} requests/giờ")
    print(f"   🌙 Giờ thấp điểm: {peak_analysis['avg_requests_per_hour'] * 0.3:,.0f} requests/giờ")
    
    print(f"\n🌐 YÊU CẦU BĂNG THÔNG:")
    print(f"   📊 Trung bình: {bandwidth_req['daily_bandwidth_gb']/24:.4f} GB/giờ")
    print(f"   🔥 Peak: {bandwidth_req['peak_bandwidth_gbps']*1000:.2f} MB/s")
    print(f"   💡 Khuyến nghị: {bandwidth_req['recommended_bandwidth_gbps']*1000:.2f} MB/s")
    
    print(f"\n🎯 CHI TIẾT THEO HỆ THỐNG:")
    for system, pred in predictions.items():
        print(f"\n   🔧 {system.upper()}:")
        print(f"      - Hiện tại: {pred['current_avg_daily']:,.0f} requests/ngày")
        print(f"      - Dự đoán: {pred['predicted_daily']:,.0f} requests/ngày")
        print(f"      - Tăng trưởng: {pred['growth_rate_percent']:+.1f}%")
        
        if system == 'nginx' and 'current_avg_bandwidth_daily' in pred:
            print(f"      - Băng thông hiện tại: {pred['current_avg_bandwidth_daily']/(1024**2):.1f} MB/ngày")
            print(f"      - Băng thông dự đoán: {pred['predicted_bandwidth_daily']/(1024**2):.1f} MB/ngày")
    
    print(f"\n💡 KHUYẾN NGHỊ HÀNH ĐỘNG:")
    
    # Phân tích status code để đưa ra khuyến nghị
    error_rate = sum(results['nginx']['status_codes'].get(str(code), 0) 
                    for code in [400, 401, 403, 404, 500, 501, 502, 503, 504])
    total_nginx = results['nginx']['requests']
    error_percentage = (error_rate / total_nginx * 100) if total_nginx > 0 else 0
    
    print(f"   🚨 Tỷ lệ lỗi hiện tại: {error_percentage:.1f}%")
    
    if error_percentage > 50:
        print(f"   ⚠️  KHẨN CẤP: Tỷ lệ lỗi quá cao! Cần kiểm tra ngay:")
        print(f"      - Lỗi 404: {results['nginx']['status_codes'].get('404', 0):,} requests")
        print(f"      - Lỗi 401: {results['nginx']['status_codes'].get('401', 0):,} requests")
        print(f"      - Kiểm tra cấu hình routing và authentication")
    
    if total_predicted > results['summary']['total_requests'] * 1.2:
        print(f"   📈 Chuẩn bị scale up: Dự kiến tăng >20%")
        print(f"      - Tăng cường server capacity")
        print(f"      - Tối ưu hóa database")
        print(f"      - Cân nhắc load balancing")
    
    print(f"   🔧 Tối ưu hóa:")
    print(f"      - Caching: Giảm tải cho backend")
    print(f"      - CDN: Tối ưu băng thông")
    print(f"      - Monitoring: Thiết lập alerts")
    print(f"      - Security: Review authentication flow")

def main():
    """Main function"""
    print("Đang phân tích xu hướng và tạo dự đoán...")
    
    # Load dữ liệu
    results = load_analysis_results()
    
    # Phân tích xu hướng
    trends = analyze_daily_trends(results)
    
    # Dự đoán tương lai
    predictions = predict_future_load(trends)
    
    # Phân tích giờ cao điểm
    peak_analysis = analyze_peak_hours(results)
    
    # Tính toán yêu cầu băng thông
    bandwidth_req = calculate_bandwidth_requirements(predictions)
    
    # Tạo báo cáo
    generate_trend_report(results, trends, predictions, peak_analysis, bandwidth_req)
    
    # Lưu kết quả dự đoán
    prediction_data = {
        'trends': trends,
        'predictions': predictions,
        'peak_analysis': peak_analysis,
        'bandwidth_requirements': bandwidth_req,
        'generated_at': datetime.now().isoformat()
    }
    
    with open('trend_predictions_july_2025.json', 'w', encoding='utf-8') as f:
        json.dump(prediction_data, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"\n✅ Phân tích xu hướng hoàn tất!")
    print(f"📁 Kết quả đã lưu: trend_predictions_july_2025.json")

if __name__ == "__main__":
    main()
