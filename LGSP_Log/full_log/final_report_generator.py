#!/usr/bin/env python3
"""
Tạo báo cáo HTML cuối cùng với biểu đồ và dự đoán chi tiết
"""

import json
import os
from datetime import datetime

def load_comprehensive_results():
    """Load kết quả phân tích toàn diện"""
    with open('comprehensive_log_analysis_july_2025.json', 'r', encoding='utf-8') as f:
        return json.load(f)

def calculate_predictions(results):
    """Tính toán dự đoán chi tiết"""
    summary = results['summary']
    
    # Dự đoán cho tháng 8
    current_daily_avg = summary['avg_daily_requests']
    growth_rate = 0.15  # <PERSON><PERSON><PERSON> định tăng trưởng 15% dựa trên xu hướng
    
    predicted_august = {
        'daily_requests': current_daily_avg * (1 + growth_rate),
        'monthly_requests': current_daily_avg * (1 + growth_rate) * 31,
        'daily_bandwidth_gb': summary['avg_daily_bandwidth_gb'] * (1 + growth_rate),
        'monthly_bandwidth_gb': summary['avg_daily_bandwidth_gb'] * (1 + growth_rate) * 31,
        'peak_daily_requests': current_daily_avg * (1 + growth_rate) * 1.5,  # Peak 50% higher
        'growth_rate_percent': growth_rate * 100
    }
    
    # Dự đoán tài nguyên cần thiết
    resource_requirements = {
        'cpu_cores': max(4, int(predicted_august['peak_daily_requests'] / 10000)),  # 1 core per 10k requests
        'ram_gb': max(8, int(predicted_august['peak_daily_requests'] / 5000)),     # 1GB per 5k requests
        'storage_gb': predicted_august['monthly_bandwidth_gb'] * 2,                # 2x bandwidth for storage
        'bandwidth_mbps': max(10, int(predicted_august['daily_bandwidth_gb'] * 1024 / 24 / 3600 * 8 * 3))  # 3x peak bandwidth
    }
    
    return predicted_august, resource_requirements

def generate_final_html_report(results, predictions, resources):
    """Tạo báo cáo HTML cuối cùng với biểu đồ"""
    
    summary = results['summary']
    lgsp = summary['systems_breakdown']['lgsp']
    nginx = summary['systems_breakdown']['nginx']
    wso2 = summary['systems_breakdown']['wso2']
    
    html_content = f"""
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Báo cáo phân tích Log tháng 7/2025 - Toàn diện</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }}
        .container {{ max-width: 1400px; margin: 0 auto; background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.3); overflow: hidden; }}
        .header {{ background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); color: white; padding: 30px; text-align: center; }}
        .header h1 {{ margin: 0; font-size: 2.5em; font-weight: 300; }}
        .header p {{ margin: 10px 0 0 0; opacity: 0.9; font-size: 1.1em; }}
        .content {{ padding: 30px; }}
        .summary-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 30px 0; }}
        .metric-card {{ background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 25px; border-radius: 12px; text-align: center; border-left: 5px solid #3498db; transition: transform 0.3s ease; }}
        .metric-card:hover {{ transform: translateY(-5px); box-shadow: 0 5px 20px rgba(0,0,0,0.1); }}
        .metric-value {{ font-size: 2.5em; font-weight: bold; color: #2c3e50; margin-bottom: 5px; }}
        .metric-label {{ color: #7f8c8d; font-size: 1.1em; }}
        .system-section {{ margin: 40px 0; padding: 25px; background: #f8f9fa; border-radius: 12px; border-left: 5px solid #e74c3c; }}
        .system-title {{ font-size: 1.8em; color: #2c3e50; margin-bottom: 20px; display: flex; align-items: center; }}
        .system-icon {{ font-size: 1.2em; margin-right: 10px; }}
        .chart-container {{ margin: 30px 0; padding: 20px; background: white; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        .chart-title {{ font-size: 1.5em; color: #2c3e50; margin-bottom: 20px; text-align: center; }}
        .prediction-section {{ background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%); color: white; padding: 30px; border-radius: 12px; margin: 30px 0; }}
        .prediction-title {{ font-size: 2em; margin-bottom: 20px; text-align: center; }}
        .prediction-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; }}
        .prediction-card {{ background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px; text-align: center; }}
        .prediction-value {{ font-size: 2em; font-weight: bold; margin-bottom: 5px; }}
        .recommendation-section {{ background: linear-gradient(135deg, #00b894 0%, #00a085 100%); color: white; padding: 30px; border-radius: 12px; margin: 30px 0; }}
        .recommendation-title {{ font-size: 2em; margin-bottom: 20px; text-align: center; }}
        .recommendation-list {{ list-style: none; padding: 0; }}
        .recommendation-list li {{ background: rgba(255,255,255,0.1); margin: 10px 0; padding: 15px; border-radius: 8px; border-left: 4px solid #fff; }}
        .status-table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
        .status-table th, .status-table td {{ padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }}
        .status-table th {{ background: #34495e; color: white; }}
        .error {{ color: #e74c3c; font-weight: bold; }}
        .success {{ color: #27ae60; font-weight: bold; }}
        .warning {{ color: #f39c12; font-weight: bold; }}
        .footer {{ background: #2c3e50; color: white; padding: 20px; text-align: center; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 Báo cáo phân tích Log tháng 7/2025</h1>
            <p>Phân tích toàn diện 3 hệ thống: LGSP, WSO2, Nginx</p>
            <p>Thời gian: 01/07/2025 - 31/07/2025 | Files: {summary['total_files_analyzed']} | Requests: {summary['total_requests']:,}</p>
        </div>
        
        <div class="content">
            <h2>🎯 Tổng quan hệ thống</h2>
            <div class="summary-grid">
                <div class="metric-card">
                    <div class="metric-value">{summary['total_requests']:,}</div>
                    <div class="metric-label">Tổng Requests</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{summary['total_bandwidth_gb']:.2f} GB</div>
                    <div class="metric-label">Tổng băng thông</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{summary['avg_daily_requests']:,.0f}</div>
                    <div class="metric-label">Requests/ngày (TB)</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{summary['total_files_analyzed']}</div>
                    <div class="metric-label">Files đã phân tích</div>
                </div>
            </div>
            
            <div class="chart-container">
                <div class="chart-title">📈 Phân bố Requests theo hệ thống</div>
                <canvas id="systemChart" width="400" height="200"></canvas>
            </div>
            
            <div class="system-section">
                <div class="system-title"><span class="system-icon">⚙️</span>LGSP (Core System)</div>
                <div class="summary-grid">
                    <div class="metric-card">
                        <div class="metric-value">{lgsp['requests']:,}</div>
                        <div class="metric-label">Requests</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value error">{lgsp['errors']:,}</div>
                        <div class="metric-label">Errors</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value error">{lgsp['error_rate']:.1f}%</div>
                        <div class="metric-label">Error Rate</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{lgsp['files']}</div>
                        <div class="metric-label">Log Files</div>
                    </div>
                </div>
                <p><strong>⚠️ Cảnh báo:</strong> Tỷ lệ lỗi 100% cho thấy hệ thống LGSP đang gặp vấn đề nghiêm trọng cần xử lý ngay!</p>
            </div>
            
            <div class="system-section">
                <div class="system-title"><span class="system-icon">🌐</span>Nginx (Web Server)</div>
                <div class="summary-grid">
                    <div class="metric-card">
                        <div class="metric-value">{nginx['requests']:,}</div>
                        <div class="metric-label">Requests</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{nginx['bandwidth_gb']:.2f} GB</div>
                        <div class="metric-label">Băng thông</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{nginx['avg_response_size']:.0f} bytes</div>
                        <div class="metric-label">TB Response Size</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{nginx['files']}</div>
                        <div class="metric-label">Log Files</div>
                    </div>
                </div>
                
                <div class="chart-container">
                    <div class="chart-title">📊 Status Codes Distribution</div>
                    <canvas id="statusChart" width="400" height="200"></canvas>
                </div>
            </div>
            
            <div class="system-section">
                <div class="system-title"><span class="system-icon">🔐</span>WSO2 (API Gateway)</div>
                <div class="summary-grid">
                    <div class="metric-card">
                        <div class="metric-value">{wso2['requests']:,}</div>
                        <div class="metric-label">Requests</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{wso2['unique_users']}</div>
                        <div class="metric-label">Unique Users</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{wso2['unique_actions']}</div>
                        <div class="metric-label">Action Types</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{wso2['files']}</div>
                        <div class="metric-label">Log Files</div>
                    </div>
                </div>
            </div>
            
            <div class="prediction-section">
                <div class="prediction-title">🔮 Dự đoán tháng 8/2025</div>
                <div class="prediction-grid">
                    <div class="prediction-card">
                        <div class="prediction-value">{predictions['monthly_requests']:,.0f}</div>
                        <div>Requests dự kiến</div>
                    </div>
                    <div class="prediction-card">
                        <div class="prediction-value">{predictions['monthly_bandwidth_gb']:.2f} GB</div>
                        <div>Băng thông dự kiến</div>
                    </div>
                    <div class="prediction-card">
                        <div class="prediction-value">{predictions['peak_daily_requests']:,.0f}</div>
                        <div>Peak load/ngày</div>
                    </div>
                    <div class="prediction-card">
                        <div class="prediction-value">+{predictions['growth_rate_percent']:.0f}%</div>
                        <div>Tăng trưởng dự kiến</div>
                    </div>
                </div>
            </div>
            
            <div class="recommendation-section">
                <div class="recommendation-title">💡 Khuyến nghị hành động</div>
                <ul class="recommendation-list">
                    <li><strong>🚨 KHẨN CẤP - LGSP:</strong> Kiểm tra và sửa lỗi hệ thống LGSP ngay lập tức (100% error rate)</li>
                    <li><strong>🔧 Nginx Optimization:</strong> Tối ưu hóa routing để giảm 404 errors (58% requests)</li>
                    <li><strong>🔐 Security Review:</strong> Kiểm tra authentication flow (17.3% requests bị 401)</li>
                    <li><strong>📈 Capacity Planning:</strong> Chuẩn bị tăng {resources['cpu_cores']} CPU cores, {resources['ram_gb']} GB RAM</li>
                    <li><strong>💾 Storage:</strong> Dự trữ {resources['storage_gb']:.1f} GB storage cho tháng 8</li>
                    <li><strong>🌐 Bandwidth:</strong> Nâng cấp bandwidth lên {resources['bandwidth_mbps']} Mbps</li>
                    <li><strong>📊 Monitoring:</strong> Thiết lập alerts cho error rate > 5%</li>
                    <li><strong>🔄 Load Balancing:</strong> Cân nhắc triển khai load balancer cho peak load</li>
                </ul>
            </div>
            
            <div class="chart-container">
                <div class="chart-title">📈 Dự đoán xu hướng tăng trưởng</div>
                <canvas id="trendChart" width="400" height="200"></canvas>
            </div>
        </div>
        
        <div class="footer">
            <p>📅 Báo cáo được tạo tự động vào {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}</p>
            <p>🔧 Công cụ: Comprehensive Log Analyzer | 📊 Dữ liệu: {summary['total_files_analyzed']} files, {summary['total_requests']:,} requests</p>
        </div>
    </div>
    
    <script>
        // System Distribution Chart
        const systemCtx = document.getElementById('systemChart').getContext('2d');
        new Chart(systemCtx, {{
            type: 'doughnut',
            data: {{
                labels: ['Nginx', 'WSO2', 'LGSP'],
                datasets: [{{
                    data: [{nginx['requests']}, {wso2['requests']}, {lgsp['requests']}],
                    backgroundColor: ['#3498db', '#e74c3c', '#f39c12'],
                    borderWidth: 2,
                    borderColor: '#fff'
                }}]
            }},
            options: {{
                responsive: true,
                plugins: {{
                    legend: {{
                        position: 'bottom'
                    }}
                }}
            }}
        }});
        
        // Status Codes Chart
        const statusCtx = document.getElementById('statusChart').getContext('2d');
        new Chart(statusCtx, {{
            type: 'bar',
            data: {{
                labels: ['200 (OK)', '404 (Not Found)', '401 (Unauthorized)', '400 (Bad Request)', 'Others'],
                datasets: [{{
                    label: 'Requests',
                    data: [247706, 661892, 197312, 24912, 10134],
                    backgroundColor: ['#27ae60', '#e74c3c', '#f39c12', '#e67e22', '#95a5a6'],
                    borderWidth: 1
                }}]
            }},
            options: {{
                responsive: true,
                scales: {{
                    y: {{
                        beginAtZero: true
                    }}
                }}
            }}
        }});
        
        // Trend Chart
        const trendCtx = document.getElementById('trendChart').getContext('2d');
        new Chart(trendCtx, {{
            type: 'line',
            data: {{
                labels: ['Tháng 7 (Thực tế)', 'Tháng 8 (Dự đoán)', 'Tháng 9 (Dự đoán)', 'Tháng 10 (Dự đoán)'],
                datasets: [{{
                    label: 'Requests/tháng',
                    data: [{summary['total_requests']}, {predictions['monthly_requests']:.0f}, {predictions['monthly_requests'] * 1.15:.0f}, {predictions['monthly_requests'] * 1.32:.0f}],
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    tension: 0.4,
                    fill: true
                }}, {{
                    label: 'Băng thông (GB)',
                    data: [{summary['total_bandwidth_gb']}, {predictions['monthly_bandwidth_gb']:.2f}, {predictions['monthly_bandwidth_gb'] * 1.15:.2f}, {predictions['monthly_bandwidth_gb'] * 1.32:.2f}],
                    borderColor: '#e74c3c',
                    backgroundColor: 'rgba(231, 76, 60, 0.1)',
                    tension: 0.4,
                    fill: true,
                    yAxisID: 'y1'
                }}]
            }},
            options: {{
                responsive: true,
                scales: {{
                    y: {{
                        type: 'linear',
                        display: true,
                        position: 'left',
                    }},
                    y1: {{
                        type: 'linear',
                        display: true,
                        position: 'right',
                        grid: {{
                            drawOnChartArea: false,
                        }},
                    }}
                }}
            }}
        }});
    </script>
</body>
</html>
    """
    
    return html_content

def main():
    """Main function"""
    print("📊 Đang tạo báo cáo cuối cùng...")
    
    # Load dữ liệu
    results = load_comprehensive_results()
    
    # Tính toán dự đoán
    predictions, resources = calculate_predictions(results)
    
    # Tạo báo cáo HTML
    html_report = generate_final_html_report(results, predictions, resources)
    
    # Lưu báo cáo
    with open('final_comprehensive_report_july_2025.html', 'w', encoding='utf-8') as f:
        f.write(html_report)
    
    print("✅ Báo cáo cuối cùng đã được tạo: final_comprehensive_report_july_2025.html")
    print(f"📊 Tổng kết:")
    print(f"   - Files phân tích: {results['summary']['total_files_analyzed']}")
    print(f"   - Tổng requests: {results['summary']['total_requests']:,}")
    print(f"   - Băng thông: {results['summary']['total_bandwidth_gb']:.2f} GB")
    print(f"   - Dự đoán tháng 8: {predictions['monthly_requests']:,.0f} requests")
    print(f"   - Tài nguyên cần: {resources['cpu_cores']} cores, {resources['ram_gb']} GB RAM")

if __name__ == "__main__":
    main()
