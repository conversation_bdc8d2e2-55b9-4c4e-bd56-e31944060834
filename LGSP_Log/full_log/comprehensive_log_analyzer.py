#!/usr/bin/env python3
"""
Script phân tích toàn diện tất cả file log tháng 7/2025
Hỗ trợ nhiều định dạng log khác nhau trong từng hệ thống
"""

import os
import re
import gzip
import json
from datetime import datetime, timedelta
from collections import defaultdict, Counter
import glob

class ComprehensiveLogAnalyzer:
    def __init__(self, base_path):
        self.base_path = base_path
        self.lgsp_path = os.path.join(base_path, "lgsp_log")
        self.nginx_path = os.path.join(base_path, "nginx_log") 
        self.wso2_path = os.path.join(base_path, "wso2_log")
        
        # <PERSON><PERSON>t quả phân tích chi tiết
        self.results = {
            'lgsp': {
                'total_files': 0,
                'total_requests': 0,
                'total_errors': 0,
                'daily_stats': {},
                'file_types': {},
                'api_endpoints': Counter(),
                'error_types': Counter()
            },
            'nginx': {
                'total_files': 0,
                'total_requests': 0,
                'total_bandwidth': 0,
                'status_codes': Counter(),
                'daily_stats': {},
                'file_types': {},
                'top_ips': Counter(),
                'top_endpoints': Counter(),
                'user_agents': Counter()
            },
            'wso2': {
                'total_files': 0,
                'total_requests': 0,
                'actions': Counter(),
                'daily_stats': {},
                'file_types': {},
                'users': Counter(),
                'outcomes': Counter()
            },
            'summary': {}
        }

    def discover_log_files(self):
        """Khám phá tất cả file log trong từng thư mục"""
        print("🔍 Đang khám phá cấu trúc file log...")
        
        # LGSP files
        lgsp_files = glob.glob(os.path.join(self.lgsp_path, "*.log"))
        lgsp_july_files = [f for f in lgsp_files if "072025" in os.path.basename(f)]
        
        # Nginx files  
        nginx_files = glob.glob(os.path.join(self.nginx_path, "*.log*"))
        nginx_july_files = [f for f in nginx_files if "202507" in os.path.basename(f) or "access.log" in os.path.basename(f)]
        
        # WSO2 files
        wso2_files = glob.glob(os.path.join(self.wso2_path, "*.log"))
        wso2_july_files = [f for f in wso2_files if "-07-2025" in os.path.basename(f)]
        
        print(f"📁 LGSP: {len(lgsp_july_files)} files tháng 7")
        print(f"📁 Nginx: {len(nginx_july_files)} files tháng 7") 
        print(f"📁 WSO2: {len(wso2_july_files)} files tháng 7")
        
        return {
            'lgsp': lgsp_july_files,
            'nginx': nginx_july_files,
            'wso2': wso2_july_files
        }

    def analyze_lgsp_file(self, filepath):
        """Phân tích một file log LGSP"""
        filename = os.path.basename(filepath)
        file_stats = {
            'filename': filename,
            'requests': 0,
            'errors': 0,
            'api_calls': Counter(),
            'error_details': []
        }
        
        try:
            with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line:
                        continue
                        
                    # Đếm requests
                    if '[REQUEST]' in line:
                        file_stats['requests'] += 1
                        self.results['lgsp']['total_requests'] += 1
                        
                        # Extract API endpoint
                        api_match = re.search(r'"apiPath":"([^"]+)"', line)
                        if api_match:
                            api_path = api_match.group(1)
                            file_stats['api_calls'][api_path] += 1
                            self.results['lgsp']['api_endpoints'][api_path] += 1
                    
                    # Đếm errors
                    if any(error_keyword in line for error_keyword in ['Exception', 'ERROR', 'Error', 'FAILED']):
                        file_stats['errors'] += 1
                        self.results['lgsp']['total_errors'] += 1
                        
                        # Extract error type
                        if 'Exception' in line:
                            error_match = re.search(r'(\w+Exception)', line)
                            if error_match:
                                error_type = error_match.group(1)
                                self.results['lgsp']['error_types'][error_type] += 1
                        
                        file_stats['error_details'].append({
                            'line': line_num,
                            'content': line[:200] + '...' if len(line) > 200 else line
                        })
                        
        except Exception as e:
            print(f"❌ Lỗi đọc file {filename}: {e}")
            
        return file_stats

    def analyze_nginx_file(self, filepath):
        """Phân tích một file log Nginx"""
        filename = os.path.basename(filepath)
        file_stats = {
            'filename': filename,
            'requests': 0,
            'bandwidth': 0,
            'status_codes': Counter(),
            'ips': Counter(),
            'endpoints': Counter(),
            'user_agents': Counter()
        }
        
        # Pattern cho nginx log
        nginx_pattern = r'(\S+) - (\S+) \[([^\]]+)\] "(\S+) ([^"]*)" (\d+) (\d+) "([^"]*)" "([^"]*)"'
        
        try:
            # Xử lý file nén hoặc thường
            if filepath.endswith('.gz'):
                file_obj = gzip.open(filepath, 'rt', encoding='utf-8', errors='ignore')
            else:
                file_obj = open(filepath, 'r', encoding='utf-8', errors='ignore')
                
            with file_obj as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line:
                        continue
                        
                    match = re.match(nginx_pattern, line)
                    if match:
                        ip, user, timestamp, method, url, status, size, referer, user_agent = match.groups()
                        
                        file_stats['requests'] += 1
                        self.results['nginx']['total_requests'] += 1
                        
                        # Status code
                        status_code = int(status)
                        file_stats['status_codes'][status_code] += 1
                        self.results['nginx']['status_codes'][status_code] += 1
                        
                        # Bandwidth
                        try:
                            bytes_sent = int(size)
                            file_stats['bandwidth'] += bytes_sent
                            self.results['nginx']['total_bandwidth'] += bytes_sent
                        except:
                            pass
                            
                        # IP addresses
                        file_stats['ips'][ip] += 1
                        self.results['nginx']['top_ips'][ip] += 1
                        
                        # Endpoints
                        endpoint = url.split('?')[0]  # Remove query params
                        file_stats['endpoints'][endpoint] += 1
                        self.results['nginx']['top_endpoints'][endpoint] += 1
                        
                        # User agents
                        if user_agent != '-':
                            ua_short = user_agent[:50] + '...' if len(user_agent) > 50 else user_agent
                            file_stats['user_agents'][ua_short] += 1
                            self.results['nginx']['user_agents'][ua_short] += 1
                            
        except Exception as e:
            print(f"❌ Lỗi đọc file {filename}: {e}")
            
        return file_stats

    def analyze_wso2_file(self, filepath):
        """Phân tích một file log WSO2"""
        filename = os.path.basename(filepath)
        file_stats = {
            'filename': filename,
            'requests': 0,
            'actions': Counter(),
            'users': Counter(),
            'outcomes': Counter()
        }
        
        try:
            with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line:
                        continue
                        
                    if 'AUDIT_LOG' in line:
                        file_stats['requests'] += 1
                        self.results['wso2']['total_requests'] += 1
                        
                        # Extract action
                        action_match = re.search(r'Action=([^\s]+)', line)
                        if action_match:
                            action = action_match.group(1)
                            file_stats['actions'][action] += 1
                            self.results['wso2']['actions'][action] += 1
                        
                        # Extract target user
                        target_match = re.search(r'Target=([^\s]+)', line)
                        if target_match:
                            user = target_match.group(1)
                            file_stats['users'][user] += 1
                            self.results['wso2']['users'][user] += 1
                        
                        # Extract outcome
                        outcome_match = re.search(r'Outcome=([^\s]+)', line)
                        if outcome_match:
                            outcome = outcome_match.group(1)
                            file_stats['outcomes'][outcome] += 1
                            self.results['wso2']['outcomes'][outcome] += 1
                            
        except Exception as e:
            print(f"❌ Lỗi đọc file {filename}: {e}")
            
        return file_stats

    def analyze_all_files(self, file_lists):
        """Phân tích tất cả file log"""
        print("\n🔄 Bắt đầu phân tích chi tiết...")
        
        # Phân tích LGSP
        print(f"\n📊 Phân tích {len(file_lists['lgsp'])} file LGSP...")
        for filepath in file_lists['lgsp']:
            file_stats = self.analyze_lgsp_file(filepath)
            date_key = self.extract_date_from_lgsp_filename(os.path.basename(filepath))
            if date_key:
                self.results['lgsp']['daily_stats'][date_key] = file_stats
            self.results['lgsp']['total_files'] += 1
            
        # Phân tích Nginx
        print(f"\n🌐 Phân tích {len(file_lists['nginx'])} file Nginx...")
        for filepath in file_lists['nginx']:
            file_stats = self.analyze_nginx_file(filepath)
            date_key = self.extract_date_from_nginx_filename(os.path.basename(filepath))
            if date_key:
                if date_key not in self.results['nginx']['daily_stats']:
                    self.results['nginx']['daily_stats'][date_key] = []
                self.results['nginx']['daily_stats'][date_key].append(file_stats)
            self.results['nginx']['total_files'] += 1
            
        # Phân tích WSO2
        print(f"\n🔐 Phân tích {len(file_lists['wso2'])} file WSO2...")
        for filepath in file_lists['wso2']:
            file_stats = self.analyze_wso2_file(filepath)
            date_key = self.extract_date_from_wso2_filename(os.path.basename(filepath))
            if date_key:
                self.results['wso2']['daily_stats'][date_key] = file_stats
            self.results['wso2']['total_files'] += 1

    def extract_date_from_lgsp_filename(self, filename):
        """Extract date từ filename LGSP (ddmmyyyy.log)"""
        match = re.search(r'(\d{2})(\d{2})(\d{4})\.log', filename)
        if match:
            day, month, year = match.groups()
            if month == '07' and year == '2025':
                return f"2025-07-{day}"
        return None

    def extract_date_from_nginx_filename(self, filename):
        """Extract date từ filename Nginx"""
        match = re.search(r'access\.log-(\d{4})(\d{2})(\d{2})\.gz', filename)
        if match:
            year, month, day = match.groups()
            if month == '07' and year == '2025':
                return f"2025-07-{day}"
        elif 'access.log' in filename and not filename.endswith('.gz'):
            # Current log file
            return f"2025-07-31"  # Assume current date
        return None

    def extract_date_from_wso2_filename(self, filename):
        """Extract date từ filename WSO2 (audit-dd-mm-yyyy.log)"""
        match = re.search(r'audit-(\d{2})-(\d{2})-(\d{4})\.log', filename)
        if match:
            day, month, year = match.groups()
            if month == '07' and year == '2025':
                return f"2025-07-{day}"
        return None

    def calculate_summary(self):
        """Tính toán tổng kết"""
        print("\n📈 Đang tính toán tổng kết...")
        
        total_requests = (self.results['lgsp']['total_requests'] + 
                         self.results['nginx']['total_requests'] + 
                         self.results['wso2']['total_requests'])
        
        total_bandwidth_gb = self.results['nginx']['total_bandwidth'] / (1024**3)
        
        self.results['summary'] = {
            'analysis_date': datetime.now().isoformat(),
            'total_files_analyzed': (self.results['lgsp']['total_files'] + 
                                   self.results['nginx']['total_files'] + 
                                   self.results['wso2']['total_files']),
            'total_requests': total_requests,
            'total_bandwidth_gb': total_bandwidth_gb,
            'avg_daily_requests': total_requests / 31 if total_requests > 0 else 0,
            'avg_daily_bandwidth_gb': total_bandwidth_gb / 31 if total_bandwidth_gb > 0 else 0,
            'systems_breakdown': {
                'lgsp': {
                    'files': self.results['lgsp']['total_files'],
                    'requests': self.results['lgsp']['total_requests'],
                    'errors': self.results['lgsp']['total_errors'],
                    'error_rate': (self.results['lgsp']['total_errors'] / self.results['lgsp']['total_requests'] * 100) if self.results['lgsp']['total_requests'] > 0 else 0
                },
                'nginx': {
                    'files': self.results['nginx']['total_files'],
                    'requests': self.results['nginx']['total_requests'],
                    'bandwidth_gb': total_bandwidth_gb,
                    'avg_response_size': self.results['nginx']['total_bandwidth'] / self.results['nginx']['total_requests'] if self.results['nginx']['total_requests'] > 0 else 0
                },
                'wso2': {
                    'files': self.results['wso2']['total_files'],
                    'requests': self.results['wso2']['total_requests'],
                    'unique_users': len(self.results['wso2']['users']),
                    'unique_actions': len(self.results['wso2']['actions'])
                }
            }
        }

    def generate_comprehensive_report(self):
        """Tạo báo cáo toàn diện"""
        print("\n" + "="*100)
        print("📊 BÁO CÁO PHÂN TÍCH LOG TOÀN DIỆN - THÁNG 7/2025")
        print("="*100)
        
        summary = self.results['summary']
        
        print(f"\n🎯 TỔNG QUAN:")
        print(f"   📁 Tổng số file đã phân tích: {summary['total_files_analyzed']}")
        print(f"   📊 Tổng số requests: {summary['total_requests']:,}")
        print(f"   💾 Tổng băng thông: {summary['total_bandwidth_gb']:.2f} GB")
        print(f"   📅 Trung bình requests/ngày: {summary['avg_daily_requests']:,.0f}")
        print(f"   💿 Trung bình băng thông/ngày: {summary['avg_daily_bandwidth_gb']:.3f} GB")
        
        print(f"\n🔧 CHI TIẾT THEO HỆ THỐNG:")
        
        # LGSP
        lgsp = summary['systems_breakdown']['lgsp']
        print(f"\n   ⚙️ LGSP:")
        print(f"      📁 Files: {lgsp['files']}")
        print(f"      📊 Requests: {lgsp['requests']:,}")
        print(f"      ❌ Errors: {lgsp['errors']:,}")
        print(f"      📈 Error rate: {lgsp['error_rate']:.1f}%")
        
        if self.results['lgsp']['api_endpoints']:
            print(f"      🔝 Top API endpoints:")
            for endpoint, count in self.results['lgsp']['api_endpoints'].most_common(5):
                print(f"         - {endpoint}: {count:,}")
        
        # Nginx
        nginx = summary['systems_breakdown']['nginx']
        print(f"\n   🌐 NGINX:")
        print(f"      📁 Files: {nginx['files']}")
        print(f"      📊 Requests: {nginx['requests']:,}")
        print(f"      💾 Bandwidth: {nginx['bandwidth_gb']:.2f} GB")
        print(f"      📏 Avg response size: {nginx['avg_response_size']:.0f} bytes")
        
        print(f"      📈 Top status codes:")
        for status, count in self.results['nginx']['status_codes'].most_common(10):
            percentage = (count / nginx['requests'] * 100) if nginx['requests'] > 0 else 0
            print(f"         - {status}: {count:,} ({percentage:.1f}%)")
        
        print(f"      🌍 Top IP addresses:")
        for ip, count in self.results['nginx']['top_ips'].most_common(5):
            print(f"         - {ip}: {count:,}")
        
        # WSO2
        wso2 = summary['systems_breakdown']['wso2']
        print(f"\n   🔐 WSO2:")
        print(f"      📁 Files: {wso2['files']}")
        print(f"      📊 Requests: {wso2['requests']:,}")
        print(f"      👥 Unique users: {wso2['unique_users']}")
        print(f"      🎯 Unique actions: {wso2['unique_actions']}")
        
        print(f"      🔝 Top actions:")
        for action, count in self.results['wso2']['actions'].most_common(5):
            print(f"         - {action}: {count:,}")
        
        print(f"      ✅ Outcomes:")
        for outcome, count in self.results['wso2']['outcomes'].most_common():
            print(f"         - {outcome}: {count:,}")

    def save_results(self):
        """Lưu kết quả chi tiết"""
        output_file = "comprehensive_log_analysis_july_2025.json"
        
        # Convert Counter objects to dict for JSON serialization
        json_results = json.loads(json.dumps(self.results, default=str))
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(json_results, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"\n✅ Kết quả chi tiết đã được lưu: {output_file}")

    def run_comprehensive_analysis(self):
        """Chạy phân tích toàn diện"""
        print("🚀 Bắt đầu phân tích log toàn diện tháng 7/2025...")
        
        # Khám phá file
        file_lists = self.discover_log_files()
        
        # Phân tích tất cả file
        self.analyze_all_files(file_lists)
        
        # Tính toán tổng kết
        self.calculate_summary()
        
        # Tạo báo cáo
        self.generate_comprehensive_report()
        
        # Lưu kết quả
        self.save_results()
        
        print(f"\n🎉 Phân tích toàn diện hoàn tất!")

if __name__ == "__main__":
    base_path = "/Users/<USER>/LGSP_Log/full_log"
    analyzer = ComprehensiveLogAnalyzer(base_path)
    analyzer.run_comprehensive_analysis()
