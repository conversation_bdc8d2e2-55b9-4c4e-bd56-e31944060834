
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>B<PERSON>o c<PERSON>o thống kê API Paths và Domains</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .container { max-width: 1800px; margin: 0 auto; background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.3); overflow: hidden; }
        .header { background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); color: white; padding: 30px; text-align: center; }
        .header h1 { margin: 0; font-size: 3em; font-weight: 300; }
        .header .subtitle { font-size: 1.3em; margin: 10px 0; opacity: 0.9; }
        .header .stats { font-size: 1.1em; margin: 15px 0; opacity: 0.8; }
        .content { padding: 30px; }
        .mega-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 30px 0; }
        .metric-card { background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 20px; border-radius: 12px; text-align: center; border-left: 5px solid #3498db; transition: all 0.3s ease; }
        .metric-card:hover { transform: translateY(-5px); box-shadow: 0 8px 25px rgba(0,0,0,0.15); }
        .metric-value { font-size: 2.2em; font-weight: bold; color: #2c3e50; margin-bottom: 5px; }
        .metric-label { color: #7f8c8d; font-size: 1em; }
        .section { margin: 40px 0; padding: 30px; background: #f8f9fa; border-radius: 15px; border-left: 6px solid #e74c3c; }
        .section-title { font-size: 2em; color: #2c3e50; margin-bottom: 25px; display: flex; align-items: center; }
        .section-icon { font-size: 1.3em; margin-right: 15px; }
        .chart-container { margin: 30px 0; padding: 25px; background: white; border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); }
        .chart-title { font-size: 1.6em; color: #2c3e50; margin-bottom: 20px; text-align: center; font-weight: 600; }
        .api-table { width: 100%; border-collapse: collapse; margin: 20px 0; background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .api-table th, .api-table td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        .api-table th { background: #34495e; color: white; font-weight: 600; }
        .api-table tr:hover { background: #f8f9fa; }
        .error { color: #e74c3c; font-weight: bold; }
        .success { color: #27ae60; font-weight: bold; }
        .warning { color: #f39c12; font-weight: bold; }
        .info { color: #3498db; font-weight: bold; }
        .highlight-box { background: linear-gradient(135deg, #ff7675 0%, #d63031 100%); color: white; padding: 20px; border-radius: 10px; margin: 20px 0; }
        .success-box { background: linear-gradient(135deg, #00b894 0%, #00a085 100%); color: white; padding: 20px; border-radius: 10px; margin: 20px 0; }
        .info-box { background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%); color: white; padding: 20px; border-radius: 10px; margin: 20px 0; }
        .api-path { font-family: 'Courier New', monospace; background: #f1f2f6; padding: 2px 6px; border-radius: 4px; }
        .percentage { font-size: 0.9em; color: #7f8c8d; }
        .footer { background: #2c3e50; color: white; padding: 30px; text-align: center; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 Báo cáo thống kê API Paths & Domains</h1>
            <div class="subtitle">Phân tích toàn diện API usage từ LGSP, Nginx, WSO2</div>
            <div class="stats">
                📊 169,886 API paths unique | 
                🌐 123 domains | 
                📈 1,354,515 total calls
            </div>
        </div>
        
        <div class="content">
            <h2>🎯 Tổng quan thống kê</h2>
            <div class="mega-grid">
                <div class="metric-card">
                    <div class="metric-value">169,886</div>
                    <div class="metric-label">API Paths Unique</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">123</div>
                    <div class="metric-label">Domains Unique</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">1,354,515</div>
                    <div class="metric-label">Total API Calls</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">10</div>
                    <div class="metric-label">LGSP APIs</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">168,536</div>
                    <div class="metric-label">Nginx Endpoints</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">1,368</div>
                    <div class="metric-label">WSO2 Endpoints</div>
                </div>
            </div>
            
            <div class="chart-container">
                <div class="chart-title">📈 Phân bố API calls theo hệ thống</div>
                <canvas id="systemApiChart" width="400" height="200"></canvas>
            </div>
            
            <div class="section">
                <div class="section-title"><span class="section-icon">🔝</span>Top 20 API Paths theo Traffic</div>
                <table class="api-table">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>API Path</th>
                            <th>Calls</th>
                            <th>%</th>
                            <th>Category</th>
                        </tr>
                    </thead>
                    <tbody>
    
                        <tr>
                            <td>1</td>
                            <td><span class="api-path">/api/dichvuconglienthong/layKetQua HTTP/1.1</span></td>
                            <td><strong>191,622</strong></td>
                            <td><span class="percentage">14.1%</span></td>
                            <td>API</td>
                        </tr>
        
                        <tr>
                            <td>2</td>
                            <td><span class="api-path">/oauth2/token</span></td>
                            <td><strong>158,361</strong></td>
                            <td><span class="percentage">11.7%</span></td>
                            <td>Authentication</td>
                        </tr>
        
                        <tr>
                            <td>3</td>
                            <td><span class="api-path">/api/lylichtuphap/capNhatTrangThai HTTP/1.1</span></td>
                            <td><strong>118,337</strong></td>
                            <td><span class="percentage">8.7%</span></td>
                            <td>API</td>
                        </tr>
        
                        <tr>
                            <td>4</td>
                            <td><span class="api-path">null</span></td>
                            <td><strong>41,077</strong></td>
                            <td><span class="percentage">3.0%</span></td>
                            <td>Other</td>
                        </tr>
        
                        <tr>
                            <td>5</td>
                            <td><span class="api-path">/ HTTP/1.1</span></td>
                            <td><strong>26,158</strong></td>
                            <td><span class="percentage">1.9%</span></td>
                            <td>Other</td>
                        </tr>
        
                        <tr>
                            <td>6</td>
                            <td><span class="api-path">/</span></td>
                            <td><strong>20,574</strong></td>
                            <td><span class="percentage">1.5%</span></td>
                            <td>Other</td>
                        </tr>
        
                        <tr>
                            <td>7</td>
                            <td><span class="api-path">/lgsp-igate-hs/1.0.0/api_get_key HTTP/1.1</span></td>
                            <td><strong>18,904</strong></td>
                            <td><span class="percentage">1.4%</span></td>
                            <td>API</td>
                        </tr>
        
                        <tr>
                            <td>8</td>
                            <td><span class="api-path">/lgsp-igate-hs/1.0.0/api_thongke_dvc HTTP/1.1</span></td>
                            <td><strong>18,899</strong></td>
                            <td><span class="percentage">1.4%</span></td>
                            <td>API</td>
                        </tr>
        
                        <tr>
                            <td>9</td>
                            <td><span class="api-path">/manager/html HTTP/1.1</span></td>
                            <td><strong>17,849</strong></td>
                            <td><span class="percentage">1.3%</span></td>
                            <td>Other</td>
                        </tr>
        
                        <tr>
                            <td>10</td>
                            <td><span class="api-path">/token HTTP/1.1</span></td>
                            <td><strong>10,252</strong></td>
                            <td><span class="percentage">0.8%</span></td>
                            <td>Authentication</td>
                        </tr>
        
                        <tr>
                            <td>11</td>
                            <td><span class="api-path">/index.php</span></td>
                            <td><strong>9,602</strong></td>
                            <td><span class="percentage">0.7%</span></td>
                            <td>Other</td>
                        </tr>
        
                        <tr>
                            <td>12</td>
                            <td><span class="api-path">/.htaccessbqIizSIHCIGHPWJf HTTP/1.1</span></td>
                            <td><strong>9,009</strong></td>
                            <td><span class="percentage">0.7%</span></td>
                            <td>Other</td>
                        </tr>
        
                        <tr>
                            <td>13</td>
                            <td><span class="api-path">/.htaccessXXRbRonl HTTP/1.1</span></td>
                            <td><strong>9,009</strong></td>
                            <td><span class="percentage">0.7%</span></td>
                            <td>Other</td>
                        </tr>
        
                        <tr>
                            <td>14</td>
                            <td><span class="api-path">/adminRnvmmImuustGUDGa HTTP/1.1</span></td>
                            <td><strong>9,009</strong></td>
                            <td><span class="percentage">0.7%</span></td>
                            <td>Administration</td>
                        </tr>
        
                        <tr>
                            <td>15</td>
                            <td><span class="api-path">/adminDyBYptwb HTTP/1.1</span></td>
                            <td><strong>9,009</strong></td>
                            <td><span class="percentage">0.7%</span></td>
                            <td>Administration</td>
                        </tr>
        
                        <tr>
                            <td>16</td>
                            <td><span class="api-path">/bKaMVIjxBgAyWepd HTTP/1.1</span></td>
                            <td><strong>9,009</strong></td>
                            <td><span class="percentage">0.7%</span></td>
                            <td>Other</td>
                        </tr>
        
                        <tr>
                            <td>17</td>
                            <td><span class="api-path">/sMLqCqDg HTTP/1.1</span></td>
                            <td><strong>9,009</strong></td>
                            <td><span class="percentage">0.7%</span></td>
                            <td>Other</td>
                        </tr>
        
                        <tr>
                            <td>18</td>
                            <td><span class="api-path">/+CSCOE+/logon.html HTTP/1.0</span></td>
                            <td><strong>7,183</strong></td>
                            <td><span class="percentage">0.5%</span></td>
                            <td>Other</td>
                        </tr>
        
                        <tr>
                            <td>19</td>
                            <td><span class="api-path">/token</span></td>
                            <td><strong>5,919</strong></td>
                            <td><span class="percentage">0.4%</span></td>
                            <td>Authentication</td>
                        </tr>
        
                        <tr>
                            <td>20</td>
                            <td><span class="api-path">/cyble.svg</span></td>
                            <td><strong>5,656</strong></td>
                            <td><span class="percentage">0.4%</span></td>
                            <td>Other</td>
                        </tr>
        
                    </tbody>
                </table>
            </div>
            
            <div class="chart-container">
                <div class="chart-title">🌐 Top Domains Distribution</div>
                <canvas id="domainsChart" width="400" height="200"></canvas>
            </div>
            
            <div class="section">
                <div class="section-title"><span class="section-icon">📊</span>Phân loại API theo chức năng</div>
                <div class="chart-container">
                    <canvas id="categoryChart" width="400" height="200"></canvas>
                </div>
                <div class="mega-grid">
    
                    <div class="metric-card">
                        <div class="metric-value">382,469</div>
                        <div class="metric-label">API (28.2%)</div>
                    </div>
        
                    <div class="metric-card">
                        <div class="metric-value">18,992</div>
                        <div class="metric-label">Data Services (1.4%)</div>
                    </div>
        
                    <div class="metric-card">
                        <div class="metric-value">663,681</div>
                        <div class="metric-label">Other (49.0%)</div>
                    </div>
        
                    <div class="metric-card">
                        <div class="metric-value">180,208</div>
                        <div class="metric-label">Authentication (13.3%)</div>
                    </div>
        
                    <div class="metric-card">
                        <div class="metric-value">96,571</div>
                        <div class="metric-label">Administration (7.1%)</div>
                    </div>
        
                    <div class="metric-card">
                        <div class="metric-value">12,594</div>
                        <div class="metric-label">Government Services (0.9%)</div>
                    </div>
        
                </div>
            </div>
            
            <div class="section">
                <div class="section-title"><span class="section-icon">❌</span>APIs có nhiều lỗi nhất</div>
                <div class="highlight-box">
                    <h3>🚨 CẢNH BÁO</h3>
                    <p>Có 10 APIs với tỷ lệ lỗi cao cần được kiểm tra!</p>
                </div>
                <table class="api-table">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>API Path</th>
                            <th>Errors</th>
                            <th>Error Rate</th>
                            <th>Total Calls</th>
                        </tr>
                    </thead>
                    <tbody>
    
                        <tr>
                            <td>1</td>
                            <td><span class="api-path">/api/dichvuconglienthong/layKetQua HTTP/1.1</span></td>
                            <td><span class="error">129,334</span></td>
                            <td><span class="error">67.5%</span></td>
                            <td>191,622</td>
                        </tr>
        
                        <tr>
                            <td>2</td>
                            <td><span class="api-path">null</span></td>
                            <td><span class="error">41,077</span></td>
                            <td><span class="error">100.0%</span></td>
                            <td>41,077</td>
                        </tr>
        
                        <tr>
                            <td>3</td>
                            <td><span class="api-path">/ HTTP/1.1</span></td>
                            <td><span class="error">20,762</span></td>
                            <td><span class="error">79.4%</span></td>
                            <td>26,158</td>
                        </tr>
        
                        <tr>
                            <td>4</td>
                            <td><span class="api-path">/</span></td>
                            <td><span class="error">18,273</span></td>
                            <td><span class="error">88.8%</span></td>
                            <td>20,574</td>
                        </tr>
        
                        <tr>
                            <td>5</td>
                            <td><span class="api-path">/manager/html HTTP/1.1</span></td>
                            <td><span class="error">17,849</span></td>
                            <td><span class="error">100.0%</span></td>
                            <td>17,849</td>
                        </tr>
        
                        <tr>
                            <td>6</td>
                            <td><span class="api-path">/index.php</span></td>
                            <td><span class="error">9,592</span></td>
                            <td><span class="error">99.9%</span></td>
                            <td>9,602</td>
                        </tr>
        
                        <tr>
                            <td>7</td>
                            <td><span class="api-path">/.htaccessbqIizSIHCIGHPWJf HTTP/1.1</span></td>
                            <td><span class="error">9,009</span></td>
                            <td><span class="error">100.0%</span></td>
                            <td>9,009</td>
                        </tr>
        
                        <tr>
                            <td>8</td>
                            <td><span class="api-path">/.htaccessXXRbRonl HTTP/1.1</span></td>
                            <td><span class="error">9,009</span></td>
                            <td><span class="error">100.0%</span></td>
                            <td>9,009</td>
                        </tr>
        
                        <tr>
                            <td>9</td>
                            <td><span class="api-path">/adminRnvmmImuustGUDGa HTTP/1.1</span></td>
                            <td><span class="error">9,009</span></td>
                            <td><span class="error">100.0%</span></td>
                            <td>9,009</td>
                        </tr>
        
                        <tr>
                            <td>10</td>
                            <td><span class="api-path">/adminDyBYptwb HTTP/1.1</span></td>
                            <td><span class="error">9,009</span></td>
                            <td><span class="error">100.0%</span></td>
                            <td>9,009</td>
                        </tr>
        
                    </tbody>
                </table>
            </div>
            
            <div class="section">
                <div class="section-title"><span class="section-icon">🔒</span>Phân tích bảo mật</div>
                <div class="mega-grid">
                    <div class="metric-card">
                        <div class="metric-value error">198,462</div>
                        <div class="metric-label">Failed Authentications</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value warning">274</div>
                        <div class="metric-label">Bot Detections</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">30076</div>
                        <div class="metric-label">APIs with Auth Issues</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">2</div>
                        <div class="metric-label">Bot Attack Types</div>
                    </div>
                </div>
                
                <div class="info-box">
                    <h3>🔍 Top APIs với Authentication Failures:</h3>
                    <ul>
    <li><strong>/%5C..%5C..%5C..%5C..%5C..%5C..%5C..%5C..%5C..%5Cetc%5Cpassw</strong>: 2 failures</li><li><strong>/api/dichvuconglienthong/layKetQua HTTP/1.1</strong>: 128,825 failures</li><li><strong>/..\x5C..\x5C..\x5C..\x5C..\x5C..\x5C..\x5C..\x5C..\x5C..\x5</strong>: 38 failures</li><li><strong>/..\x5C..\x5C..\x5C..\x5C..\x5C..\x5C..\x5C..\x5C..\x5C..\x5</strong>: 39 failures</li><li><strong>/lgsp-vdblis/1.0.0/tiepnhanhosomotcua HTTP/1.1</strong>: 70 failures</li>
                    </ul>
                </div>
            </div>
            
            <div class="section">
                <div class="section-title"><span class="section-icon">⚡</span>Phân tích hiệu năng</div>
                <div class="mega-grid">
    
                    <div class="metric-card">
                        <div class="metric-value warning">1.000s</div>
                        <div class="metric-label">Slowest: /pma/...</div>
                    </div>
            
                    <div class="metric-card">
                        <div class="metric-value warning">0.428s</div>
                        <div class="metric-label">Slowest: /services/UserInformationRecov...</div>
                    </div>
            
                    <div class="metric-card">
                        <div class="metric-value warning">0.281s</div>
                        <div class="metric-label">Slowest: /carbon/admin/login_action.jsp...</div>
                    </div>
            
                    <div class="metric-card">
                        <div class="metric-value info">1,362,575 bytes</div>
                        <div class="metric-label">Largest: /console/static/js/vendors~mai...</div>
                    </div>
            
                    <div class="metric-card">
                        <div class="metric-value info">977,483 bytes</div>
                        <div class="metric-label">Largest: /myaccount/static/js/vendors~m...</div>
                    </div>
            
                    <div class="metric-card">
                        <div class="metric-value info">886,980 bytes</div>
                        <div class="metric-label">Largest: /console/static/js/main.8862e2...</div>
                    </div>
            
                </div>
            </div>
            
            <div class="chart-container">
                <div class="chart-title">📈 API Usage Trends</div>
                <canvas id="trendsChart" width="400" height="200"></canvas>
            </div>
            
            <div class="section">
                <div class="section-title"><span class="section-icon">💡</span>Khuyến nghị</div>
                <div class="success-box">
                    <h3>🎯 Khuyến nghị tối ưu hóa:</h3>
                    <ul>
                        <li><strong>API Optimization:</strong> Tối ưu hóa top 5 APIs có traffic cao nhất</li>
                        <li><strong>Error Handling:</strong> Xử lý 10 APIs có tỷ lệ lỗi cao</li>
                        <li><strong>Security:</strong> Tăng cường bảo mật cho 198,462 failed authentications</li>
                        <li><strong>Performance:</strong> Caching cho APIs có response size lớn</li>
                        <li><strong>Monitoring:</strong> Thiết lập alerts cho top error-prone APIs</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>📅 Báo cáo được tạo vào 05/08/2025 07:09:47</p>
            <p>📊 Dữ liệu: 169,886 API paths, 1,354,515 calls từ 3 hệ thống</p>
        </div>
    </div>
    
    <script>
        // System API Distribution Chart
        const systemApiCtx = document.getElementById('systemApiChart').getContext('2d');
        new Chart(systemApiCtx, {
            type: 'doughnut',
            data: {
                labels: ['Nginx (168,536)', 'WSO2 (1,368)', 'LGSP (10)'],
                datasets: [{
                    data: [168536, 1368, 10],
                    backgroundColor: ['#3498db', '#e74c3c', '#f39c12'],
                    borderWidth: 3,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20
                        }
                    }
                }
            }
        });
        
        // Domains Chart
        const domainsCtx = document.getElementById('domainsChart').getContext('2d');
        const topDomains = [('api.ngsp.gov.vn', 1551), ('unknown', 1996), ('localhost', 1117787), ('***********', 10147), ('***********:443', 85), ('() { ignored; }; echo Content-Type: text', 465), ('***********:443admin', 3), ('wp.app.optinmonster.test', 41), ('${jndi:ldap:', 143), ('***********:443}}', 1)];
        new Chart(domainsCtx, {
            type: 'bar',
            data: {
                labels: ['api.ngsp.gov.vn', 'unknown', 'localhost', '***********', '***********:443', '() { ignored; }; echo Content-Type: text', '***********:443admin', 'wp.app.optinmonster.test', '${jndi:ldap:', '***********:443}}'],
                datasets: [{
                    label: 'API Calls',
                    data: [1551, 1996, 1117787, 10147, 85, 465, 3, 41, 143, 1],
                    backgroundColor: '#3498db',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        type: 'logarithmic'
                    }
                }
            }
        });
        
        // Category Chart
        const categoryCtx = document.getElementById('categoryChart').getContext('2d');
        new Chart(categoryCtx, {
            type: 'pie',
            data: {
                labels: ['API', 'Data Services', 'Other', 'Authentication', 'Administration', 'Government Services'],
                datasets: [{
                    data: [382469, 18992, 663681, 180208, 96571, 12594],
                    backgroundColor: ['#3498db', '#e74c3c', '#f39c12', '#2ecc71', '#9b59b6', '#1abc9c'],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'right'
                    }
                }
            }
        });
        
        // Trends Chart (placeholder - would need time series data)
        const trendsCtx = document.getElementById('trendsChart').getContext('2d');
        new Chart(trendsCtx, {
            type: 'line',
            data: {
                labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
                datasets: [{
                    label: 'API Calls',
                    data: [300000, 350000, 320000, 380000],
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    </script>
</body>
</html>
    