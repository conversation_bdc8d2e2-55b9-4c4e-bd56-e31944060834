#!/usr/bin/env python3
"""
<PERSON>ript thống kê chi tiết API paths và domains từ toàn bộ log
Phân tích LGSP, <PERSON><PERSON>x, WSO2 để tạo báo cáo thống kê API usage
"""

import os
import re
import gzip
import json
from datetime import datetime
from collections import defaultdict, Counter
import glob
from urllib.parse import urlparse, parse_qs

class APIAndDomainAnalyzer:
    def __init__(self, base_path):
        self.base_path = base_path
        self.lgsp_path = os.path.join(base_path, "lgsp_log")
        self.nginx_path = os.path.join(base_path, "nginx_log") 
        self.wso2_path = os.path.join(base_path, "wso2_log")
        
        # Kết quả thống kê API và Domain
        self.results = {
            'api_statistics': {
                'lgsp_apis': Counter(),
                'nginx_endpoints': Counter(),
                'wso2_endpoints': Counter(),
                'all_apis_combined': Counter()
            },
            'domain_statistics': {
                'nginx_domains': Counter(),
                'wso2_domains': Counter(),
                'lgsp_domains': Counter(),
                'all_domains_combined': Counter()
            },
            'detailed_analysis': {
                'api_methods': defaultdict(Counter),  # GET, POST, etc per API
                'api_status_codes': defaultdict(Counter),  # Status codes per API
                'api_response_sizes': defaultdict(list),  # Response sizes per API
                'api_user_agents': defaultdict(Counter),  # User agents per API
                'hourly_api_usage': defaultdict(Counter),  # API usage by hour
                'daily_api_usage': defaultdict(Counter)   # API usage by day
            },
            'security_analysis': {
                'failed_authentications': Counter(),  # 401/403 per API
                'bot_detected_apis': Counter(),
                'suspicious_patterns': Counter()
            },
            'performance_metrics': {
                'slowest_apis': defaultdict(list),  # Response times
                'highest_traffic_apis': Counter(),
                'error_prone_apis': Counter()
            },
            'summary': {}
        }

    def extract_domain_from_url(self, url):
        """Trích xuất domain từ URL"""
        try:
            if url.startswith('http'):
                parsed = urlparse(url)
                return parsed.netloc
            elif '/' in url:
                # Handle relative URLs
                return url.split('/')[0] if url.split('/')[0] else 'localhost'
            return 'unknown'
        except:
            return 'unknown'

    def extract_api_path_from_url(self, url):
        """Trích xuất API path từ URL"""
        try:
            if url.startswith('http'):
                parsed = urlparse(url)
                path = parsed.path
                # Remove query parameters for cleaner grouping
                return path if path else '/'
            elif url.startswith('/'):
                return url.split('?')[0]  # Remove query params
            return url
        except:
            return url

    def categorize_api_endpoint(self, endpoint):
        """Phân loại API endpoint"""
        endpoint_lower = endpoint.lower()
        
        if 'oauth' in endpoint_lower or 'token' in endpoint_lower:
            return 'Authentication'
        elif 'api' in endpoint_lower:
            return 'API'
        elif any(term in endpoint_lower for term in ['admin', 'management', 'config']):
            return 'Administration'
        elif any(term in endpoint_lower for term in ['data', 'notify', 'sync']):
            return 'Data Services'
        elif any(term in endpoint_lower for term in ['lgsp', 'igate', 'dichvu']):
            return 'Government Services'
        else:
            return 'Other'

    def analyze_lgsp_logs(self):
        """Phân tích API paths từ LGSP logs"""
        print("📊 Phân tích API paths từ LGSP logs...")
        
        lgsp_files = glob.glob(os.path.join(self.lgsp_path, "*.log"))
        
        for filepath in lgsp_files:
            try:
                with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                    for line in f:
                        if '[REQUEST]' in line:
                            # Extract API path from LGSP log
                            api_match = re.search(r'"apiPath":"([^"]+)"', line)
                            if api_match:
                                full_url = api_match.group(1)
                                
                                # Extract domain and API path
                                domain = self.extract_domain_from_url(full_url)
                                api_path = self.extract_api_path_from_url(full_url)
                                
                                self.results['api_statistics']['lgsp_apis'][api_path] += 1
                                self.results['api_statistics']['all_apis_combined'][api_path] += 1
                                self.results['domain_statistics']['lgsp_domains'][domain] += 1
                                self.results['domain_statistics']['all_domains_combined'][domain] += 1
                                
                                # Extract timestamp for hourly analysis
                                time_match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}):', line)
                                if time_match:
                                    hour_key = time_match.group(1)
                                    self.results['detailed_analysis']['hourly_api_usage'][api_path][hour_key] += 1
                                    
                                    date_key = hour_key.split(' ')[0]
                                    self.results['detailed_analysis']['daily_api_usage'][api_path][date_key] += 1
                                
            except Exception as e:
                print(f"❌ Lỗi đọc LGSP file {os.path.basename(filepath)}: {e}")

    def analyze_nginx_logs(self):
        """Phân tích endpoints và domains từ Nginx logs"""
        print("🌐 Phân tích endpoints và domains từ Nginx logs...")
        
        nginx_files = glob.glob(os.path.join(self.nginx_path, "*.log*"))
        nginx_pattern = r'(\S+) - (\S+) \[([^\]]+)\] "(\S+) ([^"]*)" (\d+) (\d+) "([^"]*)" "([^"]*)"'
        
        for filepath in nginx_files:
            try:
                if filepath.endswith('.gz'):
                    file_obj = gzip.open(filepath, 'rt', encoding='utf-8', errors='ignore')
                else:
                    file_obj = open(filepath, 'r', encoding='utf-8', errors='ignore')
                    
                with file_obj as f:
                    for line in f:
                        match = re.match(nginx_pattern, line)
                        if match:
                            ip, user, timestamp, method, url, status, size, referer, user_agent = match.groups()
                            
                            # Extract API path and domain
                            api_path = self.extract_api_path_from_url(url)
                            
                            # Extract domain from Host header or referer
                            domain = 'localhost'  # Default for nginx
                            if referer and referer != '-':
                                domain = self.extract_domain_from_url(referer)
                            
                            self.results['api_statistics']['nginx_endpoints'][api_path] += 1
                            self.results['api_statistics']['all_apis_combined'][api_path] += 1
                            self.results['domain_statistics']['nginx_domains'][domain] += 1
                            self.results['domain_statistics']['all_domains_combined'][domain] += 1
                            
                            # Detailed analysis
                            self.results['detailed_analysis']['api_methods'][api_path][method] += 1
                            self.results['detailed_analysis']['api_status_codes'][api_path][int(status)] += 1
                            
                            try:
                                response_size = int(size)
                                self.results['detailed_analysis']['api_response_sizes'][api_path].append(response_size)
                            except:
                                pass
                                
                            if user_agent != '-':
                                ua_short = user_agent[:30] + '...' if len(user_agent) > 30 else user_agent
                                self.results['detailed_analysis']['api_user_agents'][api_path][ua_short] += 1
                            
                            # Security analysis
                            if int(status) in [401, 403]:
                                self.results['security_analysis']['failed_authentications'][api_path] += 1
                            
                            # Performance metrics
                            self.results['performance_metrics']['highest_traffic_apis'][api_path] += 1
                            if int(status) >= 400:
                                self.results['performance_metrics']['error_prone_apis'][api_path] += 1
                                
                            # Extract hour from timestamp
                            time_match = re.search(r'(\d{2}/\w{3}/\d{4}:\d{2}):', timestamp)
                            if time_match:
                                hour_key = time_match.group(1)
                                self.results['detailed_analysis']['hourly_api_usage'][api_path][hour_key] += 1
                                
                                date_key = hour_key.split(':')[0]
                                self.results['detailed_analysis']['daily_api_usage'][api_path][date_key] += 1
                                
            except Exception as e:
                print(f"❌ Lỗi đọc Nginx file {os.path.basename(filepath)}: {e}")

    def analyze_wso2_logs(self):
        """Phân tích endpoints từ WSO2 logs"""
        print("🔐 Phân tích endpoints từ WSO2 logs...")
        
        # Analyze HTTP Access logs
        http_access_files = glob.glob(os.path.join(self.wso2_path, "http_access*.log"))
        http_pattern = r'(\S+) - - \[([^\]]+)\] (\S+) ([^\s]+) HTTP/1\.1 (\d+) (\S+) - ([^\s]+) ([\d\.]+)'
        
        for filepath in http_access_files:
            try:
                with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                    for line in f:
                        match = re.match(http_pattern, line)
                        if match:
                            ip, timestamp, method, endpoint, status, size, user_agent, response_time = match.groups()
                            
                            # Clean endpoint
                            api_path = self.extract_api_path_from_url(endpoint)
                            domain = 'wso2-gateway'  # WSO2 internal
                            
                            self.results['api_statistics']['wso2_endpoints'][api_path] += 1
                            self.results['api_statistics']['all_apis_combined'][api_path] += 1
                            self.results['domain_statistics']['wso2_domains'][domain] += 1
                            self.results['domain_statistics']['all_domains_combined'][domain] += 1
                            
                            # Detailed analysis
                            self.results['detailed_analysis']['api_methods'][api_path][method] += 1
                            self.results['detailed_analysis']['api_status_codes'][api_path][int(status)] += 1
                            
                            # Performance metrics
                            try:
                                rt = float(response_time)
                                self.results['performance_metrics']['slowest_apis'][api_path].append(rt)
                            except:
                                pass
                                
                            self.results['performance_metrics']['highest_traffic_apis'][api_path] += 1
                            
                            if int(status) >= 400:
                                self.results['performance_metrics']['error_prone_apis'][api_path] += 1
                                
            except Exception as e:
                print(f"❌ Lỗi đọc WSO2 HTTP Access file {os.path.basename(filepath)}: {e}")
        
        # Analyze Bot Detection logs for suspicious API access
        bot_files = glob.glob(os.path.join(self.wso2_path, "wso2-BotDetectedData*.log"))
        for filepath in bot_files:
            try:
                with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                    for line in f:
                        # Extract API from bot detection
                        if 'Request Method' in line:
                            method_match = re.search(r'Request Method : ([^\s]+)', line)
                            if method_match:
                                method = method_match.group(1)
                                self.results['security_analysis']['bot_detected_apis'][f'Bot-{method}'] += 1
                                
            except Exception as e:
                print(f"❌ Lỗi đọc WSO2 Bot file {os.path.basename(filepath)}: {e}")

    def calculate_api_statistics(self):
        """Tính toán thống kê API chi tiết"""
        print("📈 Đang tính toán thống kê API chi tiết...")
        
        # Calculate average response sizes
        avg_response_sizes = {}
        for api, sizes in self.results['detailed_analysis']['api_response_sizes'].items():
            if sizes:
                avg_response_sizes[api] = sum(sizes) / len(sizes)
        
        # Calculate average response times
        avg_response_times = {}
        for api, times in self.results['performance_metrics']['slowest_apis'].items():
            if times:
                avg_response_times[api] = sum(times) / len(times)
        
        # Top APIs by different metrics
        top_apis_by_traffic = self.results['performance_metrics']['highest_traffic_apis'].most_common(20)
        top_apis_by_errors = self.results['performance_metrics']['error_prone_apis'].most_common(10)
        
        # API categorization
        api_categories = defaultdict(int)
        for api, count in self.results['api_statistics']['all_apis_combined'].items():
            category = self.categorize_api_endpoint(api)
            api_categories[category] += count
        
        self.results['summary'] = {
            'total_unique_apis': len(self.results['api_statistics']['all_apis_combined']),
            'total_unique_domains': len(self.results['domain_statistics']['all_domains_combined']),
            'total_api_calls': sum(self.results['api_statistics']['all_apis_combined'].values()),
            'top_apis_by_traffic': dict(top_apis_by_traffic),
            'top_apis_by_errors': dict(top_apis_by_errors),
            'api_categories': dict(api_categories),
            'avg_response_sizes': avg_response_sizes,
            'avg_response_times': avg_response_times,
            'analysis_date': datetime.now().isoformat()
        }

    def generate_api_domain_report(self):
        """Tạo báo cáo thống kê API và Domain"""
        print("\n" + "="*120)
        print("📊 BÁO CÁO THỐNG KÊ API PATHS VÀ DOMAINS - TOÀN BỘ LOG")
        print("="*120)
        
        summary = self.results['summary']
        
        print(f"\n🎯 TỔNG QUAN:")
        print(f"   📊 Tổng số API paths unique: {summary['total_unique_apis']}")
        print(f"   🌐 Tổng số domains unique: {summary['total_unique_domains']}")
        print(f"   📈 Tổng số API calls: {summary['total_api_calls']:,}")
        
        print(f"\n📋 PHÂN BỐ THEO HỆ THỐNG:")
        print(f"   ⚙️ LGSP APIs: {len(self.results['api_statistics']['lgsp_apis'])} unique")
        print(f"   🌐 Nginx Endpoints: {len(self.results['api_statistics']['nginx_endpoints'])} unique")
        print(f"   🔐 WSO2 Endpoints: {len(self.results['api_statistics']['wso2_endpoints'])} unique")
        
        print(f"\n🔝 TOP 20 API PATHS BY TRAFFIC:")
        for i, (api, count) in enumerate(summary['top_apis_by_traffic'].items(), 1):
            percentage = (count / summary['total_api_calls'] * 100)
            category = self.categorize_api_endpoint(api)
            print(f"   {i:2d}. {api[:80]:<80} | {count:>8,} calls ({percentage:5.1f}%) | {category}")
        
        print(f"\n🌐 TOP DOMAINS:")
        for i, (domain, count) in enumerate(self.results['domain_statistics']['all_domains_combined'].most_common(10), 1):
            percentage = (count / summary['total_api_calls'] * 100)
            print(f"   {i:2d}. {domain:<30} | {count:>8,} calls ({percentage:5.1f}%)")
        
        print(f"\n📊 PHÂN LOẠI API THEO CHỨC NĂNG:")
        for category, count in summary['api_categories'].items():
            percentage = (count / summary['total_api_calls'] * 100)
            print(f"   📋 {category:<20} | {count:>8,} calls ({percentage:5.1f}%)")
        
        print(f"\n❌ TOP 10 API PATHS CÓ NHIỀU LỖI NHẤT:")
        for i, (api, count) in enumerate(summary['top_apis_by_errors'].items(), 1):
            total_calls = self.results['performance_metrics']['highest_traffic_apis'][api]
            error_rate = (count / total_calls * 100) if total_calls > 0 else 0
            print(f"   {i:2d}. {api[:60]:<60} | {count:>6,} errors ({error_rate:5.1f}% rate)")
        
        print(f"\n🔒 PHÂN TÍCH BẢO MẬT:")
        print(f"   🚫 Failed Authentications: {sum(self.results['security_analysis']['failed_authentications'].values()):,}")
        print(f"   🤖 Bot Detections: {sum(self.results['security_analysis']['bot_detected_apis'].values()):,}")
        
        if self.results['security_analysis']['failed_authentications']:
            print(f"   🔝 Top APIs with Auth Failures:")
            for api, count in self.results['security_analysis']['failed_authentications'].most_common(5):
                print(f"      - {api[:50]:<50} | {count:>6,} failures")
        
        print(f"\n⚡ PHÂN TÍCH HIỆU NĂNG:")
        if summary['avg_response_times']:
            print(f"   🐌 Slowest APIs (avg response time):")
            sorted_by_time = sorted(summary['avg_response_times'].items(), key=lambda x: x[1], reverse=True)[:5]
            for api, avg_time in sorted_by_time:
                print(f"      - {api[:50]:<50} | {avg_time:>8.3f}s avg")
        
        if summary['avg_response_sizes']:
            print(f"   📦 Largest Response APIs (avg size):")
            sorted_by_size = sorted(summary['avg_response_sizes'].items(), key=lambda x: x[1], reverse=True)[:5]
            for api, avg_size in sorted_by_size:
                print(f"      - {api[:50]:<50} | {avg_size:>8,.0f} bytes avg")
        
        print(f"\n📅 PHÂN TÍCH THEO THỜI GIAN:")
        # Most active APIs by day
        daily_totals = defaultdict(int)
        for api, daily_data in self.results['detailed_analysis']['daily_api_usage'].items():
            for date, count in daily_data.items():
                daily_totals[date] += count
        
        if daily_totals:
            print(f"   📈 Top 5 ngày có traffic cao nhất:")
            for i, (date, count) in enumerate(sorted(daily_totals.items(), key=lambda x: x[1], reverse=True)[:5], 1):
                print(f"      {i}. {date} | {count:>8,} API calls")

    def save_detailed_results(self):
        """Lưu kết quả chi tiết"""
        output_file = "api_domain_statistics_report.json"
        
        # Convert Counter objects to dict for JSON serialization
        json_results = {}
        for key, value in self.results.items():
            if isinstance(value, dict):
                json_results[key] = {}
                for subkey, subvalue in value.items():
                    if isinstance(subvalue, (Counter, defaultdict)):
                        json_results[key][subkey] = dict(subvalue)
                    else:
                        json_results[key][subkey] = subvalue
            else:
                json_results[key] = value
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(json_results, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"\n✅ Kết quả chi tiết đã được lưu: {output_file}")

    def run_analysis(self):
        """Chạy phân tích toàn bộ"""
        print("🚀 Bắt đầu phân tích API paths và domains...")
        
        self.analyze_lgsp_logs()
        self.analyze_nginx_logs()
        self.analyze_wso2_logs()
        self.calculate_api_statistics()
        self.generate_api_domain_report()
        self.save_detailed_results()
        
        print(f"\n🎉 Phân tích API và Domain hoàn tất!")

if __name__ == "__main__":
    base_path = "/Users/<USER>/LGSP_Log/full_log"
    analyzer = APIAndDomainAnalyzer(base_path)
    analyzer.run_analysis()
