#!/usr/bin/env python3
"""
Script phân tích log toàn diện và chi tiết nhất cho tháng 7/2025
<PERSON><PERSON> g<PERSON><PERSON> tất cả các loại file log: LGSP, Nginx, và WSO2 (tấ<PERSON> cả variants)
"""

import os
import re
import gzip
import json
from datetime import datetime, timedelta
from collections import defaultdict, Counter
import glob

class UltimateLogAnalyzer:
    def __init__(self, base_path):
        self.base_path = base_path
        self.lgsp_path = os.path.join(base_path, "lgsp_log")
        self.nginx_path = os.path.join(base_path, "nginx_log") 
        self.wso2_path = os.path.join(base_path, "wso2_log")
        
        # Kết quả phân tích siêu chi tiết
        self.results = {
            'lgsp': {
                'total_files': 0,
                'total_requests': 0,
                'total_errors': 0,
                'daily_stats': {},
                'api_endpoints': Counter(),
                'error_types': Counter()
            },
            'nginx': {
                'total_files': 0,
                'total_requests': 0,
                'total_bandwidth': 0,
                'status_codes': Counter(),
                'daily_stats': {},
                'top_ips': Counter(),
                'top_endpoints': Counter(),
                'user_agents': Counter(),
                'file_types': {}
            },
            'wso2': {
                'total_files': 0,
                'file_types': {
                    'audit': {'files': 0, 'requests': 0, 'actions': Counter(), 'users': Counter(), 'outcomes': Counter()},
                    'http_access': {'files': 0, 'requests': 0, 'status_codes': Counter(), 'endpoints': Counter(), 'ips': Counter()},
                    'apigw_errors': {'files': 0, 'errors': 0, 'error_types': Counter(), 'components': Counter()},
                    'apigw_service': {'files': 0, 'requests': 0, 'services': Counter(), 'response_times': []},
                    'carbon': {'files': 0, 'logs': 0, 'log_levels': Counter(), 'components': Counter()},
                    'bot_detected': {'files': 0, 'detections': 0, 'ips': Counter(), 'methods': Counter()}
                },
                'daily_stats': {},
                'total_requests': 0,
                'total_errors': 0
            },
            'summary': {}
        }

    def discover_all_log_files(self):
        """Khám phá tất cả file log chi tiết"""
        print("🔍 Đang khám phá toàn bộ cấu trúc file log...")
        
        # LGSP files
        lgsp_files = glob.glob(os.path.join(self.lgsp_path, "*072025.log"))
        
        # Nginx files  
        nginx_files = glob.glob(os.path.join(self.nginx_path, "*202507*.gz")) + \
                     glob.glob(os.path.join(self.nginx_path, "access.log*"))
        
        # WSO2 files - phân loại chi tiết
        wso2_files = {
            'audit': glob.glob(os.path.join(self.wso2_path, "audit-*-07-2025.log")),
            'http_access': glob.glob(os.path.join(self.wso2_path, "http_access*2025-07*.log")),
            'apigw_errors': glob.glob(os.path.join(self.wso2_path, "wso2-apigw-errors-07-*-2025*.log*")),
            'apigw_service': glob.glob(os.path.join(self.wso2_path, "wso2-apigw-service-07-*-2025*.log*")),
            'carbon': glob.glob(os.path.join(self.wso2_path, "wso2carbon-07-*-2025*.log")),
            'bot_detected': glob.glob(os.path.join(self.wso2_path, "wso2-BotDetectedData-07-*-2025.log"))
        }
        
        print(f"📁 LGSP: {len(lgsp_files)} files")
        print(f"📁 Nginx: {len(nginx_files)} files")
        print(f"📁 WSO2 breakdown:")
        for file_type, files in wso2_files.items():
            print(f"   - {file_type}: {len(files)} files")
        
        return {
            'lgsp': lgsp_files,
            'nginx': nginx_files,
            'wso2': wso2_files
        }

    def analyze_wso2_audit_file(self, filepath):
        """Phân tích file audit WSO2"""
        stats = {'requests': 0, 'actions': Counter(), 'users': Counter(), 'outcomes': Counter()}
        
        try:
            with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                for line in f:
                    if 'AUDIT_LOG' in line:
                        stats['requests'] += 1
                        
                        # Extract action
                        action_match = re.search(r'Action=([^\s]+)', line)
                        if action_match:
                            stats['actions'][action_match.group(1)] += 1
                        
                        # Extract user
                        target_match = re.search(r'Target=([^\s]+)', line)
                        if target_match:
                            stats['users'][target_match.group(1)] += 1
                        
                        # Extract outcome
                        outcome_match = re.search(r'Outcome=([^\s]+)', line)
                        if outcome_match:
                            stats['outcomes'][outcome_match.group(1)] += 1
        except Exception as e:
            print(f"❌ Lỗi đọc audit file {os.path.basename(filepath)}: {e}")
            
        return stats

    def analyze_wso2_http_access_file(self, filepath):
        """Phân tích file http_access WSO2"""
        stats = {'requests': 0, 'status_codes': Counter(), 'endpoints': Counter(), 'ips': Counter()}
        
        # Pattern: IP - - [timestamp] METHOD endpoint HTTP/1.1 status size - user_agent response_time
        pattern = r'(\S+) - - \[([^\]]+)\] (\S+) ([^\s]+) HTTP/1\.1 (\d+) (\S+) - ([^\s]+) ([\d\.]+)'
        
        try:
            with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue
                        
                    match = re.match(pattern, line)
                    if match:
                        ip, timestamp, method, endpoint, status, size, user_agent, response_time = match.groups()
                        
                        stats['requests'] += 1
                        stats['status_codes'][int(status)] += 1
                        stats['endpoints'][endpoint] += 1
                        stats['ips'][ip] += 1
                    else:
                        # Handle malformed lines
                        if ' - ' in line and '[' in line:
                            stats['requests'] += 1
                            
        except Exception as e:
            print(f"❌ Lỗi đọc http_access file {os.path.basename(filepath)}: {e}")
            
        return stats

    def analyze_wso2_apigw_errors_file(self, filepath):
        """Phân tích file wso2-apigw-errors"""
        stats = {'errors': 0, 'error_types': Counter(), 'components': Counter()}
        
        try:
            if filepath.endswith('.gz'):
                file_obj = gzip.open(filepath, 'rt', encoding='utf-8', errors='ignore')
            else:
                file_obj = open(filepath, 'r', encoding='utf-8', errors='ignore')
                
            with file_obj as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue
                        
                    stats['errors'] += 1
                    
                    # Extract error level
                    if ' ERROR ' in line:
                        stats['error_types']['ERROR'] += 1
                    elif ' WARN ' in line:
                        stats['error_types']['WARN'] += 1
                    elif ' INFO ' in line:
                        stats['error_types']['INFO'] += 1
                    
                    # Extract component
                    component_match = re.search(r'\{([^}]+)\}', line)
                    if component_match:
                        component = component_match.group(1).split('.')[-1]  # Get last part
                        stats['components'][component] += 1
                        
        except Exception as e:
            print(f"❌ Lỗi đọc apigw_errors file {os.path.basename(filepath)}: {e}")
            
        return stats

    def analyze_wso2_carbon_file(self, filepath):
        """Phân tích file wso2carbon"""
        stats = {'logs': 0, 'log_levels': Counter(), 'components': Counter()}
        
        try:
            with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue
                        
                    stats['logs'] += 1
                    
                    # Extract log level
                    if ' ERROR ' in line:
                        stats['log_levels']['ERROR'] += 1
                    elif ' WARN ' in line:
                        stats['log_levels']['WARN'] += 1
                    elif ' INFO ' in line:
                        stats['log_levels']['INFO'] += 1
                    elif ' DEBUG ' in line:
                        stats['log_levels']['DEBUG'] += 1
                    
                    # Extract component
                    component_match = re.search(r'\{([^}]+)\}', line)
                    if component_match:
                        component = component_match.group(1).split('.')[-1]
                        stats['components'][component] += 1
                        
        except Exception as e:
            print(f"❌ Lỗi đọc carbon file {os.path.basename(filepath)}: {e}")
            
        return stats

    def analyze_wso2_bot_detected_file(self, filepath):
        """Phân tích file wso2-BotDetectedData"""
        stats = {'detections': 0, 'ips': Counter(), 'methods': Counter()}
        
        try:
            with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue
                        
                    stats['detections'] += 1
                    
                    # Extract client IP
                    ip_match = re.search(r'client Ip : ([^\s]+)', line)
                    if ip_match:
                        stats['ips'][ip_match.group(1)] += 1
                    
                    # Extract request method
                    method_match = re.search(r'Request Method : ([^\s]+)', line)
                    if method_match:
                        stats['methods'][method_match.group(1)] += 1
                        
        except Exception as e:
            print(f"❌ Lỗi đọc bot_detected file {os.path.basename(filepath)}: {e}")
            
        return stats

    def analyze_all_wso2_files(self, wso2_files):
        """Phân tích tất cả file WSO2"""
        print("\n🔐 Phân tích chi tiết tất cả file WSO2...")
        
        # Audit files
        print(f"   📋 Phân tích {len(wso2_files['audit'])} audit files...")
        for filepath in wso2_files['audit']:
            stats = self.analyze_wso2_audit_file(filepath)
            self.results['wso2']['file_types']['audit']['files'] += 1
            self.results['wso2']['file_types']['audit']['requests'] += stats['requests']
            self.results['wso2']['file_types']['audit']['actions'].update(stats['actions'])
            self.results['wso2']['file_types']['audit']['users'].update(stats['users'])
            self.results['wso2']['file_types']['audit']['outcomes'].update(stats['outcomes'])
            self.results['wso2']['total_requests'] += stats['requests']
        
        # HTTP Access files
        print(f"   🌐 Phân tích {len(wso2_files['http_access'])} http_access files...")
        for filepath in wso2_files['http_access']:
            stats = self.analyze_wso2_http_access_file(filepath)
            self.results['wso2']['file_types']['http_access']['files'] += 1
            self.results['wso2']['file_types']['http_access']['requests'] += stats['requests']
            self.results['wso2']['file_types']['http_access']['status_codes'].update(stats['status_codes'])
            self.results['wso2']['file_types']['http_access']['endpoints'].update(stats['endpoints'])
            self.results['wso2']['file_types']['http_access']['ips'].update(stats['ips'])
            self.results['wso2']['total_requests'] += stats['requests']
        
        # API Gateway Errors files
        print(f"   ❌ Phân tích {len(wso2_files['apigw_errors'])} apigw_errors files...")
        for filepath in wso2_files['apigw_errors']:
            stats = self.analyze_wso2_apigw_errors_file(filepath)
            self.results['wso2']['file_types']['apigw_errors']['files'] += 1
            self.results['wso2']['file_types']['apigw_errors']['errors'] += stats['errors']
            self.results['wso2']['file_types']['apigw_errors']['error_types'].update(stats['error_types'])
            self.results['wso2']['file_types']['apigw_errors']['components'].update(stats['components'])
            self.results['wso2']['total_errors'] += stats['errors']
        
        # Carbon files
        print(f"   ⚙️ Phân tích {len(wso2_files['carbon'])} carbon files...")
        for filepath in wso2_files['carbon']:
            stats = self.analyze_wso2_carbon_file(filepath)
            self.results['wso2']['file_types']['carbon']['files'] += 1
            self.results['wso2']['file_types']['carbon']['logs'] += stats['logs']
            self.results['wso2']['file_types']['carbon']['log_levels'].update(stats['log_levels'])
            self.results['wso2']['file_types']['carbon']['components'].update(stats['components'])
        
        # Bot Detection files
        print(f"   🤖 Phân tích {len(wso2_files['bot_detected'])} bot_detected files...")
        for filepath in wso2_files['bot_detected']:
            stats = self.analyze_wso2_bot_detected_file(filepath)
            self.results['wso2']['file_types']['bot_detected']['files'] += 1
            self.results['wso2']['file_types']['bot_detected']['detections'] += stats['detections']
            self.results['wso2']['file_types']['bot_detected']['ips'].update(stats['ips'])
            self.results['wso2']['file_types']['bot_detected']['methods'].update(stats['methods'])
        
        # Tính tổng files WSO2
        self.results['wso2']['total_files'] = sum(
            self.results['wso2']['file_types'][file_type]['files'] 
            for file_type in self.results['wso2']['file_types']
        )

    def analyze_lgsp_files(self, lgsp_files):
        """Phân tích file LGSP (giữ nguyên logic cũ)"""
        print(f"\n📊 Phân tích {len(lgsp_files)} file LGSP...")
        
        for filepath in lgsp_files:
            filename = os.path.basename(filepath)
            daily_requests = 0
            daily_errors = 0
            
            try:
                with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                    for line in f:
                        if '[REQUEST]' in line:
                            daily_requests += 1
                            self.results['lgsp']['total_requests'] += 1
                            
                            # Extract API endpoint
                            api_match = re.search(r'"apiPath":"([^"]+)"', line)
                            if api_match:
                                self.results['lgsp']['api_endpoints'][api_match.group(1)] += 1
                            
                        if any(error_keyword in line for error_keyword in ['Exception', 'ERROR', 'Error']):
                            daily_errors += 1
                            self.results['lgsp']['total_errors'] += 1
                            
                            if 'Exception' in line:
                                error_match = re.search(r'(\w+Exception)', line)
                                if error_match:
                                    self.results['lgsp']['error_types'][error_match.group(1)] += 1
                                    
                date_key = self.extract_date_from_lgsp_filename(filename)
                if date_key:
                    self.results['lgsp']['daily_stats'][date_key] = {
                        'requests': daily_requests,
                        'errors': daily_errors
                    }
                    
            except Exception as e:
                print(f"❌ Lỗi đọc LGSP file {filename}: {e}")
                
        self.results['lgsp']['total_files'] = len(lgsp_files)

    def analyze_nginx_files(self, nginx_files):
        """Phân tích file Nginx (giữ nguyên logic cũ nhưng cải thiện)"""
        print(f"\n🌐 Phân tích {len(nginx_files)} file Nginx...")
        
        nginx_pattern = r'(\S+) - (\S+) \[([^\]]+)\] "(\S+) ([^"]*)" (\d+) (\d+) "([^"]*)" "([^"]*)"'
        
        for filepath in nginx_files:
            filename = os.path.basename(filepath)
            daily_requests = 0
            daily_bandwidth = 0
            daily_status = Counter()
            
            try:
                if filepath.endswith('.gz'):
                    file_obj = gzip.open(filepath, 'rt', encoding='utf-8', errors='ignore')
                else:
                    file_obj = open(filepath, 'r', encoding='utf-8', errors='ignore')
                    
                with file_obj as f:
                    for line in f:
                        match = re.match(nginx_pattern, line)
                        if match:
                            ip, user, timestamp, method, url, status, size, referer, user_agent = match.groups()
                            
                            daily_requests += 1
                            self.results['nginx']['total_requests'] += 1
                            
                            status_code = int(status)
                            daily_status[status_code] += 1
                            self.results['nginx']['status_codes'][status_code] += 1
                            
                            try:
                                bytes_sent = int(size)
                                daily_bandwidth += bytes_sent
                                self.results['nginx']['total_bandwidth'] += bytes_sent
                            except:
                                pass
                                
                            self.results['nginx']['top_ips'][ip] += 1
                            endpoint = url.split('?')[0]
                            self.results['nginx']['top_endpoints'][endpoint] += 1
                            
                            if user_agent != '-':
                                ua_short = user_agent[:50] + '...' if len(user_agent) > 50 else user_agent
                                self.results['nginx']['user_agents'][ua_short] += 1
                                
                date_key = self.extract_date_from_nginx_filename(filename)
                if date_key:
                    self.results['nginx']['daily_stats'][date_key] = {
                        'requests': daily_requests,
                        'bandwidth': daily_bandwidth,
                        'status_codes': dict(daily_status)
                    }
                    
            except Exception as e:
                print(f"❌ Lỗi đọc Nginx file {filename}: {e}")
                
        self.results['nginx']['total_files'] = len(nginx_files)

    def extract_date_from_lgsp_filename(self, filename):
        """Extract date từ filename LGSP"""
        match = re.search(r'(\d{2})(\d{2})(\d{4})\.log', filename)
        if match:
            day, month, year = match.groups()
            if month == '07' and year == '2025':
                return f"2025-07-{day}"
        return None

    def extract_date_from_nginx_filename(self, filename):
        """Extract date từ filename Nginx"""
        match = re.search(r'access\.log-(\d{4})(\d{2})(\d{2})\.gz', filename)
        if match:
            year, month, day = match.groups()
            if month == '07' and year == '2025':
                return f"2025-07-{day}"
        elif 'access.log' in filename and not filename.endswith('.gz'):
            return f"2025-07-31"
        return None

    def calculate_ultimate_summary(self):
        """Tính toán tổng kết siêu chi tiết"""
        print("\n📈 Đang tính toán tổng kết siêu chi tiết...")
        
        total_requests = (self.results['lgsp']['total_requests'] + 
                         self.results['nginx']['total_requests'] + 
                         self.results['wso2']['total_requests'])
        
        total_bandwidth_gb = self.results['nginx']['total_bandwidth'] / (1024**3)
        total_files = (self.results['lgsp']['total_files'] + 
                      self.results['nginx']['total_files'] + 
                      self.results['wso2']['total_files'])
        
        self.results['summary'] = {
            'analysis_date': datetime.now().isoformat(),
            'total_files_analyzed': total_files,
            'total_requests': total_requests,
            'total_bandwidth_gb': total_bandwidth_gb,
            'total_errors': self.results['lgsp']['total_errors'] + self.results['wso2']['total_errors'],
            'avg_daily_requests': total_requests / 31,
            'avg_daily_bandwidth_gb': total_bandwidth_gb / 31,
            'systems_breakdown': {
                'lgsp': {
                    'files': self.results['lgsp']['total_files'],
                    'requests': self.results['lgsp']['total_requests'],
                    'errors': self.results['lgsp']['total_errors'],
                    'error_rate': (self.results['lgsp']['total_errors'] / self.results['lgsp']['total_requests'] * 100) if self.results['lgsp']['total_requests'] > 0 else 0
                },
                'nginx': {
                    'files': self.results['nginx']['total_files'],
                    'requests': self.results['nginx']['total_requests'],
                    'bandwidth_gb': total_bandwidth_gb,
                    'avg_response_size': self.results['nginx']['total_bandwidth'] / self.results['nginx']['total_requests'] if self.results['nginx']['total_requests'] > 0 else 0
                },
                'wso2': {
                    'total_files': self.results['wso2']['total_files'],
                    'total_requests': self.results['wso2']['total_requests'],
                    'total_errors': self.results['wso2']['total_errors'],
                    'file_types_breakdown': {}
                }
            }
        }
        
        # WSO2 breakdown chi tiết
        for file_type, data in self.results['wso2']['file_types'].items():
            self.results['summary']['systems_breakdown']['wso2']['file_types_breakdown'][file_type] = {
                'files': data['files'],
                'main_metric': data.get('requests', data.get('errors', data.get('logs', data.get('detections', 0))))
            }

    def run_ultimate_analysis(self):
        """Chạy phân tích siêu toàn diện"""
        print("🚀 Bắt đầu phân tích log SIÊU TOÀN DIỆN tháng 7/2025...")
        
        # Khám phá file
        file_lists = self.discover_all_log_files()
        
        # Phân tích từng hệ thống
        self.analyze_lgsp_files(file_lists['lgsp'])
        self.analyze_nginx_files(file_lists['nginx'])
        self.analyze_all_wso2_files(file_lists['wso2'])
        
        # Tính toán tổng kết
        self.calculate_ultimate_summary()
        
        # Tạo báo cáo
        self.generate_ultimate_report()
        
        # Lưu kết quả
        self.save_ultimate_results()
        
        print(f"\n🎉 Phân tích siêu toàn diện hoàn tất!")

    def generate_ultimate_report(self):
        """Tạo báo cáo siêu chi tiết"""
        print("\n" + "="*120)
        print("📊 BÁO CÁO PHÂN TÍCH LOG SIÊU TOÀN DIỆN - THÁNG 7/2025")
        print("="*120)
        
        summary = self.results['summary']
        
        print(f"\n🎯 TỔNG QUAN SIÊU CHI TIẾT:")
        print(f"   📁 Tổng số file: {summary['total_files_analyzed']}")
        print(f"   📊 Tổng requests: {summary['total_requests']:,}")
        print(f"   💾 Tổng băng thông: {summary['total_bandwidth_gb']:.2f} GB")
        print(f"   ❌ Tổng errors: {summary['total_errors']:,}")
        print(f"   📅 TB requests/ngày: {summary['avg_daily_requests']:,.0f}")
        
        # LGSP
        lgsp = summary['systems_breakdown']['lgsp']
        print(f"\n⚙️ LGSP SYSTEM:")
        print(f"   📁 Files: {lgsp['files']} | 📊 Requests: {lgsp['requests']:,} | ❌ Errors: {lgsp['errors']:,} | 📈 Error rate: {lgsp['error_rate']:.1f}%")
        
        # Nginx
        nginx = summary['systems_breakdown']['nginx']
        print(f"\n🌐 NGINX SYSTEM:")
        print(f"   📁 Files: {nginx['files']} | 📊 Requests: {nginx['requests']:,} | 💾 Bandwidth: {nginx['bandwidth_gb']:.2f} GB")
        
        # WSO2 - Chi tiết từng loại file
        wso2 = summary['systems_breakdown']['wso2']
        print(f"\n🔐 WSO2 SYSTEM - SIÊU CHI TIẾT:")
        print(f"   📁 Tổng files: {wso2['total_files']} | 📊 Tổng requests: {wso2['total_requests']:,} | ❌ Tổng errors: {wso2['total_errors']:,}")
        
        for file_type, breakdown in wso2['file_types_breakdown'].items():
            print(f"   📋 {file_type.upper()}: {breakdown['files']} files, {breakdown['main_metric']:,} events")
        
        # Chi tiết WSO2
        print(f"\n🔍 WSO2 CHI TIẾT THEO LOẠI FILE:")
        
        # Audit
        audit = self.results['wso2']['file_types']['audit']
        if audit['files'] > 0:
            print(f"   📋 AUDIT ({audit['files']} files, {audit['requests']:,} requests):")
            print(f"      🔝 Top actions: {dict(audit['actions'].most_common(3))}")
            print(f"      👥 Users: {len(audit['users'])} unique")
            print(f"      ✅ Outcomes: {dict(audit['outcomes'])}")
        
        # HTTP Access
        http_access = self.results['wso2']['file_types']['http_access']
        if http_access['files'] > 0:
            print(f"   🌐 HTTP_ACCESS ({http_access['files']} files, {http_access['requests']:,} requests):")
            print(f"      📈 Top status codes: {dict(http_access['status_codes'].most_common(5))}")
            print(f"      🔝 Top endpoints: {dict(http_access['endpoints'].most_common(3))}")
        
        # API Gateway Errors
        apigw_errors = self.results['wso2']['file_types']['apigw_errors']
        if apigw_errors['files'] > 0:
            print(f"   ❌ APIGW_ERRORS ({apigw_errors['files']} files, {apigw_errors['errors']:,} errors):")
            print(f"      🔝 Error types: {dict(apigw_errors['error_types'].most_common(3))}")
            print(f"      🔧 Components: {dict(apigw_errors['components'].most_common(3))}")
        
        # Carbon
        carbon = self.results['wso2']['file_types']['carbon']
        if carbon['files'] > 0:
            print(f"   ⚙️ CARBON ({carbon['files']} files, {carbon['logs']:,} logs):")
            print(f"      📊 Log levels: {dict(carbon['log_levels'])}")
            print(f"      🔧 Components: {dict(carbon['components'].most_common(3))}")
        
        # Bot Detection
        bot_detected = self.results['wso2']['file_types']['bot_detected']
        if bot_detected['files'] > 0:
            print(f"   🤖 BOT_DETECTED ({bot_detected['files']} files, {bot_detected['detections']:,} detections):")
            print(f"      🌍 Top IPs: {dict(bot_detected['ips'].most_common(3))}")
            print(f"      📡 Methods: {dict(bot_detected['methods'])}")

    def save_ultimate_results(self):
        """Lưu kết quả siêu chi tiết"""
        output_file = "ultimate_log_analysis_july_2025.json"
        
        # Convert Counter objects to dict for JSON serialization
        json_results = json.loads(json.dumps(self.results, default=str))
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(json_results, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"\n✅ Kết quả siêu chi tiết đã được lưu: {output_file}")

if __name__ == "__main__":
    base_path = "/Users/<USER>/LGSP_Log/full_log"
    analyzer = UltimateLogAnalyzer(base_path)
    analyzer.run_ultimate_analysis()
