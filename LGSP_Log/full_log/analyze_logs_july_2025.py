#!/usr/bin/env python3
"""
Script phân tích log tháng 7/2025 cho 3 hệ thống: LGSP, WSO2, Nginx
Phân tích: tổng số kết nối, request, status code, dự đoán dung lượng và băng thông
"""

import os
import re
import gzip
import json
from datetime import datetime, timedelta
from collections import defaultdict, Counter

class LogAnalyzer:
    def __init__(self, base_path):
        self.base_path = base_path
        self.lgsp_path = os.path.join(base_path, "lgsp_log")
        self.nginx_path = os.path.join(base_path, "nginx_log") 
        self.wso2_path = os.path.join(base_path, "wso2_log")
        
        # Kết quả phân tích
        self.results = {
            'lgsp': {'requests': 0, 'connections': 0, 'errors': 0, 'daily_stats': {}},
            'nginx': {'requests': 0, 'connections': 0, 'status_codes': Counter(), 'bandwidth': 0, 'daily_stats': {}},
            'wso2': {'requests': 0, 'connections': 0, 'actions': Counter(), 'daily_stats': {}},
            'summary': {}
        }

    def analyze_lgsp_logs(self):
        """Phân tích log LGSP tháng 7/2025"""
        print("Đang phân tích log LGSP...")
        
        for day in range(1, 32):  # 1-31 tháng 7
            filename = f"{day:02d}072025.log"
            filepath = os.path.join(self.lgsp_path, filename)
            
            if not os.path.exists(filepath):
                continue
                
            daily_requests = 0
            daily_errors = 0
            
            try:
                with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                    for line in f:
                        if '[REQUEST]' in line:
                            daily_requests += 1
                            self.results['lgsp']['requests'] += 1
                            
                        if 'Exception' in line or 'ERROR' in line:
                            daily_errors += 1
                            self.results['lgsp']['errors'] += 1
                            
                self.results['lgsp']['daily_stats'][f"2025-07-{day:02d}"] = {
                    'requests': daily_requests,
                    'errors': daily_errors
                }
                
            except Exception as e:
                print(f"Lỗi đọc file {filename}: {e}")

    def analyze_nginx_logs(self):
        """Phân tích log Nginx tháng 7/2025"""
        print("Đang phân tích log Nginx...")
        
        # Pattern để parse log nginx
        nginx_pattern = r'(\S+) - (\S+) \[([^\]]+)\] "(\S+) ([^"]*)" (\d+) (\d+) "([^"]*)" "([^"]*)" "([^"]*)"'
        
        for day in range(1, 32):  # 1-31 tháng 7
            filename = f"access.log-202507{day:02d}.gz"
            filepath = os.path.join(self.nginx_path, filename)
            
            if not os.path.exists(filepath):
                continue
                
            daily_requests = 0
            daily_bandwidth = 0
            daily_status = Counter()
            unique_ips = set()
            
            try:
                with gzip.open(filepath, 'rt', encoding='utf-8', errors='ignore') as f:
                    for line in f:
                        match = re.match(nginx_pattern, line)
                        if match:
                            ip, user, timestamp, method, url, status, size, referer, user_agent, forwarded = match.groups()
                            
                            daily_requests += 1
                            self.results['nginx']['requests'] += 1
                            
                            # Đếm status code
                            status_code = int(status)
                            daily_status[status_code] += 1
                            self.results['nginx']['status_codes'][status_code] += 1
                            
                            # Tính băng thông
                            try:
                                bytes_sent = int(size)
                                daily_bandwidth += bytes_sent
                                self.results['nginx']['bandwidth'] += bytes_sent
                            except:
                                pass
                                
                            # Đếm unique IP
                            unique_ips.add(ip)
                            
                self.results['nginx']['daily_stats'][f"2025-07-{day:02d}"] = {
                    'requests': daily_requests,
                    'bandwidth': daily_bandwidth,
                    'status_codes': dict(daily_status),
                    'unique_ips': len(unique_ips)
                }
                
            except Exception as e:
                print(f"Lỗi đọc file {filename}: {e}")

    def analyze_wso2_logs(self):
        """Phân tích log WSO2 tháng 7/2025"""
        print("Đang phân tích log WSO2...")
        
        for day in range(1, 32):  # 1-31 tháng 7
            filename = f"audit-{day:02d}-07-2025.log"
            filepath = os.path.join(self.wso2_path, filename)
            
            if not os.path.exists(filepath):
                continue
                
            daily_requests = 0
            daily_actions = Counter()
            
            try:
                with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                    for line in f:
                        if 'AUDIT_LOG' in line:
                            daily_requests += 1
                            self.results['wso2']['requests'] += 1
                            
                            # Extract action
                            action_match = re.search(r'Action=([^\s]+)', line)
                            if action_match:
                                action = action_match.group(1)
                                daily_actions[action] += 1
                                self.results['wso2']['actions'][action] += 1
                                
                self.results['wso2']['daily_stats'][f"2025-07-{day:02d}"] = {
                    'requests': daily_requests,
                    'actions': dict(daily_actions)
                }
                
            except Exception as e:
                print(f"Lỗi đọc file {filename}: {e}")

    def calculate_summary(self):
        """Tính toán tổng kết và dự đoán"""
        print("Đang tính toán tổng kết...")
        
        total_requests = (self.results['lgsp']['requests'] + 
                         self.results['nginx']['requests'] + 
                         self.results['wso2']['requests'])
        
        total_bandwidth_gb = self.results['nginx']['bandwidth'] / (1024**3)  # Convert to GB
        
        # Dự đoán cho tháng (31 ngày)
        avg_daily_requests = total_requests / 31
        predicted_monthly_requests = avg_daily_requests * 31
        
        avg_daily_bandwidth = total_bandwidth_gb / 31
        predicted_monthly_bandwidth = avg_daily_bandwidth * 31
        
        self.results['summary'] = {
            'total_requests': total_requests,
            'total_bandwidth_gb': total_bandwidth_gb,
            'avg_daily_requests': avg_daily_requests,
            'avg_daily_bandwidth_gb': avg_daily_bandwidth,
            'predicted_monthly_requests': predicted_monthly_requests,
            'predicted_monthly_bandwidth_gb': predicted_monthly_bandwidth,
            'top_status_codes': dict(self.results['nginx']['status_codes'].most_common(10)),
            'top_wso2_actions': dict(self.results['wso2']['actions'].most_common(10))
        }

    def generate_report(self):
        """Tạo báo cáo chi tiết"""
        print("\n" + "="*80)
        print("BÁO CÁO PHÂN TÍCH LOG THÁNG 7/2025")
        print("="*80)
        
        print(f"\n1. TỔNG QUAN:")
        print(f"   - Tổng số requests: {self.results['summary']['total_requests']:,}")
        print(f"   - Tổng băng thông: {self.results['summary']['total_bandwidth_gb']:.2f} GB")
        print(f"   - Trung bình requests/ngày: {self.results['summary']['avg_daily_requests']:.0f}")
        print(f"   - Trung bình băng thông/ngày: {self.results['summary']['avg_daily_bandwidth_gb']:.2f} GB")
        
        print(f"\n2. CHI TIẾT THEO HỆ THỐNG:")
        print(f"   LGSP:")
        print(f"     - Requests: {self.results['lgsp']['requests']:,}")
        print(f"     - Errors: {self.results['lgsp']['errors']:,}")
        
        print(f"   Nginx:")
        print(f"     - Requests: {self.results['nginx']['requests']:,}")
        print(f"     - Băng thông: {self.results['nginx']['bandwidth'] / (1024**3):.2f} GB")
        
        print(f"   WSO2:")
        print(f"     - Requests: {self.results['wso2']['requests']:,}")
        
        print(f"\n3. TOP STATUS CODES (Nginx):")
        for status, count in self.results['summary']['top_status_codes'].items():
            print(f"     - {status}: {count:,}")
            
        print(f"\n4. TOP WSO2 ACTIONS:")
        for action, count in self.results['summary']['top_wso2_actions'].items():
            print(f"     - {action}: {count:,}")
            
        print(f"\n5. DỰ ĐOÁN CHO THÁNG 7:")
        print(f"   - Dự kiến tổng requests: {self.results['summary']['predicted_monthly_requests']:,.0f}")
        print(f"   - Dự kiến tổng băng thông: {self.results['summary']['predicted_monthly_bandwidth_gb']:.2f} GB")

    def save_results(self):
        """Lưu kết quả ra file JSON"""
        output_file = "log_analysis_july_2025.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2, default=str)
        print(f"\nKết quả đã được lưu vào: {output_file}")

    def run_analysis(self):
        """Chạy toàn bộ phân tích"""
        print("Bắt đầu phân tích log tháng 7/2025...")
        
        self.analyze_lgsp_logs()
        self.analyze_nginx_logs() 
        self.analyze_wso2_logs()
        self.calculate_summary()
        self.generate_report()
        self.save_results()
        
        print("\nPhân tích hoàn tất!")

if __name__ == "__main__":
    # Đường dẫn tới thư mục log
    base_path = "/Users/<USER>/LGSP_Log/full_log"
    
    analyzer = LogAnalyzer(base_path)
    analyzer.run_analysis()
